/*
 * @Author: your name
 * @Date: 2021-09-28 15:11:37
 * @LastEditTime: 2021-10-14 10:09:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /hll-finrisk-web/src/pages/RiskPreRun/service.ts
 */
import { getVanEvn } from '@/utils/utils';
import { CoupeSDK } from '@hll/coupe-sdk';
import { request } from '@umijs/max';
import type { MultipleFormParam, RiskDetailParams, RiskListParams, SingleFormParam } from './data';

let isNewSwitch = false;
let init = false;
const getSwitch = async () => {
  if (init) return Promise.resolve(isNewSwitch);
  const sdk = new CoupeSDK({
    // Coupe ID
    coupeId: '870cf2ab422a4f70a4d9f3b90b22748e',
    // 数据环境
    env: getVanEvn(),
  });

  // 拉取数据
  const res: any = await sdk.fetch();
  init = true;
  isNewSwitch = res?.interfaceSwitch;
  return Promise.resolve(isNewSwitch);
};

export async function queryRiskPreList(params?: RiskListParams) {
  await getSwitch();
  const url = isNewSwitch ? '/bizadmin/cms/listPrepare' : '/loan/cms/listPrepare';
  const headers = isNewSwitch
    ? {
        'hll-appId': 'bme-finance-bizadmin-svc',
      }
    : undefined;
  return request(url, {
    params,
    headers,
    ifTrimParams: true,
  });
}

export async function queryPrepareDetail(params?: RiskDetailParams) {
  await getSwitch();
  const url = isNewSwitch ? '/bizadmin/cms/listPrepareDetail' : '/loan/cms/listPrepareDetail';
  const headers = isNewSwitch
    ? {
        'hll-appId': 'bme-finance-bizadmin-svc',
      }
    : undefined;
  return request(url, {
    params,
    headers,
  });
}

export async function submitPrepareMultiple(data?: MultipleFormParam) {
  await getSwitch();
  const url = isNewSwitch ? '/bizadmin/prepare/upload' : '/loan/prepare/upload';
  const headers = isNewSwitch
    ? {
        'hll-appId': 'bme-finance-bizadmin-svc',
      }
    : undefined;
  return request(url, {
    method: 'POST',
    data,
    headers,
  });
}

export async function submitPrepareSingle(data?: SingleFormParam) {
  await getSwitch();
  const url = isNewSwitch ? '/bizadmin/prepare/submitPrepare' : '/loan/prepare/submitPrepare';
  const headers = isNewSwitch
    ? {
        'hll-appId': 'bme-finance-bizadmin-svc',
      }
    : undefined;
  return request(url, {
    method: 'POST',
    data,
    headers,
  });
}

// 任务类型
export async function downTemplateExample(taskType: string) {
  await getSwitch();
  const url = isNewSwitch ? '/bizadmin/prepare/downloadExample' : '/loan/prepare/downloadExample';
  const headers = isNewSwitch
    ? {
        'hll-appId': 'bme-finance-bizadmin-svc',
      }
    : undefined;
  return request(url, {
    getResponse: true,
    responseType: 'blob',
    method: 'GET',
    params: { taskType },
    headers,
  });
}

// 导出接口
export async function exportRiskResult(flowNo: string) {
  await getSwitch();
  const url = isNewSwitch ? '/bizadmin/cms/list/export' : '/loan/cms/list/export';
  const headers = isNewSwitch
    ? {
        'hll-appId': 'bme-finance-bizadmin-svc',
      }
    : undefined;
  return request(url, {
    getResponse: true,
    responseType: 'blob',
    method: 'GET',
    params: { flowNo },
    headers,
  });
}
