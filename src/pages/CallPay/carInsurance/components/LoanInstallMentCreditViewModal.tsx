import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import LoadingButton from '@/components/LoadingButton';
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { Alert, Descriptions, Form, message, Space, Spin, Table } from 'antd';
import type { MutableRefObject } from 'react';
import React, { memo, useRef, useState } from 'react';
import { OfflineRepayReviewModalColumns } from '../columns/OfflineRepayReviewModalColumns';
import { applyOrderInfo, submitBillAudit } from '../services';
import type { IapplyOrderInfo, IofflineRepayReviewItem } from '../types';
import { IstatusEnCode } from '../types';
import './index.less';

type Props = {
  record?: IofflineRepayReviewItem;
  remissionReviewModalOpen: boolean;
  setRemissionReviewModalOpen: (val: boolean) => void;
  actionRef: MutableRefObject<ActionType | undefined>;
};
const LoanInstallMentCreditViewModal: React.FC<Props> = (props) => {
  const { record, remissionReviewModalOpen, setRemissionReviewModalOpen, actionRef } = props;
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const [loading, setLoading] = useState(false);
  const [applyOrderInfoData, setApplyOrderInfoData] = useState<IapplyOrderInfo>();
  const [confirmReject, setConfirmReject] = useState(false); // 是否确认驳回
  const imagePreviewRef = useRef<ImagePreviewInstance>(null);
  const [form] = Form.useForm();

  async function getApplyOrderInfo() {
    try {
      if (record?.businessNo) {
        setLoading(true);
        const data = await applyOrderInfo({ businessNo: record?.businessNo });
        setApplyOrderInfoData(data);
        setRemissionReviewModalOpen(true);
      }
    } finally {
      setLoading(false);
    }
  }
  return (
    <>
      <ModalForm
        title={<div className="modal_tit">催收减免</div>}
        open={remissionReviewModalOpen}
        form={form}
        onOpenChange={(val) => {
          if (!val) {
            // 当弹窗被关闭的时候 清空数据
            setApplyOrderInfoData(undefined);
            form.resetFields();
          } else {
            getApplyOrderInfo();
          }
        }}
        width={1000}
        modalProps={{
          maskClosable: true,
          onCancel: () => {
            setRemissionReviewModalOpen(false);
          },
        }}
        submitter={{
          render() {
            return (
              // 只有待审批 才展示
              applyOrderInfoData?.status === IstatusEnCode.PendingApproval && (
                <Space>
                  <LoadingButton
                    type="primary"
                    danger
                    onClick={async () => {
                      if (!confirmReject) {
                        setConfirmReject(true);
                        return;
                      }
                      try {
                        form.validateFields(['auditRemark']).then(async (values) => {
                          if (record?.businessNo) {
                            fullScreenLoadingRef.current?.open();
                            await submitBillAudit({
                              auditStatus: 1,
                              businessNo: record?.businessNo,
                              auditRemark: values.auditRemark, // 驳回原因
                            });
                            message.success('提交成功');
                            setRemissionReviewModalOpen(false);
                            actionRef.current?.reload();
                          }
                        });
                      } catch (error) {
                      } finally {
                        fullScreenLoadingRef.current?.close();
                      }
                    }}
                  >
                    驳回
                  </LoadingButton>
                  <LoadingButton
                    type="primary"
                    onClick={async () => {
                      try {
                        if (record?.businessNo) {
                          fullScreenLoadingRef.current?.open();
                          await submitBillAudit({
                            auditStatus: 2,
                            businessNo: record?.businessNo,
                          });
                          message.success('提交成功');
                          setRemissionReviewModalOpen(false);
                          actionRef.current?.reload();
                        }
                      } catch (error) {
                      } finally {
                        fullScreenLoadingRef.current?.close();
                      }
                    }}
                  >
                    审核通过
                  </LoadingButton>
                </Space>
              )
            );
          },
        }}
      >
        <Spin spinning={loading}>
          {/* 拒绝tip */}
          {confirmReject && (
            <div className="reject_tip_wrap">
              <Alert message="是否确认驳回该笔催收减免" type="warning" showIcon />
            </div>
          )}
          <div className="relief_tit">减免详情：</div>
          <div className="block_area">
            <div className="no_border relief_detail_row">
              <span className="relief_amount_label">减免总额：</span>
              <span className="relief_amount_value">
                {applyOrderInfoData?.exemptionAmount || '-'}
              </span>
              元
            </div>

            <Table
              className="relief_detail_table"
              pagination={false}
              dataSource={applyOrderInfoData?.remissionList}
              columns={[
                {
                  title: '费项',
                  dataIndex: 'option',
                  render: () => {
                    return '金额';
                  },
                },
                {
                  title: '减免本金(元)',
                  dataIndex: 'remissionPrincipal',
                },
                {
                  title: '减免利息(元)',
                  dataIndex: 'remissionInterest',
                },
                {
                  title: '减免罚息(元)',
                  dataIndex: 'remissionPenalty',
                },
                {
                  title: '减免滞纳金(元)',
                  dataIndex: 'remissionDelayAmount',
                },
              ]}
            />
          </div>
          <div className="relief_detail_account_info relief_tit">
            请确认账单信息和还款金额无误后提交：
          </div>
          <div className="block_area bill_info_wrao">
            <Table
              title={() => (
                <>
                  <ExclamationCircleOutlined
                    style={{ fontSize: 16, color: 'orange', marginRight: 5 }}
                  />
                  以下为操作还款时，账单快照数据。
                </>
              )}
              pagination={false}
              columns={OfflineRepayReviewModalColumns.map((column) => {
                // 小贷催收详情不展示车架号，默认为 隐藏
                if (
                  record?.secondProductCode === SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY &&
                  (column?.dataIndex === 'subjectMatterNo' || column?.key === 'subjectMatterNo')
                ) {
                  return {
                    ...column,
                    hidden: true,
                  };
                }
                return column;
              })}
              dataSource={applyOrderInfoData?.billRspDTOList}
              scroll={{ x: 'max-content' }}
            />
          </div>
          <div className="relief_detail_row relief_tit attachment">补充信息：</div>
          <div className="block_area pt_0">
            <Descriptions bordered column={2} size={'small'} style={{ marginTop: 10 }}>
              <Descriptions.Item span={2} label="付款凭证">
                {applyOrderInfoData?.attachList?.length
                  ? applyOrderInfoData?.attachList?.map((file: any) => {
                      const { url } = file;
                      const urlList = applyOrderInfoData?.attachList?.map((item: any) => item.url);
                      return (
                        <div key={url}>
                          <a
                            onClick={() => {
                              imagePreviewRef.current?.previewFile({ url, urlList });
                            }}
                          >
                            {file.name}
                          </a>
                        </div>
                      );
                    })
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item span={2} label="备注">
                {applyOrderInfoData?.remark}
              </Descriptions.Item>
              {applyOrderInfoData?.status !== IstatusEnCode.PendingApproval && (
                <Descriptions.Item span={2} label="驳回原因">
                  {applyOrderInfoData?.reason}
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
          {applyOrderInfoData?.status === IstatusEnCode.PendingApproval && (
            <div className="reject_reason_wrap">
              <div className="reject_reason_tit relief_tit">驳回原因：</div>
              <ProFormTextArea
                labelCol={{ span: 3 }}
                placeholder="请输入驳回原因"
                width="md"
                rules={[{ required: confirmReject, message: '请输入驳回原因' }]}
                fieldProps={{ maxLength: 200 }}
                name="auditRemark"
              />
            </div>
          )}
        </Spin>
      </ModalForm>
      <ImagePreview ref={imagePreviewRef} />
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};
export default memo(LoanInstallMentCreditViewModal);
