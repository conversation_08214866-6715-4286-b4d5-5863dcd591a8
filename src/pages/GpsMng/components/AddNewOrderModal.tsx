// 下单安装设备弹窗
import CityPicker from '@/components/CityPicker';
import { pattern } from '@/utils/validate';
import {
  ModalForm,
  ProForm,
  ProFormCheckbox,
  ProFormDateTimePicker,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { ProFormTextArea } from '@ant-design/pro-form';
import { useRequest } from '@umijs/max';
import { Col, Form, message } from 'antd';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import { createGpsOrder, getGpsDefaultData } from '../service';
import { ORDER_TYPE } from './consts';

type AddNewOrderModalProps = {
  open: boolean;
  orderNo?: string;
  onCancel: () => void;
  onSuccess: () => void;
};

type OrderForm = {
  productId: { label: string; value: string }; // 产品设备id
  contactName: string; // 安装联系人
  contactPhone: string; // 安装联系人电话
  installationSite: string; // 施工地点
  siteCode: string; // 施工地区行政编码
  carModel: string; // 车型
  vin: string; // 车架号
  remark: string; //备注
  appointTime: string; // 安装时间
};

const AddNewOrderModal = (props: AddNewOrderModalProps) => {
  const { orderNo, open } = props;
  const [productList, setProductList] = useState([]);
  const [btnLoading, setBtnLoading] = useState(false);

  const [form] = Form.useForm<OrderForm>();

  const { data: defaultData } = useRequest(() => {
    return getGpsDefaultData(orderNo);
  });

  const formatCity = (cityValues: string[]) => {
    let cityStr = '';
    cityValues.forEach((item, index) => {
      cityStr += item.split('_')[0];
      if (index !== cityValues.length - 1) {
        cityStr += '-';
      }
    });
    return cityStr;
  };

  const onSubmit = async (values: OrderForm) => {
    const { siteCode, productId, supplierCode } = values;
    const newSiteCode = siteCode[siteCode.length - 1]?.split('_')[1];
    const params = {
      ...values,
      orderType: ORDER_TYPE.CREATE,
      orderNo,
      productId: productId.value,
      productName: productId.label,
      siteCode: newSiteCode,
      siteData: formatCity(siteCode),
    };
    if (supplierCode) {
      params.supplierCode = supplierCode[0];
    }
    setBtnLoading(true);
    try {
      await createGpsOrder(params);
      message.success('安装下单成功！');
      setBtnLoading(false);
      props.onSuccess();
    } catch (res) {
      if (res?.ret === 30019) {
        // 未知异常，关闭弹窗通知父组件轮询结果
        props.onSuccess();
      }
      setBtnLoading(false);
    }
  };

  useMemo(() => {
    if (defaultData && open) {
      const { productList } = defaultData;
      const options = productList.map((item) => {
        return {
          label: item.productName,
          value: item.productId,
        };
      });
      setProductList(options);
      // 只有一条默认选中
      if (options?.length && options.length === 1) {
        form.setFieldsValue({
          productId: options[0],
        });
      }
    }
  }, [defaultData, open]);

  return (
    <ModalForm<OrderForm>
      title="下单安装GPS设备"
      form={form}
      layout="horizontal"
      open={open}
      grid
      rowProps={{
        gutter: [16, 0],
      }}
      initialValues={{
        carModel: defaultData?.carModel,
        vin: defaultData?.vin,
        appointTime: dayjs(),
      }}
      modalProps={{
        width: 1100,
        destroyOnClose: true,
        maskClosable: false,
        onCancel: props.onCancel,
        okButtonProps: {
          loading: btnLoading,
        },
      }}
      onFinish={onSubmit}
    >
      <ProForm.Group title="基础信息">
        <ProFormSelect
          required
          colProps={{ span: 7 }}
          labelCol={{ span: 8 }}
          name="productId"
          label="产品设备"
          options={productList}
          fieldProps={{
            labelInValue: true,
          }}
          rules={[{ required: true }]}
        />
        <ProFormText
          required
          colProps={{ span: 9 }}
          labelCol={{ span: 8 }}
          name="contactName"
          label="安装联系人"
          fieldProps={{
            maxLength: 50,
            autoComplete: 'on',
          }}
          rules={[{ required: true }]}
        />
        <ProFormText
          required
          colProps={{ span: 8 }}
          labelCol={{ span: 9 }}
          name="contactPhone"
          label="安装联系人电话"
          fieldProps={{
            maxLength: 50,
            autoComplete: 'on',
          }}
          rules={[{ required: true }, { pattern: pattern.phone, message: '格式错误' }]}
        />
        <Col span={7}>
          <Form.Item
            required
            label="施工地点"
            name="siteCode"
            labelCol={{ span: 8 }}
            rules={[{ required: true }]}
          >
            <CityPicker level={2} />
          </Form.Item>
        </Col>
        <ProFormText
          required
          colProps={{ span: 9 }}
          labelCol={{ span: 8 }}
          name="installationSite"
          label="施工地点地址"
          fieldProps={{
            maxLength: 200,
            autoComplete: 'on',
          }}
          rules={[{ required: true }]}
        />
        <ProFormDateTimePicker
          required
          name="appointTime"
          label="安装时间"
          colProps={{ span: 8 }}
          labelCol={{ span: 9 }}
          width="md"
          rules={[{ required: true }]}
        />
      </ProForm.Group>
      <ProForm.Group title="车辆信息">
        <ProFormText
          required
          colProps={{ span: 7 }}
          labelCol={{ span: 8 }}
          name="carModel"
          label="车型"
          rules={[{ required: true }]}
          disabled={!!defaultData?.carModel}
          fieldProps={{
            autoComplete: 'on',
          }}
        />
        <ProFormText
          required
          colProps={{ span: 9 }}
          labelCol={{ span: 8 }}
          name="vin"
          label="车架号"
          rules={[{ required: true }]}
          disabled={!!defaultData?.vin}
          fieldProps={{
            autoComplete: 'on',
          }}
        />
      </ProForm.Group>
      {!orderNo && (
        <ProForm.Group title="GPS供应商">
          <ProFormCheckbox.Group
            name="supplierCode"
            colProps={{ span: 7 }}
            labelCol={{ span: 8 }}
            label="GPS供应商"
            options={[{ label: '中瑞平台', value: 'ZhongRui' }]}
          />
        </ProForm.Group>
      )}
      <ProForm.Group title="备注信息">
        <ProFormTextArea
          label="备注"
          name="remark"
          width="lg"
          labelCol={{ span: 5 }}
          fieldProps={{ rows: 4, maxLength: 500 }}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default AddNewOrderModal;
