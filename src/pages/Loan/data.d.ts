/*
 * @Author: your name
 * @Date: 2020-11-23 17:16:51
 * @LastEditTime: 2024-04-11 14:51:10
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/data.d.ts
 */
export interface LeanListParams {
  endApplyTime?: string; // 申请单号
  fundFlow?: boolean; //
  lendingMaster?: string;
  lendingModel?: number;
  lendingNo?: string;
  orderNo?: string;
  receiptMaster?: string;
  applyTime?: string;
  states?: number;
  type?: number;
  current?: number;
  pageSize?: number;
  productCode?: string;
  productSecondCode?: string;
}
export interface LoanExportPages {
  current?: number;
  pageSize?: number;
}
export interface LoanListItem {
  amount: number;
  applyTime: string;
  fundFlow: boolean;
  lendingMaster: string;
  lendingModel: number;
  lendingNo: string;
  orderNo: string;
  receiptMaster: string;
  status: number;
  type: number;
  debtNo: string;
  productName: string;
  productCode: string;
  productSecondCode: string;
  remark: string;
  unsubmittedAlert: number;
  funderChannelCode: string;
}

export interface LeanListPagination {
  total: number;
  pageSize: number;
  current: number;
}
export interface LeanListData {
  list: LoanListItem[];
  pagination: Partial<LeanListPagination>;
}

export interface LendingParams {
  lendingNo: string[];
  type: number;
  token: number;
}

export interface PopParams {
  curAmount: number;
  items: number;
  tips: string;
  extraInfo?: any;
}

// 融租详情
export interface AddApplyDataParams {
  businessInsurance?: string[];
  carPurchaseInvoice?: string[];
  carRegistration?: string[];
  drivingLicense?: string[];
  gpsSysScreenshot?: string[];
  lendingNo?: string;
  orderNo?: string;
  otherEnclosure?: string[];
  otherMsg?: string;
  purchaseTaxReceipt?: string[];
  trafficInsurance?: string[];
  useNo?: string;
  lendingStatus?: number;
  notarizationStatus: number;
}

// 融租操作放款

export interface LeaseLendingParams {
  auditNo?: number;
  auditType?: number;
  lendingNos?: any[];
  auditResult?: number;
  productCode: string;
  remark?: string;
  gpsLessOrderNos?: any[];
  passOrderNos?: any[];
}

export interface DataInfo {
  amount: ReactNode;
  orderNo: any;
  lendingNo: any;
  status: number;
  type: number;
}

// 上传 interface
export interface UploadData {
  filePath?: string;
  name?: string;
  uid?: string;
  url?: string;
}

// 线下放款提交 interface
export interface ApplymoneyOffline {
  lendingNo?: string;
  orderNo?: string;
  otherEnclosure?: UploadData[];
  otherMsg?: string;
  userNo?: string;
}

export enum EOcrFileType {
  CAR_PURCHASE_INVOICE = 1, // 购车发票
  PURCHASE_TAX_RECEIPT = 2, // 购置税票
  DRIVING_LICENSE = 3, // 行驶证
  CAR_REGISTRATION = 4, // 车辆登记证
  TRAFFIC_INSURANCE = 5, // 交强险单
  BUSINESS_INSURANCE = 6, // 商业保险
  GPS_SYS_SCREENSHOT = 7, // gps系统截图
  OTHER_ENCLOSURE = 8, // 其它附件
  OTHER_MSG = 9, // 其它备注消息
  LETTER_OF_GUARANTEE = 10, // 担保确认函
}

export type UOcrFileType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
