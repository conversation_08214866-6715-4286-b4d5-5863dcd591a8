export enum CAR {
  NEW = 1,
  OLD = 2,
}

export const CAR_TYPE = [
  { label: '新车', value: CAR.NEW },
  { label: '二手车', value: CAR.OLD },
];

export const LICENSE_TYPE_NAME: Record<string, string> = {
  1: '挂靠',
  2: '个户',
};

export enum LICENSE_TYPE {
  COMPANY = 1,
  PERSONAL = 2,
}

export enum OPTIONS_STATUS {
  ENABLE = 1,
  DISABLED = 2,
}

// 新车层级索引
export const NEW_TREE_NAME = [
  'cityName',
  'manufacturer',
  'carModel',
  'carYears',
  'carEmission',
  'seatsNum',
  'color',
  'otherMsg',
];
// 二手车树索引
export const OLD_TREE_NAME = [
  'cityName',
  'manufacturer',
  'carModel',
  'carYears',
  'carEmission',
  'seatsNum',
  'color',
  'otherMsg',
  'firstRegisterDate',
  'mileage',
  'licenseCode',
];

export const newCarData = {
  groupName: '请确认车辆信息',
  groupDesc: '输入车型码，自动关联其他车辆信息更方便',
  child: [
    {
      name: 'cityName',
      label: '库存城市',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'manufacturer',
      label: '厂商',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'carModel',
      label: '车型',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'carYears',
      label: '年代款',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'carEmission',
      label: '排量',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'seatsNum',
      label: '座位数',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'color',
      label: '颜色',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'otherMsg',
      label: '其他',
      type: 'select',
    },
    {
      name: 'carCode',
      label: '车型码',
      type: 'input',
      disableView: true,
    },
  ],
};

export const usedCarData = {
  groupName: '请确认车辆信息',
  groupDesc: '输入车辆识别代码，自动关联其他车辆信息更方便',
  child: [
    {
      name: 'cityName',
      label: '库存城市',
      type: 'select',
    },
    {
      name: 'manufacturer',
      label: '厂商',
      type: 'select',
    },
    {
      name: 'carModel',
      label: '车型',
      type: 'select',
    },
    {
      name: 'carYears',
      label: '年代款',
      type: 'select',
    },
    {
      name: 'carEmission',
      label: '排量',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'seatsNum',
      label: '座位数',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'color',
      label: '颜色',
      type: 'select',

      rules: [{ required: true, message: '必填' }],
    },
    {
      name: 'otherMsg',
      label: '其他',
      type: 'select',
    },
    {
      name: 'firstRegisterDate',
      label: '首次上牌日期',
      type: 'select',
    },
    {
      name: 'mileage',
      label: '里程数(公里)',
      type: 'select',
    },
    {
      name: 'licenseCode',
      label: '车牌',
      type: 'select',
    },
    {
      name: 'carCode',
      label: '车辆识别代码',
      type: 'input',
      disableView: true,
    },
  ],
};
