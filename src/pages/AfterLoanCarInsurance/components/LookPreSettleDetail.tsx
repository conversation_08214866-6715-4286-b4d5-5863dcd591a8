/* eslint-disable no-param-reassign */
/* eslint-disable react-hooks/exhaustive-deps */
// import { EcarStatus } from '@/utils/bankend/enum';
import { EcarStatus } from '@/utils/bankend/enum';
import { Space, Tooltip } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo, useState } from 'react';
import type { IremissionDetailItem, IrepaymentInfoItem, TreduceFeeMap } from '../types';

type HomeProps = {
  carPolicyTermRspBOS: IrepaymentInfoItem[];
  preSettleAmount: number; // 后端算好的提前结清应还的金额
  onChange?: (repayAmount: number) => void;
  preSettleDamages: number; // 违约金利率
  remissionDetailList: IremissionDetailItem[]; // 减免金额
  flush: any;
};
const LookPreSettleDetail: React.FC<HomeProps> = (props) => {
  const { carPolicyTermRspBOS, preSettleAmount, preSettleDamages, remissionDetailList } = props;
  const [repayAmount, setRepayAmount] = useState(0);
  useEffect(() => {
    const remissionAmount = remissionDetailList.reduce((pre, cur) => {
      return pre + cur.remissionAmount!;
    }, 0);
    // 实际还款金额 = 提前结清金额 - 减免金额
    const ampunt = new BigNumber(preSettleAmount - remissionAmount).toFixed(2);
    props?.onChange?.(Number(ampunt));
    setRepayAmount(Number(ampunt));
  }, [preSettleAmount, props.flush]);

  const title = useMemo(() => {
    const amount = carPolicyTermRspBOS.reduce(
      (pre, cur) => {
        const { status } = cur;
        const {
          remainingPrincipal,
          remainingOverdueInterest,
          remainingOverdueLatePaymentFee,
        } = pre;
        // 提前结清的金额只需要算 逾期和待还款的
        if (status === EcarStatus.PENDING_REPAYMENT || status === EcarStatus.OVERDUE) {
          // 剩余未还利息只需算到最新一期
          return {
            remainingPrincipal: remainingPrincipal + Number(cur.remainingPrincipal),
            remainingOverdueInterest:
              remainingOverdueInterest + Number(cur.remainingOverdueInterest),
            remainingOverdueLatePaymentFee:
              remainingOverdueLatePaymentFee + Number(cur.remainingOverdueLatePaymentFee),
          };
        }
        return {
          remainingPrincipal,
          remainingOverdueInterest,
          remainingOverdueLatePaymentFee,
        };
      },
      {
        remainingPrincipal: 0,
        remainingOverdueInterest: 0,
        remainingOverdueLatePaymentFee: 0,
      },
    );

    // 利息算往期和延后一期的
    // 如果是 14 号  延后一期就是 05-15
    // 今天是 15 号  延后一期就是 05-15
    // 如果是 16 号  延后一期就是 06-15
    // 理论上后端是按照顺序返回的 先按照期数从小到大排序
    carPolicyTermRspBOS.sort((a, b) => Number(a.term) - Number(b.term));
    const currentTimeStamp = dayjs().valueOf();
    const totalTerm = [];
    for (let i = 0; i < carPolicyTermRspBOS.length; i++) {
      const item = carPolicyTermRspBOS[i];
      const repayTimeStamp = dayjs(`${item.repayTime} 24:00:00`).valueOf();
      // 这里的日期不可能正好等于 24:00:00 不需要考虑
      if (currentTimeStamp > repayTimeStamp) {
        // 往期的
        totalTerm.push(item);
      }
      if (currentTimeStamp < repayTimeStamp) {
        totalTerm.push(item);
        // 延后一期的
        break;
      }
    }

    const remainingInterest = totalTerm
      .filter((car) => [EcarStatus.PENDING_REPAYMENT, EcarStatus.OVERDUE].includes(car.status))
      .reduce((pre, cur) => pre + Number(cur.remainingInterest), 0);

    // 剩余未还本金 剩余未还逾期罚息 剩余未还逾期滞纳金 算所有期的
    const { remainingPrincipal, remainingOverdueInterest, remainingOverdueLatePaymentFee } = amount;

    const remissMap: Partial<Record<TreduceFeeMap, number>> = {};
    remissionDetailList.forEach((item) => {
      remissMap[item.costType!] = item.remissionAmount;
    });

    return (
      <div>
        <div>
          本金¥
          {new BigNumber(remainingPrincipal - (remissMap[4]! || 0)).toFixed(2)}
        </div>
        <div>
          利息¥
          {new BigNumber(remainingInterest - (remissMap[1] || 0)).toFixed(2)}
        </div>
        <div>
          提前结清违约金¥
          {new BigNumber(remainingPrincipal * preSettleDamages).toFixed(2)}
        </div>
        <div>
          逾期罚息¥
          {new BigNumber(remainingOverdueInterest - (remissMap[3] || 0)).toFixed(2)}
        </div>
        <div>
          逾期滞纳金¥
          {new BigNumber(remainingOverdueLatePaymentFee - (remissMap[5] || 0)).toFixed(2)}
        </div>
      </div>
    );
  }, [carPolicyTermRspBOS, remissionDetailList]);
  return (
    <Space>
      <a>¥ {repayAmount}</a>
      <Tooltip title={title}>
        <a>查看明细</a>
      </Tooltip>
    </Space>
  );
};
export default memo(LookPreSettleDetail);
