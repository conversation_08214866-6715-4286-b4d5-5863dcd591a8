import { forwardRef, useImperativeHandle, useState } from 'react';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { ProCard, ProFormTextArea } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Form, message, Spin } from 'antd';
import { getCallFailRecord, submitRiskPatch } from '../../../service';
import style from './index.less';

type CallFailProps = {
  orderNo: string;
  riskOrderNo: string;
  isLook: boolean;
  onSubmit: () => void;
};
//反馈信息状态 0-未反馈 1-已反馈
enum FEEDBACK_STATUS {
  NOT = 0,
  DONE = 1,
}

const CallFail = forwardRef((props: CallFailProps, ref) => {
  const { orderNo, riskOrderNo, isLook } = props;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [readOnly, setReadOnly] = useState(false);

  const { data: rejectInfo } = useRequest(() => {
    return getCallFailRecord({ riskOrderNo, status: FEEDBACK_STATUS.NOT });
  });

  const handleSubmit = async () => {
    await form.validateFields();
    const values = form.getFieldsValue();
    const { remark } = values;
    const params = {
      orderNo,
      riskSupplementReqList: [
        {
          id: rejectInfo[0].id,
        },
      ],
      remark,
    };
    setLoading(true);
    submitRiskPatch(params)
      .then(() => {
        message.success('提交成功！');
        setReadOnly(true);
        props.onSubmit();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: () => {
      handleSubmit();
    },
  }));

  return (
    <Spin spinning={loading}>
      {!isLook && (
        <ProCard
          title="电联失败"
          bordered
          headerBordered
          collapsible
          bodyStyle={{ paddingTop: 30 }}
        >
          <Form
            form={form}
            style={{ width: 800, margin: 'auto' }}
            labelWrap={true}
            layout="horizontal"
          >
            {rejectInfo?.[0]?.riskRefuseReason && (
              <div className={style.rejectMsg}>
                <ExclamationCircleOutlined style={{ color: '#FFC87A', marginRight: 4 }} />
                电联记录: {JSON.parse(rejectInfo?.[0]?.riskRefuseReason)?.join('、')}
              </div>
            )}
            <ProFormTextArea
              label="备注"
              name="remark"
              disabled={readOnly}
              fieldProps={{ maxLength: 300, showCount: true }}
            />
          </Form>
        </ProCard>
      )}
    </Spin>
  );
});

export default CallFail;
