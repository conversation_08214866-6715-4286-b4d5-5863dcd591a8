import { useAccess } from '@umijs/max';
import { <PERSON><PERSON>, Col, Form, message, Modal, Row } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { DetailInfo, ListItem, RefundTypeEnum, refundTypeEnumMap } from '../const';
import '../index.less';
import { cancellationRelaunch } from '../service';
import BasicInfo from './BasicInfo';
import RefundAllocationFormItem from './RefundAllocationFormItem';
import UploadPannel from './UploadPannel';

interface ModalProps {
  closeModal: (refresh?: boolean) => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  detailInfo: DetailInfo;
  bankSearchProps: any;
}

const Page: React.FC<ModalProps> = ({
  visible,
  closeModal,
  currentUser,
  record,
  detailInfo,
  bankSearchProps,
}) => {
  const { orderNo, bizNo } = record;
  const [submitLoading, setSubmitLoading] = useState(false);
  const access = useAccess();
  //
  const uploadRef = useRef<any>();
  const [uploadLoading, setUploadLoading] = useState(false);
  const handleUploadSubmit = async () => {
    setUploadLoading(true);
    const res: any = await uploadRef?.current?.submitUpload().catch(() => {});
    console.log(res);
    setUploadLoading(false);
  };
  //
  const { asyncBankListBankNoMap } = bankSearchProps;
  //
  const [formOfRefundFail] = Form.useForm();
  // 表单回显
  const handleInitValuesOfRefundFail = async () => {
    formOfRefundFail.setFieldsValue({
      //
      yirenxingRefundAmount: detailInfo?.yirenxingRefundAmount,
      //
      userRefundAmount: detailInfo?.userRefundAmount,
      accountName: detailInfo?.userBankInfo?.accountName,
      bankCardNo: detailInfo?.userBankInfo?.bankCardNo,
      bankNo: detailInfo?.userBankInfo?.bankNumber,
    });
  };
  // 重新发起
  const handleRefundFailSubmit = async () => {
    try {
      await formOfRefundFail.validateFields();
    } catch {
      return Promise.reject();
    }
    const formValues = formOfRefundFail.getFieldsValue();
    const currentUserBankInfo = asyncBankListBankNoMap[formValues?.bankNo] || {};
    setSubmitLoading(true);
    const data: any = {
      bizNo,
      createdBy: currentUser?.accountName,
      // 客户
      accountName: formValues?.accountName,
      bankCardNo: formValues?.bankCardNo,
      // 客户支行信息
      subBranchName: currentUserBankInfo?.subBranchName,
      bankName: currentUserBankInfo?.bankName,
      bankNumber: currentUserBankInfo?.bankNo,
    };
    console.log('data', data);
    //
    const res: any = (await cancellationRelaunch(data).catch(() => {})) || {};
    setSubmitLoading(false);
    if (res?.success) {
      message.success('操作成功');
      closeModal(true);
    }
  };
  useEffect(() => {
    if (visible) {
      handleInitValuesOfRefundFail();
    }
  }, [visible]);
  return (
    <>
      <Modal
        width={960}
        destroyOnClose
        title={`退保详情`}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            {access?.hasAccess('upload_file_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" onClick={handleUploadSubmit} loading={uploadLoading} ghost>
                保存附件
              </Button>
            )}
            {access?.hasAccess('relaunch_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" loading={submitLoading} onClick={handleRefundFailSubmit}>
                重新发起
              </Button>
            )}
          </>
        }
      >
        {/*  */}
        <BasicInfo detailInfo={detailInfo} orderStatus={detailInfo.orderStatus} />
        <UploadPannel
          ref={uploadRef}
          detailInfo={detailInfo}
          defaultFileList={detailInfo?.fileInfoAttach ? detailInfo?.fileInfoAttach : []}
        />
        {/*  */}
        <Form
          form={formOfRefundFail}
          labelCol={{ span: 3 }}
          labelAlign="left"
          wrapperCol={{ span: 18 }}
        >
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <span className="detail-label">退款分配：</span>
              <span>
                {refundTypeEnumMap[detailInfo?.refundAllocation as RefundTypeEnum] || '-'}
              </span>
            </Col>
          </Row>
          {/* 可编辑表格 */}
          <RefundAllocationFormItem
            refundAllocation={detailInfo?.refundAllocation}
            detailInfo={detailInfo}
            bankSearchProps={bankSearchProps}
            formInstance={formOfRefundFail}
            showRefundResult={true}
            disabledInput={!access?.hasAccess('relaunch_biz_businessMng_postLoanMng_cancellation')}
          />
        </Form>
      </Modal>
    </>
  );
};
export default Page;
