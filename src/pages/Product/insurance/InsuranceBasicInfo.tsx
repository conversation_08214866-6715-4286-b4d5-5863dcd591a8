import { CLASSIFICATION, SECONDARY_CLASSIFICATION } from '@/enums';
import globalStyle from '@/global.less';
import { useModel } from '@umijs/max';
import { Card, Col, Form, Input, Row, Select } from 'antd';
import React, { useEffect } from 'react';

const BasicInfo = () => {
  const [form] = Form.useForm();
  const { detailData } = useModel('Product.insuranceProduct');
  useEffect(() => {
    const { secondaryClassification, classification, productName } = detailData;
    form.setFieldsValue({
      secondaryClassification,
      classification,
      productName,
    });
  }, [detailData, form]);
  return (
    <Card title="基础信息" className={globalStyle.mt30}>
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }} name={'basicInfo'}>
        <Row gutter={20}>
          <Col span={10} offset={1}>
            <Form.Item label="产品一级分类" name="classification">
              <Select placeholder="保理" disabled>
                {Object.keys(CLASSIFICATION).map((key) => (
                  <Select.Option key={key} value={key}>
                    {CLASSIFICATION[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="secondaryClassification" label="产品二级分类">
              <Select placeholder="明保" disabled>
                {Object.keys(SECONDARY_CLASSIFICATION).map((key) => (
                  <Select.Option key={key} value={key}>
                    {SECONDARY_CLASSIFICATION[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="productName" rules={[{ required: true }]} label="产品名称">
              <Input maxLength={50} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default React.memo(BasicInfo);
