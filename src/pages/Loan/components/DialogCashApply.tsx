import { ModalForm } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Button, message } from 'antd';
import React, { useMemo } from 'react';
import { addCashApplyData } from '../service';

export type DialogCashApplyProps = {
  status: number | undefined;
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  refresh: () => void;
};

const DialogCashApply: React.FC<DialogCashApplyProps> = ({
  status,
  visible,
  onVisibleChange,
  refresh,
}) => {
  const { lendingNo } = history.location.query as { lendingNo: string };
  const cashApplyVisible = useMemo(() => [0, 3, 12].includes(status as number), [status]);
  const contentSty = {
    fontSize: '14px',
  };

  return (
    <ModalForm
      title={cashApplyVisible ? '请款' : '提示'}
      layout="horizontal"
      visible={visible}
      width={520}
      onVisibleChange={(value) => {
        onVisibleChange(value);
      }}
      onFinish={async () => {
        try {
          await addCashApplyData(lendingNo);
          refresh();
          message.success('请款操作成功');
          onVisibleChange(false);
        } catch (error) {
          message.info('稍后重试');
        }

        return true;
      }}
      submitter={{
        render: (props) => {
          const actions = cashApplyVisible
            ? [
                <Button
                  key="ok"
                  type="primary"
                  onClick={() => {
                    props.submit();
                    onVisibleChange(false);
                  }}
                >
                  确定
                </Button>,
                <Button
                  key="cancel"
                  onClick={() => {
                    onVisibleChange(false);
                  }}
                >
                  取消
                </Button>,
              ]
            : [
                <Button
                  key="cancel"
                  type="primary"
                  onClick={() => {
                    onVisibleChange(false);
                  }}
                >
                  确定
                </Button>,
              ];

          return actions;
        },
      }}
    >
      {cashApplyVisible && <p style={contentSty}>是否确认请款？</p>}
      {!cashApplyVisible && <p style={contentSty}>仅待请款、放款失败、驳回时支持提交请款</p>}
    </ModalForm>
  );
};

export default DialogCashApply;
