/*
 * @Author: your name
 * @Date: 2021-09-16 20:04:03
 * @LastEditTime: 2023-03-22 17:51:52
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/AuthorizedChange.tsx
 */
import ProForm, { ProFormDatePicker, ProFormDigit, ProFormTextArea } from '@ant-design/pro-form';
import { Button, Form, message, Modal } from 'antd';
import React, { useState } from 'react';
// import dayjs from 'dayjs';
import { DividerTit, ShowInfo } from '@/components';
import { CommonImageUpload } from '@/components/ReleaseCom';
import globalStyle from '@/global.less';
import { convertUploadFileList } from '@/utils/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import billDetailMngStyle from '../index.less';
import { manualRepay } from '../service';

interface ManualRepayModalProps {
  title: string;
  visible: boolean;
  onCancel: () => void;
  refresh: () => void;
  onVisibleChange?: any;
  billInfo: Record<string, any>;
}

const ManualRepayModal: React.FC<ManualRepayModalProps> = (props) => {
  const { title, visible, onCancel, billInfo } = props;
  const { accountNumber, billNo } = history?.location?.query;
  // const { data: formAuth, run } = useRequest(() => {
  //   return getAuthInfo({ enterpriseId: userNo, productSecondTypeCode: '0101' });
  // });

  // const previewPDF = (url: string, name: string) => {
  //   // getOssPath(url).then((res) => {
  //   getBlob(url, (blob: Blob) => {
  //     const exc = name.substring(name.lastIndexOf('.') + 1);
  //     if ('.jpg.jpeg.gif'.includes(exc)) {
  //       previewAS(blob, 'image/jpeg;chartset=UTF-8');
  //     } else if (exc === 'pdf') {
  //       previewAS(blob);
  //     } else {
  //       saveAs(blob, name);
  //     }
  //     // });
  //   });
  // };
  const [loading, setLoading] = useState<boolean>(false);
  const [allFileList, handleFileList] = useState<any>({});
  // console.log(allFileList);
  const [form] = Form.useForm();

  const mapFileList = (allFile: any) => {
    handleFileList(allFile);
  };
  const billInfoMap = {
    accountName: '企业名称',
    accountNumber: '用户ID',
    billNo: '账单ID',
    pendingAmount: '待还款金额(元)',
    desc: '账单状态',
    billCycle: '账单周期',
    repaymentDate: '还款日',
  };
  const submitReapy = async (values: any) => {
    const mapUploadFile = convertUploadFileList(allFileList, ['attachList']);
    // console.log({ ...values, ...mapUploadFile, userNo: accountNumber, orderNo: billNo });
    setLoading(true);
    await manualRepay({
      ...values,
      ...mapUploadFile,
      userNo: accountNumber,
      orderNo: billNo,
    })
      .then(() => {
        setLoading(false);
        message.success('操作成功');
        onCancel();
        props.refresh();
      })
      .catch(() => {
        setLoading(false);
      });
    handleFileList({});
  };
  const openConfirm = (values: any, content: React.ReactNode) => {
    Modal.confirm({
      title: '提示',
      icon: <ExclamationCircleOutlined />,
      content: content,
      centered: true,
      okText: '确认提交',
      cancelText: '取消',
      onOk: () => {
        submitReapy(values);
      },
    });
  };
  return (
    <Modal
      destroyOnClose
      centered
      title={title}
      open={visible}
      onCancel={() => onCancel()}
      width={800}
      maskClosable={false}
      // onVisibleChange={onVisibleChange}
      afterClose={() => {
        handleFileList({});
        form.resetFields();
      }}
      footer={null}
    >
      <div>
        <DividerTit title="账单信息" style={{ marginTop: 0 }}>
          <ShowInfo
            noCard
            rowSpan={12}
            infoMap={billInfoMap}
            // itemMap={itemBillMap}
            data={billInfo}
          />
        </DividerTit>
        <DividerTit title="变更信息" />
        <ProForm
          className={`${billDetailMngStyle.formModal} ${globalStyle.mt20}`}
          layout="horizontal"
          initialValues={{ modifyDirection: 0, dataInfo: { type: 1 } }}
          form={form}
          submitter={{
            render: (propsSubmitter) => (
              <div className="buttonCss">
                <Button
                  type="primary"
                  key="submit"
                  loading={loading}
                  onClick={() => propsSubmitter.form?.submit?.()}
                >
                  提交
                </Button>
                <Button key="rest" onClick={onCancel} className={globalStyle?.ml10}>
                  取消
                </Button>
              </div>
            ),
          }}
          onFinish={async (values: Record<string, any>) => {
            // console.log(allFileList);
            // await waitTime(2000);
            if (billInfo?.pendingAmount === values?.repayMoney) {
              submitReapy(values);
            } else if (billInfo?.pendingAmount > values?.repayMoney) {
              const content = (
                <>
                  账单待还金额为
                  <span className={billDetailMngStyle.colorRed}>{billInfo?.pendingAmount}</span>
                  元,本次人工后台还款为
                  <span className={billDetailMngStyle.colorRed}>
                    {values?.repayMoney.toFixed(2)}
                  </span>
                  元，是否确认提交?
                </>
              );
              openConfirm(values, content);
            } else {
              const content = (
                <>
                  账单待还金额为
                  <span className={billDetailMngStyle.colorRed}>{billInfo?.pendingAmount}</span>
                  元,本次人工后台还款为
                  <span className={billDetailMngStyle.colorRed}>
                    {values?.repayMoney.toFixed(2)}
                  </span>
                  元，提交后将产生
                  <span className={billDetailMngStyle.colorRed}>
                    {(values?.repayMoney - billInfo?.pendingAmount).toFixed(2)}
                  </span>
                  元溢缴款，是否确认提交?
                </>
              );
              openConfirm(values, content);
            }
          }}
        >
          <ProFormDigit
            label="还款金额（元）"
            rules={[{ required: true }]}
            width="sm"
            fieldProps={{ precision: 2 }}
            name="repayMoney"
          />
          <ProFormDatePicker
            label="还款日期"
            rules={[{ required: true }]}
            width="sm"
            name="repayTime"
          />
          <CommonImageUpload
            extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
            label="附件"
            name="attachList"
            max={5}
            listType="text"
            size={10}
            fileListEdit={allFileList?.attachList || []}
            desPath="EP_AUTH_INFO"
            mapFileList={mapFileList}
            accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
          />
          <ProFormTextArea
            width="lg"
            label="备注"
            name="remark"
            fieldProps={{ maxLength: 500, showCount: true }}
          />
        </ProForm>
      </div>
    </Modal>
  );
};
export default ManualRepayModal;
