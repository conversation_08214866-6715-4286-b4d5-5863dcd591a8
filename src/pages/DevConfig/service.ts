/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2022-01-07 13:50:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
// import { request } from '@umijs/max';
// import { getSSOCookie } from '@/utils/auth';
import { getWorkerApiHost, workerRequest } from '@/utils/bike';
import type { CardItem } from './data';

// bike
export async function getBizSSOGrayInfo() {
  return workerRequest(`${getWorkerApiHost()}/getBizSSOGrayInfo`, {
    method: 'POST',
    hideToast: true,
  });
}
export async function batchUpdateBizSSOGrayInfo(list: CardItem[]) {
  return workerRequest(`${getWorkerApiHost()}/batchUpdateBizSSOGrayInfo`, {
    method: 'POST',
    data: { list },
    hideToast: true,
  });
}
//
export async function fetchDevAccess() {
  return workerRequest(`${getWorkerApiHost()}/getDevAccess`, {
    method: 'POST',
    hideToast: true,
  });
}

// van
export async function fetchVanCanary(data: { ssoCookie: string }) {
  return workerRequest(`${getWorkerApiHost()}/getBizVanCanary`, {
    method: 'POST',
    data,
  });
}
