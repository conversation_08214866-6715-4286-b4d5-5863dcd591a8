import { request } from '@umijs/max';

// 1、获取验证码开关状态
export async function fetchCaptchaSwitchStatus() {
  return request('/bizadmin/auth/queryLoginGray', {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// 2、配合验证码的新登录接口
export async function newLoginAfterCaptcha(data: any) {
  return request('/bizadmin/auth/loginPassword', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}
