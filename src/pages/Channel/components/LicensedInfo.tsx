/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-10-18 16:18:42
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-23 19:53:31
 * @FilePath: /lala-finance-biz-web/src/pages/Channel/components/LicensedInfo.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { LICENSE_TYPES, LICENSE_TYPES_MAP } from '@/enums';
import globalStyle from '@/global.less';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { But<PERSON>, Card, message, Modal, Table } from 'antd';
import React from 'react';
import { modifyLicenseStatus } from '../service';
import AddLicensedInfo from './AddLicensedInfo';

const STATUS_LABEL = {
  1: '启用',
  0: '禁用',
};

const LicensedInfo: React.FC<{
  licenseRspList: any[];
  refresh: () => void;
  currentLicenseType: string;
}> = ({ licenseRspList, refresh, currentLicenseType }) => {
  const channelId = history?.location?.query?.id as string;
  // const { tableProps, refresh } = useRequest(
  //   () => {
  //     return getLicenseList({ channelId });
  //   },
  //   {
  //     paginated: true,
  //     defaultPageSize: 10,
  //     formatResult: (response) => {
  //       return { list: response.data, total: response.total };
  //     },
  //   },
  // );
  const columns = [
    {
      title: '上牌类型',
      key: 'licenseType',
      dataIndex: 'licenseType',
      render: (licenseType: number) => {
        return LICENSE_TYPES_MAP[licenseType] || '-';
      },
    },
    {
      title: '上牌公司',
      key: 'licenseCompany',
      dataIndex: 'licenseCompany',
      render: (licenseCompany: string) => licenseCompany || '-',
    },
    {
      title: '上牌城市',
      key: 'licenseCityList',
      dataIndex: 'licenseCityList',
      render: (licenseCityList: any[]) => {
        return licenseCityList?.map((item) => item?.licenseCity).join('、') || '-';
      },
    },
    {
      title: '统一信用代码',
      key: 'orgCode',
      dataIndex: 'orgCode',
      render: (orgCode: string) => orgCode || '-',
    },
    {
      title: '联系电话',
      key: 'phone',
      dataIndex: 'phone',
      render: (phone: string) => phone || '-',
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (status: number) => {
        return STATUS_LABEL[status] || '-';
      },
    },
    {
      title: '操作',
      key: 'option',
      // dataIndex: '',
      render: (_, row) => {
        // console.log(row);
        return (
          <>
            <a
              onClick={() => {
                optimizationModalWrapper(AddLicensedInfo)({
                  title: '编辑',
                  curRow: row,
                  refresh,
                  channelId,
                });
              }}
            >
              编辑
            </a>
            {/* 个户隐藏禁用/启用按钮 */}
            {row?.licenseType != LICENSE_TYPES.CUSTOMER && (
              <a
                className={globalStyle.ml10}
                onClick={() => {
                  Modal.confirm({
                    title: `你确定${row?.status === 0 ? '启用' : '禁用'}${
                      row?.licenseCompany || ''
                    }?`,
                    icon: <ExclamationCircleOutlined />,
                    // content: 'Some descriptions',
                    okText: '确认',
                    okType: 'danger',
                    cancelText: '取消',
                    onOk: async () => {
                      await modifyLicenseStatus(row?.id, row?.status == 0 ? 1 : 0).then(() => {
                        message.success('修改成功');
                        refresh();
                      });
                    },
                  });
                }}
              >
                {row?.status === 0 ? '启用' : '禁用'}
              </a>
            )}
          </>
        );
      },
    },
  ];

  return (
    <Card
      title="上牌方信息"
      extra={
        <Button
          type="primary"
          onClick={() => {
            optimizationModalWrapper(AddLicensedInfo)({
              title: '新增',
              refresh,
              channelId,
              currentLicenseType,
            });
          }}
        >
          新增
        </Button>
      }
      className={globalStyle.mt20}
    >
      <Table columns={columns} rowKey="id" dataSource={licenseRspList} />
    </Card>
  );
};

export default LicensedInfo;
