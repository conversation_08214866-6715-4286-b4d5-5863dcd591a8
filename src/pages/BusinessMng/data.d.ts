/*
 * @Author: your name
 * @Date: 2020-11-23 16:54:48
 * @LastEditTime: 2023-03-28 17:22:36
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/data.d.ts
 */

export interface OrderListItem {
  orderAmount: string;
  orderNo: string;
  orderTime: number;
  productName: string;
  status: string;
  userName: number;
  userNo: string;
}

export interface OrderListParams {
  channel?: string;
  classification?: string;
  current?: number;
  endApplyTime?: string;
  orderNo?: string;
  pageSize?: number;
  productName?: string;
  secondClassification?: string;
  startApplyTime?: string;
  status?: number;
  userName?: string;
}
export interface OrderListPagination {
  total: number;
  pageSize: number;
  current: number;
}

export interface OrderListData {
  list: OrderListItem[];
  pagination: Partial<OrderListPagination>;
}

export interface OrderInfoParams {
  orderNo?: string;
}

export interface OrderInfo {
  orderAmount: string;
  orderChannel: string;
  orderNo: string;
  orderTime: string;
  productName: string;
  status: number;
  userName: string;
  userNo: string;
}

export interface ListRspList {
  amountDue: number;
  cost: number;
  interest: number;
  principal: number;
  repayTime: string;
}

export interface RepayInfo {
  listRspList: ListRspList[];
  repayMode: number;
  repayTerm: number;
}

export interface RepayRegistItem {
  actualRepaymentAmount: number;
  cost: number;
  interest: number;
  penaltyInterest: number;
  principal: number;
  recordingNo: string;
  repayTime: string;
  repayType: string;
}

export interface ProductNameList {
  code: string;
  desc: string;
}

export interface DebtItem {
  dealTime: string;
  loadFromName: string;
  loadToName: string;
  loanAmount: number;
  startingTime: string;
  status: number;
  conversionNo: string;
}

export interface ReleaseFeeItem {
  expenseItem: string;
  receivingNode: string;
  receivingTime: string;
  funders: string;
  receiver: string;
  amount: string;
  chargeMethod: string;
  returnNode: string;
  amountDue: number;
}

export interface RepayItem {
  amountDue: number;
  cost: number;
  interest: number;
  principal: number;
  repayDay: string;
  termDetail: string;
  children?: RepayItem[];
  status: number;
  repayTime: string;
  overdueCaseNo?: string;
  repayPlanNo: string;
}
