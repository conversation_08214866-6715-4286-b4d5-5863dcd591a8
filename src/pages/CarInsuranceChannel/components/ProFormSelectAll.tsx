import { ProFormSelect, ProFormSelectProps } from '@ant-design/pro-components';
import { Checkbox, Divider } from 'antd';
import React, { useState } from 'react';

export type ProFormSelectAllIntance = {};

type ProFormSelectAllProps = ProFormSelectProps & {
  onSelectAllChange: (val: boolean) => void;
};
const ProFormSelectAll: React.FC<ProFormSelectAllProps> = (props) => {
  const { onSelectAllChange, options } = props;
  const [indeterminate, setIndeterminate] = useState(false);
  const [checked, setChecked] = useState(false);

  return (
    <ProFormSelect
      {...props}
      mode="multiple" // 支持全选必须是多选
      fieldProps={{
        ...props?.fieldProps,
        onChange: (values, option: any) => {
          props?.fieldProps?.onChange?.(values, option);
          if (!values?.length) {
            setChecked(false);
            setIndeterminate(false);
            return;
          } else {
            setChecked(options?.length === values.length); // 全选
            setIndeterminate(options?.length !== values.length);
          }
        },
        dropdownRender: (menu) => {
          return (
            <>
              {menu}
              <Divider style={{ margin: '8px 0' }} />
              <Checkbox
                checked={checked}
                indeterminate={indeterminate}
                onChange={(e) => {
                  onSelectAllChange(e.target.checked);
                  setIndeterminate(false);
                  setChecked(e.target.checked);
                }}
              >
                全选
              </Checkbox>
            </>
          );
        },
      }}
    />
  );
};

export default ProFormSelectAll;
