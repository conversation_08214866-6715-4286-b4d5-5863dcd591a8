export type SeimDataParams = {
  from: string; //  日志来源
  hller: string; //  操作人
  hller_name: string; //  操作人名称(中文名)
  ucenter_hller_id: string; //  用户ID
  route: string; //  路由(通常可以以接口路径来标识,如order/order_detail)
  action?: string; //  路由名称(对应route的说明,如查看订单信息)
  url?: string; //  页面URL
  gets?: any; //  本次请求的urlparam,以json的形式
  posts?: any; //  本次http请求的body,可以以json形式上报
  inputs?: any; //  其他的输入信息,可作为补充加入此字段中
};

export type GetSeimDataParams = SeimDataParams & {
  code: string; //  校验码
  data: string; //  需解密的数据
};
