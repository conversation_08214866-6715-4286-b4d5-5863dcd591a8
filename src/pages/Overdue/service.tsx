/*
 * @Author: your name
 * @Date: 2021-01-12 17:01:50
 * @LastEditTime: 2023-07-05 15:05:53
 * @LastEditors: elisa.z<PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/service.tsx
 */

import { headers } from '@/utils/constant';
import { getBlob, saveAs } from '@/utils/utils';
import { request } from '@umijs/max';
// import { getOssPath } from '@/services/global';
import { queryAllUserList } from '@/services/global';
import type {
  AddCallPayParams,
  ApplyReliefParams,
  CallPayParams,
  DispatchParams,
  ICallDetail,
  OverdueParams,
  SubmitCallBackParams,
  WithHoldParams,
} from './data';
// 获取催收记录

export async function getOverdueList(params?: OverdueParams) {
  // /repayment/cms/overdue/management
  return request('/bizadmin/overdue/management', {
    method: 'POST',
    data: { ...params },
    headers,
    ifTrimParams: true,
  });
}
export async function getOverdueListDetail(overdueId?: string) {
  return request(`/repayment/cms/overdue/management/detail/${overdueId}`, {
    method: 'GET',
  });
}

// 催收减免导出
export async function overdueExport(params?: any) {
  return request('/repayment/cms/overdue/management/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

// 催收记录
export async function getCallPayList(params?: CallPayParams) {
  return request('/quota/user/enterprise/list', {
    method: 'GET',
    params: { ...params },
  });
}
// 回款
export async function getCallPayMoneyList(params?: CallPayParams) {
  return request('/quota/user/enterprise/list', {
    method: 'GET',
    params: { ...params },
  });
}
// 回款
// export async function getCollectReliefList(params?: CallPayParams) {
//   return request('/quota/user/enterprise/list', {
//     method: 'GET',
//     params: { ...params },
//   });
// }

// 派单 --已复制
export async function dispatch(data: DispatchParams) {
  return request('/repayment/cms/overdue/management/batch/dispatch', {
    method: 'POST',
    data,
  });
}

// 提交催收回款--已复制
export async function submitCallBack(data: SubmitCallBackParams) {
  return request('/repayment/cms/overdue/management/payment', {
    method: 'POST',
    data,
  });
}

// 申请减免--已复制
export async function applyRelief(data: ApplyReliefParams) {
  return request('/repayment/cms/overdue/management/remission', {
    method: 'POST',
    data,
  });
}

// 添加催收记录
// export async function addCallPay(data: AddCallPayParams) {
export async function addCallPay(data: AddCallPayParams) {
  return request('/repayment/cms/overdue/management/record', {
    method: 'POST',
    data,
  });
}

// 获取用户列表
export async function getUserList() {
  // return request('/auth/operator/privilege/getUserList');
  return queryAllUserList();
}
//已复制
export const downLoad = (url: string, filename: string) => {
  // getOssPath(url).then((res) => {
  getBlob(url, (blob: Blob) => {
    saveAs(blob, filename);
  });
  // });
};

// 代扣记录 --已经copy到collectionservice
export async function withHoldList(params: {
  overdueId?: string; // 催收单号
  current?: number; // 当前页
  pageSize?: number; // 页大小
}) {
  return request('/repayment/cms/overdue/management/withholdRecord', {
    method: 'GET',
    params: { ...params },
  });
}

// 查询代扣信息--已经copy到collectionservice
export async function queryWithHoldInfo(overdueId: string) {
  return request('/repayment/cms/overdue/management/withhold', {
    method: 'GET',
    params: {
      overdueId,
    },
  });
}

// 提交代扣信息--已经copy到collectionservice
export async function postWithHold(data: WithHoldParams) {
  return request('/repayment/cms/overdue/management/postWithhold', {
    method: 'POST',
    data,
  });
}

// 获取行动标识枚举--已经copy到collectionservice
export async function getActionList(businessType: string) {
  return request(`/repayment/cms/overdue/management/callAction/action/${businessType}`);
}

// 外呼动作插入--已经copy到collectionservice
export function postCaller(data: ICallDetail) {
  return request(`/repayment/cms/overdue/management/callAction`, {
    method: 'POST',
    data,
  });
}

// 获取外呼事件详情--已经copy到collectionservice
export async function getCallerDetail(callRecord: string) {
  // return request(`/repayment/cms/overdue/management/callAction/detail/${callRecord}`);
  return request(`/bizadmin/outbound/get/record`, {
    params: { callRecord },
    headers,
  });
}

// 获取外呼录音文件--已经copy到collectionservice
export async function getCallerAudioRecord(callRecord: string) {
  // return request(`/repayment/cms/overdue/management/callAction/record/${callRecord}`);
  return request(`/bizadmin/outbound/get/recording`, {
    params: { callRecord },
    headers,
  });
}

// 添加联系人 --已经copy到collectionservice
export async function addContact(data: any) {
  return request('/repayment/cms/overdue/management/add/contact', {
    method: 'POST',
    data,
  });
}

// 删除联系人 --已经copy到collectionservice
export async function deleteContact(contactId: string) {
  return request(`/repayment/cms/overdue/management/delete/contact?contactId=${contactId}`, {
    method: 'POST',
  });
}

// 查询历史还款记录--已经copy到collectionservice
export async function repayRecording(params?: any) {
  return request('/repayment/cms/overdue/management/repay/recording', {
    method: 'GET',
    params: { ...params },
  });
}
