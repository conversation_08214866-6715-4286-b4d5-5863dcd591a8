/*
 * @Date: 2024-01-27 15:49:15
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-02-21 10:34:29
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/CashProductConfig/SettleEarlyCash.tsx
 * @Description:
 */
import React, { useEffect } from 'react';
import { Row, Col, Form, Radio, Input } from 'antd';
import { ProFormDependency, ProFormText } from '@ant-design/pro-form';
import dayjs from 'dayjs';
import { EARLY_SETTLE_IF_ENUM } from '@/enums';
import './index.less';
type SettleEarlyProps = {
  settleEarlyData?: any;
  name?: string;
};

const SettleEarlyCash: React.FC<SettleEarlyProps> = ({ settleEarlyData, name }) => {
  // const { settleEarlyData } = props;
  const [form] = Form.useForm();
  useEffect(() => {
    if (settleEarlyData) {
      form.setFieldsValue({
        ...settleEarlyData,
        // minPaidTerm1: settleEarlyData.minPaidTerm,
        operatorTime: settleEarlyData.operatorTime
          ? dayjs(settleEarlyData.operatorTime).format('YYYY-MM-DD hh:mm:ss')
          : '-',
      });
    }
  }, [settleEarlyData]);
  return (
    <Form
      form={form}
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 24 }}
      labelAlign="right"
      name={name}
    >
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item name="settleEarly" label="是否允许提前结清">
            <Radio.Group disabled>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>

        <Col span={10} offset={1}>
          <Form.Item name="handlingCharge" label="是否收取违约金">
            <Radio.Group disabled>
              <Radio value={true}>收取</Radio>
              <Radio value={false}>不收取</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        {/* <Col span={10} offset={1}>
          <Form.Item
            name="minPaidTerm"
            label="提前结清时间(已还大于等于x期)"
            labelCol={{ span: 10 }}
          >
            <Input disabled />
          </Form.Item>
        </Col> */}
      </Row>
      <Row>
        <Col span={10} offset={1}>
          <Form.Item name="settleEarlyTimeEnum" label="提前结清发起条件">
            <Radio.Group disabled>
              {Object.keys(EARLY_SETTLE_IF_ENUM).map((key) => (
                <Radio key={key} value={key}>
                  {EARLY_SETTLE_IF_ENUM[key]}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={10} offset={1}>
          <Form.Item name="inAdvanceInterestAccrualByInterestModeEnum" label="往后收取利息">
            <Input disabled className="small-input" value={1} />{' '}
            <span className="margin-l-r-5 grey">期</span>
          </Form.Item>
        </Col>
        <Col span={10} offset={4}>
          <ProFormDependency name={['settleEarlyTimeEnum']}>
            {/* 实际还款方为第三方时需要展示 */}
            {({ settleEarlyTimeEnum }) => {
              return (
                <div className="flx grey">
                  {settleEarlyTimeEnum == 'LOAN_LEND_DATE' ? '距离放款日' : '需还款期数'}
                  <span className="margin-l-r-5">{'>'}</span>
                  <Form.Item name="minPaidTerm">
                    <Input
                      disabled
                      // name="minPaidTerm1"
                      className="small-input"
                      defaultValue={settleEarlyData?.minPaidTerm}
                    />
                  </Form.Item>
                  <span className="margin-l-r-5 ">
                    {settleEarlyTimeEnum == 'LOAN_LEND_DATE' ? '天' : '期'}
                  </span>
                </div>
              );
            }}
          </ProFormDependency>
        </Col>
      </Row>
      {/* <Row>
        <Col span={10} offset={1}>
          <ProFormText label="操作人" name="operatorBy" readonly />
        </Col>
        <Col span={10} offset={1}>
          <ProFormText label="操作/生效时间" name="operatorTime" readonly />
        </Col>
      </Row> */}
    </Form>
  );
};

export default SettleEarlyCash;
