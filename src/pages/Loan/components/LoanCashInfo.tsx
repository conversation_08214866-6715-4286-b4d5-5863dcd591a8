/* eslint-disable react-hooks/exhaustive-deps */
import { DividerTit } from '@/components';
import ShowInfo from '@/components/ShowInfo';
import StepProgress from '@/components/StepProgress/index';
import globalStyle from '@/global.less';
import { useRequest } from '@umijs/max';
import { But<PERSON>, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import type { DataInfo } from '../data';
import { leaseDetail } from '../service';
import DialogCashApply from './DialogCashApply';
import DialogChangeCard from './DialogChangeCard';
import DialogOfflineLoan from './DialogOfflineLoan';

interface LoanLeaseInfoProps {
  dataLending: DataInfo;
  userNo: string;
  lendingNo: string;
  orderNo?: string;
  productCode: string;
  auditLogs: [];
  refresh: () => void;
  isCarInsurance?: boolean;
}
const LoanLeaseInfo: React.FC<LoanLeaseInfoProps> = (props) => {
  const { dataLending, auditLogs, lendingNo, userNo, productCode, orderNo, isCarInsurance } = props;
  const loanInfoMap = {
    lendingNo: '放款流水号',
    type: '放款类型',
    amount: '放款金额',
    lendingMaster: '放款主体',
    receiptMaster: '收款主体',
    re: '放款周期',
    lendingModel: '放款方式',
    fundFlow: '是否发生资金流',
    lendingTime: '放款时间',
    status: '放款状态',
  };
  const itemMap = {
    type: {
      1: '进件',
      2: '债转',
      3: '代偿',
    },
    fundFlow: {
      false: '否',
      true: '是',
    },
    status: {
      1: '待放款',
      0: '放款失败',
      2: '放款成功',
      3: '待请款',
      10: '待一审',
      11: '待二审',
      12: '驳回',
      13: '待换卡',
      '-1': '失效',
    },
    lendingModel: {
      1: '线上',
      2: '线下',
    },
  };
  const [dialogOfflineLoanVisible, setDialogOfflineLoanVisible] = useState<boolean>(false);
  const [dialogChangeCardVisible, setDialogChangeCardVisible] = useState<boolean>(false);
  const [dialogCardApplyVisible, setDialogCardApplyVisible] = useState<boolean>(false);

  // 获取线下还款详情
  const { data: offlineLoanData, run } = useRequest(
    () => {
      return leaseDetail(lendingNo, orderNo!);
    },
    {
      manual: true, // 手动触发
    },
  );

  useEffect(() => {
    // 只有非车险分期时才调用API
    if (!isCarInsurance) {
      run();
    }
  }, [isCarInsurance]);

  function renderChildren() {
    return (
      <>
        <DividerTit title="请款资料">
          {true && (
            <Button
              className={globalStyle.ml10}
              onClick={() => {
                setDialogCardApplyVisible(true);
              }}
            >
              提交请款
            </Button>
          )}
          {!isCarInsurance && (
            <Button
              className={globalStyle.ml10}
              onClick={() => {
                setDialogChangeCardVisible(true);
              }}
            >
              触发更换银行卡
            </Button>
          )}
          {!isCarInsurance && (
            <Button
              className={globalStyle.ml10}
              onClick={() => {
                setDialogOfflineLoanVisible(true);
              }}
            >
              线下放款
            </Button>
          )}
        </DividerTit>
        {auditLogs?.length ? (
          <Row>
            <StepProgress
              className={globalStyle.stepItemDesc}
              progressDot
              lineLength={7}
              stepStatus={auditLogs?.map(
                (item: {
                  desc: string;
                  statusDesc: string;
                  time: string;
                  operatorBy: string;
                  remark: string;
                }) => {
                  return {
                    bol: false,
                    desc: item.desc,
                    remark: item.remark,
                    localDate: `${item.time} ${item.operatorBy ? item.operatorBy : '-'}`,
                  };
                },
              )}
            />
          </Row>
        ) : null}
        {(offlineLoanData?.otherEnclosure || offlineLoanData?.otherMsg) && (
          <>
            <Row>
              <div className={globalStyle.mt30}>
                <span className="l-label">附件：</span>
                <span className="l-content">
                  {offlineLoanData?.otherEnclosure?.map(
                    (
                      item: {
                        url: string | undefined;
                        name: string | undefined;
                      },
                      index: number,
                    ) => (
                      <>
                        &nbsp;
                        <a key={item.url} href={item.url}>
                          {item.name}
                        </a>
                        &nbsp;
                        {index < offlineLoanData?.otherEnclosure?.length - 1 && '|'}
                      </>
                    ),
                  )}
                </span>
              </div>
            </Row>
            <Row>
              <div className={globalStyle.mt30}>
                <span className="l-label">备注：</span>
                <span className="l-content">{offlineLoanData?.otherMsg}</span>
              </div>
            </Row>
          </>
        )}
        <DialogOfflineLoan
          refresh={props.refresh}
          status={dataLending?.status}
          userNo={userNo}
          productCode={productCode}
          visible={dialogOfflineLoanVisible}
          onVisibleChange={(value: boolean, isSubmit?: boolean) => {
            if (isSubmit) {
              run();
            }
            setDialogOfflineLoanVisible(value);
          }}
        />
        <DialogChangeCard
          refresh={props.refresh}
          status={dataLending?.status}
          productCode={productCode}
          visible={dialogChangeCardVisible}
          onVisibleChange={(value: boolean, isShowOfflineLoan?: boolean) => {
            setDialogChangeCardVisible(value);
            if (isShowOfflineLoan) {
              setDialogChangeCardVisible(true);
            }
          }}
        />
        <DialogCashApply
          refresh={props.refresh}
          status={dataLending?.status}
          visible={dialogCardApplyVisible}
          onVisibleChange={(value: boolean) => {
            setDialogCardApplyVisible(value);
          }}
        />
      </>
    );
  }
  return (
    <>
      {isCarInsurance ? (
        renderChildren()
      ) : (
        <ShowInfo title="放款信息" infoMap={loanInfoMap} data={dataLending} itemMap={itemMap}>
          {renderChildren()}
        </ShowInfo>
      )}
    </>
  );
};

export default LoanLeaseInfo;
