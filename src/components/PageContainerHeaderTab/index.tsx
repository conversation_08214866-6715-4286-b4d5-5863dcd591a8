import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import type { ReactElement } from 'react';
import React, { memo } from 'react';

type Tprops = {
  children: ReactElement;
  extra?: ReactElement[];
  loading?: boolean;
};
const PageContainerHeaderTab: React.FC<Tprops> = (props) => {
  const { extra = [] } = props;
  return (
    <>
      <HeaderTab />
      <PageContainer header={{ extra }}>{props.children}</PageContainer>
    </>
  );
};
export default memo(PageContainerHeaderTab);
