/*
 * @Date: 2024-01-27 15:55:59
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-01-27 15:55:59
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/CashProductConfig/BasicInfo.tsx
 * @Description:
 */
/*

/* eslint-disable react-hooks/exhaustive-deps */

import React, { useEffect } from 'react';
import { Card, Form, Row, Col, Select, Input, Radio } from 'antd';
import globalStyle from '@/global.less';
import { CLASSIFICATION, SECONDARY_CLASSIFICATION } from '@/enums';

interface BasicInfoCashProps {
  baseInfo?: any;
  isAddProduct?: boolean; //
  name?: string;
  drawerStatus?: boolean;
}

const BasicInfoCash: React.FC<BasicInfoCashProps> = (props) => {
  const { baseInfo, isAddProduct = false, name, drawerStatus = true } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(baseInfo);
  }, [baseInfo]);

  return (
    <Card title="基础信息" className={globalStyle.mt30}>
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }} name={name}>
        <Row gutter={20}>
          <Col span={10} offset={1}>
            <Form.Item label="产品一级分类" name="classification">
              <Select placeholder="保理" disabled>
                {Object.keys(CLASSIFICATION).map((key) => (
                  <Select.Option key={key} value={key}>
                    {CLASSIFICATION[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="secondaryClassification" label="产品二级分类">
              <Select placeholder="明保" disabled>
                {Object.keys(SECONDARY_CLASSIFICATION).map((key) => (
                  <Select.Option key={key} value={key}>
                    {SECONDARY_CLASSIFICATION[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="productName" rules={[{ required: !drawerStatus }]} label="产品名称">
              <Input disabled={!isAddProduct} maxLength={50} />
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="userType" label="用户类型">
              <Select placeholder="企业" disabled>
                <Select.Option value="PERSONAGE">个人</Select.Option>
                <Select.Option value="COMPANY">企业</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item label="产品类型" name="selfType">
              <Radio.Group disabled>
                <Radio value={1}>自营</Radio>
                <Radio value={2}>非自营</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item label="额度类型" name="quotaType">
              <Radio.Group disabled>
                <Radio value={2}>循环额度</Radio>
                <Radio value={1}>一次性额度</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default React.memo(BasicInfoCash);
