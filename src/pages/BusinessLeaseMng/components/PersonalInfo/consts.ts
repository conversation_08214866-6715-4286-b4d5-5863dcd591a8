import { pattern, rules } from '@/utils/validate';

// 临时存储参数名
export const TEMP_PARAMS = ['first', 'second'];

export const PERSONAL_STEP_TITLE: Record<string, string> = {
  baseFieldConfig: '基础信息',
  bankFieldConfig: '银行信息',
  workFieldConfig: '工作信息',
  imagingFieldConfig: '影像件信息',
  relativeFieldConfig: '联系人信息',
  enterpriseFieldConfig: '企业信息',
};

export const PERSONAL_STEP_SORT = [
  'baseFieldConfig',
  'workFieldConfig',
  'enterpriseFieldConfig',
  'relativeFieldConfig',
  'bankFieldConfig',
  'imagingFieldConfig',
];

// 特殊组件
export const SP_COMPONENT_TYPE: Record<string, boolean> = {
  DATE: true,
  CITY: true,
  IMAGE: true,
};

// 校验map
export const VALIDATE_MAP: Record<string, { pattern: RegExp; message?: string }> = {
  PHONE: { pattern: pattern.phone, message: '格式有误' }, // 手机号
  CARD_ID: { pattern: pattern.idCard, message: '格式有误' }, //身份证号
  NUMBER: { pattern: pattern.number, message: '格式有误' }, //数字
  EMAIL: { pattern: pattern.email, message: '格式有误' }, // 邮箱
};

export enum FIELD_KEY {
  NAME = '0100', //姓名
  SEX = '0101', //性别
  DATE_OF_BIRTH = '0102', // 出生日期
  PHONE_1 = '0103', // 本人手机号1
  PHONE_2 = '0104', // 本人手机号2
  ID = '0107', //身份证号
  ID_CARD_START_DATE = '0108', // 身份证有效期开始日期
  ID_CARD_END_DATE = '0109', //身份证有效期终止日期
  HOUSEHOLD_ADDRESS = '0111', //户籍地址（详细地址）
  HOUSEHOLD_CITY = '0112', //户籍所在地行政区划（省市区）
  HOME_STATUS = '0113', // 居住状况
  HOME_ADDRESS = '0114', // 现居住地详细地址
  HOME_CODE = '0115', // 现居住地邮编
  HOME_CITY = '0116', // 现居住地省市区
  EDUCATION = '0119', // 学历
  MARITAL_STATUS = '0121', // 婚姻状况
  NAME_OF_SPOUSE = '0122', // 配偶姓名
  IDCARD_OF_SPOUSE = '0123', //配偶身份证号
  PHONE_OF_SPOUSE = '0124', //配偶联系电话
  QQ = '0125', // QQ
  WEI_XIN = '0126', // 微信
  EMAIL = '0127', // 电子邮箱
  CHILDREN_NUM = '0129', // 子女数
  CHILDREN_AGE_1 = '0130', //子女1年龄
  CHILDREN_AGE_2 = '0131', //子女2年龄
  CHILDREN_AGE_3 = '0132', //子女3年龄
  NATION = '0133', // 民族
  HOUSEHOLD_TYPE = '0137', // 户口性质
  COMPANY_NAME = '0202', //单位名称
  COMPANY_ADDRESS = '0205', // 单位详细地址
  COMPANY_CODE = '0206', // 单位所在地邮编
  COMPANY_AREA = '0207', // 单位所在地行政区划
  COMPANY_TEL = '0208', // 单位电话
  DRIVER_CARD_ID = '0215', // 驾驶证档案号
  DRIVER_ALLOW_TYPE = '0216', // 准驾类型
  DRIVER_CARD_DATE = '0217', // 驾驶证初次领证日期
  DRIVER_START_DATE = '0218', // 驾驶证有效期开始时间
  DRIVER_END_DATE = '0219', // 驾驶证有效期结束时间
  DRIVER_SIGN = '0221', // 驾驶证签发机关
  IMMEDIATE_CONTACT_NAME = '040201', //直系亲属联系人姓名
  FIRST_CONTACT_TYPE = '0401', //第一紧急联系人类型
  FIRST_CONTACT_NAME = '0402', //第一紧急联系人姓名
  FIRST_CONTACT_PHONE = '0403', //第一紧急联系人手机号
  SECOND_CONTACT_NAME = '0405', //第二紧急联系人姓名
  THIRD_CONTACT_NAME = '0408', // 第三紧急联系人姓名
  FOURTH_CONTACT_NAME = '0411', //第四紧急联系人姓名
  FIVE_CONATCT_NAME = '0414', // 第五紧急联系人姓名
  OTHER_CONATCT_NAME = '0450', // 其他紧急联系人姓名
}
// 要从基础信息接口获取的数据，清空草稿时不能被清空
export const baseFieldKeys = ['0100', '0101', '0102', '0103', '0107'];

// 指定字段的校验规则补充
export const FIELED_VALIDATE_MAP: Record<string, any[]> = {
  [FIELD_KEY.NAME]: [{ validator: rules.asyncNotAllNumber, message: '不能为纯数字' }],
  [FIELD_KEY.NAME_OF_SPOUSE]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],
  // 联系人姓名只能是汉字或者字母
  [FIELD_KEY.IMMEDIATE_CONTACT_NAME]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],

  [FIELD_KEY.FIRST_CONTACT_NAME]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],
  [FIELD_KEY.SECOND_CONTACT_NAME]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],
  [FIELD_KEY.THIRD_CONTACT_NAME]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],
  [FIELD_KEY.FOURTH_CONTACT_NAME]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],
  [FIELD_KEY.FIVE_CONATCT_NAME]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],
  [FIELD_KEY.OTHER_CONATCT_NAME]: [
    {
      pattern: pattern.ch,
      message: '不能录入非汉字且不少于2个汉字',
    },
  ],
  [FIELD_KEY.ID_CARD_END_DATE]: [
    {
      validator: rules.idCardEndDate,
    },
  ],
};

// 特殊字段需要的配置
export const SP_FIELD_CONFIG: Record<string, any> = {
  [FIELD_KEY.DATE_OF_BIRTH]: {
    maxDate: new Date(),
  },
  [FIELD_KEY.ID_CARD_END_DATE]: {
    showLongTimeOption: true, //显示永久有效选项
  },
  [FIELD_KEY.DRIVER_END_DATE]: {
    showLongTimeOption: true, //显示永久有效选项
  },
  [FIELD_KEY.FIRST_CONTACT_NAME]: {
    placeholder: '例如:张三',
  },
  [FIELD_KEY.SECOND_CONTACT_NAME]: {
    placeholder: '例如:李四',
  },

  [FIELD_KEY.FIRST_CONTACT_NAME]: { maxLength: 15 },
  [FIELD_KEY.SECOND_CONTACT_NAME]: { maxLength: 15 },
  [FIELD_KEY.HOUSEHOLD_CITY]: {
    placeholder: '请选择省市区',
  },
  [FIELD_KEY.HOUSEHOLD_ADDRESS]: {
    placeholder: '请填写详细地址（30字内）',
    maxLength: 30,
  },
  [FIELD_KEY.HOME_CITY]: {
    placeholder: '请选择省市区',
  },
  [FIELD_KEY.HOME_ADDRESS]: {
    placeholder: '请填写详细地址（30字内）',
    maxLength: 30,
  },
  [FIELD_KEY.COMPANY_AREA]: {
    placeholder: '请选择省市区',
  },
  [FIELD_KEY.COMPANY_ADDRESS]: {
    placeholder: '请填写详细地址（30字内）',
    maxLength: 30,
  },
};

// 下面字段若有相邻则需要处理下排序
export const SORT_FIELD_SPECIAL: { [key: string]: any } = {
  baseFieldConfig: {
    [FIELD_KEY.HOUSEHOLD_CITY]: { order: 101 },
    [FIELD_KEY.HOUSEHOLD_ADDRESS]: { order: 102 },
    [FIELD_KEY.HOME_CITY]: { order: 103 },
    [FIELD_KEY.HOME_ADDRESS]: { order: 104 },
    [FIELD_KEY.HOME_CODE]: { order: 105 },
  },
  workFieldConfig: {
    [FIELD_KEY.COMPANY_AREA]: { order: 201 },
    [FIELD_KEY.COMPANY_ADDRESS]: { order: 202 },
    [FIELD_KEY.COMPANY_CODE]: { order: 203 },
  },
};

// 联系人类型
export const CONTACT_PERSOAN_TYPE = [
  '0401',
  '040101',
  '0404',
  '0407',
  '0410',
  '0413',
  '0446',
  '0449',
];

// 联系人姓名code
export const CONTACT_PERSOAN_NAME = [
  '0402',
  '040201',
  '0405',
  '0408',
  '0411',
  '0414',
  '0447',
  '0450',
];
// 联系人手机号code
export const CONTACT_PERSOAN_PHONE = [
  '0403',
  '040301',
  '0406',
  '0409',
  '0412',
  '0415',
  '0448',
  '0451',
];
//配偶信息code
export const SPOUSE_INFO_CODE = ['0122', '0123', '0124'];

// 撤销后重新进件，可以反显数据的字段
export const FIELD_CAN_SHOW = [
  FIELD_KEY.HOUSEHOLD_ADDRESS,
  FIELD_KEY.HOUSEHOLD_CITY,
  FIELD_KEY.EDUCATION,
  FIELD_KEY.CHILDREN_NUM,
  FIELD_KEY.CHILDREN_AGE_1,
  FIELD_KEY.CHILDREN_AGE_2,
  FIELD_KEY.CHILDREN_AGE_3,
  FIELD_KEY.NATION,
  FIELD_KEY.COMPANY_NAME,
  FIELD_KEY.COMPANY_ADDRESS,
  FIELD_KEY.COMPANY_AREA,
  FIELD_KEY.COMPANY_TEL,
  FIELD_KEY.DRIVER_SIGN,
];

// 预审通过后不能修改的字段
export const FIELD_CAN_EDIT = [
  FIELD_KEY.NAME,
  FIELD_KEY.SEX,
  FIELD_KEY.NATION,
  FIELD_KEY.DATE_OF_BIRTH,
  FIELD_KEY.PHONE_1,
  FIELD_KEY.PHONE_2,
  FIELD_KEY.ID,
  FIELD_KEY.ID_CARD_START_DATE,
  FIELD_KEY.ID_CARD_END_DATE,
  FIELD_KEY.HOUSEHOLD_TYPE,
  FIELD_KEY.HOUSEHOLD_CITY,
  FIELD_KEY.HOUSEHOLD_ADDRESS,
  FIELD_KEY.HOME_STATUS,
  FIELD_KEY.HOME_CITY,
  FIELD_KEY.HOME_ADDRESS,
  FIELD_KEY.EDUCATION,
  FIELD_KEY.MARITAL_STATUS,
  FIELD_KEY.NAME_OF_SPOUSE,
  FIELD_KEY.IDCARD_OF_SPOUSE,
  FIELD_KEY.PHONE_OF_SPOUSE,
  FIELD_KEY.QQ,
  FIELD_KEY.WEI_XIN,
  FIELD_KEY.EMAIL,
  FIELD_KEY.CHILDREN_NUM,
  FIELD_KEY.CHILDREN_AGE_1,
  FIELD_KEY.CHILDREN_AGE_2,
  FIELD_KEY.CHILDREN_AGE_3,
  FIELD_KEY.DRIVER_CARD_ID,
  FIELD_KEY.DRIVER_ALLOW_TYPE,
  FIELD_KEY.DRIVER_CARD_DATE,
  FIELD_KEY.DRIVER_START_DATE,
  FIELD_KEY.DRIVER_END_DATE,
  FIELD_KEY.DRIVER_SIGN,
];
