import { Carousel, Modal, Image } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import React, { useImperativeHandle, useRef } from 'react';
import { getBase64, getBlob, previewAS, saveAs } from '@/utils/utils';
import './index.less';
import { CarouselRef } from 'antd/lib/carousel';

// 支持图片和PDF的预览

export type previewObjProps = {
  previewImage: string;
  previewVisible: boolean;
  previewTitle: string;
  previewIndex: number;
};

type modalProps = {
  previewRef?: any;
  previewObj: previewObjProps;
  handlePreviewObj: (params: previewObjProps) => void;
  previewType?: string; //  是否支持多图预览
  loadType?: boolean; //  上传中或者url预览
  fileList?: any;
  fullView?: boolean; //  浏览器全屏预览
};

const PreviewModalCon: React.FC<modalProps> = ({
  handlePreviewObj,
  previewObj,
  fileList,
  loadType,
  previewRef,
  fullView,
  previewType,
}) => {
  const carouselRef = useRef<CarouselRef>(null);

  // 预览url初始化
  const previewUrlTransfer = async (list: any) => {
    for (let i = 0; i < list.length; i++) {
      if (!list[i].url && !list[i].preview && list[i].originFileObj) {
        list[i]['preview'] = await getBase64(list[i].originFileObj);
      }
    }
  };

  const getFileTypeContent = (fileType: string) => {
    let final = '';
    switch (fileType.toLowerCase()) {
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'bmp':
        final = 'image/jpeg;chartset=UTF-8';
        break;
      case 'gif':
        final = 'image/GIF;chartset=UTF-8';
        break;
      case 'txt':
        final = 'text/plain;chartset=UTF-8';
        break;
      case 'pdf':
        final = 'application/pdf;chartset=UTF-8';
        break;
    }
    return final;
  };

  //  浏览器方式预览文件
  const previewFile = (url: string, filename: string) => {
    getBlob(url, (blob: Blob) => {
      //判断是否是支持预览的类型，如果是，则预览，不是则直接下载
      const fileType = filename.substring(filename.lastIndexOf('.') + 1);
      if (['png', 'jpg', 'jpeg', 'bmp', 'gif', 'pdf'].includes(fileType)) {
        const finalFileType = getFileTypeContent(fileType);
        previewAS(blob, finalFileType);
      } else {
        saveAs(blob, filename);
      }
    });
  };

  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview && file.originFileObj) {
      file.preview = await getBase64(file.originFileObj);
    }
    //如果是pdf，在浏览器打开预览，编辑的时候['pdf'].includes(file?.name?.substring(file?.name.lastIndexOf('.') + 1)
    if (
      file?.type === 'application/pdf' ||
      ['pdf'].includes(file?.name?.substring(file?.name.lastIndexOf('.') + 1))
    ) {
      getBlob(file?.url || file?.preview, (blob: Blob) => {
        previewAS(blob);
      });
      return;
    }

    if (fileList?.length > 1 && previewType === 'multiple' && loadType) {
      // 上传中的图片预览资源从base64获取
      await previewUrlTransfer(fileList); //  多图预览初始化
    }

    const index = fileList?.findIndex((item: any) => item?.uid === file.uid);
    if (index >= 0) carouselRef?.current?.goTo(index);
    handlePreviewObj({
      previewImage: file.url || file.preview,
      previewVisible: true,
      previewTitle: file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
      previewIndex: index,
    });
  };

  useImperativeHandle(previewRef, () => ({
    handlePreview,
  }));

  return (
    <Modal
      className="release-com-modal"
      open={previewObj.previewVisible}
      title={
        fileList?.length > 1 && previewType === 'multiple' ? '图片预览' : previewObj?.previewTitle
      }
      footer={null}
      onCancel={() => {
        handlePreviewObj({ ...previewObj, previewVisible: false });
      }}
    >
      {fileList?.length > 1 && previewType === 'multiple' ? (
        <Carousel
          ref={carouselRef}
          dots={false}
          arrows={true}
          adaptiveHeight={true}
          initialSlide={previewObj?.previewIndex}
          prevArrow={<LeftOutlined />}
          nextArrow={<RightOutlined />}
        >
          {fileList?.map((file: any) => {
            return (
              <>
                <div>
                  <div style={{ marginBottom: 15 }}>{file?.name}</div>
                  <div className="release-com-img-wrap">
                    <Image
                      className="release-com-img"
                      alt="支持预览"
                      style={{ width: '100%' }}
                      src={file?.url || file?.preview}
                    />
                    {file?.type === 'application/pdf' ||
                    ['pdf'].includes(file?.name?.substring(file?.name.lastIndexOf('.') + 1)) ? (
                      <a
                        onClick={() => {
                          handlePreview(file);
                        }}
                      >
                        点击打开新浏览器窗口进行查看
                      </a>
                    ) : (
                      fullView && (
                        <a
                          onClick={() => {
                            previewFile(file.url, file.name);
                          }}
                        >
                          浏览器查看
                        </a>
                      )
                    )}
                  </div>
                </div>
              </>
            );
          })}
        </Carousel>
      ) : (
        <div className="release-com-img-wrap">
          <Image
            className="release-com-img"
            alt="支持预览"
            style={{ width: '100%' }}
            src={previewObj?.previewImage}
          />
        </div>
      )}
    </Modal>
  );
};

export default PreviewModalCon;
