// 垫付规则
import { LEASE_LOAN_CHANNEL_NAME } from '@/enums';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { <PERSON><PERSON>, Card, Switch } from 'antd';
import React, { useRef, useState } from 'react';
import { changePayConfigStatus, getPayRuleList } from '../service';
import type { PayRuleListItem } from '../types';
import AddPayRuleModal from './AddPayRuleModal';

enum STATUS {
  OPEN = 1,
  CLOSE = 2,
}
const PayRule = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [switchLoading, setSwitchLoading] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<PayRuleListItem>();

  const actionRef = useRef<ActionType>();

  const handleEdit = (record: PayRuleListItem) => {
    setCurrentRecord({ ...record });
    setShowAddModal(true);
  };
  const onSwitchChange = async (record: PayRuleListItem) => {
    const { configId, status } = record;
    const params = {
      id: configId,
      status: status === STATUS.OPEN ? STATUS.CLOSE : STATUS.OPEN,
    };
    setSwitchLoading(true);
    try {
      await changePayConfigStatus(params);
      actionRef.current?.reload();
      setSwitchLoading(false);
    } catch {
      setSwitchLoading(false);
    }
  };

  const columns = [
    {
      title: '放款资方',
      dataIndex: 'capital',
      valueType: 'select',
      valueEnum: LEASE_LOAN_CHANNEL_NAME,
    },
    {
      title: '合作业务线',
      dataIndex: 'productCode',
      valueEnum: {
        '0201': '融资租赁',
      },
    },
    {
      title: '垫付场景',
      dataIndex: 'rule',
    },
    {
      title: '操作',
      render: (dom, record) => {
        return (
          <Button
            type="link"
            onClick={() => {
              handleEdit(record);
            }}
          >
            编辑
          </Button>
        );
      },
    },
    {
      title: '开关',
      render: (dom, record) => {
        return (
          <Switch
            checked={record?.status === STATUS.OPEN}
            onChange={() => {
              onSwitchChange(record);
            }}
            loading={switchLoading}
          />
        );
      },
    },
  ];

  return (
    <Card
      title="垫付规则"
      extra={
        <Button
          type="primary"
          onClick={() => {
            setShowAddModal(true);
          }}
        >
          新增
        </Button>
      }
      style={{ marginBottom: 20 }}
    >
      <ProTable<PayRuleListItem>
        search={false}
        actionRef={actionRef}
        columns={columns}
        request={(params) => getPayRuleList(params)}
      />
      <AddPayRuleModal
        open={showAddModal}
        data={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
          setShowAddModal(false);
        }}
        onCancel={() => setShowAddModal(false)}
      />
    </Card>
  );
};

export default PayRule;
