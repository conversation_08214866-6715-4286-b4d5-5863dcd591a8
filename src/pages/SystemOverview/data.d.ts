export type DataType = {
  comeNumber: string;
  waitApply: string;
  pass: string;
  refuse: string;
  passRatio: string;
  creditAmount: string;
};

export type DataBoardType = {
  factoring: DataType;
  rent: DataType;
  loan: DataType;
};

export type StatisticParams = {
  lastMonth?: string;
  lastSevenDay?: string;
  lastThirdDay?: string;
  lastThirtyDay?: string;
  productSecondCode: string;
  thisMonth?: string;
  today?: string;
  yesterday?: string;
};
