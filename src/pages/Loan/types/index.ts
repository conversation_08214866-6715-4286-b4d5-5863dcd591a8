export interface IlistRspListItem {
  id: number;
  amountDue: string;
  principal: string;
  interest: string;
  cost: string;
  paidAmount: string;
  repayTime: string;
  termDetail: string;
}
export interface IgetCarRepaymentRes {
  repayTerm: number;
  repayMode: number;
  annualInterestRate: string;
  listRspList: IlistRspListItem[];
}

export interface IocrItem {
  result: boolean;
  fieldName: string;
  fieldValue: string;
}
export interface IocrInfo {
  returnCode: number;
  ocrValueList: IocrItem[];
}

export interface IocrListInfo {
  carPurchaseInvoice: IocrInfo;
  trafficInsurance: IocrInfo;
  businessInsurance: IocrInfo;
}

export type FileItem = {
  name: string;
  filePath: string;
  url: string;
  uid: string;
};
export type IleaseDetail = {
  linceseType: number;
  lendingNo: string;
  otherMsg: string;
  lendingStatus: number;
  carPurchaseInvoiceOcr: string;
  trafficInsuranceOcr: string;
  businessInsuranceOcr: string;
} & Record<string, FileItem[]>;
