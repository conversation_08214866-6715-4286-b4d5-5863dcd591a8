/*
 * @Author: your name
 * @Date: 2020-11-23 17:22:42
 * @LastEditTime: 2023-03-22 17:42:56
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/data.d.ts
 */
export interface BillListParams {
  pageNumber?: number;
  pageSize?: number;
  accountName?: string;
  accountNumber?: string;
  billCycleTime?: string;
  billNo?: string;
  productName?: string;
  status?: number;
}

export interface BillExportPages {
  pageNumber?: number;
  pageSize?: number;
}

export interface BillListItem {
  accountName: string;
  accountNumber: string;
  billCycle: string;
  billNo: string;
  billingDate: string;
  orderNo: string;
  productName: string;
  status: number;
  totalAmount: number;
}
export interface BillListPagination {
  total: number;
  size: number;
  current: number;
}

export interface BillListListData {
  list: BillListItem[];
  pagination: Partial<BillListPagination>;
}

export interface LogListItem {
  afterValue?: [];
  beforeValue?: [];
  createdAt?: string;
  modifyItem?: string;
  operatorBy?: string;
  fileInfo: { url: string; name: string }[];
}

export interface ManualRepay {
  userNo?: string;
  orderNo?: string;
  repayMoney?: string;
  repayTime?: string;
  attachList?: Record<string, any>;
  remark?: string;
}

export interface RepayListItem {
  accountBank?: string;
  accountName?: string;
  accountNumber?: string;
  actualRepaymentAmount?: number;
  attachInfoList?: any[];
  bankSerialNo?: string;
  certificateUrl?: string;
  createdAt?: string;
  overpayments?: number;
  remark?: string;
  repayBillAmount?: number;
  repayChannel?: number;
  repayTime?: string;
}

export interface ModifyBillData {
  billNo?: string;
  repayBillStatus?: number;
  repaymentDate?: string;
  modifyDate?: string;
  modifyDirection?: number;
  map?: Record<K, V>;
  enclosure?: Record<K, V>[];
  remark?: string;
  accountNumber?: string;
}

export interface ModifyBillLogsData {
  billNo?: string;
  current?: number;
  pageSize?: number;
}

export type CombineRepayBackParams = {
  initiateBillRepaymentReq: {
    productCode: string;
    accountBank: string;
    applyId: string;
    bankName: string;
    bankNumber: string;
    billDate: string;
    billType: number;
    certificateUrl: string;
    productCode: string;
    repayChannel: number;
    repayEntrance: number;
    repayMoney: number;
    repayTime: string;
  };
  offlineRemitApplyReq: {
    applyName: string;
    attach: {
      filePath: string;
      name: string;
    }[];

    remark: string;
    remitAmount: number;
    remitDate: string;
    remitType: string;
    repayPlayNo?: string;
    sendRecordUid: string;
  };
};
export interface BillDetailInfoItem {
  orderNo?: string;
  incomingTime?: string;
  repayTerm: number;
  amountDue?: number;
  totalRemainingRepayment?: number;
  status: string;
  repayTime: string;
  bizOrderNo: string;
}
