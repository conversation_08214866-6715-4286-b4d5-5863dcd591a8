/**
 * 融租-线上还款
 */
import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import LoadingButton from '@/components/LoadingButton';
import { RePaymentQrViewer } from '@/components/QrViewer/RepaymentQrViewer';
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { rules } from '@/utils/validate';
import {
  ActionType,
  EditableProTable,
  ModalForm,
  ProFormDependency,
  ProFormRadio,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Alert, Descriptions, Form, message } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import type { PropsWithChildren } from 'react';
import React, { memo, useRef, useState } from 'react';
import { OnlineRepayColumns } from '../columns/OnlineRepayColumns';
import { billInfo, submitBillRepay } from '../services';
import type { IbillInfo, IbillListItem, Idimension } from '../types';
import './index.less';

type Props = {
  selectedRows: IbillListItem[];
  dimension: Idimension;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};
const OnlineRepay = (props: PropsWithChildren<Props>) => {
  const { selectedRows, dimension, actionRef } = props;
  const [billInfoData, setBillInfoData] = useState<IbillInfo>();
  const [open, setOpen] = useState(false);
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const access = useAccess();
  const [form] = Form.useForm();
  const [errorDesc, setErrorDesc] = useState('');
  const [qrVisible, setQrVisible] = useState(false);
  const [qrData, setQrData] = useState({});

  const remissionDefaultData = {
    id: 1,
    remissionPrincipal: 0,
    remissionInterest: 0,
    remissionPenalty: 0,
    remissionAdvanceSettleLiquidatedDamages: 0,
    remissionDelayAmount: 0,
  };

  // 计算减免总金额
  function calculateRemissionAmount(remission: any) {
    const {
      remissionPrincipal,
      remissionInterest,
      remissionPenalty,
      remissionAdvanceSettleLiquidatedDamages,
      remissionDelayAmount,
    } = remission;
    const amount = new BigNumber(remissionPrincipal)
      .plus(remissionInterest)
      .plus(remissionPenalty)
      .plus(remissionAdvanceSettleLiquidatedDamages)
      .plus(remissionDelayAmount)
      .toNumber();
    return amount;
  }

  function getAmount() {
    const init = {
      expectRepayAmount: 0,
      totalAmountUnpaid: 0,
      totalPrincipalUnpaid: 0,
      totalInterestUnpaid: 0,
      totalOverduePenaltyUnpaid: 0,
      totalBreach: 0,
      totalLate: 0,
    };
    if (billInfoData?.billRspDTOList?.length) {
      return billInfoData?.billRspDTOList?.reduce((pre: any, cur) => {
        const {
          expectRepayAmount,
          totalAmountUnpaid,
          totalPrincipalUnpaid,
          totalInterestUnpaid,
          totalOverduePenaltyUnpaid,
          totalBreach,
          totalLate,
          // payee = SHANG_HAI_YING_HANG 则为上银账单
          payee,
        } = cur;
        const _expectRepayAmount = new BigNumber(pre.expectRepayAmount)
          .plus(expectRepayAmount)
          .toNumber();
        const _totalAmountUnpaid = new BigNumber(pre.totalAmountUnpaid)
          .plus(totalAmountUnpaid)
          .toNumber();
        const _totalPrincipalUnpaid = new BigNumber(pre.totalPrincipalUnpaid)
          .plus(totalPrincipalUnpaid)
          .toNumber();
        const _totalInterestUnpaid = new BigNumber(pre.totalInterestUnpaid)
          .plus(totalInterestUnpaid)
          .toNumber();
        // 罚息
        // const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
        //   .plus(totalOverduePenaltyUnpaid)
        //   .toNumber();
        // 债权归属上银的单子，不减免罚息
        const isSHYH = payee === 'SHANG_HAI_YING_HANG';
        const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
          .plus(isSHYH ? 0 : totalOverduePenaltyUnpaid)
          .toNumber();
        //
        const _totalBreach = new BigNumber(pre.totalBreach).plus(totalBreach).toNumber();
        const _totalLate = new BigNumber(pre.totalLate).plus(totalLate).toNumber();
        return {
          expectRepayAmount: isNaN(_expectRepayAmount) ? 0 : _expectRepayAmount,
          totalAmountUnpaid: isNaN(_totalAmountUnpaid) ? 0 : _totalAmountUnpaid,
          totalPrincipalUnpaid: isNaN(_totalPrincipalUnpaid) ? 0 : _totalPrincipalUnpaid,
          totalInterestUnpaid: isNaN(_totalInterestUnpaid) ? 0 : _totalInterestUnpaid,
          totalOverduePenaltyUnpaid: isNaN(_totalOverduePenaltyUnpaid)
            ? 0
            : _totalOverduePenaltyUnpaid,
          totalBreach: isNaN(_totalBreach) ? 0 : _totalBreach,
          totalLate: isNaN(_totalLate) ? 0 : _totalLate,
        };
      }, init);
    } else {
      return init;
    }
  }
  console.log(getAmount(), '000000');

  //从后端获取数据
  // const getRepayData = async () => {
  //   try {
  //     // form?.validateFields();
  //     const err = form.getFieldsError(['remission']);
  //     console.log(err);
  //     const hasErrors = err.some((item) => item.errors.length > 0);
  //     console.log(hasErrors);
  //     //如果有校验error不请求后端接口
  //     if (hasErrors) {
  //       return;
  //     }
  //     const curFormData = form?.getFieldsValue();
  //     const { remission } = curFormData || {};
  //     const remissionTotalAmount = calculateRemissionAmount(remission?.[0]);
  //     const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
  //       .minus(remissionTotalAmount)
  //       .toNumber();
  //     fullScreenLoadingRef.current?.open();
  //     const res = await calculateBill({
  //       billInfoList: billInfoData?.billRspDTOList || [],
  //       remissionList: remission,
  //       repayAmount: repayTotalAmount,
  //     });
  //     setErrorDesc('');
  //     setBillInfoData(res);
  //   } catch (error: any) {
  //     //试算单独处理报错逻辑
  //     // console.log([0, 200].includes(error?.ret));
  //     if (![0, 200].includes(error?.ret)) {
  //       setErrorDesc(error?.msg);
  //     }
  //     // console.log(error?.msg);
  //     // console.log(error);
  //   } finally {
  //     fullScreenLoadingRef.current?.close();
  //   }
  // };

  // const { run, cancel } = useDebounceFn(getRepayData, { wait: 500 });

  // 排序规则：
  // 1. 多笔订单下，所选账单应还款日期远的订单排前面
  // 2. 同一笔订单下，期数早的排前面
  const sortBillListData = (data: any) => {
    // 按 `orderNo` 字段进行分类
    const groupedData = data?.billRspDTOList?.reduce((acc: any, item: any) => {
      if (!acc[item.orderNo]) {
        acc[item.orderNo] = [];
      }
      acc[item.orderNo].push(item);
      return acc;
    }, {});
    // 对每个组内的时间进行排序并取最早的时间作为排序依据
    const sortedGroups = Object.keys(groupedData).map((orderNo) => {
      const group = groupedData[orderNo];
      const earliestTime = group.map((item: any) => new Date(item.dueDate).getTime())?.[0];
      return { orderNo, earliestTime, items: group };
    });
    // 按最早时间排序每个组
    sortedGroups.sort((a, b) => a.earliestTime - b.earliestTime);
    // 拼接最后的结果
    const sortedData = sortedGroups.reduce((acc, group) => {
      return acc.concat(group.items);
    }, []);
    return { ...data, billRspDTOList: sortedData };
  };

  return (
    <>
      <LoadingButton
        type="primary"
        onClick={async () => {
          try {
            if (!selectedRows?.length) {
              message.error('还未勾选任何数据');
              return;
            }
            fullScreenLoadingRef.current?.open();
            const billNoList = selectedRows.map((item) => item.billNo);
            const data = await billInfo({
              billNoList,
              dimension,
              secondaryClassification: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
              onlineRepay: true,
            });
            setBillInfoData(sortBillListData(data));
            setOpen(true);
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        线上还款
      </LoadingButton>
      <ModalForm
        open={open}
        form={form}
        layout="horizontal"
        labelCol={{ style: { width: 120 } }}
        title={<div className="modal_tit">线上还款</div>}
        modalProps={{
          onCancel: () => {
            // cancel(); //取消防抖请求 有bug,关闭弹窗，同时是失焦的会触发计算请求。有一次报错展示
            setOpen(false);
            setErrorDesc('');
          },
          destroyOnClose: true,
          okText: '二维码还款',
        }}
        initialValues={{
          remission: [remissionDefaultData],
        }}
        width={1000}
        onFinish={async (values) => {
          try {
            console.log('values', values);
            //试算报错不允许提交
            if (errorDesc) {
              return;
            }
            // 是否有权限提交
            if (!access.hasAccess('bill_submit_repay_info_postLoanMng_afterLoanList')) {
              message.error('没有权限提交');
              return;
            }

            const { remission, attach, isRemission, ...rest } = values;
            const remissionTotalAmount = calculateRemissionAmount(values.remission?.[0]);
            const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              .minus(remissionTotalAmount)
              .toNumber();
            fullScreenLoadingRef.current?.open();
            const params = {
              remissionList: remission,
              billInfoList: billInfoData?.billRspDTOList,
              attach,
              // billNoList,
              repayAmount: repayTotalAmount,
              dimension,
              bankAmount: repayTotalAmount, //  兼容融租
              bankAmountDate: dayjs().format('YYYY-MM-DD'), //  兼容融租
              remitType: 4, //  此次默认收银台支付
              //
              onlineRepay: true,
              //
              ...rest,
            };
            await submitBillRepay(params).then((res) => {
              if (res?.data?.h5RepayUrl) {
                setQrData({
                  h5RepayUrl: res?.data?.h5RepayUrl,
                  businessNo: res?.data?.businessNo,
                  accountName: billInfoData?.accountName,
                });
                setQrVisible(true);
              }
            });
            message.success('提交成功');
            setOpen(false);
            actionRef.current?.reload();
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        <div className="block_area">
          <Descriptions bordered column={1} size={'small'} style={{ marginBottom: 20 }}>
            <Descriptions.Item label={<div>渠道名称</div>}>
              {billInfoData?.channelName}
            </Descriptions.Item>
            <Descriptions.Item label={<div>客户名称</div>}>
              {billInfoData?.accountNameList?.join(',')}
            </Descriptions.Item>
          </Descriptions>
          {/* <ProFormText
            rules={[{ required: true }]}
            name="channelName"
            width="sm"
            label={<div className='label_css'>渠道名称</div>}
            readonly
            fieldProps={{ value: billInfoData?.channelName }}
          /> */}
          <ProFormRadio.Group
            rules={[{ required: true, message: '请选择实际还款方' }]}
            name="actualRepayRole"
            width="sm"
            label={<div className="label_css">实际还款方</div>}
            options={[
              {
                label: '用户本人',
                value: '1',
              },
              {
                label: '第三方',
                value: '2',
              },
            ]}
            fieldProps={{
              onChange: (e) => {
                form.setFieldsValue({
                  remission: [
                    e.target.value === '2'
                      ? {
                          ...remissionDefaultData,
                          remissionPenalty: getAmount().totalOverduePenaltyUnpaid,
                        }
                      : remissionDefaultData,
                  ],
                });
                // run();
              },
            }}
          />
          <ProFormText
            rules={[{ validator: rules.phone }]}
            tooltip="该手机号用于接收二维码链接"
            placeholder={'选填'}
            name="customerPhone"
            width="sm"
            label={<div className="label_css">发送手机号</div>}
          />
          {/* <ProFormText
            rules={[{ required: true }]}
            name="channelName"
            width="sm"
            label={<div className='label_css'>计划还款金额</div>}
            readonly
            fieldProps={{ value: getAmount().expectRepayAmount }}
          /> */}
        </div>
        <div className="tit">还款和减免金额:</div>
        <div className="block_area">
          {errorDesc && (
            <Alert message={errorDesc} type="error" showIcon style={{ marginBottom: 10 }} />
          )}
          <ProFormDependency name={['remission']}>
            {(values) => {
              const remissionTotalAmount = calculateRemissionAmount(values.remission?.[0]);
              // const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              //   .plus(remissionTotalAmount)
              //   .toNumber();
              const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
                .minus(remissionTotalAmount)
                .toNumber();
              return (
                <div className="line">
                  <span className="submit_offline_label">计划还款金额:</span>
                  <span className="submit_offline_amount">
                    {isNaN(repayTotalAmount) ? '-' : repayTotalAmount}
                    {/* {getAmount().expectRepayAmount} */}
                  </span>
                  元
                </div>
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['remission']}>
            {(values) => {
              const amount = calculateRemissionAmount(values.remission?.[0]);
              return (
                <div className="line no_border ">
                  <span className="submit_offline_label">减免总额:</span>
                  <span className="submit_offline_amount">{isNaN(amount) ? '-' : amount}</span>元
                </div>
              );
            }}
          </ProFormDependency>
          <EditableProTable
            name="remission"
            rowKey="id"
            toolBarRender={false}
            pagination={false}
            columns={[
              {
                title: '减免本金(元)',
                dataIndex: 'remissionPrincipal',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2, disabled: true },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalPrincipalUnpaid,
                      message: `金额不可超过${getAmount().totalPrincipalUnpaid}`,
                    },
                  ],
                },
              },
              {
                title: '减免利息(元)',
                dataIndex: 'remissionInterest',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2, disabled: true },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalInterestUnpaid,
                      message: `金额不可超过${getAmount().totalInterestUnpaid}`,
                    },
                  ],
                },
              },
              {
                title: '减免罚息(元)',
                dataIndex: 'remissionPenalty',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2, disabled: true },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalOverduePenaltyUnpaid,
                      message: `金额不可超过${getAmount().totalOverduePenaltyUnpaid}`,
                    },
                  ],
                },
              },
              {
                title: '减免违约金(元)',
                dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2, disabled: true },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalBreach,
                      message: `金额不可超过${getAmount().totalBreach}`,
                    },
                  ],
                },
              },
              // {
              //   title: '减免滞纳金(元)',
              //   dataIndex: 'remissionDelayAmount',
              //   valueType: 'digit',
              //   fieldProps: { style: { width: '100%' }, precision: 2, disabled: true },
              //   formItemProps: {
              //     rules: [
              //       { required: true, message: '请输入大于等于0的数字' },
              //       {
              //         type: 'number',
              //         max: getAmount().totalLate,
              //         message: `金额不可超过${getAmount().totalLate}`,
              //       },
              //     ],
              //   },
              // },
            ]}
            recordCreatorProps={false}
            editable={{
              type: 'multiple',
              editableKeys: [1],
              // onChange: setEditableRowKeys,
              actionRender: () => {
                return [];
              },
            }}
          />
        </div>
        <div className="tit">请确认账单信息和还款金额无误后提交:</div>
        <div className="block_area pl_0">
          <ProTable
            search={false}
            options={false}
            pagination={false}
            columns={OnlineRepayColumns}
            dataSource={billInfoData?.billRspDTOList as any}
            scroll={{ x: 'max-content' }}
          />
        </div>
      </ModalForm>
      <RePaymentQrViewer
        data={qrData}
        qrVisible={qrVisible}
        handleQRVisible={setQrVisible}
        title="还款二维码"
      />
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};

export default memo(OnlineRepay);
