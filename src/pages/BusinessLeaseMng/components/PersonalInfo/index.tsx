/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Col, Form, message, Row, Spin } from 'antd';
import dayjs from 'dayjs';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import type { FinalRiskParams } from '../../data';
import {
  editFinalRiskField,
  getBasePersonalInfo,
  getEnterConfigByProductCode,
  getTempMetaData,
  submitFinalRiskField,
  temporaryStorage,
} from '../../service';
import {
  baseFieldKeys,
  FIELD_CAN_SHOW,
  PERSONAL_STEP_SORT,
  PERSONAL_STEP_TITLE,
  SORT_FIELD_SPECIAL,
  TEMP_PARAMS,
} from './consts';
import FormPage from './FormPage';
import './index.less';
import type { MetaDataItem, Step, SubmitData, TempDataObj } from './types';
import { formatCity, splitJsonStr } from './utils';

enum ACTION_MODE {
  NEW = 'new', // 用户新增
  EDIT = 'edit', // 修改某个模块
  ADD = 'add', // 补充字段
  VIEW = 'view', // 查看，不能编辑
}

type PersonalInfoType = {
  orderNo: string;
  productCode: string;
  actionMode: ActionMode;
  readOnly: boolean;
  remarkForm: any;
  onSubmit: () => void;
};

const PersonalInfo = forwardRef((props: PersonalInfoType, ref) => {
  const { productCode, orderNo, actionMode, readOnly, remarkForm } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const [mode, setMode] = useState(actionMode);
  const [allSteps, setAllSteps] = useState<Step[]>([]);
  const [metaData, setMetaData] = useState<SubmitData>({});
  const [baseInfo, setBaseInfo] = useState({});

  const valueData = useRef<TempDataObj>({});

  // 相邻的部份特殊字段做下排序，其他字段保持不变
  const sortSpecialFileItem = (obj: any) => {
    try {
      PERSONAL_STEP_SORT.forEach((key: string) => {
        if (key in SORT_FIELD_SPECIAL && obj[key]) {
          const MAP = SORT_FIELD_SPECIAL[key];
          obj[key]?.list?.sort(function (a: any, b: any) {
            if (a?.fieldCode in MAP && b?.fieldCode in MAP)
              return MAP[a.fieldCode].order - MAP[b.fieldCode].order;
            return 0;
          });
        }
      });
      return obj;
    } catch (e) {
      console.log('sortSpecialFileItem error', e);
      return obj;
    }
  };
  // 读取进件配置
  const initEnterConfig = async () => {
    if (!productCode) {
      message.warning('请先选择车辆租赁方案！');
      return;
    }
    setLoading(true);
    let mode = actionMode;
    // 获取三要素信息
    const baseInfoRes = await getBasePersonalInfo(orderNo);
    const info = baseInfoRes?.data;
    setBaseInfo(info);
    // 获取风控配置
    const res = await getEnterConfigByProductCode(productCode);
    const configData = res.data;
    if (configData) {
      // 获取值数据
      const tempRes = await getTempMetaData(orderNo);
      const tempData = tempRes.data || {};
      let jsonData: TempDataObj = {};
      // 如果有暂存的数据
      if (tempData.first) {
        let jsonStr = '';
        TEMP_PARAMS.forEach((key: string) => {
          if (tempData[key]) {
            jsonStr += tempData[key];
          }
        });
        jsonData = JSON.parse(jsonStr);
      }
      // 修改时进件字段和之前的有差异
      if (tempData.updateField) {
        mode = ACTION_MODE.ADD;
      }

      // 把值写入表单中
      const stepArr: any[] = [];
      const formObj: ObjectType = {};
      const curIndex = 0;
      let curKey = '';
      // 新暂存表单
      let newTempForm = {};

      PERSONAL_STEP_SORT.forEach((key: string, index: number) => {
        let finished = false;
        let list = [];
        // 查看模式使用历史暂存的配置数据， 创建、修改、补充模式使用最新的配置数据
        if (mode === ACTION_MODE.VIEW) {
          finished = true;
          list = jsonData[key]?.list;
        } else {
          // 读取该模块是否已经完成
          finished = !!jsonData[key]?.finished;
          list = configData[key];
          if (list && list.length) {
            list.forEach((item: MetaDataItem) => {
              const value = jsonData?.form?.[item.fieldCode];
              // 此字段表示是否是撤销后的进件
              if (jsonData?.isLastOrderData === '1') {
                // 撤销后二次进件的，需要判断是否可以反显
                if (value && FIELD_CAN_SHOW.includes(item.fieldCode)) {
                  item.attribute.value = value;
                }
              } else if (value) {
                item.attribute.value = value;
              } else if (!value && !item.attribute.allowNull) {
                // 如果该模块存在必填项却没有值，则需要把该模块置为未完成，让用户填写
                finished = false;
              }

              // 生成一份和当前配置保持一致的数据值暂存数据
              newTempForm = {
                ...newTempForm,
                [item.fieldCode]: value,
              };
            });
          }
        }
        if (list && list.length) {
          formObj[key] = {
            finished,
            list,
          };

          switch (mode) {
            case ACTION_MODE.NEW:
              stepArr.push({ title: PERSONAL_STEP_TITLE[key], ename: key });
              break;
            case ACTION_MODE.ADD:
              // 增加模式只需要向用户展示需要补充填写的模块和字段，
              // 所以要剔除已完成的模块
              if (!finished) {
                stepArr.push({ title: PERSONAL_STEP_TITLE[key], ename: key });
              }
              break;
            case ACTION_MODE.EDIT:
            case ACTION_MODE.VIEW:
              stepArr.push({ title: PERSONAL_STEP_TITLE[key], ename: key });

              break;
            default:
              break;
          }
        }
      });

      if (stepArr.length) {
        if ([ACTION_MODE.NEW, ACTION_MODE.ADD].includes(mode)) {
          curKey = stepArr[curIndex === stepArr.length ? stepArr.length - 1 : curIndex]?.ename;
        }
        console.log('allSteps', stepArr);
        // 暂存值数据,表单数据需要用新配置的表单数据值覆盖掉
        valueData.current = { ...jsonData, form: newTempForm };
        setMode(mode); // 操作模式
        sortSpecialFileItem(formObj); //  处理排序
        setMetaData(formObj); // 元数据
        setAllSteps(stepArr);
      }
      setLoading(false);
    }
  };

  //  读取暂存的风控字段
  const initEnterConfigFromTemp = async () => {
    setLoading(true);
    // 获取三要素信息
    const res = await getBasePersonalInfo(orderNo);
    setBaseInfo(res?.data);
    // 读取暂存的数据
    getTempMetaData(orderNo)
      .then((res: any) => {
        const { data } = res;
        if (!data?.first) {
          // 第一个对象就不存在的话，表示从没有暂存过
          initEnterConfig(); // 此时从风控拉取最原始的配置字段
          return;
        }
        let jsonStr = '';
        TEMP_PARAMS.forEach((key: string) => {
          if (data[key]) {
            jsonStr += data[key];
          }
        });

        const jsonData = JSON.parse(jsonStr);
        const stepArr: any[] = [];
        PERSONAL_STEP_SORT.forEach((key: string) => {
          if (jsonData[key]) {
            stepArr.push({ title: PERSONAL_STEP_TITLE[key], ename: key });
          }
        });

        setAllSteps(stepArr);
        sortSpecialFileItem(jsonData); //  处理排序
        setMetaData(jsonData); // 元数据
        console.log('metadata', jsonData);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (productCode && orderNo) {
      // initEnterConfigFromTemp();
      initEnterConfig();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [productCode, orderNo]);

  // 检查图片是否上传完成
  const checkImgLoadSuccess = (values: Record<string, any>) => {
    let validate = true;
    Object.keys(values).every((key) => {
      const val = values[key];
      if (typeof val === 'string' && /(fakepath)/.test(val)) {
        message.warning('请等待图片上传完成！');
        validate = false;
        return false;
      }
      return true;
    });
    return validate;
  };

  // 处理表单数据，把表单值写到元数据里面
  const parseFormData = () => {
    const values = form.getFieldsValue();

    // 保存配置草稿的原因是，为了后续追溯有依据，也为了查看个人信息时候能查看到历史配置
    Object.keys(metaData).forEach((key) => {
      const target: SubmitDataItem = metaData[key];
      target.list.forEach((item) => {
        let value = values[item.fieldCode];
        if (item.attribute.fieldType === 'OPTIONS') {
          value = [value];
        }
        // 在表单里的模块才能用表单读取的值进行覆盖
        if (allSteps.find((item) => item.ename === key)) {
          item.attribute.value = value;
        }
      });

      if (!target.finished) {
        target.finished = true;
      }
    });

    // 把表单map组装进去，暂存到后端
    const result = {
      ...metaData,
      form: {
        ...valueData.current.form,
        ...values,
      },
      isLastOrderData: false, // 保存草稿后要把撤销后进件的标识写成false，后面就正常填写信息
    };
    console.log('parseFormData', result);
    valueData.current = result;
    return result;
  };

  //  保存草稿
  const tempSave = (showMsg?: boolean) => {
    if (loading) return;
    setLoading(true);
    return new Promise((resolve, reject) => {
      try {
        const parseData = parseFormData();
        const jsonStr = JSON.stringify(parseData);
        const jsonArr: any = splitJsonStr(jsonStr, TEMP_PARAMS.length);
        const params: any = {};
        TEMP_PARAMS.forEach((key: string, index: number) => {
          params[key] = jsonArr[index];
        });
        const remark = remarkForm?.getFieldValue('remark');
        // 发送请求
        temporaryStorage({ orderNo, ...params, remark })
          .then(() => {
            if (showMsg) {
              message.success('保存成功');
            }

            resolve(true);
          })
          .catch(() => {
            reject();
          })
          .finally(() => {
            setLoading(false);
          });
      } catch {
        setLoading(false);
        reject();
      }
    });
  };

  // 处理最后保存的数据
  const parseFinalValue = (data: any[]) => {
    const result: Record<string, any> = {};
    data.forEach((item: MetaDataItem) => {
      const key = item.fieldCode;
      let value = item.attribute.value;
      if (item.attribute.fieldType === 'DATE' && value) {
        value = dayjs(value).format('YYYY-MM-DD');
      }
      if (item.attribute.fieldType === 'CITY' && value) {
        value = formatCity(value);
      }
      if (Array.isArray(value)) {
        value = value.join('');
      }
      result[key] = value;
    });
    return result;
  };

  const handleFinishFailed = () => {
    // 滚动到校验报错的位置
    setTimeout(() => {
      document
        .querySelector('.ant-form-item-has-error')
        ?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 0);
  };

  // 最终提交
  const onFormFinish = async (values: Record<string, any>) => {
    if (loading) {
      return;
    }
    // 检查图片是否完成上传
    if (!checkImgLoadSuccess(values)) return;
    // 先保存一下草稿，这样h5才能正常查看填写的个人资料
    await tempSave();
    setLoading(true);
    const remark = remarkForm?.getFieldValue('remark');
    const params: FinalRiskParams = {
      orderNo,
      riskFieldList: [],
      remark,
    };
    let isError = false;
    Object.keys(metaData).forEach((key: string) => {
      const moduleId = metaData[key].list[0].attribute.moduleId;
      const obj: FinalRiskParams = { riskKey: '', param: {} };
      obj.riskKey = moduleId;
      const values = parseFinalValue(metaData[key].list);
      if (Object.keys(values).length) {
        obj.param = values;
      } else {
        isError = true; // 数据异常要给出提示，不能提交到后端
      }
      params.riskFieldList.push(obj);
    });

    if (isError) {
      message.warning('表单数据异常，请重新点击提交按钮或刷新页面重试');
      return;
    }

    console.log('finalParams', params);
    if (mode === ACTION_MODE.NEW) {
      // 提交请求
      await submitFinalRiskField(params);
    } else if (mode === ACTION_MODE.EDIT) {
      await editFinalRiskField(params);
    } else if (mode === ACTION_MODE.ADD) {
      await editFinalRiskField(params);
      // 补充之后不需要在补充了，刷新一下配置接口，变成编辑模式
      initEnterConfig();
    }
    message.success('提交成功！');
    // 通知父组件刷新页面
    props.onSubmit();
    setLoading(false);
  };

  const clearForm = () => {
    const values = form.getFieldsValue();
    const keys = Object.keys(values);
    // 基础信息不能被清空
    const filterKeys = keys.filter((key) => !baseFieldKeys.includes(key));
    form.resetFields(filterKeys);
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    clear: () => {
      clearForm();
      tempSave();
    },
    save: () => {
      tempSave(true);
    },
    submit: () => {
      form.submit();
    },
  }));

  return (
    <Spin spinning={loading}>
      {mode === ACTION_MODE.ADD && (
        <div className="form-tips">
          <ExclamationCircleOutlined />
          <span>识别到产品方案发生变更，请补充申请资料</span>
        </div>
      )}
      <Form
        form={form}
        onFinishFailed={handleFinishFailed}
        onFinish={onFormFinish}
        wrapperCol={{ span: 16 }}
        labelCol={{ span: 8 }}
      >
        <Row gutter={[10, 8]} align="top">
          {allSteps.map((item, index) => {
            return (
              <Col span={12}>
                <ProCard title={item.title} bordered headerBordered collapsible>
                  <FormPage
                    form={form}
                    data={metaData[item.ename]?.list}
                    baseInfo={baseInfo}
                    step={index}
                    viewMode={readOnly}
                    editMode={mode === ACTION_MODE.EDIT}
                  />
                </ProCard>
              </Col>
            );
          })}
        </Row>
      </Form>
    </Spin>
  );
});

export default PersonalInfo;
