/* eslint-disable react-hooks/exhaustive-deps */
import ProForm, { ModalForm } from '@ant-design/pro-form';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { message } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import { cancelPolicy, getLookCarInfoInfo } from '../services';
import styles from '../styles/index.less';
import type { IcancelPolicyParams, IremissionDetailItem } from '../types';
import UploadVoucher from './UploadVoucher';
type EditTableProps = {
  onChange?: (value: any[]) => void;
};
const EditTable: React.FC<EditTableProps> = (props) => {
  const { orderNo } = (history as any).location.query;
  const { onChange } = props;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);

  useEffect(() => {
    onChange?.(dataSource);
  }, [dataSource]);
  const columns: ProColumns<any>[] = [
    {
      title: <div className={styles.required}>车辆</div>,
      key: 'schemaItemNo',
      dataIndex: 'schemaItemNo',
      valueType: 'select',
      params: {
        key: orderNo,
      },
      request: async () => {
        const data = await getLookCarInfoInfo(orderNo);
        return data.map((item) => {
          const { schemaItemNo, licensePlateNo } = item;
          return {
            label: licensePlateNo,
            value: schemaItemNo,
          };
        });
      },
    },
    {
      title: <div className={styles.required}>退保金额</div>,
      key: 'cancelAmount',
      dataIndex: 'cancelAmount',
      valueType: 'money',
    },
    {
      title: <div className={styles.required}>凭证</div>,
      key: 'attach',
      dataIndex: 'attach',
      renderFormItem() {
        return <UploadVoucher />;
      },
    },
    {
      title: '操作',
      dataIndex: 'delete',
      valueType: 'option',
    },
  ];
  return (
    <EditableProTable<IremissionDetailItem>
      bordered
      rowKey="id"
      value={dataSource}
      toolBarRender={false}
      onChange={(values) => {
        console.log('values123', values);
        setDataSource(values);
      }}
      columns={columns}
      recordCreatorProps={{
        newRecordType: 'dataSource',
        position: 'top',
        // 新增一行的数据
        record: () => ({
          id: Date.now(), // 如果不写会用 index 但是 不会触发editable.onChange 导致bug
        }),
      }}
      editable={{
        type: 'multiple',
        onValuesChange: (record, recordList) => {
          console.log('record', record);
          console.log('recordList', recordList);
          setDataSource(recordList);
        },
        editableKeys,
        onChange: (keys) => {
          // 删除 添加 一行数据时会触发 keys 表示目前存在的行
          console.log('keys', keys);
          setEditableRowKeys(keys);
        },
        actionRender: (row, _, dom) => {
          return [dom.delete];
        },
      }}
    />
  );
};

type Props = {
  children: any;
};
const CancelPolicyModal: React.FC<Props> = (props) => {
  const { orderNo } = (history as any).location.query;

  function verifyCancelPolicy(cancelPolicyList: IcancelPolicyParams[]) {
    if (!cancelPolicyList?.length) {
      message.error('尚未添加数据');
      return false;
    }
    for (let i = 0; i < cancelPolicyList?.length; i++) {
      const item = cancelPolicyList[i];
      if (!item.attach?.length) {
        message.error('凭证是必填的');
        return false;
      }
      if (!item.cancelAmount) {
        message.error('退保金额是必填的');
        return false;
      }
      if (!item.schemaItemNo) {
        message.error('车辆是必填的');
        return false;
      }
    }
    return true;
  }
  const onFinish = async (values: { cancelPolicyList: IcancelPolicyParams[] }) => {
    console.log('valuesvalues', values);

    if (verifyCancelPolicy(values.cancelPolicyList)) {
      await cancelPolicy(values.cancelPolicyList.map((item) => ({ ...item, orderNo })));
      message.success('操作成功');
      return true;
    }
    return false;
  };

  return (
    <ModalForm<{ cancelPolicyList: IcancelPolicyParams[] }>
      trigger={props.children}
      title="退保结项"
      //   width={1000}
      modalProps={{
        destroyOnClose: true,
      }}
      layout="horizontal"
      onFinish={onFinish}
    >
      <ProForm.Item name="cancelPolicyList">
        <EditTable />
      </ProForm.Item>
    </ModalForm>
  );
};

export default memo(CancelPolicyModal);
