/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-29 16:14:45
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-06-03 17:44:01
 * @FilePath: /lala-finance-biz-web/src/components/ReleaseCom/CommonDraggleUpload.tsx
 * @Description: CommonDraggleUpload
 */
import { ProFormUploadDragger } from '@ant-design/pro-form';

import React, { useRef, useState } from 'react';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import PreviewModalCon from '../PreviewModal';
interface ComImageUploadProps {
  extra?: string;
  extraTrack?: boolean;
  label?: string;
  name: string;
  width?: string;
  title?: string;
  max?: number;
  accept?: string;
  // rulesMessage: string;
  listType?: 'picture' | 'text' | 'picture-card' | undefined;
  rules?: any;
  labelCol?: any;
  size: number;
  icon?: any;
  buttonProps?: any;
  fileListEdit?: [];
  mapFileList: (allFileList: any) => void;
  desPath?: string;
  orderNo?: string;
  multiple?: boolean;
  description?: string;
  previewType?: string; //  多图预览
  sceneCode?: 0 | 1; // 0 默认场景 1 代表接口只能串行在后端执行，大文件上传减轻服务器压力 // 不传和传0效果是一样，但是不能传undefined
}
interface previewObj {
  previewVisible: boolean;
  previewTitle: string;
  previewImage: string;
  previewIndex: number;
}
/**
 * @Date: 2021-04-26 15:40:18
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: Do not edit
 * @FilePath: Do not edit
 * @param {*} props
 */
const CommonDraggleUpload: React.FC<ComImageUploadProps> = ({
  extra,
  extraTrack = true,
  label,
  name,
  width,
  title,
  max,
  accept,
  listType,
  rules,
  labelCol,
  size,
  icon,
  buttonProps,
  fileListEdit,
  mapFileList,
  desPath,
  orderNo = '',
  multiple,
  previewType,
  sceneCode = 0,
  ...rest
}) => {
  // console.log(fileListEdit);
  const base_url = getBaseUrl();
  const [errorTips, handleError] = useState<any>({ error: false, tips: '' });
  const [previewObj, handlePreviewObj] = useState<previewObj>({});
  const [fileListOwn, handleFileList] = useState<[]>(fileListEdit || []);
  const upload_url = `${base_url}/repayment/oss/common/uploadfile`;
  const previewRef = useRef<any>();
  const beforeUpload = (file: any, fileList: any) => {
    //上传数量
    let isSizeError = false;
    //文件个数限制存在  因为多选的时候和max冲突，需要额外判断
    if (max) {
      isSizeError = fileList?.length >= max;
    }
    if (isSizeError) {
      handleError({
        error: true,
        tips: `文件个数最多为${max}个,请检查`,
      });
    }
    // console.log(file);
    const isLt10M = file.size / 1024 / 1024 < size;
    if (!isLt10M) {
      handleError({ error: true, tips: `有文件超过${size}兆,请检查` });
      // eslint-disable-next-line no-param-reassign
      file.status = 'error';
    }
    //有些文件限制了accept，还是能上传，手动加判断，兼容大小写后缀
    const exc = file?.name.substring(file?.name.lastIndexOf('.') + 1)?.toLowerCase();
    if (extraTrack && !extra?.includes(exc)) {
      handleError({ error: true, tips: '格式不正确' });
      // eslint-disable-next-line no-param-reassign
      file.status = 'error';
    }
    return isLt10M && (!extraTrack || (extraTrack && extra?.includes(exc)));
  };

  const handleChange = async (props: any) => {
    const fileList =
      props?.fileList.map((item: any) => {
        //  业务API响应错误, 0和200为上传成功
        if (item.response && ![0, 200].includes(item.response?.ret)) item.status = 'error';
        return item;
      }) || [];

    //上传数量
    let isSizeError = false;
    //文件个数限制存在  因为多选的时候和max冲突，需要额外判断
    if (max) {
      isSizeError = fileList?.length > max;
    }

    const isError = fileList.some((item: any) => {
      return item.status === 'error' && item.size / 1024 / 1024 > size;
    });
    const onlyError = fileList.some((item: any) => {
      return item.status === 'error';
    });
    // 超过size
    if (isError || onlyError) {
      // 显示10
      handleError({
        error: true,
        tips: isError ? `有文件超过${size}兆,请检查` : '上传错误,请检查',
      });
    } else if (isSizeError) {
      handleError({
        error: true,
        tips: `文件个数最多为${max}个,请检查`,
      });
    } else {
      handleError({ error: false, tips: '' });
    }

    handleFileList(fileList);
    mapFileList({ [name]: fileList });
  };
  return (
    <>
      <ProFormUploadDragger
        width={width}
        extra={extra}
        label={label}
        name={name}
        title={title}
        max={max}
        listType={listType}
        icon={icon}
        buttonProps={buttonProps}
        labelCol={labelCol}
        accept={accept}
        onChange={handleChange}
        rules={(rules || []).concat([
          {
            validator: () => {
              // 校验必填
              if (errorTips?.error) {
                return Promise.reject(new Error(errorTips?.tips));
              }
              return Promise.resolve();
            },
          },
        ])}
        action={upload_url}
        fieldProps={{
          fileList: fileListOwn,
          headers: { ...getAuthHeaders() },
          name: 'file',
          data: {
            acl: 'PUBLIC_READ',
            destPath: desPath || 'USER_ORDER_APPLY_MONEY',
            orderNo,
            sceneCode,
          }, // 后端商量默认格式
          beforeUpload,
          onPreview: (file) => {
            previewRef.current?.handlePreview(file);
          },
          multiple,
        }}
        {...rest}
      />
      <PreviewModalCon
        previewRef={previewRef}
        fileList={fileListOwn}
        previewObj={previewObj}
        handlePreviewObj={handlePreviewObj}
        loadType={true}
        previewType={previewType}
      />
    </>
  );
};
export default CommonDraggleUpload;
