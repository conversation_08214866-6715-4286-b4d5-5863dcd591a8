import HeaderTab from '@/components/HeaderTab';
import {
  GUARANTEE_TYPES,
  LICENSE_TYPES,
  LICENSE_TYPES_MAP,
  LICENSE_TYPES_MAP_ENUM,
  PRODUCT_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_CODE,
  USER_TYPE,
} from '@/enums';
import globalStyle from '@/global.less';
import { getProductNameEnum } from '@/services/enum';
import { filterProps } from '@/utils/tools';
import { disableFutureDate, downLoadExcel } from '@/utils/utils';
import { ExclamationCircleOutlined, SaveOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, Link } from '@umijs/max';
import { Button, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import BigNumber from 'bignumber.js';
import React, { useEffect, useRef, useState } from 'react';
import LicenseTabsCom from './components/LicenseTabsCom';
import RegionInputInterest from './components/RegionInputInterest';
import type { ProductListItem, ProductListParams } from './data';
import { productExport, queryProduct, updateStatus } from './service';
import {
  firstClassificationMap,
  firstClassificationValueEnum,
  secondClassificationMap,
  secondClassificationValueEnum,
} from './types';

const getExport = (form: ProductListParams) => {
  productExport(form).then((res) => {
    downLoadExcel(res);
  });
};
const mapProductDetail = {
  [PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING]: 'bill',
  [PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE]: 'lease',
  [PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN]: 'cash',
};

const ProductList: React.FC<any> = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const [curRow, setCurrentRow] = useState<ProductListItem>();
  const handleConfirm = () => {
    // console.log(curRow?.id);
    const func = updateStatus({
      id: curRow?.id as number,
      status: curRow?.status === 1 ? 0 : 1,
      productCode: curRow!.productCode,
    });
    return func.then(() => true);
  };

  const licenseTabsRef = useRef<any>(null);
  const [licenseType, setLicenseType] = useState<string>(LICENSE_TYPES.AFFILIATE);

  // 所有的产品
  const [productCodeList, setProductCodeList] = useState<{ value: string; label: string }[]>([]);

  // 不同的一级和二级 有不同的选项
  const [productCodeValueEnum, setProductCodeValueEnum] = useState<Record<string, string>>({});

  // 不同的一级code 会有不同的 二级产品 的选项
  // 注意每次都是从 所有的分类中找
  const [secondaryClassificationValueEnum, setSecondaryClassificationValueEnum] = useState<
    Record<string, string>
  >(secondClassificationValueEnum);

  useEffect(() => {
    getProductNameEnum().then((data) => {
      setProductCodeList(data);
      const valueEnum = getProductValueEnum(data);
      setProductCodeValueEnum(valueEnum);
    });
  }, []);

  function getProductValueEnum(productCodeList: { value: string; label: string }[]) {
    const firstCode = formRef.current?.getFieldValue('classification');
    const secondCode = formRef.current?.getFieldValue('secondaryClassification');
    const valueEnum: any = {};

    productCodeList.forEach((productCodeItem) => {
      const { value: productCode, label } = productCodeItem;
      if (secondCode) {
        // 以二级分类优先
        if (secondCode === productCode.substring(0, 4)) {
          valueEnum[productCode] = label;
        }
      } else if (firstCode) {
        if (firstCode === productCode.substring(0, 2)) {
          valueEnum[productCode] = label;
        }
      } else {
        valueEnum[productCode] = label;
      }
    });
    return valueEnum;
  }

  function getSecondaryClassificationValueEnum() {
    const classification = formRef.current?.getFieldValue('classification');
    const valueEnum = {};
    for (const value in secondClassificationValueEnum) {
      const firstCode = value.substring(0, 2);
      if (firstCode === classification) {
        valueEnum[value] = secondClassificationValueEnum[value];
      }
    }
    return classification ? valueEnum : secondClassificationValueEnum;
  }

  const columns: ProColumns<ProductListItem>[] = [
    {
      title: '产品ID',
      dataIndex: 'productCode',
      fieldProps: {
        onChange: (e) => {
          formRef.current?.setFieldValue('productCode', e.target.value?.trim());
        },
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      valueEnum: productCodeValueEnum,
      valueType: 'select',
      // request: getProductNameEnum,
      fieldProps: {
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        onChange: (val: string) => {
          formRef.current?.setFieldValue('classification', val?.substring(0, 2));
          formRef.current?.setFieldValue('secondaryClassification', val?.substring(0, 4));

          licenseTabsRef?.current?.setVisible(
            val?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE,
          );
        },
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      hideInTable: true,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      valueType: 'select',
      // valueEnum: CLASSIFICATION,
      search: {
        transform(value: string) {
          return {
            classification: firstClassificationMap[value],
          };
        },
      },
      valueEnum: firstClassificationValueEnum,
      fieldProps: {
        onChange: (val: string) => {
          formRef.current?.setFieldValue('secondaryClassification', undefined);
          formRef.current?.setFieldValue('productCode', undefined);

          const valueEnum = getProductValueEnum(productCodeList);
          setProductCodeValueEnum(valueEnum);

          const secondaryClassificationValueEnum = getSecondaryClassificationValueEnum();
          setSecondaryClassificationValueEnum(secondaryClassificationValueEnum);

          licenseTabsRef?.current?.setVisible(val === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE);
        },
      },
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondaryClassification',
      valueType: 'select',
      search: {
        transform(value: string) {
          return {
            secondaryClassification: secondClassificationMap[value],
          };
        },
      },
      // valueEnum: SECONDARY_CLASSIFICATION,
      valueEnum: secondaryClassificationValueEnum,
      fieldProps: {
        onChange: (val: string) => {
          if (!val) return;
          formRef.current?.setFieldValue('classification', val?.substring(0, 2));
          formRef.current?.setFieldValue('productCode', undefined);

          const valueEnum = getProductValueEnum(productCodeList);
          setProductCodeValueEnum(valueEnum);

          licenseTabsRef?.current?.setVisible(
            val?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE,
          );
        },
      },
    },
    {
      title: '用户类型',
      dataIndex: 'userType',
      valueType: 'select',
      valueEnum: USER_TYPE,
    },
    {
      title: '产品类型',
      dataIndex: 'selfType',
      valueType: 'select',
      valueEnum: {
        1: '自营', // 自营
        2: '非自营', // 非自营
      },
    },
    {
      title: '上牌类型',
      dataIndex: 'registerLicenceType',
      valueType: 'select',
      valueEnum: LICENSE_TYPES_MAP,
      hideInSearch: true,
      render: (_, record) => {
        return LICENSE_TYPES_MAP[LICENSE_TYPES[record?.registerLicenceType]] || '-';
      },
    },
    {
      title: '利率',
      dataIndex: 'interest',
      // search: {
      //   transform: (value) => {
      //     console.log(value);
      //   },
      // },
      renderFormItem: (_: any, { type, defaultRender, ...rest }: any) => {
        return (
          <RegionInputInterest
            minValueReplace="startInterest"
            maxValueReplace="endInterest"
            {...rest}
            className={globalStyle.w100}
          />
        );
      },

      // filterDropdown: (_, { setSelectedKeys, confirm, clearFilters }) => {
      //   const handleSearch = () => {
      //     confirm();
      //   };

      //   const handleReset = () => {
      //     clearFilters();
      //   };

      //   return (
      //     <div style={{ padding: 8 }}>
      //       <InputNumber
      //         style={{ width: '45%', marginBottom: 8, display: 'block' }}
      //         placeholder="最小值"
      //         onChange={(value) => setSelectedKeys(value ? [value, undefined] : [])}
      //       />
      //       <InputNumber
      //         style={{ width: '45%', marginBottom: 8, display: 'block' }}
      //         placeholder="最大值"
      //         onChange={(value) => setSelectedKeys(value ? [undefined, value] : [])}
      //       />
      //       <div>
      //         <Button
      //           type="primary"
      //           onClick={handleSearch}
      //           size="small"
      //           style={{ width: '45%', marginRight: 8 }}
      //         >
      //           筛选
      //         </Button>
      //         <Button onClick={handleReset} size="small" style={{ width: '45%' }}>
      //           重置
      //         </Button>
      //       </div>
      //     </div>
      //   );
      // },
      // onFilter: (value, record) => {
      //   const [min, max] = value;
      //   const number = record.numberRange;
      //   return (min === undefined || number >= min) && (max === undefined || number <= max);
      // },
      render: (_, row) => {
        return row?.interest ? (
          <span>{new BigNumber(row.interest).times(100).toString()}%</span>
        ) : (
          <>-</>
        );
      },
    },
    // {
    //   title:'',
    //   hideInTable:true,
    //   renderFormItem
    // },
    {
      title: '期数',
      dataIndex: 'periods',
      key: 'periods',
      render: (_, row) => {
        return row?.periodsList ? row?.periodsList?.join('/') : '-';
      },
    },
    {
      title: '是否担保',
      dataIndex: 'guaranteeType',
      valueType: 'select',
      valueEnum: GUARANTEE_TYPES,
    },
    {
      title: '担保期数',
      dataIndex: 'guaranteePeriods',
      key: 'guaranteePeriods',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.createdAt;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          createdAtStart: `${value[0].split(' ')[0]} 00:00:00`,
          createdAtEnd: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '禁用', status: 'Error' },
        1: { text: '启用', status: 'Success' },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => {
        const { productCode, id } = record;
        const detailType = mapProductDetail[record?.productCode.substring(0, 2)];
        const secondProductCode = productCode.substring(0, 4);
        const oldUrl = `/businessMng/product-${detailType}-detail?productCode=${productCode}&id=${id}&secondProductCode=${secondProductCode}`;
        const newUrl = `/businessMng/add-product?productCode=${productCode}&id=${id}&secondProductCode=${secondProductCode}`;
        // 小圆车险、融资租赁跳转可编辑页面
        const url = [
          SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE,
          SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
        ].includes(secondProductCode as any)
          ? newUrl
          : oldUrl;
        return (
          <>
            <Link to={url}>查看详情</Link>
            <a
              onClick={() => {
                handleOptVisible(true);
                setCurrentRow(record);
              }}
              className={globalStyle?.ml10}
            >
              {record?.status ? '禁用' : '启用'}
            </a>
          </>
        );
      },
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer
        extra={
          <Link to="/businessMng/draft-list">
            <SaveOutlined style={{ color: '#1677ff', fontSize: 18 }} />
            <span> 草稿箱</span>
          </Link>
        }
      >
        <ProTable<ProductListItem>
          // headerTitle="查询表格"
          actionRef={actionRef}
          formRef={formRef}
          rowKey="productCode"
          scroll={{ x: 'max-content' }}
          search={{
            labelWidth: 100,
          }}
          request={async ({ current, interest, ...rest }) => {
            const interestTemp: any = {};
            if (interest) {
              const { startInterest, endInterest } = interest;
              if (startInterest || startInterest === 0) {
                interestTemp.startInterest = Number(new BigNumber(startInterest).div(100));
              }
              if (endInterest || endInterest === 0) {
                interestTemp.endInterest = Number(new BigNumber(endInterest).div(100));
              }
            }
            const { data, total } = await queryProduct({
              pageNumber: current,
              ...interestTemp,
              ...filterProps({
                ...rest,
                registerLicenceType: licenseTabsRef?.current?.visible
                  ? LICENSE_TYPES_MAP_ENUM[licenseType]
                  : undefined,
              }),
            });
            return {
              data,
              success: true,
              // 不传会使用 data 的长度，如果是分页一定要传
              total,
            };
          }}
          onReset={() => {
            licenseTabsRef?.current?.setVisible(false); //  重置则隐藏tabs
          }}
          headerTitle={
            <LicenseTabsCom
              tabsRef={licenseTabsRef}
              onChange={(val) => {
                setLicenseType(val as string);
                formRef?.current?.submit();
              }}
              onReset={() => setLicenseType(LICENSE_TYPES.AFFILIATE)}
            />
          }
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  const { createdAt, interest, ...data } = formRef?.current?.getFieldsValue();
                  let newForm = { ...data };
                  if (createdAt?.length) {
                    const createdAtStart = `${createdAt[0].format('YYYY-MM-DD')} 00:00:00`;
                    const createdAtEnd = `${createdAt[1].format('YYYY-MM-DD')} 23:59:59`;
                    newForm = { ...data, createdAtStart, createdAtEnd };
                  }

                  const interestTemp: any = {};
                  if (interest) {
                    const { startInterest, endInterest } = interest;
                    if (startInterest || startInterest === 0) {
                      interestTemp.startInterest = Number(new BigNumber(startInterest).div(100));
                    }
                    if (endInterest || endInterest === 0) {
                      interestTemp.endInterest = Number(new BigNumber(endInterest).div(100));
                    }
                  }
                  newForm = {
                    ...newForm,
                    ...interestTemp,
                    classification: firstClassificationMap[newForm.classification],
                    registerLicenceType: licenseTabsRef?.current?.visible
                      ? LICENSE_TYPES_MAP_ENUM[licenseType]
                      : undefined,
                  };
                  getExport(newForm);
                  // getExport(formRef?.current?.getFieldsValue());
                }}
              >
                导出
              </Button>,
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  history.push('/businessMng/add-product?secondProductCode=0201&isAddProduct=1');
                }}
              >
                创建产品
              </Button>,
            ];
          }}
          columns={columns}
        />
        <ModalForm
          title="提示"
          width="400px"
          modalProps={{
            centered: true,
            destroyOnClose: true,
            afterClose: () => {
              setCurrentRow(undefined);
            },
          }}
          visible={optVisible}
          onVisibleChange={handleOptVisible}
          onFinish={async () => {
            const success: boolean = await handleConfirm();
            if (success) {
              message.success(`${curRow?.status ? '禁用' : '启用'}成功`);
            }
            actionRef?.current?.reload();
            handleOptVisible(false);
            return true;
          }}
        >
          <div>
            <ExclamationCircleOutlined className={globalStyle.iconCss} />
            是否确认{curRow?.status ? '禁用' : '启用'}该产品?
          </div>
        </ModalForm>
      </PageContainer>
    </>
  );
};

export default ProductList;
