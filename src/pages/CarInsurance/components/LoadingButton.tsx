import { Button, ButtonProps } from 'antd';
import React from 'react';
import { useState } from 'react';

type Props = {
  text: string;
  onClick?: () => Promise<void>; // 必须是 async 或者 返回值是promise类型
  buttonProps?: Omit<ButtonProps, 'onClick' | 'loading'>;
};
const LoadingButton: React.FC<Props> = (props) => {
  const { text, onClick, buttonProps } = props;
  const [loading, setLoading] = useState(false);
  return (
    <Button
      {...buttonProps}
      onClick={async () => {
        try {
          setLoading(true);
          await onClick?.();
        } finally {
          setLoading(false);
        }
      }}
      loading={loading}
    >
      {text}
    </Button>
  );
};

export default LoadingButton;
