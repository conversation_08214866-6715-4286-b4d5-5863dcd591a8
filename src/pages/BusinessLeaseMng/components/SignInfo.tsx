// 签约信息
import globalStyle from '@/global.less';
import { <PERSON><PERSON>, Card, Tabs } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import { LICENSE_TYPE } from './CarInfo/ChooseCar/data';
import Contract from './Contract';
import Notarization from './Notarization';

export type SignInfoProps = {
  orderNo: string;
  dataExtend?: any;
  data?: any;
};

enum TABS {
  CONTRACT = 'CONTRACT', // 合同
  NOTARIZATION = 'NOTARIZATION', // 公证
}

const SignInfo = (props: SignInfoProps) => {
  const { data, dataExtend, orderNo } = props;
  const [currentTab, setCurrentTab] = useState<string>(TABS.CONTRACT);

  const contractRef = useRef<any>(null);

  const contractList = useMemo(() => {
    const list = [
      {
        key: TABS.CONTRACT,
        label: '合同',
        children: (
          <Contract
            ref={contractRef}
            dataContract={dataExtend?.contractInfo}
            orderNo={orderNo}
            userName={data?.userName}
            productCode={data?.productCode}
            carCity={dataExtend?.carInfo?.carCityDetail?.label}
            guaranteeType={data?.guaranteeType}
          />
        ),
      },
    ];
    // 个户需要展示赋强公证
    if (dataExtend?.carInfo?.licenseType === LICENSE_TYPE.PERSONAL) {
      list.push({
        key: TABS.NOTARIZATION,
        label: '赋强公证',
        children: <Notarization data={dataExtend?.notarizationInfo} />,
      });
    }
    return list;
  }, [dataExtend, data, orderNo]);

  return (
    <Card title="签约" className={globalStyle.mt30}>
      <Tabs
        tabBarExtraContent={{
          right: currentTab === TABS.CONTRACT && (
            <Button
              type="primary"
              onClick={() => {
                if (currentTab === TABS.CONTRACT) {
                  contractRef?.current?.downLoadZip();
                }
              }}
            >
              批量下载
            </Button>
          ),
        }}
        items={contractList}
        onChange={(key: string) => {
          setCurrentTab(key);
        }}
      />
    </Card>
  );
};

export default SignInfo;
