import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import LoadingButton from '@/components/LoadingButton';
import { LOAN_MANAGE_CODE } from '@/enums';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl, getBlob, saveAs } from '@/utils/utils';
import { DeleteOutlined, DownloadOutlined } from '@ant-design/icons';
import { ProFormUploadDragger } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import type { UploadFile } from 'antd';
import { Col, message, Row, Tag, Upload } from 'antd';
import type { Rule } from 'antd/es/form';
import imageCompression from 'browser-image-compression';
import React, { memo, useRef } from 'react';
import { vehicleIdentifyOcrInfo } from '../service';
import type { UloanStatus } from '../types/map';
import { useIsLeaseChannelUser } from '../utils';

export const channelUseAuthMap: Record<string, (string | number)[]> = {
  delete: [LOAN_MANAGE_CODE.WAIT_CAPTURE],
  ocr: [LOAN_MANAGE_CODE.WAIT_CAPTURE, LOAN_MANAGE_CODE.REJECTED],
  submit: [LOAN_MANAGE_CODE.WAIT_CAPTURE, LOAN_MANAGE_CODE.REJECTED],
  save: [LOAN_MANAGE_CODE.WAIT_CAPTURE, LOAN_MANAGE_CODE.REJECTED],
};

export const fileTypeOCRMap = {
  carPurchaseInvoice: 1, // 购车发票
  trafficInsurance: 5,
  businessInsurance: 6,
};

type Props = {
  label: string;
  name: string;
  max?: number;
  extra?: string;
  size?: number;
  accept?: string;
  isNeedOCR?: boolean; // 是否需要ocr识别
  sceneCode?: 0 | 1; // 0 默认场景 1 代表接口只能串行在后端执行，大文件上传减轻服务器压力 // 不传和传0效果是一样，但是不能传undefined
  applyData?: (operateType: 'save' | 'submit', ocr: Record<string, string>) => void; // 保存和提交的方法
  status: UloanStatus;
  noGuaranteeTypes: boolean;
  required?: boolean;
  rules?: Rule[];
  ocrData?: string; // ocr 数据
  customHeader?: React.ReactNode;
};
const LoanProFormUploadDragger: React.FC<Props> = (props) => {
  const {
    label,
    sceneCode = 0,
    isNeedOCR,
    accept = '.doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar',
    name,
    max = 5,
    extra,
    size = 50,
    applyData,
    status,
    required,
    rules,
    ocrData,
    customHeader,
  } = props;
  const imagePreviewRef = useRef<ImagePreviewInstance>(null);

  const ocr = JSON.parse(ocrData || '{}');

  const action = `${getBaseUrl()}/repayment/oss/common/uploadfile`;

  const { orderNo, lendingNo }: any = history.location.query;
  const isRequesting = useRef(false);
  const download = (url: string, filename: string) => {
    message.loading('下载文件中...');
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
      message.destroy();
    });
  };

  const isLeaseChannelUser = useIsLeaseChannelUser();

  const renderDelete = (actions: any) => {
    const deleteJsx = (
      <DeleteOutlined
        onClick={() => {
          actions.remove();
        }}
      />
    );
    if (isLeaseChannelUser) {
      if (channelUseAuthMap.delete.includes(status)) {
        // 如果是渠道用户 看下他 有没有删除权限
        return deleteJsx;
      }
    } else {
      // 非渠道用户
      return deleteJsx;
    }
    return null;
  };

  const renderOcr = (file: UploadFile) => {
    const { filePath } = file as any;
    const ocrJsx = (
      <LoadingButton
        size="small"
        type="primary"
        onClick={async () => {
          const data = await vehicleIdentifyOcrInfo({
            filePath,
            lendingNo,
            fileType: fileTypeOCRMap[name],
          });
          console.log(
            'filePath',
            JSON.stringify({
              ...data,
              filePath,
            }),
          );
          await applyData?.('save', {
            [`${name}Ocr`]: JSON.stringify({
              ...data,
              filePath,
            }),
          });
        }}
      >
        OCR
      </LoadingButton>
    );

    if (isLeaseChannelUser) {
      if (channelUseAuthMap.ocr.includes(status)) {
        return ocrJsx;
      }
    } else {
      return ocrJsx;
    }
    return null;
  };

  return (
    <>
      <Row>
        <Col span={4} style={{ textAlign: 'right', paddingRight: '8px' }}>
          {required && (
            <span style={{ color: '#ff4d4f', fontSize: '14px', marginInlineEnd: '4px' }}>*</span>
          )}
          {label}:
        </Col>
        <Col span={20}>
          {customHeader}
          <ProFormUploadDragger
            name={name}
            action={action}
            max={max}
            rules={rules}
            onChange={(info) => {
              info.fileList.map((file) => {
                const { response } = file;
                if (response?.ret === 200) {
                  // 上传成功
                  const { netWorkPath, filePath } = response?.data || {};

                  // 后续全部用url 作为文件的地址
                  file.url = netWorkPath;
                  (file as any).filePath = filePath;
                }
              });

              const oneFile = (info as any).fileList?.[0];
              const oneFilePath = oneFile?.filePath;
              if (info.fileList.length === 1 && oneFilePath && !ocrData && isNeedOCR) {
                if (isRequesting.current) {
                  return;
                }
                isRequesting.current = true;
                // 自动识别第一张图片 filepath 存在 ocr没识别过
                message.loading('ocr识别中...');
                vehicleIdentifyOcrInfo({
                  filePath: oneFilePath,
                  lendingNo,
                  fileType: fileTypeOCRMap[name],
                })
                  .then((data) => {
                    applyData?.('save', {
                      [`${name}Ocr`]: JSON.stringify({
                        filePath: oneFilePath,
                        ...data,
                      }),
                    });
                  })
                  .finally(() => {
                    isRequesting.current = false;
                    message.destroy();
                  });
              }
            }}
            extra={extra}
            fieldProps={{
              headers: { ...getAuthHeaders() },
              multiple: true,
              name: 'file',
              accept,
              data: {
                acl: 'PUBLIC_READ',
                destPath: 'USER_ORDER_APPLY_MONEY',
                orderNo,
                sceneCode,
              }, // 后端商量默认格式
              itemRender(originNode, file, fileList, actions) {
                const urlList = fileList.map((item) => item.url || '');
                if (file.url) {
                  // 上传成功的展示出来
                  const { name: fileName, url = '', uid } = file;
                  const { filePath } = file as any;
                  return (
                    <div style={{ display: 'flex', gap: 20, marginTop: 10 }} key={uid}>
                      <a
                        // 支持预览的就预览，不支持的就下载
                        title={
                          ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'pdf'].includes(
                            fileName.substring(fileName.lastIndexOf('.') + 1)?.toLowerCase(),
                          )
                            ? '预览'
                            : '下载'
                        }
                        onClick={() => {
                          imagePreviewRef.current?.previewFile({ url, urlList });
                        }}
                        style={{
                          flex: 1,
                        }}
                      >
                        {fileName}
                      </a>
                      {ocr?.filePath === filePath ? <Tag color="success">已识别</Tag> : null}
                      {isNeedOCR && renderOcr(file)}
                      <DownloadOutlined
                        onClick={() => {
                          download(url, fileName);
                        }}
                      />
                      {renderDelete(actions)}
                    </div>
                  );
                } else {
                  // 有进度条样式
                  return originNode;
                }
              },
              beforeUpload(file) {
                if ([',', ':', '，', '：'].some((item) => file.name?.includes(item))) {
                  // 含有其中任何一个 都不能上传
                  message.error('文件名称不能含有中英文逗号和冒号');
                  return false || Upload.LIST_IGNORE;
                }

                if (isNeedOCR) {
                  // ocr识别的图片需要被压缩
                  // pdf 没有压缩方案 限制大小
                  // 图片格式
                  if (file.size > 4 * 1024 * 1024) {
                    const imgTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                    if (imgTypes.includes(file.type)) {
                      message.info(`${file.name}文件大于4m,正在被压缩`);
                      return imageCompression(file, {
                        maxSizeMB: 4,
                        useWebWorker: true,
                        onProgress: (progress) => {
                          console.log('progress', progress);
                        },
                      });
                    } else {
                      message.error('pdf格式的文件必须得小于4m');
                      return false || Upload.LIST_IGNORE;
                    }
                  }
                }

                if (file.size > size * 1024 * 1024) {
                  const error = `${file.name}超过了${size}M`;
                  message.error(error);
                  return false || Upload.LIST_IGNORE;
                }
                return true;
              },
            }}
          />
        </Col>
      </Row>
      <ImagePreview ref={imagePreviewRef} />
    </>
  );
};

export default memo(LoanProFormUploadDragger);
