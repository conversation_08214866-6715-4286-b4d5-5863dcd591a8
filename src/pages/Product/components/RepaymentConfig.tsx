import globalStyle from '@/global.less';
import { Card, Divider, Input, Table } from 'antd';
import React from 'react';

const Repayment: React.FC<any> = (props) => {
  const { costParameterDTO } = props.data;
  // costConfigDTO
  const columns = [
    {
      title: '评分等级',
      dataIndex: 'auditResultDesc',
    },
    {
      title: '利息（年利率）',
      dataIndex: 'interestRate',
      editable: true,
      render: (_, record: any) => (
        <>
          <Input
            style={{ width: '90%' }}
            disabled
            // className={`${record.errorFiled ? styles.errorinput : ''} ${styles.tableinput}`}
            // onChange={(e) => changeValue1(e, record)}
            value={record.interestRate}
          />
          {/* <div style={{ display: record.errorFiled ? 'block' : 'none' }} className={styles.tabletip}>{record.fieldName}必填</div> */}
        </>
      ),
    },
    {
      title: '提前结清违约金（日利率）',
      dataIndex: 'settleEarly',
      editable: true,
      render: (_, record: any) => (
        <>
          <Input
            style={{ width: '90%' }}
            disabled
            // className={`${record.errorFiled ? styles.errorinput : ''} ${styles.tableinput}`}
            // onChange={(e) => changeValue2(e, record)}
            value={record.settleEarly}
          />
          {/* <div style={{ display: record.errorFiled ? 'block' : 'none' }} className={styles.tabletip}>{record.fieldName}必填</div> */}
        </>
      ),
    },
    {
      title: '逾期罚息（日利率）',
      dataIndex: 'overduePenaltyInterest',
      editable: true,
      render: (_, record: any) => (
        <>
          <Input
            style={{ width: '90%' }}
            disabled
            // className={`${record.errorFiled ? styles.errorinput : ''} ${styles.tableinput}`}
            // onChange={(e) => changeValue3(e, record)}
            value={record.overduePenaltyInterest}
          />
          {/* <div style={{ display: record.errorFiled ? 'block' : 'none' }} className={styles.tabletip}>{record.fieldName}必填</div> */}
        </>
      ),
    },
    {
      title: '保理服务费（日利率）',
      dataIndex: 'factoringServiceFee',
      editable: true,
      render: (_, record: any) => (
        <>
          <Input
            style={{ width: '90%' }}
            disabled
            // className={`${record.errorFiled ? styles.errorinput : ''} ${styles.tableinput}`}
            // onChange={(e) => changeValue4(e, record)}
            value={record.factoringServiceFee}
          />
          {/* <div style={{ display: record.errorFiled ? 'block' : 'none' }} className={styles.tabletip}>{record.fieldName}必填</div> */}
        </>
      ),
    },
  ];
  const columnsConfig = [
    {
      title: '费用项',
      dataIndex: 'expenseItem',
    },
    {
      title: '收取节点',
      dataIndex: 'receivingNode',
    },
    {
      title: '出资方',
      dataIndex: 'fundersEnum',
    },
    {
      title: '收取方',
      dataIndex: 'receiverEnum',
    },
    {
      title: '计收方式',
      dataIndex: 'collectionMethodEnum',
    },
    {
      title: '单利 / 复利',
      dataIndex: 'interestTypeEnum',
    },
    {
      title: '计算方式',
      dataIndex: 'calculationEnum',
    },
    {
      title: '基数',
      dataIndex: 'baseEnum',
    },
    {
      title: '比例数',
      dataIndex: 'proportionNumberEnum',
    },
    {
      title: '收取方式',
      dataIndex: 'chargeMethodEnum',
    },
    {
      title: '返还节点',
      dataIndex: 'returnNodeEnum',
    },
    {
      title: '抵扣项',
      dataIndex: 'deductionEnum',
    },
  ];
  const costConfigDTOList = [
    {
      expenseItem: '保理服务费',
      receivingNode: '债权转让放款时',
      fundersEnum: '啦啦（天津）汽车科技有限公司',
      receiverEnum: '广州易人行商业保理有限公司',
      collectionMethodEnum: '28天',
      interestTypeEnum: '单利',
      calculationEnum: '按比例',
      baseEnum: '应收账款',
      proportionNumberEnum: '按参数表',
      chargeMethodEnum: '线上',
      returnNodeEnum: '不返还',
      deductionEnum: '不抵扣',
    },
    {
      expenseItem: '利息',
      receivingNode: '还款时',
      fundersEnum: '进件用户',
      receiverEnum: '啦啦（天津）汽车科技有限公司',
      collectionMethodEnum: '按天',
      interestTypeEnum: '单利',
      calculationEnum: '按比例',
      baseEnum: '本金',
      proportionNumberEnum: '按参数表',
      chargeMethodEnum: '线上',
      returnNodeEnum: '不返还',
      deductionEnum: '不抵扣',
    },
    {
      expenseItem: '提前结清违约金',
      receivingNode: '提前结清时',
      fundersEnum: '进件用户',
      receiverEnum: '啦啦（天津）汽车科技有限公司',
      collectionMethodEnum: '按天',
      interestTypeEnum: '单利',
      calculationEnum: '按比例',
      baseEnum: '本金',
      proportionNumberEnum: '按参数表',
      chargeMethodEnum: '线上',
      returnNodeEnum: '不返还',
      deductionEnum: '不抵扣',
    },
    {
      expenseItem: '逾期罚息',
      receivingNode: '还款时',
      fundersEnum: '进件用户',
      receiverEnum: '啦啦（天津）汽车科技有限公司',
      collectionMethodEnum: '按天累计',
      interestTypeEnum: '复利',
      calculationEnum: '按比例',
      baseEnum: '本金',
      proportionNumberEnum: '按参数表',
      chargeMethodEnum: '线上',
      returnNodeEnum: '不返还',
      deductionEnum: '不抵扣',
    },
  ];
  return (
    <Card title="还款表配置" className={globalStyle.mt30}>
      <Divider type="vertical" style={{ borderLeft: '3px solid #1677ff', height: '1.5em' }} />
      <h3 style={{ display: 'inline-block' }}>费用参数表</h3>
      <Table
        rowKey="auditResultDesc"
        columns={columns}
        dataSource={costParameterDTO}
        pagination={false}
      />
      <Divider type="vertical" style={{ borderLeft: '3px solid #1677ff', height: '1.5em' }} />
      <h3 style={{ display: 'inline-block', marginTop: 25 }}>费用配置表</h3>
      <Table
        rowKey="expenseItem"
        columns={columnsConfig}
        dataSource={costConfigDTOList}
        scroll={{ x: 'max-content' }}
        pagination={false}
      />
    </Card>
  );
};

export default Repayment;
