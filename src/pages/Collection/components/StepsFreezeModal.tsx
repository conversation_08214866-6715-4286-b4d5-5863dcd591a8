/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-11-21 14:52:27
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-11-21 15:20:30
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/StepsFreezeModal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2022-02-15 13:46:21
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/CollectRelief/components/PassModel.ts
 */
import { Modal, Steps } from 'antd';
import React, { useEffect, useState } from 'react';
import { overdueSteps } from '../service';

export type StepsFreezeModalProps = {
  close: () => void;
  onOk?: () => Promise<void>;
  visible: boolean;
  overdueCaseNo: string;
};

const StepsFreezeModal: React.FC<StepsFreezeModalProps> = ({ visible, close, overdueCaseNo }) => {
  const { Step } = Steps;
  const [stepList, setStepList] = useState<
    { operator: string; status: number; statusName: string; time: string }[]
  >([]);

  //获取进度条接口
  useEffect(() => {
    overdueSteps(overdueCaseNo).then((res) => {
      setStepList(res?.data);
    });
  }, [overdueCaseNo]);

  return (
    <>
      <Modal
        destroyOnClose
        centered
        title="钱包提现状态"
        open={visible}
        // footer={submitter}
        onOk={async () => {
          close();
        }}
        onCancel={() => {
          close();
        }}
        footer={null}
      >
        {
          <>
            {stepList?.length ? (
              <Steps direction="vertical" size="small" current={stepList?.length} progressDot>
                {stepList?.map(({ operator, statusName, time }) => {
                  return (
                    <Step
                      title={statusName}
                      description={
                        <>
                          <div>
                            {time || '-'}
                            {operator || '-'}
                          </div>
                        </>
                      }
                    />
                  );
                })}
              </Steps>
            ) : (
              '暂无数据'
            )}
          </>
        }
      </Modal>
    </>
  );
};

export default StepsFreezeModal;
