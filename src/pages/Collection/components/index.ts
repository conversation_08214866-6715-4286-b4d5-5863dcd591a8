/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 14:49:59
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-13 14:28:10
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import AddCallPayModal from './AddCallPayModal';
import AddWithHoldModal from './AddWithHoldModal';
import AddWithHoldWalletModal from './AddWithHoldWalletModal';
import ApplyReliefModal from './ApplyReliefModal';
import BasicInfo from './BasicInfo';
import CallDetailModal from './CallDetailModal';
import CallPayList from './CallPayList';
import CallPayMoneyList from './CallPayMoneyList';
import CollectDetail from './CollectDetail';
import CollectInfo from './CollectInfo';
import CollectReliefList from './CollectReliefList';
import Contact from './Contact';
import DetailPager from './DetailPager';
import DispatchModal from './DispatchModal';
import NewAddReliefModal from './NewApplyReliefModal';
import UploadCom from './UploadCom';
import WithHold from './WithHold';

export {
  BasicInfo,
  AddWithHoldModal,
  AddWithHoldWalletModal,
  WithHold,
  Contact,
  CollectDetail,
  CollectInfo,
  CallPayList,
  CallDetailModal,
  AddCallPayModal,
  UploadCom,
  ApplyReliefModal,
  NewAddReliefModal,
  CallPayMoneyList,
  CollectReliefList,
  DetailPager,
  DispatchModal,
};
