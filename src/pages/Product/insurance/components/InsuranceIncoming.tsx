import { useModel } from '@umijs/max';
import { Checkbox, Col, Form, Input, Row } from 'antd';
import React, { useEffect } from 'react';

const LeaseIncoming: React.FC<any> = () => {
  const [form] = Form.useForm();
  const { detailData, setCostConfigDTO, costConfigDTO, defaultConfig } = useModel(
    'Product.insuranceProduct',
  );

  useEffect(() => {
    const { activateFormula, expenseItems } = detailData?.activateDTO || {};
    form.setFieldsValue({ expenseItems, activateFormula });

    // expenseItems 根据初始的 expenseItems 获取最终的配置
    // 再新增时 后端返回了所有的配置， 前端根据 保证金和首付是否勾选 添加和排除config
    if (expenseItems?.length) {
      const dto = getConfig(expenseItems);
      setCostConfigDTO(dto);
    }
  }, [detailData?.activateDTO, form]);

  function getConfig(values: string[]) {
    const defautCostConfigDTO = defaultConfig?.costConfigDTO;

    // 如果还有配置需要动态的增加减少，只需要 在 dynamicDtoConfigList 加 expenseItem
    // 动态变化的配置 也就是 BAIL COMMISSION通过勾选来决定 是否存在于最终的配置中
    const dynamicDtoConfigList = ['BAIL', 'COMMISSION'];
    // 相当于排除掉了首付
    const formValueDynamicDtoConfigList = values.filter((item) =>
      dynamicDtoConfigList.includes(item),
    );
    // 永远存在的配置
    const existDtoConfig = costConfigDTO.filter(
      (dto) => !dynamicDtoConfigList.includes(dto.expenseItem),
    );

    // 假如 已经配置了 优先使用上一次配置了的 ， 比如初始化的详情
    // 或者 配置更改， 在删除 增加， 使用上一次更改了的配置数据
    const dynamicConfig = formValueDynamicDtoConfigList.map((expenseItem) => {
      // 优先使用配置的了
      const detailConfig = costConfigDTO?.find((item) => item.expenseItem === expenseItem);
      // 默认的
      const defaultConfig = defautCostConfigDTO.find((item) => item.expenseItem === expenseItem);

      return detailConfig ? detailConfig! : defaultConfig!;
    });

    return [...existDtoConfig, ...dynamicConfig];
  }

  return (
    <Form form={form} name={'insuranceIncoming'} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
      <Row>
        <Col span={12}>
          <Form.Item
            name="activateFormula"
            label="进件金额公式"
            labelCol={{ span: 7 }}
            rules={[{ required: true }]}
          >
            <Input disabled />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="expenseItems" label="前置费用" rules={[{ required: true }]}>
            <Checkbox.Group
              onChange={(values) => {
                const dto = getConfig(values as string[]);
                setCostConfigDTO(dto);
              }}
            >
              <Checkbox value="LOW_DOWN_PAYMENT" style={{ lineHeight: '32px' }} disabled>
                首付
              </Checkbox>
              <Checkbox value="BAIL" style={{ lineHeight: '32px' }}>
                保证金
              </Checkbox>
              <Checkbox value="COMMISSION" style={{ lineHeight: '32px' }}>
                手续费
              </Checkbox>
            </Checkbox.Group>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default React.memo(LeaseIncoming);
