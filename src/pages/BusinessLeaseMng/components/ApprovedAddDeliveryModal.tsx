/*
 * @Author: your name
 * @Date: 2021-04-12 15:18:02
 * @LastEditTime: 2025-05-28 17:54:03
 * @LastEditors: alan771.tu <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/BindNewCar.tsx
 */
import { CommonDraggleUpload, CommonImageUpload } from '@/components/ReleaseCom';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Form, message } from 'antd';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { submitOtherDeliveryInfo } from '../service';

export type ApprovedAddDeliveryModalProps = {
  onOk: () => Promise<void>;
  modalRef: React.RefObject<any>;
  otherRemark?: string;
  formEdit?: Record<string, any>;
};

// 其他交车资料上传路径
enum DELIVER_OTHER_TYPE {
  H5 = 1,
  BIZ_ADMIN = 2,
}

const ApprovedAddDeliveryModal: React.FC<ApprovedAddDeliveryModalProps> = (props) => {
  const { orderNo }: any = history.location.query;
  const { formEdit, modalRef } = props;
  const [form] = Form.useForm();
  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  const [fileListEditMap, setFileListEidtMap] = useState({
    groupPhotoOfPeopleAndVehicles: [],
    frameNumberPhoto: [],
    otherDeliveryDataPhotoList: [],
  });

  const [open, setOpen] = useState<boolean>(false);

  // 更新文件列表
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  useImperativeHandle(modalRef, () => ({
    show: () => {
      form.resetFields();
      setOpen(true);
    },
    hide: () => {
      setOpen(false);
    },
  }));

  // 设置回显表单值
  useEffect(() => {
    const filterList =
      formEdit?.otherDeliveryDataPhotoList?.filter(
        (item: { type: DELIVER_OTHER_TYPE }) => item.type === DELIVER_OTHER_TYPE.BIZ_ADMIN,
      ) || [];
    const otherDeliveryDataPhotoList = filterList?.map(
      (item: { otherDeliveryDataPhoto: []; name: string }) => {
        return {
          url: item.otherDeliveryDataPhoto,
          name: item.name,
        };
      },
    );

    const dataMap = {
      groupPhotoOfPeopleAndVehicles: [
        {
          url: formEdit?.groupPhotoOfPeopleAndVehicles,
          name: '人车合影照片',
        },
      ],
      frameNumberPhoto: [
        {
          url: formEdit?.frameNumberPhoto,
          name: '车架号照片',
        },
      ],
      otherDeliveryDataPhotoList,
    };

    // 设置文件列表
    handleFileList(dataMap);
    setFileListEidtMap(dataMap as any);
    // 设置表单值
    form.setFieldsValue({
      otherDeliveryDataPhotoList,
      groupPhotoOfPeopleAndVehicles: formEdit?.groupPhotoOfPeopleAndVehicles,
      frameNumberPhoto: formEdit?.frameNumberPhoto,
      remark: formEdit?.remark,
    });
  }, [formEdit, form]);

  return (
    <>
      <ModalForm
        title="补充交车资料"
        width={800}
        layout="horizontal"
        open={open}
        onOpenChange={setOpen}
        form={form}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            form.resetFields();
            handleFileList({});
            setFileListEidtMap({
              groupPhotoOfPeopleAndVehicles: [],
              frameNumberPhoto: [],
              otherDeliveryDataPhotoList: [],
            });
          },
        }}
        onFinish={async (values) => {
          try {
            const mapUploadFile = allFileList.otherDeliveryDataPhotoList.map((item: any) => {
              return {
                otherDeliveryDataPhoto: item.response?.data?.netWorkPath || item?.url || '',
                type: 2, //1. 用户上传 2.后台上传
                name: item.name,
              };
            });
            // 组合所有类型图片
            const h5FileList =
              formEdit?.otherDeliveryDataPhotoList?.filter(
                (item: { type: DELIVER_OTHER_TYPE }) => item.type === DELIVER_OTHER_TYPE.H5,
              ) || [];
            const allFile = [...h5FileList, ...mapUploadFile];
            const data = {
              orderNo,
              remark: values.remark,
              otherDeliveryDataPhotoList: allFile,
              frameNumberPhoto:
                allFileList.frameNumberPhoto[0]?.response?.data?.netWorkPath ||
                allFileList.frameNumberPhoto[0]?.url ||
                '',
              groupPhotoOfPeopleAndVehicles:
                allFileList.groupPhotoOfPeopleAndVehicles[0]?.response?.data?.netWorkPath ||
                allFileList.groupPhotoOfPeopleAndVehicles[0]?.url ||
                '',
              vinOcrInfo: formEdit?.vinOcrInfo || null, // 再次提交 清空重置ocr信息
            };

            console.log('data', data);
            await submitOtherDeliveryInfo(data);
            message.success('添加成功');
            props.onOk();
            return true;
          } catch (error) {
            return false;
          }
        }}
      >
        <CommonImageUpload
          extraTrack={false}
          extra="请将车辆处于启动状态，打开双闪，主驾车门打开，司机站在车门后，照片要求能看清楚车牌号码及车辆正面。"
          label="人车合影照片"
          icon={<PlusOutlined />}
          labelCol={{ span: 4 }}
          name="groupPhotoOfPeopleAndVehicles"
          max={1}
          mapFileList={mapFileList}
          fileListEdit={fileListEditMap.groupPhotoOfPeopleAndVehicles as any}
          buttonProps={{ type: 'text' }}
          listType="picture-card"
          accept=".png,.jpg,.jpeg,.gif"
          rules={[{ required: true, message: '请上传人车合影照片' }]}
          size={10}
        />
        <CommonImageUpload
          extraTrack={false}
          extra="请于车辆前挡风玻璃的下方，拍摄清晰的车架号。"
          label="车架号照片"
          labelCol={{ span: 4 }}
          name="frameNumberPhoto"
          max={1}
          size={10}
          icon={<PlusOutlined />}
          buttonProps={{ type: 'text' }}
          listType="picture-card"
          mapFileList={mapFileList}
          fileListEdit={fileListEditMap.frameNumberPhoto as any}
          accept=".png,.jpg,.jpeg,.gif"
          rules={[{ required: true, message: '请上传车架号照片' }]}
        />
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="其他交车资料"
          labelCol={{ span: 4 }}
          name="otherDeliveryDataPhotoList"
          max={5}
          size={50}
          fileListEdit={fileListEditMap.otherDeliveryDataPhotoList as any}
          listType="text"
          multiple={true}
          mapFileList={mapFileList}
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          rules={[{ required: true, message: '其他交车资料' }]}
        />
        <ProFormText
          name="remark"
          labelCol={{ span: 4 }}
          width="md"
          fieldProps={{ maxLength: 20 }}
          label="其他"
          placeholder="请输入"
          // rules={[{ required: true }]}
        />
      </ModalForm>
    </>
  );
};

export default ApprovedAddDeliveryModal;
