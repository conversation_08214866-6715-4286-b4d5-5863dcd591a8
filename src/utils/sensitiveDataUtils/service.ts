import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { GetSeimDataParams, SeimDataParams } from './data';

// SEIM审计日志上报
export async function sendSeimData(data: SeimDataParams) {
  return request('/bizadmin/seim/log', {
    data,
    method: 'POST',
    headers: bizAdminHeader,
    skipGlobalErrorTip: true,
  }).catch(() => {});
}

// 获取解密信息
export async function getPlainInfo(data: GetSeimDataParams) {
  return request('/bizadmin/seim/getPlainInfo', {
    data,
    method: 'POST',
    headers: bizAdminHeader,
  });
}
