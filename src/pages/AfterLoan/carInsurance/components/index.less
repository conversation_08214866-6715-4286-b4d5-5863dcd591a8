.modal_tit {
  font-size: 24px;
}
.label_width {
  width: 70px;
  min-width: 70px;
}
.block_area {
  padding-top: 10px;
  padding-left: 20px;
  &.pt_0 {
    padding-top: 0px;
  }
  &.pl_0 {
    padding-left: 0px;
  }
}
.tit {
  margin-top: 40px;
  font-weight: bold;
  font-size: 18px;
  &.mr5 {
    margin-right: 5px;
  }
}
.line {
  line-height: 40px;
  &.mt10 {
    margin-top: 20px;
  }
  .submit_offline_label {
    display: inline-block;
    width: 110px;
    margin-right: 6px;
    font-size: 18px;
    text-align: right;
  }
  .submit_offline_amount {
    margin-right: 10px;
    margin-left: 10px;
    color: red;
    font-size: 18px;
  }
  &::after {
    display: block;
    height: 0.5px; /* 设置分割线的高度为0.5px */
    margin: 10px auto; /* 上下外边距，以及自动左右外边距来居中 */
    background: #eee; /* 分割线的颜色 */
    content: '';
  }
  &.no_border {
    margin-bottom: 10px;
  }
  &.no_border::after {
    content: none;
  }
}
.remission_sel {
  display: flex;
  align-items: baseline;
  .ant-form-item {
    margin-bottom: 0px;
  }
}

.settle_tit {
  font-weight: bold;
  font-size: 18px;
}
