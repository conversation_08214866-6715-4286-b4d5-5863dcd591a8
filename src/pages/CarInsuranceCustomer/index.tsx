/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2025-03-11 10:42:28
 * @LastEditors: alan771.tu
 * @LastEditTime: 2025-03-11 10:42:28
 * @FilePath: lala-finance-biz-web/src/pages/CarInsuranceCustomer/index.tsx
 * @Description: index.tsx
 */

import HeaderTab from '@/components/HeaderTab';
import { getSortOrder } from '@/utils/tools';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { history } from '@umijs/max';
import { Button, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import type { CarInsuranceCustomerFields, IgetListParams } from './data.d';
import { statusMap } from './enum';
import { getCarInsuranceCustomerList } from './services';

const orderSort = ['idNo', 'authStatus', 'channelName', 'createdAt', 'authDate'];

const CarInsuranceCustomer: React.FC = () => {
  const columns: ProColumns<CarInsuranceCustomerFields>[] = [
    {
      title: '用户ID',
      dataIndex: 'userId',
      search: false,
    },
    {
      title: '客户名称',
      dataIndex: 'name',
      search: false,
    },
    {
      title: '身份证号',
      dataIndex: 'idNo',
      order: getSortOrder('idNo', orderSort),
    },
    {
      title: '所属渠道',
      dataIndex: 'channelName',
      order: getSortOrder('channelName', orderSort),
      search: false,
      render: (_, record) => {
        if (record?.channelName) {
          const channelList = record?.channelName.split(',') ?? [];
          return (
            <Tooltip title={channelList.join(',')}>
              {channelList.length > 1
                ? `${channelList[0]}等${channelList.length}个渠道`
                : channelList[0]}
            </Tooltip>
          );
        }
        return '/';
      },
    },
    {
      title: '状态',
      dataIndex: 'authStatus',
      valueEnum: statusMap,
      order: getSortOrder('authStatus', orderSort),
      fieldProps: {
        mode: 'multiple',
        showArrow: true,
        options: Object.entries(statusMap).map(([key, value]) => ({
          label: value,
          value: key,
        })),
      },
      search: {
        transform: (value: any) => {
          return {
            authStatusList: value,
          };
        },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      order: getSortOrder('createdAt', orderSort),
      // 默认一年
      initialValue: [dayjs().subtract(1, 'year'), dayjs()],
      fieldProps: {
        allowClear: false,
      },
      search: {
        transform: (value: any) => {
          const startDate = value[0];
          const endDate = value[1];
          return {
            startCreatedAt: dayjs(startDate).format('YYYY-MM-DD 00:00:00'),
            endCreatedAt: dayjs(endDate).format('YYYY-MM-DD 23:59:59'),
          };
        },
      },
      render: (_, record) => {
        return record.createdAt ?? '/';
      },
    },
    {
      title: '认证时间',
      dataIndex: 'authDate',
      valueType: 'dateRange',
      order: getSortOrder('authDate', orderSort),
      search: {
        transform: (value: any) => {
          const startDate = value[0];
          const endDate = value[1];
          return {
            startAuthDate: dayjs(startDate).format('YYYY-MM-DD 00:00:00'),
            endAuthDate: dayjs(endDate).format('YYYY-MM-DD 23:59:59'),
          };
        },
      },
      render: (_, record) => {
        return record.authDate ?? '/';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      search: false,
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              console.log(record);
              history.push(`/userMng/personalMng/car-insurance-detail?id=${record.authRecordId}`);
            }}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable
          columns={columns}
          request={async (params: IgetListParams) => {
            return await getCarInsuranceCustomerList({
              ...params,
              productSecondCodeList: ['0303'],
            });
          }}
          rowKey="authRecordId"
          scroll={{ x: 'max-content' }}
          search={{
            defaultCollapsed: false,
          }}
        />
      </PageContainer>
    </>
  );
};

export default CarInsuranceCustomer;
