import logo from '@/assets/logo.svg';
import Footer from '@/components/Footer';
import VerificationCode from '@/components/VerificationCode';
import type { LoginParamsType } from '@/services/login';
import { login } from '@/services/login';
import { removeLoginStorage, setPid, setToken, toAuth } from '@/utils/auth';
import { asyncEncryptWithRSA, md5 } from '@/utils/encrypt';
import { getDeviceInfo, isExternalNetwork } from '@/utils/utils';
import { history, Link, useModel } from '@umijs/max';
import { Alert, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import LoginFrom from './components/Login';
import { fetchCaptchaSwitchStatus, newLoginAfterCaptcha } from './service';
import styles from './style.less';
// import { getCarInsuranceDetail, uploadImg } from '../../CarInsurance/services';
// import { queryOrderDetail } from '@/pages/BusinessLeaseMng/service';
// import { getCities } from '@/services/global';

// const { Tab, Username, Password, Googlecode, Mobile, Captcha, Submit } = LoginFrom;
const { Tab, Username, Password, Submit } = LoginFrom;

// const LoginMessage: React.FC<{
//   content: string;
// }> = ({ content }) => (
//   <Alert
//     style={{
//       marginBottom: 24,
//     }}
//     message={content}
//     type="error"
//     showIcon
//   />
// );

/**
 * 此方法会跳转到 redirect 参数所在的位置
 */
const goto = () => {
  const { query } = history.location;
  const { redirect } = query as { redirect: string };
  window.location.href = redirect || '/';
};
const Login: React.FC<{}> = () => {
  // const [userLoginState, setUserLoginState] = useState<API.LoginStateType>({});
  const [form] = Form.useForm();
  // 清空表单密码
  const clearPassword = () => {
    form?.setFieldsValue({
      password: '',
    });
  };
  // 获取验证码开关，开关为true时，登录前需要验证验证码，同时走新登录接口
  const [captchaSwitch, setCaptchaSwitch] = useState(false);
  const [captchaSwitchFetching, setCaptchaSwitchFetching] = useState(false);
  // 获取验证码开关状态
  const getCaptchaSwitchStatus = async () => {
    setCaptchaSwitchFetching(true);
    const res = (await fetchCaptchaSwitchStatus().catch(() => {})) || {};
    setCaptchaSwitchFetching(false);
    console.log('res', res);
    if (res?.ret === 0 && res?.data === true) {
      // true表示需要验证验证码、新登录接口
      setCaptchaSwitch(true);
    }
  };
  //
  const init = async () => {
    // 获取验证码开关状态
    await getCaptchaSwitchStatus()
      .catch(() => {})
      .finally(() => {
        setCaptchaSwitchFetching(false);
      });
  };
  useEffect(() => {
    init().catch(() => {});
  }, []);

  // 验证码弹窗
  const [visibleOfVerificationCode, setVisibleOfVerificationCode] = useState(false);
  const handleVisibleOfVerificationCode = (v: boolean) => {
    setVisibleOfVerificationCode(v);
  };
  // 验证码验证成功回调，触发登录
  const [formValues, setFormValues] = useState<any>({});

  const [submitting, setSubmitting] = useState(false);
  const { initialState, setInitialState } = useModel('@@initialState');
  const [type, setType] = useState<string>('account');
  const handleSubmit = async (values: LoginParamsType, verifyPass: boolean) => {
    // 先进行验证码校验
    console.log('verifyPass', verifyPass);
    if (verifyPass === false && captchaSwitch) {
      handleVisibleOfVerificationCode(true);
      setFormValues(values);
      return;
    }
    //
    setSubmitting(true);
    removeLoginStorage();
    try {
      // 登录
      // const msg = await fakeAccountLogin({ ...values, type });
      // let data;
      // if (window.location.search?.includes('type=standby')) {
      //   setIsStandbyLogin();
      //   const res = await fakeAccountLogin(values);
      //   data = res.data;
      // } else {
      //   const res = await login({
      //     passportNo: values?.accountName,
      //     password: values?.password,
      //   });
      //   data = res.data;
      //   setPid(data.pid);
      // }
      console.log('values', values, captchaSwitch);
      let res = {};
      if (captchaSwitch === false) {
        res = await login({
          passportNo: values?.accountName,
          password: values?.password,
        });
      } else {
        // 新登录接口, 需要对密码进行加密
        const pwdMd5: string = md5(values?.password);
        const passwordStr: string = (await asyncEncryptWithRSA(pwdMd5).catch(() => {})) || pwdMd5;
        // 登录
        console.log('passwordStr', passwordStr);
        //
        const deviceInfo = getDeviceInfo();
        //
        res = await newLoginAfterCaptcha({
          passportNo: values?.accountName,
          password: passwordStr,
          // 新接口传设备信息
          deviceInfo,
        });
      }
      const data = res.data;
      setPid(data.pid);
      //
      console.log('data', data);
      setToken(data.token);
      // console.log('hahahah');
      if (initialState) {
        message.success('登录成功！');
        const currentUser = await initialState?.fetchUserInfo();
        // const currentUser = data;
        setInitialState({
          ...initialState,
          currentUser,
        });
        goto();
        return;
      }
      // 如果失败去设置用户错误信息
      // setUserLoginState(msg);
    } catch (error) {
      message.error('登录失败，请重试！');
      if (error && error?.ret && (error?.ret === 3022 || error?.ret === 3033)) {
        // 密码错误
        clearPassword();
      }
    }
    setSubmitting(false);
  };
  //
  const onVerifySucceed = () => {
    setVisibleOfVerificationCode(false);
    handleSubmit(formValues, true).catch(() => {});
    // message.success('登录成功！');
  };

  // const { status, type: loginType } = userLoginState;

  return (
    <div className={styles.container}>
      {window.location.hostname.includes('lalafin.net') && (
        <Alert
          type="warning"
          showIcon
          message=""
          description={
            <span style={{ textAlign: 'center', fontWeight: 'bolder' }}>
              基于网络安全因素考虑，内部员工请通过新的内网
              <a href="https://biz-admin.lalafin.work">https://biz-admin.lalafin.work</a> 访问。
            </span>
          }
        />
      )}
      {/* <div className={styles.lang}>
        <SelectLang />
      </div> */}
      <div className={styles.content}>
        <div className={styles.top}>
          <div className={styles.header}>
            <Link to="/">
              <img alt="logo" className={styles.logo} src={logo} />
              <span className={styles.title}>小圆金科业务系统</span>
            </Link>
          </div>
          <div className={styles.desc} />
        </div>

        <div className={styles.main}>
          <LoginFrom
            activeKey={type}
            from={form}
            onTabChange={setType}
            onSubmit={(values) => handleSubmit(values, false)}
          >
            <Tab key="account" tab="账号密码登录">
              {/* {status === 'error' && loginType === 'account' && !submitting && (
                <LoginMessage content="账户或密码错误（admin/ant.design）" />
              )} */}

              <Username
                name="accountName"
                placeholder="账号"
                disabled={visibleOfVerificationCode}
                rules={[
                  {
                    required: true,
                    message: '请输入用户名!',
                  },
                ]}
              />
              <Password
                name="password"
                placeholder="密码"
                disabled={visibleOfVerificationCode}
                rules={[
                  {
                    required: true,
                    message: '请输入密码！',
                  },
                ]}
              />
              {/* <Googlecode
                name="ggcode"
                placeholder="谷歌验证码"
                rules={[
                  {
                    required: true,
                    message: '请输入谷歌验证码!',
                  },
                ]}
              /> */}
            </Tab>
            {/* <Tab key="mobile" tab="手机号登录">
              {status === 'error' && loginType === 'mobile' && !submitting && (
                <LoginMessage content="验证码错误" />
              )}
              <Mobile
                name="mobile"
                placeholder="手机号"
                rules={[
                  {
                    required: true,
                    message: '请输入手机号！',
                  },
                  {
                    pattern: /^1\d{10}$/,
                    message: '手机号格式错误！',
                  },
                ]}
              />
              <Captcha
                name="captcha"
                placeholder="验证码"
                countDown={120}
                getCaptchaButtonText=""
                getCaptchaSecondText="秒"
                rules={[
                  {
                    required: true,
                    message: '请输入验证码！',
                  },
                ]}
              />
            </Tab> */}
            <Submit loading={submitting}>登录</Submit>
            <VerificationCode
              visible={visibleOfVerificationCode}
              handleVisible={handleVisibleOfVerificationCode}
              onVerifySucceed={onVerifySucceed}
            />
          </LoginFrom>
        </div>
      </div>
      <Footer />
    </div>
  );
};
const SSOLogin: React.FC<{}> = () => {
  useEffect(() => {
    // toRemoveAuth();
    removeLoginStorage();
    toAuth();
  }, []);
  return <></>;
};
const LoginPage = isExternalNetwork() ? Login : SSOLogin;
export default LoginPage;
