/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-26 14:44:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-21 14:08:56
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/CollectDetail.tsx
 * @Description: 催收详情
 */
import { DividerTit, ShowInfo } from '@/components';
// import { getOssPath } from '@/services/global';
import {
  ADVANCED_STATUS,
  CLASSIFICATION_CODE_LABEL,
  FREEZON_STATUS,
  LICENSE_TYPES_MAP,
  PRODUCT_CLASSIFICATION_CODE,
  REPURCHASE_STATUS_MAP,
  SECONDARY_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_MAP_ALL,
} from '@/enums';
import globalStyle from '@/global.less';
import { desensitizationPhone, getBlob, isExternalNetwork, previewAS, saveAs } from '@/utils/utils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-components';
import { Link } from '@umijs/max';
import { Col, Popover, Row, Table, Tooltip } from 'antd';
import React from 'react';
import { reportLog } from '../services';
import type { I_collectDetail } from '../types';

const BILL_TYPE = {
  1: '自有账单',
  2: '共享账单',
};

const CollectDetail = (props: any) => {
  const { data, billType } = props;
  const collectDetailMap = {
    productFirstName: '产品一级分类',
    productSecondName: '产品二级分类',
    productName: '产品名称',
    productCode: '产品ID',
    repurchaseStatus: '逾期代偿回购',
    funderChannelName: '资金渠道',
    withdrawStatus: '钱包提现状态',
    carUniqueCode: '车架号',
    licenseCode: '车牌号',
    returnedAmountDue: '应还月供（元）',
    repayDay: '还款日',
    remainingPrincipal: '剩余本金（元）',
    overdueMaxLevel: '最大逾期等级',
    overdueLongestDays: '最长逾期天数',
    leaseChannelName: '融租渠道名称',
  };
  //保理没有的属性；
  const factorNoOrder = {
    orderNo: '订单号',
    orderAmount: '订单金额（元）',
  };
  const factorNoBank = {
    bankName: '银行卡开户行',
    bankCode: '银行卡号',
    bankPhoneNumber: '银行卡预留手机号',
    licenseType: '上牌类型',
  };
  // 属性映射
  const showInfoItemMap = {
    licenseType: LICENSE_TYPES_MAP,
  };
  // const restCollectDetailMap = {
  //   contract: '合同',
  // };
  //处理下保理没有的属性
  const collectDetailMapFinal =
    data?.productCode?.substring(0, 4) === '0303'
      ? collectDetailMap
      : data?.productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING
      ? { ...collectDetailMap }
      : { ...factorNoOrder, ...collectDetailMap, ...factorNoBank };
  //下载合同
  const download = (url: string, filename: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
    // });
  };
  const previewPDF = (url: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      previewAS(blob);
    });
    // });
  };
  const tableDom = () => {
    return (
      <Table
        style={{ display: 'table-row' }}
        pagination={false}
        dataSource={data?.contractList}
        columns={[
          { title: '合同名称', dataIndex: 'fileDesc' },
          {
            title: '操作',
            render: (_, record: { netWorkPath: string; fileDesc: string }) =>
              !isExternalNetwork() && (
                <>
                  <a
                    onClick={() => {
                      download(record.netWorkPath, `${record.fileDesc}.pdf`);
                    }}
                  >
                    下载
                  </a>
                  <a
                    className={globalStyle.ml10}
                    onClick={() => {
                      previewPDF(record.netWorkPath);
                    }}
                  >
                    预览
                  </a>
                </>
              ),
          },
        ]}
      />
    );
  };

  //根据产品code展示不同的订单详情，账期不展示银行卡相关详情
  const linkDetail =
    data?.productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE
      ? 'lease'
      : 'cash';
  //  const
  const selfDefine = {
    orderNo: (
      <Link to={`/businessMng/${linkDetail}-detail?orderNo=${data?.orderNo}`}>{data?.orderNo}</Link>
    ),
    repurchaseStatus: REPURCHASE_STATUS_MAP[data.repurchaseStatus],
    withdrawStatus: FREEZON_STATUS[data?.withdrawStatus],
    // contract: <>{tableDom()}</>,
    // productClassicCode: <>{CLASSIFICATION_CODE_LABEL[data?.productClassicCode]}</>,
    // productSecondaryCode: <>{SECONDARY_CLASSIFICATION_MAP_ALL[data?.productSecondaryCode]}</>,
  };
  const factorColumn = [
    {
      title: '账单',
      dataIndex: 'billMonth',
      key: 'billMonth',
    },
    {
      title: '账单编号',
      dataIndex: 'billNo',
      key: 'billNo',
    },
    {
      title: '逾期总额',
      dataIndex: 'overdueAll',
      key: 'overdueAll',
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      key: 'overdueDays',
    },
    {
      title: '逾期等级',
      dataIndex: 'overdueLevel',
      key: 'overdueLevel',
    },
    {
      title: '账单类型',
      dataIndex: 'billType',
      key: 'billType',
      render: (_: any, row: { billType: string | number }) => {
        return BILL_TYPE[row.billType];
      },
    },
  ];

  const finalData: any = [];

  // type=1总计 拎出来到父层, type = 2 period已还, type= 3 period初始  key的第一位总数组所在的index+1, key的第二位为type-1
  // costType 1, 逾期总额overdueAll 2,逾期本金overduePrinciple 3，逾期利息overdueInterest 4，逾期罚息overduePenalty 5，逾期滞纳金lateFee
  const mapTitle: Record<number, string> = {
    99: 'overdueAll', //逾期总额
    1: 'overduePrinciple', //逾期本金
    2: 'overdueInterest', //逾期利息
    3: 'overduePenalty', //逾期罚息o
    4: 'lateFee', //逾期滞纳金
  };
  // console.log(data?.repayOverdueCostBO);
  data?.repayOverdueCostBO?.map((item: any, index: any) => {
    let otherDynamicColumnItem: any = {};
    let otherDynamicColumnItem2: any = {
      key: `${index + 1}1`,
      periods: '已还',
      billMonth: '已还',
    };
    let otherDynamicColumnItem3: any = {
      key: `${index + 1}2`,
      periods: '初始',
      billMonth: '初始',
    };
    item?.costItemBOList?.forEach(
      (costItemBOListItem: { costType: number; amount: number; type: number }) => {
        // return costItemBOListItem?.type === 3总数；type= 2 已还 type= 1初始
        // otherDynamicColumn = {[``]}
        if (costItemBOListItem?.type === 3) {
          otherDynamicColumnItem = {
            ...otherDynamicColumnItem,
            [mapTitle[costItemBOListItem?.costType]]: costItemBOListItem?.amount,
          };
          //children的第一行
        } else if (costItemBOListItem?.type === 2) {
          //type= 2 已还 type= 3初始 变成长度为2的数组，塞到children里边。
          otherDynamicColumnItem2 = {
            ...otherDynamicColumnItem2,
            [mapTitle[costItemBOListItem?.costType]]: costItemBOListItem?.amount,
          };
          //children的第二行
        } else if (costItemBOListItem?.type === 1) {
          otherDynamicColumnItem3 = {
            ...otherDynamicColumnItem3,
            [mapTitle[costItemBOListItem?.costType]]: costItemBOListItem?.amount,
          };
        }
      },
    );

    // 组装children
    const childrenTemp = [];
    childrenTemp.push(otherDynamicColumnItem2, otherDynamicColumnItem3);
    const itemTemp = {
      key: index + 1,
      periods: item?.periods,
      billMonth: item?.billMonth,
      overdueDays: item?.overdueDays,
      overdueLevel: item?.overdueLevel,
      billType,
      billNo: item?.billNo || '-',
      advancedStatus: item?.advancedStatus,
      ...otherDynamicColumnItem,
      children: childrenTemp,
    };
    finalData.push(itemTemp);
    // console.log(finalData);
  });
  const factorNoColumn = [
    {
      title: '期数',
      dataIndex: 'periods',
      key: 'periods',
    },
    {
      title: '逾期总额',
      dataIndex: 'overdueAll',
      key: 'overdueAll',
    },
    {
      title: '逾期本金',
      dataIndex: 'overduePrinciple',
      key: 'overduePrinciple',
    },
    {
      title: '逾期利息',
      dataIndex: 'overdueInterest',
      key: 'overdueInterest',
    },
    {
      title: '逾期罚息',
      dataIndex: 'overduePenalty',
      key: 'overduePenalty',
    },
    {
      title: '逾期滞纳金',
      dataIndex: 'lateFee',
      key: 'lateFee',
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      key: 'overdueDays',
    },
    {
      title: '逾期等级',
      dataIndex: 'overdueLevel',
      key: 'overdueLevel',
    },
    {
      title: '易人行逾期垫付',
      dataIndex: 'advancedStatus',
      key: 'advancedStatus',
      render: (_, row) => {
        return ADVANCED_STATUS.get(row.advancedStatus) || '-';
      },
    },
  ];
  //保理逾期明细
  const overdueDetailFactorDom = () => (
    <Table dataSource={finalData} columns={factorColumn} pagination={false} />
  );
  //除了保理以外的明细
  const overdueDetailNoFactorDom = () => (
    <Table dataSource={finalData} columns={factorNoColumn} pagination={false} />
  );

  // 风险判断函数
  const riskLevelMate = (days: any) => {
    if (days > 3 && days <= 15) {
      return 'B-中低风险客户';
    }
    if (days > 15 && days <= 30) {
      return 'C-中风险客户';
    }
    if (days > 30 && days <= 60) {
      return 'D-中高风险客户';
    }
    if (days > 60) {
      return 'E-高风险客户';
    }
    return 'A-低风险客户';
  };

  // 判断最大逾期天数
  const maxTermMate = () => {
    let item: any = { overdueDays: 0 };
    if (data?.repayDetailList?.length > 0) {
      data?.repayDetailList.forEach((val: any) => {
        if (val.overdueDays > item.overdueDays) {
          item = val;
        }
      });
    }
    return item;
  };
  const maxTerm = maxTermMate();

  // 历史预期明细列表
  const repayDetailListDom = () => {
    // 车险分期不用展示
    if (data?.productCode?.substring(0, 4) === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE) {
      return <></>;
    }

    const isBaoli =
      data?.productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING; // 是否属于保理

    const historyColumn = [
      {
        title: isBaoli ? '账单' : '期数',
        dataIndex: isBaoli ? 'billDate' : 'term',
        key: isBaoli ? 'billDate' : 'term',
      },
      {
        title: isBaoli ? '账单编号' : '订单号',
        dataIndex: 'orderNo',
        key: 'orderNo',
      },
      {
        title: '历史逾期天数',
        dataIndex: 'overdueDays',
        key: 'overdueDays',
        render: (_: any, record: any) => (
          <span
            style={{
              color:
                (maxTerm.billDate && maxTerm.billDate === record.billDate) ||
                (maxTerm.term && maxTerm.term === record.term)
                  ? 'red'
                  : 'unset',
            }}
          >
            {record.overdueDays}
          </span>
        ),
      },
      {
        title: '逾期等级',
        dataIndex: 'overdueLevel',
        key: 'overdueLevel',
      },
      {
        title: '还款状态',
        dataIndex: 'repayStatus',
        key: 'repayStatus',
      },
    ];

    return <Table dataSource={data?.repayDetailList} columns={historyColumn} pagination={false} />;
  };

  function getInfoColumns() {
    const columns1: ProColumns<I_collectDetail>[] = [
      {
        dataIndex: 'productClassicCode',
        valueType: 'select',
        valueEnum: CLASSIFICATION_CODE_LABEL,
        title: '产品一级分类',
      },
      {
        dataIndex: 'productSecondaryCode',
        valueType: 'select',
        valueEnum: SECONDARY_CLASSIFICATION_MAP_ALL,
        title: '产品二级分类',
      },
      {
        dataIndex: 'productName',
        title: '产品名称',
      },
      {
        dataIndex: 'productCode',
        title: '产品ID',
      },
    ];

    const columns2: ProColumns<I_collectDetail>[] = [
      {
        dataIndex: 'orderNo',
        title: '订单号',
        render(_, record) {
          return (
            <Link to={`/businessMng/${linkDetail}-detail?orderNo=${record?.orderNo}`}>
              {record?.orderNo}
            </Link>
          );
        },
      },
      {
        dataIndex: 'orderAmount',
        title: '订单金额（元）',
      },
    ];
    const columns3: ProColumns<I_collectDetail>[] = [
      {
        dataIndex: 'bankName',
        title: '银行卡开户行',
      },
      {
        dataIndex: 'bankCode',
        title: '银行卡号',
      },
      {
        dataIndex: 'bankPhoneNumber',
        title: '银行卡预留手机号',
        render(_, record) {
          return (
            <>
              {desensitizationPhone(record?.bankPhoneNumber)}
              <Popover content={_} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (record?.bankPhoneNumber) {
                      await reportLog(record?.bankPhoneNumber);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          );
        },
      },
      {
        title: '上牌类型',
        dataIndex: 'licenseType',
        valueEnum: LICENSE_TYPES_MAP,
      },
    ];

    const columns4: ProColumns<I_collectDetail>[] = [
      {
        dataIndex: 'funderChannelName',
        title: '资金渠道',
      },
      {
        dataIndex: 'withdrawStatus',
        valueType: 'select',
        valueEnum: FREEZON_STATUS,
        title: '钱包提现状态',
      },
      {
        dataIndex: 'carUniqueCode',
        title: '车架号',
      },
      {
        dataIndex: 'licenseCode',
        title: '车牌号',
      },
      {
        dataIndex: 'returnedAmountDue',
        title: '应还月供（元）',
      },
      {
        dataIndex: 'repayDay',
        title: '还款日',
      },
      {
        dataIndex: 'remainingPrincipal',
        title: '剩余本金（元）',
      },
      {
        dataIndex: 'overdueMaxLevel',
        title: '最大逾期等级',
      },
      {
        dataIndex: 'overdueLongestDays',
        title: '最长逾期天数',
      },
      {
        dataIndex: 'leaseChannelName',
        title: '融租渠道名称',
      },
    ];

    const columns: ProColumns<I_collectDetail>[] =
      data?.productCode?.substring(0, 4) === '0303'
        ? [...columns1, ...columns4]
        : data?.productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING
        ? [...columns2, ...columns4]
        : [...columns2, ...columns1, ...columns3, ...columns4];
    return columns;
  }

  return (
    <div>
      {isExternalNetwork() ? (
        <ProDescriptions dataSource={data} columns={getInfoColumns()} />
      ) : (
        <ShowInfo
          data={data}
          noCard
          infoMap={collectDetailMapFinal}
          selfDefine={selfDefine}
          itemMap={showInfoItemMap}
        >
          <Row className={globalStyle.pl20}>
            <Col span={24}>
              <div style={{ display: 'flex' }}>
                <span style={{ marginRight: 20 }}>合同:</span>
                {tableDom()}
              </div>
            </Col>
          </Row>
        </ShowInfo>
      )}
      <DividerTit title="逾期明细">
        <div style={{ margin: 20 }}>
          {/* 保理和其他产品表格展示不一致 */}
          {data?.productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING
            ? overdueDetailFactorDom()
            : overdueDetailNoFactorDom()}
        </div>
      </DividerTit>
      <DividerTit title="历史逾期明细">
        <div style={{ margin: 20 }}>
          <div style={{ marginBottom: 10 }}>
            <span style={{ fontWeight: 500 }}>客户预期等级评分</span>
            <Tooltip
              placement="top"
              title={() => (
                <>
                  <p>- A 低风险客户-历史逾期天数在[1-3]天内未超过三天，或未逾期过客户</p>
                  <p>- B 中低风险客户-历史最长逾期天数在{`{3-15]`}天内</p>
                  <p>- C 中风险客户-历史最长逾期天数在{`{15-30]`}天内</p>
                  <p>- D 中高风险客户-历史逾期最长天数在{`{30-60]`}天内</p>
                  <p>- E 高风险客户-历史长逾期天数长达61天以上客户</p>
                </>
              )}
              overlayStyle={{ maxWidth: 1000 }}
            >
              <QuestionCircleOutlined style={{ margin: 10 }} />
            </Tooltip>
            <strong style={{ fontSize: 16 }}>{riskLevelMate(maxTerm.overdueDays)}</strong>
          </div>
          {/* 保理和其他产品表格展示不一致 */}
          {repayDetailListDom()}
        </div>
      </DividerTit>
    </div>
  );
};

export default React.memo(CollectDetail);
