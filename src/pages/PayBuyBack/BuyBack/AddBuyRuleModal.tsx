// 新增回购规则
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import {
  ModalForm,
  ProForm,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Col, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import { editBuyRule } from '../service';
import type { BuyRuleListItem } from '../types';

type FormValues = {
  configId?: string;
  capital: string;
  productCode: string;
  productType: string;
  rule: string;
  ruleDay: number;
  capitalRuleCode: string;
  capitalRuleDay: number;
};
type AddPayRuleModalProps = {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  data?: BuyRuleListItem;
};

const AddPayRuleModal: React.FC<AddPayRuleModalProps> = (props) => {
  const [loading, setLoading] = useState(false);
  const [form] = ProForm.useForm();

  useEffect(() => {
    if (props.data) {
      form.setFieldsValue({ ...props.data });
    }
  }, [props.data]);

  const handleFinish = async (values: FormValues) => {
    const { productCode } = values;
    const productType = productCode.substring(0, 2);
    const params = {
      ...values,
      productType,
    };
    setLoading(true);
    try {
      await editBuyRule(params);
      setLoading(false);
      props.onSuccess();
      return true;
    } catch {
      setLoading(false);
    }
  };

  return (
    <ModalForm<FormValues>
      title="回购规则"
      open={props.open}
      form={form}
      layout="horizontal"
      modalProps={{
        destroyOnClose: true,
        onCancel: props.onCancel,
        confirmLoading: loading,
      }}
      labelCol={{ span: 8 }}
      onFinish={handleFinish}
    >
      <ProFormText name="configId" hidden />
      <Row>
        <Col span={16}>
          <ProFormSelect
            required
            width="md"
            options={[
              {
                value: 'SHANG_HAI_YING_HANG',
                label: '上海银行',
              },
            ]}
            name="capital"
            label="放款资方"
            rules={[{ required: true, message: '请选择放款资方' }]}
          />
          <ProFormSelect
            required
            width="md"
            options={[
              {
                value: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
                label: '融资租赁',
              },
            ]}
            name="productCode"
            label="合作业务线"
            rules={[{ required: true, message: '请选择合作业务线' }]}
          />
        </Col>
      </Row>

      <Row>
        <Col span={16}>
          <ProFormSelect
            required
            width="md"
            labelCol={{ span: 8 }}
            options={[
              {
                value: 'continuous_overdue',
                label: '连续逾期2期及以上，第2期逾期≥',
              },
            ]}
            name="ruleCode"
            label="内部提前回购规则"
            rules={[{ required: true, message: '请选择内部提前回购规则' }]}
          />
        </Col>
        <Col>
          <ProFormDigit
            width="xs"
            label="天数"
            labelCol={{ span: 6 }}
            name="ruleDay"
            fieldProps={{ precision: 0 }}
            min={1}
            rules={[{ required: true, message: '请输入天数' }]}
          />
        </Col>
      </Row>
      <Row>
        <Col span={16}>
          <ProFormSelect
            required
            width="md"
            labelCol={{ span: 8 }}
            options={[
              {
                value: 'capital_single_overdue',
                label: '单期连续逾期≥',
              },
            ]}
            name="capitalRuleCode"
            label="资方回购规则"
            rules={[{ required: true, message: '请选择资方回购规则' }]}
          />
        </Col>
        <Col>
          <ProFormDigit
            width="xs"
            label="天数"
            labelCol={{ span: 6 }}
            name="capitalRuleDay"
            fieldProps={{ precision: 0 }}
            min={1}
            rules={[{ required: true, message: '请输入天数' }]}
          />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default AddPayRuleModal;
