import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import React, { memo } from 'react';
import type { IoperateRecordItem } from '../type';

const columns: ProColumns<IoperateRecordItem>[] = [
  { dataIndex: 'statusDesc', title: '操作' },
  { dataIndex: 'approvalOperator', title: '操作账号' },
  { dataIndex: 'auditTime', title: '操作时间' },
];
const OperationRecords = () => {
  const {
    detailData: { operateLogs = [] },
  } = useModel('CarInsurance.carInsurance');
  return (
    <div>
      <h3>操作记录</h3>
      <ProTable
        columns={columns}
        dataSource={operateLogs}
        size="small"
        search={false}
        toolBarRender={false}
      />
    </div>
  );
};
export default memo(OperationRecords);
