/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 14:18:57
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-29 10:27:34
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/ProductBaseInfo.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DividerTit, ShowInfo } from '@/components';
import { history, Link, useRequest } from '@umijs/max';
import { Table } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { getRiskRecordList } from '../service';

const ProductBaseInfo: React.FC<any> = ({ data }) => {
  const userNo = history?.location?.query?.userNo as string;
  // console.log(subAccountNo, history?.location?.query);
  // const { data } = useRequest(() => {
  //   return getProductBaseInfo(subAccountNo as string);
  // });

  //产品信息
  const productInfo = {
    productFirstTypeName: '产品一级分类',
    productSecondTypeName: '产品二级分类',
    accountNo: '账户ID',
    riskLevel: '评级',
    creditAmount: '授信额度（元）',
    billRepayDate: '还款日（账期天数）',
    factoringServiceFee: '保理服务日费率（%）',
    status: '状态',
    masterEpName: '关联母企业',
    userTag: '客户标签',
  };

  const statusMap = {
    '0': '用户创建成功',
    '10': '待风控',
    '11': '风控中',
    '12': '企业三要素认证成功',
    '14': '授权人三要素认证成功',
    '16': '银行卡信息校验成功',
    '30': '待初审',
    '31': '待终审',
    '40': '审核通过',
    '41': '审核拒绝',
    '42': '撤销',
    '-2': '注销',
  };
  //日费率在应收和共享不展示，只有子企业展示该字段
  const productInfoFainal = useMemo(() => {
    const temp = { ...productInfo };
    if (!data?.masterEpName) delete temp.masterEpName;
    if (!data?.factoringServiceFee) delete temp.factoringServiceFee;
    return temp;
  }, [data]);

  const productInfoMap = {
    status: {
      0: '冻结',
      1: '激活',
      2: '注销',
    },
  };

  const productSelfDefine = {
    masterEpName: (
      <>
        {data?.masterEpName}/{data?.masterEpId}
      </>
    ),
  };
  // 联系人信息模块
  const contactInfo = {
    authorizedName: '授权人姓名',
    idType: '授权人证件类型',
    authorizedIdCard: '授权人证件号码',
    authorizedPhone: '授权人手机号',
    email: '授权人邮箱',
    // otherEmails: '其他联系邮箱',
  };

  const contactInfoMap = {
    idType: {
      1: '身份证',
    },
  };
  const contactInfoSelfDefine = {
    otherEmails: '其他联系邮箱',
  };

  const columns = [
    {
      title: '进件流水号',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      key: 'classification',
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondClassification',
      key: 'secondClassification',
    },
    {
      title: '申请额度',
      dataIndex: 'applyAmount',
      key: 'applyAmount',
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      key: 'applyTime',
    },
    {
      title: '进件状态',
      dataIndex: 'status',
      key: 'status',
      render: (_: any, record: any) => {
        return <div>{statusMap[record?.status]}</div>;
      },
    },
    {
      title: '授信额度',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
    },
    {
      title: '操作',
      dataIndex: '',
      key: '',
      type: 'option',
      render: (_: any, record: any) => (
        <Link to={`/userMng/enterpriseMng/detail?orderNo=${record.orderNo}`}>查看详情</Link>
      ),
    },
  ];
  // const getTableData = ({ current, pageSize }): Promise<Result> => {
  //   // let query = `page=${current}&size=${pageSize}`;

  //   return getRiskRecordList();
  // };
  const { data: incomeData, run } = useRequest(
    () => {
      return getRiskRecordList(userNo as string, data?.productSecondTypeCode);
    },
    {
      manual: true,
    },
  );
  // console.log(data?.secondClassification);
  useEffect(() => {
    if (data?.productSecondTypeCode) run();
  }, [data?.productSecondTypeCode]);
  return (
    <div>
      <DividerTit title="产品信息" />
      <ShowInfo
        infoMap={productInfoFainal}
        data={data}
        itemMap={productInfoMap}
        noCard
        selfDefine={productSelfDefine}
      />
      <DividerTit title="联系人信息" />
      <ShowInfo
        infoMap={contactInfo}
        data={data?.enterpriseUserInfoBO}
        itemMap={contactInfoMap}
        noCard
      />
      <ShowInfo
        data={data?.enterpriseUserInfoBO}
        infoMap={contactInfoSelfDefine}
        noCard
        rowSpan={24}
      />
      <DividerTit title="进件记录" />
      <Table columns={columns} rowKey="email" dataSource={incomeData} style={{ marginTop: 20 }} />
    </div>
  );
};

export default ProductBaseInfo;
