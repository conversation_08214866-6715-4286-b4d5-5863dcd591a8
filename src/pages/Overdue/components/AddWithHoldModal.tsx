/*
 * @Author: your name
 * @Date: 2021-06-30 15:33:45
 * @LastEditTime: 2022-06-14 11:44:45
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/AddWithHold.tsx
 */
/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2021-04-29 09:57:01
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/PassModel.ts
 */
import globalStyle from '@/global.less';
import {
  ModalForm,
  // ProFormUploadButton,
  ProFormDigit,
} from '@ant-design/pro-form';
import { history, useRequest } from '@umijs/max';
import { Col, message, Row } from 'antd';
import React from 'react';
import type { WithHoldParams } from '../data';
import { postWithHold, queryWithHoldInfo } from '../service';

export type AddReliefModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  // values: CallPayForm|{};
  onVisibleChange: any;
  overDueAmount: number;
};

const AddWithHoldModal: React.FC<AddReliefModalProps> = (props) => {
  const { overdueId } = history.location.query;
  const { data } = useRequest(() => {
    return queryWithHoldInfo(overdueId);
  });
  return (
    <>
      <ModalForm
        title="发起代扣"
        width={600}
        layout="horizontal"
        visible={props.modalVisible}
        onVisibleChange={props.onVisibleChange}
        initialValues={{ withholdMoney: props.overDueAmount }}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
        }}
        onFinish={async (value) => {
          try {
            await postWithHold({ overdueId, ...value } as WithHoldParams);
            message.success('添加成功');
            props.onOk();
            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            <span>姓名:</span>
          </Col>
          <span className={globalStyle.ml10}>{data?.userName}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            银行卡号:
          </Col>
          <span className={globalStyle.ml10}>{data?.bankNo}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            银行预留手机号:
          </Col>
          <span className={globalStyle.ml10}>{data?.phone}</span>
        </Row>
        <ProFormDigit
          name="withholdMoney"
          width="md"
          labelCol={{ span: 5 }}
          label="代扣金额(元)"
          placeholder="请输入代扣金额"
          fieldProps={{ min: 0, precision: 2 }}
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 有坑，同时出现很多个error,待优化
                if (/^\d+(\.\d+)?$/.test(val) && val && val > props.overDueAmount) {
                  // callBack();
                  return Promise.reject(new Error('代扣金额不能大于当前剩余应还总额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  // callBack('请输入数字');
                  return Promise.reject(new Error('请输入数字'));
                }
                return Promise.resolve();
              },
            },
          ]}
        />
      </ModalForm>
    </>
  );
};

export default AddWithHoldModal;
