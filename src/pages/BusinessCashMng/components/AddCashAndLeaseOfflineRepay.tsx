/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2023-02-06 10:08:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-10-21 15:56:28
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/components/AddCashAndLeaseOfflineReapy.tsx
 * @Description: AddCashAndLeaseOfflineReapy
 */
import { DividerTit, ShowInfo } from '@/components';
import { CommonImageUpload } from '@/components/ReleaseCom';
import { CHANNEL_BANK_NO, PRODUCT_CLASSIFICATION_CODE, RECIEVE_BANK_CHANNEL } from '@/enums';
import globalStyle from '@/global.less';
import { getRepayInfo } from '@/services/global';
import { convertUploadFileList, disableFutureDate } from '@/utils/utils';
import { DeleteOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import ProForm, {
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import { Button, Form, message, Table, Tooltip } from 'antd';
import BigNumber from 'bignumber.js';
import type { ColumnType } from 'rc-table/lib/interface';
import React, { useEffect, useRef, useState } from 'react';
import { COST_TYPE, mapStatusZh } from '../const';
import type { RepayEditItem, RepayItem, ShouldRepayItem } from '../data';
import itemStyle from '../index.less';
import { getDetailRemission, getRepayingMon, submitOfflineRepay } from '../service';

type AddCashAndLeaseOfflineRepayProps = {
  visible: boolean;
  close: () => void;
  productCode: string;
  orderNo: string;
  accountName: string;
  accountNumber: string;
};

const totalQuotaItemMap = {
  shouldAmountDueAll: '应还款总金额/元',
  shouldPrincipalAll: '应还款总本金/元',
  shouldInterestAll: '应还总利息/元',
  overdueInterestAll: '应还总罚息/元',
  otherCostAll: '应还总其他费用/元',
};

//实际还款方
enum ACTUAL_REPAY_ROLE {
  PERSON = '1', //个人
  THIRD_PARTY = '2', //第三方
}
//代偿方式
enum COMPENSATE_TYPE {
  SINGLE = '1', //单期
  MULTIPLE = '2', //多期
}

const MapPeriod = {};
new Array(42).fill('').map((item, index) => {
  MapPeriod[index + 1] = index + 1;
});
// //需要减免的费用项  { title: '应还利息', dataIndex: 'shouldInterest', key: 'shouldInterest' },
// const NEED_REMISSION_ITEM = {
//   shouldInterest: '利息',
//   overdueInterest: '逾期罚息',
//   otherCost: '其他费用',
// };
const AddCashAndLeaseOfflineRepay: React.FC<AddCashAndLeaseOfflineRepayProps> = ({
  visible,
  productCode,
  close,
  orderNo,
  accountName,
  accountNumber,
}) => {
  // console.log(accountName, accountNumber, productCode);
  const [form] = Form.useForm();
  const { data } = useRequest(() => {
    return getRepayInfo(orderNo);
  });

  // console.log(data);

  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const actionRef = useRef<ActionType>();
  const [dataSource, setDataSource] = useState<RepayEditItem[]>(() => []);
  const [someMount, setRepayingMount] = useState<{
    repayingMount: string | number | BigNumber;
    orderAmount?: string | number | BigNumber;
    shouldRepayRestAmount?: string | number | BigNumber;
  }>({ repayingMount: 0, orderAmount: 0, shouldRepayRestAmount: 0 });
  const [selectRows, setSelectRows] = useState<RepayItem[]>([]);

  //获取还款中额度

  useEffect(() => {
    getRepayingMon(orderNo).then((res) => {
      const { freezingAmount, orderAmount } = res?.data;
      setRepayingMount({
        repayingMount: freezingAmount || 0,
        orderAmount,
        shouldRepayRestAmount: new BigNumber(orderAmount || 0)
          .minus(freezingAmount || 0)
          .toFixed(2),
      });
    });
  }, []);

  // );

  const [totalQuotaItem, setTotalQuotaItem] = useState<{
    shouldAmountDueAll: number;
    shouldPrincipalAll: number;
    shouldInterestAll: number;
    overdueInterestAll: number;
    otherCostAll: number;
  }>({
    shouldAmountDueAll: 0,
    shouldPrincipalAll: 0,
    shouldInterestAll: 0,
    overdueInterestAll: 0,
    otherCostAll: 0,
  });

  const selfDefineTotal = {
    shouldAmountDueAll: <span style={{ color: 'red' }}>{totalQuotaItem.shouldAmountDueAll} </span>,
  };
  const [totalRemissionItem, setTotalRemissionItem] = useState<{
    totalRemissionAmount: number | BigNumber | string;
    totalAfterDeduct: number | BigNumber | string;
    principalAfterDeduct: number | BigNumber | string;
    interestAfterDeduct: number | BigNumber | string;
    penaltyAfterDeduct: number | BigNumber | string;
    otherCostAfterDeduct: number | BigNumber | string;
  }>({
    totalRemissionAmount: 0,
    totalAfterDeduct: 0,
    principalAfterDeduct: 0,
    interestAfterDeduct: 0,
    penaltyAfterDeduct: 0,
    otherCostAfterDeduct: 0,
  });
  const [selectRepayKeys, setSelectRepayKeys] = useState<React.Key[]>([]);

  const [hasSomeIsNotRepay, setHasSomeIsNotRepay] = useState<boolean>(false);
  useEffect(() => {
    setHasSomeIsNotRepay(
      data?.data.some((item: ShouldRepayItem) => {
        return mapStatusZh[item.status] !== '待还款';
      }),
    );
  }, data);

  //算应还总额
  const getTotal: any = (selectedRows: ShouldRepayItem[]) => {
    let shouldAmountDueAll: number | BigNumber | string = 0;
    let shouldPrincipalAll: number | BigNumber | string = 0;
    let shouldInterestAll: number | BigNumber | string = 0;
    let overdueInterestAll: number | BigNumber | string = 0;
    let otherCostAll: number | BigNumber | string = 0;
    // console.log(selectedRows);
    selectedRows?.map((item) => {
      shouldAmountDueAll = new BigNumber(item?.remainingAmountDue).plus(shouldAmountDueAll) + ''; //剩余应还总额
      shouldPrincipalAll = new BigNumber(item?.shouldPrincipal).plus(shouldPrincipalAll) + '';
      shouldInterestAll = new BigNumber(item?.shouldInterest).plus(shouldInterestAll) + '';
      overdueInterestAll = new BigNumber(item?.overdueInterest).plus(overdueInterestAll) + '';
      otherCostAll = new BigNumber(item?.otherCost).plus(otherCostAll) + '';
    });
    // console.log(selectedRows, shouldAmountDueAll, shouldPrincipalAll);
    setTotalQuotaItem({
      shouldAmountDueAll,
      shouldPrincipalAll,
      shouldInterestAll,
      overdueInterestAll,
      otherCostAll,
    });
    return {
      shouldAmountDueAll,
      shouldPrincipalAll,
      shouldInterestAll,
      overdueInterestAll,
      otherCostAll,
    };
  };

  const selectWithDeduct = (selectedRows: RepayItem[]) => {
    const { shouldPrincipalAll, shouldInterestAll, overdueInterestAll, otherCostAll } = getTotal(
      selectedRows,
    );
    const selectPeriods = selectedRows.map((item) => {
      return item?.termDetail?.split('/')[0];
    });
    //减免费用项
    let defaultData = [];
    getDetailRemission(orderNo).then((res) => {
      let interestRemission: string | number = 0,
        overdueInterestRemission: string | number = 0,
        overdueDelayRemission: string | number = 0,
        earlySettleRemission: string | number = 0;
      defaultData =
        res?.data?.map((item: { costType: string; remissionAmount: string }) => {
          switch (
            item?.costType //几种减免类型
          ) {
            case '1':
              interestRemission = item?.remissionAmount;
            case '3':
              overdueInterestRemission = item?.remissionAmount;
            case '5':
              overdueDelayRemission = item?.remissionAmount; //逾期滞纳金
            case '2':
              earlySettleRemission = item?.remissionAmount;
          }

          return {
            ...item,
            id: (Math.random() * 1000000).toFixed(0),
            remissionPeriod: selectPeriods,
          };
        }) || [];

      setDataSource(defaultData);
      // setEditableRowKeys(defaultData.map((item) => item.id));

      // console.log(
      //   new BigNumber(shouldPrincipalAll)
      //     .minus(repayingMount)
      //     .minus(interestRemission)
      //     .minus(overdueInterestRemission)
      //     .minus(overdueDelayRemission)
      //     .minus(earlySettleRemission),
      // );
      const totalRemissionAmount =
        new BigNumber(interestRemission)
          .plus(overdueInterestRemission)
          .plus(overdueDelayRemission)
          .plus(earlySettleRemission) + '';
      let totalAfterDeduct: number | string | BigNumber =
        new BigNumber(shouldPrincipalAll)
          .minus(someMount?.repayingMount)
          .minus(totalRemissionAmount) + '';
      totalAfterDeduct = Number(totalAfterDeduct) < 0 ? 0.0 : Number(totalAfterDeduct);
      //当前只是第三方且为代偿结清，全部减免
      setTotalRemissionItem({
        totalRemissionAmount,
        totalAfterDeduct,
        principalAfterDeduct: shouldPrincipalAll,
        interestAfterDeduct: new BigNumber(shouldInterestAll).minus(interestRemission) + '', //减免后利息
        penaltyAfterDeduct: new BigNumber(overdueInterestAll).minus(overdueInterestRemission) + '', //减免后罚息
        otherCostAfterDeduct:
          new BigNumber(otherCostAll).minus(earlySettleRemission).minus(overdueDelayRemission) + '', //减免后的其他费用
      });
    });
  };

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: RepayItem[]) => {
      setSelectRepayKeys(selectedRowKeys);
      setSelectRows(selectedRows);
      //拿到应还总利息/元 应还总罚息/元  应还总其他费用/元
      selectWithDeduct(selectedRows);
      // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    selectedRowKeys: selectRepayKeys,
    getCheckboxProps: (record: RepayItem) => {
      // let itemDisabled = false;

      const compensateType = form.getFieldValue('compensateType');
      // const actualRepayRole = form.getFieldValue('actualRepayRole');
      const itemDisabled =
        compensateType === COMPENSATE_TYPE.MULTIPLE || mapStatusZh[record.status] !== '待还款';
      // if (
      //   compensateType === COMPENSATE_TYPE.MULTIPLE &&
      //   actualRepayRole === ACTUAL_REPAY_ROLE.THIRD_PARTY
      // ) {
      //   itemDisabled = true;
      // } else {
      //   // 单期代偿和用户本人都可以选逾期和待还款
      //   if (mapStatusZh[record.status] === '待还款' || mapStatusZh[record.status] === '逾期') {
      //     itemDisabled = false;
      //   } else {
      //     itemDisabled = true;
      //   }
      // }

      return {
        disabled: itemDisabled, // 不为待还款的不允许选中 ,实际还款人=第三方代偿
      };
    },
  };

  const columns: ProColumns<RepayItem>[] = [
    {
      title: '期数',
      dataIndex: 'termDetail',
    },
    {
      title: '还款状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, row) => {
        return mapStatusZh[row?.status];
      },
    },
    { title: '应还日期', dataIndex: 'repayTime', key: 'repayTime' },
    { title: '应还总额', dataIndex: 'shouldAmountDue', key: 'shouldAmountDue' },
    { title: '应还本金', dataIndex: 'shouldPrincipal', key: 'shouldPrincipal' },
    { title: '应还利息', dataIndex: 'shouldInterest', key: 'shouldInterest' },
    { title: '逾期罚息', dataIndex: 'overdueInterest', key: 'overdueInterest' },
    {
      title: '其他费用',
      dataIndex: 'otherCost',
      key: 'otherCost',
    },
    {
      title: '剩余应还总额',
      dataIndex: 'remainingAmountDue',
      key: 'remainingAmountDue',
    },
  ];
  // //动态部分
  // const columnsDynamic = Object.keys(NEED_REMISSION_ITEM).map((item) => {
  //   return { title: NEED_REMISSION_ITEM[item], dataIndex: item, key: item };
  // });
  // const columns = [...columnsFix, columnsDynamic];
  const editColumns: ProColumns<RepayEditItem>[] = [
    {
      title: '规则序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 80,
    },
    {
      title: '*减免费项',
      key: 'costType',
      dataIndex: 'costType',
      valueType: 'select',
      valueEnum: COST_TYPE,
      fieldProps: {
        disable: true,
      },
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
    },
    {
      title: '*减免期数',
      key: 'remissionPeriod',
      dataIndex: 'remissionPeriod',
      valueType: 'select',
      fieldProps: {
        // options: { 1: '1', 2: '2', 3: '3', 4: '4' },
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        // optionFilterProp: 'label',
        // filterOption: (input: string, option: { label: string }) =>
        //   option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      valueEnum: MapPeriod,
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      render: (_, row) => {
        return row.remissionPeriod
          ? `${row.remissionPeriod[0]}~${row.remissionPeriod[row.remissionPeriod.length - 1]}`
          : '-';
      },
    },
    {
      title: '*减免金额',
      dataIndex: 'remissionAmount',
      fieldProps: {
        disable: true,
      },
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '此项是必填项',
          },
          {
            message: '必须包含数字',
            pattern: /\d+(\.\d{0,2})?/,
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, row) => {
        return (
          <>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              disabled
              onClick={() => {
                actionRef.current?.addEditRecord?.({
                  id: (Math.random() * 1000000).toFixed(0),
                  // title: '新的一行',
                });
              }}
            />
            <Button
              disabled
              className={itemStyle.buttonStyle}
              type="primary"
              icon={<DeleteOutlined />}
              onClick={() => {
                setDataSource(dataSource.filter((item) => item?.id !== row?.id));
              }}
            />
          </>
        );
      },
    },
  ];

  const checkArrIsSeque = (arr: number[]) => {
    // console.log(arr, 'arr');
    const arrNew = arr.sort((a, b) => {
      return a - b;
    });
    const first = arrNew[0];
    if (arr.length === 1) return true;
    // console.log(arr.sort(), 'arr.sort');
    //不是有序的
    const isNotSeque = arrNew.some((item, index) => {
      if (index !== 0) {
        return item !== first + index;
      }
    });
    return !isNotSeque;
  };
  return (
    <>
      <ModalForm
        title="线下还款"
        layout="horizontal"
        className={itemStyle.offLineDefine}
        visible={visible}
        modalProps={{
          centered: true,
          onCancel: close,
          width: 1000,
          okText: '提交',
          // destroyOnClose: true,
        }}
        form={form}
        onFinish={async (value) => {
          const { compensateType, isFullAmount, actualAmount, isRemission } = value;
          //accountName accountNumber  orderNo productCode repayPlayNos
          const repayPlayNos = selectRows?.map((item) => item.repayPlanNo);
          const mapUploadFile = convertUploadFileList(allFileList, ['attach']);
          const actualAmountTemp = isFullAmount ? totalQuotaItem?.shouldAmountDueAll : actualAmount;
          const isRemissionTemp = isFullAmount ? isRemission === false : isRemission;
          // if (Number(someMount?.shouldRepayRestAmount) === 0) {
          //   message.error('当前无可应还金额，无法提交!');
          //   return;
          // }
          //检查选中的是否跳期还款
          let selectPeriods = selectRows.map((item) => {
            return Number(item?.termDetail?.split('/')[0]);
          });
          selectPeriods = selectPeriods?.sort((a: number, b: number) => Number(a) - Number(b));
          //当前的还款表格能还期数的数组 逾期的不允许在线下还款操作，如果选了
          const canReapyPeriods = data?.data
            .filter((item: RepayItem) => {
              return mapStatusZh[item.status] === '待还款' || mapStatusZh[item.status] === '逾期';
            })
            .map((itemFilter: RepayItem) => {
              // console.log(itemFilter?.termDetail?.split('/')[0]);
              return Number(itemFilter?.termDetail?.split('/')[0]);
            });
          // console.log(checkArrIsSeque(selectPeriods), selectPeriods, canReapyPeriods);
          if (canReapyPeriods[0] !== selectPeriods[0]) {
            message.error('不允许跳期还款，必须按未还期数依次还款。');
            return;
          }
          if (!checkArrIsSeque(selectPeriods)) {
            message.error('不允许跳期还款，必须按未还期数依次还款。');
            return;
          }
          //代偿结清的时候，减免后应收还款金额 需要等于实际还款金额
          if (
            compensateType === COMPENSATE_TYPE.MULTIPLE &&
            Number(actualAmountTemp) !== Number(totalRemissionItem.totalAfterDeduct)
          ) {
            message.error('请调整减免规则，确保【减免后应收金额】与【实际还款金额】一致');
            return;
          }

          // return;

          //判断是否跳期还款

          // console.log('----', value, mapUploadFile);
          submitOfflineRepay({
            ...value,
            ...mapUploadFile,
            accountName,
            accountNumber,
            orderNo,
            productCode,
            repayPlayNos,
            actualAmount: actualAmountTemp, //如果是足额还款  取应还  不然写实际的
            isRemission: isRemissionTemp,
            costDetail: compensateType === COMPENSATE_TYPE.MULTIPLE ? dataSource || [] : [],
            totalRepayAmount: totalQuotaItem?.shouldAmountDueAll, //应还总额 actualAmount是实际应还总额
            freezeAmount: someMount?.repayingMount,
            totalRemissionAmount: totalRemissionItem.totalRemissionAmount,
            // 小贷提交接口调整，必传参数兼容
            proportionNumberValue: 0,
            customLiquidatedDamages: false,
            liquidatedDamagesAmount: 0,
          }).then(() => {
            message.success('操作成功');
            close();
          });
        }}
      >
        <DividerTit title="还款信息" style={{ marginTop: 0 }}>
          <ProForm.Group>
            <ProFormSelect
              rules={[{ required: true }]}
              name="actualRepayRole"
              width="sm"
              label="实际还款方"
              placeholder="请选择"
              options={[
                {
                  label: '用户本人',
                  value: '1',
                },
                {
                  label: '第三方',
                  value: '2',
                  disabled: productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN,
                },
              ]}
              fieldProps={{
                onChange: (val) => {
                  if (val === ACTUAL_REPAY_ROLE.PERSON) {
                    //多期代偿不足而还款，需要减免
                    form.setFieldsValue({
                      isFullAmount: true,
                      isRemission: false,
                      compensateType: '',
                    });
                    setSelectRepayKeys([]);
                    getTotal([]);
                  }
                },
              }}
            />
            <ProFormDatePicker
              name="repayDate"
              width="sm"
              rules={[{ required: true }]}
              fieldProps={{
                disabledDate: disableFutureDate,
              }}
              placeholder="选择日期"
              label="还款日期"
            />
          </ProForm.Group>
          <ProFormDependency name={['actualRepayRole']}>
            {({ actualRepayRole }) => {
              return actualRepayRole == ACTUAL_REPAY_ROLE.THIRD_PARTY ? (
                <ProForm.Group>
                  <ProFormSelect
                    rules={[{ required: true }]}
                    name="compensateType"
                    width="sm"
                    label="代偿方式"
                    // placeholder="请选择"
                    options={[
                      {
                        label: '单期代偿',
                        value: '1',
                      },
                      {
                        label: '代偿结清',
                        value: '2',
                        disabled: hasSomeIsNotRepay, //只有全部为贷款还款才能选中代偿结清
                      },
                    ]}
                    fieldProps={{
                      //如果为代偿结清，待还款的item全部要选中
                      onChange: (val) => {
                        if (val === COMPENSATE_TYPE.MULTIPLE) {
                          //多期代偿不足而还款，需要减免
                          form.setFieldsValue({ isFullAmount: false, isRemission: true });
                          setSelectRepayKeys(
                            data?.data.map((item: ShouldRepayItem) => {
                              if (mapStatusZh[item.status] === '待还款') return item.termDetail;
                            }),
                          );
                          setSelectRows(data?.data);
                          selectWithDeduct(
                            data?.data?.filter((item: ShouldRepayItem) => {
                              return mapStatusZh[item.status] === '待还款';
                            }),
                          );
                        } else if (val === COMPENSATE_TYPE.SINGLE) {
                          //单期代偿 足额还款，不默认 不减免默认
                          form.setFieldsValue({ isFullAmount: true, isRemission: false });
                          setSelectRepayKeys([]);
                          getTotal([]);
                        }
                      },
                    }}
                  />
                  <ProFormText
                    rules={[{ required: true }]}
                    name="compensateOrgName"
                    width="sm"
                    label="代偿第三方名称"
                  />
                </ProForm.Group>
              ) : (
                ''
              );
            }}
          </ProFormDependency>
          <ProForm.Group>
            <ProFormText
              rules={[
                {
                  required: true,
                  max: 64,
                  pattern: /^[A-Za-z0-9]+$/,
                  message: '请输入三方还款流水号（支持大小写字母和数字）',
                },
              ]}
              name="thirdFlowId"
              width="sm"
              label="三方还款流水号"
            />
            <ProFormRadio.Group
              name="isAllowJumpTerm"
              disabled
              width="sm"
              rules={[{ required: true }]}
              label="是否支持跳期还款"
              initialValue={false}
              options={[
                {
                  label: '是',
                  value: true,
                },
                {
                  label: '否',
                  value: false,
                },
              ]}
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormSelect
              rules={[{ required: true }]}
              name="repayChannel"
              width="sm"
              label="还款渠道"
              options={[
                {
                  label: '微信二维码',
                  value: RECIEVE_BANK_CHANNEL.WEIXIN,
                },
                {
                  label: '对公打款',
                  value: RECIEVE_BANK_CHANNEL.COMPANY,
                },
              ]}
              fieldProps={{
                onChange: (val) => {
                  form.setFieldsValue({ repayBankNo: CHANNEL_BANK_NO[val] });
                },
              }}
            />
            <ProFormText
              rules={[{ required: true }]}
              name="repayBankNo"
              width="sm"
              label="银行卡号"
              disabled={true}
              tooltip={{
                title:
                  '因财务对账所需，需录入我们的收款账户号。（微信二维码：**********、对公打款：***************）',
                overlayStyle: { minWidth: 300 },
              }}
            />
          </ProForm.Group>
        </DividerTit>

        <DividerTit
          title={
            <>
              还款期数{' '}
              <span style={{ color: 'grey', fontSize: 14 }}>
                注：汇总数据是根据选中的期数进行汇总。
              </span>
            </>
          }
        >
          <ShowInfo
            noCard
            infoMap={totalQuotaItemMap}
            data={totalQuotaItem}
            selfDefine={selfDefineTotal}
            // textClassName={globalStyle?.colorRed}
          />
          <Table
            rowSelection={{
              type: 'checkbox',
              ...rowSelection,
            }}
            pagination={false}
            rowKey="termDetail"
            columns={columns as ColumnType<RepayItem>[]}
            dataSource={data?.data}
          />
          <ProForm.Group style={{ marginTop: 10 }}>
            <ProForm.Item label="还款中/元" className={itemStyle.formItemRemission}>
              <div>{someMount?.repayingMount}</div>
            </ProForm.Item>
            <ProForm.Item
              label={
                <>
                  <b>
                    <Tooltip placement="topLeft" title="注释：剩余应还金额=应还款总金额-还款中">
                      <QuestionCircleOutlined className={globalStyle.iconCss} />
                    </Tooltip>
                    剩余应还金额/元：
                  </b>
                </>
              }
              className={itemStyle.formItemRemission}
            >
              <div>{someMount?.shouldRepayRestAmount}</div>

              {/* <div>{new BigNumber(someMount?.orderAmount).minus(someMount?.repayingMount)}</div> */}
            </ProForm.Item>
          </ProForm.Group>
        </DividerTit>
        <DividerTit title="减免规则">
          <ProFormDependency name={['compensateType', 'actualRepayRole']} style={{ marginTop: 10 }}>
            {({ actualRepayRole, compensateType }) => {
              return (
                <>
                  <ProForm.Group>
                    <ProFormRadio.Group
                      name="isFullAmount"
                      width="sm"
                      disabled={
                        actualRepayRole === ACTUAL_REPAY_ROLE.THIRD_PARTY &&
                        compensateType === COMPENSATE_TYPE.MULTIPLE
                      }
                      rules={[{ required: true }]}
                      // value={actualRepayRole===ACTUAL_REPAY_ROLE.THIRD_PARTY?false:null}
                      label="是否足额还款"
                      options={[
                        {
                          label: '是',
                          value: true,
                        },
                        {
                          label: '否',
                          value: false,
                        },
                      ]}
                    />
                    <ProFormDependency name={['isFullAmount']}>
                      {({ isFullAmount }) => {
                        // console.log(isFullAmount);

                        return isFullAmount === false ? (
                          <ProFormDigit
                            name="actualAmount"
                            fieldProps={{ precision: 2 }}
                            rules={[
                              { required: true },
                              {
                                validator: (_, value) => {
                                  if (value === 0) {
                                    return Promise.reject(new Error('实际还款金额为0,需大于0'));
                                  }
                                  if (value > Number(totalQuotaItem?.shouldAmountDueAll)) {
                                    return Promise.reject(
                                      new Error('实际还款金额不应大于应还款总金额'),
                                    );
                                  }
                                  if (value) {
                                    // console.log(value);
                                    // const phoneReg = /^1\d{10}$/;
                                    if (value > Number(someMount.shouldRepayRestAmount)) {
                                      return Promise.reject(
                                        new Error('实际还款金额不应大于剩余应还金额'),
                                      );
                                    }
                                  }
                                  return Promise.resolve();
                                },
                              },
                            ]}
                            label="实际还款金额/元"
                          />
                        ) : (
                          <></>
                        );
                      }}
                    </ProFormDependency>
                  </ProForm.Group>
                </>
              );
            }}
          </ProFormDependency>
          <ProForm.Group>
            <ProFormDependency name={['isFullAmount']}>
              {({ isFullAmount }) => {
                return isFullAmount === false ? (
                  <ProFormRadio.Group
                    width="sm"
                    name="isRemission"
                    rules={[{ required: true }]}
                    // disabled={
                    //   // (compensateType === COMPENSATE_TYPE.MULTIPLE || compensateType === COMPENSATE_TYPE.SINGLE) &&
                    //   actualRepayRole === ACTUAL_REPAY_ROLE.THIRD_PARTY ||
                    // }
                    disabled={true}
                    label="是否需要减免"
                    options={[
                      {
                        label: '是',
                        value: true,
                      },
                      {
                        label: '否',
                        value: false,
                      },
                    ]}
                  />
                ) : (
                  ''
                );
              }}
            </ProFormDependency>
            {/* 需要减免的才展示 */}
            <ProFormDependency name={['isRemission']}>
              {({ isRemission }) => {
                return isRemission ? (
                  <ProForm.Item label="减免后剩余应收金额/元">
                    <div style={{ marginLeft: 7 }}>
                      <span className={globalStyle?.colorRed}>
                        {totalRemissionItem.totalAfterDeduct}
                      </span>
                    </div>
                  </ProForm.Item>
                ) : (
                  <></>
                );
              }}
            </ProFormDependency>
          </ProForm.Group>
          <ProFormDependency name={['isRemission']}>
            {({ isRemission }) => {
              //需要减免和有勾选中
              return isRemission && selectRepayKeys.length ? (
                <>
                  <ProForm.Group>
                    <ProForm.Item label="减免后总本金/元" className={itemStyle.formItemRemission}>
                      <div>{totalRemissionItem.principalAfterDeduct}</div>
                    </ProForm.Item>
                    <ProForm.Item label="减免后总利息/元" className={itemStyle.formItemRemission}>
                      <div>{totalRemissionItem.interestAfterDeduct}</div>
                    </ProForm.Item>
                  </ProForm.Group>
                  <ProForm.Group>
                    <ProForm.Item label="减免后总罚息/元" className={itemStyle.formItemRemission}>
                      <div>{totalRemissionItem.penaltyAfterDeduct}</div>
                    </ProForm.Item>
                    <ProForm.Item
                      label="减免后总其他费用/元"
                      className={itemStyle.formItemRemission}
                    >
                      <div>{totalRemissionItem.otherCostAfterDeduct}</div>
                    </ProForm.Item>
                  </ProForm.Group>
                  <EditableProTable
                    rowKey="id"
                    toolBarRender={false}
                    value={dataSource}
                    onChange={(val) => {
                      // console.log(val);
                      setDataSource(val);
                    }}
                    scroll={{ x: 'max-content' }}
                    actionRef={actionRef}
                    columns={editColumns}
                    // controlled
                    recordCreatorProps={false}
                    editable={{
                      type: 'multiple',
                      editableKeys,
                      onChange: setEditableRowKeys,
                      onValuesChange: (record, recordList) => {
                        // console.log(recordList);
                        setDataSource(recordList);
                      },
                      actionRender: (row) => {
                        return [
                          <Button
                            key="plusBtn"
                            type="primary"
                            icon={<PlusOutlined />}
                            // disabled={true}
                            onClick={() => {
                              // const newDataSource = [ ].concat(dataSource);
                              // setDataSource(newDataSource);
                              actionRef?.current?.addEditRecord({
                                id: (Math.random() * 1000000).toFixed(0),
                              });
                            }}
                          />,
                          <Button
                            key="deleteBtn"
                            type="primary"
                            icon={<DeleteOutlined />}
                            onClick={() => {
                              setDataSource(dataSource.filter((item) => item?.id !== row?.id));
                            }}
                          />,
                        ];
                      },
                    }}
                  />
                </>
              ) : (
                <></>
              );
            }}
          </ProFormDependency>
        </DividerTit>
        <DividerTit title="还款凭证">
          <CommonImageUpload
            extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
            label="上传附件"
            name="attach"
            rules={[{ required: true }]}
            // labelCol={{ span: 5 }}
            max={5}
            listType="text"
            size={10}
            fileListEdit={allFileList?.attach || []}
            desPath="EP_AUTH_INFO"
            mapFileList={mapFileList}
            accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
          />
        </DividerTit>
      </ModalForm>
    </>
  );
};

export default AddCashAndLeaseOfflineRepay;
