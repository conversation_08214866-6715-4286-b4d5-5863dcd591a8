import { request } from '@umijs/max';
import type {
  IcancelPolicyParams,
  IgetCarLoanInfoItem,
  IgetCarRepaymentRes,
  IgetIncommingInfo,
  IgetListParams,
  IlistItem,
  IlookCarInfoItem,
  IofflineRepayParams,
  IremissionInfo,
  IrepaymentInfoItem,
} from '../types';

/**
 * 接口 https://micro-stg.huolala.work/ldoc/view#/ldoc/view/je24ozLJ/jP81wdzq/pX9E5AXr/q2RLxEmX
 * bme - lalafinance - repayment - api
 * commitid stg - ab78bfe4
 */
/**
 * 获取列表
 */
export function getList(
  params: IgetListParams,
): Promise<{ data: IlistItem[]; total: number; success: boolean }> {
  return request(`/repayment/cms/bill/order/repay/list`, {
    method: 'GET',
    params,
  });
}

/**
 * 还款管理 导出
 */
export async function exportExcel(params: IgetListParams) {
  return request(`/repayment/cms/bill/order/repay/policy/export`, {
    responseType: 'blob',
    params,
    getResponse: true,
  });
}

/**
 * 获取还款管理 - 车险 - 详情信息 的进件信息 /bizadmin/cash/order/detail/03032023060916525555943
 */
export async function getIncommingInfo(orderNo: string): Promise<IgetIncommingInfo> {
  const data = await request(`/bizadmin/cash/order/detail/${orderNo}`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data?.data;
}

/**
 * 获取贷后管理 - 还款管理 - 车险 - 详情信息 的还款计划
 */
export async function getCarRepayment(orderNo: string): Promise<IgetCarRepaymentRes> {
  const data = await request(`/repayment/cms/bill/repay/plan/${orderNo}`, {
    method: 'GET',
  });
  return data?.data || {};
}

/**
 * 获取贷后管理 - 还款管理 - 车险 - 详情信息 的放款信息 多笔
 */
export async function getCarLoanInfo(orderNo: string): Promise<IgetCarLoanInfoItem[]> {
  const data = await request(`/quota/lending/getLendingInfoList/${orderNo}`, {
    method: 'GET',
  });
  return data?.data || {};
}

/**
 * 获取贷后管理 - 还款管理 - 车险 - 详情信息 的还款信息 多笔
 */
export async function getCarRepaymentInfo(orderNo: string): Promise<IrepaymentInfoItem[]> {
  const data = await request(`/repayment/cms/bill/repay/info/${orderNo}`, {
    method: 'GET',
  });
  return data;
}

/**
 * 获取贷后管理 - 还款管理 - 车险 - 详情信息 的 还款信息的 按照车辆查看信息
 * bme - lalafinance - repayment - api
 * commitid stg - ab78bfe4
 */
export async function getLookCarInfoInfo(orderNo: string): Promise<IlookCarInfoItem[]> {
  const data = await request(`/repayment/cms/bill/repay/info/policy/${orderNo}`, {
    method: 'GET',
  });
  return data?.data;
}

/**
 * 获取贷后管理 -> 还款管理 - 车险 -> 详情信息 的 还款信息 的 查看还款登记 的 还款登记列表
 */
export async function getRepayRegist(planNo: string): Promise<IlookCarInfoItem[]> {
  const data = await request(`/repayment/cms/bill/record/${planNo}`, {
    method: 'GET',
  });
  return data;
}

/**
 * 获取贷后管理 -> 还款管理 - 车险 -> 详情信息 的 还款信息 的 查看还款登记 的 减免项
 * 获取减免信息
 */
export async function getRemission(
  orderNo: string,
  repayPlanNo: string,
): Promise<IremissionInfo[]> {
  const data = await request(`/repayment/offlineRemit/getRemission/${orderNo}/${repayPlanNo}`, {
    method: 'GET',
  });
  return Array.isArray(data?.data) ? data?.data : [data?.data];
}

/**
 * 获取还款凭证
 */
export async function getRecord(repayPlanNo: string) {
  return request(`/repayment/offlineRemit/getRecord`, {
    method: 'GET',
    params: { repayPlanNo },
  });
}

/**
 * 还款详情导出
 */
export async function exportRepayDetail({ orderNo }: any) {
  return request(`/repayment/cms/bill/repay/detail/excel/${orderNo}`, {
    method: 'GET',
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * 线下还款 提交审批
 */
export async function offlineRepay(data: IofflineRepayParams) {
  return request(`/repayment/offlineRemit/policy/apply`, {
    method: 'POST',
    data,
  });
}

/**
 * 退保结项 提交审批
 */
export async function cancelPolicy(data: IcancelPolicyParams[]) {
  return request(`/repayment/offlineRemit/cancelPolicy`, {
    method: 'POST',
    data,
  });
}
