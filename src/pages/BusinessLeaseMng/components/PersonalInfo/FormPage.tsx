import classNames from 'classnames';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import {
  CONTACT_PERSOAN_NAME,
  CONTACT_PERSOAN_PHONE,
  CONTACT_PERSOAN_TYPE,
  FIELD_CAN_EDIT,
  FIELD_KEY,
  FIELED_VALIDATE_MAP,
  SPOUSE_INFO_CODE,
  VALIDATE_MAP,
} from './consts';
import DisplayItem from './DisplayItem';
import style from './index.less';
import type { FieldItem, FormPageProps } from './types';

const FormPage = (props: FormPageProps) => {
  const { form, data, baseInfo, viewMode, editMode } = props;
  const [rulesObj, setRulesObj] = useState<Record<string, any>>({});
  const [hiddenFiled, setHiddenFiled] = useState<Record<string, any>>({}); // 隐藏不显示的字段
  const [disabledObj, setDisabledObj] = useState<Record<string, boolean>>({});

  // 校验手机号重复
  const validatePhoneRepeat = (obj: any, value: string): Promise<any> => {
    const values = form.getFieldsValue();
    const phone1 = values[FIELD_KEY.PHONE_1];
    const phone2 = values[FIELD_KEY.PHONE_2];
    const spousePhone = values[FIELD_KEY.PHONE_OF_SPOUSE];

    let errorMsg = '';
    for (let i = 0; i < CONTACT_PERSOAN_PHONE.length; i++) {
      const code = CONTACT_PERSOAN_PHONE[i];
      const otherValue = values[code];
      if (value && value === otherValue && obj.field !== code) {
        errorMsg = '联系人手机号重复,请更换其他';
        return Promise.reject(new Error(errorMsg));
        break;
      }
      if ((value && value === phone1) || (value && value === phone2)) {
        errorMsg = '联系人手机号不能与本人手机号相同';
        return Promise.reject(new Error(errorMsg));
        break;
      }
      if (value && value === spousePhone) {
        errorMsg = '联系人手机号不能与配偶手机号相同';
        return Promise.reject(new Error(errorMsg));
        break;
      }
    }
    return Promise.resolve();
  };

  const validateNameRepeat = (obj: any, value: string): Promise<any> => {
    const values = form.getFieldsValue();
    // TODO: 本人姓名来源
    const name = baseInfo?.name;
    const spouseName = values[FIELD_KEY.NAME_OF_SPOUSE];

    let errorMsg = '';

    for (let i = 0; i < CONTACT_PERSOAN_NAME.length; i++) {
      const code = CONTACT_PERSOAN_NAME[i];
      const otherValue = values[code];
      if (value && value === otherValue && obj.field !== code) {
        errorMsg = '联系人姓名重复,请更换其他';
        return Promise.reject(new Error(errorMsg));
        break;
      }
      if (value && value === name) {
        errorMsg = '联系人姓名不能与本人姓名相同';
        return Promise.reject(new Error(errorMsg));
        break;
      }
      if (value && value === spouseName) {
        errorMsg = '联系人姓名不能与配偶姓名相同';
        return Promise.reject(new Error(errorMsg));
        break;
      }
    }
    return Promise.resolve();
  };

  // 校验联系人类型是否重复，不能与其他紧急联系类型均为配偶，否则提示：联系人类型不能有多个配偶;
  // 若基础信息选择已婚，配偶信息已填， 则联系类型不能选择配偶，否则提示：联系人类型不能选择配偶
  const validateContactType = (): Promise<any> => {
    const values = form.getFieldsValue();
    const maritalStatus = values[FIELD_KEY.MARITAL_STATUS];

    let count = 0;

    let errorMsg = '';
    for (let i = 0; i < CONTACT_PERSOAN_TYPE.length; i++) {
      const code = CONTACT_PERSOAN_TYPE[i];
      const value = values[code];
      // 20 是已婚状态, 1是配偶类型
      if (maritalStatus && maritalStatus === '20' && value && value === '1') {
        errorMsg = '联系人类型不能选择配偶';
        return Promise.reject(new Error(errorMsg));
        break;
      } else if (value && value === '1') {
        count++;
      }
    }
    if (count > 1) {
      errorMsg = '联系人类型不能有多个配偶';
      return Promise.reject(new Error(errorMsg));
    }
    return Promise.resolve();
  };

  // 检查配偶信息，配偶姓名与本人实名姓名不能相同；
  // 配偶手机号不能与本人手机号1、本人手机号2相同
  const validateSpouseInfo = (obj: any): Promise<any> => {
    const values = form.getFieldsValue();
    const spouseName = values[FIELD_KEY.NAME_OF_SPOUSE];
    const spouseId = values[FIELD_KEY.IDCARD_OF_SPOUSE];
    const spousePhone = values[FIELD_KEY.PHONE_OF_SPOUSE];
    const name = baseInfo?.name;
    const ID = values[FIELD_KEY.ID];
    const phone1 = values[FIELD_KEY.PHONE_1];
    const phone2 = values[FIELD_KEY.PHONE_2];

    let errorMsg = '';
    if (obj.field === FIELD_KEY.NAME_OF_SPOUSE && spouseName && spouseName === name) {
      errorMsg = '配偶姓名不能与本人姓名相同';
      return Promise.reject(new Error(errorMsg));
    }
    if (obj.field === FIELD_KEY.IDCARD_OF_SPOUSE && spouseId && spouseId === ID) {
      errorMsg = '配偶身份证不能与本人身份证相同';
      return Promise.reject(new Error(errorMsg));
    }
    if (
      (obj.field === FIELD_KEY.PHONE_OF_SPOUSE && spousePhone && spousePhone === phone1) ||
      (spousePhone && spousePhone === phone2)
    ) {
      errorMsg = '配偶手机号不能与本人手机号相同';
      return Promise.reject(new Error(errorMsg));
    }
    return Promise.resolve();
  };

  const initRules = (item: any) => {
    const { fieldCode } = item;
    const { allowNull, fieldType = '' } = item.attribute;
    let ruleArr: any = [];
    const required = !allowNull;
    if (required) {
      ruleArr.push({ required: true });
    } else {
      ruleArr.push({ required: false });
    }
    if (VALIDATE_MAP[fieldType]) {
      ruleArr.push(VALIDATE_MAP[fieldType]);
    }
    // 有补充的校验规则
    if (FIELED_VALIDATE_MAP[fieldCode]) {
      ruleArr = ruleArr.concat(FIELED_VALIDATE_MAP[fieldCode]);
    }
    // 联系人手机号统一添加是否重复校验规则
    if (CONTACT_PERSOAN_PHONE.includes(fieldCode)) {
      ruleArr.push({
        validator: validatePhoneRepeat,
      });
    }
    // 联系人姓名统一添加是否重复校验规则
    if (CONTACT_PERSOAN_NAME.includes(fieldCode)) {
      ruleArr.push({
        validator: validateNameRepeat,
      });
    }
    // 联系人类型添加校验
    if (CONTACT_PERSOAN_TYPE.includes(fieldCode)) {
      ruleArr.push({
        validator: validateContactType,
      });
    }
    // 配偶信息校验
    if (SPOUSE_INFO_CODE.includes(fieldCode)) {
      ruleArr.push({
        validator: validateSpouseInfo,
      });
    }

    return ruleArr;
  };

  const resetRules = (id: string, rules: Record<string, any>, required: boolean) => {
    const originRule = rules[id];
    if (originRule && originRule[0] && originRule[0].required !== undefined) {
      originRule[0].required = required;
    }
    return originRule;
  };

  // 婚姻状况规则打补丁
  const patchMaritalRule = (value: string, originRule: Record<string, any>) => {
    /* 特殊逻辑：婚姻状况必填的情况下，
     * 选择20已婚、21初婚、22再婚、23复婚的情况下配偶信息必填；
     * 选择10未婚、30丧偶、40离婚、90未说明的婚姻状况的情况下，配偶信息非必填；
     * (配偶姓名、配偶身份证、配偶联系电话）
     */
    const rulsTemp: any = {};
    const spouseNameIndex: number = data.findIndex(
      (field: FieldItem) => field.fieldCode === FIELD_KEY.NAME_OF_SPOUSE,
    );
    const spouseIdIndex: number = data.findIndex(
      (field: FieldItem) => field.fieldCode === FIELD_KEY.IDCARD_OF_SPOUSE,
    );
    const spousePhoneIndex: number = data.findIndex(
      (field: FieldItem) => field.fieldCode === FIELD_KEY.PHONE_OF_SPOUSE,
    );
    if (value === '20' || value === '21' || value === '22' || value === '23') {
      if (spouseNameIndex > -1) {
        data[spouseNameIndex].attribute.allowNull = false;
        const id = data[spouseNameIndex].fieldCode;
        const newRule = resetRules(id, originRule, true);
        rulsTemp[id] = newRule;
      }
      if (spouseIdIndex > -1) {
        data[spouseIdIndex].attribute.allowNull = false;
        const id = data[spouseIdIndex].fieldCode;
        const newRule = resetRules(id, originRule, true);
        rulsTemp[id] = newRule;
      }
      if (spousePhoneIndex > -1) {
        data[spousePhoneIndex].attribute.allowNull = false;
        const id = data[spousePhoneIndex].fieldCode;
        const newRule = resetRules(id, originRule, true);
        rulsTemp[id] = newRule;
      }
      // 显示字段
      setHiddenFiled((origin) => {
        return {
          ...origin,
          [FIELD_KEY.NAME_OF_SPOUSE]: false,
          [FIELD_KEY.IDCARD_OF_SPOUSE]: false,
          [FIELD_KEY.PHONE_OF_SPOUSE]: false,
        };
      });
    } else if (value === '10' || value === '30' || value === '40' || value === '90') {
      if (spouseNameIndex > -1) {
        data[spouseNameIndex].attribute.allowNull = true;
        const id = data[spouseNameIndex].fieldCode;
        const newRule = resetRules(id, originRule, false);
        rulsTemp[id] = newRule;
      }
      if (spouseIdIndex > -1) {
        data[spouseIdIndex].attribute.allowNull = true;
        const id = data[spouseIdIndex].fieldCode;
        const newRule = resetRules(id, originRule, false);

        rulsTemp[id] = newRule;
      }
      if (spousePhoneIndex > -1) {
        data[spousePhoneIndex].attribute.allowNull = true;
        const id = data[spousePhoneIndex].fieldCode;
        const newRule = resetRules(id, originRule, false);

        rulsTemp[id] = newRule;
      }
      // 清除掉原来填写的内容
      form.setFieldsValue({
        [FIELD_KEY.NAME_OF_SPOUSE]: '',
        [FIELD_KEY.IDCARD_OF_SPOUSE]: '',
        [FIELD_KEY.PHONE_OF_SPOUSE]: '',
      });
      // 隐藏字段
      setHiddenFiled((origin) => {
        return {
          ...origin,
          [FIELD_KEY.NAME_OF_SPOUSE]: true,
          [FIELD_KEY.IDCARD_OF_SPOUSE]: true,
          [FIELD_KEY.PHONE_OF_SPOUSE]: true,
        };
      });
    }

    console.log('rulsTemp', rulsTemp);

    setRulesObj({ ...rulesObj, ...rulsTemp });
  };

  const initData = async () => {
    const obj: Record<string, any> = {};
    const rules: Record<string, any> = {};
    const disabled: Record<string, any> = {};

    data.forEach((item: any) => {
      let value: any = item.attribute.value || undefined;
      if (item.attribute.fieldType === 'DATE') {
        value = value ? dayjs(item.attribute.value) : '';
      }
      if (item.attribute.fieldType === 'CITY') {
        value = value ? value : [];
      }
      if (item.attribute.fieldType === 'OPTIONS' && value && Array.isArray(value)) {
        value = value.join('');
      }
      // if (item.attribute.fieldType === 'IMAGE') {
      //   value = value ? [value] : [];
      // }
      // 姓名从接口里取
      if (baseInfo && item.fieldCode === FIELD_KEY.NAME) {
        value = baseInfo.name;
        disabled[item.fieldCode] = true;
      }
      // 性别从接口里取
      if (baseInfo && item.fieldCode === FIELD_KEY.SEX) {
        value = [baseInfo.sex];
        disabled[item.fieldCode] = true;
      }
      // 出生日期
      if (baseInfo && item.fieldCode === FIELD_KEY.DATE_OF_BIRTH) {
        value = dayjs(baseInfo.birthDate);
        disabled[item.fieldCode] = true;
      }
      // 本人手机号1
      if (baseInfo && item.fieldCode === FIELD_KEY.PHONE_1) {
        value = baseInfo.phone;
        disabled[item.fieldCode] = true;
      }
      // 身份证
      if (baseInfo && item.fieldCode === FIELD_KEY.ID) {
        value = baseInfo.idNo;
        disabled[item.fieldCode] = true;
      }

      // 如果是修改模式，根据黑名单判断哪些字段需要禁止修改
      if (editMode && FIELD_CAN_EDIT.includes(item.fieldCode)) {
        disabled[item.fieldCode] = true;
      }

      obj[item.fieldCode] = value;
      const ruleArr = initRules(item);
      rules[item.fieldCode] = ruleArr;
    });
    form.setFieldsValue(obj);
    console.log('form data', obj);
    //婚姻状况信息需要打补丁
    const maritalInfo = data.find((item) => item.fieldCode === FIELD_KEY.MARITAL_STATUS);
    if (maritalInfo) {
      const value = maritalInfo.attribute.value;
      patchMaritalRule(value, rules);
    }
    // 设置校验规则
    setRulesObj(rules);
    // 不可编辑的字段
    setDisabledObj(disabled);
    console.log('rules obj', rules);
  };

  useEffect(() => {
    if (data) {
      initData();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, editMode]);

  const handleLongDateChange = (name: string, date: any) => {
    // 选择了长期有效，则写入长期有效的日期值
    form.setFieldValue(name, date);
  };

  const handleUploadChange = (fileUrl: string, fieldCode: string) => {
    form.setFieldValue(fieldCode, fileUrl);
  };

  const handlePickerChange = (value: any, item: FieldItem) => {
    /* 特殊逻辑：婚姻状况必填的情况下，
     * 选择20已婚、21初婚、22再婚、23复婚的情况下配偶信息必填；
     * 选择10未婚、30丧偶、40离婚、90未说明的婚姻状况的情况下，配偶信息非必填；
     * (配偶姓名、配偶身份证、配偶联系电话）
     */
    if (item.fieldCode === FIELD_KEY.MARITAL_STATUS) {
      patchMaritalRule(value, rulesObj);
    }
  };

  return (
    <>
      {data?.map((item) => {
        return (
          <div
            key={item.fieldCode}
            className={classNames({
              [style.hidden]: hiddenFiled[item.fieldCode],
            })}
          >
            <DisplayItem
              data={data}
              label={item.attribute.fieldDesc}
              name={item.fieldCode}
              renderType={item.attribute.componentType}
              fieldType={item.attribute.fieldType}
              options={item.attribute.fieldOptions}
              disabled={viewMode || disabledObj[item.fieldCode]}
              rules={rulesObj[item.fieldCode]}
              defaultValue={item.attribute.value}
              onLongDateSelect={(value) => {
                handleLongDateChange(item.fieldCode, value);
              }}
              form={form}
              onPickerChange={(value) => {
                handlePickerChange(value, item);
              }}
              onUploadChange={(fileUrl) => {
                handleUploadChange(fileUrl, item.fieldCode);
              }}
            />
          </div>
        );
      })}
    </>
  );
};

export default FormPage;
