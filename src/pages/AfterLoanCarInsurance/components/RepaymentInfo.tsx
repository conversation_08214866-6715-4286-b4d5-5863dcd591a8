import { getCarInsuranceDetail } from '@/pages/CarInsurance/services';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, useAccess } from '@umijs/max';
import { Descriptions, Space } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import { exportRepayDetail, getCarRepaymentInfo } from '../services';
import type { IgetIncommingInfo, IrepaymentInfoItem } from '../types';
import ExportExcel from './ExportExcel';
import LookCarInfoModal from './LookCarInfoModal';
import LookRepayRegistModal from './LookRepayRegistModal';
import OfflineRepayModal from './OfflineRepayModal';

type Props = {
  incomingInfo: IgetIncommingInfo;
};
const RepaymentInfo: React.FC<Props> = (props) => {
  const { orderNo } = (history as any).location.query;
  const [lookCarInfoModalVisible, setLookCarInfoModalVisible] = useState(false);
  const [lookRepayRegistModalVisible, setLookRepayRegistModalVisible] = useState(false);
  const [currentTerm, setCurrentTerm] = useState(0);
  const [repayPlanNo, setRepayPlanNo] = useState('');
  const [termDetail, setTermDetail] = useState('');
  const [carOrderNoDetail, setCarOrderNoDetail] = useState<any>({});
  const [offlineRepayOpen, setOfflineRepayOpen] = useState(false);
  const access = useAccess();
  useEffect(() => {
    getCarInsuranceDetail(orderNo).then((data) => {
      setCarOrderNoDetail(data);
    });
  }, []);

  const expandeOptions = [
    { label: '已还款总额', value: 'returnedAmountDue' },
    { label: '已还本金', value: 'returnedPrincipal' },
    { label: '已还利息', value: 'returnedInterest' },
    { label: '已还提前结清违约金', value: 'preSettleCost' },
    { label: '剩余应还总额', value: 'remainingAmountDue' },
    { label: '剩余应还本金', value: 'remainingPrincipal' },
    { label: '剩余应还利息', value: 'remainingInterest' },
    { label: '已还退保盈余', value: 'policyCancelFee' },
  ];
  const repaymentInfoColumns: ProColumns<IrepaymentInfoItem>[] = [
    { dataIndex: 'termDetail', title: '期数' },
    { dataIndex: 'repayTime', title: '应还日期' },
    { dataIndex: 'shouldAmountDue', title: '应还总额' },
    { dataIndex: 'shouldPrincipal', title: '应还本金' },
    { dataIndex: 'shouldInterest', title: '应还利息' },
    { dataIndex: 'overdueInterest', title: '逾期罚息' },
    { dataIndex: 'otherCost', title: '其他费用' },
    {
      dataIndex: 'options',
      title: '操作',
      render(_, record) {
        return (
          <Space>
            {access.hasAccess('repayment_detail_recording_postLoanMng_detail') && (
              <a
                onClick={() => {
                  setRepayPlanNo(record?.repayPlanNo);
                  setLookRepayRegistModalVisible(true);
                }}
              >
                查看还款登记
              </a>
            )}
            <a
              onClick={() => {
                setCurrentTerm(record?.term);
                setTermDetail(record?.termDetail);
                setLookCarInfoModalVisible(true);
              }}
            >
              按车辆查看
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        search={false}
        bordered
        size="small"
        columns={repaymentInfoColumns}
        pagination={false}
        request={async () => getCarRepaymentInfo(orderNo)}
        expandable={{
          expandedRowRender: (record) => {
            return (
              <Descriptions column={4}>
                {expandeOptions.map((item) => {
                  const { label, value } = item;
                  return <Descriptions.Item label={label}>{record[value]}</Descriptions.Item>;
                })}
              </Descriptions>
            );
          },
        }}
        toolBarRender={() => [
          // access.hasAccess('repayment_detail_closing_postLoanMng_detail') && (
          //   <CancelPolicyModal>
          //     <Button type="primary" danger>
          //       结项
          //     </Button>
          //   </CancelPolicyModal>
          // ),
          // access.hasAccess('repayment_detail_submit_postLoanMng_detail') && (
          //   <Button
          //     type="primary"
          //     onClick={async (e) => {
          //       // 获取订单状态 只有还款中的订单才能线下还款

          //       const { orderStatus } = carOrderNoDetail;
          //       if (
          //         ![EcarInsuranceStatus.REPAYING, EcarInsuranceStatus.OVERDUE].includes(orderStatus)
          //       ) {
          //         e.stopPropagation();
          //         Modal.info({
          //           icon: null,
          //           content: (
          //             <div>
          //               只有订单状态为 <Tag>还款中</Tag> 和 <Tag>已逾期</Tag> 才可以线下还款
          //             </div>
          //           ),
          //         });
          //       } else {
          //         setOfflineRepayOpen(true);
          //       }
          //     }}
          //   >
          //     线下还款
          //   </Button>
          // ),

          access.hasAccess('repayment_detail_export_postLoanMng_detail') && (
            <ExportExcel getParams={() => ({ orderNo })} exportExcel={exportRepayDetail} />
          ),
        ]}
      />

      {offlineRepayOpen && (
        <OfflineRepayModal
          incomingInfo={props.incomingInfo}
          offlineRepayOpen={offlineRepayOpen}
          setOfflineRepayOpen={setOfflineRepayOpen}
        />
      )}

      <LookCarInfoModal
        currentTerm={currentTerm}
        termDetail={termDetail}
        lookCarInfoModalVisible={lookCarInfoModalVisible}
        setLookCarInfoModalVisible={setLookCarInfoModalVisible}
      />
      <LookRepayRegistModal
        repayPlanNo={repayPlanNo}
        lookRepayRegistModalVisible={lookRepayRegistModalVisible}
        setLookRepayRegistModalVisible={setLookRepayRegistModalVisible}
      />
    </>
  );
};
export default memo(RepaymentInfo);
