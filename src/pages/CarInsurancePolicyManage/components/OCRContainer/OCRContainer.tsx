import { useCarInsuranceBulkUploadContext } from '@/pages/CarInsurancePolicyManage/context/CarInsuranceBulkUploadContext';
import { Button, Divider, message, Popconfirm, Spin } from 'antd';
import { pick } from 'lodash';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useReducer,
  useRef,
  useState,
} from 'react';
import type { CarInsurancePolicyBaseInfoParams } from '../../data';
import { PolicyEvent, policyHolderMap, policyInfoTemplate, PostPolicyBaseParams } from '../../enum';
import { editUpdateCarInsurancePolicy, getCarInsurancePolicyOcrInfo } from '../../services';
import {
  carInsurancePolicyBaseInfoKeys,
  checkReturnStatus,
  diffSingleDuplicateOcrInfo,
  transformFile2OcrStruct,
} from '../../utils';
import PdfView from '../PdfView';
import type { UploadOcrModalRef } from '../UploadOcrModal';
import UploadOcrModal from '../UploadOcrModal';
import './index.less';
import ResultForm from './ResultForm';

const disabledStyle = {
  color: '#00000040',
  cursor: 'not-allowed',
};

const ocrResultReducer: React.Reducer<Record<string, any>, Record<string, any>> = (
  state: Record<string, any>,
  action: Record<string, any>,
) => {
  switch (action.type) {
    case 'update_selectedItem':
      return {
        ...state,
        selectedItem: action.payload.item,
        lastSelectedItem: action.payload.item,
      };
    case 'init_update_sortDataSource_and_selectedItem':
      const structureData = {
        ...state,
        sortDataSource: action.payload.sortDataSource,
        selectedItem: action.payload.selectedItem,
        lastSelectedItem: state.lastSelectedItem, // 初始化时，顺带更新上次切换的保单
      };
      // 初始化时，如果存在上次切换的保单，则直接返回上次切换的保单
      // 这里会存在一个冲突情况，如果重新上传保单，新的文件uid也会覆盖，导致第三条个判断逻辑正确，则会返回上一次的选择项（实际上该项已不存在）
      if (
        state?.lastSelectedItem &&
        Object.keys(state.lastSelectedItem).length &&
        action.payload?.sortDataSource?.find(
          (item: any) => item.uid === state.lastSelectedItem?.uid,
        ) !== undefined && // 判断文件是否存在于列表中，以便处理重新ocr时的当前选择文件高亮问题
        state.lastSelectedItem.uid !== action?.payload?.selectedItem?.uid && // 处理重新ocr时的当前选择文件高亮问题
        action.payload.sortDataSource?.length === state.sortDataSource?.length // 存在删除保单文件时列表不对等时，不返回上次切换的保单，走重新排序
      ) {
        return {
          ...structureData,
          selectedItem: state?.lastSelectedItem,
        };
      }
      return structureData;
    case 'clear_policy':
      return {
        ...state,
        sortDataSource: null,
        selectedItem: null,
      };
    default:
      throw new Error('未知的 action type');
  }
};

const OCRContainer: React.FC<any> = forwardRef((props, ref) => {
  const { state, dispatch } = useCarInsuranceBulkUploadContext();
  const [isLoading, setIsLoading] = useState(false);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [currentOCRMsgState, dispathOCRMsgResult] = useReducer(
    ocrResultReducer,
    {
      selectedItem: {},
      lastSelectedItem: {},
      sortDataSource: [],
    },
    (initialState) => {
      return initialState;
    },
  );

  const reUploadOcrModalRef = useRef<UploadOcrModalRef>(null);
  const formCheckRef = useRef<any>(null);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    hasPass: (callback: () => void) => {
      formCheckRef.current?.validateFields().then(() => {
        const hasPass = currentOCRMsgState.sortDataSource?.every(
          (item: any) => item.ocrInfo.checkFlag === true,
        );
        if (!hasPass) {
          message.error('当前保单列表里存在保单状态异常的内容，无法提交，请重新检查后再尝试提交');
          return;
        }
        callback();
      });
    },
  }));

  // 再次ocr
  const reUploadOcr = () => {
    if (!currentOCRMsgState.selectedItem?.response?.data?.filePath) {
      message.error('当前保单不存在，无法进行ocr识别');
      return;
    }
    let itemInfo;

    setIsLoading(true);
    getCarInsurancePolicyOcrInfo({
      shortFilePath: [currentOCRMsgState.selectedItem?.response?.data?.filePath],
    })
      .then((result) => {
        if (result && result?.success) {
          message.success('ocr识别成功');
          itemInfo = {
            ...currentOCRMsgState.selectedItem,
            ocrInfo: transformFile2OcrStruct(result?.data[0]),
          };
        }
      })
      .catch(() => {
        itemInfo = {
          ...currentOCRMsgState.selectedItem,
          ocrInfo: transformFile2OcrStruct(policyInfoTemplate),
        };
        return false;
      })
      .finally(() => {
        // diff筛选
        const newFile = diffSingleDuplicateOcrInfo(
          state.safePolicyInfoList,
          itemInfo,
          currentOCRMsgState.selectedItem,
        );
        // 重新ocr更新当前保单
        dispathOCRMsgResult({
          type: 'update_selectedItem',
          payload: {
            item: newFile,
          },
        });
        dispatch({
          type: 'sync_update_policy',
          payload: {
            uid: currentOCRMsgState.selectedItem?.uid,
            file: newFile,
          },
        });
        setIsLoading(false);
      });
  };

  // 更新校验编辑过后的数据
  const validatorUpdateEditbleData = async () => {
    setIsLoading(true);
    const form = formCheckRef?.current?.getForm();
    const values = form?.getFieldsValue();
    const formValues = {
      ...currentOCRMsgState?.selectedItem?.ocrInfo,
      ...values,
    };
    const newFormValues = {
      ...formValues,
      commercialInsuranceStartDate: formValues.commercialInsuranceStartDate
        ? formValues.commercialInsuranceStartDate.format('YYYY-MM-DD HH:mm:ss')
        : undefined, // 只有在undefined时date picker组件才会正确显示时间内容，不然会出现一堆invalid date
      commercialInsuranceEndDate: formValues.commercialInsuranceEndDate
        ? formValues.commercialInsuranceEndDate.format('YYYY-MM-DD HH:mm:ss')
        : undefined,
    };
    newFormValues.event = PolicyEvent.POLICY_VERIFICATION; // 事件类型-保单校验
    // 提交接口校验
    const pickKeys = carInsurancePolicyBaseInfoKeys<CarInsurancePolicyBaseInfoParams>(
      PostPolicyBaseParams,
    );
    // 挑选必要字段
    const formatParams = pick(newFormValues, pickKeys);

    editUpdateCarInsurancePolicy(formatParams)
      .then((result) => {
        if (result && result.success) {
          message.success('校验成功');
          delete newFormValues.event;
          let ocrInfo = {
            ...currentOCRMsgState.selectedItem?.ocrInfo,
            ...newFormValues,
            ...(result?.data || {}),
            policyHolder:
              policyHolderMap?.[newFormValues.policyHolder] || newFormValues.policyHolder, // 转一层投保人
            checkStatus: checkReturnStatus(result?.data),
            id: result?.data?.carPolicyRspDTO?.id, // 更新id，如果checkFlag为true，id则会在此结构体内
            orderNo: result?.data?.carPolicyRspDTO?.orderNo, // 更新orderNo，如果checkFlag为true，orderNo则会在此结构体内
          };
          // 如果是首次通过的保单，在后续有编辑提交校验的话，需要重置校验初始化状态
          if (ocrInfo?.passInit) {
            ocrInfo = transformFile2OcrStruct(ocrInfo); // 重新转换处理ocr信息，将passInit状态重置（因为passInit状态在初始化时会自动通过）
          }
          const fileInfo = {
            ...currentOCRMsgState.selectedItem,
            ocrInfo,
          };
          // diff筛选
          const newFile = diffSingleDuplicateOcrInfo(
            state.safePolicyInfoList,
            fileInfo,
            currentOCRMsgState.selectedItem,
          );
          // 更新到顶层centext, state 更新会自动触发当前模块关联值的update
          dispathOCRMsgResult({
            type: 'update_selectedItem',
            payload: {
              item: newFile,
            },
          });
          dispatch({
            type: 'sync_update_policy',
            payload: {
              uid: currentOCRMsgState.selectedItem?.uid,
              file: newFile,
            },
          });
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // 点击侧边栏切换保单
  const switchUpdateOcrResult = (itemInfo: any) => {
    // 如果正在加载中，则不进行切换
    if (isLoading) return;
    // 更新切换的目标
    dispathOCRMsgResult({
      type: 'update_selectedItem',
      payload: {
        item: itemInfo,
      },
    });
    // 切换后触发表单校验
    formCheckRef?.current?.validateFields();
  };

  // 重新上传ocr
  const againUploadOcr = async (files) => {
    const file = files[0];

    let error;
    const abController = new AbortController();
    setAbortController(abController);
    return await getCarInsurancePolicyOcrInfo(
      {
        shortFilePath: [file.response.data.filePath],
      },
      {
        signal: abController.signal,
      },
    )
      .then((ocrResponse) => {
        if (ocrResponse?.success && !!ocrResponse?.data) {
          (file as any).ocrInfo = transformFile2OcrStruct(ocrResponse?.data[0]);

          return true;
        }
        return true;
      })
      .catch((err) => {
        error = err;
        file.ocrInfo = transformFile2OcrStruct(policyInfoTemplate);
        return err?.code !== 'ERR_CANCELED';
      })
      .finally(() => {
        if (error?.code === 'ERR_CANCELED') {
          message.error('OCR识别取消');
          return false;
        }
        // 更新到顶层centext
        /**
         *  这里理论上的uid是需要新文件的uid，但实际旧的uid需要用于diff，只有通过diff后才会被覆盖为新文件的uid
         */
        // 格式化校验是否存在重复保单
        const newFile = diffSingleDuplicateOcrInfo(
          state.safePolicyInfoList,
          file,
          currentOCRMsgState.selectedItem,
        );
        // 因ocr识别情况，产品要求所有异常都需要正常回显，不能block业务
        dispathOCRMsgResult({
          type: 'update_selectedItem',
          payload: {
            item: newFile,
          },
        });

        dispatch({
          type: 'sync_update_policy',
          payload: {
            uid: currentOCRMsgState.selectedItem?.uid,
            file: newFile,
          },
        });
        message.success('ocr识别成功');
        return true;
      });
  };

  console.log('currentOCRMsgState', currentOCRMsgState);

  // 渲染ocr提示内容
  const renderOcrResult = (ocrInfo: any) => {
    if (ocrInfo?.passInit || ocrInfo?.checkStatus === 'success') {
      return '校验通过';
    }
    return ocrInfo?.exceptionMsgList?.[0] ?? 'ocr识别失败';
  };

  // 默认选中第一个
  useEffect(() => {
    const new_dataSource = state.safePolicyInfoList.sort((a, b) => {
      // 按照 checkStatus 排序: error > warning > success
      // error状态：值为空，checkFlag为false
      // warring状态：值不为空，checkFlag为true，exceptionMsgList不为空
      // success状态：值不为空，checkFlag为true，exceptionMsgList为空
      const statusOrder = { error: 3, warning: 2, success: 1 };
      return (
        (statusOrder[b?.ocrInfo?.checkStatus] || 0) - (statusOrder[a?.ocrInfo?.checkStatus] || 0)
      );
    });
    dispathOCRMsgResult({
      type: 'init_update_sortDataSource_and_selectedItem',
      payload: {
        sortDataSource: new_dataSource,
        selectedItem: new_dataSource[0],
      },
    });
  }, [state.safePolicyInfoList]);

  useEffect(() => {
    if (formCheckRef?.current) {
      // 进入页面后触发表单校验，必定会有异常，除非所有保单都验证通过
      formCheckRef?.current?.validateFields();
    }
    return () => {
      // 清理当前ocr结果
      dispathOCRMsgResult({ type: 'clear_policy' });
    };
  }, []);

  return (
    <Spin spinning={isLoading}>
      <div className="bulk-upload-content">
        <div className="bulk-upload-content-wrapper">
          <div className="bulk-upload-content-left">
            <div className="bulk-upload-content-header">
              <div className="bulk-upload-content-col">序号</div>
              <div className="bulk-upload-content-col">保单文件</div>
              <div className="bulk-upload-content-col">校验结果</div>
            </div>
            <div className="bulk-upload-content-body">
              {currentOCRMsgState.sortDataSource?.map((item, index) => (
                <div
                  key={item.uid}
                  className={`${'bulk-upload-content-row'} ${
                    currentOCRMsgState.selectedItem?.uid === item.uid ? `${'selected'}` : ''
                  }`}
                  onClick={() => {
                    switchUpdateOcrResult(item);
                  }}
                >
                  <p className="bulk-upload-content-col text-item">{index + 1}</p>
                  <p className="bulk-upload-content-col text-item">{item?.name}</p>
                  <p
                    className={`${'bulk-upload-content-col text-item'} ${`action-status-${item?.ocrInfo?.checkStatus}`}`}
                  >
                    {renderOcrResult(item?.ocrInfo)}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <div className="bulk-upload-content-right">
            {/* 右侧头部 */}
            <div className="bulk-upload-content-header">
              <div className="bulk-upload-content-ocr-header">
                <div className="bulk-upload-content-ocr-title">OCR识别结果</div>
                <div className="bulk-upload-content-ocr-action">
                  <span
                    style={
                      currentOCRMsgState.selectedItem?.ocrInfo?.duplicate
                        ? disabledStyle
                        : undefined
                    }
                    onClick={(e) => {
                      if (currentOCRMsgState.selectedItem?.ocrInfo?.duplicate) {
                        return;
                      }
                      e.stopPropagation();
                      reUploadOcr();
                    }}
                  >
                    再次OCR识别
                  </span>
                  <span
                    onClick={(e) => {
                      e.stopPropagation();
                      reUploadOcrModalRef?.current?.show();
                    }}
                  >
                    重新上传
                  </span>
                  <span
                    style={
                      currentOCRMsgState.selectedItem?.ocrInfo?.duplicate
                        ? disabledStyle
                        : undefined
                    }
                    onClick={(e) => {
                      if (currentOCRMsgState.selectedItem?.ocrInfo?.duplicate) {
                        return;
                      }
                      e.stopPropagation();
                      validatorUpdateEditbleData();
                    }}
                  >
                    检测校验
                  </span>
                  <Popconfirm
                    title="确定要删除此文件吗？"
                    okText="确定"
                    cancelText="取消"
                    placement="bottomLeft"
                    onConfirm={() => {
                      dispatch({
                        type: 'delete_policy',
                        payload: {
                          uid: currentOCRMsgState.selectedItem?.uid,
                        },
                      });
                    }}
                  >
                    <Button type="link" style={{ padding: '0' }}>
                      删除此文件
                    </Button>
                  </Popconfirm>
                </div>
              </div>
            </div>
            {/* ocr 详情内容 */}
            <div className="bulk-upload-content-ocr-container">
              {/* 识别结果 */}
              <div className="bulk-upload-content-ocr-result">
                <ResultForm
                  ref={formCheckRef}
                  // checkStatus={editableState}
                  dataInfo={currentOCRMsgState?.selectedItem?.ocrInfo}
                />
              </div>
              <Divider style={{ margin: '24px 0 12px 0' }} />
              {/* pdf 预览 */}
              <div className={'bulk-upload-content-ocr-pdfquote'}>
                <PdfView url={currentOCRMsgState.selectedItem?.response?.data?.netWorkPath} />
              </div>
            </div>
          </div>
        </div>
        {/* 重新上传 */}
        <UploadOcrModal
          visibleRef={reUploadOcrModalRef}
          needOcr={true}
          needCustomRequest={false}
          onFinish={async (values) => {
            if (values?.carInsurancePolicyFile?.[0]?.status === 'done') {
              return againUploadOcr(values?.carInsurancePolicyFile);
            }
            message.error('正在上传中，请等待上传完毕再提交');
            return false;
          }}
          title="重新上传"
          draggerProps={{
            name: 'carInsurancePolicyFile',
            isNeedLimit: true,
            limitCount: 1,
            max: 1,
            onRemove: () => {
              abortController?.abort();
            },
          }}
        />
      </div>
    </Spin>
  );
});

export default OCRContainer;
