/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-12-05 13:53:23
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2025-01-22 15:35:07
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/consts.ts
 * @Description: consts
 */
export enum ORDER_STATUS {
  REGISTRY = 1,
  SIGN_DONE = 3,
  PRE_AUDIT_PASS = 15, // 预审通过
  RISK_REJECT = 22, // 风控驳回
  EDIT_PLAN = 30, // 金融方案待调整
  REDUCE_CREDIT_PASS = 50, // 降额通过
  SIGN_SUCCESS = 54, //  签约成功
  THIRD_PENDING = 55, // 资方授信审批中
  THIRD_PASS = 56, // 资方授信审批通过
  CHANNEL_APPROVE_REJECT = 57, // 资方授信审批拒绝
  WAIT_LOAN = 60, //  待放款
  LOAN_SUCCESS = 61, //  放款审批通过
  THIRD_LOAN_ING = 62, // 资方放款中
  THIRD_USE_FAIL = 63, // 资方支用失败
  THIRD_LOAN_SUCCESS = 64, // 资方放款成功
  THIRD_LOAN_FAIL = 65, // 资方放款失败
  SUBMIT_CAR_DELIVERY = 70, // 交车资料提交成功
  CAR_DELIVERY_REJECT = 71, // 交车资料驳回
  WAIT_REPAYMENT = 80, // 待还款
  OVERDUE = 81, // 逾期
  NORMAL_SETTLEMENT = 82, // 正常结清
  OVERDUE_SETTLEMENT = 83, // 逾期结清
  PRE_SETTLEMENT = 84, // 提前结清
}

// 允许显示交车按钮的状态
export const allowShowDeliveryButtonStatus = [
  ORDER_STATUS.SIGN_SUCCESS,
  ORDER_STATUS.WAIT_LOAN,
  ORDER_STATUS.LOAN_SUCCESS,
  ORDER_STATUS.THIRD_LOAN_ING,
  ORDER_STATUS.THIRD_USE_FAIL,
  ORDER_STATUS.THIRD_LOAN_SUCCESS,
  ORDER_STATUS.THIRD_LOAN_FAIL,
  ORDER_STATUS.SUBMIT_CAR_DELIVERY,
  ORDER_STATUS.CAR_DELIVERY_REJECT,
  ORDER_STATUS.WAIT_REPAYMENT,
  ORDER_STATUS.OVERDUE,
  ORDER_STATUS.NORMAL_SETTLEMENT,
  ORDER_STATUS.OVERDUE_SETTLEMENT,
  ORDER_STATUS.PRE_SETTLEMENT,
];

export enum APPLY_SOURCE {
  USER = 1, //  个人用户进件
  CHANNEL = 2, // 渠道用户进件
}

export const statusLeaseMap = {
  0: '订单创建成功',
  1: '已注册',
  3: '授权签约成功',
  5: '进件失败',
  10: '定金支付成功',
  13: '预审中',
  14: '预审驳回',
  15: '预审通过',
  16: '预审拒绝',
  18: '风控中',
  20: '待初审',
  21: '待复审',
  22: '风控驳回',
  // 23: '降额待确认',
  30: '方案待调整',
  40: '审核通过',
  41: '审核拒绝',
  50: '降额通过',
  51: '车辆绑定成功',
  53: '银行卡绑定成功',
  54: '签约成功',
  55: '资方授信审批中',
  56: '资方授信审批通过',
  57: '资方授信审批拒绝',
  '-1': '失效',
  '-2': '撤销',
  60: '待放款',
  61: '放款审批通过',
  62: '资方放款中',
  63: '资方支用失败',
  64: '资方放款成功',
  65: '资方放款失败',
  // 70: '交车资料提交成功',
  // 71: '交车资料驳回',
  80: '待还款',
  81: '逾期',
  82: '正常结清',
  83: '逾期结清',
  84: '提前结清',
};

export const channelStatusMap = {
  '-1': '-',
  0: '待放款',
  1: '待还款',
  2: '提前结清',
  3: '结清',
  4: '逾期',
  5: '逾期结清',
  6: '坏账',
  7: '待完单',
  8: '单期代偿',
  9: '代偿结清',
  10: '退保结项',
  11: '回购结清',
  12: '结清中',
  13: '还款中',
};

export const COST_TYPE_OPT = [
  { value: 1, label: '利息' },
  { value: 2, label: '提前结清违约金' },
  { value: 3, label: '逾期罚息' },
  { value: 5, label: '逾期滞纳金' },
];

export const mapStatusZh = {
  0: '待放款',
  1: '待还款',
  2: '提前结清',
  3: '正常还款',
  4: '逾期',
  5: '逾期结清',
  6: '坏账',
  8: '单期代偿',
  9: '代偿结清',
};

// 可编辑状态: 15-预审通过 22-风控驳回
export const CAN_EDIT_STATUS = [1, 3, 15, 22, 30];
// 查看进件单信息:预审中-13、初审中-20、终审中-21、审核通过-40、审核拒绝-41、失效-'-1'
export const CAN_LOOK_INCOME_STATUS = [13, 20, 21, 40, 41, -1];

// 进件状态
export enum INCOME_STATUS {
  ADDITIONAL_MATERIALS = 11, //补充资料
  ADDITIONAL_MATERIALS_AGAIN = 13, //再次补充资料
  RECONFIRM_LEASE_PLAN = 16, // 重新确认方案，可以改首付
  RESELECT_VEHICLE = 19, // 重新选择车辆，退回补件时退回的
  RESELECT_LEASE_PROGRAM = 20, //重新选择租赁方案
  CALL_FAIL = 26, // 电联失败
}

// 上海银行审批状态
export const SH_BANK_STATUS: Record<number, string> = {
  0: '初始化',
  8: '前筛拒绝',
  10: '待申请',
  20: '处理中',
  30: '处理成功',
  31: '处理失败',
  40: '审核通过',
  41: '审核拒绝',
};

export enum PRE_STATUS {
  NO_EXECUTE = 0, //未执行
  RISKING = 10, //风控中
  AUDIT_SUCCESS = 20, //评估完成
}

// 交车资料状态后端枚举
export enum CAR_DELIVERY_STATUS_MAP {
  INIT = -1, // 初始化
  NOT_SUBMIT = 0, // 未提交
  OVER_SUBMIT = 1, // 超期未提交
  INITATED = 2, // 待审核
  ADOPT = 3, // 审核通过
  REJECT = 4, // 驳回
}

// 交车资料状态
export const CAR_DELIVERY_STATUS = {
  0: '未提交',
  1: '超期未提交',
  2: '待审核',
  3: '审核通过',
  4: '驳回',
};
