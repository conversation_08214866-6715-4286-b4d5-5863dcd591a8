/*
 * @Date: 2023-09-04 16:44:25
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-10-30 17:16:19
 * @FilePath: /lala-finance-biz-web/src/pages/FirstAudit/detail.tsx
 * @Description:
 */
import { DividerTit, ShowInfo } from '@/components';
import HeaderTabs from '@/components/HeaderTab';
import { needsTypeMap } from '@/enums';
import { PageContainer } from '@ant-design/pro-layout';
import { history } from '@umijs/max';
import { Card, Empty } from 'antd';
import React, { useEffect, useState } from 'react';
import { getFirstAuditList } from './service';

const businessTypeMap = {
  0: '企业',
  1: '冷链',
  2: '零担',
};

const FirstAuditDetail: React.FC = () => {
  const flowNo = history?.location?.query?.flowNo as string;
  const [riskData, setRiskData] = useState<Record<string, any>[]>([]);
  const [baseInfo, setBaseInfo] = useState<Record<string, any>>();
  const baseInfoMap = {
    organization: '上报机构',
    companyName: '企业名称',
    staffCount: '员工人数',
    enterpriseRevenue: '年营收',
    enterpriseType: '企业类型',
    tradeCode: '行业类型',
    industryPosition: '行业地位',
    monthFlow: '月流水（元）',
    goodsType: '货物类型', //枚举
    carScene: '用车场景',
    requireVehicles: '需求车辆',
    userCarFrequency: '发车频次',
    billDay: '账期天数',
    needsType: '需求类型',
    amount: '账期额度',
    businessType: '业务类型',
    ownerTitle: '归属抬头',
  };

  const baseInfoDefine = {
    needsType: baseInfo?.needsType
      .split(',')
      .map((item) => needsTypeMap[item])
      .join(','),
    businessType: businessTypeMap[baseInfo?.businessType],
    // carSense: carSceneMap[baseInfo?.carSense],
  };

  const statusMap = {
    0: '拒绝',
    1: '通过',
    2: '驳回',
  };

  const overRunMap = {
    0: '否',
    1: '超限',
  };

  const aboveQuotaMap = {
    0: '否',
    1: '超额',
  };

  // 审查信息
  const examinationInfo = {
    status: '审查结果',
    grade: '审查评级',
    timeLimit: '评级期限上限',
    suggestMaxAmount: '评级审查意见(最高额度/万)',
    auditAccountDays: '审查账期天数',
    auditAmount: '建议额度(万)',
    suggest: '审查意见',
  };
  const auditRecord = {
    status: '审批结果',
    grade: '审批等级',
    suggestMaxAmount: '评级复合意见（最高额度/万）',
    auditAccountDays: '审批账期天数（自然日）',
    timeLimit: '评级期限上限',
    auditAmount: '审批额度（万）',
    overRun: '是否超限（60日然自）',
    aboveQuota: '是否超额（信用等级额度上限）',
    suggest: '审批意见',
  };

  useEffect(() => {
    getFirstAuditList({ flowNo }).then((res) => {
      setBaseInfo(JSON.parse(res?.data?.[0]?.basicInformation));
      const riskInfoList = [];
      (res?.data?.[0]?.riskCallbackInfoList || []).forEach((item) => {
        const { riskPeriod } = item || {};
        let { riskInfo } = item || {};
        if (riskInfo) {
          riskInfo = JSON.parse(riskInfo);
        }

        const obj = {
          ...item,
          riskInfo,
        };
        if (riskPeriod) {
          riskInfoList[riskPeriod - 1] = obj;
        }
      });
      setRiskData(riskInfoList);
    });
  }, []);
  // console.log(data);
  return (
    <div>
      <HeaderTabs />
      <PageContainer>
        <ShowInfo
          infoMap={baseInfoMap}
          data={baseInfo}
          title="基础信息"
          selfDefine={baseInfoDefine}
        />
        <Card title="风控信息" style={{ marginTop: 30 }}>
          <DividerTit title="审查信息" style={{ marginTop: 10 }} />
          {riskData[0]?.riskInfo ? (
            <ShowInfo
              noCard
              infoMap={examinationInfo}
              data={riskData[0]?.riskInfo}
              selfDefine={{
                status: statusMap[riskData[0]?.riskInfo?.status],
              }}
            />
          ) : (
            <Empty />
          )}
          <DividerTit title="审批信息" style={{ marginTop: 10 }} />
          {riskData[1]?.riskInfo ? (
            <ShowInfo
              noCard
              infoMap={auditRecord}
              data={riskData[1]?.riskInfo}
              selfDefine={{
                status: statusMap[riskData[1]?.riskInfo?.status],
                overRun: overRunMap[riskData[1]?.riskInfo?.overRun],
                aboveQuota: aboveQuotaMap[riskData[1]?.riskInfo?.aboveQuota],
              }}
            />
          ) : (
            <Empty />
          )}
        </Card>
      </PageContainer>
    </div>
  );
};

export default FirstAuditDetail;
