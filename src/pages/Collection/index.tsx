import { buyBackBlackListCode, payBlackListCode } from '@/access/accessCode';
import HeaderTab from '@/components/HeaderTab/index';
import { REPURCHASE_STATUS_MAP, SECONDARY_CLASSIFICATION_MAP_ALL } from '@/enums';
import { getAllChannelNameEnum, getProductNameEnum, getUserListEnum } from '@/services/enum';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { downLoadExcel, getUuid, isExternalNetwork } from '@/utils/utils';
import { ExclamationCircleFilled } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess, useRequest } from '@umijs/max';
import { useMount } from 'ahooks';
import { Button, message, Modal, Radio, Tabs, Tag } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import { DispatchModal } from './components';
import AddBuyBlackListButton from './components/AddBuyBlackListButton';
import AddPayBlackListButton from './components/AddPayBlackListButton';
import BatchSendOverdueSMSCon from './components/BatchSendOverdueSMSCon';
import PhoneSearchUserInfo from './components/PhoneSearchUserInfo';
import StepsFreezeModal from './components/StepsFreezeModal';
import type { OverdueCaseList, OverdueCaseListParams } from './data';
import { CLASSIFYTYPE, SORT_MAP, STATUS_CASE } from './data.d';
import { freeze, overdueCaseCount, overdueCaseExport, overdueCaseList, unFreeze } from './service';

enum ROLE_CASE {
  'ALL' = '1',
  'MINE' = '2',
}
enum DISPATCH {
  REPATCH = '2',
}

const FREEZON_STATUS = {
  1: '-',
  2: '正常',
  3: '逾期冻结',
  4: '人工冻结',
  5: '结清解冻',
  6: '人工解冻',
};
const Collection: React.FC<any> = () => {
  const { TabPane } = Tabs;
  const access = useAccess();
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  //子tab
  const [dispatchType, setDispatchType] = useState(STATUS_CASE.WAIT_FOLLOW_UP);
  //父tab
  const [activeKey, setActiveTabKey] = useState<string>(
    access.hasAccess('overdueCase_list_all') ? ROLE_CASE.ALL : ROLE_CASE.MINE,
  );
  //跟进中子tab
  const [followUpType, setFollowUpType] = useState('');

  const [exportLoading, setExportLoading] = useState(false);
  const [totalNum, setTotalNum] = useState<any>({});
  // const [dataSource, setDataSource] = useState([]);

  const [dispatchModalVisible, handleDispatchModalVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>([]);

  const [currentRecords, setCurrentRecords] = useState<OverdueCaseList[]>([]);
  const userlistMap = useRef({});
  const overdueCaseNoList = useRef([]);
  const [productCodeValueEnum, setProductCodeValueEnum] = useState<Record<string, string>>({});

  useEffect(() => {
    getProductNameEnum().then((data) => {
      setProductCodeValueEnum(
        data.reduce((pre, cur) => {
          return {
            ...pre,
            [cur.value]: cur.label,
          };
        }, {}),
      );
    });
  }, []);

  function getExport(form: OverdueCaseListParams) {
    setExportLoading(true);
    overdueCaseExport({ ...form })
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  }
  const [dataCount, setDataCount] = useState<{
    todayNewCase?: number;
    todaySettleCase?: number;
    todayFollowedCase?: number;
  }>();

  const { run } = useRequest(
    (key) => {
      return overdueCaseCount(key === ROLE_CASE.MINE);
    },
    {
      // manual: true,
      onSuccess: (res) => {
        if (res) setDataCount(res);
      },
    },
  );

  const runTable = (params: any, sorter: any) => {
    const sortKey = Object.keys(sorter)[0];
    const sortTemp = sortKey ? { [SORT_MAP[sortKey]]: sorter[sortKey]?.replace('end', '') } : []; //  排序映射
    if (dispatchType === STATUS_CASE.FOLLOWING_UP && followUpType != '')
      params.classifyType = followUpType; //  跟进中状态有更细的分类
    return overdueCaseList({
      ...params,
      urgeStatusList:
        dispatchType === STATUS_CASE.SETTLE
          ? [STATUS_CASE.SETTLE, STATUS_CASE.REVOKE]
          : [dispatchType],
      myList: activeKey === ROLE_CASE.MINE,
      ...sortTemp,
    }).then((res) => {
      // setDataSource(res.data);

      overdueCaseNoList.current = res.data?.map((item: any) => {
        return item.overdueCaseNo;
      });

      switch (dispatchType) {
        case STATUS_CASE.WAIT_FOLLOW_UP:
          setTotalNum({ ...totalNum, totalWait: res.total });
          break;
        case STATUS_CASE.FOLLOWING_UP:
          if (followUpType === '') {
            setTotalNum({ ...totalNum, totalFollow: res.total });
          } else {
            setTotalNum({
              ...totalNum,
              [`FOLLOWING_UP_${followUpType}`]: res.total,
            });
          }
          break;
        case STATUS_CASE.SETTLE:
          setTotalNum({ ...totalNum, totalSettle: res.total });
          break;
        default:
          break;
      }
      return res;
    });
  };

  const columns: ProColumns<OverdueCaseList>[] = [
    {
      title: '催收案件编号',
      dataIndex: 'overdueCaseNo',
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
    },
    {
      title: '钱包提现状态',
      dataIndex: 'withdrawStatus',
      key: 'withdrawStatus',
      valueType: 'select',
      valueEnum: FREEZON_STATUS,
      render: (_, row) => (
        <>
          {/* <span>{dom(row)}</span> */}
          {FREEZON_STATUS[row.withdrawStatus as keyof typeof FREEZON_STATUS] &&
          row.withdrawStatus !== 1 ? (
            <a
              onClick={() => {
                // handleStatusModalVisible(true);
                // setCurrentRow(row);
                optimizationModalWrapper(StepsFreezeModal)({
                  overdueCaseNo: row.overdueCaseNo,
                });
              }}
            >
              {FREEZON_STATUS[row.withdrawStatus as keyof typeof FREEZON_STATUS]}
            </a>
          ) : (
            '-'
          )}
        </>
      ),
    },
    {
      title: '产品ID',
      key: getUuid(),
      dataIndex: 'productCode',
      search: false,
    },

    {
      title: '产品名称',
      key: 'productCode',
      dataIndex: 'productCode',
      valueEnum: () => {
        // 二级分类是多选
        const productSecondCode = formRef.current?.getFieldValue('productSecondTypeCodeList');
        const productCodeMap: any = {};
        for (const productCode in productCodeValueEnum) {
          const secondCode = productCode.substring(0, 4);
          if (productSecondCode?.includes(secondCode)) {
            productCodeMap[productCode] = productCodeValueEnum[productCode];
          }
        }
        return productSecondCode?.length ? productCodeMap : productCodeValueEnum;
      },
      valueType: 'select',
      fieldProps: {
        onChange(val: string) {
          formRef.current?.setFieldValue(
            'productSecondTypeCodeList',
            val ? [val?.substring(0, 4)] : undefined,
          );
        },
      },
      hideInTable: true,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '逾期账单笔数/期数',
      dataIndex: 'overdueTotalCount',
      search: false,
    },
    //已结案不展示
    {
      title: '待还逾期金额（元）',
      dataIndex: 'totalOverdueAmount',
      search: false,
      sorter: true,
      hideInTable: dispatchType === STATUS_CASE.SETTLE,
    },
    {
      title: '已还期数',
      dataIndex: 'actualRepayTerm',
      search: false,
    },
    {
      title: '车架号',
      dataIndex: 'carUniqueCode',
      search: false,
    },
    {
      title: '车牌号',
      dataIndex: 'licenseCode',
      search: false,
    },
    {
      title: '应还月供（元）',
      dataIndex: 'returnedAmountDue',
      search: false,
    },
    {
      title: '还款日',
      dataIndex: 'repayDay',
      search: false,
      render(_, record: any) {
        return record?.repayDay || '-';
      },
    },
    {
      title: '剩余本金（元）',
      dataIndex: 'remainingPrincipal',
      search: false,
      sorter: true,
      hideInTable: dispatchType === STATUS_CASE.SETTLE,
    },
    {
      title: '最大逾期等级',
      dataIndex: 'overdueMaxLevel',
      valueEnum: {
        M1: 'M1',
        M2: 'M2',
        M3: 'M3',
        M4: 'M4',
        M5: 'M5',
        M6: 'M6',
        'M6+': 'M6+',
      },
      fieldProps: {
        mode: 'multiple',
        showArrow: true,
      },
    },
    {
      title: '最长逾期天数',
      dataIndex: 'overdueLongestDays',
      sorter: true,
      search: false,
    },
    {
      title: '资金渠道',
      dataIndex: 'funderChannelName',
      search: false,
    },
    {
      title: '逾期代偿回购',
      dataIndex: 'repurchaseStatus',
      search: false,
      valueEnum: REPURCHASE_STATUS_MAP,
    },
    {
      title: '融租渠道名称',
      dataIndex: 'leaseChannelName',
      search: false,
    },
    {
      title: '催收员',
      dataIndex: 'urgeUserId',
      valueType: 'select',
      // request: getUserListEnum,
      valueEnum: userlistMap.current,
      search: activeKey === ROLE_CASE?.MINE ? false : undefined, //全部tab没有催收员的搜索
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_: any, row: any) => {
        return (
          <>
            {userlistMap.current[row.urgeUserId as keyof typeof userlistMap.current] ||
              row.urgeUserId}
          </>
        );
      },
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeCodeList',
      valueType: 'select',
      // initialValue: 'FINANCE_LEASE',
      valueEnum: SECONDARY_CLASSIFICATION_MAP_ALL,
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        onChange: () => {
          formRef.current?.setFieldValue('productCode', undefined);
        },
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    //待跟进没有最近催收时间
    {
      title: '最近催收时间',
      dataIndex: 'latestUrgeDateTime',
      search: false,
      hideInTable: dispatchType === STATUS_CASE.WAIT_FOLLOW_UP,
    },
    {
      title: '行动标识',
      dataIndex: 'actionSign',
      hideInTable: dispatchType === STATUS_CASE.WAIT_FOLLOW_UP,
      search:
        dispatchType !== STATUS_CASE.WAIT_FOLLOW_UP
          ? {
              // 有个ts问题 所以这样写
              transform(value) {
                return { actionSign: value };
              },
            }
          : false,
      valueEnum: {
        1: '承诺标识',
        2: '有效联系标识',
        3: '无效标识',
      },
    },
    {
      title: '最新催收结果',
      dataIndex: 'latestUrgeResult',
      hideInTable: dispatchType === STATUS_CASE.WAIT_FOLLOW_UP,
      search: false,
    },
    {
      title: '结案时间',
      dataIndex: 'settleDateTime',
      search: false,
      hideInTable: dispatchType !== STATUS_CASE.SETTLE,
    },
    {
      title: '结案金额（元）',
      dataIndex: 'finishAmount',
      search: false,
      hideInTable: dispatchType !== STATUS_CASE.SETTLE,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
    },

    {
      title: '渠道名称',
      dataIndex: 'channelIds',
      valueType: 'select',
      request: () => {
        return getAllChannelNameEnum().then((res: any) => {
          // 渠道帐号限制渠道
          if (
            access.hasRole('leaseChannelUser') &&
            res?.length &&
            access?.currentUser?.channelCode
          ) {
            const val: any = res.filter((item: any) => {
              return item.id === access.currentUser.channelCode;
            });
            if (val?.length) formRef.current?.setFieldValue('channelIds', val[0].id);
          }
          return res;
        });
      },
      debounceTime: 600000,
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: access.hasRole('leaseChannelUser') && !!access.currentUser?.channelCode,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '操作',
      fixed: 'right',
      render: (_, row, dataIndex) => {
        return (
          <>
            {/* 全部tab下的待跟进和跟进中才能派单 */}
            {/* 重新派单 */}
            {activeKey === ROLE_CASE.ALL &&
              !(dispatchType == STATUS_CASE.SETTLE) &&
              access?.hasAccess('againOrder_overdueCase_list') && (
                <a
                  onClick={() => {
                    if (row?.productCode?.slice(0, 4) === '0303') {
                      message.warning('车险分期产品的催收不支持重新派单');
                      return;
                    }
                    handleDispatchModalVisible(true);
                    // confirm()
                    setCurrentRow([row.overdueCaseNo]);
                  }}
                  style={{ marginRight: 5 }}
                >
                  重新派单
                </a>
              )}
            <Link
              state={{
                curList: overdueCaseNoList.current || [],
                curItemIndex: dataIndex,
              }}
              to={{
                pathname: `/businessMng/postLoanMng/collection-detail`,
                search: `?overdueCaseNo=${row?.overdueCaseNo}&remainingPrincipal=${row?.remainingPrincipal}`,
              }}
            >
              查看详情
            </Link>
          </>
        );
      },
    },
  ];
  // console.log(actionRef);
  const tabsClickCall = useCallback((key: any, type: string) => {
    // console.log(actionRef?.current);
    if (type === 'Dispatch') {
      setDispatchType(key);
      if (key === STATUS_CASE.FOLLOWING_UP) {
        setFollowUpType(''); //  切回跟进中时，细分项默认不选择
      }
    } else if (type === 'Follow') {
      setFollowUpType(key);
    }
    actionRef?.current?.reloadAndRest?.();
  }, []);

  const tabsAllOrMineChange = (key: string) => {
    run(key);
    // refresh();
    setActiveTabKey(key);
    setTotalNum({});
    // totalNum.current = {}
    actionRef?.current?.reloadAndRest?.();
  };
  enum FREEZE_UNFREEZE {
    FREEZE = 1,
    UNFREEZE = 2,
  }
  const freezeOrUnFreezeOpenModal = (type: FREEZE_UNFREEZE) => {
    // console.log(currentRow);
    if (currentRow.length === 0) {
      message.warning('请至少选择一行数据！');
      return;
    }
    const typeStr = type === FREEZE_UNFREEZE.FREEZE ? '冻结' : '解冻';
    Modal.confirm({
      title: `你确定${typeStr}勾选的这些吗?`,
      icon: <ExclamationCircleFilled />,
      content: '',
      centered: true,
      onOk: async () => {
        //区分类型
        const func = type === FREEZE_UNFREEZE.FREEZE ? freeze : unFreeze;
        const dataList = currentRecords?.map(
          ({
            accountNumber,
            overdueCaseNo,
            productCode,
            totalOverdueAmount,
            withdrawStatus,
          }: OverdueCaseList) => {
            return {
              accountNumber,
              overdueCaseNo,
              productCode,
              totalOverdueAmount,
              withdrawStatus,
            };
          },
        );
        // console.log(dataList);

        // const dataFinal = FREEZE_UNFREEZE.FREEZE
        //   ? { batchFreezeReq: { overdueCaseReqList: dataList } }
        //   : { batchUnfreezeReq: { overdueCaseReqList: dataList } };
        func({ overdueCaseReqList: dataList } as any).then(() => {
          message.success(`${typeStr}成功`);
          actionRef?.current?.reload();
          actionRef?.current?.clearSelected?.();
        });
      },
      onCancel() {
        // console.log('Cancel');
      },
    });
  };

  useMount(async () => {
    const data = await getUserListEnum();
    if (data?.length) {
      data.forEach((item: { value: string; label: string }) => {
        userlistMap.current = {
          ...userlistMap.current,
          [item.value]: item.label,
        };
      });
    }
  });
  // console.log('render');
  // console.log(pathList);
  return (
    <>
      {/* <HeaderTabs pathList={pathList} /> */}
      <PageContainer>
        <Tabs
          style={{ background: '#fff', padding: '0 10px' }}
          defaultActiveKey={
            access.hasAccess('overdueCase_list_all') ? ROLE_CASE.ALL : ROLE_CASE.MINE
          }
          // activeKey={activeKey}
          onChange={tabsAllOrMineChange}
        >
          {access.hasAccess('overdueCase_list_all') && <TabPane tab="全部" key={ROLE_CASE.ALL} />}
          <TabPane tab="我的催收案件" key={ROLE_CASE.MINE} />
        </Tabs>
        <ProTable<OverdueCaseList>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          search={{ labelWidth: 120 }}
          dateFormatter="string"
          rowKey="overdueCaseNo"
          scroll={{ x: 'max-content' }}
          request={(params, sorter) => {
            return runTable(params, sorter);
          }}
          tableAlertRender={false}
          beforeSearchSubmit={(params) => {
            //重新数量接口
            run(activeKey);
            return params;
          }}
          rowSelection={{
            onChange: (selectedRowKeys, selectRows) => {
              // console.log(selectRows);
              setCurrentRow(selectedRowKeys);
              setCurrentRecords(selectRows);
              // console.log(selectedRowKeys);
            },
          }}
          // onSubmit={}
          // request={(params) => overdueCaseList(params)}
          headerTitle={
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <div style={{ margin: '0px  0px 20px 0px' }}>
                <Tag color="magenta">今日新增{dataCount?.todayNewCase}个</Tag>
                <Tag color="volcano">今日结案{dataCount?.todaySettleCase}个</Tag>
                <Tag color="lime">今日已跟进个数{dataCount?.todayFollowedCase}个</Tag>
              </div>
              <div>
                <Tabs
                  onTabClick={(key) => {
                    tabsClickCall(key, 'Dispatch');
                  }}
                  defaultActiveKey={STATUS_CASE.WAIT_FOLLOW_UP}
                >
                  <TabPane
                    tab={`待跟进${totalNum.totalWait ?? ''}`}
                    key={STATUS_CASE.WAIT_FOLLOW_UP}
                  />
                  <TabPane
                    tab={`跟进中${totalNum.totalFollow ?? ''}`}
                    key={STATUS_CASE.FOLLOWING_UP}
                  />
                  <TabPane tab={`已结案${totalNum.totalSettle ?? ''}`} key={STATUS_CASE.SETTLE} />
                </Tabs>
              </div>
              {dispatchType === STATUS_CASE.FOLLOWING_UP && (
                <div style={{ marginTop: 10 }}>
                  <Radio.Group
                    value={followUpType}
                    onChange={(e) => tabsClickCall(e.target.value, 'Follow')}
                  >
                    <Radio.Button value={CLASSIFYTYPE.NO_CALL}>{`未拨打案件${
                      totalNum.FOLLOWING_UP_0 === undefined ? '' : `(${totalNum.FOLLOWING_UP_0})`
                    }`}</Radio.Button>
                    <Radio.Button value={CLASSIFYTYPE.ENSURE}>{`承诺还款${
                      totalNum.FOLLOWING_UP_1 === undefined ? '' : `(${totalNum.FOLLOWING_UP_1})`
                    }`}</Radio.Button>
                    <Radio.Button value={CLASSIFYTYPE.EFFECTIVE}>{`有效联络${
                      totalNum.FOLLOWING_UP_2 === undefined ? '' : `(${totalNum.FOLLOWING_UP_2})`
                    }`}</Radio.Button>
                    <Radio.Button value={CLASSIFYTYPE.INVALID}>{`失联案件/无效联络${
                      totalNum.FOLLOWING_UP_3 === undefined ? '' : `(${totalNum.FOLLOWING_UP_3})`
                    }`}</Radio.Button>
                  </Radio.Group>
                </div>
              )}
            </div>
          }
          toolBarRender={() => {
            return [
              <>
                {/* 加入回购黑名单 */}
                {access.hasAccess(buyBackBlackListCode) && (
                  <AddBuyBlackListButton list={currentRecords} />
                )}
                {/* 加入垫付黑名单 */}
                {access.hasAccess(payBlackListCode) && (
                  <AddPayBlackListButton list={currentRecords} />
                )}
                {/* 批量发送催收短信 */}
                {!isExternalNetwork() && <BatchSendOverdueSMSCon />}
                {/* 来电客户身份查询 */}
                <PhoneSearchUserInfo />
                {!isExternalNetwork() && (
                  <>
                    <Button
                      type="primary"
                      onClick={() => {
                        // 车险的单不支持
                        if (currentRecords?.[0]?.productCode?.slice(0, 4) === '0303') {
                          message.warning('车险分期产品的催收不支持提现冻结');
                          return;
                        }
                        freezeOrUnFreezeOpenModal(FREEZE_UNFREEZE.FREEZE);

                        // handleDispatchModalVisible(true);
                      }}
                    >
                      提现冻结
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        if (currentRecords?.[0]?.productCode?.slice(0, 4) === '0303') {
                          message.warning('车险分期产品的催收不支持提现解冻');
                          return;
                        }
                        freezeOrUnFreezeOpenModal(FREEZE_UNFREEZE.UNFREEZE);
                        // handleDispatchModalVisible(true);
                      }}
                    >
                      提现解冻
                    </Button>
                    {/* 全部tab下的待跟进和跟进中才能派单 */}
                    {activeKey === ROLE_CASE.ALL &&
                      !(dispatchType == STATUS_CASE.SETTLE) &&
                      access?.hasAccess('againBulkOrder_overdueCase_list') && (
                        <Button
                          key="dispatch"
                          onClick={() => {
                            if (currentRow.length === 0) {
                              message.warning('请至少选择一行数据！');
                              return;
                            }
                            if (currentRecords?.[0]?.productCode?.slice(0, 4) === '0303') {
                              message.warning('车险分期产品的催收不支持重新批量派单');
                              return;
                            }
                            handleDispatchModalVisible(true);
                          }}
                          type="primary"
                        >
                          重新批量派单
                        </Button>
                      )}
                    <Button
                      key="button"
                      type="primary"
                      loading={exportLoading}
                      // loading={exportLoading}
                      onClick={() => {
                        const { ...data } = formRef?.current?.getFieldsValue();
                        const newForm = {
                          ...data,
                          urgeStatusList:
                            dispatchType === STATUS_CASE.SETTLE
                              ? [STATUS_CASE.SETTLE, STATUS_CASE.REVOKE]
                              : [dispatchType],
                          myList: activeKey === ROLE_CASE.MINE,
                        };
                        if (dispatchType === STATUS_CASE.FOLLOWING_UP && followUpType != '')
                          newForm.classifyType = followUpType; //  跟进中状态有更细的分类
                        getExport(newForm);
                      }}
                    >
                      导出
                    </Button>
                  </>
                )}
              </>,
            ];
          }}
        />
        {/* 派单 */}
        <DispatchModal
          onOk={async () => {
            handleDispatchModalVisible(false);
            setCurrentRow([]);
            if (actionRef?.current) {
              actionRef?.current?.reload();
              actionRef?.current?.clearSelected?.();
            }
          }}
          onCancel={() => {
            handleDispatchModalVisible(false);
            setCurrentRow([]);
          }}
          // 重新分配枚举为2
          type={DISPATCH.REPATCH}
          onVisibleChange={handleDispatchModalVisible}
          modalVisible={dispatchModalVisible}
          overdueCaseNo={currentRow}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'businessMng/postLoanMng/collection'}>
      <Collection />
    </KeepAlive>
  </>
);

// export default React.memo(Collection);
