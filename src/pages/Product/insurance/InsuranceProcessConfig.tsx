import { Card, Tabs } from 'antd';
import React from 'react';
import { memo } from 'react';
import InsuranceIncoming from './components/InsuranceIncoming';
import InsuranceLoan from './components/InsuranceLoan';
import InsuranceOverdue from './components/InsuranceOverdue';
import InsurancePayBack from './components/InsurancePayBack';
import InsuranceSettleEarly from './components/InsuranceSettleEarly';
import globalStyle from '@/global.less';

type Props = {
  currentKey: string;
  setCurrentKey: (val: string) => void;
};
const ProcessConfig: React.FC<Props> = (props) => {
  const { currentKey = 'insuranceIncoming', setCurrentKey } = props;
  return (
    <Card title="产品流程配置" className={globalStyle.mt30}>
      <Tabs
        activeKey={currentKey}
        onChange={(key) => {
          setCurrentKey(key);
        }}
      >
        <Tabs.TabPane tab="进件" key="insuranceIncoming" forceRender>
          <InsuranceIncoming />
        </Tabs.TabPane>
        <Tabs.TabPane tab="放款" key="insuranceLoan" forceRender>
          <InsuranceLoan />
        </Tabs.TabPane>
        <Tabs.TabPane tab="还款" key="insurancePayBack" forceRender>
          <InsurancePayBack />
        </Tabs.TabPane>
        <Tabs.TabPane tab="提前结清" key="insuranceSettleEarly" forceRender>
          <InsuranceSettleEarly />
        </Tabs.TabPane>
        <Tabs.TabPane tab="逾期" key="insuranceOverdue" forceRender>
          <InsuranceOverdue />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};
export default memo(ProcessConfig);
