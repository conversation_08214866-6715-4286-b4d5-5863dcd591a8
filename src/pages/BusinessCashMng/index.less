.formItemRemission {
  :global {
    .ant-col.ant-form-item-control {
      width: 221px;
    }
  }
}

.offLineDefine {
  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-form-item-label {
      width: 150px;
    }
  }
}

.buttonStyle {
  :global {
    margin-right: 10px;
    margin-left: 10px;
    color: white;
    background-color: rgb(129, 201, 22);
    border-color: rgb(129, 201, 22);
  }
}
.buttonStyle:hover {
  color: white;
  background-color: rgb(129, 201, 22);
  border-color: rgb(129, 201, 22);
}
.buttonStyle:focus {
  color: white;
  background-color: rgb(129, 201, 22);
  border-color: rgb(129, 201, 22);
}
