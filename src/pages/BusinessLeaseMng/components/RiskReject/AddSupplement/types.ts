import type { Rule } from 'antd/es/form';
export type ItemType = {
  supplementValueList: string[];
  id: number;
  supplementDesc: string;
  supplementValue: string;
};

export type CardProps = {
  data: ItemType;
  disabled?: boolean;
};

export type DataTypes = {
  supplementValueList: string[];
  id: number;
  orderNo: string;
  supplementDesc: string;
  supplementValue: string;
};

export type Options = {
  optionCode: string;
  optionValue: string;
};

export type FieldItem = {
  fieldValue: string;
  fieldCode: string;
  attribute: {
    allowNull: boolean;
    componentType: string;
    fieldDesc: string;
    fieldOptions: { optionCode: string; optionValue: string }[];
    fieldType: string;
    moduleId: string;
    value: string;
  };
};

export type ItemProps = {
  label: string;
  renderType: string;
  fieldType?: string;
  name: string;
  defaultValue?: any;
  options?: Options[] | null;
  readonly?: boolean;
  disabled?: boolean;
  rules?: Rule;
  onInputBlur?: (v: any) => void;
  onPickerChange?: (v: any) => void;
  onDateOk?: (v: Date) => void;
  onUploadSuccess?: (fileUrl: string) => void;
};
