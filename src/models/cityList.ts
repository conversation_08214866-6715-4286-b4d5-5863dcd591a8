/*
 * @Author: your name
 * @Date: 2021-01-27 11:35:34
 * @LastEditTime: 2021-11-30 18:02:25
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/models/userList.js
 */
import { request, useModel } from '@umijs/max';
import { useEffect, useState } from 'react';

export default () => {
  // const [mapCityList, setUserListMap] = useState({});
  const [cityList, setCityList] = useState([]);
  const [cityListNew, setCityListNew] = useState([]);
  const { initialState } = useModel('@@initialState');
  useEffect(() => {
    if (initialState?.currentUser) {
      request('/risk/cityList', { skipErrorHandler: true })
        .then((res) => {
          // const mapTemp = {};
          const cityListTemp = res.data?.map((item: { adcode: string; name: string }) => {
            // mapTemp[item.id] = item.name;
            return { value: item.adcode, label: item.name };
          });
          const cityListNewTemp = res.data?.map((item: { adcode: string; name: string }) => {
            // mapTemp[item.id] = item.name;
            return { value: item.name, label: item.name };
          });

          setCityList(cityListTemp);
          setCityListNew(cityListNewTemp);

          // setUserListMap(mapTemp);
        })
        .catch(() => {
          // console.log(e)
        });
    }
  }, [initialState?.currentUser]);
  return { cityList, cityListNew };
};
