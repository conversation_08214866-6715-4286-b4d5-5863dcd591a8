/**
 * 融租 - 订单账期 支持跨页勾选和虚拟列表
 */
// @ts-ignore
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import {
  getChannelStoreRequestParams,
  setFormValuesForChannelStore,
} from '@/pages/AfterLoan/utils';
import { filterProps, removeBlankFromObject } from '@/utils/tools'; // @ts-ignore
import type { ActionType, ProFormInstance } from '@ant-design/pro-components'; // @ts-ignore
import { ProTable } from '@ant-design/pro-components';
import { useAccess, useModel } from '@umijs/max';
import BigNumber from 'bignumber.js';
import React, { memo, useEffect, useRef, useState } from 'react';
import { VList } from 'virtuallist-antd';
import { getFinancePaymentTermListColumn } from './columns/FinancePaymentTermListColumn';
import OnlineRepay from './components/OnlineRepay';
import { billExport, billList } from './services';
import type { IbillListItem, IbillListParams } from './types';
import { IdimensionEnCode } from './types';

type Props = {};
const secondaryClassification = SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND; //  小圆车融
const FinanceLeaseRepayDetailList: React.FC<Props> = () => {
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Record<string, string[]>>({});
  // 无论分页怎么变化 只要含有key 就会被勾选
  const [allSelectedRowKeys, setAllSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<string, IbillListItem[]>>({});
  const [allSelectedRows, setAllSelectedRows] = useState<IbillListItem[]>([]);
  const { initialState }: any = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { channelCode } = currentUser || {};
  const [currentPage, setCurrentPage] = useState(1);

  const [isBigData, setIsBigData] = useState(false); // 是否是大数据量 大数据量下会卡顿 所以启用虚拟列表

  const actionRef = useRef<ActionType>();

  useEffect(() => {
    setAllSelectedRows(
      Array.from(
        new Map(
          Object.values(selectedRows)
            .flat(2)
            ?.map((item) => [item.id, item]),
        ).values(),
      ) as any,
    );
    setAllSelectedRowKeys([...new Set(Object.values(selectedRowKeys).flat(2))] as any);
  }, [selectedRows, selectedRowKeys]);

  useEffect(() => {
    setFormValuesForChannelStore(access, formRef, { store: 'storeIdList' });
  }, [access]);

  function caculate(rows: any) {
    const amount = rows.reduce((pre: any, cur: any) => {
      return new BigNumber(cur.totalAmountUnpaid || 0).plus(pre);
    }, 0);
    const applyAmount = Number(new BigNumber(amount));
    return applyAmount;
  }

  function getSelectedAlertRender() {
    const amount = caculate(allSelectedRows);
    return allSelectedRows.length ? (
      <div style={{ padding: '0 24px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          已选择 {allSelectedRows.length} 条数据, 涉及汇款金额
          <span style={{ color: 'red' }}>{amount} 元</span>
        </div>
        <a
          onClick={() => {
            setAllSelectedRows([]);
            setSelectedRowKeys({});
            setSelectedRows({});
          }}
        >
          取消选择
        </a>
      </div>
    ) : null;
  }

  return (
    <ProTable
      search={{
        defaultCollapsed: false,
        labelWidth: 'auto',
      }}
      columns={getFinancePaymentTermListColumn({ access, channelCode })}
      request={async (values) => {
        const { current = 1, pageSize = 20, billNo, termList } = values;
        const channelStoreParams = getChannelStoreRequestParams(
          access,
          { channel: 'channelCode', store: 'storeIdList' },
          { channel: 'string' },
        );
        const params: IbillListParams = {
          ...values,
          dimension: IdimensionEnCode.TERM_BILL,
          current,
          pageSize,
          secondaryClassification,
          billNoList: billNo ? [billNo] : undefined,
          termList: termList ? [termList] : undefined,
          ...channelStoreParams,
        };
        return billList(removeBlankFromObject(filterProps(params)));
      }}
      expandable={{}}
      rowKey="id"
      scroll={isBigData ? { y: window.innerHeight - 224 } : { x: 'max-content' }}
      components={
        isBigData
          ? VList({
              height: window.innerHeight - 224,
            })
          : null
      }
      pagination={{
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onShowSizeChange: (currentPage, pageSize) => {
          setCurrentPage(currentPage);
          if (pageSize >= 500) {
            setIsBigData(true);
          } else {
            setIsBigData(false);
          }
        },
      }}
      tableExtraRender={() => {
        return getSelectedAlertRender();
      }}
      rowSelection={{
        selectedRowKeys: allSelectedRowKeys,
        onChange: (_selectedRowKeys, _selectedRowsArg) => {
          setSelectedRowKeys({ ...selectedRowKeys, [currentPage]: _selectedRowKeys });
          setSelectedRows({ ...selectedRows, [currentPage]: _selectedRowsArg });
        },
      }}
      tableAlertOptionRender={false}
      tableAlertRender={false}
      formRef={formRef}
      toolBarRender={() => {
        return [
          access.hasAccess('bill_apply_export_postLoanMng_afterLoanList') && (
            <AsyncExport
              getSearchDataTotal={async () => {
                const values = formRef.current?.getFieldsFormatValue?.();
                const { billNo, termList } = values;
                const params: IbillListParams = {
                  ...values,
                  billNoList: billNo ? [billNo] : undefined,
                  termList: termList ? [termList] : undefined,
                  dimension: IdimensionEnCode.TERM_BILL,
                  current: 1,
                  pageSize: 1,
                  secondaryClassification,
                };
                const data = await billList(removeBlankFromObject(filterProps(params)));
                return data?.total;
              }}
              getSearchParams={() => {
                const values = formRef.current?.getFieldsFormatValue?.();
                const { current = 1, pageSize = 20, billNo, termList } = values;
                const params: IbillListParams = {
                  ...values,
                  billNoList: billNo ? [billNo] : undefined,
                  termList: termList ? [termList] : undefined,
                  dimension: IdimensionEnCode.TERM_BILL,
                  current,
                  pageSize,
                  secondaryClassification,
                };
                return removeBlankFromObject(filterProps(params));
              }}
              getSelectedParams={() => {
                const values = formRef.current?.getFieldsFormatValue?.();
                return {
                  billNoList: allSelectedRows.map((item) => item.billNo),
                  dimension: IdimensionEnCode.TERM_BILL,
                  secondaryClassification,
                  channelCode: values?.channelCode,
                };
              }}
              getSelectedTotal={() => {
                return allSelectedRows.length;
              }}
              exportAsync={billExport}
              taskCode={[ItaskCodeEnValueEnum.REPAY_LEASE_BILL]}
            />
          ),
          access.hasAccess('bill_order_info_postLoanMng_afterLoanList') && (
            <OnlineRepay
              selectedRows={allSelectedRows}
              dimension={IdimensionEnCode.TERM_BILL}
              actionRef={actionRef}
            />
          ),
        ];
      }}
    />
  );
};

export default memo(FinanceLeaseRepayDetailList);
