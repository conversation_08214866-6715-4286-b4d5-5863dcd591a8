import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import ProForm, { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import { history, Link, useRequest } from '@umijs/max';
import { Alert, Button, Col, Form, Image, message, Modal, Row, Steps } from 'antd';
import { useMemo, useState } from 'react';
import { auditEarlyEnd, getEarlyEndDetail } from './service';

const { Step } = Steps;

const STATUS_ENUM = {
  WAIT_AUDIT_FIRST: '待初审',
  FIRST_AUDIT_REJECT: '初审拒绝',
  FIRST_AUDIT_PASS: '初审通过',
  SECOND_AUDIT_REJECT: '复审拒绝',
  SECOND_AUDIT_PASS: '复审通过',
  AUTO_REVOKE: '自动撤销',
};
//订单审核状态
enum ORDER_STATUS {
  WAIT_AUDIT_FIRST = 1,
  FIRST_AUDIT_REJECT = 2,
  FIRST_AUDIT_PASS = 3,
  SECOND_AUDIT_REJECT = 4,
  SECOND_AUDIT_PASS = 5,
  AUTO_REVOKE = 6,
}

//审核操作状态
enum AUDIT_STATUS {
  FIRST_AUDIT_REJECT = 2,
  FIRST_AUDIT_PASS = 3,
}

enum REJECT_REASON {
  a = '还款金额错误',
  b = '还款账号填写错误',
  c = '还款开户行填写错误',
  d = '不符合结清条件',
}

const rejectReasonOptions = [
  {
    label: '还款金额错误',
    value: 'a',
  },
  {
    label: '还款账号填写错误',
    value: 'b',
  },
  {
    label: '还款开户行填写错误',
    value: 'c',
  },
  {
    label: '不符合结清条件',
    value: 'd',
  },
];

const EarlyEndDetail = () => {
  const { id } = history.location.query;
  const [form] = ProForm.useForm();
  const [readOnly, setReadOnly] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);

  const { data, run: reloadData } = useRequest(() => {
    return getEarlyEndDetail(id);
  });

  const handlePass = async () => {
    await form.validateFields();
    Modal.confirm({
      title: '审核通过后将推送到支付系统由财务进行复核，确认资料已审核无误？',
      cancelText: '取消',
      okText: '确认',
      onOk() {
        const outerFormValue = form.getFieldsValue();
        const { prepayType, otherMsg } = outerFormValue;
        const params = {
          id,
          status: AUDIT_STATUS.FIRST_AUDIT_PASS, //初审通过
          prepayType,
          otherMsg,
        };
        //审核通过
        auditEarlyEnd(params).then(() => {
          message.success('审核成功！');
          setModalVisible(false);
          reloadData();
        });
      },
    });
  };

  const handleRejectSubmit = (values) => {
    const outerFormValue = form.getFieldsValue();
    const { otherMsg, prepayType } = outerFormValue;
    const params = {
      id,
      status: AUDIT_STATUS.FIRST_AUDIT_REJECT, //初审拒绝
      rejectReason: values.rejectReason || REJECT_REASON[values.rejectReasonType],
      prepayType,
      otherMsg,
    };
    //审核驳回
    auditEarlyEnd(params).then(() => {
      message.success('驳回成功！');
      setModalVisible(false);
      reloadData();
    });
  };

  useMemo(() => {
    if (data) {
      const {
        orderNo,
        status,
        otherMsg,
        prepayType,
        prepayCostInfoBO,
        payerInfoDTO,
        prepayTime,
      } = data;
      if (status === ORDER_STATUS.WAIT_AUDIT_FIRST || status === ORDER_STATUS.SECOND_AUDIT_REJECT) {
        setReadOnly(false);
      } else {
        setReadOnly(true);
      }

      form.setFieldsValue({
        orderNo,
        advanceSettleAmount: prepayCostInfoBO?.advanceSettleAmount,
        principal: prepayCostInfoBO?.principal,
        advanceSettleAmount: prepayCostInfoBO?.advanceSettleAmount,
        interest: prepayCostInfoBO?.interest,
        advanceSettleLiquidatedDamages: prepayCostInfoBO?.advanceSettleLiquidatedDamages,
        cardNo: payerInfoDTO?.cardNo,
        accountName: payerInfoDTO?.accountName,
        bankName: payerInfoDTO?.bankName,
        otherMsg,
        prepayType: prepayType || undefined,
        prepayTime,
      });
    }
  }, [data]);

  return (
    <>
      <HeaderTab />
      <PageContainer style={{ background: '#fff' }}>
        <ProForm
          form={form}
          style={{ paddingBottom: '40px' }}
          layout="horizontal"
          labelCol={{ span: 2 }}
          submitter={false}
        >
          {/* 待初审和复审拒绝时显示操作按钮 */}
          {(data?.status === ORDER_STATUS.WAIT_AUDIT_FIRST ||
            data?.status === ORDER_STATUS.SECOND_AUDIT_REJECT) && (
            <div>
              <Button className={globalStyle.ml10} onClick={handlePass}>
                通过
              </Button>
              <Button
                className={globalStyle.ml10}
                onClick={() => {
                  setModalVisible(true);
                }}
              >
                驳回
              </Button>
            </div>
          )}
          {data?.status === ORDER_STATUS.FIRST_AUDIT_PASS && (
            <Alert
              style={{ width: 250 }}
              showIcon
              message="提前结清初审通过—待财务复核"
              type="info"
            />
          )}
          <div>
            {data?.statusInfoDTOList?.length && (
              <Row className={globalStyle.mt20} gutter={[16, 16]}>
                <Col span={20}>
                  <Steps
                    progressDot
                    style={{ flexWrap: 'wrap' }}
                    current={data?.statusInfoDTOList?.length}
                    className={globalStyle.stepItemDesc}
                  >
                    {data?.statusInfoDTOList?.map(
                      (item: { type: number; time: string; operatorBy: string }) => {
                        return (
                          <Step
                            style={{ flexBasis: 200 }}
                            key={`${STATUS_ENUM[item.statusEnum]} ${item.time || '-'} ${
                              item.operatorBy || '-'
                            }`}
                            title={
                              <>
                                <div>{STATUS_ENUM[item.statusEnum]} </div>
                                {item.rejectReason && (
                                  <span style={{ color: 'red', fontSize: '12px' }}>
                                    (原因：{item.rejectReason})
                                  </span>
                                )}
                              </>
                            }
                            description={`${item.time || '-'} ${item.operatorBy || '-'}`}
                          />
                        );
                      },
                    )}
                  </Steps>
                </Col>
              </Row>
            )}
          </div>
          <Form.Item label="订单号">
            <Link
              to={`/businessMng/postLoanMng/after-loan-detail?orderNo=${data?.orderNo}&productCode=0201`}
            >
              {data?.orderNo}
            </Link>
          </Form.Item>
          <ProFormText label="结清总金额" readonly name="advanceSettleAmount" width="md" />
          <ProFormText label="结清本金" readonly name="principal" width="md" />
          <ProFormText label="结清利息" readonly name="interest" />
          <ProFormText label="结清违约金" readonly name="advanceSettleLiquidatedDamages" />
          <ProFormText label="付款账号" readonly name="cardNo" />
          <ProFormText label="付款户名" readonly name="accountName" />
          <ProFormText label="付款银行" readonly name="bankName" />
          <ProFormText label="付款日期" readonly name="prepayTime" />

          <Form.Item label="付款凭证">
            <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              {data?.payFileInfoList?.map((item) => {
                return (
                  <div style={{ flexBasis: 200, marginRight: 10 }}>
                    <Image width="200px" height="200px" src={item.fileNetWorkPath} />
                  </div>
                );
              })}
            </div>
          </Form.Item>

          <ProFormText
            name="otherMsg"
            // labelCol={{ span: 5 }}
            width="sm"
            fieldProps={{ maxLength: 20 }}
            label="其他"
            placeholder="请输入其他信息"
            readonly={readOnly}
          />
          <ProFormSelect
            name="prepayType"
            label="提前结清类型"
            width="sm"
            rules={[{ required: true }]}
            readonly={readOnly}
            options={[
              {
                value: 1,
                label: '正常提前结清',
              },
              {
                value: 2,
                label: '异常提前结清(已还6期)',
              },
              {
                value: 3,
                label: '异常提前结清(6期内)',
              },
            ]}
          />
        </ProForm>
        <ModalForm
          title="确定驳回提前结清申请？"
          visible={modalVisible}
          layout="horizontal"
          onFinish={handleRejectSubmit}
          onVisibleChange={setModalVisible}
          modalProps={{
            destroyOnClose: true,
          }}
        >
          <ProForm.Group>
            <ProFormSelect
              rules={[{ required: true }]}
              options={rejectReasonOptions}
              label="驳回原因"
              placeholder="请选择驳回原因"
              name="rejectReasonType"
              width="sm"
            />
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                if (getFieldValue('rejectReasonType') === 'd')
                  return (
                    <ProFormText
                      rules={[
                        { required: true, message: '请输入驳回原因' },
                        { max: 20, message: '驳回原因不超过20个字' },
                      ]}
                      options={rejectReasonOptions}
                      placeholder="请输入驳回原因"
                      name="rejectReason"
                      width="sm"
                    />
                  );
              }}
            </Form.Item>
          </ProForm.Group>
        </ModalForm>
      </PageContainer>
    </>
  );
};

export default EarlyEndDetail;
