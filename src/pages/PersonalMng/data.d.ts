/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2024-01-24 18:08:20
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalMng/data.d.ts
 */
// 获取企业用户管理列表的请求数据类型
export interface EnterpriseUserListParams {
  endApplyTime?: string; // 创建结束时间
  enterpriseName?: string; // 企业名称
  idNo?: string; // 统一社会信用代码
  startApplyTime?: string; // 创建开始时间
  status?: number; // 状态
  userNo?: number; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 页大小
}

// 获取企业用户管理列表的响应数据类型
export interface EnterpriseUserListItem {
  createdAt: string; // 创建时间
  creditAmount: number; // 授信总金额
  currentAmount: number; // 剩余可用额度
  enterpriseName: string; // 企业名称
  freezeAmount: number; // 冻结额度
  productSecondTypeName: string; // 产品二级分类
  idNo: string; // 统一社会信用代码
  status: number; // 状态
  usedAmount: number; // 已使用额度
  userNo: string; // 用户编号
}

// 获取企业用户详情---风控数据---额度详情的请求参数数据类型
export interface QuotaDetailParams {
  subAccountNo: string; // 子账户编号
  userNo: string; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 每页的数据量
}

// 获取企业用户详情---风控数据---额度详情的响应参数数据类型
export interface QuotaDetailItem {
  createTime: string; // 创建时间
  creditAmount: number; // 总额度
  id: string; // id
  idNo: string; // 身份证号
  name: string; // 企业名
  operator: string; // 操作人
  operatorAmount: number; // 调整额度
  operatorType: number; // 操作类型
  riskProductName: string; // 风控产品名
  riskProductTypeName: string; // 风控产品类型名
  status: number; // 状态
}

export interface ProductDataListItem {
  createTime: string;
  creditAmount: number;
  currAmount: number;
  freezeAmount: number;
  overdueAmount: number;
  productFirstTypeName: string;
  productSecondTypeName: string;
  riskLevel: string;
  subAccountNo: string;
  usedAmount: number;
  status: number;
  userName: string;
  userNo: string;
  productCode: string;
}

export interface ModifyStatusParams {
  operationType: number;
  subAccountNo?: string;
  token: string;
  userNo?: string;
  productCode?: string;
}

export interface PaybackListItem {
  accountName: string;
  accountNumber: string;
  billNo: string;
  contactPerson: string;
  contactPhone: string;
  createdAt: string;
  daysOverdue: number;
  overdueAmount: number;
  overdueId: string;
  overdueInterest: number;
  overduePenaltyInterest: number;
  productName: string;
  returnedAmount: number;
  urgePerson: string;
  urgeRecentlyMsg: string;
  urgeRecentlyTime: string;
  urgeState: number;
  productCode?: string;
}

export interface QuotaFlowListItem{
  operationTypeDesc: string;
  operationAmount: string;
  subAfterCreditAmount: string;
  subAfterUsedAmount: string;
  subAfterCurrentAmount: string;
  createdAt: string;
}
