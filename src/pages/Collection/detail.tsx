/* eslint-disable react-hooks/exhaustive-deps */
/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 14:11:46
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-27 16:22:58
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/detail.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import HeaderTab from '@/components/HeaderTab';
import { PRODUCT_CLASSIFICATION_CODE } from '@/enums';
import { desensitizationBankAndIdCard, getUuid, isExternalNetwork } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useLocation, useModel, useRequest, useSearchParams } from '@umijs/max';
import type { TabsProps } from 'antd';
import { message, Tabs } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { BasicInfo, CollectDetail, CollectInfo, DetailPager } from './components';
import CollectDetailSmallLoan from './components/CollectDetailSmallLoan';
import RefundCarInfo from './components/RefundCarInfo';
import './index.less';
import {
  fetchDriverID,
  fetchDriverWalletInfo,
  getOverdueCaseDetail,
  getOverdueDetail,
  getTotalTenDaysAmount,
} from './service';

const CollectionDetail = () => {
  const pagerRef: any = useRef(null);
  const { setHelpStr } = useModel('caller');
  const location = useLocation();
  const [search] = useSearchParams();
  const [overdueCaseNo, setOverdueCaseNo] = useState<string>(
    (search.get('overdueCaseNo') as string) || '',
  );

  const [orderNoList, setOrderNoList] = useState([]);
  const { data, run, loading } = useRequest(
    async (caseNo: string) => {
      const res = await getOverdueCaseDetail(caseNo || overdueCaseNo);
      res?.contactPhoneBOList?.forEach((item: any) => {
        // Date.now() performance.now() // 可能一样
        item.rowKeyId = getUuid();
      });
      if (isExternalNetwork()) {
        // 外网手机号需要脱敏
        const { bankCode, certificateCode } = res;
        res.bankCode = desensitizationBankAndIdCard(bankCode);
        res.certificateCode = desensitizationBankAndIdCard(certificateCode);
      }

      return { data: res };
    },
    {
      onError: (res) => {
        if (res?.message?.indexOf('逾期案件催收人与当前登录用户不一致') > -1) {
          message.destroy();
          pagerRef?.current?.runNextOrLeft();
        }
      },
    },
  );

  const { data: dataDetail, run: run1 } = useRequest(async (caseNo: string) => {
    const res = await getOverdueDetail(caseNo || overdueCaseNo);
    if (isExternalNetwork()) {
      // 外网手机号需要脱敏
      const { bankCode, certificateCode } = res.data;
      res.data.bankCode = desensitizationBankAndIdCard(bankCode);
      res.data.certificateCode = desensitizationBankAndIdCard(certificateCode);
    }
    return res;
  });
  const { curList, curItemIndex } = (location?.state as any) || {
    curItemIndex: null,
    curList: [],
  };
  useEffect(() => {
    setHelpStr(
      JSON.stringify({
        scene: 'BUSINESS_COLLECTION_CALL',
        productCode: data?.productCode,
        productName: data?.productName,
      }),
    );
  }, [data]);
  useEffect(() => {
    const tempArr = dataDetail?.map(
      ({ orderNo, totalOverdueAmount }: { orderNo: string; totalOverdueAmount: string }) => {
        return {
          label: orderNo,
          value: orderNo,
          totalOverdueAmount,
        };
      },
    );
    setOrderNoList(tempArr);
  }, [dataDetail]);

  const scrollToAnchor = (anchorName: string) => {
    if (anchorName) {
      const anchorElement = document.getElementById(anchorName);
      if (anchorElement) {
        anchorElement.scrollIntoView();
      }
    }
  };

  const [driverWalletInfo, setDriverWalletInfo] = useState<any>({});
  const [customerType, setCustomerType] = useState('');
  const [driverId, setDriverId] = useState('');
  const getDriverExtraInfo = async (caseNo: string) => {
    const driverInfo =
      (await fetchDriverID(caseNo)
        .then((res) => {
          if (res?.success && res?.data) {
            return res?.data;
          }
          return {};
        })
        .catch(() => {})) || {};
    if (driverInfo && driverInfo?.customerType) {
      setCustomerType(driverInfo?.customerType);
    }
    console.log('fetchDriverID', driverInfo);
    let info = {};
    if (driverInfo && driverInfo?.driverId) {
      setDriverId(driverInfo?.driverId);
      info = (await fetchDriverWalletInfo({
        ...driverInfo,
        overdueCaseNo,
      })
        .then((res) => {
          if (res?.success && res?.data) {
            return res?.data;
          }
          return { driverId: driverInfo.driverId };
        })
        .catch(() => {})) || { driverId: driverInfo.driverId };
      console.log('fetchDriverWalletInfo', info);

      // 近十天流水总金额
      const totalTenDaysAmountInfo =
        (await getTotalTenDaysAmount({
          driverId: driverInfo.driverId,
          overdueCaseNo,
        })
          .then((res) => {
            if (res?.success && res?.data) {
              return res?.data;
            }
            return {};
          })
          .catch(() => {})) || {};
      console.log('getTotalTenDaysAmount', totalTenDaysAmountInfo);
      setDriverWalletInfo({
        ...info,
        totalTenDaysAmount: totalTenDaysAmountInfo?.totalTenDaysAmount,
      });
    }
  };
  useEffect(() => {
    if (
      overdueCaseNo &&
      data?.productSecondaryCode &&
      ['0301', '0201'].includes(data?.productSecondaryCode)
    ) {
      getDriverExtraInfo(overdueCaseNo).catch(() => {});
    } else {
      setDriverWalletInfo({});
    }
  }, [overdueCaseNo, data?.productSecondaryCode]);

  //小贷是额外一套ui，有循环额度
  const isSmallLoanDom = () => {
    if (data?.productClassicCode === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN) {
      return <CollectDetailSmallLoan data={dataDetail} />;
    }
    return (
      <CollectDetail
        data={{ ...dataDetail?.[0], repurchaseStatus: data.repurchaseStatus }}
        billType={data?.billType}
      />
    );
  };

  const renderBaseInfo = () => {
    return data ? (
      <BasicInfo
        data={data}
        refresh={run}
        overdueCaseNo={overdueCaseNo}
        customerType={customerType}
        productCode={dataDetail?.[0]?.productCode}
        isError={data?.error}
        dataDetail={dataDetail}
        userNo={data?.userNo}
        driverWalletInfo={driverWalletInfo}
      />
    ) : (
      '暂无数据'
    );
  };

  const renderCollectInfo = () => {
    return data && dataDetail?.[0] ? (
      <CollectInfo
        data={data}
        orderNoList={orderNoList}
        dataDetail={dataDetail?.[0]}
        refresh={async (type?: string) => {
          await run(overdueCaseNo);
          if (type === 'scroll') {
            setTimeout(() => {
              scrollToAnchor('add-contact-scroll');
            }, 300);
          }
        }}
        overdueCaseNo={overdueCaseNo}
        btnRef={pagerRef}
      />
    ) : (
      '暂无数据'
    );
  };

  const tabsItems: TabsProps['items'] = [
    { label: '基础信息', key: '1', children: renderBaseInfo() },
    { label: '详情', key: '2', children: data ? isSmallLoanDom() : '暂无数据' },
    {
      label: '催收信息',
      key: '3',
      children: renderCollectInfo(),
      forceRender: true,
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer
        loading={loading}
        title={
          data
            ? `${data?.accountName},逾期${data?.totalOverdueAmount}元,${data?.overdueMaxLevel},${data?.overdueLongestDays}天`
            : ''
        }
        extra={
          <>
            <DetailPager
              list={curList}
              onChange={(curOverdueCaseNo, index, overdueCaseNoList) => {
                if (loading) return;
                history.push(
                  {
                    pathname: `/businessMng/postLoanMng/collection-detail`,
                    search: `?overdueCaseNo=${curOverdueCaseNo}`,
                  },
                  {
                    curList: overdueCaseNoList || [],
                    curItemIndex: index,
                  },
                );
                setOverdueCaseNo(curOverdueCaseNo);
                run(curOverdueCaseNo);
                run1(curOverdueCaseNo);
              }}
              ref={pagerRef}
              curItemIndex={curItemIndex}
            />
          </>
        }
      >
        <Tabs
          style={{ background: '#fff', padding: '10px 20px 20px' }}
          items={
            data?.productSecondaryCode === '0201'
              ? [
                  ...tabsItems,
                  {
                    label: '退车信息',
                    key: '4',
                    children: (
                      <RefundCarInfo
                        orderNo={dataDetail?.[0]?.orderNo}
                        productCode={dataDetail?.[0]?.productCode}
                      />
                    ),
                  },
                ]
              : tabsItems
          }
        />
      </PageContainer>
    </>
  );
};
// export default () => (
//   <>
//     <HeaderTab />
//     <KeepAlive
//       id={`businessMng/postLoanMng/collection-detail/${history.location.query?.orderNo}`}
//       name={`businessMng/postLoanMng/collection-detail/${history?.location?.query?.overdueCaseNo}`}
//     >
//       <CollectionDetail />
//     </KeepAlive>
//   </>
// );

export default React.memo(CollectionDetail);
