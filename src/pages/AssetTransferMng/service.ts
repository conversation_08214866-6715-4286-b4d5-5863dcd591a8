import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';
import type { AssetTransferListParams, RecordLogParams } from './type';

// 转让资产管理-列表
export function getTransferList(params: AssetTransferListParams) {
  return request('/bizadmin/repayment/transfer/transferOfClaimsList', {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}

export function getTransferDetail(recordNo: string) {
  return request('/bizadmin/repayment/transfer/transferClaimsInfo', {
    method: 'POST',
    data: { recordNo },
    headers: {
      ...bizadminHeader,
    },
  });
}

// 导出转让资产包
export function exportTransferPackage(recordNo: string) {
  return request('/bizadmin/repayment/transfer/exportTransferClaimsOrder', {
    method: 'GET',
    params: { recordNo },
    responseType: 'blob',
    headers: {
      ...bizadminHeader,
    },
  });
}

export function getTransferLog(params: RecordLogParams) {
  return request('/bizadmin/repayment/transfer/transferClaimLogPageList', {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}
