/*
 * @Author: your name
 * @Date: 2021-04-20 17:37:25
 * @FilePath: /lala-finance-biz-web/src/components/ReleaseCom/CommonImageUpload/index.tsx
 * @Date: 2021-04-26 15:40:34
 * @Author: elisa.<PERSON><PERSON>
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-01-03 11:13:42
 * @FilePath: Do not edit
 */

import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl, getBlob, saveAs } from '@/utils/utils'; // @ts-ignore
import { ProFormUploadButton } from '@ant-design/pro-components';
import { message, Modal } from 'antd';
import React, { useState } from 'react';
interface ComImageUploadProps {
  extra?: string;
  extraTrack?: boolean;
  needOcr?: boolean;
  ocrHandler?: (files: any[]) => void;
  label?: string;
  name: string;
  max?: number;
  accept?: string;
  // rulesMessage: string;
  listType?: 'picture' | 'text' | 'picture-card' | undefined;
  rules?: any;
  labelCol?: any;
  size: number;
  icon?: any;
  buttonProps?: any;
  fileListEdit?: [];
  mapFileList: (allFileList: any) => void;
  desPath?: string;
  orderNo?: string;
  multiple?: boolean;
  uploadPath?: string;
  handleExtraChange?: (res: any) => void;
  handlePreviewAction?: (file: any) => void;
  disabled?: boolean;
  isShowDownloadIcon?: boolean;
}
interface previewObj {
  previewVisible: boolean;
  previewTitle: string;
  previewImage: string;
}
const styles = {
  wth: {
    width: '100%',
  },
};
/**
 * @Date: 2021-04-26 15:40:18
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: Do not edit
 * @FilePath: Do not edit
 * @param {*} props
 */
const CommonImageUpload: React.FC<ComImageUploadProps> = (props) => {
  const {
    extra,
    extraTrack = true,
    needOcr = false,
    ocrHandler,
    label,
    name,
    max,
    accept,
    listType,
    rules,
    labelCol,
    size,
    icon,
    buttonProps,
    fileListEdit,
    mapFileList,
    desPath,
    orderNo = '',
    multiple,
    uploadPath, // 新增可选参数上传路径，不传为缺省值接口
    handleExtraChange,
    handlePreviewAction,
    disabled,
    isShowDownloadIcon,
  } = props;
  // console.log(fileListEdit);
  // 本地走到代理 线上拼接域名
  const base_url = getBaseUrl();
  console.log('base_url', base_url);
  // if (/localhost|^172/.test(window.location.hostname)) {
  //   // base_url = 'http://lalafin-loan-dev.huolala.cn:8890';
  //   base_url = 'https://finance-api-stg.lalafin.net';
  // } else {
  //   base_url = getBaseUrl();
  // }
  const [errorTips, handleError] = useState<any>({ error: false, tips: '' });
  const [previewObj, handlePreviewObj] = useState<any>({});
  const [fileListOwn, handleFileList] = useState<[]>(fileListEdit || []);
  // const upload_url = `${base_url}/repayment/oss/common/uploadfile`;
  const upload_url = `${base_url}${uploadPath ? uploadPath : '/repayment/oss/common/uploadfile'}`;
  const beforeUpload = (file: any) => {
    const isLt10M = file.size / 1024 / 1024 < size;
    if (!isLt10M) {
      handleError({ error: true, tips: `有文件超过${size}兆,请检查` });
      // eslint-disable-next-line no-param-reassign
      file.status = 'error';
    }
    const exc = file?.name.substring(file?.name.lastIndexOf('.') + 1);
    if (extraTrack && !extra?.includes(exc)) {
      handleError({ error: true, tips: '格式不正确' });
      // eslint-disable-next-line no-param-reassign
      file.status = 'error';
    }
    return isLt10M && (!extraTrack || (extraTrack && extra?.includes(exc)));
  };
  const getBase64 = (file: any) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  };
  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      // eslint-disable-next-line no-param-reassign
      file.preview = await getBase64(file.originFileObj);
      // file.preview = URL.createObjectURL(file.originFileObj);
    }
    handlePreviewObj({
      previewImage: file.url || file.preview,
      previewVisible: true,
      previewTitle: file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
    });
  };

  const handleChange = (props) => {
    const fileList =
      props?.fileList.map((item: any) => {
        //  业务API响应错误, 0和200为上传成功
        if (item.response && ![0, 200].includes(item.response?.ret)) item.status = 'error';
        return item;
      }) || [];
    //上传数量
    let isSizeError = false;
    //文件个数限制存在  因为多选的时候和max冲突，需要额外判断
    if (max) {
      isSizeError = fileList?.length > max;
    }

    const isError = fileList.some((item) => {
      return item.status === 'error' && item.size / 1024 / 1024 > size;
    });
    // console.log(fileList);
    const onlyError = fileList.some((item) => {
      return item.status === 'error';
    });
    // console.log(onlyError, isError);
    // 上传错误
    // if (onlyError) {
    //   handleError({ error: true, tips: '上传错误' });
    // } else {
    //   handleError({ error: false, tips: '' });
    // }
    // console.log(isError || onlyError);
    // 超过size
    if (isError || onlyError) {
      // 显示10
      handleError({
        error: true,
        tips: isError ? `有文件超过${size}兆,请检查` : '上传错误,请检查',
      });
    } else if (isSizeError) {
      handleError({
        error: true,
        tips: `文件个数最多为${max}个,请检查`,
      });
    } else {
      handleError({ error: false, tips: '' });
    }

    handleFileList(fileList);
    // console.log('fileList', fileList)
    if (needOcr && props?.file?.status === 'done') {
      ocrHandler?.(fileList);
      mapFileList({ [name]: fileList });
    } else {
      mapFileList({ [name]: fileList });
    }

    handleExtraChange?.(fileList);
  };
  // useEffect(() => {
  //   console.log('mapFileList', fileListOwn, name);
  //   mapFileList({ [name]: fileListOwn });
  //   // return () => {
  //   //   cleanup
  //   // }
  // }, [fileListOwn]);
  const headers: any = upload_url.includes('bizadmin')
    ? {
        ...getAuthHeaders(),
        'hll-appid': 'bme-finance-bizadmin-svc',
      }
    : { ...getAuthHeaders() };

  const download = (url: string, filename: string) => {
    message.loading({ content: '下载文件中...', key: 'download_file' });
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
      message.destroy('download_file');
    });
  };

  return (
    <>
      <ProFormUploadButton
        extra={extra}
        label={label}
        name={name}
        max={max}
        listType={listType}
        icon={icon}
        buttonProps={buttonProps}
        // rules={rules}
        // fileList={}
        // value={fileListOwn}
        labelCol={labelCol}
        accept={accept}
        onChange={handleChange}
        rules={(rules || []).concat([
          {
            validator: (_, val) => {
              if (errorTips?.error) {
                return Promise.reject(new Error(errorTips?.tips));
              }
              if (val?.[0]?.status === 'uploading') {
                //  只要是上传过程就校验不通过，不给提交
                return Promise.reject('');
              }
              return Promise.resolve();
            },
          },
        ])}
        // rules={[{ required: true, message: rulesMessage }]}
        action={upload_url}
        fieldProps={{
          fileList: fileListOwn,
          headers,
          name: 'file',
          data: { acl: 'PUBLIC_READ', destPath: desPath || 'USER_ORDER_APPLY_MONEY', orderNo }, // 后端商量默认格式
          beforeUpload,
          onPreview: handlePreviewAction ? handlePreviewAction : handlePreview,
          onDownload: (file: any) => {
            const url = file?.url || file?.response?.data?.netWorkPath;
            if (url && file?.name) {
              download(url, file.name);
            } else {
              message.error('下载参数错误！', file);
            }
          },
          multiple,
          disabled,
          showUploadList: {
            showDownloadIcon: isShowDownloadIcon,
          },
        }}
      />
      <Modal
        open={previewObj?.previewVisible}
        title={previewObj?.previewTitle}
        footer={null}
        onCancel={() => {
          handlePreviewObj({ ...previewObj, previewVisible: false });
        }}
      >
        <img alt="该文件不支持预览" style={styles.wth} src={previewObj?.previewImage} />
      </Modal>
    </>
  );
};
export default CommonImageUpload;
