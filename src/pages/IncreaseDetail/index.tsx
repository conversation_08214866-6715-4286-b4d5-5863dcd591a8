/*
 * @Date: 2023-08-29 17:48:48
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-09-25 17:35:44
 * @FilePath: /lala-finance-biz-web/src/pages/IncreaseDetail/index.tsx
 * @Description:
 */
import HeaderTabs from '@/components/HeaderTab';
import { SECOND_PRODUCT_SOME } from '@/enums';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message, Modal } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useRef } from 'react';
import AddIncreaseDetailModal from './components/AddIncreaseDetailModal';
import type { IncreaseDetailItems } from './data';
import { freezeIncrease, getIncreaseDetailList, unfreezeIncrease } from './service';

const IncreaseDetail: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const columns: ProColumns<IncreaseDetailItems>[] = [
    {
      title: '配置场景',
      dataIndex: 'configScene',
      key: 'configScene',
      render: () => {
        return '大额提额';
      },
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      key: 'productCode',
      valueType: 'select',
      valueEnum: SECOND_PRODUCT_SOME,
    },
    {
      title: '企业ID',
      dataIndex: 'userNo',
      key: 'userNo',
    },
    {
      title: '社会统一信用代码',
      dataIndex: 'orgCode',
      key: 'orgCode',
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
      key: 'enterpriseName',
    },
    // {
    //   title: '创建时间',
    //   dataIndex: 'createdAt',
    //   key: 'createdAt',

    // },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateRange',
      // initialValue: [dayjs().subtract(6, 'month'), dayjs()],
      search: {
        transform: (value: any) => {
          if (typeof value[0] !== 'string') {
            return {
              startCreateTime: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
              endCreateTime: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
            };
          }
          return {
            startCreateTime: `${value[0].split(' ')[0]} 00:00:00`,
            endCreateTime: `${value[1].split(' ')[0]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record?.createdAt;
      },
    },
    {
      title: '配置状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        2: { text: '禁用', status: 'error' },
        1: { text: '开启', status: 'success' },
      },
    },
    {
      title: '创建人',
      dataIndex: 'operationName',
      key: 'operationName',
    },
    {
      title: '操作',
      key: 'option',
      width: 200,
      fixed: 'right',
      valueType: 'option',
      render: (_, row) => {
        let optText = '';
        let func: (id: string) => Promise<any>;
        if (row?.status === 1) {
          optText = '禁用';
          func = freezeIncrease;
        } else {
          optText = '开启';
          func = unfreezeIncrease;
        }
        return (
          <>
            <a
              onClick={() => {
                optimizationModalWrapper(AddIncreaseDetailModal)({
                  currentRow: { ...row, productType: row?.productCode?.substring(0, 2) || '' },
                  showMode: true,
                });
              }}
            >
              查看
            </a>
            <a
              style={{ marginLeft: 5 }}
              onClick={async () => {
                // console.log(row);
                Modal.confirm({
                  title: `你确定${optText}${row?.enterpriseName}?`,
                  icon: <ExclamationCircleOutlined />,
                  // content: 'Some descriptions',
                  okText: '确认',
                  okType: 'danger',
                  cancelText: '取消',
                  centered: true,
                  onOk: async () => {
                    await func(row.configId).then(() => {
                      message.success(`${optText}成功`);
                      actionRef?.current?.reload();
                    });
                  },
                });
              }}
            >
              {optText}
            </a>
          </>
        );
      },
    },
  ];

  return (
    <>
      <HeaderTabs />
      <PageContainer>
        <ProTable<IncreaseDetailItems>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          search={{ labelWidth: 120 }}
          scroll={{ x: 'max-content' }}
          rowKey="configId"
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  optimizationModalWrapper(AddIncreaseDetailModal)({
                    onOk: () => {
                      actionRef?.current?.reload();
                    },
                  });
                }}
              >
                添加
              </Button>,
            ];
          }}
          request={(params) => getIncreaseDetailList(params)}
        />
      </PageContainer>
    </>
  );
};

export default IncreaseDetail;
