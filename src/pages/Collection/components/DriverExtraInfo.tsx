import { DividerTit } from '@/components';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Col, Row, Tooltip } from 'antd';
import React, { useEffect, useRef } from 'react';
import { SIJI_STATUS_MAP } from '../data.d';
import '../index.less';
import { depositList, freezeList, openAP2DriverPage } from '../service';

interface InfoProps {
  driverWalletInfo: any;
  overdueCaseNo: string;
  customerType: string;
  // freezeData?: any;
  // depositData?: any;
}
interface FreezeColumnsType {
  channel: string;
  type: number;
  status: boolean;
  operationAt: string;
}
interface DepositColumnsType {
  createdAt: string;
  type: number;
  amount: string;
  remark: string;
  cause: string;
}

const DriverExtraInfo = (props: InfoProps) => {
  const { driverWalletInfo = {}, overdueCaseNo, customerType } = props;
  const { driverId } = driverWalletInfo;
  const freezeActionRef = useRef<ActionType>();
  const depositActionRef = useRef<ActionType>();
  // 冻结金额列表列
  const freezeColumn: ProColumns<FreezeColumnsType>[] = [
    {
      title: '发起渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 200,
    },
    {
      title: '处置方式',
      dataIndex: 'type',
      key: 'type',
      valueEnum: {
        0: '冻结',
        1: '解冻',
      },
      width: 150,
    },
    {
      title: '处置状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        true: '成功',
        false: '失败',
      },
      width: 150,
    },
    {
      title: '操作时间',
      dataIndex: 'operationAt',
      key: 'operationAt',
      width: 300,
    },
  ];
  // 保证金列表列
  const depositColumn: ProColumns<DepositColumnsType>[] = [
    {
      title: '写入时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 200,
    },
    {
      title: '入账/扣除',
      dataIndex: 'operateType',
      key: 'operateType',
      width: 200,
      valueEnum: {
        1: '增加',
        2: '减少',
        3: '冻结',
        4: '解冻',
        5: '解冻并扣减',
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 200,
    },
    {
      title: '原因',
      dataIndex: 'bizTypeName',
      key: 'bizTypeName',
      width: 200,
    },
  ];
  // const accountStatusMap: any = {
  //   5: '审批中',
  //   10: '正常',
  //   20: '锁定(资金不可出不可入) ',
  //   25: '资金不可出',
  //   30: '不可入',
  // };
  // 缺省值处理为'-'
  const infoWithDefaultValue = new Proxy(driverWalletInfo, {
    get: (target, key) => {
      if (target[key] === undefined || target[key] === null) {
        return '-';
      }
      return target[key];
    },
  });
  useEffect(() => {
    if (driverId) {
      freezeActionRef.current?.reload();
      depositActionRef.current?.reload();
    }
  }, [driverId]);

  return (
    <div>
      <DividerTit title="车辆">
        <div style={{ margin: 20 }}>
          <Row>
            <Col span={8}>
              司机ID：
              <span
                className="link-text"
                onClick={() => openAP2DriverPage(driverWalletInfo?.driverId)}
              >
                {infoWithDefaultValue?.driverId}
              </span>
            </Col>
            <Col span={8}>
              司机状态：{SIJI_STATUS_MAP[infoWithDefaultValue?.driverStatus] || '-'}
            </Col>
            <Col span={8}>行驶证所有人：{infoWithDefaultValue?.driverName || '-'}</Col>
          </Row>
        </div>
      </DividerTit>
      <DividerTit title="钱包">
        <div style={{ margin: 20 }}>
          <Row className="mb20">
            <Col span={8}>钱包余额：{infoWithDefaultValue?.totalAmount}</Col>
            <Col span={8}>可用余额：{infoWithDefaultValue?.avlAmount}</Col>
            <Col span={8}>冻结金额：{infoWithDefaultValue?.freezeAmount}</Col>
          </Row>
          <Row>
            <Col span={8}>
              近十天流水总金额&nbsp;
              <Tooltip placement="top" title="为0说明近10天无接单行为">
                <QuestionCircleOutlined />
              </Tooltip>
              &nbsp;：{infoWithDefaultValue?.totalTenDaysAmount}
            </Col>
            <Col span={8}>提现状态：{infoWithDefaultValue?.subAccountStatus}</Col>
          </Row>
          {/*  */}
          <p className="mt20 fontWBold">冻结记录如下：</p>
          <ProTable<FreezeColumnsType>
            columns={freezeColumn}
            actionRef={freezeActionRef}
            scroll={{ x: 'max-content' }}
            rowKey="id"
            search={false}
            pagination={{ pageSizeOptions: [10, 20, 30], defaultPageSize: 10 }}
            options={false}
            toolBarRender={false}
            request={(params) => freezeList({ ...params, driverId, overdueCaseNo, customerType })}
          />
        </div>
      </DividerTit>
      <DividerTit title="保证金">
        <div style={{ margin: 20 }}>
          <Row>
            <Col className="mb20" span={8}>
              保证金余额：{infoWithDefaultValue?.depositAmount}
            </Col>
          </Row>
          <ProTable<DepositColumnsType>
            columns={depositColumn}
            actionRef={depositActionRef}
            scroll={{ x: 'max-content' }}
            rowKey="id"
            search={false}
            pagination={{ pageSize: 10 }}
            options={false}
            toolBarRender={false}
            request={(params) => depositList({ ...params, driverId, overdueCaseNo, customerType })}
          />
        </div>
      </DividerTit>
    </div>
  );
};

export default React.memo(DriverExtraInfo);
