import {
  ModalForm,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProFormDateTimePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import { But<PERSON>, Card, message } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import { IaddRefundCardRecord, IrefundCarRecord } from '../data';
import { addRefundCarRecord, getRefundCarRecord } from '../service';

type Props = {
  orderNo: string;
  productCode: string;
};
const RefundCarInfo: React.FC<Props> = (props) => {
  const { orderNo, productCode } = props;
  const [refundCarInfo, setRefundCarInfo] = useState<IrefundCarRecord>({} as IrefundCarRecord);
  const [refundCarInfoLoading, setRefundCarInfoLoading] = useState(false);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { userName } = currentUser || {};

  const { remainingPrincipal } = history?.location?.query as any;

  function getRefundCarRecord1() {
    setRefundCarInfoLoading(true);
    getRefundCarRecord(orderNo)
      .then((data) => {
        setRefundCarInfo({
          ...data?.data,
          remainingPrincipal,
        });
        setRefundCarInfoLoading(false);
      })
      .catch(() => setRefundCarInfoLoading(false));
  }

  useEffect(() => {
    getRefundCarRecord1();
  }, [orderNo]);

  const columns: ProDescriptionsItemProps<IaddRefundCardRecord>[] = [
    {
      title: '退车时间',
      key: 'refundTime',
      dataIndex: 'refundTime',
    },
    {
      title: '退车费',
      key: 'refundAmount',
      dataIndex: 'refundAmount',
      render(text) {
        return <>{text || text === 0 ? `${text}元` : '-'}</>;
      },
    },
    {
      title: '担保期数',
      key: 'guaranteePeriod',
      dataIndex: 'guaranteePeriod',
      render(text) {
        return <>{text || text === 0 ? `${text}期` : '-'}</>;
      },
    },
    {
      title: '备注',
      key: 'remark',
      dataIndex: 'remark',
    },
    {
      title: '退车时已还期数',
      key: 'settlePeriods',
      dataIndex: 'settlePeriods',
      render(text) {
        return <>{text || text === 0 ? `${text}期` : '-'}</>;
      },
    },
    {
      title: '退车时剩余本金',
      key: 'remainingPrincipal',
      dataIndex: 'remainingPrincipal',
      render(text) {
        return <>{text || text === 0 ? `${text}元` : '-'}</>;
      },
    },
    {
      title: '担保类型',
      key: 'guaranteeType',
      dataIndex: 'guaranteeType',
    },
    {
      title: '退车原因',
      key: 'refundReason',
      dataIndex: 'refundReason',
    },
    {
      title: '渠道名称',
      key: 'channelName',
      dataIndex: 'channelName',
    },
  ];

  function renderExtra() {
    return (
      !refundCarInfo?.id &&
      !refundCarInfoLoading && (
        <ModalForm<IaddRefundCardRecord>
          layout="horizontal"
          labelCol={{ span: 4 }}
          title="添加催收信息"
          trigger={<Button type="primary">添加退车信息</Button>}
          onFinish={async (values) => {
            try {
              await addRefundCarRecord({
                ...values,
                orderNo,
                productCode,
              });
              message?.success('操作成功');
              getRefundCarRecord1();
              return true;
            } catch (error) {
              message.error('操作失败');
              return false;
            }
          }}
        >
          <div style={{ marginLeft: 60, marginBottom: 20 }}>催收员: {userName}</div>
          <ProFormDateTimePicker
            name="refundTime"
            label="退车时间"
            rules={[{ required: true, message: '必填' }]}
          />
          <ProFormSelect
            rules={[{ required: true }]}
            name="refundReason"
            label="退车原因"
            valueEnum={{
              渠道代运营: '渠道代运营',
              弃车: '弃车',
              协商退车: '协商退车',
              违约收车: '违约收车',
            }}
          />
          <ProFormDigit
            label="退车费"
            rules={[{ required: true }]}
            name="refundAmount"
            fieldProps={{ precision: 2 }}
          />
          <ProFormTextArea label="备注" fieldProps={{ maxLength: 150 }} name="remark" />
        </ModalForm>
      )
    );
  }

  return (
    <Card title="退车详情" extra={renderExtra()} loading={refundCarInfoLoading}>
      {refundCarInfo?.id ? (
        <ProDescriptions dataSource={refundCarInfo} columns={columns} />
      ) : (
        '暂无数据'
      )}
    </Card>
  );
};

export default memo(RefundCarInfo);
