/*
 * @Date: 2024-01-23 16:47:34
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-03-08 15:26:22
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalMng/components/SmallLoanQuotaIncome.tsx
 * @Description:
 */
import { ShowInfo } from '@/components';
import { privateStatusMap } from '@/pages/PersonalIncoming/const';
import { DownOutlined, ExclamationCircleOutlined, UpOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Button, Card, message, Modal } from 'antd';
import React, { useState } from 'react';
import type { QuotaFlowListItem } from '../data';
import { freezeOrUnfreezeQuota, getInComeBaseInfo, getQuota, getQuotaFlow } from '../service';
import './index.less';

const SmallLoanQuotaIncome: React.FC = () => {
  const [activeKeys, setActivityKeys] = useState<string[]>([]);
  const [allHasActiveKeys, setAllHasActivityKeys] = useState<string[]>([]);
  const { userNo, productSecondCode } = history.location.query as {
    userNo: string;
    productSecondCode: string;
  };

  const { data: quotaData, run } = useRequest(() => {
    return getQuota(userNo, productSecondCode);
  });
  const { data: incomeDataList } = useRequest(() => {
    return getInComeBaseInfo(userNo);
  });
  // console.log(incomeDataList);
  const dataMap = {
    totalAmount: '授信额度',
    usedAmount: '已用额度',
    currentAmount: '可用额度',
    status: '额度状态',
  };
  enum ENUM_STATUS {
    UNFREEZE = '1',
    FREEZE1 = '0',
    FREEZE2 = '2',
    FREEZE3 = '3',
    FAIL = '4',
  }
  const statusToLabel = {
    [ENUM_STATUS.UNFREEZE]: '解冻',
    [ENUM_STATUS.FREEZE1]: '冻结',
    [ENUM_STATUS.FREEZE2]: '冻结',
    [ENUM_STATUS.FREEZE3]: '冻结',
    [ENUM_STATUS.FAIL]: '失效',
  };

  const statusToLabelReverseOpt = {
    [ENUM_STATUS.UNFREEZE]: '冻结',
    [ENUM_STATUS.FREEZE1]: '解冻',
    [ENUM_STATUS.FREEZE2]: '解冻',
    [ENUM_STATUS.FREEZE3]: '解冻',
    [ENUM_STATUS.FAIL]: '',
  };
  const [modal, contextHolder] = Modal.useModal();
  const freezeOrUnfreezeQuotaFunc = () => {
    // 状态为失效不可操作
    // if (!(`${quotaData?.data?.status}` === ENUM_STATUS.FAIL)) {
    modal.confirm({
      title: <>请确认是否{statusToLabelReverseOpt[quotaData?.data?.status]}该用户?</>,
      icon: <ExclamationCircleOutlined />,
      // content:,
      okText: '确认',
      cancelText: '取消',
      centered: true,
      onOk: () => {
        const freezeAction = `${quotaData?.data?.status}` === ENUM_STATUS.UNFREEZE;
        freezeOrUnfreezeQuota({
          userNo,
          productSecondCode,
          freezeAction,
        }).then(() => {
          message.success(`${statusToLabelReverseOpt[quotaData?.data?.status]}成功`);
          // load页面
          run();
        });
      },
    });

    // }
  };
  const selfDefine = {
    status: (
      <>
        <span>{statusToLabel[quotaData?.data?.status]}</span>
        {/* 操作按钮 */}
        {!(`${quotaData?.data?.status}` === ENUM_STATUS.FAIL) && (
          <Button onClick={freezeOrUnfreezeQuotaFunc} type="primary" className="opt-btn">
            {statusToLabelReverseOpt[quotaData?.data?.status]}
          </Button>
        )}
      </>
    ),
  };

  //
  const itemDataMap = {
    orderNo: '进件流水号',
    classification: '产品分类',
    secondClassification: '产品名称',
    applyTime: '申请时间',
    status: '进件状态',
  };
  const selfDefineItem = (param: { orderNo: string; status: number | string }) => {
    return {
      orderNo: (
        <>
          {param.orderNo}
          <a
            onClick={() => {
              history.push(`/userMng/personalMng/detail?orderNo=${param.orderNo}`);
            }}
            className="income-detail"
          >
            详情
          </a>
        </>
      ),
      status: privateStatusMap[param.status],
    };
  };

  const expandFunc = (status: number, key: string) => {
    // 31，拒绝（基础前筛）33，拒绝（风控前筛）35拒绝（风控审核）
    if (![31, 33, 35].includes(status)) {
      // setCurIndex(index);
      // !collapse&&actionRef
      const activeKeysTemp = activeKeys;
      if (activeKeysTemp.includes(key)) {
        activeKeysTemp.splice(activeKeysTemp.indexOf(key), 1);
      } else {
        activeKeysTemp.push(key);
      }
      setAllHasActivityKeys([...new Set([...activeKeys, key])]);
      setActivityKeys([...activeKeysTemp]);
    }
    // 表格请求数据
  };
  const column: ProColumns<QuotaFlowListItem>[] = [
    {
      title: '操作类型',
      key: 'operationTypeDesc',
      dataIndex: 'operationTypeDesc',
    },
    {
      title: '操作额度',
      key: 'operationAmount',
      dataIndex: 'operationAmount',
    },
    {
      title: '操作后授信额度',
      key: 'subAfterCreditAmount',
      dataIndex: 'subAfterCreditAmount',
    },
    {
      title: '操作后已用额度',
      key: 'subAfterUsedAmount',
      dataIndex: 'subAfterUsedAmount',
    },
    {
      title: '操作后剩余额度',
      key: 'subAfterCurrentAmount',
      dataIndex: 'subAfterCurrentAmount',
    },
    {
      title: '更新时间',
      key: 'createdAt',
      dataIndex: 'createdAt',
    },
  ];

  //
  return (
    <div>
      <Card title="进件与额度" className="info-quota">
        <ShowInfo noCard data={quotaData?.data} infoMap={dataMap} selfDefine={selfDefine} />
        {incomeDataList?.data?.map((item: { status: number; orderNo: string }) => {
          return (
            <div key={item?.orderNo}>
              <div className="info-quota-wrap">
                <ShowInfo
                  noCard
                  data={item}
                  infoMap={itemDataMap}
                  selfDefineFunc={selfDefineItem(item)}
                />
                <div
                  className="collapse"
                  onClick={() => {
                    //
                    expandFunc(item.status, item?.orderNo);
                  }}
                >
                  <>
                    {activeKeys.includes(item?.orderNo) ? (
                      <>
                        <span>折叠</span>
                        <DownOutlined style={{ color: '#1677ff' }} />
                      </>
                    ) : (
                      <>
                        <span className="color-opt">展开</span>
                        <UpOutlined style={{ color: '#1677ff' }} />
                      </>
                    )}
                  </>
                </div>
              </div>
              {/* 只要打开过都缓存 ,没点开的就请求数据 */}
              {allHasActiveKeys.includes(item?.orderNo) && (
                <div
                  className="table-wrap"
                  // none没有销毁dom,可以做到缓存曾经打开过得，再次点击不用额外请求数据
                  style={{
                    display: activeKeys.includes(item?.orderNo) ? 'block' : 'none',
                  }}
                >
                  <ProTable
                    columns={column}
                    search={false}
                    // pagination={false}
                    toolBarRender={false}
                    request={(params) =>
                      getQuotaFlow({
                        activateOrderNo: item?.orderNo,
                        ...params,
                      } as {
                        activateOrderNo: string;
                        pageSize: number;
                        current: number;
                      })
                    }
                  />
                </div>
              )}
            </div>
          );
        })}
      </Card>
      {contextHolder}
    </div>
  );
};

export default SmallLoanQuotaIncome;
