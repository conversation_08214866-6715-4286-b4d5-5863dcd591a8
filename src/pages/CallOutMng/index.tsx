import Audio from '@/components/Audio';
import HeaderTab from '@/components/HeaderTab/index';
import { desensitizationPhone, isExternalNetwork } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Popover } from 'antd';
import React from 'react';
import { reportLog } from '../Collection/services';
import type { ICallAction, ICallDetail } from '../Overdue/data';
import { getCallerAudioRecord } from '../Overdue/service';
import { getCallerBusinessType, getCallerList, getCallerResultType } from './service';

const CallOutList: React.FC<any> = () => {
  // state

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const columns: ProColumns<ICallDetail>[] = [
    {
      title: '业务身份',
      dataIndex: 'productCode',
      valueType: 'select',
      request: getCallerBusinessType,
      hideInTable: true,
    },
    {
      title: '通话记录ID',
      dataIndex: 'callRecord',
    },
    {
      title: '催收案件编号',
      dataIndex: 'accNo',
    },
    {
      title: '业务',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '催收人',
      dataIndex: 'ucenterName',
      initialValue: isExternalNetwork() ? currentUser?.userName : undefined,
      fieldProps: {
        disabled: isExternalNetwork(),
      },
    },
    {
      title: '拨打号码',
      dataIndex: 'dialNumber',
      render(_, record) {
        return isExternalNetwork() ? (
          <>
            {desensitizationPhone(record?.dialNumber)}
            <Popover content={_} trigger="click">
              <a
                onClick={async () => {
                  // 发送查看日志
                  if (record?.dialNumber) {
                    await reportLog(record?.dialNumber);
                  }
                }}
              >
                查看
              </a>
            </Popover>
          </>
        ) : (
          <>{record?.dialNumber}</>
        );
      },
    },
    {
      title: '拨打时间',
      dataIndex: 'callDateDetail',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '通话时间',
      key: 'callTimeDetail',
      search: false,
      render: (_, record) => {
        return (
          <>
            <p>拨打时间：{record.dialTime}</p>
            <p>接通时间：{record.turnOnTime}</p>
            <p>通话结束时间：{record.callEndTime}</p>
          </>
        );
      },
    },
    {
      title: '通话时长（秒）',
      dataIndex: 'talkTimeSecond',
      search: false,
    },
    {
      title: '通话状态',
      dataIndex: 'status',
      valueType: 'select',
      request: getCallerResultType,
    },
    {
      title: '通话录音',
      key: 'option',
      valueType: 'option',
      render: (_, record) => {
        return (
          <Audio
            controls
            downloadShow
            downloadHandle={() => getCallerAudioRecord(record.callRecord)}
          />
        );
      },
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<ICallDetail>
          rowKey="id"
          scroll={{ x: 'max-content' }}
          search={{
            labelWidth: 100,
          }}
          request={async (callerListParams: any) => {
            // 有时候  首次渲染 初始值 初始值 无法携带上
            // 会有初始值 催收员 外网情况下
            if (isExternalNetwork()) {
              if (!callerListParams?.ucenterName) {
                callerListParams.ucenterName = currentUser?.userName;
              }
            }

            const { callDateDetail, ...rest } = callerListParams;
            const callDateParam = callDateDetail
              ? {
                  dialStartTime: `${callDateDetail[0]} 00:00:00`,
                  dialEndTime: `${callDateDetail[1]} 23:59:59`,
                }
              : null;
            const params = {
              ...callDateParam,
              scene: 'BUSINESS',
              ...rest,
            };
            const data = await getCallerList(params as ICallAction);
            return {
              data: data.data,
              success: true,
              // 不传会使用 data 的长度，如果是分页一定要传
              total: data.total,
            };
          }}
          columns={columns}
        />
      </PageContainer>
    </>
  );
};

export default CallOutList;
