/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2021-11-04 18:10:21
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
import { request } from '@umijs/max';
import type { ProductListParams } from './data';

/**
 * 产品管理列表
 */
export async function queryProduct(params?: ProductListParams) {
  //  /loan/product/list
  return request('/bizadmin/product/list', {
    params,
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
    ifTrimParams: true,
  });
}
/**
 * 产品管理详情
 */
export async function queryProductDetail(productCode?: string) {
  // return request(`/loan/product/detail/${productCode}`);
  return request(`/bizadmin/product/detail/${productCode}`, {
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
  });
}

/**
 * 获取产品默认配置
 */
export async function queryDefaultDetail(code?: string) {
  // /loan/product/defaultConfig/${code}
  return request(`/bizadmin/product/defaultConfig/${code}`, {
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
  });
}
// export async function queryProductBase(productCode?: string) {
//   return request(`/loan/product/detail/${productCode}`);
// }

// 产品管理导出
export async function productExport(params: ProductListParams) {
  // /loan/product/list/excel
  return request('/bizadmin/product/list/excel', {
    params,
    responseType: 'blob',
    getResponse: true,
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
    ifTrimParams: true,
  });
}

/**
 * 新增产品
 */
export async function addProduct(data: any) {
  // /bizadmin/product /loan/product
  return request('/bizadmin/product', {
    method: 'POST',
    data,
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
  });
}

export async function updateStatus(data: { id: number; status: number; productCode: string }) {
  // /loan/product/updateStatus
  return request('/bizadmin/product/updateStatus', {
    method: 'PUT',
    data,
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
  });
}

/**
 * 删除草稿
 */
export async function delProductFromDraft(id: number) {
  // bizadmin/product/${productId} /loan/product/${id}
  return request(`/bizadmin/product/${id}`, {
    method: 'DELETE',
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
  });
}

/**
 * 更新融租的费用配置表的提前结清违约金
 * 只有提前结清违约金可以改其他保持原有逻辑不变
 * 去掉原有loan接口该用admin
 */
export async function updateFeeConfig(data: { id: number; proportionalNumber: number }) {
  return request('/bizadmin/product/leaseUpdateLiquidatedProportionNumber', {
    method: 'PUT',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
