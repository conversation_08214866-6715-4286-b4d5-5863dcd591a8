/*
 * @Author: your name
 * @Date: 2020-11-23 16:54:48
 * @LastEditTime: 2024-09-26 15:19:27
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/data.d.ts
 */

export interface OrderListItem {
  applyAmount: number;
  createdAt: string;
  id: number;
  interestRate: string;
  loanTerm: string;
  orderNo: string;
  orderStatus: number;
  productName: string;
  repayType: string;
  secondTypeCode: string;
  userName: string;
  userNo: string;
}

export interface OrderListParams {
  applyTimeEnd?: string;
  applyTimeStart?: string;
  current?: number;
  idNo?: string;
  orderNo?: string;
  orderStatus?: number;
  pageSize?: number;
  phone?: string;
  userNo?: string;
}
export interface OrderListPagination {
  total: number;
  pageSize: number;
  current: number;
}

export interface OrderListData {
  list: OrderListItem[];
  pagination: Partial<OrderListPagination>;
}

export interface LeaseFeeItem {
  deductAmount: number;
  deductDesc: string;
  feeStatus: number;
  feeType: number;
  id: number;
  orderNo: string;
  payAmount: number;
  payDate: string;
  payType: number;
  payee: string;
  payer: string;
  returnFlag: number;
  userNo: string;
}

export interface RepayItem {
  amountDue: number;
  cost: number;
  interest: number;
  principal: number;
  repayTime: string;
  termDetail: string;
  children?: RepayItem[];
}

export interface ShouldRepayItem {
  termDetail?: string;
  status: number;
  repayTime?: string;
  shouldAmountDue: number;
  shouldPrincipal: number;
  shouldInterest: number;
  overdueInterest: number;
  otherCost: number;
  repayPlanNo: string;
  remainingAmountDue: number;
  term?: number;
}

export interface BindCarParams {
  carUniqueCode?: string;
  engineCode?: string;
  orderNo?: string;
  licenseCode?: number;
  remark?: string;
  totalPrice?: string;
}

export interface DeliveryCarParams {
  carDeliveryReceipt: [];
  groupPhoto: [];
  orderNo: string;
  remark?: string;
  userNo: string;
}

export interface BindCarInfo {
  carType: number;
  carUniqueCode: string;
  channelId?: string;
  licenseCode: string;
  engineCode: string;
  bindCarAuditLogs: [];
  bindCarStatus: number;
  carDeliveryInfo: {
    auditLogs: [];
    auditStatus: number;
    carDeliveryReceipt: [];
    groupPhoto: [];
    remark: string;
  };
  // remark: string;
  bindRemark: string;
  // carInfo:{
  //   remark: string;
  //   carUniqueCode: string;
  //   licenseCode: string;
  //   engineCode: string;
  // }
}

export interface OrderInfoParams {
  orderNo?: string;
}

export interface OrderInfo {
  orderAmount: string;
  orderChannel: string;
  orderNo: string;
  orderTime: string;
  productName: string;
  status: number;
  userName: string;
  userNo: string;
}

export interface ListRspList {
  amountDue: number;
  cost: number;
  interest: number;
  principal: number;
  repayTime: string;
}

export interface RepayInfo {
  listRspList: ListRspList[];
  repayMode: number;
  repayTerm: number;
}

export interface RepayRegistItem {
  actualRepaymentAmount: number;
  cost: number;
  interest: number;
  penaltyInterest: number;
  principal: number;
  recordingNo: string;
  repayTime: string;
  repayType: string;
}

export interface ProductNameList {
  code: string;
  desc: string;
}

export interface DebtItem {
  dealTime: string;
  loadFromName: string;
  loadToName: string;
  loanAmount: number;
  startingTime: string;
  status: number;
  conversionNo: string;
}

export interface RegistList {
  applyName?: string;
  createdAt?: string;
  remark?: string;
  remitAmount?: string;
  remitType?: string;
  status: number;
  repayPlayNo: string;
  attach?: { netWorkPath: string; name: string }[];
}

export interface OffLineParams {
  actualRepayAmount?: number; //实际还款金额
  actualRepayRole?: number; //实际还款方枚举
  applyUserId?: string; //提交人
  compensateOrgName?: string; //代偿机构名称
  compensateType?: number; //代偿方式
  costDetail?: { costType: number; id: number; remissionAmount: number; remissionTerms: [] }[]; //减免金额明细
  freezeAmount?: number; //还款中金额
  isAllowJumpTerm?: boolean; //是否支持跳期还款
  isFullAmount?: boolean; //是否足额还款
  isRemission?: boolean; //是否需要减免
  orderNo?: string; //订单编号
  productCode?: string; //产品code
  repayDate?: string; //还款日期
  repayPlayNos?: []; //还款计划编号
  totalRemissionAmount?: number; //总减免金额
  totalRepayAmount?: number; //应还总金额
  userName?: string; //用户名称
  userNo?: string; //用户编号
  attach?: { filePath: string; name: string }[]; //附件
  repayBankNo?: string; //  银行卡号
  thirdFlowId?: string; //  三方流水号
  repayChannel?: string; //  还款渠道
}

export interface RepayItem {
  termDetail?: string;
  status: number;
  repayTime?: string;
  shouldAmountDue?: number;
  shouldPrincipal?: number;
  shouldInterest?: number;
  overdueInterest?: number;
  otherCost?: number;
  repayPlanNo?: string;
}

export interface RepayEditItem {
  id: number;
  costType: number;
  remissionPeriod: number[];
}

export interface IcontractItem {
  orderNo: string;
  contractName: string;
  contractNo: string;
  ossPath: string;
  body?: ReadableStream;
}
// export  interface FeeItem = { costType: string; remissionAmount: number }[];
// 导出合同
export interface IexportContractRes {
  [key: string]: IcontractItem[];
}
