import { LICENSE_TYPES, LICENSE_TYPES_MAP } from '@/enums';
import { Radio } from 'antd';
import React, { useEffect, useImperativeHandle, useState } from 'react';

type LicenseTabsComProps = {
  defaultVisible?: boolean;
  tabsRef: any;
  onChange: (activeKey: React.Key) => void;
  onReset?: () => void;
};
const LicenseTabsCom = ({
  tabsRef,
  onChange,
  onReset,
  defaultVisible = false,
}: LicenseTabsComProps) => {
  const [activeKey, setActiveKey] = useState<React.Key>(LICENSE_TYPES.AFFILIATE); //  上牌类型tab，个户或者挂靠
  const [visible, setVisible] = useState(defaultVisible);

  useImperativeHandle(tabsRef, () => {
    return {
      visible,
      setVisible: (visible: boolean) => {
        setVisible(visible);
        if (!visible) setActiveKey(LICENSE_TYPES.AFFILIATE);
      },
    };
  });

  useEffect(() => {
    if (!visible) {
      onReset?.();
    }
  }, [visible]);

  // 选中或者取消
  const cancelOrSelect = (e, type?: string) => {
    const value = e?.target?.value === activeKey ? '' : e.target.value?.toString(); // 相等就清空，不等就赋值
    if (type === 'cancel' && e?.target?.value !== activeKey) return; //  只有value是空值时才取消
    setActiveKey(value);
    onChange(value);
  };

  return (
    <>
      <div>
        {visible && (
          <Radio.Group value={activeKey} onChange={cancelOrSelect}>
            <Radio.Button
              value={LICENSE_TYPES.AFFILIATE}
              onClick={(e) => cancelOrSelect(e, 'cancel')}
            >
              {LICENSE_TYPES_MAP[LICENSE_TYPES.AFFILIATE]}
            </Radio.Button>
            <Radio.Button
              value={LICENSE_TYPES.CUSTOMER}
              onClick={(e) => cancelOrSelect(e, 'cancel')}
            >
              {LICENSE_TYPES_MAP[LICENSE_TYPES.CUSTOMER]}
            </Radio.Button>
          </Radio.Group>
        )}
      </div>
    </>
  );
};

export default LicenseTabsCom;
