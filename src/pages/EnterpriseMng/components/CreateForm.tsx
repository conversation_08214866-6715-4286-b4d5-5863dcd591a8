import { Modal } from 'antd';
import React from 'react';

interface CreateFormProps {
  title: string;
  modalVisible: boolean;
  onCancel: () => void;
}

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { title, modalVisible, onCancel } = props;

  return (
    <Modal
      destroyOnClose
      title={title}
      open={modalVisible}
      onCancel={() => onCancel()}
      width={900}
      footer={null}
    >
      {props.children}
    </Modal>
  );
};
export default CreateForm;
