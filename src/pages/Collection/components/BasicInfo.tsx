/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 14:42:11
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-27 16:33:09
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/BasicInfo.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import errorImg from '@/assets/error-img.png';
import { ShowInfo } from '@/components';
import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { aiBankDetail, incomeDetail } from '@/pages/PersonalIncoming/service';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import {
  desensitizationBankAndIdCard,
  desensitizationPhone,
  isExternalNetwork,
} from '@/utils/utils';
import type { ProColumns } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { Col, Image, Popover, Row, Tabs } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { reportLog } from '../services';
import type { I_baseInfo } from '../types';
import Contact from './Contact';
import DriverExtraInfo from './DriverExtraInfo';
import './index.less';
import UrgePersonRecord from './UrgePersonRecord';
import WithHold from './WithHold';
const { TabPane } = Tabs;

const BasicInfo = ({
  data,
  refresh,
  overdueCaseNo,
  userNo,
  productCode,
  isError,
  dataDetail,
  driverWalletInfo,
  customerType,
}: any) => {
  const access = useAccess();
  // console.log(data);
  const infoMap = {
    // accountName: '姓名',
    // certificateCode: '证件号',
    totalOverdueAmount: '逾期金额',
    registerPhone: '注册手机号',
    // address: '住址',
  };
  //保理
  const infoFactor = {
    accountName: '用户名称',
    certificateCode: '统一社会信用代码/身份证',
    // phoneNumber: '手机号',
    // address: '住址',
    totalOverdueAmount: '逾期金额',
    // phoneNumber: '注册手机号',
  };
  // console.log(dataDetail?.[0]?.totalOverdueAmount);
  const selfDefine = {
    totalOverdueAmount: <>{data?.totalOverdueAmount}元</>,
  };
  const finalInfoMap =
    dataDetail?.[0]?.productCode?.substring(0, 4) === '0303'
      ? infoFactor
      : dataDetail?.[0]?.productCode?.substring(0, 2) ===
        PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING
      ? infoFactor
      : infoMap;

  const urgeStatus = {
    20: '待跟进',
    30: '跟进中',
    40: '催收结案',
    50: '催收撤销',
  };
  const csInfoMap = {
    overdueCaseNo: '催收案件编号',
    urgeUserId: '催收员',
    status: '催收状态',
    createdAt: '创建时间',
  };
  const openUpdateRecord = () => {
    // console.log(JSON.parse(data?.urgeChangeHistory));
    optimizationModalWrapper(UrgePersonRecord)({
      urgeChangeHistory: JSON.parse(data?.urgeChangeHistory) || [],
    });
  };
  const { mapUrgePersonList: mapUserList } = useModel('userList');
  const csSelfDefine = {
    urgeUserId: (
      <>
        {mapUserList[data?.urgeUserId]}
        <a
          style={{ marginLeft: 5 }}
          onClick={() => {
            openUpdateRecord();
          }}
        >
          变更记录
        </a>
      </>
    ),
    status: <>{urgeStatus[data?.status]}</>,
  };

  //根据二级产品分类类型
  const getBaseInfoFunc = async (
    activateOrderNo: string,
    is030101HistoryIncome: boolean,
    productCodeStr: string,
  ) => {
    if (productCodeStr?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING) {
      return;
    }
    if (!activateOrderNo) {
      return;
    }
    //旧小易速贷和融租数据走老得接口，新的走圆易借
    return is030101HistoryIncome ||
      productCodeStr?.substring(0, 4) === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND
      ? incomeDetail({ orderNo: activateOrderNo, overdueCaseNo, access })
      : aiBankDetail({ orderNo: activateOrderNo, overdueCaseNo });
  };

  const { data: baseInfo, run } = useRequest(getBaseInfoFunc, { manual: true });
  // console.log(dataDetail);
  useEffect(() => {
    run(
      data?.activateOrderNo,
      dataDetail?.[0]?.is030101HistoryIncome,
      dataDetail?.[0]?.productCode,
    );
  }, [dataDetail, data]);

  const [faceImgUrl, setFaceImgUrl] = useState('');
  const getFileUrl = (url: string) => {
    setFaceImgUrl(url);
  };

  const FACE_ID = '0503';
  useMemo(() => {
    if (baseInfo && baseInfo?.imagingInfo) {
      const faceItem = baseInfo?.imagingInfo.find((item) => item.fieldCode === FACE_ID);
      if (faceItem) {
        getFileUrl(faceItem.fieldValue);
      }
    }
  }, [baseInfo]);

  const INCOME_BASE_INFO = {
    baseInfo: '个人信息',
    // workInfo: '工作信息',
    // relativeInfo: '联系信息',
    // bankInfo: '银行信息',
    imagingInfo: '影像资料',
    // supplementInfo: '补件资料',
    realNameInfo: '实名信息',
    driverExtraInfo: '车辆&钱包&保证金',
  };
  //将数组排序,如果是实名信息需要排序
  const sortArr = (arr: [], key: string) => {
    if (key === 'realNameInfo') {
      const complarItem = ['NetWorkPath'];
      arr?.sort((a: { fieldCode: string }, b: { fieldCode: string }) => {
        return a.fieldCode.includes(complarItem) - b.fieldCode.includes(complarItem);
      });
    }
    return arr;
  };

  //获取各种格式
  const getFuncDom = (
    key: string,
    item: {
      fieldValue: string;
      fieldDesc: string;
      fieldCode: string;
    },
    index: number,
  ) => {
    let vDom = <></>;

    const renderFieldValueJsx = () => {
      if (!item.fieldValue) return '-';
      if (isExternalNetwork()) {
        if (['phone', '0103', '0104', '0105', '0106', '0124'].includes(item.fieldCode)) {
          // 假如是手机号
          return (
            <>
              {desensitizationPhone(item?.fieldValue)}{' '}
              <Popover content={item.fieldValue} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (item.fieldValue) {
                      await reportLog(item.fieldValue);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          );
        }
        if (['idNo', '0107', '0123'].includes(item.fieldCode)) {
          return desensitizationBankAndIdCard(item.fieldValue);
        }

        if (
          ['idAddress', '0114', '0111', 'eduBackground', 'homeAddressDetail'].includes(
            item.fieldCode,
          )
        ) {
          return '**************';
        }
      }

      return item.fieldValue;
    };
    //表格格式
    const tableTemplate = (
      <>
        {index === 0 ? (
          <>
            <Col className="border-col header" span={6}>
              项
            </Col>
            <Col className="border-col header" span={6}>
              值
            </Col>
            <Col span={12} />
          </>
        ) : null}
        <>
          <Col className="border-col" span={6}>
            {item.fieldDesc}
          </Col>
          <Col className="border-col" span={6}>
            {/* 外网情况下手机号要脱敏 */}

            {renderFieldValueJsx()}
          </Col>
          <Col span={12} />
        </>
      </>
    );
    // 图片格式
    const imageTemplate = (
      <>
        <div className="col_item">
          <Image
            className="personal-img"
            width="100%"
            height={150}
            src={
              isExternalNetwork()
                ? 'https://static.huolala.cn/image/9bd72ae3cdaf2dc24cb4ab4d0264c62945f87470.png'
                : item.fieldCode === FACE_ID
                ? faceImgUrl || errorImg
                : item.fieldValue || errorImg
            }
          />
          <span style={{ fontWeight: '500' }}>{item.fieldDesc}</span>
        </div>
      </>
    );
    switch (key) {
      //表格
      case 'baseInfo':
      case 'workInfo':
      case 'relativeInfo':
      case 'bankInfo':
        vDom = tableTemplate;
        break;
      //图片
      case 'imagingInfo':
      case 'supplementInfo':
        vDom = imageTemplate;
        break;
      //图片+表格
      case 'realNameInfo':
        if (['frontFilePath', 'unFrontFilePath'].includes(item.fieldCode)) {
          return;
        }
        vDom = <>{item.fieldCode.includes('NetWorkPath') ? imageTemplate : tableTemplate}</>;
        break;
      default:
        vDom = <></>;
        break;
    }
    return vDom;
  };

  function getInfoColumns() {
    const columns1: ProColumns<I_baseInfo>[] = [
      {
        title: '逾期金额',
        key: 'totalOverdueAmount',
        dataIndex: 'totalOverdueAmount',
        render(_, record) {
          return <>{record?.totalOverdueAmount}元</>;
        },
      },
      {
        title: '注册手机号',
        key: 'registerPhone',
        dataIndex: 'registerPhone',
        render(_, record) {
          return isExternalNetwork() ? (
            <>
              {desensitizationPhone(record?.registerPhone)}
              <Popover content={_} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (record?.registerPhone) {
                      await reportLog(record?.registerPhone);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          ) : (
            <>{record?.registerPhone}</>
          );
        },
      },
    ];
    const columns2: ProColumns<I_baseInfo>[] = [
      {
        title: '逾期金额',
        key: 'totalOverdueAmount',
        dataIndex: 'totalOverdueAmount',
        render(_, record) {
          return <>{record?.totalOverdueAmount}元</>;
        },
      },
      {
        title: '企业名称',
        key: 'accountName',
        dataIndex: 'accountName',
      },
      {
        title: '统一社会信用代码',
        key: 'certificateCode',
        dataIndex: 'certificateCode',
      },
    ];
    const secondaryClassificationCode = dataDetail?.[0]?.productCode?.substring(0, 4);

    const firstClassificationCode = dataDetail?.[0]?.productCode?.substring(0, 2);

    if (secondaryClassificationCode === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE) {
      // 如果二级分类是 车险分期
      return columns2;
    }
    if (firstClassificationCode === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING) {
      // 如果一级分类是 保理
      return columns2;
    }
    return columns1;
  }
  return (
    <div>
      {isExternalNetwork() ? (
        <ProDescriptions dataSource={data} columns={getInfoColumns()} />
      ) : (
        <ShowInfo infoMap={finalInfoMap} data={data} noCard selfDefine={selfDefine} />
      )}

      {/* 保理不展示 */}
      {dataDetail?.[0]?.productCode?.substring(0, 2) ===
      PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING ? (
        ''
      ) : (
        <Tabs>
          {Object.keys(INCOME_BASE_INFO).map((key) => {
            if (key === 'driverExtraInfo') {
              if (
                driverWalletInfo.driverId === null ||
                driverWalletInfo.driverId === undefined ||
                driverWalletInfo.driverId === ''
              ) {
                return null;
              }
              return (
                <TabPane key={key} tab={INCOME_BASE_INFO[key]}>
                  <DriverExtraInfo
                    driverWalletInfo={driverWalletInfo}
                    overdueCaseNo={overdueCaseNo}
                    customerType={customerType}
                  />
                </TabPane>
              );
            }
            return (
              <>
                {baseInfo?.[key]?.length ? (
                  <TabPane key={key} tab={INCOME_BASE_INFO[key as keyof typeof INCOME_BASE_INFO]}>
                    {baseInfo?.[key]?.length ? (
                      <Row className="personal-list">
                        {
                          <>
                            {/* {sortArr(baseInfo?.[key], key)} */}
                            {sortArr(baseInfo?.[key], key)?.map(
                              (
                                item: {
                                  fieldValue: string;
                                  fieldDesc: string;
                                  fieldCode: string;
                                },
                                index: number,
                              ) => {
                                return getFuncDom(key, item, index);
                              },
                            )}
                          </>
                        }
                      </Row>
                    ) : (
                      '暂无数据'
                    )}
                  </TabPane>
                ) : (
                  ''
                )}
              </>
            );
          })}
        </Tabs>
      )}

      {/* 代扣 */}
      {
        // 车险的企业用户 没有代扣
        data.userType !== 5 && (
          <WithHold
            overDueAmount={Number(dataDetail?.[0]?.totalOverdueAmount)}
            data={data}
            overdueCaseNo={overdueCaseNo}
            dataDetail={dataDetail}
            driverWalletInfo={driverWalletInfo}
            customerType={customerType}
          />
        )
      }

      {/* 联系人 */}
      <Contact
        overdueCaseNo={overdueCaseNo}
        contactPhoneBOList={data?.contactPhoneBOList}
        refresh={refresh}
        data={data}
        productCode={productCode}
        userNo={userNo}
        dataDetail={dataDetail?.[0]}
        isError={isError}
      />
      <ShowInfo infoMap={csInfoMap} data={data} selfDefine={csSelfDefine} noCard />
    </div>
  );
};
export default React.memo(BasicInfo);
