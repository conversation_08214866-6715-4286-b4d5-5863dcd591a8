/*
 * @Date: 2023-08-29 17:26:49
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-09-11 14:25:41
 * @FilePath: /lala-finance-biz-web/src/pages/OperateConfig/components/AddConfigModal.tsx
 * @Description:
 */
import React from 'react';
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { message } from 'antd';
import { addOperate } from '../service';
import { CLASSIFICATION_CODE_LABEL, SCENE, FINACNE_SCENE } from '@/enums';

export type AddConfigModalProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  isFinaceRole: boolean;
};

const AddConfigModal: React.FC<AddConfigModalProps> = ({ visible, close, onOk, isFinaceRole }) => {
  const configOptions = isFinaceRole ? FINACNE_SCENE : SCENE;
  return (
    <>
      <ModalForm
        title="添加运营配置"
        width="400px"
        layout="horizontal"
        visible={visible}
        modalProps={{
          centered: true,
          onCancel: close,
          okText: '提交',
        }}
        onFinish={async (values) => {
          // const mapUploadFile = convertUploadFileList(allFileList, ['attach']);
          addOperate({
            ...values,
          }).then(() => {
            message.success('添加成功');
            close();
            onOk();
          });
          return true;
        }}
      >
        <ProFormSelect
          rules={[{ required: true }]}
          labelCol={{ span: 8 }}
          options={Object.keys(configOptions).map((key) => ({
            value: key,
            label: SCENE[key],
          }))}
          width="sm"
          name="configScene"
          label="配置场景"
        />
        <ProFormSelect
          rules={[{ required: true }]}
          labelCol={{ span: 8 }}
          // request={getUserListEnum}
          options={Object.keys(CLASSIFICATION_CODE_LABEL).map((key) => ({
            value: key,
            label: CLASSIFICATION_CODE_LABEL[key],
          }))}
          width="sm"
          name="productType"
          label="产品一级分类"
          disabled={isFinaceRole}
          initialValue={isFinaceRole ? '01' : undefined}
        />
      </ModalForm>
    </>
  );
};

export default AddConfigModal;
