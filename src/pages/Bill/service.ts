/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2022-01-07 13:50:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
import { request } from '@umijs/max';
import type {
  BillListParams,
  CombineRepayBackParams,
  ManualRepay,
  ModifyBillData,
  ModifyBillLogsData,
} from './data';

export async function getBillList(params: BillListParams) {
  console.log('getBillList', params);
  return request('/repayment/cms/bill', {
    params,
    ifTrimParams: true,
  });
}

export async function getBillDetailInfo(accountNumber: string, billNo: string) {
  return request(`/repayment/cms/bill/${accountNumber}/${billNo}`);
}
export async function getBillListDetail(params: {
  accountNumber: string;
  billNo: string;
  pageSize: number;
  current: number;
}) {
  return request(`/repayment/cms/bill/detail/${params.accountNumber}/${params.billNo}`, {
    method: 'get',
    params: {
      pageSize: params.pageSize,
      current: params.current,
    },
  });
}

export async function billExport(params: BillListParams) {
  return request('/repayment/cms/bill/excel', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}
export async function internalBillExport(params: { accountNumber: string; billNo: string }) {
  return request(
    `/repayment/cms/bill/getBillDetailExport/${params.accountNumber}/${params.billNo}`,
    {
      responseType: 'blob',
      // params,
      getResponse: true,
    },
  );
}

export async function custmerBillExport(params: { accountNumber: string; billNo: string }) {
  return request(`/repayment/cms/bill/getBillPdf/${params.accountNumber}/${params.billNo}`);
}

export async function custmerBillExportExcel(params: { accountNumber: string; billNo: string }) {
  return request(`/repayment/cms/bill/getBillExcel/${params.accountNumber}/${params.billNo}`);
}

export async function manualRepay(data: ManualRepay) {
  return request(`/repayment/repay/backgroundRepay`, {
    method: 'POST',
    data,
  });
}

export async function modifyBillDate(data: ModifyBillData) {
  return request(`/repayment/bill/modifyBillDate`, {
    method: 'post',
    data,
  });
}

//ModifyBillData  ModifyBillLogsData

export async function modifyBillLog(data: ModifyBillLogsData) {
  return request('/repayment/bill/getModifyBillLogs', {
    data,
    method: 'post',
  });
}

// 获取企业用户列表
export async function getBillUserNameList(params: BillListParams) {
  return request('/repayment/cms/bill/getAccountName', {
    params,
  });
}

/**
 * 获取用于提交还款唯一键
 */
export function getRepayApplyId() {
  return request('/repayment/repay/getRepayApplyId', {
    method: 'GET',
  });
}

// 填写还款信息并提交回款信息（催收回款、线下回款）
export async function combineRepayBack(data: CombineRepayBackParams) {
  return request('/repayment/account/repay/initAndCommit', {
    method: 'POST',
    data,
    hideToast: true,
  });
}

// 获取催收还款银行列表
export async function getBankList(): Promise<
  { bankCode: string; bankName: string; imgUrl: string }[]
> {
  return request('/loan/cash/bankcard/bankList', {
    method: 'post',
  });
}
