/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-09-08 15:30:39
 * @modify date 2021-09-08 15:30:39
 * @desc 订单详情（账期、融租、小贷）、放款详情 共用组件： 进件信息
 */

import globalStyle from '@/global.less';
import { getUuid } from '@/utils/utils';
import { request, useRequest } from '@umijs/max';
import { Card, Col, Row, Table } from 'antd';
import React from 'react';
// import { getIncomingInfo, getRepayPlan } from '../service';

// 进件信息
async function getIncomingInfo(orderNo: string) {
  return request(`/quota/order/getOrderInfo/${orderNo}`);
}

// 还款计划
async function getRepayPlan(orderNo: string) {
  return request(`/repayment/cms/bill/repay/plan/${orderNo}`);
}

interface IncomingInfoProps {
  orderNo: string;
}
const columns = [
  {
    title: '应还总额',
    dataIndex: 'amountDue',
    key: 'amountDue',
  },
  {
    title: '应还本金',
    dataIndex: 'principal',
    key: 'principal',
  },
  {
    title: '应还利息',
    dataIndex: 'interest',
    key: 'interest',
  },
  {
    title: '费用',
    dataIndex: 'cost',
    key: 'cost',
  },
  {
    title: '应还日期',
    dataIndex: 'repayTime',
    key: 'repayTime',
    render: (_: any, record: { repayTime: any }) => {
      return record.repayTime ? record.repayTime : '待确定';
    },
  },
];
const IncomingInfo: React.FC<IncomingInfoProps> = (props) => {
  const { data } = useRequest(() => {
    return getIncomingInfo(props.orderNo);
  });
  const { data: otherData } = useRequest(() => {
    return getRepayPlan(props.orderNo);
  });
  const otherDataMap = {
    repayMode: otherData?.repayMode,
    repayTerm: otherData?.repayTerm,
    annualInterestRate: otherData?.annualInterestRate,
  };
  const incomingInfo = {
    orderNo: '订单号',
    userNo: '用户ID',
    userName: '用户名称',
    productName: '申请产品',
    orderChannel: '渠道商户',
    orderAmount: '申请金额',
    repayTerm: '期限',
    repayMode: '还款方式',
    annualInterestRate: '年利率',
    orderTime: '进件时间',
    // status: '进件状态',
  };
  const itemMap = {
    repayMode: {
      1: '一次本息',
    },
    repayTerm: {
      1: '固定还款日',
    },
    status: {
      // 0: '待放款',
      // 10: '已放款',
      // 20: '撤单处理中',
      // 21: '提前结清',
      0: '进件',
      1: '待还款',
      3: '结清',
      4: '逾期',
      5: '逾期结清',
      6: '坏账',
      10: '已放款',
      20: '撤单处理中',
      21: '提前结清',
    },
  };
  return (
    <Card title="进件信息" className={globalStyle.mt30}>
      <Row className={globalStyle.pl20}>
        {Object.keys(incomingInfo).map((item) => {
          let value = '';
          if (data && data[item]) {
            value = (itemMap[item] ? itemMap[item][data[item]] : data[item]) || '';
          } else if (otherData && otherDataMap[item]) {
            value = itemMap[item] ? itemMap[item][otherDataMap[item]] : otherDataMap[item];
          }
          return (
            <Col span={8} key={`${item}a`}>
              <div className={globalStyle.lineHeight40}>
                <span>{incomingInfo[item]}:</span>
                <span className={globalStyle.ml20}>{value}</span>
              </div>
            </Col>
          );
        })}
      </Row>
      <Row className={globalStyle.pl20}>
        <Col span={2}>
          <span className={`${globalStyle.lineHeight40}`}>还款计划：</span>
        </Col>
        <Col>
          <Table
            rowKey={getUuid}
            dataSource={otherData?.listRspList}
            columns={columns}
            pagination={false}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default IncomingInfo;
