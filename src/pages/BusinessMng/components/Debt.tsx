/*
 * @Author: your name
 * @Date: 2021-03-20 15:03:17
 * @LastEditTime: 2021-04-28 11:09:37
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/components/Debt.tsx
 */
import globalStyle from '@/global.less';
import ProTable from '@ant-design/pro-table';
import { Link, useRequest } from '@umijs/max';
import { Card } from 'antd';
import React from 'react';
import type { DebtItem } from '../data';
import { getDebtByOrderId } from '../service';

interface DebtProps {
  orderNo: string;
}
const columns = [
  {
    title: '债转流水号',
    dataIndex: 'conversionNo',
  },
  {
    title: '应收账款金额（元）',
    dataIndex: 'loanAmount',
  },
  {
    title: '出让方',
    dataIndex: 'loadFromName',
  },
  {
    title: '受让方',
    dataIndex: 'loadToName',
  },
  {
    title: '发起时间',
    dataIndex: 'startingTime',
  },
  {
    title: '成交时间',
    dataIndex: 'dealTime',
  },
  {
    title: '状态',
    dataIndex: 'status',
    valueEnum: {
      1: '已发起',
      2: '已受理',
      3: '待放款',
      4: '放款成功',
    },
  },
  {
    title: '操作',
    dataIndex: 'option',
    valueType: 'option',
    fixed: 'right',
    width: 120,
    render: (_: any, record: DebtItem) => (
      <>
        {record?.status ? (
          <Link to={`/businessMng/debt-detail?debtNo=${record?.conversionNo}`}>查看详情</Link>
        ) : (
          '-'
        )}
      </>
    ),
  },
];
const Debt: React.FC<DebtProps> = (props) => {
  const { data } = useRequest(() => {
    return getDebtByOrderId(props.orderNo);
  });

  return (
    <Card title="债权转让" className={globalStyle.mt30}>
      <ProTable<DebtItem>
        rowKey="conversionNo"
        scroll={{ x: 'max-content' }}
        dataSource={data}
        columns={columns}
        pagination={false}
        search={false}
        toolBarRender={false}
      />
    </Card>
  );
};

export default Debt;
