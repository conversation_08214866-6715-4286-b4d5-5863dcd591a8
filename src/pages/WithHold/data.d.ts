/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-07-13 10:04:13
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Incoming/data.d.ts
 */
// 获取企业用户管理列表的请求数据类型
export interface OverdueConfigListParams {
  id: string; //
  productName: string; //
  productFirstName: string; //
  productSecondName: string; //
  userType: string; // 用户类型
  status: string; // 状态
  current?: number; // 当前页
  pageSize?: number; // 页大小
}

// 获取企业用户管理列表的响应数据类型
export interface OverdueConfigListItem {
  id: string; //
  productCode: string; // 产品名称
  productName: string; //
  productFirstName: string; //
  productSecondName: string; //
  userType: string; // 用户类型
  status: string; // 状态
}
