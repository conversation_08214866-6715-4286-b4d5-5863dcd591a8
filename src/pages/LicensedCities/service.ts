/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2021-07-22 11:09:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from 'umi';
import type { LicensedCitiesListParams } from './data';

// 获取用户管理列表数据
export async function getLicenseCityList(params?: LicensedCitiesListParams) {
  return request('/bizadmin/lease/licenseCity/list4Page', {
    method: 'GET',
    params: { ...params },
    headers: bizAdminHeader,
  });
}

// 添加上牌城市
export async function addLicensedCities(cityName?: string) {
  return request('/bizadmin/lease/licenseCity/add', {
    method: 'POST',
    data: { cityName },
    headers: bizAdminHeader,
  });
}

// 修改上牌城市
export async function modifyLicensedCities(data: {
  cityName?: string;
  id?: number;
  status?: number;
}) {
  return request('/bizadmin/lease/licenseCity/modify', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

export async function delLicensedCities(id?: number) {
  return request(`/bizadmin/lease/licenseCity/delete/${id}`, {
    method: 'DELETE',
    headers: bizAdminHeader,
  });
}
// export async function getWitholdConfig(productCode: string) {
//   return request(`/loan/product/detail/${productCode}`);
// }
// getLicenseCityList ,modifyLicensedCities,delLicensedCities,addLicensedCitis
