/*
 * @Author: your name
 * @Date: 2021-01-18 16:33:12
 * @LastEditTime: 2024-01-29 17:54:39
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/components/Contract.tsx
 */
import { downLoadExcel, getBlob, isExternalNetwork, previewAS, saveAs } from '@/utils/utils';
import { Button, message, Table } from 'antd';
import React, { useState } from 'react';
// import { getOssPath } from '@/services/global';
import globalStyle from '@/global.less';
import { fileZips } from '@/services/global';

interface ContractProps {
  orderNo: string;
  dataContract: ContractItems[];
  isXiaoYiSifang: boolean;
  userName?: string;
  productCode?: string;
}
interface ContractItems {
  fileDesc: string;
  filePath: string;
  netWorkPath: string;
}

const Contract: React.FC<ContractProps> = (props) => {
  const { dataContract, orderNo, isXiaoYiSifang, userName } = props;
  const [currentSelRow, setCurrentSelRow] = useState<ContractItems[]>([]);
  const download = (url: string, filename: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
    // });
  };
  const previewPDF = (url: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      previewAS(blob);
    });
    // });
  };

  const columns = [
    {
      title: '合同名称',
      dataIndex: 'fileDesc',
      key: 'fileDesc',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      // width: 140,
      render: (_: React.ReactNode, record: ContractItems) => {
        return (
          <>
            {!isExternalNetwork() && (
              <>
                {isXiaoYiSifang ? (
                  <>
                    <a
                      className={globalStyle.ml10}
                      onClick={() => {
                        // previewPDF(record.filePath);
                        window.open(record.filePath);
                      }}
                    >
                      预览
                    </a>
                  </>
                ) : (
                  <>
                    <a
                      onClick={() => {
                        download(record.netWorkPath, `${record.fileDesc}${userName}${orderNo}.pdf`);
                      }}
                    >
                      下载
                    </a>
                    <a
                      className={globalStyle.ml10}
                      onClick={() => {
                        previewPDF(record.netWorkPath);
                      }}
                    >
                      预览
                    </a>
                  </>
                )}
              </>
            )}
          </>
        );
      },
    },
  ];
  // const arr = dataContract ? [{ ...dataContract }] : [];
  const downZip = () => {
    // console.log(currentSelRow, '-----');
    //账单号+客户名称+合同
    if (currentSelRow?.length < 1) {
      message.warning('请先勾选合同');
      return;
    }
    if (currentSelRow?.length === 1) {
      message.warning('请选择至少两个文件');
      return;
    }
    const postData = {
      orderNo,
      contractFileReq: currentSelRow.map((item) => {
        return { ...item, contractName: item.fileDesc };
      }),
      userName,
      // productCode,
    };
    // console.log(postData);

    fileZips(postData)
      .then((res) => {
        // console.log(res, '---合同-');
        const type =
          currentSelRow?.length === 1 ? undefined : 'application/octet-stream;charset=UTF-8';
        downLoadExcel(res, type);
      })
      .catch(() => {
        // console.log(e);
        // message.warning(e?.message);
      });
  };
  return (
    <>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        {!isExternalNetwork() && !isXiaoYiSifang ? (
          <Button onClick={() => downZip()} type="primary">
            批量下载
          </Button>
        ) : (
          ''
        )}
      </div>
      <Table
        rowKey={(record) => {
          return record.filePath;
        }}
        rowSelection={{
          onChange: (_, rowSelection: ContractItems[]) => {
            setCurrentSelRow(rowSelection);
          },
        }}
        style={{ width: 400 }}
        dataSource={dataContract}
        columns={columns}
        pagination={false}
      />
    </>
  );
};

export default Contract;
