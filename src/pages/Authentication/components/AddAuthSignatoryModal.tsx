import { ModalForm } from '@ant-design/pro-form';
import type { ReactElement } from 'react';
import React, { memo } from 'react';
import AddAuthSignatory from './AddAuthSignatory';

type Props = {
  children: ReactElement;
  reload: () => void;
};
const AddAuthSignatoryModal: React.FC<Props> = (props) => {
  const { reload } = props;
  const onFinish = async () => {
    await new Promise((res) =>
      setTimeout(() => {
        res(2000);
      }, 2000),
    );
    reload();
    return true;
  };
  return (
    <ModalForm
      onFinish={onFinish}
      trigger={props.children}
      width={1200}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
    >
      <AddAuthSignatory />
    </ModalForm>
  );
};
export default memo(AddAuthSignatoryModal);
