/*
 * @Author: your name
 * @Date: 2021-01-18 16:32:16
 * @LastEditTime: 2021-05-07 14:25:06
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/components/FeeRecord.tsx
 */
import globalStyle from '@/global.less';
import { useRequest } from '@umijs/max';
import { Card, Table } from 'antd';
import React from 'react';
import { getFeePlan } from '../service';

interface DebtProps {
  orderNo: string;
}
const columns = [
  {
    title: '费用项',
    dataIndex: 'expenseItem',
  },
  {
    title: '收取节点',
    dataIndex: 'receivingNode',
  },
  {
    title: '收取时间',
    dataIndex: 'receivingTime',
  },
  {
    title: '出资方',
    dataIndex: 'funders',
  },
  {
    title: '收取方',
    dataIndex: 'receiver',
  },
  {
    title: '收取金额',
    dataIndex: 'amount',
  },
  {
    title: '收取方式',
    dataIndex: 'chargeMethod',
  },
  {
    title: '是否返还',
    dataIndex: 'returnNode',
  },
];
const FeeRecord: React.FC<DebtProps> = (props) => {
  const { data } = useRequest(() => {
    return getFeePlan(props.orderNo);
  });

  return (
    <Card title="费用记录" className={globalStyle.mt30}>
      <Table
        rowKey={(record) => {
          return record.amountDue + record.receivingTime;
        }}
        scroll={{ x: 'max-content' }}
        dataSource={data}
        columns={columns}
        pagination={false}
      />
    </Card>
  );
};

export default FeeRecord;
