.audioBox {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: flex-start;
}
.audioBox-button {
  line-height: 100%;
}
.audioBox-button-icon {
  font-size: 20px;
  cursor: pointer;
}
.audioBox-progress {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100px;
  padding: 0 6px;
}
.audioBox-progress-time {
  color: #363636;
  font-size: 10px;
}
.audioBox-progress-bar {
  position: relative;
  height: 6px;
  background-color: darkgray;
  border-radius: 6px;
}
.audioBox-progress-now {
  width: 0;
  height: 100%;
  background-color: #1677ff;
  border-radius: 6px;
  transition: width;
}
.audioBox-download-icon {
  font-size: 18px;
  cursor: pointer;
}
