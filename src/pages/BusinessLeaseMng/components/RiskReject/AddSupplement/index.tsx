/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
// 提交补件
import { pattern } from '@/utils/validate';
import { ProCard, ProFormTextArea } from '@ant-design/pro-components';
import { Form, message, Spin } from 'antd';
import { get, omit } from 'lodash-es';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { getPatchData, submitRiskPatch } from '../../../service';
import { VALIDATE_MAP } from '../../PersonalInfo/consts';
import { contactNames, contactPhones } from './consts';
import DisplayItem from './DisplayItem';
import Upload from './upload';

type AddSupplementProps = {
  orderNo: string;
  isLook: boolean; // 只展示补件记录
  riskOrderNo: string;
  onSubmit: () => void;
};

const AddSupplement = forwardRef((props: AddSupplementProps, ref) => {
  const { orderNo, riskOrderNo, isLook } = props;

  const [form] = Form.useForm();

  const [readOnly, setReadOnly] = useState(false);
  const [infoList, setInfoList] = useState([]);
  const [dataList, setDataList] = useState([]);
  const [moduleId, setModuleId] = useState(0);
  const [rulesObj, setRulesObj] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(false);

  const validatePhoneRepeat = (obj: any, value: string): Promise<any> => {
    const values = form.getFieldsValue();
    let errorMsg = '';
    for (let i = 0; i < contactPhones.length; i++) {
      const code = contactPhones[i];
      const otherValue = values[code];
      if (value && value === otherValue && obj.field !== code) {
        errorMsg = '联系人手机号重复,请更换其他';
        return Promise.reject(new Error(errorMsg));
        break;
      }
    }
    return Promise.resolve();
  };

  const validateNameRepeat = (obj: any, value: string): Promise<any> => {
    const values = form.getFieldsValue();
    let errorMsg = '';

    for (let i = 0; i < contactNames.length; i++) {
      const code = contactNames[i];
      const otherValue = values[code];
      if (value && value === otherValue && obj.field !== code) {
        errorMsg = '联系人姓名重复,请更换其他';
        return Promise.reject(new Error(errorMsg));
        break;
      }
    }
    return Promise.resolve();
  };

  const initRules = (item: any) => {
    const { fieldType = '', fieldDesc } = item.attribute;
    const ruleArr = [];
    // 全都必填
    ruleArr.push({ required: true });

    if (VALIDATE_MAP[fieldType]) {
      ruleArr.push(VALIDATE_MAP[fieldType]);
    }
    const isName = fieldDesc.includes('姓名');
    const isPhone = fieldDesc.includes('手机号');

    // 联系人姓名增加格式校验
    // 联系人姓名统一添加是否重复校验规则
    if (isName) {
      ruleArr.push({
        pattern: pattern.ch,
        message: '不能录入非汉字且不少于2个汉字',
      });
      ruleArr.push({
        validator: validateNameRepeat,
      });
    }
    // 联系人手机号统一添加是否重复校验规则
    if (isPhone) {
      ruleArr.push({
        validator: validatePhoneRepeat,
      });
    }

    return ruleArr;
  };

  const initPatchInfo = () => {
    if (!riskOrderNo) return;
    getPatchData(riskOrderNo).then((res: any) => {
      const { data } = res;
      const dataList = get(data, 'supplementOthers') || [];
      setModuleId(get(data, 'supplementConfigMetaRes.id'));
      const metaRes = get(data, 'supplementConfigMetaRes', {}) || {};
      const allFieldKeys = Object.keys(metaRes)?.filter((item) => item !== 'id');
      const infoList = allFieldKeys?.reduce((pre, cur) => {
        const ret = get(data, `supplementConfigMetaRes.${cur}`) || [];
        return pre.concat(ret);
      }, []);
      // 初始化校验规则
      const rules: Record<string, any> = {};
      infoList.forEach((item) => {
        const ruleArr = initRules(item);
        rules[item.fieldCode] = ruleArr;
      });

      setRulesObj(rules);
      setDataList(dataList);
      setInfoList(infoList);
    });
  };

  useEffect(() => {
    if (riskOrderNo && !isLook) {
      initPatchInfo();
    }
  }, [riskOrderNo]);

  const onUploadChange = (fileList: any[], id: number) => {
    let tempList = fileList.map((file) => {
      return file.response?.data?.netWorkPath;
    });
    // 只存入成功上传的图片
    tempList = tempList.filter((url) => url);
    form.setFieldValue(id, tempList);
  };

  const handleSubmit = async () => {
    // 先校验一下表单
    await form.validateFields();
    if (loading) return;
    setLoading(true);
    const values = form.getFieldsValue();
    // 写入图片列表
    const dataListTmp = dataList?.map((item) => {
      const tmp = omit(item, ['supplementValue']);
      const { id } = tmp;
      const fileList = values[id];
      tmp.supplementValueList = fileList;
      return tmp;
    });

    const infoListTmp = infoList?.map((item) => {
      const key = item.fieldCode;
      return {
        moduleId: item.attribute.moduleId,
        fieldCode: key,
        fieldDesc: item.attribute.fieldDesc,
        fieldValue: values[key],
        attribute: JSON.stringify(item.attribute),
      };
    });

    const params = {
      orderNo,
      riskEnterFieldReqList: infoListTmp,
      riskSupplementReqList: dataListTmp,
      riskEnterFieldListId: moduleId,
      remark: values.remark,
    };
    console.log('补充资料参数', params);
    submitRiskPatch(params)
      .then(() => {
        message.success('提交成功！');
        setReadOnly(true);
        props.onSubmit();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: () => {
      handleSubmit();
    },
  }));

  return (
    <Spin spinning={loading}>
      {!isLook && (
        <ProCard
          title="补充资料"
          bordered
          headerBordered
          collapsible
          bodyStyle={{ paddingTop: 30 }}
          style={{ marginBottom: 10 }}
        >
          <Form
            form={form}
            style={{ width: 800, margin: 'auto' }}
            // wrapperCol={{ span: 16 }}
            // labelCol={{ span: 8 }}
            labelWrap={true}
            layout="vertical"
          >
            {infoList?.map((item) => {
              return (
                <div key={item.fieldCode}>
                  <DisplayItem
                    label={item.attribute.fieldDesc}
                    name={item.fieldCode}
                    rules={rulesObj[item.fieldCode]}
                    renderType={item.attribute.componentType}
                    options={item.attribute.fieldOptions}
                    disabled={readOnly}
                    defaultValue={item.attribute.value}
                  />
                </div>
              );
            })}
            {dataList?.map((item, index) => {
              return (
                <Form.Item
                  name={item.id}
                  label={item.supplementDesc}
                  rules={[{ required: true }]}

                  // wrapperCol={{ span: 24 }}
                  // labelCol={{ span: 6 }}
                >
                  <Upload
                    disabled={readOnly}
                    MAX={20}
                    onUploadChange={(fileList) => {
                      onUploadChange(fileList, item.id);
                    }}
                  />
                </Form.Item>
              );
            })}
            <ProFormTextArea
              label="备注"
              name="remark"
              disabled={readOnly}
              fieldProps={{ maxLength: 300, showCount: true }}
            />
          </Form>
        </ProCard>
      )}
    </Spin>
  );
});

export default AddSupplement;
