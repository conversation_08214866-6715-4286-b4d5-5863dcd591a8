import { useState } from 'react';
import type { IdefaultConfig, IfeeConfigItem, IrepaymentDTO } from '../type';

// 这个model 只在车险中用到，在以前老的融租是没有用到的
const InsuranceProduct = () => {
  const [detailData, setDetailData] = useState<IdefaultConfig>({} as any); // 详情数据
  const [costConfigDTO, setCostConfigDTO] = useState<IfeeConfigItem[]>([]); // 费用配置
  const [defaultConfig, setDefaultConfig] = useState<IdefaultConfig>({} as any); // 默认的配置
  const [payBackData, setPayBackData] = useState<IrepaymentDTO>({} as any);

  function clearModeState() {
    setDetailData({} as any);
    setCostConfigDTO([]);
    setDefaultConfig({} as any);
  }

  return {
    detailData,
    setDetailData,
    costConfigDTO,
    setCostConfigDTO,
    defaultConfig,
    setDefaultConfig,
    payBackData,
    setPayBackData,
    clearModeState,
  };
};
export default InsuranceProduct;
