/*
 * @Author: your name
 * @Date: 2021-01-11 15:43:07
 * @LastEditTime: 2021-04-13 15:11:13
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Incoming/components/ProductData.tsx
 */
/*
 * @Author: your name
 * @Date: 2021-01-11 15:43:07
 * @LastEditTime: 2021-01-14 17:03:20
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Incoming/components/ProductData.tsx
 */
import { useRequest } from '@umijs/max';
import React, { useRef, useState } from 'react';
// import ProCard from '@ant-design/pro-card';
import globalStyle from '@/global.less';
import { ModalForm } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { But<PERSON>, Card, Col, message, Row } from 'antd';
import type { ProductDataListItem } from '../data';
import { getProductData, modifyAccountStatus } from '../service';

interface ProductDataProps {
  userNo: string;
}

const ProductData: React.FC<ProductDataProps> = (props) => {
  const [currentRow, setCurrentRow] = useState<ProductDataListItem>();

  const [statusModalVisible, handleModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const columns: ProColumns<ProductDataListItem>[] = [
    {
      title: '产品一级分类',
      dataIndex: 'productFirstTypeName',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeName',
    },
    {
      title: '评级',
      dataIndex: 'riskLevel',
    },
    {
      title: '授信额度',
      dataIndex: 'creditAmount',
    },
    {
      title: '已用额度',
      dataIndex: 'usedAmount',
    },
    {
      title: '冻结额度',
      dataIndex: 'freezeAmount',
    },
    {
      title: '剩余可用额度',
      dataIndex: 'currAmount',
    },
    {
      title: '逾期金额',
      dataIndex: 'overdueAmount',
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        1: '激活',
        0: '冻结',
      },
      key: 'status',
    },
    {
      title: '操作',
      width: 80,
      fixed: 'right',
      render: (_: any, record: ProductDataListItem) => (
        <>
          <Button
            type="link"
            onClick={() => {
              setCurrentRow(record);
              handleModalVisible(true);
            }}
          >
            {record.status === 1 ? '冻结' : '激活'}
          </Button>
        </>
      ),
    },
  ];

  const { data: otherData, loading: ProductDataLoading, run } = useRequest(() => {
    return getProductData(props.userNo);
  });

  const handleConfirm = () => {
    const params = {
      operationType: currentRow?.status ? 0 : 1,
      subAccountNo: currentRow?.subAccountNo,
      userNo: currentRow?.userNo,
      token: `${new Date().getTime()}${Math.floor(Math.random() * 1000)}`, // 模仿
      productCode: currentRow?.productCode,
    };
    modifyAccountStatus(params).then(() => {
      message.success('操作成功');
      run();
      return true;
    });
    return false;
  };

  return (
    <Card title="产品数据" bordered loading={ProductDataLoading} className={globalStyle.mt20}>
      <Row>
        <Col span={24} key="repay">
          <div style={{ lineHeight: '40px' }}>
            <ProTable<ProductDataListItem>
              actionRef={actionRef}
              rowKey="subAccountNo"
              scroll={{ x: 'max-content' }}
              // dataSource={[{ cost: 500, amountDue: 2 }]}
              dataSource={otherData}
              columns={columns}
              toolBarRender={false}
              search={false}
              pagination={false}
            />
          </div>
        </Col>
      </Row>
      <ModalForm
        title="提示"
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
        }}
        visible={statusModalVisible}
        onVisibleChange={handleModalVisible}
        onFinish={async () => {
          const success: boolean = await handleConfirm();
          if (success) {
            handleModalVisible(false);
          }
          return true;
        }}
      >
        <div>
          是否确认{currentRow?.status === 1 ? '冻结' : '激活'}
          {currentRow?.userName}
          {currentRow?.productSecondTypeName}？
        </div>
      </ModalForm>
    </Card>
  );
};

export default ProductData;
