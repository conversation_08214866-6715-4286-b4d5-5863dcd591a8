import { Col, Row, Skeleton } from 'antd';
import React from 'react';

// 骨架屏组件 - 完全符合实际布局的加载状态
const UploadStepSkeleton = () => (
  <div style={{ animation: 'fadeIn 0.3s ease-in-out' }}>
    <style>
      {`
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `}
    </style>

    <Row style={{ height: '100%' }}>
      {/* 分隔线 */}
      <div
        style={{ width: '100%', height: '1px', backgroundColor: '#f0f0f0', marginBottom: '16px' }}
      />

      <Col span={2}>
        {/* 左侧标题 */}
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
          <Skeleton.Button active size="small" style={{ width: '60px', height: '18px' }} />
        </div>
      </Col>

      <Col span={22} style={{ height: '100%' }}>
        {/* 右侧描述和单选按钮区域 */}
        <div style={{ marginBottom: '12px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Skeleton.Input active size="small" style={{ width: '200px', height: '16px' }} />
            <div style={{ display: 'flex', gap: '16px', marginLeft: '8px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <Skeleton.Avatar active size={16} shape="square" />
                <Skeleton.Button active size="small" style={{ width: '20px', height: '14px' }} />
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <Skeleton.Avatar active size={16} shape="square" />
                <Skeleton.Button active size="small" style={{ width: '20px', height: '14px' }} />
              </div>
            </div>
          </div>
        </div>

        {/* 文件类型说明 */}
        <Skeleton.Input
          active
          size="small"
          style={{ width: '100%', height: '12px', marginBottom: '8px' }}
        />

        {/* 拖拽上传区域 */}
        <div
          style={{
            marginBottom: '16px',
            border: '2px dashed #d9d9d9',
            backgroundColor: '#fafafa',
            textAlign: 'center',
            padding: '40px 20px',
            borderRadius: '6px',
          }}
        >
          <Skeleton.Avatar active size={48} style={{ marginBottom: '16px' }} />
          <Skeleton.Input active size="small" style={{ width: '180px', height: '16px' }} />
        </div>

        {/* 上传状态统计区域 - 蓝色背景 */}
        {/* <div style={{
            margin: '16px 0',
            padding: '12px',
            backgroundColor: '#e6f7ff',
            borderRadius: '6px',
          }}>
            <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap', alignItems: 'center' }}>
              <Skeleton.Button active size="small" style={{ width: '120px', height: '16px' }} />
              <Skeleton.Button active size="small" style={{ width: '80px', height: '16px' }} />
              <Skeleton.Button active size="small" style={{ width: '100px', height: '16px' }} />
            </div>
          </div> */}

        {/* 文件列表区域 */}
        <Row style={{ display: 'flex', justifyContent: 'center', width: '100%', gap: 120 }}>
          <Col span={12} style={{ flex: 1, minWidth: 200, overflow: 'hidden' }}>
            {[1, 2].map((item) => (
              <div
                key={item}
                style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}
              >
                {/* 文件状态图标 */}
                <div style={{ marginRight: 8, minWidth: 16 }}>
                  <Skeleton.Avatar active size={16} />
                </div>

                <div style={{ flex: 1, minWidth: 0 }}>
                  {/* 文件名 */}
                  <Skeleton.Input
                    active
                    size="small"
                    style={{ width: '85%', height: '14px', marginBottom: '4px' }}
                  />

                  {/* 状态标签 */}
                  <div style={{ marginTop: 4 }}>
                    <Skeleton.Button
                      active
                      size="small"
                      style={{ width: '50px', height: '20px', borderRadius: '4px' }}
                    />
                  </div>
                </div>

                {/* 操作按钮 */}
                <div style={{ marginLeft: 8, display: 'flex', gap: 8 }}>
                  <Skeleton.Button active size="small" style={{ width: '30px', height: '12px' }} />
                </div>
              </div>
            ))}
          </Col>
          <Col span={12} style={{ flex: 1, minWidth: 200, overflow: 'hidden' }}>
            {[1, 2].map((item) => (
              <div
                key={item}
                style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}
              >
                {/* 文件状态图标 */}
                <div style={{ marginRight: 8, minWidth: 16 }}>
                  <Skeleton.Avatar active size={16} />
                </div>

                <div style={{ flex: 1, minWidth: 0 }}>
                  {/* 文件名 */}
                  <Skeleton.Input
                    active
                    size="small"
                    style={{ width: '85%', height: '14px', marginBottom: '4px' }}
                  />

                  {/* 状态标签 */}
                  <div style={{ marginTop: 4 }}>
                    <Skeleton.Button
                      active
                      size="small"
                      style={{ width: '50px', height: '20px', borderRadius: '4px' }}
                    />
                  </div>
                </div>

                {/* 操作按钮 */}
                <div style={{ marginLeft: 8, display: 'flex', gap: 8 }}>
                  <Skeleton.Button active size="small" style={{ width: '30px', height: '12px' }} />
                </div>
              </div>
            ))}
          </Col>
        </Row>
      </Col>
    </Row>
  </div>
);

export default React.memo(UploadStepSkeleton);
