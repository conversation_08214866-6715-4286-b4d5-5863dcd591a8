import ImagePreview from '@/components/ImagePreview';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { CloseCircleOutlined } from '@ant-design/icons';
import { Button, Upload } from 'antd';
import type { UploadProps } from 'antd/es/upload';
import React, { memo } from 'react';
import './index.less';

const UploadFileFormItem = (props: UploadProps) => {
  const base_url = getBaseUrl();
  const upload_url = `${base_url}/base/oss/common/uploadfile`;
  // const upload_url = `${base_url}/repayment/oss/common/uploadfile`;

  const uploadProps: UploadProps = {
    name: 'file',
    accept: '.jpg,.jpeg,.png,.bmp,.pdf',
    action: upload_url,
    headers: { ...getAuthHeaders() },
    maxCount: 1,
    data: { acl: 'PUBLIC_READ', destPath: 'biz/remit/file' }, // 后端商量默认格式
  };

  const onChange = (info: any) => {
    const { fileList } = info;
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      const values = fileList.map((item) => {
        const {
          name,
          response: { data = {} },
        } = item;
        const { filePath, netWorkPath } = data;
        return {
          name,
          filePath,
          netWorkPath,
        };
      });
      props?.onChange?.(values?.[0]);
    } else if (info.file.status === 'error') {
      //
    }
  };
  return (
    <>
      {props?.value?.netWorkPath && (
        <div className="upload-attach-warp">
          <ImagePreview url={props?.value?.netWorkPath}>
            <div className="upload-file-form-item">
              <span style={{ color: '#1677ff' }}>{props?.value?.name}</span>
              <CloseCircleOutlined
                style={{ color: 'red', marginLeft: 10 }}
                onClick={() => {
                  props?.onChange?.(null);
                }}
              />
            </div>
          </ImagePreview>
        </div>
      )}

      {!props?.value?.netWorkPath && (
        <Upload onChange={onChange} {...uploadProps}>
          <Button type="link">点击此处上传</Button>
        </Upload>
      )}
    </>
  );
};

export default memo(UploadFileFormItem);
