import { useAccess } from '@umijs/max';
import { But<PERSON>, Col, Divider, Modal, Row } from 'antd';
import React, { useRef, useState } from 'react';
import {
  DetailInfo,
  ListItem,
  RefundStatusEnum,
  refundStatusEnumMap,
  RefundTypeEnum,
  refundTypeEnumMap,
} from '../const';
import '../index.less';
import BasicInfo from './BasicInfo';
import UploadPannel from './UploadPannel';

interface ModalProps {
  closeModal: (refresh?: boolean) => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  detailInfo: DetailInfo;
}

const Page: React.FC<ModalProps> = ({ visible, closeModal, currentUser, record, detailInfo }) => {
  // const { orderNo, bizNo } = record;
  const uploadRef = useRef<any>();
  const access = useAccess();
  const [uploadLoading, setUploadLoading] = useState(false);
  const handleUploadSubmit = async () => {
    setUploadLoading(true);
    const res: any = await uploadRef?.current?.submitUpload().catch(() => {});
    console.log(res);
    setUploadLoading(false);
  };
  //
  return (
    <>
      <Modal
        width={960}
        destroyOnClose
        title={`退保详情`}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            {access?.hasAccess('upload_file_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" onClick={handleUploadSubmit} loading={uploadLoading} ghost>
                保存附件
              </Button>
            )}
          </>
        }
      >
        {/*  */}
        <BasicInfo detailInfo={detailInfo} orderStatus={detailInfo?.orderStatus} />
        {/*  */}
        <UploadPannel
          ref={uploadRef}
          detailInfo={detailInfo}
          defaultFileList={detailInfo?.fileInfoAttach ? detailInfo?.fileInfoAttach : []}
        />
        {/*  */}
        <>
          <Row gutter={[16, 12]}>
            <Col span={24}>
              <span className="detail-label">退款分配：</span>
              <span>
                {refundTypeEnumMap[detailInfo?.refundAllocation as RefundTypeEnum] || '-'}
              </span>
            </Col>
          </Row>
          {/* 表头 */}
          <Row gutter={[0, 0]} className="mt-14">
            <Col span={2} className="gray-text">
              收款方
            </Col>
            <Col span={3} className="gray-text">
              回款金额
            </Col>
            <Col span={5} className="gray-text">
              收款账户名
            </Col>
            <Col span={5} className="gray-text">
              收款账号
            </Col>
            <Col span={4} className="gray-text">
              收款银行
            </Col>
            <Col span={5} className="gray-text">
              退款结果
            </Col>
          </Row>
          {[
            RefundTypeEnum.ALLOCATION_1,
            RefundTypeEnum.ALLOCATION_2,
            RefundTypeEnum.ALLOCATION_3,
            RefundTypeEnum.ALLOCATION_5,
          ].includes(detailInfo?.refundAllocation) && (
            <>
              {/* 分割线 */}
              <div className="row-divider-wrap">
                <Divider className="row-divider" />
              </div>
              {/* 易人行 */}
              <Row gutter={[0, 0]} className="table-row-wrap">
                <Col span={2}>{detailInfo?.yiRenXingBankInfo?.userName || '-'}</Col>
                <Col span={3}>
                  {detailInfo.yirenxingRefundAmount ? `￥${detailInfo.yirenxingRefundAmount}` : '-'}
                </Col>
                <Col span={5}>{detailInfo?.yiRenXingBankInfo?.accountName || '-'}</Col>
                <Col span={5}>{detailInfo?.yiRenXingBankInfo?.bankCardNo || '-'}</Col>
                <Col span={4}>{detailInfo?.yiRenXingBankInfo?.subBranchName || '-'}</Col>
                <Col span={5}>
                  <div>
                    {refundStatusEnumMap[
                      detailInfo?.yiRenXingBankInfo?.refundStatus as RefundStatusEnum
                    ] || '-'}
                  </div>
                  <div>
                    {detailInfo?.yiRenXingBankInfo?.refundStatus ===
                      RefundStatusEnum.REFUND_SUCCESS && (
                      <>{detailInfo?.yirenxingRefundTime || '-'}</>
                    )}
                  </div>
                </Col>
              </Row>
            </>
          )}
          {[
            RefundTypeEnum.ALLOCATION_1,
            RefundTypeEnum.ALLOCATION_2,
            RefundTypeEnum.ALLOCATION_4,
            RefundTypeEnum.ALLOCATION_5,
          ].includes(detailInfo?.refundAllocation) && (
            <>
              {/* 分割线 */}
              <div className="row-divider-wrap">
                <Divider className="row-divider" />
              </div>
              {/* 客户 */}
              <Row gutter={[0, 0]} className="table-row-wrap">
                <Col span={2}>客户</Col>
                <Col span={3}>
                  {detailInfo?.userRefundAmount ? `￥${detailInfo.userRefundAmount}` : '-'}
                </Col>
                <Col span={5}>{detailInfo?.userBankInfo?.accountName || '-'}</Col>
                <Col span={5}>{detailInfo?.userBankInfo?.bankCardNo || '-'}</Col>
                <Col span={4}>{detailInfo?.userBankInfo?.subBranchName || '-'}</Col>
                <Col span={5}>
                  <div>
                    {refundStatusEnumMap[
                      detailInfo?.userBankInfo?.refundStatus as RefundStatusEnum
                    ] || '-'}
                  </div>
                  <div>
                    {detailInfo?.userBankInfo?.refundStatus === RefundStatusEnum.REFUND_SUCCESS && (
                      <>{detailInfo?.userRefundTime || '-'}</>
                    )}
                  </div>
                </Col>
              </Row>
            </>
          )}
          {/* 分割线 */}
          <div className="row-divider-wrap">
            <Divider className="row-divider" />
          </div>
        </>
      </Modal>
    </>
  );
};
export default Page;
