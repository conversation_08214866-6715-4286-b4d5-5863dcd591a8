/*
 * @Date: 2023-08-31 18:21:07
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-10-30 17:15:59
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseIncoming/components/HeavyQuotaPromoteDetail.tsx
 * @Description:
 */
import { DividerTit, ShowInfo } from '@/components';
import HeaderTabs from '@/components/HeaderTab';
import {
  CLASSIFICATION_CODE_LABEL,
  needsTypeMap,
  SECONDARY_CLASSIFICATION_MAP,
  SECOND_PRODUCT_SOME,
} from '@/enums';
import globalStyle from '@/global.less';
import { ExclamationCircleOutlined, RollbackOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import { history } from '@umijs/max';
import { Empty, message, Modal } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useEffect, useState } from 'react';
import { getPromoteList, revokeOrder } from '../service';

const statusMap = {
  0: '拒绝',
  1: '通过',
  2: '驳回',
};

const HeavyQuotaPromoteDetail: React.FC = () => {
  const orderNo = history?.location?.query?.orderNo;
  const [data, setData] = useState<Record<string, any>>();
  const [extendInfo, setExtendInfo] = useState<Record<string, any>>();
  const [riskData, setRiskData] = useState<Record<string, string>[]>([]);
  const [revokeVisible, handleRevoke] = useState(false);

  const baseInfoMap = {
    riskOrderNo: '进件流水号',
    userNo: '用户ID',
    userName: '企业名称',
    productFirstCode: '产品一级分类',
    productSecondTypeCode: '产品二级分类',
    channelMerchant: '渠道商户',
    certNo: '统一社会信用代码',
    applyAmount: '申请额度（元）',
    applyTime: '提额时间',
    userTag: '客户标签',
    organization: '上报机构',
    ownerTitle: '归属抬头',
    rejectReason: '拒绝原因',
    suggest: '初审意见',
  };

  const baseInfoDefine = {
    productFirstCode: CLASSIFICATION_CODE_LABEL[data?.productFirstCode],
    productSecondTypeCode: SECOND_PRODUCT_SOME[data?.productSecondTypeCode],
  };

  const promoteApply = {
    monthFlow: '每月用车流水（元）', //分
    applyAmount: '需求额度（元）', //
    mainBusiness: '主营业务',
    goodsType: '货物类型',
    carScene: '用车场景', //转
    staffCount: '员工人数',
    billDay: '账期天数',
    needsType: '需求类型', //需求类型
  };

  const promoteApplySelfDefine = {
    enterpriseFlow: data?.enterpriseFlow
      ? Number(new BigNumber(data?.enterpriseFlow).div(100))
      : '-',
    applyAmount: data?.applyAmount || '-',
    productSecondTypeCode: SECONDARY_CLASSIFICATION_MAP[data?.productSecondTypeCode],
    monthFlow: extendInfo?.monthFlow ? Number(new BigNumber(extendInfo?.monthFlow).div(100)) : '-',
    // carScene: extendInfo?.carScene ? carSceneMap[extendInfo?.carScene] : '-',
    needsType: extendInfo?.needsType
      ? extendInfo?.needsType
          .split(',')
          .map((item) => needsTypeMap[item])
          .join(',')
      : '-',
  };

  const overRunMap = {
    0: '否',
    1: '超限',
  };

  const aboveQuotaMap = {
    0: '否',
    1: '超额',
  };

  // 审查信息
  const aduitInfo = {
    status: '审查结果',
    grade: '审查评级',
    timeLimit: '评级期限上限',
    suggestMaxAmount: '评级审查意见(最高额度/万)',
    auditAccountDays: '审查账期天数(自然日)',
    auditAmount: '建议额度(万)',
    suggest: '审查意见',
  };

  // 审批信息
  const promoteResult = {
    status: '审批结果', //需要转
    grade: '审批评级',
    suggestMaxAmount: '评级复核意见(最高额度/万)',
    auditAccountDays: '审批账期天数(自然日)',
    timeLimit: '评级期限上限',
    auditAmount: '审批额度(万)',
    overRun: '是否超限（60日然自）',
    aboveQuota: '是否超额（信用等级额度上限）',
    suggest: '审批意见',
  };

  const finalAudit = {
    status: '终审结果',
    result: '终审情况',
    auditAccountDays: '终审账期天数(自然日)',
    auditAmount: '终审额度(万元)',
    suggest: '终审意见',
  };

  const initPage = () => {
    getPromoteList({ applyNo: orderNo }).then((res) => {
      const dataInfo = res?.data?.[0];
      setData(dataInfo);
      setExtendInfo(JSON.parse(res?.data?.[0]?.extendInfo));
      const riskInfoList = [];
      (dataInfo?.riskInformation || []).forEach((item) => {
        const { riskPeriod } = item || {};
        let { riskInfo } = item || {};
        if (riskInfo) {
          riskInfo = JSON.parse(riskInfo);
        }

        const obj = {
          ...item,
          riskInfo,
        };
        if (riskPeriod) {
          riskInfoList[riskPeriod - 1] = obj;
        }
      });
      setRiskData(riskInfoList);
    });
  };

  useEffect(() => {
    initPage();
  }, [orderNo]);

  const handleRevokeOpt = (form: { remark: string }) => {
    revokeOrder(orderNo, form?.remark)
      .then(() => {
        initPage();
        handleRevoke(false);
        message.success(`撤销成功`);
      })
      .catch((err) => {
        handleRevoke(false);
        Modal.error({
          title: err.message || err.msg,
          width: 400,
          centered: true,
          okText: '好的',
        });
      });
  };

  return (
    <div>
      <HeaderTabs />
      <PageContainer
        extra={
          //  1: '申请单创建成功',2: '已提交用户信息',
          [1, 2].includes(data?.status)
            ? [
                <a
                  key={'handleRevoke'}
                  onClick={() => {
                    handleRevoke(true);
                  }}
                >
                  <RollbackOutlined />
                  撤销订单
                </a>,
              ]
            : []
        }
      >
        <ProCard title="基础信息" headerBordered>
          <ShowInfo noCard infoMap={baseInfoMap} data={data} selfDefine={baseInfoDefine} />
        </ProCard>
        <ShowInfo
          title="提额申请信息"
          infoMap={promoteApply}
          data={extendInfo}
          selfDefine={promoteApplySelfDefine}
        />
        <ProCard title="提额结果风控信息" headerBordered style={{ marginTop: 30 }}>
          <DividerTit title="审查信息" />
          {riskData[0]?.riskInfo ? (
            <ShowInfo
              noCard
              infoMap={aduitInfo}
              data={riskData[0]?.riskInfo}
              selfDefine={{
                status: statusMap[riskData[0]?.riskInfo?.status],
              }}
            />
          ) : (
            <Empty />
          )}
          <DividerTit title="审批信息" />
          {riskData[1]?.riskInfo ? (
            <ShowInfo
              noCard
              infoMap={promoteResult}
              data={riskData[1]?.riskInfo}
              selfDefine={{
                status: statusMap[riskData[1]?.riskInfo?.status],
                overRun: overRunMap[riskData[1]?.riskInfo?.overRun],
                aboveQuota: aboveQuotaMap[riskData[1]?.riskInfo?.aboveQuota],
              }}
            />
          ) : (
            <Empty />
          )}
          <DividerTit title="终审信息" />
          {riskData[2]?.riskInfo ? (
            <ShowInfo
              noCard
              infoMap={finalAudit}
              data={riskData[2]?.riskInfo}
              selfDefine={{
                status: statusMap[riskData[2]?.riskInfo?.status],
              }}
            />
          ) : (
            <Empty />
          )}
        </ProCard>
        <ModalForm
          title="撤销"
          visible={revokeVisible}
          layout="horizontal"
          onFinish={(values: any) => handleRevokeOpt(values)}
          modalProps={{
            centered: true,
            destroyOnClose: true,
            okText: '确认撤销',
            onCancel: () => handleRevoke(false),
          }}
          width="400px"
        >
          <div className={globalStyle.textCenter}>
            <ExclamationCircleOutlined className={globalStyle.iconCss} />
            <span className={`${globalStyle.fontWBold} ${globalStyle.fontS16}`}>
              是否确认撤销该笔订单?
            </span>
            <p className={globalStyle.mt10}>撤销后，订单将无法继续进行，请谨慎操作！</p>
          </div>
          <ProFormText
            width="sm"
            labelCol={{ span: 6 }}
            name="remark"
            rules={[{ required: true }]}
            label="撤销原因"
            placeholder="请输入撤销原因"
          />
        </ModalForm>
      </PageContainer>
    </div>
  );
};

export default HeavyQuotaPromoteDetail;
