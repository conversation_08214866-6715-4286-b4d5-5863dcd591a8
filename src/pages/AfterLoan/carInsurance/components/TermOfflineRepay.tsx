/**
 * 订单纬度 - 线下还款
 */
import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import LoadingButton from '@/components/LoadingButton';
import { getAuthHeaders } from '@/utils/auth';
import { disableFutureDate, getBaseUrl } from '@/utils/utils';
import type { ActionType } from '@ant-design/pro-components';
import {
  EditableProTable,
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';

import type { UploadFile } from 'antd';
import { Col, Descriptions, Form, message, Row, Upload } from 'antd';
import BigNumber from 'bignumber.js';
import type { PropsWithChildren } from 'react';
import React, { memo, useRef, useState } from 'react';
import { useAccess } from 'umi';
import { CarOfflineRepayColumns } from '../columns/CarOfflineRepayColumns';
import { OrderOfflineRepayColumns } from '../columns/OrderOfflineRepayColumns';
import { billInfo, submitBillRepay } from '../services';
import type { IbillInfo, IbillListItem, Idimension } from '../types';
import { IdimensionEnCode } from '../types';

type Props = {
  selectedRows: IbillListItem[];
  dimension: Idimension;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};
const TermOfflineRepay = (props: PropsWithChildren<Props>) => {
  const { selectedRows, dimension, actionRef } = props;
  const [billInfoData, setBillInfoData] = useState<IbillInfo>();
  const [open, setOpen] = useState(false);
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const access = useAccess();
  const [form] = Form.useForm();

  // 计算减免总金额
  function calculateRemissionAmount(remission: any) {
    const {
      remissionPrincipal,
      remissionInterest,
      remissionPenalty,
      remissionAdvanceSettleLiquidatedDamages,
      remissionDelayAmount,
    } = remission;
    const amount = new BigNumber(remissionPrincipal)
      .plus(remissionInterest)
      .plus(remissionPenalty)
      .plus(remissionAdvanceSettleLiquidatedDamages)
      .plus(remissionDelayAmount)
      .toNumber();
    return amount;
  }

  function getAmount() {
    const init = {
      totalPrincipalUnpaid: 0,
      totalInterestUnpaid: 0,
      totalOverduePenaltyUnpaid: 0,
      totalBreach: 0,
      totalLate: 0,
    };
    if (billInfoData?.billRspDTOList.length) {
      return billInfoData?.billRspDTOList?.reduce((pre: any, cur) => {
        const {
          totalPrincipalUnpaid,
          totalInterestUnpaid,
          totalOverduePenaltyUnpaid,
          totalBreach,
          totalLate,
        } = cur;
        const _totalPrincipalUnpaid = new BigNumber(pre.totalPrincipalUnpaid)
          .plus(totalPrincipalUnpaid)
          .toNumber();
        const _totalInterestUnpaid = new BigNumber(pre.totalInterestUnpaid)
          .plus(totalInterestUnpaid)
          .toNumber();
        const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
          .plus(totalOverduePenaltyUnpaid)
          .toNumber();
        const _totalBreach = new BigNumber(pre.totalBreach).plus(totalBreach).toNumber();
        const _totalLate = new BigNumber(pre.totalLate).plus(totalLate).toNumber();
        return {
          totalPrincipalUnpaid: isNaN(_totalPrincipalUnpaid) ? 0 : _totalPrincipalUnpaid,
          totalInterestUnpaid: isNaN(_totalInterestUnpaid) ? 0 : _totalInterestUnpaid,
          totalOverduePenaltyUnpaid: isNaN(_totalOverduePenaltyUnpaid)
            ? 0
            : _totalOverduePenaltyUnpaid,
          totalBreach: isNaN(_totalBreach) ? 0 : _totalBreach,
          totalLate: isNaN(_totalLate) ? 0 : _totalLate,
        };
      }, init);
    } else {
      return init;
    }
  }
  console.log('asdasd', getAmount());
  return (
    <>
      <LoadingButton
        type="primary"
        onClick={async () => {
          try {
            if (!selectedRows?.length) {
              message.error('还未勾选任何数据');
              return;
            }
            fullScreenLoadingRef.current?.open();
            const billNoList = selectedRows.map((item) => item.billNo);
            const data = await billInfo({
              billNoList,
              dimension,
              secondaryClassification: '0303',
            });
            setBillInfoData(data);
            setOpen(true);
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        线下还款
      </LoadingButton>
      <ModalForm
        open={open}
        // open={true}
        form={form}
        title={<div className="modal_tit">线下还款</div>}
        layout="horizontal"
        labelCol={{ span: 3 }}
        modalProps={{
          onCancel: () => {
            setOpen(false);
          },
          destroyOnClose: true,
          // open: open
        }}
        initialValues={{
          remission: [
            {
              id: 1,
              remissionPrincipal: 0,
              remissionInterest: 0,
              remissionPenalty: 0,
              remissionAdvanceSettleLiquidatedDamages: 0,
              remissionDelayAmount: 0,
            },
          ],
        }}
        width={1000}
        onFinish={async (values) => {
          try {
            console.log('values', values);
            // 是否有权限提交
            if (!access.hasAccess('bill_submit_repay_info_postLoanMng_afterLoanList')) {
              message.error('没有权限提交');
              return;
            }

            const { remission, attach, ...rest } = values;
            // const billNoList = billInfoData?.billRspDTOList.map((item) => item.billNo) || [];
            const {
              remissionPrincipal,
              remissionInterest,
              remissionPenalty,
              remissionAdvanceSettleLiquidatedDamages,
              remissionDelayAmount,
            } = remission?.[0];
            const remissionTotalAmount = calculateRemissionAmount(values.remission?.[0]);
            const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              .minus(remissionTotalAmount)
              .toNumber();
            fullScreenLoadingRef.current?.open();
            console.log({
              remissionList: [
                {
                  remissionPrincipal,
                  remissionInterest,
                  remissionPenalty,
                  remissionAdvanceSettleLiquidatedDamages,
                  remissionDelayAmount,
                },
              ],
              attach,
              billInfoList: billInfoData?.billRspDTOList,
              repayAmount: repayTotalAmount,
              dimension,
            });
            await submitBillRepay({
              remissionList: [
                {
                  remissionPrincipal,
                  remissionInterest,
                  remissionPenalty,
                  remissionAdvanceSettleLiquidatedDamages,
                  remissionDelayAmount,
                },
              ],
              attach,
              billInfoList: billInfoData?.billRspDTOList,
              repayAmount: repayTotalAmount,
              dimension,
              ...rest,
            });
            message.success('提交成功');
            setOpen(false);
            actionRef.current?.reload();
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        <div className="block_area">
          <Descriptions bordered column={1} size={'small'}>
            <Descriptions.Item label={<div className="label_width">渠道名称</div>}>
              {billInfoData?.channelName}
            </Descriptions.Item>
            <Descriptions.Item label={<div className="label_width">客户名称</div>}>
              {billInfoData?.accountNameList.join(',')}
            </Descriptions.Item>
          </Descriptions>
        </div>
        <div className="tit">如需要批量减免请在下方输入金额</div>
        <div className="block_area">
          <ProFormDependency name={['remission']}>
            {(values) => {
              const amount = calculateRemissionAmount(values.remission?.[0]);
              return (
                <p>
                  减免总金额: <span style={{ color: 'red' }}>{isNaN(amount) ? '-' : amount}</span>元
                </p>
              );
            }}
          </ProFormDependency>

          {/* <ProForm.Item name="remission" trigger="onValuesChange">

        </ProForm.Item> */}
          <EditableProTable
            name="remission"
            rowKey="id"
            toolBarRender={false}
            columns={[
              {
                title: '减免本金(元)',
                dataIndex: 'remissionPrincipal',
                valueType: 'digit',

                fieldProps: { style: { width: '100%' }, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalPrincipalUnpaid,
                      message: '不能超过未还本金之和',
                    },
                  ],
                },
              },
              {
                title: '减免利息(元)',
                dataIndex: 'remissionInterest',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalInterestUnpaid,
                      message: '不能超过未还利息之和',
                    },
                  ],
                },
              },
              {
                title: '减免罚息(元)',
                dataIndex: 'remissionPenalty',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalOverduePenaltyUnpaid,
                      message: '不能超过未还罚息之和',
                    },
                  ],
                },
              },
              {
                title: '减免违约金(元)',
                dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalBreach,
                      message: '不能超过未还违约金之和',
                    },
                  ],
                },
              },
              {
                title: '减免滞纳金(元)',
                dataIndex: 'remissionDelayAmount',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalLate,
                      message: '不能超过未还滞纳金之和',
                    },
                  ],
                },
              },
            ]}
            recordCreatorProps={false}
            editable={{
              type: 'multiple',
              editableKeys: [1],
              // onChange: setEditableRowKeys,
              actionRender: () => {
                return [];
              },
            }}
          />
        </div>

        <div className="tit">请确认账单信息无误后提交:</div>
        <ProFormDependency name={['remission']}>
          {(values) => {
            const remissionTotalAmount = calculateRemissionAmount(values.remission?.[0]);
            const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              .minus(remissionTotalAmount)
              .toNumber();
            return (
              <p>
                还款总金额:
                <span style={{ color: 'red' }}>
                  {isNaN(repayTotalAmount) ? '-' : repayTotalAmount}
                </span>
                元
              </p>
            );
          }}
        </ProFormDependency>
        <ProTable
          search={false}
          options={false}
          columns={
            dimension === IdimensionEnCode.ORDER_TERM_BILL
              ? OrderOfflineRepayColumns
              : CarOfflineRepayColumns
          }
          dataSource={billInfoData?.billRspDTOList as any}
          scroll={{ x: 'max-content' }}
          pagination={false}
        />
        <div className="tit">付款信息</div>
        <div className="block_area">
          <Row>
            <Col span={12}>
              <ProFormUploadButton
                labelCol={{ span: 6 }}
                action={`${getBaseUrl()}/repayment/oss/common/uploadfile`}
                rules={[{ required: true }]}
                fieldProps={{
                  headers: getAuthHeaders(),
                  name: 'file',
                  data: { acl: 'PUBLIC_READ', destPath: 'EP_AUTH_INFO' }, // 后端商量默认格式
                  multiple: true,
                  beforeUpload: (file) => {
                    const isLt10M = file.size / 1024 / 1024 < 10;
                    if (!isLt10M) {
                      message.error(`${file.name}超过了10M`);
                      return false || Upload.LIST_IGNORE;
                    }
                    return true;
                  },
                }}
                onChange={({ file }) => {
                  if (
                    file.status === 'done' &&
                    file.response?.ret === 200 &&
                    file?.response?.data?.netWorkPath
                  ) {
                    file.url = file?.response?.data?.netWorkPath;
                    (file as any).filePath = file?.response?.data?.filePath;
                  }
                }}
                transform={(values: UploadFile[]) => {
                  const attach = values
                    ?.filter((item) => item.url)
                    .map((file) => {
                      const { url, name, uid } = file;
                      const { filePath } = file as any;
                      return {
                        url,
                        name,
                        uid,
                        filePath,
                      };
                    });
                  return { attach };
                }}
                max={10}
                label="还款凭证"
                name="attach"
                accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
              />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <ProFormSelect
                labelCol={{ span: 6 }}
                name="remitType"
                label="支付方式"
                initialValue={2}
                width="sm"
                rules={[{ required: true }]}
                options={[
                  { label: '普通对公打款', value: 2 },
                  { label: '线下二维码还款', value: 3 },
                ]}
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                labelCol={{ span: 6 }}
                name="bankAmount"
                label="打款金额"
                fieldProps={{ min: 0, precision: 2 }}
                placeholder="请输入打款金额"
                width="sm"
                rules={[
                  { required: true },
                  // {
                  //   validator: (_, val) => {
                  //     // 有坑，同时出现很多个error,待优化
                  //     if (Number(val) <= 0) {
                  //       // callBack('回款金额不能大于逾期金额');
                  //       return Promise.reject(new Error('线下汇款金额不能为0'));
                  //     }
                  //     if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  //       // callBack();
                  //       return Promise.reject(new Error('请输入数字'));
                  //     }

                  //     // callBack();
                  //     return Promise.resolve();
                  //   },
                  // },
                ]}
              />
            </Col>
            <Col span={12}>
              <ProFormDatePicker
                name="bankAmountDate"
                labelCol={{ span: 6 }}
                label="打款日期"
                fieldProps={{ disabledDate: disableFutureDate }}
                rules={[{ required: true }]}
                placeholder="请输入打款日期"
                width="sm"
                // fieldProps={{
                //   className: 'offline-repay-date',
                //   getPopupContainer: () => {
                //     return document.getElementsByClassName('offline-repay-date')[0];
                //   },
                // }}
              />
            </Col>
            <Col span={12}>
              <ProFormText name="bankNo" labelCol={{ span: 6 }} label="付款账号" width="sm" />
            </Col>
            <Col span={12}>
              <ProFormText name="bankAccount" labelCol={{ span: 6 }} label="付款户名" width="sm" />{' '}
            </Col>
            <Col span={12}>
              <ProFormText name="bankName" labelCol={{ span: 6 }} label="付款银行" width="sm" />
            </Col>
            <Col span={12}>
              <ProFormText
                name="subBranchBank"
                labelCol={{ span: 6 }}
                label="付款银行支行"
                width="sm"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="repaySerialNo"
                labelCol={{ span: 6 }}
                label="银行到账流水号"
                width="sm"
              />
            </Col>
          </Row>
        </div>
      </ModalForm>
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};

export default memo(TermOfflineRepay);
