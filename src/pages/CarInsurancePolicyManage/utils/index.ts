import dayjs from 'dayjs';
import { CHANNEL_LEVEL } from '../enum';
import { getItem } from './generateSubMenu';

// 根据ocr结果返回状态
const checkReturnStatus = (ocrResult: any) => {
  if (!ocrResult?.checkFlag) {
    return 'error';
  }
  if (ocrResult?.checkFlag && ocrResult?.exceptionMsgList?.length > 0) {
    return 'warning';
  }
  return 'success';
};

// 创建一个类型辅助工具
export const carInsurancePolicyBaseInfoKeys = <T extends Record<string, any>>(obj: T) => {
  return Object.keys(obj) as (keyof T)[];
};

// 判断车险渠道用户是否为一级/二级渠道
export const isFirstAndSecondChannel = (channelLevel: CHANNEL_LEVEL) => {
  return (
    channelLevel === CHANNEL_LEVEL.FIRST_CHANNEL ||
    channelLevel === CHANNEL_LEVEL.SECOND_CHANNEL ||
    false
  );
};

/**
 * 判断第一次上传/重新上传识别ocr结果是否直接通过，此flag写在本地state中，直到所有保单提交审核后，再清空
 * 该flag用于：
 * 1. 如果ocr识别结果直接通过，则直接跳过ocr识别，在提交审核时将后端约定的event变更为AUTO_APPROVE_POLICY
 * 2. 后端针对直接通过的ocr不会返回任何exceptionMsgList，所以需要补充一条【校验通过】的msg
 * 3. 针对初始化直接通过校验的ocr，其表单结果可直接禁用无法编辑
 * @param ocrInfo
 * @returns
 */
export const isPassInit = (ocrInfo: any) => {
  return (
    ocrInfo?.checkFlag && (!ocrInfo?.exceptionMsgList || ocrInfo?.exceptionMsgList?.length === 0)
  );
};

export const diffFormValues = (newValues: any, oldValues: any) => {
  const hasDifference = Object.keys(newValues).some((key) => {
    let newValue = newValues[key];
    let oldValue = oldValues[key];

    newValue = dayjs.isDayjs(newValue) ? newValue.format('YYYY-MM-DD HH:mm:ss') : newValue;
    oldValue = dayjs.isDayjs(oldValue) ? oldValue.format('YYYY-MM-DD HH:mm:ss') : oldValue;
    if (typeof newValue === 'number' && typeof oldValue === 'string') {
      return newValue.toString() !== oldValue;
    }
    if (typeof newValue === 'string' && typeof oldValue === 'number') {
      return newValue !== oldValue.toString();
    }

    return newValue !== oldValue;
  });

  return hasDifference;
};

// 将文件信息转换为ocr识别结果结构
export const transformFile2OcrStruct = (source: any) => {
  return {
    ...(source || {}),
    extendInfo: source?.carPolicyRspDTO?.extendInfo,
    checkStatus: checkReturnStatus(source),
    passInit: isPassInit(source),
  };
};

export function getRandomKey() {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

// 处理重复ocr信息
export function processDuplicateOcrInfo(fileList: any[]) {
  const uniqueKeys = new Map();
  // 先遍历一遍现有ocrlist中的重复数据，并标记信息，count为存在相同重复的数量，index则为后续作为标记重复项的基准
  fileList.forEach((file) => {
    let key = '';
    if (file?.ocrInfo?.orderNo && file?.ocrInfo?.vin) {
      key = `${file?.ocrInfo?.orderNo}${file?.ocrInfo?.vin}`;
    } else {
      key = getRandomKey();
    }
    // 将每个重复项的列表合并到结果中
    if (uniqueKeys.has(key)) {
      uniqueKeys.set(key, {
        count: uniqueKeys.get(key).count + 1,
        index: 0,
      });
    } else {
      uniqueKeys.set(key, {
        count: 1,
        index: 0,
      });
    }
  });
  const processedFileList = fileList.map((file) => {
    let key = '';
    if (file?.ocrInfo?.orderNo && file?.ocrInfo?.vin) {
      key = `${file?.ocrInfo?.orderNo}${file?.ocrInfo?.vin}`;
    } else {
      key = getRandomKey(); // 如果orderNo和vin为空，则生成随机数作为key, 防止空字段重复
    }
    const mapInfo = uniqueKeys.get(key); // 获取key对应的重复信息

    // 当后续上传的ocr文件里再次存在重复的保单时，将之前第一个标记重复的ocr信息重置
    if (mapInfo && mapInfo.count > 1 && mapInfo.index === 0 && file?.ocrInfo?.duplicate) {
      // 删除掉原重复信息里的exceptionMsgList首项
      const exceptionMsgList = file.ocrInfo.exceptionMsgList.slice(1);
      file.ocrInfo.exceptionMsgList = exceptionMsgList;
      // 恢复flag
      file.ocrInfo.checkFlag = file?.ocrInfo?.cacheFlag;
      // 更新key
      uniqueKeys.set(key, {
        ...mapInfo,
        index: (mapInfo?.index || 0) + 1, // 更新index
      });
      return {
        ...file,
        ocrInfo: {
          ...transformFile2OcrStruct(file?.ocrInfo || {}),
          duplicate: false,
        },
      };
    }
    // 如果新的ocrList，则进行重复判断, 如果已经存在相同的订单号和车架号，手动进行额外处理，单个保单校验时只有新保单不会有old标识
    if (mapInfo && mapInfo.count > 1 && mapInfo.index > 0 && !file?.ocrInfo?.old) {
      // 更新key
      uniqueKeys.set(key, {
        ...mapInfo,
        index: (mapInfo?.index || 0) + 1,
      });
      return {
        ...file,
        ocrInfo: {
          ...file.ocrInfo,
          duplicate: true, // 标记为重复
          passInit: false, // 标记为不通过
          checkFlag: false, // 标记为不通过
          cacheFlag: file?.ocrInfo?.cacheFlag || file?.ocrInfo?.checkFlag, // 如果之前的cache为true，则使用cache，否则缓存当前flag状态
          checkStatus: 'error', // 标记为异常
          exceptionMsgList: Array.from(
            new Set([
              '本次上传存在重复保单,请删除重复数据',
              ...(file?.ocrInfo?.exceptionMsgList || []),
            ]),
          ),
        },
      };
    }
    // 如果ocrList(删除/重新上传/校验检测)其余文件后，不存在重复的保单，则将重复标记和异常内容去掉
    if (mapInfo && mapInfo.count === 1 && mapInfo.index === 0 && file?.ocrInfo?.duplicate) {
      // 删除掉原重复信息里的exceptionMsgList首项
      const exceptionMsgList = file?.ocrInfo?.exceptionMsgList?.slice(1);
      file.ocrInfo.exceptionMsgList = exceptionMsgList;
      // 恢复flag
      file.ocrInfo.checkFlag = file?.ocrInfo?.cacheFlag;
      // 更新key
      uniqueKeys.set(key, {
        ...mapInfo,
        index: (mapInfo?.index || 0) + 1,
      });
      return {
        ...file,
        ocrInfo: {
          ...transformFile2OcrStruct(file?.ocrInfo || {}),
          duplicate: false,
        },
      };
    }
    // 更新key
    uniqueKeys.set(key, {
      ...(mapInfo || { count: 0 }),
      index: (mapInfo?.index || 0) + 1,
    });
    return file;
  });
  return processedFileList;
}
// 处理单个重复ocr信息
export function diffSingleDuplicateOcrInfo(ocrLists: any[], newOcrInfo: any, targetOcrInfo: any) {
  // 格式化校验是否存在重复保单
  const coverFileList = ocrLists.map((item: any) => {
    if (item.uid === targetOcrInfo?.uid) {
      // 将当前选择的文件覆盖为最新文件内容，作为列表候选项进行重复校验
      return newOcrInfo;
    }
    return {
      ...item,
      ocrInfo: {
        ...item.ocrInfo,
        old: true, // 标记是之前识别上传过的
      },
    };
  });
  const formatFiles = processDuplicateOcrInfo(coverFileList);
  // 将处理好的当前保单数据组装好，返回给state
  const newFile = formatFiles.find((item: any) => item.uid === newOcrInfo.uid);
  return newFile;
}

export { getItem, checkReturnStatus };
