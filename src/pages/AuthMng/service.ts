import { request } from 'umi';
import { bizadminHeader } from '@/services/consts';

interface QueryFeatureListParams {
  privilegeNameList?: string[];
  privilegeCodeList?: string[];
  url?: string;
  markList?: string[];
  pageSize: number;
  current: number;
}
// 分页查询权限列表
export async function queryFeatureList(data: QueryFeatureListParams) {
  return request(`/bizadmin/role/privilege/queryPrivilegePageByByCondition`, {
    headers: bizadminHeader,
    data,
    method: 'POST',
  });
}

// 分页查询权限树形列表
export async function queryFeatureTreeList() {
  return request(`/bizadmin/role/privilege/queryTreePrivilegeList`, {
    headers: bizadminHeader,
    method: 'GET',
  });
}

interface updateFeatureParams {
  privilegeCode: string;
  privilegeName: string;
  mark: string;
  method: string;
  realUrl: string;
  parentPrivilegeCode?: string;
}

// 更改权限信息
export async function updateFeature(data: updateFeatureParams) {
  return request(`/bizadmin/role/privilege/updateByPrivilegeCode`, {
    headers: bizadminHeader,
    data,
    method: 'PUT',
  });
}

// 模糊搜索权限列表
export async function queryFeatureListByName(params: { privilegeName: string }) {
  return request(`/bizadmin/role/privilege/queryByNameLike`, {
    headers: bizadminHeader,
    method: 'GET',
    params,
  });
}

interface createFeatureParams {
  privilegeName: string;
  privilegeCode: string;
  realUrl: string;
  mark: string;
  method: string;
  parentPrivilegeCode: string;
}
// 创建权限
export async function createFeature(data: createFeatureParams) {
  return request(`/bizadmin/role/privilege/createPrivilege`, {
    headers: bizadminHeader,
    data,
    method: 'POST',
  });
}

interface QueryRoleListParams {
  roleNameList?: string[];
  roleCodeList?: string[];
  pageSize: number;
  current: number;
}
// 分页查询角色列表
export async function queryRoleList(data: QueryRoleListParams) {
  return request(`/bizadmin/role/privilege/queryBindRoleByPrivilege`, {
    headers: bizadminHeader,
    data,
    method: 'POST',
  });
}

interface UpdateRoleParams {
  roleCode?: string;
  roleName: string;
}

// 创建权限信息
export async function createRole(data: UpdateRoleParams) {
  return request(`/bizadmin/role/privilege/createRole`, {
    headers: bizadminHeader,
    data,
    method: 'POST',
  });
}
// 更新角色信息
export async function updateRole(data: UpdateRoleParams) {
  return request(`/bizadmin/role/privilege/updateByRoleCode`, {
    headers: bizadminHeader,
    data,
    method: 'POST',
  });
}
// 查询权限-角色列表
export async function queryPrivilegeRoleList(params: { privilegeCode: string }) {
  return request(`/bizadmin/role/privilege/queryBindRoleByPrivilege`, {
    headers: bizadminHeader,
    params,
  });
}

// 查询角色-权限列表
export async function queryRolePrivilegeList(data: { roleCodes: string[] }) {
  return request(`/bizadmin/role/privilege/queryRolePrivilegeListByRoleCodes`, {
    headers: bizadminHeader,
    data,
    method: 'POST',
  });
}

// 绑定角色权限
export async function bindRolePrivilege(data: { roleCode: string; privilegeCodeList: string[] }) {
  return request(`/bizadmin/role/privilege/bindOrUnbindRolePrivilege`, {
    headers: bizadminHeader,
    data,
    method: 'POST',
  });
}

// 分页查询用户列表
interface QueryUserParams {
  pageSize?: number;
  current?: number;
  pidList?: string[];
  employeeName?: string; // 模糊匹配
  employeeType?: number; // 9运营| 10车险渠道| 11委外
  notInEmployeeType?: number[]; // 排除的员工类型
  phone?: string;
  email?: string;
  status?: number;
  // 传roleCodes时，表示角色筛选，符合其中一个角色就会返回
  roleCodes?: string[]; // 角色code
}
export async function queryUserList(data: QueryUserParams) {
  return request(`/bizadmin/employee/queryEmployeeListByPage`, {
    headers: bizadminHeader,
    data: {
      ...data,
      // 只查外部用户
      notInEmployeeType: [9],
    },
    method: 'POST',
  });
}

// 查询用户-角色列表，绑定关系列表
export async function queryUserRoleList(pid: string) {
  return request(`/bizadmin/employee/queryEmployeeByReqPid/${pid}`, {
    headers: bizadminHeader,
    method: 'GET',
  });
}

// 绑定用户-角色
export async function bindUserRoles(data: { employeeId: string; roleCodes: string[] }) {
  return request(`/bizadmin/employee/updateByEmployeeId`, {
    headers: bizadminHeader,
    data,
    method: 'PUT',
  });
}
