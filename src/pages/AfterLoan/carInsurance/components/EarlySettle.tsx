/**
 * 订单纬度 - 线下还款
 */
import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import LoadingButton from '@/components/LoadingButton';
import { getAuthHeaders } from '@/utils/auth';
import { disableFutureDate, getBaseUrl } from '@/utils/utils';
import type { ExportExcelParams } from '@/utils/xlsx-export';
import { exportExcelByXLSX, getTableData } from '@/utils/xlsx-export';
import type { ActionType } from '@ant-design/pro-components';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components';
import type { UploadFile } from 'antd';
import { Button, Col, Descriptions, Form, message, Row, Upload } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import type { PropsWithChildren } from 'react';
import React, { memo, useRef, useState } from 'react';
import { useAccess } from 'umi';
import { EarlySettleModalColumns } from '../columns/EarlySettleModalColumns';
import { billSettleInfo, submitBillSettleRepay } from '../services';
import type { IbillInfo, IbillListItem, Idimension } from '../types';
import { earlySettleExportTableHeader } from './data';
import './index.less';
import RedText from './RedText';
import RemissionEditTable from './RemissionEditTable';

type Props = {
  selectedRows: IbillListItem[];
  dimension: Idimension;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};
const EarlySettle = (props: PropsWithChildren<Props>) => {
  const { selectedRows, dimension, actionRef } = props;

  const [billInfoData, setBillInfoData] = useState<IbillInfo>();
  const [open, setOpen] = useState(false);
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const access = useAccess();
  const [form] = Form.useForm();
  const [exportLoading, setExportLoading] = useState(false);

  // 计算减免总金额
  function calculateRemissionAmount(remission: any) {
    return (
      remission?.reduce((pre, cur) => {
        const {
          remissionPrincipal = 0,
          remissionInterest = 0,
          remissionPenalty = 0,
          remissionAdvanceSettleLiquidatedDamages = 0,
          remissionDelayAmount = 0,
        } = cur;

        const amount = new BigNumber(remissionPrincipal)
          .plus(remissionInterest)
          .plus(remissionPenalty)
          .plus(remissionAdvanceSettleLiquidatedDamages)
          .plus(remissionDelayAmount)
          .toNumber();
        return new BigNumber(pre).plus(amount).toNumber();
      }, 0) || 0
    );
  }

  const remissionEditRef = useRef();

  // 导出
  const handleExport = async () => {
    setExportLoading(true);
    const tableData = getTableData(
      earlySettleExportTableHeader,
      billInfoData?.billRspDTOList || [],
    );
    const params: ExportExcelParams = {
      header: earlySettleExportTableHeader,
      fileName: `结清账单-${dayjs().format('YYYY.MM.DD HH:mm:ss')}`,
      data: tableData,
    };
    try {
      exportExcelByXLSX(params);
    } catch (error) {
      message.error('导出失败');
    } finally {
      // 等待一段时间再放开按钮
      setTimeout(() => {
        setExportLoading(false);
      }, 2e3);
    }
  };

  // console.log('asdasd', getAmount());
  return (
    <>
      <LoadingButton
        type="primary"
        onClick={async () => {
          try {
            if (!selectedRows?.length) {
              message.error('还未勾选任何数据');
              return;
            }
            fullScreenLoadingRef.current?.open();
            const billNoList = selectedRows.map((item) => item.billNo);
            const data = await billSettleInfo({
              billNoList,
              dimension,
              secondaryClassification: '0303',
              settleType: true,
            });

            // console.log(JSON.stringify(data));
            // const data = JSON.parse(data1);
            // const data = { billRspDTOList: [{ subjectMatterNo: '111' }] };
            setBillInfoData(data);
            setOpen(true);
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        线下提前结清
      </LoadingButton>
      <ModalForm
        open={open}
        // open={true}
        // form={form}
        layout="horizontal"
        labelCol={{ span: 3 }}
        title={<div className="modal_tit">提前结清</div>}
        modalProps={{
          onCancel: () => {
            setOpen(false);
          },
          destroyOnClose: true,
          // open: open
        }}
        width={1000}
        initialValues={{
          remission: [
            {
              id: 1,
              // remissionPrincipal: 0,
              // remissionInterest: 0,
              // remissionPenalty: 0,
              // remissionAdvanceSettleLiquidatedDamages: 0,
              // remissionDelayAmount: 0,
            },
          ],
        }}
        onFinish={async (values) => {
          try {
            console.log('values', values);
            const { remission, attach, isRemission, ...rest } = values;
            console.log(isRemission);
            if (isRemission) {
              await remissionEditRef?.current?.validatorEditRemission(); //校验不通过不执行
            }
            // 是否有权限提交
            if (!access.hasAccess('biz_billList_earlySettlement_commit_button')) {
              message.error('没有权限提交');
              return;
            }

            // const billNoList = billInfoData?.billRspDTOList.map((item) => item.billNo) || [];
            // const {
            //   remissionPrincipal,
            //   remissionInterest,
            //   remissionPenalty,
            //   remissionAdvanceSettleLiquidatedDamages,
            //   remissionDelayAmount,
            // } = remission?.[0];
            const remissionTotalAmount = calculateRemissionAmount(values.remission);
            const remissionT = remission?.map(({ billNo, ...rest }) => {
              const billNoT = billNo.split('-')[0]; //账单号
              const remissionItemNo = billNo.split('-')[1]; //车架号
              return { billNo: billNoT, remissionItemNo, ...rest };
            });
            const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              .minus(remissionTotalAmount)
              .toNumber();
            fullScreenLoadingRef.current?.open();
            // console.log({
            //   remissionList: remissionT,
            //   billInfoList: billInfoData?.billRspDTOList,
            //   attach,
            //   // billNoList,
            //   repayAmount: repayTotalAmount,
            //   dimension,
            //   repayDate: repayDate && repayDate + ' 00:00:00',
            //   settleType: true,
            //   ...rest,
            // });
            await submitBillSettleRepay({
              // remissionPrincipal,
              // remissionInterest,
              // remissionPenalty,
              // remissionAdvanceSettleLiquidatedDamages,
              // remissionDelayAmount,
              remissionList: remissionT,
              billInfoList: billInfoData?.billRspDTOList,
              attach,
              // billNoList,
              repayAmount: repayTotalAmount,
              dimension,
              settleType: true,
              ...rest,
            });
            message.success('提交成功');
            setOpen(false);
            actionRef.current?.reload();
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        <div className="block_area">
          <Descriptions bordered column={1} size={'small'} style={{ marginBottom: 20 }}>
            <Descriptions.Item label={<div className="label_width">渠道名称</div>}>
              {billInfoData?.channelName}
            </Descriptions.Item>
            <Descriptions.Item label={<div className="label_width">客户名称</div>}>
              {billInfoData?.accountNameList?.join(',')}
            </Descriptions.Item>
          </Descriptions>
        </div>
        <div className="remission_sel">
          <ProFormRadio.Group
            name="isRemission"
            labelCol={{ span: 14 }}
            label={<div className="settle_tit">减免车辆还款金额</div>}
            initialValue={false}
            className="item_is_remission"
            fieldProps={{
              style: { display: 'flex', alignItems: 'center', justifyContent: 'center' },
            }}
            options={[
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ]}
          />
        </div>
        <ProFormDependency name={['isRemission']}>
          {({ isRemission }) => {
            return (
              isRemission && (
                <div style={{ display: isRemission ? 'block' : 'none' }}>
                  <ProFormDependency name={['remission']}>
                    {(values) => {
                      const amount = calculateRemissionAmount(values.remission);
                      return (
                        <div style={{ marginBottom: 20, marginTop: 16 }}>
                          减免总金额:
                          <span style={{ color: 'red' }}>{isNaN(amount) ? '-' : amount}</span>元
                        </div>
                      );
                    }}
                  </ProFormDependency>

                  {/* <ProForm.Item name="remission"> */}
                  <RemissionEditTable
                    billInfoData={billInfoData}
                    cRef={remissionEditRef}
                    formRef={form}
                  />
                  {/* </ProForm.Item> */}
                </div>
              )
            );
          }}
        </ProFormDependency>
        <div className="tit">请确认账单信息无误后提交: </div>
        <ProFormDependency name={['remission', 'isRemission']}>
          {(values) => {
            let repayTotalAmount = 0;
            const remissionTotalAmount = calculateRemissionAmount(values.remission);
            const repayAmount = new BigNumber(billInfoData?.repayAmount || '').toNumber(); //提前结清的额度

            if (values?.isRemission) {
              repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
                .minus(remissionTotalAmount)
                .toNumber();
            } else {
              repayTotalAmount = repayAmount;
            }

            return (
              <>
                <div style={{ marginTop: 16, marginBottom: 20 }}>
                  提前结清还款总金额:
                  <RedText text={isNaN(repayAmount) ? '-' : repayAmount} />
                  元，减去减免总金额后，实际还款金额:
                  <RedText text={isNaN(repayTotalAmount) ? '-' : repayTotalAmount} />元
                </div>
                <div>
                  合计未还本金:
                  <RedText text={billInfoData?.totalPrincipalNotRepaid || '-'} />
                  元，未还利息：
                  <RedText text={billInfoData?.totalInterestNotRepaid || '-'} />
                  元，未还罚息：
                  <RedText text={billInfoData?.totalOverduePenaltyNotRepaid || '-'} />
                  元，提前结清违约金:
                  <RedText text={billInfoData?.totalBreachNotRepaid || '-'} />元
                </div>
              </>
            );
          }}
        </ProFormDependency>
        <ProTable
          search={false}
          options={false}
          columns={EarlySettleModalColumns}
          dataSource={billInfoData?.billRspDTOList as any}
          scroll={{ x: 'max-content' }}
          toolBarRender={() => {
            return [
              <Button key="exportBtn" type="primary" onClick={handleExport} loading={exportLoading}>
                导出
              </Button>,
            ];
          }}
          pagination={false}
        />
        <div className="tit">付款信息</div>
        <div className="block_area">
          <Row>
            <Col span={12}>
              <ProFormUploadButton
                labelCol={{ span: 6 }}
                action={`${getBaseUrl()}/repayment/oss/common/uploadfile`}
                rules={[{ required: true }]}
                fieldProps={{
                  headers: getAuthHeaders(),
                  name: 'file',
                  data: { acl: 'PUBLIC_READ', destPath: 'EP_AUTH_INFO' }, // 后端商量默认格式
                  multiple: true,
                  beforeUpload: (file) => {
                    const isLt10M = file.size / 1024 / 1024 < 10;
                    if (!isLt10M) {
                      message.error(`${file.name}超过了10M`);
                      return false || Upload.LIST_IGNORE;
                    }
                    return true;
                  },
                }}
                onChange={({ file }) => {
                  if (
                    file.status === 'done' &&
                    file.response?.ret === 200 &&
                    file?.response?.data?.netWorkPath
                  ) {
                    file.url = file?.response?.data?.netWorkPath;
                    (file as any).filePath = file?.response?.data?.filePath;
                  }
                }}
                transform={(values: UploadFile[]) => {
                  const attach = values
                    ?.filter((item) => item.url)
                    .map((file) => {
                      const { url, name, uid } = file;
                      const { filePath } = file as any;
                      return {
                        url,
                        name,
                        uid,
                        filePath,
                      };
                    });
                  return { attach };
                }}
                max={10}
                label="付款凭证"
                name="attach"
                accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
              />
              {/* <CommonImageUpload
                labelCol={{ span: 6 }}
                extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
                label="付款凭证"
                name="attach"
                rules={[{ required: true }]}
                // labelCol={{ span: 5 }}
                // max={5}
                listType="text"
                size={10}
                fileListEdit={allFileList?.attach || []}
                desPath="EP_AUTH_INFO"
                mapFileList={mapFileList}
                buttonProps={{ type: 'button' }}
                accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
              /> */}
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <ProFormSelect
                labelCol={{ span: 6 }}
                name="remitType"
                label="支付方式"
                initialValue={2}
                width="sm"
                options={[
                  { label: '普通对公打款', value: 2 },
                  { label: '线下二维码还款', value: 3 },
                ]}
                rules={[{ required: true }]}
                // disabled
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                labelCol={{ span: 6 }}
                name="bankAmount"
                label="打款金额"
                fieldProps={{ min: 0, precision: 2 }}
                placeholder="请输入打款金额"
                width="sm"
                rules={[{ required: true }]}
              />
            </Col>
            <Col span={12}>
              <ProFormDatePicker
                name="bankAmountDate"
                labelCol={{ span: 6 }}
                label="打款日期"
                fieldProps={{ disabledDate: disableFutureDate }}
                rules={[{ required: true }]}
                placeholder="请输入打款日期"
                width="sm"
                // fieldProps={{
                //   className: 'offline-repay-date',
                //   getPopupContainer: () => {
                //     return document.getElementsByClassName('offline-repay-date')[0];
                //   },
                // }}
              />
            </Col>
            <Col span={12}>
              <ProFormText name="bankNo" labelCol={{ span: 6 }} label="付款账号" width="sm" />
            </Col>
            <Col span={12}>
              <ProFormText name="bankAccount" labelCol={{ span: 6 }} label="付款户名" width="sm" />{' '}
            </Col>
            <Col span={12}>
              <ProFormText name="bankName" labelCol={{ span: 6 }} label="付款银行" width="sm" />
            </Col>
            <Col span={12}>
              <ProFormText
                name="subBranchBank"
                labelCol={{ span: 6 }}
                label="付款银行支行"
                width="sm"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="repaySerialNo"
                labelCol={{ span: 6 }}
                label="银行到账流水号"
                width="sm"
              />
            </Col>
          </Row>
        </div>
      </ModalForm>
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};

export default memo(EarlySettle);
