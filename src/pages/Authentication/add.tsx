import PageContainerHeaderTab from '@/components/PageContainerHeaderTab';
import ProForm from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Button, message, Modal, Tag } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { memo, useRef, useState } from 'react';
import AddAuthSignatory from './components/AddAuthSignatory';
import AddLicense from './components/AddLicense';
import { ajaxAdd } from './services';
import styles from './styles/index.less';
const Add = () => {
  const formRef = useRef<FormInstance>();
  const [loading, setLoading] = useState(false);
  return (
    <PageContainerHeaderTab
      extra={[
        <Button
          type="primary"
          loading={loading}
          key={'save'}
          onClick={() => {
            formRef?.current?.submit();
          }}
        >
          保存
        </Button>,
      ]}
    >
      <div className={styles['add-auth']}>
        <ProForm
          submitter={false}
          formRef={formRef}
          labelWrap={true}
          layout="horizontal"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          onFinish={async (values) => {
            const {
              certiNo,
              certiName,
              legalPerson,
              legalPersonCertiNo,
              enterpriseAddress,
              tradingCertificateFilePath,
              authorizerCertiNo,
              authorizerName,
              authorizerTel,
              authorizerEmail,
              frontFilePath,
              unFrontFilePath,
            } = values;
            const params = {
              epAuthInfo: {
                certiNo, // 统一信用代码
                certiName, // 公司名称
                legalPerson, // 借款企业法人名称
                legalPersonCertiNo, // 法人证件号
                enterpriseAddress, // 企业地址
                tradingCertificateFilePath, // 企业营业执照原件oss相对路径
              },
              epAuthorizerAuthInfo: {
                authorizerCertiNo, // 授权人证件号码
                authorizerName, // 授权人姓名
                authorizerTel, // 授权人电话
                authorizerEmail, // 授权人邮箱
                frontFilePath, // 身份证正面oss相对路径
                unFrontFilePath, // 身份证反面oss相对路径
              },
            };
            setLoading(true);
            ajaxAdd(params)
              .then(() => {
                message.success('新增成功');
                history.push('/userMng/enterpriseMng/authentication/list');
              })
              .catch((err) => {
                if (err?.ret === 8002) {
                  Modal.error({
                    content: (
                      <div>
                        三要素校验失败,请核实
                        <Tag color="error">授权签约人姓名</Tag>
                        <Tag color="error">授权签约人身份证号</Tag>
                        <Tag color="error">授权签约人联系电话</Tag>信息是否准确
                      </div>
                    ),
                  });
                }
                if (err?.ret === 8003) {
                  Modal.error({
                    content: (
                      <div>
                        四要素校验失败,请核实
                        <Tag color="error">统一社会信用代码</Tag>
                        <Tag color="error">借款企业名称</Tag>
                        <Tag color="error">借款企业法人名称</Tag>
                        <Tag color="error">借款企业法人身份证号</Tag>
                        信息是否准确
                      </div>
                    ),
                  });
                }
              })
              .finally(() => {
                setLoading(false);
              });

            // return Promise.resolve(false);
          }}
        >
          {/*  企业营业执照 */}
          <AddLicense />
          {/* 授权签约人身份证号 */}
          <AddAuthSignatory />
        </ProForm>
      </div>
    </PageContainerHeaderTab>
  );
};
export default memo(Add);
