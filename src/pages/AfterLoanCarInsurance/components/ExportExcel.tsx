import { filterProps } from '@/utils/tools';
import { downLoadExcel } from '@/utils/utils';
import { Button } from 'antd';
import React, { useState } from 'react';

type Props = {
  getParams: () => any; // 获取参数
  exportExcel: (params: any) => Promise<any>; // 接口
};
const ExportExcel: React.FC<Props> = (props) => {
  const { getParams, exportExcel } = props;
  const [exportLoading, setExportLoading] = useState(false);
  const getExport = () => {
    setExportLoading(true);
    exportExcel(filterProps(getParams()))
      .then((res) => {
        console.log('res112233', res);
        downLoadExcel(res);
      })
      .catch((err) => {
        console.log('err123', err);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  return (
    <Button
      key="button"
      type="primary"
      onClick={() => {
        getExport();
      }}
      loading={exportLoading}
    >
      导出
    </Button>
  );
};

export default ExportExcel;
