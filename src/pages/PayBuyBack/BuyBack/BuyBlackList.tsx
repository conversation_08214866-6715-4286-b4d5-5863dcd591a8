// 回购黑名单
import { getAllChannelNameEnum } from '@/services/enum';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { <PERSON><PERSON>, Card } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import { changePayConfigStatus, getBuyBlackList } from '../service';
import type { PayBlackListItem } from '../types';
import AddPayBlackModal from './AddBuyBlackListModal';

enum STATUS {
  DEL = 9,
}
const PayBlackList = () => {
  const [showModal, setShowModal] = useState(false);
  const actionRef = useRef<ActionType>();

  const getAllChannelNameEnumMemo = useMemo(async () => {
    return await getAllChannelNameEnum();
  }, []);

  const handleDel = async (id: string) => {
    const params = {
      id,
      status: STATUS.DEL,
    };
    await changePayConfigStatus(params);
    actionRef.current?.reloadAndRest?.();
  };

  const columns = [
    {
      title: '业务线',
      dataIndex: 'productCode',
      valueEnum: {
        '0201': '融资租赁',
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channel',
      valueType: 'select',
      request: () => {
        return getAllChannelNameEnumMemo;
      },
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      render: (dom, record) => {
        return (
          <Button
            type="link"
            onClick={() => {
              handleDel(record.configId);
            }}
          >
            删除
          </Button>
        );
      },
    },
  ];

  return (
    <Card
      title="回购黑名单"
      extra={
        <Button
          type="primary"
          onClick={() => {
            setShowModal(true);
          }}
        >
          加入黑名单
        </Button>
      }
      style={{ marginBottom: 20 }}
    >
      <ProTable<PayBlackListItem>
        search={false}
        actionRef={actionRef}
        columns={columns}
        request={(params) => getBuyBlackList(params)}
      />
      <AddPayBlackModal
        open={showModal}
        onSuccess={() => {
          actionRef.current?.reload();
          setShowModal(false);
        }}
        onCancel={() => {
          setShowModal(false);
        }}
      />
    </Card>
  );
};

export default PayBlackList;
