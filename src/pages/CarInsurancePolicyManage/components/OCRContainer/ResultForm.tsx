import {
  ProForm,
  ProFormDateTimePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Col, Form, Row } from 'antd';
import dayjs from 'dayjs';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { policyHolderMap } from '../../enum';

interface ResultFormProps {
  dataInfo: any;
  checkStatus?: Record<string, boolean>;
  ref: React.RefObject<{
    checkForm: (callback: (isValid: boolean) => void) => void;
    resetForm: () => void;
  }>;
  validator?: (key: string, value: any, formValues: any) => any;
}

const ResultForm = forwardRef((props: ResultFormProps, ref) => {
  const { dataInfo, checkStatus, validator } = props;
  const [loading, setLoading] = useState(false);
  const [formIncetance] = Form.useForm();

  useImperativeHandle(ref, () => ({
    checkForm: (callback: (isValid: boolean) => void) => {
      formIncetance.validateFields().then((isValid) => {
        callback(isValid);
      });
    },
    getForm: () => formIncetance,
    setLoading: (value: boolean) => {
      setLoading(value);
    },
    resetForm: () => {
      formIncetance.resetFields();
    },
    validateFields: () => {
      return formIncetance.validateFields();
    },
  }));

  useEffect(() => {
    // 初始化表单项
    formIncetance.setFieldsValue({
      ...dataInfo,
      commercialInsuranceStartDate: dataInfo?.commercialInsuranceStartDate
        ? dayjs(dataInfo.commercialInsuranceStartDate)
        : undefined,
      commercialInsuranceEndDate: dataInfo?.commercialInsuranceEndDate
        ? dayjs(dataInfo.commercialInsuranceEndDate)
        : undefined,
    });
  }, [formIncetance, dataInfo]);

  return (
    <ProForm loading={loading} form={formIncetance} submitter={{ render: false }} layout="vertical">
      <Row gutter={36}>
        <Col span={8}>
          <ProFormText
            name="plateNo"
            rules={[{ required: true }, { max: 20, message: '限20个字符以内' }]}
            // readonly={!checkStatus?.plateNo}
            // disabled={dataInfo?.passInit} // 因为ocr识别有时候在第一次识别pass的时候，部分字段存在结果异常，所以运营砍掉这个首次通过时disabled的功能
            label="车牌号/合格证号"
            placeholder="请输入车牌号/合格证号"
          />
        </Col>
        <Col span={8}>
          <ProFormText
            name="vin"
            rules={[{ required: true }]}
            // readonly={!checkStatus?.vin}
            // disabled={dataInfo?.passInit}
            label="车架号"
            placeholder="请输入车架号"
          />
        </Col>
        <Col span={8}>
          <ProFormText
            name="policyNo"
            rules={[{ required: true }]}
            // readonly={!checkStatus?.policyNo}
            // disabled={dataInfo?.passInit}
            label="保单号"
            placeholder="请输入保单号"
          />
        </Col>
      </Row>
      <Row gutter={36}>
        <Col span={8}>
          {!dataInfo?.orderNoTimeList?.length ? (
            <ProFormText
              name="orderNo"
              // 不校验：订单号为空，订单号列表为空（此时会禁用按钮） 校验：订单号存在，订单号列表不为空（此时会显示下拉菜单选择订单号）
              rules={[{ required: !!dataInfo?.orderNo || !!dataInfo?.orderNoTimeList?.length }]}
              // readonly={!checkStatus?.orderNo}
              // disabled={dataInfo?.passInit}
              // disabled={!dataInfo?.orderNoList?.length} // 订单号列表不存在时，可能会存在订单号，无论有没有订单号，都会禁用输入，
              label="订单号"
              placeholder="请输入订单号"
            />
          ) : (
            <ProFormSelect
              name="orderNo"
              rules={[{ required: true }]}
              // readonly={!checkStatus?.orderNo}
              // disabled={dataInfo?.passInit}
              label="订单号"
              fieldProps={{
                showSearch: true,
                dropdownStyle: { width: '460px' },
                optionLabelProp: 'value',
              }}
              options={
                dataInfo?.orderNo
                  ? [
                      // { label: dataInfo?.orderNo, value: dataInfo?.orderNo },
                      {
                        label: (
                          <Row gutter={5}>
                            <Col span={15}>订单号:</Col>
                            <Col span={9}>放款时间:</Col>
                            <Col span={15} style={{ whiteSpace: 'wrap', wordBreak: 'break-all' }}>
                              {dataInfo?.orderNo}
                            </Col>
                            <Col span={9}>{dataInfo?.carPolicyRspDTO?.lendingTime || '-'}</Col>
                          </Row>
                        ),
                        value: dataInfo?.orderNo,
                      },
                      ...(dataInfo?.orderNoTimeList?.map((item: any) => ({
                        label: (
                          <Row gutter={5}>
                            <Col span={15}>订单号:</Col>
                            <Col span={9}>放款时间:</Col>
                            <Col span={15} style={{ whiteSpace: 'wrap', wordBreak: 'break-all' }}>
                              {item?.orderNo}
                            </Col>
                            <Col span={9}>{item?.lendingTime || '-'}</Col>
                          </Row>
                        ),
                        value: item?.orderNo,
                      })) || []),
                    ]
                  : dataInfo?.orderNoTimeList?.map((item: any) => ({
                      label: (
                        <Row gutter={5}>
                          <Col span={15}>订单号:</Col>
                          <Col span={9}>放款时间:</Col>
                          <Col span={15} style={{ whiteSpace: 'wrap', wordBreak: 'break-all' }}>
                            {item?.orderNo}
                          </Col>
                          <Col span={9}>{item?.lendingTime || '-'}</Col>
                        </Row>
                      ),
                      value: item?.orderNo,
                    }))
              }
              placeholder="请选择订单号"
              // fieldProps={{
              //   showSearch: true,
              //   optionFilterProp: 'label',
              // }}
            />
          )}
        </Col>
        <Col span={8}>
          <ProFormSelect
            rules={[{ required: true }]}
            placeholder="请选择投保人"
            fieldProps={{
              showSearch: true,
              dropdownStyle: { width: '260px' },
              optionFilterProp: 'label',
            }}
            valueEnum={policyHolderMap}
            // readonly={!checkStatus?.policyHolder}
            // disabled={dataInfo?.passInit}
            name="policyHolder"
            label="投保人"
          />
        </Col>
        <Col span={8}>
          <ProFormDigit
            name="policyCommercialInsurance"
            rules={[{ required: true }]}
            // readonly={!checkStatus?.policyCommercialInsurance}
            // disabled={dataInfo?.passInit}
            label="保单商业险金额"
            placeholder="请输入保单商业险金额"
          />
        </Col>
      </Row>
      <Row gutter={36}>
        <Col span={8}>
          <ProFormDateTimePicker
            name="commercialInsuranceStartDate"
            rules={[{ required: true }]}
            // readonly={!checkStatus?.commercialInsuranceStartDate}
            // disabled={dataInfo?.passInit}
            transform={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '')}
            fieldProps={{
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
              autoFocus: true,
            }}
            label="商业险实际开始时间"
            placeholder="请输入商业险实际开始时间"
          />
        </Col>
        <Col span={8}>
          <ProFormDateTimePicker
            rules={[{ required: true }]}
            // readonly={!checkStatus?.commercialInsuranceEndDate}
            // disabled={dataInfo?.passInit}
            transform={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '')}
            fieldProps={{
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss',
              autoFocus: true,
            }}
            placeholder="请选择商业险实际结束时间"
            name="commercialInsuranceEndDate"
            label="商业险实际结束时间"
          />
        </Col>
        <Col span={8}>
          <ProFormText
            name="factoryPlateModel"
            // rules={[{ required: true }]}
            // readonly={!checkStatus?.factoryPlateModel}
            // disabled={dataInfo?.passInit}
            label="厂牌型号"
            placeholder="请输入厂牌型号"
          />
        </Col>
      </Row>
    </ProForm>
  );
});

export default ResultForm;
