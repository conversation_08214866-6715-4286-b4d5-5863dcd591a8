/* eslint-disable @typescript-eslint/consistent-type-imports */
/*
 * @Author: your name
 * @Date: 2021-04-12 15:18:02
 * @LastEditTime: 2025-04-07 16:31:13
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/BindCarModalNew.tsx
 */
import { DividerTit } from '@/components';
import { CommonDraggleUpload } from '@/components/ReleaseCom';
import { CHANNEL_TYPES_LABEL_MAP } from '@/enums';
import globalStyle from '@/global.less';
import { getAuthHeaders } from '@/utils/auth';
import {
  asyncReadFileToDataUrl,
  compressImg,
  convertUploadFileList,
  getBase64,
  getBaseUrl,
  getBlob,
  isChannelStoreUser,
  previewAS,
  readDataUrlToImg,
} from '@/utils/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import ProForm, {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormUploadDragger,
} from '@ant-design/pro-form';
import { history, useAccess, useModel } from '@umijs/max';
import { Alert, Button, Form, message, Modal, Upload } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import type { BindCarParams } from '../data';
import {
  bindingCar,
  carFabricateDateCheck,
  getAllChannelByChannelType,
  getApplyStoreList,
  getCarOcrInfo,
  getCompanyList,
  getPromotionByCarNoAndApplyCityCode,
  validateCarCodeEqual,
} from '../service';

export type BindCarModelProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  data?: any;
  orderDetail?: any;
  formCur?: {
    carUniqueCode?: string;
    licenseCode?: string;
    licenseType: number;
    engineCode?: string;
    otherMsg?: string;
    carNo?: string;
    energyType?: string;
    totalPrice?: string;
    channelType?: number;
    channelDetail?: SelItem;
    applyCityDetail?: SelItem;
    colorCityDetail?: Record<string, any>[];
    cityDetail?: SelItem;
    applyStore?: string;
    applyStoreId?: string;
    licenseCompany: {
      label: string;
      value: string;
    };
    oosFilePath?: string;
    carRegistration?: {
      filePath: string;
      name: string;
      uid: string;
      url: string;
    }[];
    drivingLicense?: {
      filePath: string;
      name: string;
      uid: string;
      url: string;
    }[];
  };
  disableForm?: boolean;
  // deadLine: string;
};
type SelItem = {
  label: string;
  value: number | string;
};

type PreviewType = {
  previewVisible?: boolean;
  previewTitle?: string;
  previewImage?: string;
};

enum LICENSE_TYPE {
  COMPANY = 1,
  PERSONAL = 2,
}

const BindCarModalNew: React.FC<BindCarModelProps> = (props) => {
  const { orderNo }: any = history.location.query;
  // const { userList } = useModel('userList');
  // const isRandom = Math.ceil(Math.random()*10)
  const { formCur, modalVisible, onVisibleChange, data: orderData, orderDetail } = props;
  const { cityList } = useModel('cityList');

  const [form] = Form.useForm();
  const [showMoreDetail, handleShowMoreDetail] = useState<boolean>(false);
  const [channelNameList, setChannelNameList] = useState<SelItem[]>([]);
  const [licenseList, setLicenseList] = useState<SelItem[]>([]);
  const [licenseCityList, setLicenseCityList] = useState([]);
  const [storeList, setStoreList] = useState<SelItem[]>([]);
  const [newFormData, setNewForm] = useState({});
  const [btnLoading, setBtnLoading] = useState(false);

  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const access = useAccess();

  const baseUrl = getBaseUrl();
  const uploadUrl = `${baseUrl}/repayment/oss/common/uploadfile`;
  const [previewObj, handlePreviewObj] = useState<PreviewType>();

  // 绑车前做下出厂日期校验  code: 校验结果状态码; carTyingDays: 出厂日期距离绑车日期的天数; accessDays: 准入天数
  const defaultDayCheckData = { code: 0, accessDays: 0, carTyingDays: 0 };
  const [dayCheck, setDayCheck] = useState(defaultDayCheckData);
  const dayCheckRef = useRef(defaultDayCheckData);

  // 库存城市下拉
  const [libraryCarOptions, handleLibraryCarOptions] = useState<SelItem[]>([]);
  // 根据输入车型码
  const [formReset, setFormReset] = useState<Record<string, any>>({});
  const [isFirst, setIsFirst] = useState<boolean>(false);
  const [repeatError, setRepeatError] = useState<{ orderNo: string; userName: string }[] | null>(
    null,
  );
  // 合格证ocr匹配成功保存数据
  const [carOcrInfo, setCarOcrInfo] = useState({});
  const channelModifyFieldsKey = ['carUniqueCode', 'carFabricateDate', 'engineCode'];
  // 处理渠道类型联动渠道名称
  const handleSetOptionChannelName = (val: number) => {
    getAllChannelByChannelType(val).then((res) => {
      // console.log(res.data);
      // 联动
      const channelName = res.data.map((item: { id: string; channelName: string }) => {
        return { label: item?.channelName, value: item?.id };
      });
      setChannelNameList(channelName);
    });
  };

  // 处理渠道门店帐号是否需要人工复核流程
  const isChannelNeedReSureHandle = (params: any, values: any) => {
    if (!isChannelStoreUser(access)) return; //  非渠道门店账户，都需要人工复核
    if (Object.keys(carOcrInfo).length === 0) return; //  无ocr信息，都需要人工复核
    const modifyFields: any = [];
    channelModifyFieldsKey.forEach((key: string) => {
      if (values?.[key] && carOcrInfo[key] && values?.[key] !== carOcrInfo[key])
        modifyFields.push(key);
    });
    params.modifyFields = modifyFields.length ? modifyFields : ['empty'];
  };

  const [allFileList, handleFileList] = useState(formCur || {});
  useEffect(() => {
    // handleFileList(formCur);
    handleFileList({
      drivingLicense: formCur?.drivingLicense,
      carRegistration: formCur?.carRegistration,
    });
    form.setFieldsValue({
      ...formCur,
      licenseCompany:
        formCur?.licenseType === LICENSE_TYPE.PERSONAL
          ? formCur?.licenseCompany.label
          : formCur?.licenseCompany,
    });
    // console.log(formEdit);
    return () => {
      handleFileList({});
    };
  }, [formCur]);
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  const carFabricateDateCheckCon = async (data: any) => {
    // 新功能临时关闭开关,可跳过检查,稳定后该逻辑可去掉
    try {
      const newTime = new Date().getTime();
      const oldTime = localStorage.getItem('closeCheck');
      if (oldTime === '1') {
        localStorage.setItem('closeCheck', newTime.toString());
        return false;
      }
      if (oldTime) {
        if (newTime - Number(oldTime) < 3600000 * 24) return false; //  24小时生效
        localStorage.removeItem('closeCheck'); //  清除标识
      }
    } catch (e) {
      console.log(e);
    }

    const res = await carFabricateDateCheck({
      carModelCode: orderData?.carModelCode,
      productCode: orderDetail?.productCode,
      channelCode: orderData?.channelId,
      carNo: orderData?.carNo,
      carMakeDate: dayjs(data?.carFabricateDate || orderData?.carFabricateDate).format(
        'YYYY-MM-DD HH:mm:ss',
      ),
      carUniqueCode: data?.carUniqueCode || orderData?.carUniqueCode,
    }).catch((e) => {
      console.log(e);
    });
    setDayCheck({
      code: res?.data?.code || 0,
      accessDays: res?.data?.accessDays || 0,
      carTyingDays: res?.data?.carTyingDays || 0,
    });
    dayCheckRef.current = res?.data || defaultDayCheckData;
    if (res?.data?.code === 0) {
      return false; //  code 0 车辆校验通过
    }
    return true;
  };

  // 设置上牌城市
  const handleSetOptionLicenseName = (channelId: string | number, licenseType: number) => {
    getCompanyList(channelId, licenseType).then((res) => {
      const list =
        res?.data?.map((item: { licenseCompany: string; id: string }) => {
          return { ...item, value: item.id, label: item.licenseCompany };
        }) || [];
      // 个户上牌需要组装可选上牌城市
      if (formCur?.licenseType === LICENSE_TYPE.PERSONAL) {
        const cityList = list[0]?.licenseCityList.map((item) => {
          return {
            label: item.licenseCity,
            value: item.licenseCityCode,
          };
        });
        // 设置上牌城市选项
        setLicenseCityList(cityList);
      }

      // 如果上牌公司只有一个，默认填中上牌公司，上牌城市
      if (list.length === 1) {
        // 只有挂靠的上牌类型才可以修改上牌方
        if (formCur?.licenseType === LICENSE_TYPE.COMPANY) {
          form.setFieldsValue({
            licenseCompany: list[0],
            carCityDetail: {
              label: list[0].licenseCity,
              value: list[0].licenseCityCode,
            },
          });
        }
      }
      // 设置上牌公司选项
      setLicenseList(list);
    });
  };
  // 设置门店
  const handleSetOptionStoreName = (val: string | number) => {
    getApplyStoreList(val).then((res) => {
      const temp =
        res?.data?.map((item: { storeName: string; id: string }) => {
          return { ...item, value: item.id, label: item.storeName };
        }) || [];
      // 如果门店只有一个，默认选中申请门店，申请城市
      if (temp.length === 1) {
        form.setFieldsValue({
          applyStore: temp[0],
          applyCityDetail: {
            label: temp?.[0]?.saleCity,
            value: temp?.[0]?.saleCityCode,
          },
        });
      }
      setStoreList(temp);
    });
  };

  // 处理库存城市
  const handleSetCarCity = (list: any[] = []) => {
    const listCarLibrary: SelItem[] = list?.map((item: { cityName: string; cityCode: string }) => {
      return {
        label: item?.cityName,
        value: item?.cityName,
      };
    });

    handleLibraryCarOptions(listCarLibrary as SelItem[]);
  };

  const handleBeforeUpload = async (file: any) => {
    const FILE_REGEXP = /\.(pdf|png|jpg|jpeg|bmp)/i;
    const PDF_FILE_REGEXP = /\.(pdf)/i;
    const maxSize = 5 * 1024 * 1024;
    const { name, size } = file;
    if (!FILE_REGEXP.test(name)) {
      message.warning({
        content: '格式不支持，请上传pdf、png、jpg、jpeg、bmp格式文件',
      });
      return Upload.LIST_IGNORE;
    }
    // 小于5m的不压缩
    if (size <= maxSize) {
      console.log('图片小于5m 不压缩', file);
      return Promise.resolve();
    }
    // pdf文件直接返回
    if (PDF_FILE_REGEXP.test(name)) {
      return Promise.resolve();
    }

    console.log('压缩前图片', file);
    try {
      const dataUrl: string = await asyncReadFileToDataUrl(file);
      if (dataUrl) {
        const img = await readDataUrlToImg(dataUrl);
        const blob = await compressImg(img, file.type, 2000, 2000);
        if (blob) {
          const imgFile = new File([blob], file.name, { type: file.type });
          console.log('压缩完图片', imgFile);
          // 压缩完还是过大
          if (imgFile.size > maxSize) {
            message.warning({ content: '文件大小超出5M' });
            return Upload.LIST_IGNORE;
          }
          return imgFile;
        }
      }
      if (size > maxSize) {
        message.warning({ content: '文件大小超出5M' });
        return Upload.LIST_IGNORE;
      }
    } catch (error) {
      message.warning({ content: '当前环境不支持图片压缩' });
      return false;
    }
    return Promise.resolve();
  };

  // 上传合格证，做ocr识别数据
  const handleUploadChange = (info: any) => {
    // console.log(info);
    const { status, response } = info.file;
    if (status === 'uploading') {
      setBtnLoading(true);
    }
    if (status === 'done') {
      const { filePath, netWorkPath } = response?.data;
      const params = {
        filePath,
        netWorkPath,
        orderNo,
      };
      // 设置图片上传中状态
      info.file.status = 'uploading';
      setBtnLoading(true);
      message.loading({ content: '识别中...', duration: 0 });
      getCarOcrInfo(params)
        .then((res) => {
          message.destroy();
          console.log(res);
          setCarOcrInfo({}); //  初始化
          if (res && res.data) {
            const { carCode, carUniqueCode, engineCode, carFabricateDate } = res.data;
            // 全都有值才算成功
            if (carCode && carUniqueCode && engineCode && carFabricateDate) {
              // 再判断车型码是否一致,不一致给出提示
              const carModelCode = form.getFieldValue('carModelCode');
              if (carCode !== carModelCode && localStorage.getItem('debugger_ocr') !== '1') {
                // 设置图片状态失败
                info.file.status = 'error';
                message.warning({
                  content: '合格证识别的车辆型号与进件车型不一致',
                });
                form.setFieldsValue({
                  certificateFile: null,
                });
              } else {
                // 设置图片状态成功
                info.file.status = 'success';
                // 一致则反显数据
                form.setFieldsValue({
                  carUniqueCode, //车辆识别代号/车架号
                  engineCode, //发动机号,
                  carFabricateDate, //车辆制造日期
                  certificateFile: [{ url: netWorkPath }],
                });
                setCarOcrInfo(res.data); //  保存成功匹配数据
              }
            } else {
              // 设置图片状态失败
              info.file.status = 'error';
              message.warning({ content: '识别失败，请重新上传！' });
              form.setFieldsValue({
                certificateFile: null,
              });
            }
          }
        })
        .catch((error) => {
          message.destroy();
          // 设置图片状态失败
          info.file.status = 'error';
          message.warning({ content: error?.message || '识别失败，请重新上传！' });
          setCarOcrInfo({}); //  初始化
          form.setFieldsValue({
            certificateFile: null,
          });
        })
        .finally(() => {
          setBtnLoading(false);
        });
    }
  };

  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview && file.originFileObj) {
      // eslint-disable-next-line no-param-reassign
      file.preview = await getBase64(file.originFileObj);
    }
    // 如果是pdf，在浏览器打开预览
    if (
      file?.type === 'application/pdf' ||
      ['pdf'].includes(file?.name?.substring(file?.name.lastIndexOf('.') + 1))
    ) {
      getBlob(file?.url || file?.preview, (blob: Blob) => {
        previewAS(blob);
      });
      return;
    }

    handlePreviewObj({
      previewImage: file.url || file.preview,
      previewVisible: true,
      previewTitle: file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
    });
  };

  useEffect(() => {
    form.setFieldsValue({
      ...formReset,
      licenseCompany:
        formCur?.licenseType === LICENSE_TYPE.PERSONAL
          ? formCur?.licenseCompany.label
          : formCur?.licenseCompany,
    });
    // return setDisableEnvironmental(false);
  }, [formReset]);

  useEffect(() => {
    if (formCur?.channelType !== undefined) {
      handleSetOptionChannelName(formCur?.channelType);
    }

    if (formCur?.channelDetail?.value !== undefined) {
      handleSetOptionLicenseName(formCur?.channelDetail?.value, formCur?.licenseType);
      handleSetOptionStoreName(formCur?.channelDetail?.value);
    }
    if (formCur?.colorCityDetail) {
      handleSetCarCity(formCur?.colorCityDetail);
    }
    // 重新编辑的时候再写入合格证，根据是否有车辆识别代码判断，有的话就判断为是绑过车的
    if (formCur?.oosFilePath && formCur?.carUniqueCode) {
      form.setFieldsValue({
        certificateFile: [
          {
            url: formCur?.oosFilePath,
          },
        ],
      });
    }
  }, [formCur]);

  // 融租跳转订单列表页
  const skipLease = (repeatErrorArg: { orderNo?: string; userName?: string } | null) => {
    history.push(
      {
        pathname: '/businessMng/lease-list',
      },
      repeatErrorArg,
    );
  };

  return (
    <>
      <ModalForm
        title={
          <div>
            <div style={{ display: 'flex', alignItems: 'baseline' }}>
              <span style={{ width: 64 }}>绑定车辆</span>
            </div>
          </div>
        }
        // width={600}
        className={globalStyle.formModal}
        form={form}
        layout="horizontal"
        visible={modalVisible}
        onVisibleChange={onVisibleChange}
        width={1000}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            handleShowMoreDetail(false);
            setNewForm({});
            setRepeatError(null);
            setDayCheck(defaultDayCheckData); //  关闭弹窗后清楚错误提示痕迹
          },
        }}
        submitter={{
          render: (_, defaultDoms) => {
            return [
              !showMoreDetail && access.hasAccess('btn_change_carinfo_businessMng_leaseList') ? (
                <Button
                  key="update"
                  type="primary"
                  ghost
                  onClick={() => {
                    // props.submit();
                    setIsFirst(true);
                    handleShowMoreDetail(true);
                  }}
                >
                  变更车辆信息
                </Button>
              ) : null,
              ...defaultDoms,
            ];
          },
        }}
        onFinish={async (values) => {
          if (btnLoading) {
            return;
          }
          setRepeatError(null); // 清空error
          // 绑车前做下出厂日期校验
          const checkStatus = await carFabricateDateCheckCon(values);
          if (checkStatus) return; //  有异常则终止

          // 二次弹窗提示
          if (!showMoreDetail) {
            handleOptVisible(true);
            setFormReset({
              ...formCur,
              ...values,
            });

            return false;
          }
          const mapUploadFile = convertUploadFileList(allFileList, [
            'drivingLicense',
            'carRegistration',
          ]);

          let licenseCompany = values?.licenseCompany?.label;
          let licenseCompanyId = values?.licenseCompany?.value;

          if (formCur?.licenseType === LICENSE_TYPE.PERSONAL) {
            licenseCompany = formCur?.licenseCompany?.label;
            licenseCompanyId = formCur?.licenseCompany?.value;
          }

          const params = {
            orderNo,
            ...values,
            ...mapUploadFile,
            oosFilePath: values.certificateFile
              ? values.certificateFile[0]?.url
              : formCur?.oosFilePath,
            totalPrice: values?.totalPrice * 100,
            preferentialPrice: values?.totalPrice * 100,
            channelId: values?.channelDetail?.value,
            licenseType: formCur?.licenseType,
            licenseCompany,
            licenseCompanyId,
            applyStore: values?.applyStore?.label,
            applyStoreId: values?.applyStore?.value,
          };

          // 把合格证字段去掉
          delete params.certificateFile;

          // 渠道门店帐号通过和ocr的字段差异判断需不需要人工复核环节
          isChannelNeedReSureHandle(params, values);
          console.log('---看一下上传', params?.modifyFields);
          // 此处处理ocr与人工修改字段的变更逻辑
          const res = await bindingCar(params as BindCarParams);
          if (res?.data?.result) {
            if (isChannelStoreUser(access) && !res?.data?.isAutoPass) {
              Modal.warning({
                title: '车辆信息非自动识别，待金融复核绑车',
                icon: <ExclamationCircleOutlined style={{ color: 'red' }} />,
                centered: true,
                okText: '我知道了',
                maskClosable: true,
              });
              setTimeout(() => {
                Modal.destroyAll();
              }, 5000);
            } else {
              message.success('添加成功');
            }
            props.onOk();
            return true;
          }
          setRepeatError(res?.data?.userInfoList);
        }}
      >
        <div style={{ display: 'flex' }}>
          <ProFormText
            name="carUniqueCode"
            tooltip="录入限制17位字符，且字符仅支持阿拉伯数字和大写罗马字母，不支持输入空格/O/Q/I"
            // labelCol={{ span: 5 }}
            width="sm"
            label="车辆识别代码"
            placeholder="请输入车辆识别代码"
            rules={[
              { required: true },
              {
                pattern: /^[A-HJ-NPR-Z0-9]{17}$/,
                message: '字符应为17位，不可输入“空格/O/Q/I',
              },
            ]}
            validateTrigger="onBlur"
          />
          <ProFormText
            name="licenseCode"
            // labelCol={{ span: 5 }}
            width="sm"
            fieldProps={{ maxLength: 20 }}
            label="车牌"
            placeholder="请输入车牌"
            // rules={[{ required: true }]}
          />
          <ProFormText
            name="engineCode"
            // labelCol={{ span: 5 }}
            width="sm"
            fieldProps={{ maxLength: 20 }}
            label="发动机号"
            placeholder="请输入发动机号"
            rules={[{ required: true }]}
          />
        </div>
        <div style={{ display: 'flex' }}>
          <ProFormText
            name="remark"
            // labelCol={{ span: 5 }}
            width="sm"
            fieldProps={{ maxLength: 20 }}
            label="其他"
            placeholder="请输入"
            // rules={[{ required: true }]}
          />
          <ProFormDatePicker
            width="sm"
            name="registrationDateOfDrivingLicense"
            label="行驶证登记日期"
          />
          <ProFormDatePicker
            width="sm"
            name="carFabricateDate"
            label="车辆制造日期"
            rules={[{ required: true }]}
          />
        </div>
        <Modal
          open={previewObj?.previewVisible}
          title={previewObj?.previewTitle}
          footer={null}
          onCancel={() => {
            handlePreviewObj({ ...previewObj, previewVisible: false });
          }}
        >
          <img alt="该文件不支持预览" width={'100%'} src={previewObj?.previewImage} />
        </Modal>
        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
          <div style={{ flex: 1, maxWidth: '33%' }}>
            <ProFormUploadDragger
              width="sm"
              label="合格证"
              name="certificateFile"
              max={1}
              accept=".pdf,.png,.jpg,.jpeg,.bmp,.pdf"
              title="点击或拖拽上传文件"
              description=""
              action={uploadUrl}
              rules={[
                {
                  required: !access.hasAccess('form_car_fabricate_date_businessMng_leaseList'),
                  message: '请上传合格证',
                },
              ]}
              onChange={handleUploadChange}
              fieldProps={{
                multiple: false,
                listType: 'picture',
                headers: { ...getAuthHeaders() },
                name: 'file',
                data: {
                  acl: 'PUBLIC_READ',
                  destPath: 'USER_ORDER_APPLY_MONEY',
                },
                beforeUpload: handleBeforeUpload,
                onPreview: handlePreview,
              }}
            />
          </div>
          <div style={{ flex: 1, maxWidth: '33%' }}>
            <CommonDraggleUpload
              // extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
              label="行驶证"
              width="sm"
              title="点击或拖拽上传文件"
              description=""
              labelCol={{ span: 8 }}
              name="drivingLicense"
              max={5}
              listType="text"
              accept=".pdf,.png,.jpg,.jpeg,.bmp,.pdf"
              mapFileList={mapFileList}
              size={50}
              multiple={true}
              fileListEdit={formCur?.drivingLicense}
            />
          </div>
          <div style={{ flex: 1, maxWidth: '33%' }}>
            <CommonDraggleUpload
              // extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
              label="车辆登记证"
              width="sm"
              title="点击或拖拽上传文件"
              labelCol={{ span: 8 }}
              name="carRegistration"
              description=""
              max={5}
              listType="text"
              accept=".pdf,.png,.jpg,.jpeg,.bmp,.pdf"
              mapFileList={mapFileList}
              fileListEdit={formCur?.carRegistration}
              size={50}
              multiple={true}
              // buttonProps={{ type: "" }}
            />
          </div>
        </div>
        <div style={{ paddingLeft: 120, color: '#f2615e' }}>
          说明：①上传合格证；②上传行驶证+登记证。资料上传需满足两种组合方式之一，不满足则无法提交。
        </div>
        {showMoreDetail && (
          <DividerTit title="变更车辆信息">
            <ProForm.Group>
              <ProFormSelect
                rules={[{ required: true }]}
                // request={getAllChannelEnum}
                options={CHANNEL_TYPES_LABEL_MAP}
                placeholder="请选择渠道类型"
                disabled={true}
                name="channelType"
                fieldProps={{
                  onChange: (val) => {
                    form.setFieldsValue({ channelDetail: '' });
                    if (val !== undefined) {
                      // 判空
                      handleSetOptionChannelName(val);
                    } else {
                      setChannelNameList([]);
                    }
                  },
                }}
                width="sm"
                label="渠道类型"
              />
              <ProFormSelect
                rules={[{ required: true }]}
                options={channelNameList}
                // options={[{ label: '深圳', value: 1 }]}
                placeholder="请选择渠名称"
                disabled={true}
                name="channelDetail"
                width="sm"
                label="渠道名称"
                fieldProps={{
                  showSearch: true,
                  labelInValue: true,
                  optionFilterProp: 'label',
                  onChange: (item) => {
                    // console.log(val, aa, 'hahaha');
                    // 清空上牌公司，上牌城市，申请门店，申请城市
                    form.setFieldsValue({
                      licenseCompany: null,
                      applyStore: null,
                      carCityDetail: null,
                      applyCityDetail: null,
                    });
                    if (item !== undefined) {
                      // 判空
                      // handleSetOptionChannelName(val);
                      // handleSetOptionChannelName(val);
                      handleSetOptionLicenseName(item?.value, formCur?.licenseType);
                      handleSetOptionStoreName(item?.value);
                    } else {
                      // 设置上牌公司，申请门店为空
                      // setChannelNameList([]);
                      setStoreList([]);
                      setLicenseList([]);
                    }
                  },
                }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                name="carNo"
                // disabled={disableForm}
                rules={[
                  {
                    required: true,
                    validator: async (_, value) => {
                      if (value && form.getFieldValue('applyCityDetail')?.value) {
                        const mesg = await validateCarCodeEqual(
                          // formCur?.applyCityDetail?.value,
                          form.getFieldValue('applyCityDetail')?.value,
                          value,
                          orderNo,
                        )
                          .then((res) => {
                            const {
                              channelType,
                              channelDetail,
                              carUniqueCode,
                              licenseCode,
                              engineCode,
                              ...data
                            } = res.data;
                            // 检查变更后的车型码和通过合格证识别出的车辆识别码是否一致，若不一致则清空识别信息
                            const oldCarModelCode = form.getFieldValue('carModelCode');
                            if (data.carModelCode !== oldCarModelCode) {
                              form.setFieldsValue({
                                carUniqueCode: '', // 车辆识别代号/车架号
                                engineCode: '', // 发动机号,
                                carFabricateDate: null, // 车辆制造日期
                                certificateFile: null, // 合格证
                              });
                            }
                            // 对比上一次的数据是否和当前返回的数据完全一致，newFormData上一次返回的数据
                            // 首次展开，信息无变更，也不用走校验接口
                            const noValidate =
                              isFirst &&
                              JSON.stringify(newFormData) === JSON.stringify({}) &&
                              formCur?.carNo === value;
                            if (
                              !(
                                JSON.stringify({ ...data, cityName: '' }) ===
                                JSON.stringify(newFormData)
                              ) &&
                              !noValidate
                            ) {
                              // 联动库存城市
                              handleSetCarCity(data?.colorCityDetail);
                              // 给最新的
                              setNewForm({
                                ...data,
                                cityName: '',
                              });
                              // 重新赋值
                              setFormReset({
                                ...data,
                                cityName: '',
                              });
                              setIsFirst(false);
                            }

                            return '';
                          })
                          .catch((err) => {
                            setNewForm({});
                            return err.message;
                            // return Promise.reject(new Error(err));
                          });
                        console.log(mesg);
                        return mesg ? Promise.reject(new Error(mesg)) : Promise.resolve();
                      }
                      // return form.getFieldValue('applyCityDetail')?.value
                      //   ? Promise.resolve()
                      //   : Promise.reject(new Error('请输入车辆编码'));
                      if (!value) {
                        return Promise.reject(new Error('请输入车辆编码'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                fieldProps={{
                  maxLength: 20,
                  // onBlur: () => {
                  //   setFormReset(newFormData);
                  // },
                }}
                width="sm"
                label="车辆编码"
                placeholder="请输入车辆编码"
              />
              <ProFormText
                name="carModel"
                disabled
                width="sm"
                fieldProps={{ maxLength: 100 }}
                label="车型名称"
                placeholder="请输入车型名称"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                name="carModelCode"
                disabled
                fieldProps={{ maxLength: 20 }}
                width="sm"
                label="车型码"
                placeholder="请输入车型码"
              />
              <ProFormSelect
                name="carType"
                disabled
                options={[
                  { value: 1, label: '新车' },
                  { value: 2, label: '二手车' },
                ]}
                // fieldProps={{ maxLength: 20 }}
                width="sm"
                label="车辆类型"
                placeholder="请选择车辆类型"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                disabled
                options={[
                  { value: 1, label: '货车' },
                  { value: 2, label: '乘用车' },
                ]}
                placeholder="请选择车辆种类"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                }}
                width="sm"
                name="carCategory"
                label="车辆种类"
              />
              <ProFormSelect
                name="energyType"
                disabled
                options={[
                  { value: 1, label: '燃油' },
                  { value: 2, label: '纯电动' },
                  { value: 3, label: '油电混合' },
                  { value: 4, label: '插电式混动' },
                  { value: 5, label: '柴油' },
                ]}
                width="sm"
                label="能源类型"
                fieldProps={{
                  onChange: (val: number) => {
                    if (val === 2) {
                      form.setFieldsValue({ environmentalType: '' });
                    }
                  },
                }}
                placeholder="请选择能源类型"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                disabled
                options={[
                  { value: '4', label: '国四' },
                  { value: '5', label: '国五' },
                  { value: '6', label: '国六' },
                ]}
                placeholder="请选择环保标准"
                fieldProps={{
                  // disabled: disableEnvironmental,
                  // showSearch: true,
                  optionFilterProp: 'label',
                }}
                width="sm"
                name="environmentalType"
                label="环保标准"
              />
              <ProFormText
                name="manufacturer"
                disabled
                width="sm"
                fieldProps={{ maxLength: 20 }}
                label="厂商"
                placeholder="请输入厂商"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                name="carYears"
                disabled
                width="sm"
                options={[
                  { value: 2017, label: '2017' },
                  { value: 2018, label: '2018' },
                  { value: 2019, label: '2019' },
                  { value: 2020, label: '2020' },
                  { value: 2021, label: '2021' },
                  { value: 2022, label: '2022' },
                  { value: '-', label: '-' },
                ]}
                placeholder="请选择年代款"
                label="年代款"
              />
              <ProFormText
                name="carEmission"
                disabled
                width="sm"
                fieldProps={{ maxLength: 20 }}
                label="排量"
                placeholder="请输入排量"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormDigit
                label="座位数"
                disabled
                name="seatsNum"
                width="sm"
                placeholder="请输入座位数"
                min={1}
              />
              <ProFormText
                name="otherMsg"
                disabled
                width="sm"
                fieldProps={{ maxLength: 20 }}
                label="其他"
                placeholder="请输入其他"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormDigit
                disabled
                name="totalPrice"
                width="sm"
                label="全包价（元）"
                placeholder="请输入全包价"
                fieldProps={{ min: 0, precision: 2 }}
              />
              <ProFormText
                name="color"
                label="颜色"
                disabled
                width="sm"
                placeholder="请输入颜色"
                fieldProps={{ maxLength: 20 }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                rules={[{ required: true }]}
                // request={async () => }
                options={libraryCarOptions}
                placeholder="请选择库存城市"
                name="cityName"
                width="sm"
                label="库存城市"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                  // labelInValue: true,
                  // mode: 'multiple',
                }}
              />
              <ProFormText
                disabled
                name="preferentialPrice"
                width="sm"
                label="优惠金额（元）"
                placeholder="请输入优惠金额"
                // fieldProps={{ min: 0, precision: 2 }}
              />
            </ProForm.Group>

            <ProForm.Group>
              {formCur?.licenseType === LICENSE_TYPE.PERSONAL && (
                <ProFormText name="licenseCompany" label="上牌方" disabled />
              )}
              {formCur?.licenseType === LICENSE_TYPE.COMPANY && (
                <ProFormSelect
                  // disabled={disableForm}
                  rules={[{ required: true }]}
                  options={licenseList}
                  name="licenseCompany"
                  placeholder="请选择上牌公司"
                  width="sm"
                  fieldProps={{
                    showSearch: true,
                    optionFilterProp: 'label',
                    labelInValue: true,
                    onChange: (item) => {
                      if (item?.value) {
                        // 设置上牌城市
                        form.setFieldsValue({
                          carCityDetail: {
                            label: item?.licenseCity,
                            value: item?.licenseCityCode,
                          },
                        });
                      } else {
                        form.setFieldsValue({
                          carCityDetail: null,
                          licenseCompany: null,
                        });
                      }
                    },
                    // mode: 'multiple',
                  }}
                  label="上牌方"
                />
              )}
              <ProFormSelect
                // disabled={disableForm}
                rules={[{ required: true }]}
                name="carCityDetail"
                width="sm"
                disabled={formCur?.licenseType === LICENSE_TYPE.COMPANY}
                fieldProps={{
                  optionFilterProp: 'label',
                  labelInValue: true,
                  options: licenseCityList,
                }}
                placeholder="请选择上牌城市"
                label="上牌城市"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                // disabled={disableForm}
                rules={[{ required: true }]}
                options={storeList}
                name="applyStore"
                width="sm"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                  labelInValue: true,
                  onChange: (item) => {
                    if (item?.value) {
                      // 设置申请城市
                      form.setFieldsValue({
                        applyCityDetail: {
                          label: item?.saleCity,
                          value: item?.saleCityCode,
                        },
                      });
                    } else {
                      form.setFieldsValue({
                        applyStore: null,
                        applyCityDetail: null,
                      });
                    }
                  },
                  // mode: 'multiple',
                }}
                placeholder="请选择申请门店"
                label="申请门店"
              />
              <ProFormSelect
                rules={[{ required: true }]}
                request={async () => cityList}
                placeholder="请选择申请城市"
                // disabled={disableForm}
                name="applyCityDetail"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                  labelInValue: true,
                  disabled: true,
                  // value: applyStore?.saleCity
                  //   ? { label: applyStore?.saleCity, value: applyStore?.saleCityCode }
                  //   : null,
                  // mode: 'multiple',
                  onChange: (item) => {
                    if (item?.value && form?.getFieldValue('carNo')) {
                      getPromotionByCarNoAndApplyCityCode({
                        applyCityCode: item.value,
                        carNo: form?.getFieldValue('carNo'),
                      }).then((res) => {
                        form?.setFieldsValue({
                          preferentialPrice: res.data?.discount || '-',
                        });
                        form.validateFields(['carNo']);
                      });
                    }
                  },
                }}
                width="sm"
                label="申请城市"
              />
            </ProForm.Group>
          </DividerTit>
        )}

        <>
          {repeatError || dayCheck.code ? (
            <Alert
              showIcon
              message={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      textAlign: 'left',
                      width: '90%',
                      flex: 1,
                      marginLeft: 10,
                      fontSize: 12,
                    }}
                  >
                    <span>错误提示：</span>

                    {repeatError && (
                      <span style={{ color: 'red' }}>
                        提交失败，该车架号与
                        {repeatError?.map((item, index) => {
                          return (
                            <a
                              key={item.userName}
                              onClick={() => skipLease({ userName: item.userName })}
                              style={{ whiteSpace: 'nowrap' }}
                            >
                              {item.userName}
                              {index === repeatError.length - 1 ? '' : ','}
                            </a>
                          );
                        })}
                        {/* <a onClick={() => skipLease(repeatError)}>{repeatError?.userName}</a> */}
                        客户（
                        {repeatError?.map((item, index) => {
                          return (
                            <a
                              key={item.orderNo}
                              onClick={() => skipLease({ orderNo: item.orderNo })}
                              style={{ whiteSpace: 'nowrap' }}
                            >
                              {item.orderNo}
                              {index === repeatError.length - 1 ? '' : ','}
                            </a>
                          );
                        })}
                        {/* <a onClick={() => skipLease(repeatError)}>{repeatError?.orderNo}</a> */}
                        订单）绑车重复。
                      </span>
                    )}
                    {dayCheck.code === 10 && (
                      <span style={{ color: 'red' }}>
                        {/* 车辆校验失败，出厂日期不符，请联系工作人员。 */}
                        车辆校验失败，出厂日期不符，制造日期距离绑车当日
                        {dayCheck.carTyingDays}
                        天，请联系工作人员。
                      </span>
                    )}
                    {dayCheck.code === 20 && (
                      <span style={{ color: 'red' }}>
                        车辆校验失败！方案已失效，请联系工作人员。
                      </span>
                    )}
                    {dayCheck.code === 30 && (
                      <span style={{ color: 'red' }}>
                        车辆校验失败！车型未准入，请联系工作人员。
                      </span>
                    )}
                  </div>
                </div>
              }
              type="warning"
            />
          ) : (
            <></>
          )}
        </>
      </ModalForm>
      <ModalForm
        title="是否确认车辆信息无误？"
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
          okText: '我已确认无误',
        }}
        visible={optVisible}
        onVisibleChange={handleOptVisible}
        onFinish={async () => {
          console.log(allFileList, '----allFile');
          setRepeatError(null); // 清空error
          const mapUploadFile = convertUploadFileList(allFileList, [
            'drivingLicense',
            'carRegistration',
          ]);

          let licenseCompany = formReset?.licenseCompany?.label;
          let licenseCompanyId = formReset?.licenseCompany?.value;

          if (formCur?.licenseType === LICENSE_TYPE.PERSONAL) {
            licenseCompany = formCur?.licenseCompany?.label;
            licenseCompanyId = formCur?.licenseCompany?.value;
          }

          const params: any = {
            orderNo,
            ...formReset,
            ...mapUploadFile,
            oosFilePath: formReset?.certificateFile
              ? formReset.certificateFile[0]?.url
              : formCur?.oosFilePath,
            channelId: formReset?.channelDetail?.value,
            licenseType: formCur?.licenseType,
            licenseCompany,
            licenseCompanyId,
            applyStore: formReset?.applyStore?.label,
            applyStoreId: formReset?.applyStore?.value,
          };
          // 渠道门店帐号通过和ocr的字段差异判断需不需要人工复核环节
          isChannelNeedReSureHandle(params, formReset);
          console.log('---看一下上传', params?.modifyFields);
          const res = await bindingCar(params as BindCarParams);
          if (res?.data?.result) {
            if (isChannelStoreUser(access) && !res?.data?.isAutoPass) {
              Modal.warning({
                title: '车辆信息非自动识别，待金融复核绑车',
                icon: <ExclamationCircleOutlined style={{ color: 'red' }} />,
                centered: true,
                okText: '我知道了',
                maskClosable: true,
              });
              setTimeout(() => {
                Modal.destroyAll();
              }, 5000);
            } else {
              message.success('添加成功');
            }
            props.onOk();
          } else {
            setRepeatError(res?.data?.userInfoList);
          }
          return true;
        }}
      >
        <div>
          <p>如正确无误，车辆信息将录入电子合同</p>
          {access.hasAccess('btn_change_carinfo_businessMng_leaseList') ? (
            <p>
              如有变更，请
              <a
                onClick={() => {
                  handleOptVisible(false);
                  handleShowMoreDetail(true);
                  setIsFirst(true);
                }}
              >
                变更车辆信息
              </a>
            </p>
          ) : (
            <p>如有变更，请联系工作人员变更车辆信息</p>
          )}
        </div>
      </ModalForm>
    </>
  );
};

export default BindCarModalNew;
