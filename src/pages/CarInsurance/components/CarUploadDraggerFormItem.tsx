import ImagePreview from '@/components/ImagePreview';
import LoadingButton from '@/components/LoadingButton';
import { getAuthHeaders } from '@/utils/auth';
import { getBlob, saveAs } from '@/utils/utils';
import type { FormInstance } from 'antd';
import { message, Modal, Radio, Upload } from 'antd';
import imageCompression from 'browser-image-compression';
import dayjs from 'dayjs';
import React, { memo, useRef } from 'react';
import ReactDOM from 'react-dom';
import { vehicleOcrInfo } from '../services';
import type { IcarInfoItem } from '../type';
import { getBaseUploadAction, getOssPathFromUrl, transformUrlTofileList } from '../utils';
import './CarUploadDraggerFormItem.less';
const { Dragger } = Upload;

ReactDOM.unstable_batchedUpdates(() => {});

const download = (url: string, filename: string) => {
  message.loading('下载文件中...');
  getBlob(url, (blob: Blob) => {
    saveAs(blob, filename);
    message.destroy();
  });
};

type Props = {
  ossType: 'vehicleLicenseOssUrlList' | 'insuranceSlipOssUrlList';
  form: FormInstance<IcarInfoItem>;
  onChange?: (val: string[]) => void;
  value?: string[];
  // 相对路径
  ossUrlList?: string[];
  handleTipVisible?: (visible: boolean) => void;
};
const CarUploadDragger: React.FC<Props> = (props) => {
  const imagePreviewRef = useRef<any>(null);
  const { ossType, form, onChange, value, ossUrlList, handleTipVisible } = props;
  //
  // 识别类型选择，弹窗相关
  const [visibleOfFileTypeChoose, setVisibleOfFileTypeChoose] = React.useState(false);
  //
  //
  const [currentFileType, setCurrentFileType] = React.useState(1);
  const changeOCRFileType = (e: any) => {
    setCurrentFileType(e.target.value);
  };
  const [ocr, setOcr] = React.useState<any>({ filePath: '' });
  const [ocrLoading, setOcrLoading] = React.useState(false);
  const [ocrResultFilePathList, setOcrResultFilePathList] = React.useState<string[]>([]);
  //
  const handleOCR = async (file: any) => {
    console.log('handleOCR', file);
    // 车辆证件，以选择框选择为准，购车发票：1，行驶证：3, 合格证：13
    // 投保单：6
    const orcType: any = ossType === 'vehicleLicenseOssUrlList' ? currentFileType : 6;
    const { filePath } = file as any;
    // 投保单中，车架号/车牌号，是否与表单一致
    let isPlateDiff: boolean = false;
    let isVinDiff: boolean = false;
    //
    setOcrLoading(true);
    const res: any =
      (await vehicleOcrInfo({
        filePath,
        fileType: orcType,
      }).catch(() => {})) || {};
    console.log('ocr res', res);
    setOcrLoading(false);
    if (res?.success === true || res?.ret === 0) {
      const { data } = res;
      if (data?.ocrValueList && data?.ocrValueList?.length && data?.ocrValueList?.length > 0) {
        setOcrResultFilePathList((prev) => {
          // return [...prev, filePath];
          // 只展示最新一次识别结果
          return [filePath];
        });
        const { ocrValueList } = data;
        const ocrValue = {};
        // 判断投保单中车辆和表单车辆是否一致
        if (orcType === 6) {
          try {
            const plateNo = ocrValueList?.find((item: any) => item?.fieldName === '车牌号')
              ?.fieldValue;
            const vin = ocrValueList?.find((item: any) => item?.fieldName === '车架号')?.fieldValue;
            // 表单值
            const plateNoOfForm = form?.getFieldValue('plateNo'); //车牌或新车合格证号
            const vinOfForm = form?.getFieldValue('vin');
            // 原表单有值才会校验一致
            if (plateNoOfForm || vinOfForm) {
              let plateNoDiff = false;
              let vinDiff = false;
              if (plateNoOfForm && plateNo) {
                plateNoDiff = plateNoOfForm !== plateNo;
              }
              if (vinOfForm && vin) {
                vinDiff = vinOfForm !== vin;
              }
              handleTipVisible?.(plateNoDiff || vinDiff);
              isPlateDiff = plateNoDiff;
              isVinDiff = vinDiff;
            }
          } catch (error) {}
        }
        ocrValueList?.forEach((item: any) => {
          // 购车发票：1
          if (orcType === 1) {
            // 新车合格证号，formeKey=plateNo
            if (item?.fieldName === '合格证号' && item?.fieldValue) {
              ocrValue['plateNo'] = item?.fieldValue;
            }
            // 车架号， formaKey=vin
            else if (item?.fieldName === '车架号' && item?.fieldValue) {
              ocrValue['vin'] = item?.fieldValue;
            }
            // 发动机号， formaKey=engineNumber
            else if (item?.fieldName === '发动机号' && item?.fieldValue) {
              ocrValue['engineNumber'] = item?.fieldValue;
            }
            // 行车证车主， formaKey=vehicleLicenseOwner
            else if (item?.fieldName === '购买方名称' && item?.fieldValue) {
              ocrValue['vehicleLicenseOwner'] = item?.fieldValue;
            }
          }
          // 行驶证：3
          else if (orcType === 3) {
            // 车牌号，formeKey=plateNo
            if (item?.fieldName === '车牌号' && item?.fieldValue) {
              ocrValue['plateNo'] = item?.fieldValue;
            }
            // 车架号， formaKey=vin
            else if (item?.fieldName === '车架号' && item?.fieldValue) {
              ocrValue['vin'] = item?.fieldValue;
            }
            // 发动机号， formaKey=engineNumber
            else if (item?.fieldName === '发动机号' && item?.fieldValue) {
              ocrValue['engineNumber'] = item?.fieldValue;
            }
            // 行车证车主， formaKey=vehicleLicenseOwner
            else if (item?.fieldName === '所有人' && item?.fieldValue) {
              ocrValue['vehicleLicenseOwner'] = item?.fieldValue;
            }
          }
          // 合格证：13
          else if (orcType === 13) {
            // 合格证号，formeKey=plateNo
            if (item?.fieldName === '合格证编号' && item?.fieldValue) {
              ocrValue['plateNo'] = item?.fieldValue;
            }
            // 车架号， formaKey=vin
            else if (item?.fieldName === '车架号' && item?.fieldValue) {
              ocrValue['vin'] = item?.fieldValue;
            }
            // 发动机号， formaKey=engineNumber
            else if (item?.fieldName === '发动机号' && item?.fieldValue) {
              ocrValue['engineNumber'] = item?.fieldValue;
            }
          }
          // 投保单：6
          else if (orcType === 6) {
            // 车牌号， formaKey=plateNo
            if (item?.fieldName === '车牌号' && item?.fieldValue) {
              // 投保单中车牌号与原表单车牌号不一致，则不更新表单
              if (!isPlateDiff) {
                ocrValue['plateNo'] = item?.fieldValue;
              }
            }
            // 车架号， formaKey=vin
            else if (item?.fieldName === '车架号' && item?.fieldValue) {
              // 投保单中车架号与原表单车架号不一致，则不更新表单
              if (!isVinDiff) {
                ocrValue['vin'] = item?.fieldValue;
              }
            }
            // 发动机号， formaKey=engineNumber
            else if (item?.fieldName === '发动机号' && item?.fieldValue) {
              ocrValue['engineNumber'] = item?.fieldValue;
            }
            // 被保险人， formaKey=assuredName
            else if (item?.fieldName === '被保险人名称' && item?.fieldValue) {
              ocrValue['assuredName'] = item?.fieldValue;
            }
            // 商业险开始日期， formaKey=commercialInsuranceStartDate
            else if (item?.fieldName === '商业险开始时间' && item?.fieldValue) {
              try {
                ocrValue['commercialInsuranceStartDate'] = `${dayjs(item?.fieldValue).format(
                  'YYYY-MM-DD',
                )} 00:00:00`;
              } catch (error) {}
            }
            // 商业险结束日期， formaKey=commercialInsuranceEndDate
            else if (item?.fieldName === '商业险结束时间' && item?.fieldValue) {
              try {
                ocrValue['commercialInsuranceEndDate'] = `${dayjs(item?.fieldValue).format(
                  'YYYY-MM-DD',
                )} 23:59:59`;
              } catch (error) {}
            }
            // 商业险保费， formaKey=commercialInsurancePremium
            else if (item?.fieldName === '商业险保费' && item?.fieldValue) {
              ocrValue['commercialInsurancePremium'] = item?.fieldValue;
            }
            // 车船税金额， formaKey=vehicleTaxAmount
            else if (item?.fieldName === '代收车船税' && item?.fieldValue) {
              ocrValue['vehicleTaxAmount'] = item?.fieldValue;
            }
          }
          form?.setFieldsValue(ocrValue);
        });
      }
      message.success('已识别');
      if (orcType === 1 || orcType === 3 || orcType === 13) {
        setVisibleOfFileTypeChoose(false);
      }
      // 重置识别类型为默认值
      setCurrentFileType(1);
    }
  };
  //
  //
  const renderOcr = (file: any) => {
    const currentFile = {
      ...file,
      filePath: file?.filePath || file?.response?.data?.filePath,
    };

    const ocrJsx = (
      <LoadingButton
        size="small"
        type="link"
        onClick={async () => {
          setOcr(currentFile);
          if (ossType === 'vehicleLicenseOssUrlList') {
            setVisibleOfFileTypeChoose(true);
          } else if (ossType === 'insuranceSlipOssUrlList') {
            await handleOCR(currentFile).catch(() => {});
          }
        }}
      >
        OCR
      </LoadingButton>
    );
    return ocrJsx;
  };
  //
  const renderDelete = (actions: any) => {
    const deleteJsx = (
      <span
        className="btn-action-delete"
        onClick={() => {
          actions.remove();
        }}
      >
        删除
      </span>
    );
    return deleteJsx;
  };

  //
  return (
    <>
      <Dragger
        listType="text"
        action={getBaseUploadAction('/base/oss/common/uploadfile')}
        style={{ width: 480, height: 150 }}
        data={{
          attachment: false,
          acl: 'PUBLIC_READ',
          destPath: 'insurance/policy/vehicleLicense/',
        }}
        defaultFileList={transformUrlTofileList(value || [])?.map((v, index) => {
          const filePath = ossUrlList?.[index] || '';
          // console.log('filePath', filePath);
          return {
            ...v,
            filePath,
          };
        })}
        multiple={true}
        headers={{ ...getAuthHeaders() }}
        onChange={({ fileList }) => {
          const successUrlList: string[] = [];
          const successOssUrlList: string[] = [];
          fileList.forEach((file) => {
            const { response } = file as any;
            const { code, data } = response || {};
            const { netWorkPath } = data || {};
            // 接口上传改变
            if (code === 200) {
              // 上传成功
              file.thumbUrl = netWorkPath;
              file.url = netWorkPath; // 有了这个就可以 新标签页打开
            }

            // 包括了接口上传和 没有接口上传的
            if (file.url) {
              successUrlList.push(file.url);
              successOssUrlList.push(getOssPathFromUrl(file.url));
            }
          });
          onChange?.(successUrlList);
          form.setFieldsValue({
            [ossType]: successOssUrlList,
          });
        }}
        itemRender={(originNode, file, fileList, actions) => {
          const urlList = fileList.map((item) => item.url || '');
          if (file.url) {
            // 上传成功的展示出来
            const { name: fileName, url = '', uid } = file;
            const { filePath } = file as any;
            const hasOcrResult =
              ocrResultFilePathList.includes(filePath) ||
              ocrResultFilePathList.includes(file?.response?.data?.filePath);
            return (
              <div style={{ display: 'flex', gap: 6, marginTop: 10 }} key={uid}>
                <a
                  // 支持预览的就预览，不支持的就下载
                  className="file-name-text"
                  title={fileName || ''}
                  onClick={() => {
                    imagePreviewRef.current?.previewFile({ url, urlList });
                  }}
                >
                  {fileName}
                </a>
                {
                  <span className="btn-action-ocr" key={Math.random()} color="success">
                    {hasOcrResult ? '已识别' : ''}
                  </span>
                }
                {renderOcr(file)}
                <span
                  onClick={() => {
                    download(url, fileName);
                  }}
                  className="btn-action-download"
                >
                  下载
                </span>
                {renderDelete(actions)}
              </div>
            );
          } else {
            // 有进度条样式
            return originNode;
          }
        }}
        beforeUpload={(file) => {
          //
          if ([',', ':', '，', '：'].some((item) => file.name?.includes(item))) {
            // 含有其中任何一个 都不能上传
            message.error('文件名称不能含有中英文逗号和冒号');
            return false || Upload.LIST_IGNORE;
          }
          // 只限制图片的大小，其他文件大小不限制
          if (file.size > 4 * 1024 * 1024) {
            const imgTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            if (imgTypes.includes(file.type)) {
              message.info(`${file.name}文件大于4m,正在被压缩`);
              return imageCompression(file, {
                maxSizeMB: 4,
                useWebWorker: true,
                onProgress: (progress) => {
                  console.log('progress', progress);
                },
              });
            }
          }
          return true;
        }}
      >
        <p style={{ padding: '20px 0px' }}>点击或拖拽上传</p>
      </Dragger>
      <ImagePreview ref={imagePreviewRef} />
      <Modal
        open={visibleOfFileTypeChoose}
        title="请选择该文件类型"
        onOk={() => handleOCR(ocr)}
        onCancel={() => setVisibleOfFileTypeChoose(false)}
        okText="确定"
        cancelText="取消"
        okButtonProps={{ loading: ocrLoading }}
      >
        <Radio.Group onChange={changeOCRFileType} value={currentFileType}>
          <Radio value={1}>购车发票</Radio>
          <Radio value={3}>行驶证</Radio>
          <Radio value={13}>合格证</Radio>
        </Radio.Group>
      </Modal>
    </>
  );
};
export default memo(CarUploadDragger);
