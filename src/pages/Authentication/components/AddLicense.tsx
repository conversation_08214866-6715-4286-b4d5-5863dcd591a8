import { Form } from 'antd';
import React, { memo } from 'react';
import UploadImage from './UploadImage';
import styles from '../styles/index.less';
import { ProFormText } from '@ant-design/pro-form';

const AddLicense = () => {
  return (
    <div style={{ display: 'flex' }}>
      <div style={{ width: 500 }} className={styles.license}>
        <div>
          <span style={{ color: 'red' }}>*</span> 企业营业执照
          <a>需原件拍照,四角齐全,图片需小于5M,格式为.pdf,.png,.jpg,.jpeg,.webp</a>
        </div>
        <Form.Item name="tradingCertificateFilePath" rules={[{ required: true }]}>
          <UploadImage />
        </Form.Item>
      </div>
      <div style={{ width: 520, marginTop: 20 }}>
        <ProFormText
          name={'certiNo'}
          label="统一社会信用代码"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
        <ProFormText
          name={'certiName'}
          label="借款企业名称"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
        <ProFormText
          name={'legalPerson'}
          label="借款企业法人名称"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
        <ProFormText
          name={'legalPersonCertiNo'}
          label="借款企业法人身份证号"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
        <ProFormText
          name={'enterpriseAddress'}
          label="借款企业地址"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
      </div>
    </div>
  );
};
export default memo(AddLicense);
