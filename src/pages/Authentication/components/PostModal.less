.row-divider {
  margin: 12px 0;
}
.mb-20 {
  margin-bottom: 20px;
}
.mt-15 {
  margin-top: 15px;
}
.mb-15 {
  margin-bottom: 15px;
}
.mb-10 {
  margin-bottom: 10px;
}
.red-text {
  color: red;
}
.primary-text {
  color: #1677ff;
}
.gray-text {
  color: rgba(0, 0, 0, 0.45);
}
.title-row {
  margin-bottom: 20px;
}
.flex-wrap {
  display: flex;
  width: 100%;
  .img-list {
    width: 700px;
    overflow: hidden;
    .img-item {
      display: flex;
      width: 100%;
      overflow: hidden;
      color: #1677ff;
      .list-wrap {
        display: flex;
        width: 100%;
        .list-icon {
          display: inline-block;
          width: 26px;
          cursor: pointer;
        }
        .list-ellipsis-text {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          span {
            cursor: pointer;
          }
        }
      }
    }
  }
  .upload-pannel-wrapper {
    flex: 1;
    .upload-pannel-title {
      margin-bottom: 10px;
      margin-left: 8px;
      color: #888;
      font-size: 14px;
    }
    .ant-upload-wrapper {
      display: flex;
      .ant-upload-drag {
        flex: 1;
        margin-left: 5px;
      }
      .ant-upload-list {
        // flex: 1;
        width: 400px;
        margin-left: 10px;
      }
    }
  }
}
