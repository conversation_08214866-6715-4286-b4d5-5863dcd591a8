/*
 * @Author: your name
 * @Date: 2021-04-19 16:44:20
 * @LastEditTime: 2024-05-15 16:40:28
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/components/AddLoanResourceModal.tsx
 */
import { CommonDraggleUpload } from '@/components/ReleaseCom';
// import dayjs from 'dayjs';
import { LOAN_MANAGE_CODE } from '@/enums';
import { convertUploadFileList } from '@/utils/utils';
import {
  ModalForm,
  // ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Button, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import type { AddApplyDataParams } from '../data';
import { addApplyData } from '../service';
import { ORDER_STATUS } from './LoanLeaseInfo';

export type AddLoanResourceModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  // deadLine: string;
  userNo: string;
  formEdit?: {};
  status?: number | null;
  otherMsgDefault?: string;
  isEdit: boolean;
  noGuaranteeTypes: boolean;
};

const AddLoanResourceModal: React.FC<AddLoanResourceModalProps> = (props) => {
  const { lendingNo, orderNo }: any = history.location.query;
  const { userNo, formEdit, otherMsgDefault, isEdit, noGuaranteeTypes, status }: any = props;
  const [form] = Form.useForm();
  const [isDrawer, setIsDrawer] = useState(false);

  const [allFileList, handleFileList] = useState(formEdit || {});
  // console.log(allFileList);
  useEffect(() => {
    handleFileList(formEdit);
    form.setFieldsValue({
      ...formEdit,
      otherMsg: otherMsgDefault,
    });
    // console.log(formEdit);
    return () => {
      handleFileList({});
    };
  }, [formEdit]);
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  // 必填字段逻辑判断，isDrawer允许保存，默认必填（购车发票，交强险保单，商业险保单，担保函（如果是担保的订单必填），其他附件）
  const fieldRequiredJudge = (key: string) => {
    //  待一审(10)状态判断逻辑，新增GPS为必填项
    if (key === 'gpsSysScreenshot')
      return status === LOAN_MANAGE_CODE.WAIT_FIRST_INSTANCE ? !isDrawer : false;
    // 担保函（如果是担保的订单必填）
    if (key === 'guaranteeLetter' && noGuaranteeTypes) return false;
    // isDrawer允许保存
    return !isDrawer;
  };

  return (
    <>
      <ModalForm
        title={`${isEdit ? '编辑' : '添加'}请款资料`}
        // width={600}
        layout="horizontal"
        visible={props.modalVisible}
        onVisibleChange={props.onVisibleChange}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            handleFileList({});
          },
          // onPressEnter: (e) => {
          //   e.preventDefault();
          // },
        }}
        form={form}
        submitter={{
          render: (_, defaultDoms) => {
            return [
              defaultDoms[0],
              <Button
                key="update"
                type="primary"
                ghost
                onClick={() => {
                  // 仅保存允许忽略必填校验
                  setIsDrawer(true);
                  setTimeout(() => {
                    form.submit();
                  }, 500);
                }}
              >
                仅保存
              </Button>,
              // <Button
              //   key="update"
              //   type="primary"
              //   onClick={() => {
              //     setIsDrawer(false);
              //     form.submit();
              //   }}
              // >
              //   提交
              // </Button>,
              // 挂起状态不展示提交按钮
              status !== ORDER_STATUS.HANG_UP && defaultDoms[1],
            ];
          },
        }}
        onFinish={async (values) => {
          try {
            console.log(isDrawer, allFileList);
            // 转换数据为后端需要的格式，单独处理otherEnclosure，'carPurchaseInvoice',等
            const mapUploadFile = convertUploadFileList(allFileList, [
              'otherEnclosure',
              'carPurchaseInvoice',
              'purchaseTaxReceipt',
              'drivingLicense',
              'carRegistration',
              'trafficInsurance',
              'businessInsurance',
              'gpsSysScreenshot',
              'guaranteeLetter',
            ]);
            console.log(mapUploadFile);
            // console.log({
            //   ...values,
            //   ...mapUploadFile,
            //   orderNo,
            //   lendingNo,
            //   userNo,
            //   operateType: 'submit',
            // });
            setIsDrawer(false);
            addApplyData({
              ...values,
              ...mapUploadFile,
              orderNo,
              lendingNo,
              userNo,
              operateType: isDrawer ? 'save' : 'submit',
            } as AddApplyDataParams).then(() => {
              message.success('添加成功');
              props.onOk();
              return true;
            });
            // eslint-disable-next-line no-empty
          } catch (error) {
            message.error('稍后重试');
          }
        }}
      >
        <CommonDraggleUpload
          // listType="text"
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="购车发票"
          labelCol={{ span: 4 }}
          name="carPurchaseInvoice"
          fileListEdit={formEdit?.carPurchaseInvoice}
          max={5}
          mapFileList={mapFileList}
          size={50}
          multiple={true}
          rules={[
            {
              required: fieldRequiredJudge('carPurchaseInvoice'),
              message: '请上传购车发票',
            },
          ]}
        />

        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="购置税票"
          listType="text"
          labelCol={{ span: 4 }}
          name="purchaseTaxReceipt"
          max={5}
          mapFileList={mapFileList}
          size={50}
          multiple={true}
          fileListEdit={formEdit?.purchaseTaxReceipt}
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
        />
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="行驶证"
          labelCol={{ span: 4 }}
          name="drivingLicense"
          max={5}
          listType="text"
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          mapFileList={mapFileList}
          size={50}
          multiple={true}
          fileListEdit={formEdit?.drivingLicense}
        />
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="车辆登记证"
          labelCol={{ span: 4 }}
          name="carRegistration"
          max={5}
          listType="text"
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          mapFileList={mapFileList}
          fileListEdit={formEdit?.carRegistration}
          size={50}
          multiple={true}
        />
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="交强险保单"
          labelCol={{ span: 4 }}
          name="trafficInsurance"
          max={5}
          listType="text"
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          mapFileList={mapFileList}
          fileListEdit={formEdit?.trafficInsurance}
          size={50}
          multiple={true}
          rules={[
            {
              required: fieldRequiredJudge('trafficInsurance'),
              message: '请上传交强险保单',
            },
          ]}
        />
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="商业险保单"
          labelCol={{ span: 4 }}
          name="businessInsurance"
          max={5}
          listType="text"
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          mapFileList={mapFileList}
          size={50}
          multiple={true}
          fileListEdit={formEdit?.businessInsurance}
          rules={[
            {
              required: fieldRequiredJudge('businessInsurance'),
              message: '请上传商业险保单',
            },
          ]}
        />
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="GPS系统截图"
          labelCol={{ span: 4 }}
          name="gpsSysScreenshot"
          listType="text"
          max={5}
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          mapFileList={mapFileList}
          fileListEdit={formEdit?.gpsSysScreenshot}
          size={50}
          multiple={true}
          rules={[
            {
              required: fieldRequiredJudge('gpsSysScreenshot'),
              message: '请上传GPS系统截图',
            },
          ]}
        />
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="担保函"
          labelCol={{ span: 4 }}
          name="guaranteeLetter"
          rules={[
            {
              required: fieldRequiredJudge('guaranteeLetter'),
              message: '请上传担保函',
            },
          ]} //非担保类型的必填
          listType="text"
          max={5}
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          mapFileList={mapFileList}
          fileListEdit={formEdit?.guaranteeLetter}
          size={50}
          multiple={true}
        />
        <ProFormTextArea
          name="otherMsg"
          labelCol={{ span: 4 }}
          width="md"
          fieldProps={{ maxLength: 500 }}
          label="其他"
          placeholder="请输入"
          // rules={[{ required: true }]}
        />
        <CommonDraggleUpload
          extra="支持扩展名：.pdf.png.jpg.jpeg.gif.bmp,.zip,.rar,.7z单个文件最大100M"
          label="其他附件"
          labelCol={{ span: 4 }}
          name="otherEnclosure"
          max={30}
          sceneCode={1}
          listType="text"
          size={100}
          multiple={true}
          fileListEdit={formEdit?.otherEnclosure}
          mapFileList={mapFileList}
          accept=".pdf,.png,.jpg,.jpeg,.gif,.bmp,.zip,.rar,.7z"
          rules={[
            {
              required: fieldRequiredJudge('otherEnclosure'),
              message: '请上传其他附件',
            },
          ]}
          previewType={'multiple'} // 支持多图预览
        />
      </ModalForm>
    </>
  );
};

export default AddLoanResourceModal;
