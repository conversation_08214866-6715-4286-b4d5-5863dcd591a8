/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-04-28 15:38:33
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Debt/components/DebtLoanInfo.tsx
 */
import React from 'react';
import { Card, Row, Col } from 'antd';
import globalStyle from '@/global.less';

interface DebtInfoProps {
  // orderNo: string;
  dataInfo: {
    conversionNo: string;
    accountsReceivable: number;
    principal: number;
    interest: number;
    accountName: string;
    accountNumber: string;
    loadFromName: string;
    loadToName: string;
    status: number;
    startingTime: string;
    dealTime: string;
    billNo: string;
  };
}

const DebtLoanInfo: React.FC<DebtInfoProps> = (props) => {
  const data = props.dataInfo;
  const debtInfo = {
    lendingNo: '放款流水号',
    type: '放款项',
    amount: '放款金额',
    lendingMaster: '放款主体',
    receiptMaster: '收款主体',
    fundFlow: '是否发生资金流',
    lendingModel: '放款方式',
    lendingCycle: '放款周期',
    lendingTime: '放款时间',
    status: '放款状态',
  };
  const itemMap = {
    type: {
      1: '进件',
      2: '债转',
      3: '代偿',
    },
    fundFlow: {
      true: '是',
      false: '否',
    },
    lendingModel: {
      1: '线上',
      2: '线下',
    },
    status: {
      0: '放款失败',
      1: '待放款',
      2: '放款成功',
      '-1': '已取消',
    },
  };
  return (
    <Card title="债权转让放款信息" className={globalStyle.mt30}>
      <Row>
        {Object.keys(debtInfo).map((item) => {
          let value = '';
          if (data && data[item]) {
            value = (itemMap[item] ? itemMap[item][data[item]] : data[item]) || '';
          }
          return (
            <Col span={8} key={`${item}a`}>
              <div style={{ lineHeight: '40px' }}>
                <span>{debtInfo[item]}:</span>
                <span className={globalStyle.ml20}>{value}</span>
              </div>
            </Col>
          );
        })}
      </Row>
    </Card>
  );
};

export default DebtLoanInfo;
