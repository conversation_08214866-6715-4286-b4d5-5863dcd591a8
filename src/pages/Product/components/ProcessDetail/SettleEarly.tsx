/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-10-31 20:11:24
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/ProcessDetail/SettleEarly.tsx
 */
import { ProFormText } from '@ant-design/pro-form';
import { Col, Form, Input, Radio, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';

type SettleEarlyProps = {
  settleEarlyData?: any;
  name?: string;
};

const SettleEarly: React.FC<SettleEarlyProps> = ({ settleEarlyData, name }) => {
  // const { settleEarlyData } = props;
  const [form] = Form.useForm();
  useEffect(() => {
    if (settleEarlyData) {
      form.setFieldsValue({
        ...settleEarlyData,
        operatorTime: settleEarlyData.operatorTime
          ? dayjs(settleEarlyData.operatorTime).format('YYYY-MM-DD hh:mm:ss')
          : '-',
      });
    }
  }, [settleEarlyData]);
  return (
    <Form
      form={form}
      labelCol={{ span: 12 }}
      wrapperCol={{ span: 16 }}
      labelAlign="right"
      name={name}
    >
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item name="settleEarly" label="是否允许提前结清">
            <Radio.Group disabled>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>

        <Col span={10} offset={1}>
          <Form.Item name="handlingCharge" label="是否收取违约金">
            <Radio.Group disabled>
              <Radio value={true}>收取</Radio>
              <Radio value={false}>不收取</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={10} offset={1}>
          <Form.Item
            labelCol={{ offset: 0 }}
            name="minPaidTerm"
            label="提前结清时间（已还大于等于x期）"
          >
            <Input disabled />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={10} offset={1}>
          <ProFormText label="操作人" name="operatorBy" readonly />
        </Col>
        <Col span={10} offset={1}>
          <ProFormText label="操作/生效时间" name="operatorTime" readonly />
        </Col>
      </Row>
    </Form>
  );
};

export default SettleEarly;
