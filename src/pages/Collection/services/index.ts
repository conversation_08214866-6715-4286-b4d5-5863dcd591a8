import { bizadminHeader } from '@/services/consts';
import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { IbillInfo } from '../data';
import type { I_submitCollectRelief } from '../types';

// 接入SEIM日志审计系统 发送日志
export async function reportLog(phone: string) {
  return request(`/bizadmin/seim/reportedData`, {
    method: 'POST',
    data: phone,
    headers,
  });
}

// 获取催收减免账单列表
export async function getCollectReliefList(data: { caseNo: string; orderNo: string }) {
  return request(
    `/bizadmin/bill/overdueCase/remission/billList?caseNo=${data.caseNo}&orderNo=${data.orderNo}`,
    {
      method: 'POST',
      headers: bizadminHeader,
    },
  );
}

// 获取催收回款账单列表
export async function getCollectBillList(data: { caseNo: string; orderNo: string }) {
  return request(
    `/bizadmin/bill/overdueCase/payment/billList?caseNo=${data.caseNo}&orderNo=${data.orderNo}`,
    {
      method: 'POST',
      headers: bizadminHeader,
    },
  );
}

// 还款
export async function calculateBill(data: {
  billInfoList: IbillInfo[];
  remissionList: object[];
  repayAmount: number;
}) {
  return request(`/bizadmin/bill/calculateBill`, {
    headers: bizadminHeader,
    method: 'POST',
    data,
    skipGlobalErrorTip: true,
  });
}

// 提交催收减免
export async function submitCollectRelief(data: I_submitCollectRelief) {
  return request(`/bizadmin/bill/overdueCase/remission/submit`, {
    headers: bizadminHeader,
    method: 'POST',
    data,
  });
}
