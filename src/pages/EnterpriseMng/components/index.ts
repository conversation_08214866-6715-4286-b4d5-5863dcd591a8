/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 14:24:24
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-02 17:56:38
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import ProductBaseInfo from "./ProductBaseInfo";
import ProductOverpayment from "./ProductOverpayment";
import ProductBillRecord from "./ProductBillRecord";
import ProductOrderDetailRecord from "./ProductOrderDetailRecord";
import ProductOverdueDetailRecord from "./ProductOverdueDetailRecord";
import ProductQuotaBankWaterBill from "./ProductQuotaBankWaterBill";
import ProductRelativeSubEnterprise from "./ProductRelativeSubEnterprise";
import SecondAccount from './SecondAccount';


export {
  ProductBaseInfo,
  ProductOverpayment,
  ProductBillRecord,
  ProductOrderDetailRecord,
  ProductOverdueDetailRecord,
  ProductQuotaBankWaterBill,
  ProductRelativeSubEnterprise,
  SecondAccount
}
