/*
 * @Author: alan771.tu
 * @Date: 2024-11-27 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-27 09:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/context/CarInsuranceManagementContext.tsx
 * @Description: 车险保单管理的context，用于存储交换上下文内容
 */
import { useDeepCompareMemoize } from '@/utils/utils';
import type { ParamsType } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { createContext, useCallback, useContext, useState } from 'react';
import { getCarInsurancePolicyList } from '../services';

export const CarInsuranceManagementContext = createContext<{
  loading: boolean;
  updateLoading: (loading: boolean) => void;
  request: (params?: ParamsType) => Promise<any>;
}>({
  loading: false,
  updateLoading: () => {},
  request: () => Promise.resolve(null),
});

export const useCarInsuranceManagementContext = () => {
  return useContext(CarInsuranceManagementContext);
};

export const CarInsuranceManagementProvider = (props: { children: React.ReactNode }) => {
  const { children } = props;
  const [loading, setLoading] = useState(false);
  const [cacheData, setCacheData] = useState<Record<string, any> | null>(null);

  const request = useCallback(
    async (params: any) => {
      if (!params) {
        return;
      }
      if (!params?.lendingTimeStart && !params?.lendingTimeEnd) {
        message.error('请选择放款日期范围');
        return;
      }
      setLoading(true);
      try {
        const res = await getCarInsurancePolicyList(params);
        setCacheData(res);
        return res;
      } catch (error) {
        setCacheData(null);
        message.error(error?.message || '获取保单列表失败');
      } finally {
        setLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [useDeepCompareMemoize(cacheData)],
  );

  return (
    <CarInsuranceManagementContext.Provider
      value={{
        loading,
        updateLoading: setLoading,
        request,
      }}
    >
      {children}
    </CarInsuranceManagementContext.Provider>
  );
};
