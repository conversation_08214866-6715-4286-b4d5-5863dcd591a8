/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-03-29 11:08:32
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-05-21 14:50:09
 * @FilePath: /code/lala-finance-biz-web/src/pages/Channel/components/ChannelAccount.tsx
 * @Description: ChannelAccount
 */
import HeaderTab from '@/components/HeaderTab/index';
import { CHANNEL_TYPES_MAP } from '@/enums';
import { getStoreAndApplyCityAll } from '@/pages/AfterLoan/services';
import { channelSearchFilter, storeSearchFilter } from '@/pages/BusinessLeaseMng';
import { getAllChannelNameEnum } from '@/services/enum';
import { isChannelStoreUser } from '@/utils/utils';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { ModalForm } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useAccess } from '@umijs/max';
import { Button, Form, message, Modal, Switch } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import type { AccountEnableProps, AccountListItem } from '../data';
import { getLeaseAccountList, leaseAccountEnable, leaseAccountUpdate } from '../service';
import './index.less';

const ChannelAccount: React.FC = () => {
  const access = useAccess();
  const [editForm] = Form.useForm();
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  // loading
  const [loading, handleLoading] = useState({});
  // 修改密码
  const [editPassword, handleEditPassword] = useState<any>({});
  const [disableForm] = useState<boolean>(false);
  // 启用/禁用账号
  const handleEnableAccount = async (params: AccountEnableProps) => {
    handleLoading((state) => {
      return { ...state, [params.pid]: true };
    });
    const data = await leaseAccountEnable(params).finally(() => {
      handleLoading((state) => {
        return { ...state, [params.pid]: false };
      });
    });
    if (data.success) {
      if (params.enable) message.success('启用成功！账号生效');
      actionRef.current?.reload();
    } else {
      if (params.enable) message.warning('启用失败！渠道暂不可用');
    }
  };

  const columns: ProColumns<AccountListItem>[] = [
    {
      title: '渠道编码',
      dataIndex: 'channelId',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelIds',
      hideInTable: true,
      valueType: 'select',
      request: () => {
        return getAllChannelNameEnum().then((res: any) => {
          // 渠道账号限制渠道
          channelSearchFilter({ access, formRef });
          // setChannelList(res);
          return res;
        });
      },
      render(_, record) {
        return record.channelName;
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        disabled: isChannelStoreUser(access) && !!access.currentUser?.channelCode,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      search: false,
      valueEnum: {
        ...CHANNEL_TYPES_MAP,
        '': '全部',
      },
    },
    {
      title: '门店',
      dataIndex: 'storeIds',
      hideInTable: true,
      request: () => {
        return getStoreAndApplyCityAll().then((res) => {
          // 渠道账号限制门店
          storeSearchFilter({ access, formRef });
          const result =
            res?.data?.storeList?.map(
              (item: { storeName: string; id: string; channelId: string }) => {
                return {
                  value: item.id?.toString(),
                  label: item.storeName,
                  channelId: item.channelId,
                };
              },
            ) || [];
          // setStoreList(result);
          return result;
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.extSource?.storeId,
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '门店',
      dataIndex: 'storeName',
      search: false,
    },
    {
      title: '权限角色',
      dataIndex: 'roleCode',
      hideInTable: true,
      valueEnum: {
        leaseChannelUser: '渠道员工',
        leaseStoreUser: '渠道门店员工',
      },
    },
    {
      title: '账号名称',
      dataIndex: 'accountName',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        2: { text: '禁用', status: 'error' },
        1: { text: '启用', status: 'success' },
      },
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 160,
      render: (_, record) => (
        <>
          {access.hasAccess('biz_btn_edit_code_biz_channel_pid') && (
            <Button
              type="link"
              onClick={() => {
                handleEditPassword({
                  visible: true,
                  accountName: record.accountName,
                  pid: record.pid,
                });
              }}
            >
              修改密码
            </Button>
          )}
          {access.hasAccess('biz_btn_enable_biz_channel_pid') && (
            <Switch
              checkedChildren="禁用"
              unCheckedChildren="启用"
              checked={record.status === '1'}
              loading={loading[record.pid]}
              onClick={async (status) => {
                const params = {
                  pid: record.pid,
                  enable: status,
                  channelId: record.channelId,
                  storeId: record.storeId,
                };
                if (!status) {
                  Modal.confirm({
                    title: '禁用渠道账号',
                    centered: true,
                    okText: '确定',
                    cancelText: '取消',
                    onOk: () => {
                      handleEnableAccount(params); //  禁用
                    },
                    content: (
                      <div style={{ marginTop: 15 }}>
                        你确定要禁用[{record.accountName}]的账号吗？
                      </div>
                    ),
                  });
                  return;
                }
                handleEnableAccount(params); //  启用
              }}
            />
          )}
        </>
      ),
    },
  ];
  console.log('ChannelAccount render!');
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<AccountListItem>
          actionRef={actionRef}
          formRef={formRef}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          request={(params) => getLeaseAccountList(params)}
          columns={columns}
          dateFormatter="string"
        />
      </PageContainer>
      {/* 修改密码 */}
      <ModalForm
        title="修改密码"
        layout="horizontal"
        form={editForm}
        width={500}
        modalProps={{
          centered: true,
          destroyOnClose: true,
        }}
        className="edit-channel-count-modal"
        visible={editPassword.visible}
        onVisibleChange={(status) => {
          if (!status) handleEditPassword({});
        }}
        onFinish={async (values) => {
          const res: any = await leaseAccountUpdate({
            password: values.password,
            pid: editPassword.pid,
          });
          if (res.success) {
            message.success('密码修改成功！');
            return true;
          } else {
            return false;
          }
        }}
      >
        <ProForm.Group>
          <ProFormText
            name="accountName"
            disabled={true}
            rules={[{ required: true }]}
            width="md"
            label="账号"
            initialValue={editPassword.accountName}
          />
          <ProFormText
            name="password"
            disabled={disableForm}
            rules={[
              {
                required: true,
                pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8}$/,
                message: '请输入8位字符（包含至少一个字母和一个数字）',
              },
            ]}
            width="md"
            label="密码"
            placeholder={'请输入新密码'}
          />
        </ProForm.Group>
      </ModalForm>
    </>
  );
};

export default ChannelAccount;
