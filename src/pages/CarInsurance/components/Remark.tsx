import type { FormInstance } from '@ant-design/pro-components';
import { ProForm, ProFormTextArea } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import React, { memo, useEffect } from 'react';

type Props = {
  remarkForm: FormInstance;
};
const Remark: React.FC<Props> = memo(({ remarkForm }) => {
  const { detailData, isEditable } = useModel('CarInsurance.carInsurance');
  const { remark } = detailData;
  useEffect(() => {
    remarkForm.setFieldsValue({
      remark: remark,
    });
  }, [remark, remarkForm]);
  return (
    <>
      <h3>备注</h3>
      <ProForm
        form={remarkForm}
        submitter={{ render: () => null }}
        disabled={!isEditable}
        autoFocusFirstInput={false}
      >
        <ProFormTextArea name="remark" fieldProps={{ maxLength: 100, showCount: true }} />
      </ProForm>
    </>
  );
});

export default Remark;
