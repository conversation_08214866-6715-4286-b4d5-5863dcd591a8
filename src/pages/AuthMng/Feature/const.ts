export enum SYS_TYPE {
  BIZ = 'bizAdmin',
  RISK = 'riskAdmin',
}
export const sysType = [
  { label: '业务', value: SYS_TYPE.BIZ },
  { label: '风控', value: SYS_TYPE.RISK },
];
export const sysTypeMap = {
  [SYS_TYPE.BIZ]: '业务',
  [SYS_TYPE.RISK]: '风控',
};
export enum METHOD_TYPE {
  GET = 'get',
  POST = 'post',
  PUT = 'put',
  DELETE = 'delete',
}
export const methodType = [
  { label: 'get', value: METHOD_TYPE.GET },
  { label: 'post', value: METHOD_TYPE.POST },
  { label: 'put', value: METHOD_TYPE.PUT },
  { label: 'delete', value: METHOD_TYPE.DELETE },
];

export const methodTypeMap = {
  [METHOD_TYPE.GET]: 'get',
  [METHOD_TYPE.POST]: 'post',
  [METHOD_TYPE.PUT]: 'put',
  [METHOD_TYPE.DELETE]: 'delete',
};
