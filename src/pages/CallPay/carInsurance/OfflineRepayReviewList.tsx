/**
 * 回款审核-线下回款-还款单
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import { RePaymentQrViewer } from '@/components/QrViewer/RepaymentQrViewer';
import {
  SECONDARY_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_CODE_REMISSION,
  SECONDARY_CLASSIFICATION_CODE_VALUE_MAP_ALL,
} from '@/enums';
import { LEVEL } from '@/pages/CarInsurance/type';
import { filterProps, removeBlankFromObject } from '@/utils/tools'; // @ts-ignore
import { isCarInsuranceStoreUser, isChannelStoreUser } from '@/utils/utils'; // @ts-ignore
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components'; // @ts-ignore
import { ProTable } from '@ant-design/pro-components';
import { useAccess, useModel } from '@umijs/max';
import { message, Modal, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useEffect, useRef, useState } from 'react';
import { PAY_TYPE } from '../const';
import RemissionReviewModal from '../reductionOrder/components/RemissionReviewModal';
import { useChannelListSummarizeSer } from '../service';
import CollectRepayReviewModal from './components/CollectRepayReviewModal';
import EarlySettleReviewModal from './components/EarlySettleReviewModal';
import LoanInstallMentCreditViewModal from './components/LoanInstallMentCreditViewModal';
import OfflineRepayReviewModal from './components/OfflineRepayReviewModal';
import OnlineRepayReviewModal from './components/OnlineRepayReviewModal';
import TermOfflineRepayReviewModal from './components/TermOfflineRepayReviewModal';
import { applyOrderExport, offlineRepayReviewList, repayOrderApplyCancel } from './services';
import styles from './styles/index.less';
import type { IofflineRepayReviewItem } from './types';
import { payStatusMap, repayModeMap, repayStatusMap, statusMap } from './types';

type Props = {
  renderHeaderTitleJsx: () => any;
};
const OfflineRepayReviewList: React.FC<Props> = (props) => {
  const formRef = useRef<ProFormInstance>();
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const { renderHeaderTitleJsx } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [record, setRecord] = useState<IofflineRepayReviewItem>();
  const [offlineRepayReviewOpen, setOfflineRepayReviewOpen] = useState(false);
  const [earlySettleRepayReviewOpen, setEarlySettleRepayReviewOpen] = useState(false);
  const [termOfflineRepayReviewOpen, setTermOfflineRepayReviewOpen] = useState(false);
  const [remissionReviewModalOpen, setRemissionReviewModalOpen] = useState(false); // 圆易借催收减免单详情
  const [onlineRepayReviewOpen, setOnlineRepayReviewOpen] = useState(false); //  减免单详情
  const [remissionReviewOpen, setRemissionReviewOpen] = useState(false); //  减免单详情
  const [collectRepayReviewOpen, setCollectRepayReviewOpen] = useState(false); // 催收回款详情
  const actionRef = useRef<ActionType>();
  const access = useAccess();
  const { initialState = {} }: any = useModel('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode, channelLevel } = currentUser;
  const [secondaryClassification, setSecondaryClassification] = useState('');
  const channelListSummarize = useChannelListSummarizeSer({
    secondaryClassification,
    channelCode,
    channelLevel,
  });
  const [qrVisible, setQrVisible] = useState(false);
  const [qrData, setQrData] = useState({});

  useEffect(() => {
    setSecondaryClassification(isCarInsuranceStoreUser(access) ? 'CAR_INSURANCE' : 'FINANCE_LEASE'); //  默认小圆车融
  }, [currentUser]);

  const OfflineRepayReviewListColumns: ProColumns<IofflineRepayReviewItem>[] = [
    {
      title: '还款单ID',
      dataIndex: 'businessNo',
      order: 13,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondProductCode',
      valueType: 'select',
      valueEnum: SECONDARY_CLASSIFICATION_CODE_REMISSION,
      initialValue: isCarInsuranceStoreUser(access)
        ? SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE
        : SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
      order: 3,
      fieldProps: {
        onChange: (val) => {
          formRef?.current?.setFieldsValue({
            //  清空一下联动
            channelCode: '',
            repayMode: '',
          });
          setSecondaryClassification(SECONDARY_CLASSIFICATION_CODE_VALUE_MAP_ALL[val as any] || '');
        },
        allowClear: false,
        disabled: isChannelStoreUser(access) || isCarInsuranceStoreUser(access),
      },
    },
    {
      title: '还款账单ID',
      dataIndex: 'billNo',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record.billNoList.map((billNo) => {
                  return <div>{billNo}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.billNo}
            </div>
          </Tooltip>
        );
      },
      order: 12,
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record.orderNoList.map((orderNo) => {
                  return <div>{orderNo}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.orderNo}
            </div>
          </Tooltip>
        );
      },
      order: 11,
    },
    {
      title: '车架号',
      dataIndex: 'subjectUniqueNo',
      // 小贷不展示车架号
      hideInTable: secondaryClassification === 'LOAN_INSTALLMENT_CREDIT',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record.subjectUniqueNoList.map((subjectUniqueNo) => {
                  return <div key={subjectUniqueNo}>{subjectUniqueNo}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.subjectUniqueNo}
            </div>
          </Tooltip>
        );
      },
      order: 10,
    },
    {
      title: '还款场景',
      dataIndex: 'repayMode',
      valueType: 'select',
      valueEnum: repayModeMap[secondaryClassification],
      order: 9,
      render(_, record) {
        const valueEnum =
          repayModeMap[SECONDARY_CLASSIFICATION_CODE_VALUE_MAP_ALL[record?.secondProductCode]] ||
          {};
        return <>{valueEnum[record?.repayMode] || '-'}</>;
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channelCode',
      valueType: 'select',
      hideInTable: true,
      params: { key: secondaryClassification },
      request: async () => channelListSummarize,
      //二级车险渠道只有自己，禁用渠道同时，选中自己
      initialValue:
        (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL) ||
        (isChannelStoreUser(access) && !!channelCode)
          ? channelCode
          : undefined,
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        disabled:
          (isChannelStoreUser(access) && !!channelCode) ||
          (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL),
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      order: 8,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '客户名称',
      dataIndex: 'accountName',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record.accountNameList.map((accountName) => {
                  return <div>{accountName}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.accountName}
            </div>
          </Tooltip>
        );
      },
      order: 8,
    },
    {
      title: '应还款总金额',
      dataIndex: 'planRepayAmount',
      search: false,
    },
    {
      title: '还款总额',
      dataIndex: 'repayAmount',
      search: false,
    },
    {
      title: '减免总额',
      dataIndex: 'exemptionAmount',
      search: false,
    },
    {
      title: '支付方式',
      dataIndex: 'remitType',
      search: true,
      valueEnum: PAY_TYPE,
      render(_, record) {
        return PAY_TYPE?.[record?.remitType as keyof typeof PAY_TYPE] || '-';
      },
    },
    {
      title: '还款二维码',
      dataIndex: 'payRspInfo',
      search: false,
      render(_, record: any) {
        try {
          const data = JSON.parse(record?.payRspInfo);
          return data?.h5RepayUrl ? (
            <a
              onClick={() => {
                setQrData({
                  h5RepayUrl: data?.h5RepayUrl,
                  businessNo: record?.businessNo,
                  accountName: record?.accountName,
                  isCarInsurance:
                    record?.secondProductCode === SECONDARY_CLASSIFICATION_CODE?.CAR_INSURANCE,
                });
                setQrVisible(true);
              }}
            >
              查看
            </a>
          ) : (
            '-'
          );
        } catch (e) {
          return '-';
        }
      },
    },
    {
      title: '打款金额',
      dataIndex: 'bankAmount',
      search: false,
    },
    {
      title: '打款日期',
      dataIndex: 'bankAmountDate',
      search: false,
    },
    {
      title: '还款日期',
      dataIndex: 'repayDate',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(1, 'year'), dayjs()],
      search: {
        transform: (value: any) => {
          return {
            repayDateStart: `${value[0]} 00:00:00`,
            repayDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.repayDate || '-';
      },
      order: 7,
    },
    {
      title: '入账日期',
      dataIndex: 'creditDate',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            creditDateStart: `${value[0]} 00:00:00`,
            creditDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.creditDate || '-';
      },
      order: 6,
    },
    {
      title: '客户类型',
      dataIndex: 'customerType',
      valueType: 'select',
      fieldProps: {
        options: [
          { value: 1, label: '企业' },
          { value: 2, label: '个人' },
        ],
      },
      order: 5,
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      valueType: 'select',
      hideInTable: true,
      valueEnum: statusMap,
      order: 4,
    },
    {
      title: '审核状态',
      dataIndex: 'statusName',
      search: false,
    },
    {
      title: '支付状态',
      dataIndex: 'payStatus',
      valueType: 'select',
      valueEnum: payStatusMap,
      order: 2,
    },
    {
      title: '还款状态',
      dataIndex: 'repayStatus',
      valueType: 'select',
      valueEnum: repayStatusMap,
      order: 1,
    },
    {
      title: '提交人',
      dataIndex: 'applyName',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => {
        // 非催收减免
        const notLoanInstallmentCredit = record?.repayMode !== 8;
        return (
          access.hasAccess('bill_apply_info_postLoanMng_callpay') && (
            <Space>
              <a
                onClick={() => {
                  setRecord(record);
                  // 收银台支付/汇款余额支付方式的还款单查看详情，催收回款还款场景复用此弹窗
                  if ([4, 5].includes(record?.remitType as keyof typeof PAY_TYPE)) {
                    setOnlineRepayReviewOpen(true);
                    //提前结清场景
                  } else if (record?.repayMode === 6) {
                    setEarlySettleRepayReviewOpen(true);
                    //车辆分期账单还款
                  } else if (record?.repayMode === 3) {
                    setOfflineRepayReviewOpen(true);
                    //订单分期账单还款
                  } else if (record?.repayMode === 4) {
                    setTermOfflineRepayReviewOpen(true);
                  } else if (record?.repayMode === 8) {
                    setRemissionReviewModalOpen(true);
                  } else if (record?.repayMode === 10) {
                    // 催收回款审核
                    setCollectRepayReviewOpen(true);
                  }
                }}
              >
                查看详情
              </a>
              {/* 融租，非催收减免(回款)且还款状态不等于还款成功(已入帐2和取消3)时，支持取消还款单。并同步通知支付 */}
              {notLoanInstallmentCredit &&
                ![8, 10].includes(record?.repayMode) &&
                record?.secondProductCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND &&
                access.hasAccess('bill_apply_list_postLoanMng_callpay_checkstand') &&
                ![2, 3].includes(record?.repayStatus) && (
                  <a
                    onClick={async () => {
                      try {
                        if (record?.businessNo) {
                          Modal.confirm({
                            title: '确定取消该笔还款单吗？',
                            okText: '确定',
                            centered: true,
                            onOk: async () => {
                              fullScreenLoadingRef.current?.open();
                              await repayOrderApplyCancel({
                                auditStatus: -2,
                                businessNo: record?.businessNo,
                              }).finally(() => {
                                fullScreenLoadingRef.current?.close();
                              });
                              message.success('取消成功!');
                              setTermOfflineRepayReviewOpen(false);
                              actionRef.current?.reload();
                            },
                            cancelText: '取消',
                          });
                        }
                      } catch (error) {
                      } finally {
                        fullScreenLoadingRef.current?.close();
                      }
                    }}
                  >
                    取消
                  </a>
                )}
              {/* 车险，非催收减免且还款状态不等于还款成功(已入帐2和取消3)时，支持取消还款单。并同步通知支付 */}
              {notLoanInstallmentCredit &&
                record?.secondProductCode === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE &&
                access.hasAccess('bill_apply_list_postLoanMng_callpay_checkstand') &&
                ![2, 3].includes(record?.repayStatus) &&
                record?.remitType === 4 && (
                  <a
                    onClick={async () => {
                      try {
                        if (record?.businessNo) {
                          Modal.confirm({
                            title: '确定取消该笔还款单吗？',
                            okText: '确定',
                            centered: true,
                            onOk: async () => {
                              fullScreenLoadingRef.current?.open();
                              await repayOrderApplyCancel({
                                auditStatus: -2,
                                businessNo: record?.businessNo,
                              }).finally(() => {
                                fullScreenLoadingRef.current?.close();
                              });
                              message.success('取消成功!');
                              setTermOfflineRepayReviewOpen(false);
                              actionRef.current?.reload();
                            },
                            cancelText: '取消',
                          });
                        }
                      } catch (error) {
                      } finally {
                        fullScreenLoadingRef.current?.close();
                      }
                    }}
                  >
                    取消
                  </a>
                )}
            </Space>
          )
        );
      },
    },
  ];

  return (
    <>
      <ProTable
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        columns={OfflineRepayReviewListColumns}
        request={async (params) => {
          const { current = 1, pageSize = 20 } = params;
          return offlineRepayReviewList(
            removeBlankFromObject(filterProps({ ...params, current, pageSize })),
          );
        }}
        onReset={() => {
          setSecondaryClassification(
            isCarInsuranceStoreUser(access) ? 'CAR_INSURANCE' : 'FINANCE_LEASE',
          ); //  默认小圆车融
        }}
        scroll={{ x: 'max-content' }}
        rowSelection={{
          selectedRowKeys: selectedRowKeys,
          onChange: (_selectedRowKeys) => {
            setSelectedRowKeys(_selectedRowKeys);
          },
        }}
        rowKey="businessNo"
        className={styles['offline-repay-review-list']}
        headerTitle={renderHeaderTitleJsx()}
        formRef={formRef}
        actionRef={actionRef}
        toolBarRender={() => {
          return [
            access.hasAccess('bill_order_apply_export_postLoanMng_callpay') && (
              <AsyncExport
                getSearchDataTotal={async () => {
                  const searchParams = formRef.current?.getFieldsFormatValue?.();
                  const data = await offlineRepayReviewList(
                    removeBlankFromObject(
                      filterProps({
                        ...searchParams,
                        current: 1,
                        pageSize: 1,
                      }),
                    ),
                  );
                  return data?.total;
                }}
                getSearchParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  return removeBlankFromObject(filterProps({ ...params }));
                }}
                getSelectedParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  return {
                    businessNoList: selectedRowKeys,
                    channelCode: params?.channelCode,
                  };
                }}
                getSelectedTotal={() => {
                  return selectedRowKeys.length;
                }}
                exportAsync={applyOrderExport}
                taskCode={[ItaskCodeEnValueEnum.REPAY_BILL_APPROVE]}
              />
            ),
          ];
        }}
      />
      {offlineRepayReviewOpen && (
        <OfflineRepayReviewModal
          record={record}
          offlineRepayReviewOpen={offlineRepayReviewOpen}
          setOfflineRepayReviewOpen={setOfflineRepayReviewOpen}
          actionRef={actionRef}
        />
      )}
      {remissionReviewModalOpen && (
        <LoanInstallMentCreditViewModal
          record={record}
          remissionReviewModalOpen={remissionReviewModalOpen}
          setRemissionReviewModalOpen={setRemissionReviewModalOpen}
          actionRef={actionRef}
        />
      )}
      {earlySettleRepayReviewOpen && (
        <EarlySettleReviewModal
          record={record}
          earlySettleRepayReviewOpen={earlySettleRepayReviewOpen}
          setEarlySettleRepayReviewOpen={setEarlySettleRepayReviewOpen}
          actionRef={actionRef}
        />
      )}
      {termOfflineRepayReviewOpen && (
        <TermOfflineRepayReviewModal
          record={record}
          termOfflineRepayReviewOpen={termOfflineRepayReviewOpen}
          setTermOfflineRepayReviewOpen={setTermOfflineRepayReviewOpen}
          actionRef={actionRef}
        />
      )}
      <CollectRepayReviewModal
        record={record}
        collectRepayReviewOpen={collectRepayReviewOpen}
        setCollectRepayReviewOpen={setCollectRepayReviewOpen}
        actionRef={actionRef}
      />
      <OnlineRepayReviewModal
        record={record}
        onlineRepayReviewOpen={onlineRepayReviewOpen}
        setOnlineRepayReviewOpen={setOnlineRepayReviewOpen}
        setRemissionReviewOpen={setRemissionReviewOpen}
        actionRef={actionRef}
        setQrData={setQrData}
        setQrVisible={setQrVisible}
      />
      <RemissionReviewModal
        record={record}
        remissionReviewOpen={remissionReviewOpen}
        setRemissionReviewOpen={setRemissionReviewOpen}
        setOnlineRepayReviewOpen={setOnlineRepayReviewOpen}
        actionRef={actionRef}
      />
      <RePaymentQrViewer
        data={qrData}
        qrVisible={qrVisible}
        handleQRVisible={setQrVisible}
        // availableRepay={qrData?.isCarInsurance ? ['微信'] : undefined}
        title="还款二维码"
      />
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};

export default memo(OfflineRepayReviewList);
