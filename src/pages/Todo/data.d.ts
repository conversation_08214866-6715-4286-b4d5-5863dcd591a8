export type TodoItem = {
  firstAuditStatus: number;
  lastAuditStatus: number;
  operatorUserName: number;
  productCode: string;
  productName: string;
  waittingAssign: number;
  waittingFirstAudit: number;
  waittingLastAudit: number;
};

export type State = {
  orderStatus: string[] | string;
  loanStatus: string;
  operatorUserName?: number;
  startTime?: string;
  endTime?: string;
};

export type StatisticParams = {
  productSecondCode: string;
};
