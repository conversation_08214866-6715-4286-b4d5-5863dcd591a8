/*
 * @Date: 2024-01-27 15:34:01
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-02-02 11:21:31
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/CashProductConfig/PayBackCash.tsx
 * @Description:
 */
import React, { useEffect } from 'react';
import { Form, Row, Col, Input, Radio, Select } from 'antd';
import {
  REPAYMENT_CYCLESTART_TIME_ENUM,
  REPAYMENT_TERM_ENUMS,
  WITH_HOLDING_TIME,
  REPAY_DAY_ENUM,
  SUBSEQUENT_ENUM,
} from '@/enums';
import './index.less';
import { ProFormDependency } from '@ant-design/pro-form';

type PayBackCashProps = {
  isAddProduct?: boolean;
  payBackData?: any;
  name?: string;
  repayDateCalculateEnum?: string;
};

const PayBackCash: React.FC<PayBackCashProps> = (props) => {
  const { payBackData, isAddProduct, name, repayDateCalculateEnum } = props;

  const [form] = Form.useForm();
  // console.log(REPAYMENT_TERM_ENUMS);
  useEffect(() => {
    // const { repaymentTermEnums, repaymentTermEnum } = payBackData;
    form.setFieldsValue({
      ...payBackData,
      repaymentTerms: payBackData?.repaymentTerms?.length ? payBackData?.repaymentTerms : [],
      // repaymentDay: payBackData?.repaymentDay || `${type ? '放款' : '合同'}对日`, //不额外处理了
      repaymentTermEnums:
        payBackData?.repaymentTermEnums ||
        (payBackData?.repaymentTermEnum &&
          payBackData?.repaymentTermEnum
            .replace('FIXED_REPAYMENT_', '')
            .split('_')
            .map((num: string) => `FIXED_REPAYMENT_${num}`)),
      loanLendDateOfRepaymentDateStart: payBackData?.loanLendDateOfRepaymentDate[0],
      loanLendDateOfRepaymentDateEnd: payBackData?.loanLendDateOfRepaymentDate[1],
      // repayDayDetail: 1,
    });
  }, [payBackData]);
  // console.log(payBackData);

  // useEffect(() => {
  //   form.validateFields();
  // }, [drawerStatus, payBackData]);

  return (
    <Form
      form={form}
      name={name}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 24 }}
      labelAlign="right"
      // onValuesChange={payBackValueChange}
    >
      <Row>
        <Col span={11} offset={1}>
          <Form.Item name="repaymentModeEnum" label="还款方式">
            {/* <Input placeholder="一次本息" /> */}
            <Select disabled>
              <Select.Option value="ALL_AT_ONCE">一次本息</Select.Option>
              <Select.Option value="PRINCIPAL_EQUALS_INTEREST">等额本息</Select.Option>
            </Select>
          </Form.Item>
        </Col>

        <Col span={11} offset={1}>
          <Form.Item name="repaymentTermEnums" label="还款期限">
            <Select disabled={!isAddProduct} mode="tags">
              {Object.keys(REPAYMENT_TERM_ENUMS).map((key) => (
                <Select.Option key={key} value={key}>
                  {REPAYMENT_TERM_ENUMS[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        {repayDateCalculateEnum === 'CUSTOMIZE_REPAY_DATE' && (
          <Col span={11} offset={1}>
            <Form.Item name="repaymentDay" label="默认还款日期">
              <Input addonBefore="D+" suffix="日" disabled />
            </Form.Item>
          </Col>
        )}

        <Col span={11} offset={1}>
          <Form.Item name="repaymentCycleStartTimeEnum" label="还款周期起始时间">
            <Select disabled>
              {Object.keys(REPAYMENT_CYCLESTART_TIME_ENUM).map((key) => (
                <Select.Option key={key} value={key}>
                  {REPAYMENT_CYCLESTART_TIME_ENUM[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={11} offset={1}>
          <Form.Item name="partialRepayment" label="是否允许部分还款">
            <Radio.Group value={1} disabled>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>

        <Col span={11} offset={1}>
          <Form.Item name="withholdingTime" label="代扣时间">
            <Select disabled mode="multiple">
              {Object.keys(WITH_HOLDING_TIME).map((key) => (
                <Select.Option key={key} value={key}>
                  {WITH_HOLDING_TIME[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={11} offset={1}>
          <Form.Item name="defaultRepaymentDate" label="还款日">
            <Radio.Group disabled>
              {Object.keys(REPAY_DAY_ENUM).map((key) => (
                <Radio key={key} value={key}>
                  {REPAY_DAY_ENUM[key]}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={11} offset={14}>
          <ProFormDependency name={['defaultRepaymentDate']}>
            {/* 实际还款方为第三方时需要展示 */}
            {({ defaultRepaymentDate }) => {
              return defaultRepaymentDate == 'LOAN_LEND_DATE' ? (
                <div className="flx">
                  <Form.Item name="loanLendDateOfRepaymentDateStart">
                    <Input disabled className="small-input" />
                  </Form.Item>
                  <span className="margin-l-r-5">-</span>
                  <Form.Item name="loanLendDateOfRepaymentDateEnd">
                    <Input disabled className="small-input" />
                  </Form.Item>
                  <span className="margin-l-r-5 grey">日</span>
                  <div className="margin-l-r-5">
                    <Radio.Group disabled name="repayDayDetail" value={1}>
                      <Radio value={1}>
                        <div className="flx">
                          次月对日/月底+
                          <Form.Item name="loanLendDateOfPlusDays">
                            <Input disabled className="small-input" />
                          </Form.Item>
                          <span className="margin-l-r-5">天</span>
                        </div>
                      </Radio>
                      <Radio>
                        次月 <Input disabled className="small-input" />{' '}
                        <span className="margin-l-r-5">日</span>
                      </Radio>
                      <Radio>
                        次次月 <Input disabled className="small-input" />{' '}
                        <span className="margin-l-r-5">日</span>
                      </Radio>
                    </Radio.Group>
                  </div>
                </div>
              ) : (
                <></>
              );
            }}
          </ProFormDependency>
        </Col>
        <Col span={12} offset={11}>
          <ProFormDependency name={['defaultRepaymentDate']}>
            {({ defaultRepaymentDate }) => {
              return defaultRepaymentDate == 'LOAN_LEND_DATE' ? (
                <Form.Item
                  label="后续订单还款日是否与首笔有关联"
                  name="nextRepaymentDate"
                  labelCol={{ span: 9 }}
                >
                  <Radio.Group disabled>
                    {Object.keys(SUBSEQUENT_ENUM).map((key) => (
                      <Radio key={key} value={key}>
                        {SUBSEQUENT_ENUM[key]}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>
              ) : null;
            }}
          </ProFormDependency>
        </Col>
        <Col span={11} offset={16}>
          <ProFormDependency name={['nextRepaymentDate']}>
            {({ nextRepaymentDate }) => {
              return nextRepaymentDate == 'SAME_FIRST' ? (
                <div className="flx grey">
                  首次还款日距离放款日需 <span className="margin-l-r-5">{'>'}</span>
                  <Form.Item name="nextRepaymentDateOfDays">
                    <Input disabled className="small-input" />
                  </Form.Item>
                  天，否则顺延
                </div>
              ) : null;
            }}
          </ProFormDependency>
        </Col>
      </Row>
    </Form>
  );
};

export default PayBackCash;
