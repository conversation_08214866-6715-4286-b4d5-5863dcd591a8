/*
 * @Author: your name
 * @Date: 2021-06-28 11:17:53
 * @LastEditTime: 2021-07-15 19:14:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/WithHold/detail.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import { WITH_HOLDING_TIME } from '@/enums';
import globalStyle from '@/global.less';
import { TableOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { Card, Col, Dropdown, Form, Input, Row, Select, Table } from 'antd';
import { bignumber, format, multiply } from 'mathjs';
import React, { useEffect } from 'react';
import { getWitholdConfig } from './service';

const selectOption = Object.keys(WITH_HOLDING_TIME).map((key) => (
  <Select.Option ke={key} value={key}>
    {WITH_HOLDING_TIME[key]}
  </Select.Option>
));

const ComDetail: React.FC<{}> = () => {
  const [form] = Form.useForm();
  // const claimData = { time: ['09:00', '10:00'] };

  const { productCode } = history.location.query;
  const { data } = useRequest(() => {
    return getWitholdConfig(productCode);
  });
  useEffect(() => {
    form.setFieldsValue(data?.overdueDTO);
  }, [data]);

  const menu = (
    <div>
      <Table
        size="small"
        bordered
        columns={[
          { title: '部分代扣时间', dataIndex: 'time', key: 'time' },
          {
            title: '代扣比例（%）',
            dataIndex: 'interest',
            key: 'interest',
            render: (_, row) => {
              return `${format(multiply(bignumber(row?.interest), 100))}%` || '-';
            },
          },
        ]}
        pagination={false}
        dataSource={data?.overdueDTO?.partialWithHoldingGradient}
      />
    </div>
  );
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Card>
          <Form
            form={form}
            // onFinish={(values) => {
            //   console.log('Success:', values);
            // }}
            className={globalStyle.withHold}
          >
            <Row gutter={20}>
              <Col span={8}>
                <Form.Item name="overdueStartTime" label="逾期代扣起始日期">
                  {/* <span className={globalStyle.mr10}>T+</span> */}
                  <Input prefix="T+  " disabled />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="overdueCyclePeriodDay" label="逾期代扣循环周期">
                  {/* <span className={globalStyle.mr10}></span> */}
                  <Input prefix="每  " suffix="天" disabled />
                  {/* <span className={globalStyle.ml10}>天</span> */}
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="overdueClosingDate" label="逾期代扣循环截止日期">
                  {/* <span className={globalStyle.mr10}></span> */}
                  <Input prefix="T+  " disabled />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="overdueDateEnum" label="逾期代扣时间">
                  {/* <MultipleTimePicker placeholder="请选择逾期代扣时间" /> */}
                  <Select mode="multiple" showArrow disabled>
                    {selectOption}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="overduePartDay" label="部分代扣起始时间">
                  <Input prefix="T+  " disabled />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="overdueCycleWithholding" label="部分代扣循环周期">
                  <Input prefix="每  " suffix="天" disabled />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="overdueCyclePartDay" label="部分代扣循环截止日期">
                  {/* <span className={globalStyle.mr10}>T+</span> */}
                  <Input prefix="T+  " disabled />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="overduePartWithTimeEnum" label="部分代扣时间">
                  <Select mode="multiple" showArrow disabled>
                    {selectOption}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item name="partialWithHoldingGradient" label="部分代扣梯度">
                  <Dropdown overlay={menu}>
                    <Input
                      addonBefore={<span>指定比例</span>}
                      style={{ width: '100%' }}
                      value="配置表"
                      disabled
                      suffix={
                        // <Tooltip title="Extra information">

                        // </Tooltip>

                        <TableOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
                      }
                    />
                  </Dropdown>
                </Form.Item>
              </Col>
            </Row>
            {/* <Form.Item>
            <Button type="primary" htmlType="submit">
              Submit
            </Button>
          </Form.Item> */}
          </Form>
        </Card>
      </PageContainer>
    </>
  );
};

export default ComDetail;
