import { useModel } from '@umijs/max';
import { Col, Form, Radio, Row } from 'antd';
import React, { useEffect } from 'react';

const InsuranceSettleEarly: React.FC<any> = () => {
  const [form] = Form.useForm();
  const { detailData } = useModel('Product.insuranceProduct');
  useEffect(() => {
    const { settleEarly } = detailData?.inAdvanceRepaymentDTO || {};
    form.setFieldsValue({ settleEarly });
  }, [detailData, form]);
  return (
    <Form
      form={form}
      labelCol={{ span: 12 }}
      wrapperCol={{ span: 16 }}
      labelAlign="right"
      name={'insuranceSettleEarly'}
    >
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item name="settleEarly" label="是否允许提前结清">
            <Radio.Group disabled>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default InsuranceSettleEarly;
