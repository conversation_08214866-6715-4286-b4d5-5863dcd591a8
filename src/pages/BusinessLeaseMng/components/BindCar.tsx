/*
 * @Author: your name
 * @Date: 2021-04-12 11:34:56
 * @LastEditTime: 2024-04-23 18:43:03
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/BindCar.tsx
 */
import { DividerTit, StepProgress } from '@/components';
import globalStyle from '@/global.less';
import { DownloadOutlined } from '@ant-design/icons';
import { Button, Col, Image, message, Modal, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import BindCarModalNew from './BindCarModalNew';
import BindCarModalSecond from './BindCarModalSecond';

import type { BindCarInfo } from '../data';
import { audit } from '../service';
import ShowIncomingInfo from './ShowIncomingInfo';
// import dayjs from 'dayjs';
import { useAccess } from '@umijs/max';
import { ORDER_STATUS } from '../consts';
interface BindCarProps {
  orderNo: string;
  data: BindCarInfo;
  orderDetail?: any;
  refresh: () => void;
  orderStatus: number;
  showItemMap: {};
  dataShowInfo: Record<string, any>;
  showInfoMap: {};
}
const infoMapNew = {
  carUniqueCode: '车辆识别代码',
  carFabricateDate: '车辆制造日期',
  engineCode: '发动机号',
  oosFilePath: '车辆合格证',
  licenseCode: '车牌',
  registrationDateOfDrivingLicense: '行驶证登记日期',
  bindRemark: '其他',
};
const infoMapSecond = {
  // engineCode: '发动机号',
  bindRemark: '其他',
  registrationDateOfDrivingLicense: '行驶证登记日期',
};
const itemMap = {};

const BindCar: React.FC<BindCarProps> = (props) => {
  const { data, orderNo, refresh, showInfoMap, dataShowInfo, showItemMap, orderDetail } = props;
  // const { Step } = Steps;
  const [visibleBindCar, handleVisibleBindCar] = useState<boolean>(false);

  const [formEdit, setFormEdit] = useState<object>({});

  const [firstIncomingInfo, handleFirstIncomingInfo] = useState<boolean>(false);
  const [showPreview, setShowPreview] = useState(false);
  const access = useAccess();

  useEffect(() => {
    return () => {
      Modal.destroyAll();
    };
  }, []);
  // const [visibleBindSecondCar, handleVisibleBindSecondCar] = useState<boolean>(false);
  // const data ={}
  // 重新绑定，获取当前的
  // const getCurrent = () => {};
  const infoMap = data?.carType === 1 ? infoMapNew : infoMapSecond;
  // 审核交车或者绑车
  const handlePassCarResource = (auditType: number, operation: boolean) => {
    audit({
      auditType,
      orderNo,
      operation,
    }).then(() => {
      refresh();
      message.success('审核成功');
    });
  };

  const mapOptType = {
    1: '提交信息',
    20: '初审通过',
    21: '初审拒绝',
    40: '通过',
    41: '驳回',
    '-1': '车辆解绑',
  };

  const getFileName = (str: string) => {
    const index = str.lastIndexOf('/');
    const ret = str.substring(index + 1, str.length);
    return ret;
  };

  const popupBindCarModal = () => {
    setFormEdit({
      ...data,
      carUniqueCode: data?.carType !== 2 ? '' : data.carUniqueCode,
      licenseCode: data?.carType !== 2 ? '' : data.licenseCode,
      engineCode: data?.carType !== 2 ? '' : data.engineCode,
      remark: '',
      channelId: `${data?.channelId}`,
      licenseCompany: {
        label: data?.licenseCompany,
        value: data?.licenseCompanyId,
      },
      applyStore: {
        label: data?.applyStore,
        value: data?.applyStoreId,
      },
      registrationDateOfDrivingLicense: null,
      carFabricateDate: null,
      // drivingLicense: data?.drivingLicenseFiles,
      // carRegistration: data?.carRegistrationFiles,
    });
    handleVisibleBindCar(true);
  };

  const popupReduceCreditPassModal = () => {
    Modal.info({
      title: '该用户为降额通过，请确认！',
      okText: '确认',
      maskClosable: true,
      content: (
        <div>
          <p>
            <span>授信额度：</span>
            <span>{`${orderDetail?.preCreditAmount}(元)—>`}</span>
            <span style={{ color: '#4cc26b' }}>{orderDetail?.creditAmount}(元)</span>
          </p>
          <p>
            <span>月供金额：</span>
            <span>{`${orderDetail?.preRentPrice}(元)—>`}</span>
            <span style={{ color: '#4cc26b' }}>{orderDetail?.rentPrice}(元)</span>
          </p>
        </div>
      ),
      onOk: () => {
        popupBindCarModal();
      },
    });
  };

  const handleBindCar = () => {
    // 降额通过展示提示弹窗
    if (orderDetail?.orderStatus === ORDER_STATUS.REDUCE_CREDIT_PASS) {
      popupReduceCreditPassModal();
    } else {
      popupBindCarModal();
    }
  };

  const reviewAudit = () => {
    // const diffDay = dayjs(data.bindCarDate).diff(dayjs(data.carFabricateDate), 'day');
    handlePassCarResource(1, true);
    // if (diffDay > 120) {
    //   modal.confirm({
    //     title: '车辆绑定审核',
    //     icon: <ExclamationCircleOutlined />,
    //     content: (
    //       <div>
    //         制造日期距离绑车当日<span style={{ color: 'red' }}>{diffDay}</span>天,
    //         <br />
    //         <span style={{ color: 'red' }}>请确认是否审核通过车辆绑定！</span>
    //       </div>
    //     ),
    //     okText: '审核通过',
    //     cancelText: '取消',
    //     onOk: () => {
    //       handlePassCarResource(1, true);
    //     },
    //   });
    // } else {
    //   handlePassCarResource(1, true);
    // }
  };

  console.log('isNewCarDelivery', data?.isNewCarDelivery);
  return (
    <div>
      <div>
        <DividerTit title="绑定车辆">
          {data?.bindCarStatus === 0 && (
            <Button
              className={globalStyle.ml10}
              onClick={() => {
                handleBindCar();
              }}
            >
              绑定车辆
            </Button>
          )}

          {data?.bindCarStatus === 10 &&
            access.hasAccess('biz_btn_reedit_carinfo_businessMng_leaseList') && (
              <Button
                className={globalStyle.ml10}
                onClick={() => {
                  reviewAudit();
                  //
                }}
              >
                复核通过
              </Button>
            )}
          {data?.bindCarStatus === 10 &&
            access.hasAccess('biz_btn_reedit_carinfo_businessMng_leaseList') && (
              <Button
                className={globalStyle.ml10}
                onClick={() => {
                  // getCurrent();
                  setFormEdit({
                    ...data,
                    remark: data?.bindRemark,
                    channelId: `${data?.channelId}`,
                    licenseCompany: {
                      label: data?.licenseCompany,
                      value: data?.licenseCompanyId,
                    },
                    applyStore: {
                      label: data?.applyStore,
                      value: data?.applyStoreId,
                    },
                    drivingLicense: data?.drivingLicenseFiles,
                    carRegistration: data?.carRegistrationFiles,
                  });
                  handleVisibleBindCar(true);
                }}
              >
                重新编辑
              </Button>
            )}
          {dataShowInfo?.carNo && access.hasAccess('btn_view_carinfo_businessMng_leaseList') && (
            <span className={globalStyle.ml10}>
              进件车辆信息存在变更
              <a
                onClick={() => {
                  handleFirstIncomingInfo(true);
                }}
              >
                查看进件车辆信息
              </a>
            </span>
          )}
        </DividerTit>
      </div>
      {/* 绑车资料 */}
      {data?.bindCarAuditLogs?.length && (
        // <Row className={globalStyle.mt20}>
        //   <Col span={8}>
        //     <Steps
        //       progressDot
        //       current={data?.bindCarAuditLogs?.length}
        //       className={globalStyle.stepItemDesc}
        //     >
        //       {data?.bindCarAuditLogs?.map(
        //         (item: { type: number; operatorTime: string; operatorBy: string }) => {
        //           return (
        //             <Step
        //               key={`${mapOptType[item.type]} ${item.operatorTime || '-'} ${
        //                 item.operatorBy || '-'
        //               }`}
        //               title={mapOptType[item.type]}
        //               description={`${item.operatorTime || '-'} ${item.operatorBy || '-'}`}
        //             />
        //           );
        //         },
        //       )}
        //     </Steps>
        //   </Col>
        <StepProgress
          progressDot={true}
          lineLength={6}
          stepStatus={data?.bindCarAuditLogs?.map(
            (item: { type: number; operatorTime: string; operatorBy: string }) => {
              return {
                bol: false,
                desc: mapOptType[item.type],
                localDate: `${item.operatorTime || '-'} ${item.operatorBy || '-'}`,
                // subStatusDesc: item.subStatusDesc,
              };
            },
          )}
        />
        // </Row>
      )}
      {/* 0为绑卡状态 */}
      <Row className={`${globalStyle.mt20}  ${globalStyle.ml20}`}>
        {data?.engineCode && data?.bindCarStatus !== 0 ? (
          Object.keys(infoMap).map((item) => {
            const value =
              (data && (itemMap && itemMap[item] ? itemMap[item][data[item]] : data[item])) || '';
            return (
              <Col span={12} key={item}>
                <div className={globalStyle.lineHeight40}>
                  <span className={globalStyle.ml20}>{infoMap[item]}:</span>
                  {item === 'oosFilePath' && value ? (
                    <span>
                      <a
                        className={globalStyle.ml20}
                        onClick={() => {
                          setShowPreview(true);
                          return false;
                        }}
                      >
                        {getFileName(value)}
                      </a>
                      <a href={value} download className={globalStyle.ml20}>
                        <DownloadOutlined />
                      </a>
                      <Image
                        width={200}
                        style={{ display: 'none' }}
                        src={value}
                        preview={{
                          visible: showPreview,
                          src: value,
                          onVisibleChange: (visible) => {
                            setShowPreview(visible);
                          },
                        }}
                      />
                    </span>
                  ) : (
                    <span
                      className={globalStyle.ml20}
                      style={{
                        color: data.modifyFields?.includes(item) ? 'red' : 'unset',
                      }}
                    >
                      {value || '-'}
                    </span>
                  )}
                </div>
              </Col>
            );
          })
        ) : (
          <span>暂未绑定车辆信息</span>
        )}
      </Row>
      {/* {data?.isNewCarDelivery === DELIVERY_TYPE.NEW ? (
        新交车模块
        <Delivery data={data} orderStatus={orderStatus} orderNo={orderNo} refresh={refresh} />
      ) : (
        老交车模块
        <OldDelivery
          data={data}
          orderStatus={orderStatus}
          orderNo={orderNo}
          refresh={refresh}
          isNewCarDelivery={data?.isNewCarDelivery}
        />
      )} */}
      {/* 绑定车辆，区分新车和二手车  */}
      {data?.carType !== 2 ? (
        <BindCarModalNew
          modalVisible={visibleBindCar}
          formCur={formEdit}
          data={data}
          orderDetail={orderDetail}
          onOk={async () => {
            handleVisibleBindCar(false);
            props.refresh();
          }}
          // isNewCar={}
          onCancel={() => {
            handleVisibleBindCar(false);
          }}
          // isNewCar={props.deadLine}
          onVisibleChange={handleVisibleBindCar}
        />
      ) : (
        <BindCarModalSecond
          modalVisible={visibleBindCar}
          formCur={formEdit}
          onOk={async () => {
            handleVisibleBindCar(false);
            props.refresh();
          }}
          // isNewCar={}
          onCancel={() => {
            handleVisibleBindCar(false);
          }}
          onVisibleChange={handleVisibleBindCar}
        />
      )}
      <Modal
        destroyOnClose
        title="进件车辆信息"
        open={firstIncomingInfo}
        onCancel={() => {
          handleFirstIncomingInfo(false);
        }}
        width={900}
        // footer={null}
        footer={[
          <Button key="back" onClick={() => handleFirstIncomingInfo(false)}>
            取消
          </Button>,
        ]}
      >
        <ShowIncomingInfo infoMap={showInfoMap} itemMap={showItemMap} data={dataShowInfo} />
      </Modal>
      {/* {contextHolder} */}
    </div>
  );
};

export default BindCar;
