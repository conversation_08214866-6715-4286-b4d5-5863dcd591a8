/*
 * @Author: your name
 * @Date: 2021-03-20 15:48:36
 * @LastEditTime: 2021-12-27 13:53:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalIncoming/index.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import React from 'react';
// import PageContainer from '@/components/PageContainer/PageContainer'
import { KeepAlive } from 'react-activation';
import RecordTable from './components/table';

const PersonalMngList: React.FC<{}> = () => {
  return (
    <>
      <PageContainer>
        <RecordTable />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'userMng/personalMng/list'}>
      <PersonalMngList />
    </KeepAlive>
  </>
);
