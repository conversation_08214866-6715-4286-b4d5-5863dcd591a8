/*
 * @Author: your name
 * @Date: 2021-01-27 11:35:34
 * @LastEditTime: 2021-11-30 18:03:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/models/userList.js
 */
import { queryAllUserList } from '@/services/global';
import { useModel } from '@umijs/max';
import { useEffect, useState } from 'react';
export default () => {
  // 所用用户
  const [mapUserList, setUserListMap] = useState({});
  const [userList, setUserList] = useState([]);
  // 所有催收员
  const [urgePersonList, setUrgePersonList] = useState([]);
  const [mapUrgePersonList, setMapUrgePersonList] = useState({});

  const { initialState } = useModel('@@initialState');
  useEffect(() => {
    if (initialState?.currentUser) {
      // 所有已注册用户
      // request('/auth/operator/privilege/getUserList', { skipErrorHandler: true })
      queryAllUserList()
        .then((res) => {
          const mapTemp: any = {};
          const userListTemp = res.data?.map((item: { id: number; username: string }) => {
            mapTemp[item.id] = item.username;
            return { value: item.id, label: item.username };
          });
          setUserList(userListTemp);
          setUserListMap(mapTemp);
        })
        .catch(() => {
          // console.log(e)
        });
      // 所有可催收人员，不区分用户类型（渠道、运营、车险）
      queryAllUserList({
        current: 1,
        pageSize: 1000,
        roleCodes: ['biz_callAgent', 'post_loan_supervisor', 'biz_externalCallAgent'],
      })
        .then((res) => {
          const mapTemp: any = {};
          const userListTemp = res.data?.map((item: { id: number; username: string }) => {
            mapTemp[item.id] = item.username;
            return { value: item.id, label: item.username };
          });
          setUrgePersonList(userListTemp);
          setMapUrgePersonList(mapTemp);
        })
        .catch(() => {
          // console.log(e)
        });
    }
  }, [initialState?.currentUser]);
  return {
    // 所用用户
    mapUserList,
    userList,
    // 所有催收员
    urgePersonList,
    mapUrgePersonList,
  };
};
