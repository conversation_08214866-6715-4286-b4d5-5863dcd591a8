/*
 * @Author: your name
 * @Date: 2021-01-28 14:25:58
 * @LastEditTime: 2021-06-01 19:17:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/UploadCom.tsx
 */
import React from 'react';
import { ProFormUploadButton } from '@ant-design/pro-form';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';

const UploadCom: React.FC<any> = (props) => {
  const base_url = getBaseUrl();
  const upload_url = `${base_url}/repayment/uploadRepayPic`;
  return (
    <>
      <ProFormUploadButton
        labelCol={{ span: props.span || 4 }}
        extra="支持扩展名：.png.jpg.jpeg.xls.xlsx.csv.doc.docx.pdf.zip.txt"
        label="上传附件"
        name="fileList"
        fieldProps={{ headers: { ...getAuthHeaders() }, name: 'file' }}
        action={upload_url}
        accept=".zip,.png,.jpg,.jpeg,.xls,.xlsx,.csv,.doc,.docx,.pdf,.txt"
        max={5}
        rules={[
          {
            validator: (rule, val, callBack) => {
              val?.map((item: any) => {
                // 校验是否超过10兆
                const isLt10M = item.size / 1024 / 1024 < 10;
                if (!isLt10M) {
                  callBack(`【${item.name}】文件大小超过10MB!`);
                }
                const suffix = item.name.substring(item.name.lastIndexOf('.') + 1);
                const suffixList = [
                  'zip',
                  'png',
                  'jpg',
                  'jpeg',
                  'xls',
                  'xlsx',
                  'csv',
                  'doc',
                  'docx',
                  'pdf',
                  'txt',
                ];
                if (!suffixList.includes(suffix)) {
                  callBack('【此文件类型不在允许范围内】');
                }
                return item;
              });
              callBack();
            },
          },
        ]}
        title="选择文件"
      />
    </>
  );
};
export default UploadCom;
