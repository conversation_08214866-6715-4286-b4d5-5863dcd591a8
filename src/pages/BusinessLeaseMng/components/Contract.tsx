/*
 * @Author: your name
 * @Date: 2021-01-18 16:33:12
 * @LastEditTime: 2024-08-26 10:36:20
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/Contract.tsx
 */
import {
  downLoadExcel,
  getBlob,
  isExternalDesensitization,
  previewAS,
  saveAs,
} from '@/utils/utils';
import { message, Table } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
// import { getOssPath } from '@/services/global';
import globalStyle from '@/global.less';
import { fileZips } from '@/services/global';
import { useAccess } from '@umijs/max';

interface ContractProps {
  orderNo: string;
  dataContract: ContractItems[];
  userName?: string;
  productCode?: string;
  guaranteeType?: string;
  carCity: string;
}
interface ContractItems {
  contractName: string;
  contractType: string;
  filePath: string;
  netWorkPath: string;
}

const CONTRACT_NAME_TYPE = {
  车辆委托管理协议: '1',
  车辆融资租赁合同: '2',
  委托代扣授权书: '3',
  个人信息授权书: '4',
  车辆交接单: '6',

  承诺函: '8',
  挂靠协议: '9',
  委托书: '10',
  车辆抵押合同_两方: '11',
  车辆抵押合同_三方: '12',
  个人征信信息授权书: '13',
  风险告知书: '14',
  担保确认函: '15',

  车辆融资租赁合同_上银: '50',
  车辆转让合同: '51',
  资金用途承诺书: '52',
  保证函: '53',
  租金支付表: '54',
  委托代扣授权书_上银: '55',
  购车合同: '56',

  // 个户合同
  车辆交接单_个户: '201',
  车辆融资租赁合同_个户: '202',
  委托代扣授权书_个户: '203',
  购车合同_个户: '204',
  委托书_个户: '205',
  承诺函_个户: '206',
  车辆抵押合同_两方_个户: '207',
};

const SORT_LIST = [
  CONTRACT_NAME_TYPE.个人信息授权书,
  CONTRACT_NAME_TYPE.个人征信信息授权书,
  CONTRACT_NAME_TYPE.风险告知书,
  CONTRACT_NAME_TYPE.委托代扣授权书,
  CONTRACT_NAME_TYPE.委托代扣授权书_上银,
  CONTRACT_NAME_TYPE.委托代扣授权书_个户,
  CONTRACT_NAME_TYPE.承诺函,
  CONTRACT_NAME_TYPE.承诺函_个户,
  CONTRACT_NAME_TYPE.车辆转让合同,
  CONTRACT_NAME_TYPE.车辆委托管理协议,
  CONTRACT_NAME_TYPE.车辆融资租赁合同,
  CONTRACT_NAME_TYPE.车辆融资租赁合同_个户,
  CONTRACT_NAME_TYPE.车辆融资租赁合同_上银,
  CONTRACT_NAME_TYPE.资金用途承诺书,
  CONTRACT_NAME_TYPE.租金支付表,
  CONTRACT_NAME_TYPE.车辆交接单,
  CONTRACT_NAME_TYPE.车辆交接单_个户,
  CONTRACT_NAME_TYPE.车辆抵押合同_三方,
  CONTRACT_NAME_TYPE.车辆抵押合同_两方,
  CONTRACT_NAME_TYPE.车辆抵押合同_两方_个户,
  CONTRACT_NAME_TYPE.委托书,
  CONTRACT_NAME_TYPE.委托书_个户,
  CONTRACT_NAME_TYPE.挂靠协议,
  CONTRACT_NAME_TYPE.保证函,
  CONTRACT_NAME_TYPE.购车合同,
  CONTRACT_NAME_TYPE.购车合同_个户,
];

const Contract: React.FC<ContractProps> = forwardRef((props, ref) => {
  const { dataContract, orderNo, userName, productCode, guaranteeType, carCity } = props;
  const [contractList, setContractList] = useState<ContractItems[]>([]);
  const [currentSelRow, setCurrentSelRow] = useState<ContractItems[]>([]);

  const access = useAccess();

  const sortContractList = (list: any) => {
    let newList: any = [];
    SORT_LIST.forEach((item) => {
      const findFileIndex: number = list.findIndex((contract) => contract.contractType === item);
      if (findFileIndex > -1) {
        newList.push(list[findFileIndex]);
        list.splice(findFileIndex, 1);
      }
    });
    // 最后把剩余的放入列表中
    newList = newList.concat(list);
    console.log('排序后合同', newList);
    setContractList(newList);
  };

  useEffect(() => {
    if (dataContract) {
      sortContractList(dataContract);
    }
  }, [dataContract]);

  const defaultSelContractArr: string[] = useMemo(() => {
    return [
      CONTRACT_NAME_TYPE.风险告知书,
      CONTRACT_NAME_TYPE.车辆融资租赁合同,
      CONTRACT_NAME_TYPE.车辆融资租赁合同_上银,
      CONTRACT_NAME_TYPE.车辆抵押合同_三方,
      CONTRACT_NAME_TYPE.挂靠协议,
      CONTRACT_NAME_TYPE.委托书,
      CONTRACT_NAME_TYPE.保证函,
      CONTRACT_NAME_TYPE.车辆转让合同,
      CONTRACT_NAME_TYPE.购车合同,
      CONTRACT_NAME_TYPE.车辆融资租赁合同_个户,
      CONTRACT_NAME_TYPE.车辆抵押合同_两方_个户,
      CONTRACT_NAME_TYPE.购车合同_个户,
      CONTRACT_NAME_TYPE.委托书_个户,
    ];
  }, []);
  const [contractSel, setContractSel] = useState<string[]>();
  const download = (url: string, filename: string) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
  };
  const previewPDF = (url: string) => {
    getBlob(url, (blob: Blob) => {
      previewAS(blob);
    });
  };

  useEffect(() => {
    let contractKeys: string[] = defaultSelContractArr;
    //挂靠公司是深圳或上牌城市是深圳的订单需要两方版车辆抵押合同
    if (carCity === '深圳') {
      contractKeys = [...contractKeys, CONTRACT_NAME_TYPE.车辆抵押合同_两方];
    }
    //担保类 担保函
    if (guaranteeType && guaranteeType !== 'NON_GUARANTEED') {
      contractKeys = [...contractKeys, CONTRACT_NAME_TYPE.担保确认函];
    }
    const selRow = contractList?.filter((item) => {
      return contractKeys?.includes(item?.contractType);
    });
    setContractSel(contractKeys);
    setCurrentSelRow(selRow || []);
  }, [guaranteeType, carCity, contractList, defaultSelContractArr]);

  const columns = [
    {
      title: '合同名称',
      dataIndex: 'contractName',
      key: 'contractName',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      // width: 140,
      render: (_: React.ReactNode, record: ContractItems) =>
        !isExternalDesensitization(access) && (
          <>
            <a
              onClick={() => {
                let contractNameFinal = '';
                const name = userName + record.contractName;
                switch (record.contractType) {
                  case CONTRACT_NAME_TYPE.车辆抵押合同_三方:
                    contractNameFinal = name + '（一式三份-客户签字按手印+金融+上牌三方签署）';
                    break;
                  case CONTRACT_NAME_TYPE.车辆抵押合同_两方:
                    contractNameFinal = name + '（一式三份-金融+上牌三方签署）';
                    break;
                  case CONTRACT_NAME_TYPE.车辆抵押合同_两方_个户:
                    contractNameFinal = name + '-个户（一式两份-客户签字按手印+金融盖章）';
                    break;
                  case CONTRACT_NAME_TYPE.挂靠协议:
                    contractNameFinal = name + '（一式两份-客户签字按手印 +上牌公司用印）';
                    break;
                  case CONTRACT_NAME_TYPE.委托书:
                    contractNameFinal = name + '（一式两份-上牌公司用印）';
                    break;
                  case CONTRACT_NAME_TYPE.委托书_个户:
                    contractNameFinal =
                      name + '（抵押&解除抵押）（一式两份-受托人处客户签名按手印）';
                    break;
                  case CONTRACT_NAME_TYPE.车辆融资租赁合同:
                  case CONTRACT_NAME_TYPE.车辆融资租赁合同_上银:
                    contractNameFinal = name + '（一式两份-客户签字按手印）';
                    break;
                  case CONTRACT_NAME_TYPE.车辆融资租赁合同_个户:
                    contractNameFinal = name + '（一式两份-客户签字按手印）';
                    break;
                  case CONTRACT_NAME_TYPE.承诺函:
                    contractNameFinal = name + '（一式两份-司机签字按手印）';
                    break;
                  case CONTRACT_NAME_TYPE.担保确认函:
                    contractNameFinal = name + '（一式两份-收款主体公司用印）';
                    break;
                  case CONTRACT_NAME_TYPE.车辆转让合同:
                    contractNameFinal = name + '（一式两份-客户签字按手印）';
                    break;
                  case CONTRACT_NAME_TYPE.购车合同_个户:
                    contractNameFinal = name + '（一式两份-客户签字按手印）';
                    break;
                  default:
                    contractNameFinal = orderNo + name;
                    break;
                }

                // if(record.contractName)
                download(record.netWorkPath, `${contractNameFinal}.pdf`);
              }}
            >
              下载
            </a>
            <a
              className={globalStyle.ml10}
              onClick={() => {
                previewPDF(record.netWorkPath);
              }}
            >
              预览
            </a>
          </>
        ),
    },
  ];

  const downZip = () => {
    // console.log(currentSelRow, '-----');
    //账单号+客户名称+合同
    if (currentSelRow?.length < 1) {
      message.warning('请先勾选合同');
      return;
    }
    if (currentSelRow?.length === 1) {
      message.warning('请选择至少两个文件');
      return;
    }
    const postData = {
      orderNo,
      contractFileReq: currentSelRow,
      userName,
      productCode,
    };
    // console.log(postData);

    fileZips(postData).then((res) => {
      // console.log(res, '---合同-');
      const type =
        currentSelRow?.length === 1 ? undefined : 'application/octet-stream;charset=UTF-8';
      downLoadExcel(res, type);
    });
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    downLoadZip: () => {
      downZip();
    },
  }));

  return (
    <Table
      rowKey={(record) => {
        return record.contractType;
      }}
      rowSelection={{
        onChange: (selectedRowKeys: React.Key[], rowSelection) => {
          setContractSel(selectedRowKeys as string[]);
          setCurrentSelRow(rowSelection);
          // console.log(rowSelection);
        },
        selectedRowKeys: contractSel,
      }}
      style={{ width: 400 }}
      dataSource={contractList}
      columns={columns}
      pagination={false}
    />
  );
});

export default Contract;
