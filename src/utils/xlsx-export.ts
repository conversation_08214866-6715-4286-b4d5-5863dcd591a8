// 已经在html引入XLSX库cdn链接

type HeaderItem = {
  name: string;
  key: string;
};

export type ExportExcelParams = {
  header: HeaderItem[];
  data: any[];
  fileName: string;
};

export function getTableData(header: HeaderItem[], data: any[]) {
  const list: any = [];
  data?.forEach((dataItem) => {
    const listItem = {};
    header?.forEach((item) => {
      listItem[item.key] = dataItem[item.key];
    });
    list.push(listItem);
  });
  return list;
}

export function exportExcelByXLSX(params: ExportExcelParams) {
  if (!window.XLSX) return;

  const { header, data, fileName } = params;

  // 创建工作表
  const headerKeys = header.map((item) => item.key);
  const ws = window.XLSX.utils.json_to_sheet(data, { header: headerKeys });

  // 添加自定义表头
  const headerNames = header.map((item) => item.name);
  window.XLSX.utils.sheet_add_aoa(ws, [headerNames], { origin: 'A1' });

  // 创建工作簿
  const wb = window.XLSX.utils.book_new();

  // 添加工作表到工作簿
  window.XLSX.utils.book_append_sheet(wb, ws, fileName);

  // 导出Excel文件
  window.XLSX.writeFile(wb, `${fileName}.xlsx`);
}
