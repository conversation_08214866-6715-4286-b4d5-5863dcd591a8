/*
 * @Author: your name
 * @Date: 2021-04-25 11:14:33
 * @LastEditTime: 2024-11-19 17:57:41
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/services/validate.ts
 */
/**
 * @Date: 2021-04-25 17:01:59
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: Do not edit
 * @FilePath: Do not edit
 */
import { bizAdminHeader } from '@/utils/constant';
import { rules } from '@hll/finance-utils';
import { request } from 'umi';

// 校验车辆识别码
/**
 * @Date: 2021-04-25 17:01:44
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: Do not edit
 * @FilePath: Do not edit
 * @param {string} carIdCode
 * @param {number} carType
 * @param {number} validType 车型库为1，车型库促销方案为2
 */
export async function validateCarCodeUnique(carIdCode: string, carType: number, validType: number) {
  // if (!/^[0-9A-Za-z]{17}$/.test(carIdCode)) {
  //   return Promise.reject(new Error('仅支持17位的数字或者字母'));
  // }
  return request(`/bizadmin/lease/car/existCarIdCode/${carIdCode}/${carType}`, {
    headers: bizAdminHeader,
  }).then(async (res) => {
    if (validType === 1) {
      if (res.data) {
        return Promise.reject(new Error(carType === 1 ? '车型码已添加' : '车辆识别代码已添加'));
      }
    } else if (validType === 2) {
      if (!res.data) {
        return Promise.reject(
          new Error(carType === 1 ? '车型码在车型库中不存在' : '车辆识别代码在车型库中不存在'),
        );
      }
    }
    return Promise.resolve();
  });
}

//不支持汉字
export const patterNoChineseValidate = {
  pattern: /^[^\u4e00-\u9fa5]*$/,
  message: '格式不正确',
};

//邮箱
export const emailValidate = {
  pattern: rules.email,
  message: '格式不正确',
};

//手机
export const telPhoneValidate = {
  pattern: /^1[3456789]\d{9}$/,
  message: '格式不正确',
};

//统一信用代码
export const orgCodeValidate = {
  pattern: /^[0-9a-zA-Z]{18}$/,
  message: '格式不正确',
};

//仅输入数字
export const letterOrNumber = {
  pattern: /^[0-9a-zA-Z]{1,}$/,
  message: '格式不正确',
};

//非数字
export const notNumber = { pattern: /\D/, message: '格式不正确' };

export const accountRule = {
  pattern: rules.account,
  message: '请输入正确的付款账号',
};

// 中文，数字英文，不包括~ ！@ ￥ % & * 【】 {} > < = + / ? · 。
export const companyNameRule = {
  pattern: rules.companyName,
  message: '请输入正确的付款户名',
};
