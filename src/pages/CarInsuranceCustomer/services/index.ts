import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { IclaimRechargeParams, IgetListParams, TransationItemDT } from '../data.d';

// 获取个人用户下车险客户列表
export async function getCarInsuranceCustomerList(params: IgetListParams) {
  return request(`/bizadmin/insurance/policy/account/query/personal/verify/List`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 获取个人用户下车险客户认证详情
export async function getCarInsuranceCustomerDetail(params: { id: string }) {
  return request(`/bizadmin/insurance/policy/account/query/personal/verify/detail`, {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 查询认领充值列表
export async function getClaimRechargeList(params: { externalOwnerId: string | number }) {
  return request(`/bizadmin/insurance/policy/account/query/remit/list`, {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 认领充值
export async function claimRecharge(params: IclaimRechargeParams) {
  const formData = new FormData();

  Object.keys(params).forEach((key) => {
    if (key === 'file') {
      const blob = new Blob([params[key]], { type: params[key]?.type });
      const fileObj = new File([blob], params[key]?.name, { type: params[key]?.type });
      formData.append(key, fileObj);
    } else {
      formData.append(key, params[key]?.toString());
    }
  });

  return request(`/bizadmin/insurance/policy/account/remit/submit`, {
    method: 'POST',
    data: formData,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 获取流水明细列表
export async function getTransationList(data: TransationItemDT) {
  return request(`/bizadmin/insurance/policy/account/query/wallet/flow`, {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 获取融租渠道专属账号信息
export async function getCarInsuranceCustomerAccountInfo(params: { channelCode: string }) {
  return request(`/bizadmin/channel/detail`, {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}
