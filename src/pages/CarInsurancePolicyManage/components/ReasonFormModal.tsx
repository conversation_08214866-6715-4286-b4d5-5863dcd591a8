import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

export interface ReasonFormModalProps {
  onFinish: (values: any) => Promise<boolean | void>;
  trigger?: React.ReactNode;
  submitter?: any;
  rules?: any;
  okText?: string;
  okType?: 'primary' | 'dashed' | 'link' | 'text' | 'default' | 'danger';
}

export interface ReasonFormModalRef {
  setFormInfo: (info: any) => void;
  show: () => void;
  hide: () => void;
  getForm: () => any;
  updateLoading: (status: boolean) => void;
}

const ReasonFormModal = forwardRef((props: ReasonFormModalProps, ref) => {
  const { onFinish, trigger, rules, submitter, ...rest } = props;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formInfo, setFormInfo] = useState({
    title: '原因',
    name: 'reason',
    placeholder: '请输入原因',
    okText: '确认',
    okType: 'primary',
    rules: [{ required: true, message: '请输入原因' }],
  });
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    show: () => {
      setVisible(true);
    },
    hide: () => {
      setVisible(false);
    },
    getForm: () => form,
    setFormInfo: (info: any) => {
      setFormInfo((pre) => {
        return {
          ...pre,
          ...info,
        };
      });
    },
    updateLoading: (status: boolean) => {
      setLoading(status);
    },
  }));

  return (
    <ModalForm
      form={form}
      loading={loading}
      modalProps={{
        width: 500,
        okText: formInfo?.okText,
      }}
      title={formInfo?.title}
      open={visible}
      onOpenChange={setVisible}
      onFinish={onFinish}
      submitter={
        submitter || {
          render: (_, dom) => <div className="danger-button">{dom[1]}</div>,
        }
      }
      {...rest}
    >
      <ProFormTextArea
        name={formInfo?.name}
        rules={rules || formInfo?.rules}
        fieldProps={{
          rows: 2,
        }}
        placeholder={formInfo?.placeholder}
      />
    </ModalForm>
  );
});

export default ReasonFormModal;
