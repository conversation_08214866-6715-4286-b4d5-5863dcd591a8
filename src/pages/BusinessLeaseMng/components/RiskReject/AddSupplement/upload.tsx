import { useRef, useState } from 'react';

import ImagePreview from '@/components/ImagePreview';
import { getAuthHeaders } from '@/utils/auth';
import { asyncReadFileToDataUrl, compressImg, getBaseUrl, readDataUrlToImg } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps as AntUploadProps } from 'antd';
import { message, Upload } from 'antd';
import { PDF_PREVIEW_IMG } from './consts';
import style from './upload.less';

type UploadProps = {
  disabled: boolean;
  value?: string;
  MAX?: number;
  onUploadChange?: (url: string) => void;
};

const MAX_SIZE = 50 * 1024 * 1024; // 图片限制大小50M

const baseUrl = getBaseUrl();

const FORMAT_REGEXP = /\.(pdf|jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;

const UploadImage = (props: UploadProps) => {
  const { onUploadChange, disabled } = props;

  const previewRef = useRef();
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传</div>
    </button>
  );

  const handleChange: AntUploadProps['onChange'] = async (info) => {
    const list = info.fileList.slice(0, props.MAX);
    setFileList(list);
    onUploadChange(list);
  };

  const isPdf = (name: string) => {
    return /\.(pdf)/i.test(name);
  };

  const beforeUpload = async (file) => {
    if (file.size > MAX_SIZE) {
      message.warning(`${file.name}超出图片大小限制50M!`);
      return Upload.LIST_IGNORE;
    }
    if (!FORMAT_REGEXP.test(file.name)) {
      message.warning('仅支持上传图片和PDF文件!');
      return Upload.LIST_IGNORE;
    }
    if (isPdf(file.name)) {
      return file;
    }
    try {
      const dataUrl: string = await asyncReadFileToDataUrl(file);
      if (dataUrl) {
        const img = await readDataUrlToImg(dataUrl);
        const blob = await compressImg(img, file.type, 1920, 1080);
        if (blob) {
          const imgFile = new File([blob], file.name, { type: file.type });
          return imgFile;
        }
      }
      return file;
    } catch (error) {
      return false;
    }
  };

  const handlePreview = async (file: any) => {
    if (file?.response?.data?.netWorkPath) {
      previewRef.current?.previewFile({
        url: file?.response?.data?.netWorkPath,
        fileName: file.name,
        urlList: fileList.map((item) => item?.response?.data?.netWorkPath),
      });
    }
  };

  return (
    <>
      <Upload
        className={style.upload}
        disabled={disabled}
        accept=".pdf,.png,.jpg,.jpeg,.bmp"
        action={`${baseUrl}/repayment/oss/common/uploadfile`}
        name="file"
        beforeUpload={beforeUpload}
        onChange={handleChange}
        multiple={true}
        listType="picture-card"
        headers={{ ...getAuthHeaders() }}
        data={{ acl: 'PUBLIC_READ', destPath: 'USER_ORDER_APPLY_MONEY' }}
        onPreview={handlePreview}
        fileList={fileList}
        previewFile={(file) =>
          new Promise(async (resolve) => {
            if (isPdf(file.name)) {
              return resolve(PDF_PREVIEW_IMG);
            }
            const fileUrl = await asyncReadFileToDataUrl(file);
            return resolve(fileUrl);
          })
        }
      >
        {fileList.length >= props.MAX ? null : uploadButton}
      </Upload>
      <ImagePreview ref={previewRef} />
    </>
  );
};

export default UploadImage;
