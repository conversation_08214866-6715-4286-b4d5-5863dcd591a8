/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-08-03 14:21:08
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-08-02 18:00:57
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/DeatialPager.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { Modal } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';

interface DetailPagerProps {
  list: any[];
  onChange: (item: 'string', index: number, list: string[]) => void;
  curItemIndex: number;
}

const DetailPager: React.FC<DetailPagerProps> = (
  { list = [], curItemIndex, onChange },
  ref: any,
) => {
  // console.log(list);
  const openTips = (type: 'Left' | 'Right') => {
    const tips = {
      mostLeft: '已经是本页第一条啦，请返回列表页，切换至上一页操作',
      mostRight: '已经是本页最后一条啦，请返回列表页，切换至下一页操作',
    };
    // console.log(tips?.[`most${type}`]);
    Modal.warning({
      title: tips?.[`most${type}`],
      centered: true,
      okText: '我知道了',
    });
  };
  const [itemIndex, setItemIndex] = useState(curItemIndex);
  const [curType, setCurType] = useState<'Left' | 'Right'>('Right');
  const changePage = (type: 'Left' | 'Right') => {
    // console.log(type);
    setCurType(type);
    let curItemTemp = itemIndex;
    if (type === 'Left') {
      curItemTemp = itemIndex - 1;
    }
    if (type === 'Right') {
      curItemTemp = itemIndex + 1;
    }
    // 超出边界提示
    if (curItemTemp < 0 || curItemTemp > list.length - 1 || list.length === 0) {
      openTips(type);
      return false;
    }
    setItemIndex(curItemTemp);
    onChange(list[curItemTemp], curItemTemp, list);
    return true;
    //
  };
  useImperativeHandle(ref, () => ({
    runNextOrLeft: (type?: any) => {
      return changePage(type || curType);
    },
  }));

  return (
    <div>
      <DoubleLeftOutlined onClick={() => changePage('Left')} style={{ color: '#1677ff' }} />
      <DoubleRightOutlined onClick={() => changePage('Right')} style={{ color: '#1677ff' }} />
    </div>
  );
};

export default forwardRef(DetailPager);
