/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-10-09 17:48:50
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-10-09 17:50:33
 * @FilePath: /code/lala-finance-biz-web/src/pages/Overdue/index.tsx
 * @Description: Overdue/index
 */
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import { useAccess } from '@umijs/max';
import { useMount } from 'ahooks';
import type { TabsProps } from 'antd';
import { Tabs } from 'antd';
import React, { useState } from 'react';
import { KeepAlive } from 'react-activation';
import AlreadyDistributeTable from './components/AlreadyDistributeTable';
import SettledTable from './components/SettledTable';
import WaitDistributeTable from './components/WaitDistributeTable';

const Overdue: React.FC<any> = () => {
  const access = useAccess();

  const [activeKey, setActiveKey] = useState('waitDistribute');

  useMount(() => {
    if (!access.hasAccess('biz_repay_no_dispatch')) {
      setActiveKey('alreadyDistribute');
    }
  });

  function getItems() {
    const items: TabsProps['items'] = [];
    if (access.hasAccess('biz_repay_no_dispatch')) {
      items.push({
        label: '待分配',
        key: 'waitDistribute',
        children: <WaitDistributeTable />,
        style: { margin: 0, padding: 0 },
      });
    }
    items.push(
      { label: '已分配', key: 'alreadyDistribute', children: <AlreadyDistributeTable /> },
      { label: '已结清', key: 'settled', children: <SettledTable /> },
    );
    return items;
  }

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Tabs
          destroyInactiveTabPane={true}
          items={getItems()}
          defaultActiveKey={activeKey}
          onChange={(key) => {
            setActiveKey(key);
          }}
          style={{ background: '#fff', padding: '0 10px' }}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name="businessMng/postLoanMng/overdue">
      <Overdue />
    </KeepAlive>
  </>
);
