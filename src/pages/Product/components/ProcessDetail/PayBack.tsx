/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-11-19 14:19:14
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/ProcessDetail/PayBack.tsx
 */
import React, { useEffect } from 'react';
import { Form, Row, Col, Input, Radio, Select } from 'antd';
import { REPAYMENT_CYCLESTART_TIME_ENUM, REPAYMENT_TERM_ENUMS, WITH_HOLDING_TIME } from '@/enums';

type PayBackProps = {
  isAddProduct?: boolean;
  payBackData?: any;
  isLease?: boolean;
  type?: boolean;
  name?: string;
  drawerStatus?: boolean;
  repayDateCalculateEnum?: string;
};

const PayBack: React.FC<PayBackProps> = (props) => {
  const {
    payBackData,
    isLease,
    type,
    isAddProduct,
    name,
    repayDateCalculateEnum,
    drawerStatus,
  } = props;

  const [form] = Form.useForm();
  // console.log(REPAYMENT_TERM_ENUMS);
  useEffect(() => {
    // const { repaymentTermEnums, repaymentTermEnum } = payBackData;
    form.setFieldsValue({
      ...payBackData,
      repaymentTerms: payBackData?.repaymentTerms?.length ? payBackData?.repaymentTerms : [],
      // repaymentDay: payBackData?.repaymentDay || `${type ? '放款' : '合同'}对日`, //不额外处理了
      repaymentTermEnums:
        payBackData?.repaymentTermEnums ||
        (payBackData?.repaymentTermEnum &&
          payBackData?.repaymentTermEnum
            .replace('FIXED_REPAYMENT_', '')
            .split('_')
            .map((num: string) => `FIXED_REPAYMENT_${num}`)),
    });
  }, [payBackData]);

  // useEffect(() => {
  //   form.validateFields();
  // }, [drawerStatus, payBackData]);

  const checkTag = (_, value: []) => {
    // console.log(_, value);
    // 暂时不校验
    let errorTips;
    // if (!value?.length) {
    //   errorTips = '请输入还款期限';
    // }
    // console.log(value);
    value.forEach((item: any, index) => {
      if (Number.isNaN(Number(item))) {
        errorTips = '请输入数字';
      }
      if (Number(item) > 100) {
        errorTips = '最大限制为100';
      }
      // console.log(value.indexOf(Number(item)), value.indexOf(Number(item)) !== index);
      if (value.indexOf(Number(item)) !== -1 && value.indexOf(Number(item)) !== index) {
        errorTips = '请勿重复输入';
      }
    });
    if (errorTips) {
      return Promise.reject(new Error(errorTips));
    }
    // const repaymentTermsTemp = value.map((item: any) => {
    //   return Number(item);
    // });

    // form?.setFieldsValue({
    //   ...form.getFieldsValue(),
    //   repaymentTerms: [...new Set(repaymentTermsTemp)],
    // });

    return Promise.resolve();
  };
  return (
    <Form
      form={form}
      name={name}
      labelCol={{ span: 10 }}
      wrapperCol={{ span: 10 }}
      labelAlign="right"
      // onValuesChange={payBackValueChange}
    >
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item name="repaymentModeEnum" label="还款方式">
            {/* <Input placeholder="一次本息" /> */}
            <Select disabled>
              <Select.Option value="ALL_AT_ONCE">一次本息</Select.Option>
              <Select.Option value="PRINCIPAL_EQUALS_INTEREST">等额本息</Select.Option>
            </Select>
          </Form.Item>
        </Col>

        <Col span={10} offset={1}>
          {isLease ? (
            <Form.Item
              name="repaymentTerms"
              label="还款期限"
              rules={[{ required: !drawerStatus }, { validator: checkTag }]}
            >
              <Select disabled={!isAddProduct} mode="tags">
                {/* <Select.Option></Select.Option> */}
              </Select>
            </Form.Item>
          ) : (
            <Form.Item name="repaymentTermEnums" label="还款期限">
              <Select disabled={!isAddProduct} mode="tags">
                {Object.keys(REPAYMENT_TERM_ENUMS).map((key) => (
                  <Select.Option key={key} value={key}>
                    {REPAYMENT_TERM_ENUMS[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          )}
        </Col>
        {repayDateCalculateEnum === 'CUSTOMIZE_REPAY_DATE' && (
          <Col span={10} offset={1}>
            {isLease ? (
              <Form.Item name="repaymentDay" label="默认还款日期">
                <Select disabled>
                  <Select.Option value={0}>{type ? '放款' : '合同'}对日</Select.Option>
                </Select>
              </Form.Item>
            ) : (
              // <Form.Item>
              //   T+
              <Form.Item name="repaymentDay" label="默认还款日期">
                <Input addonBefore="D+" suffix="日" disabled />
              </Form.Item>
            )}
          </Col>
        )}

        <Col span={10} offset={1}>
          <Form.Item name="repaymentCycleStartTimeEnum" label="还款周期起始时间">
            <Select disabled>
              {Object.keys(REPAYMENT_CYCLESTART_TIME_ENUM).map((key) => (
                <Select.Option key={key} value={key}>
                  {REPAYMENT_CYCLESTART_TIME_ENUM[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={10} offset={1}>
          <Form.Item name="partialRepayment" label="是否允许部分还款">
            <Radio.Group value={1} disabled>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        {!isLease ? (
          <Col span={10} offset={1}>
            <Form.Item name="withholdingChannels" label="代扣渠道">
              <Input placeholder="" disabled />
            </Form.Item>
          </Col>
        ) : null}
        <Col span={10} offset={1}>
          <Form.Item name="withholdingTime" label="代扣时间">
            <Select disabled mode="multiple">
              {Object.keys(WITH_HOLDING_TIME).map((key) => (
                <Select.Option key={key} value={key}>
                  {WITH_HOLDING_TIME[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default PayBack;
