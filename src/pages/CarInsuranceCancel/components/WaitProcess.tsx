import ImagePreview, { ImagePreviewInstance } from '@/components/ImagePreview';
import { getBizadminUploadAction } from '@/pages/BusinessExcel/utils';
import { getAuthHeaders } from '@/utils/auth';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Radio,
  Row,
  Tooltip,
  Upload,
} from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import { DetailInfo, ListItem } from '../const';
import '../index.less';
import { cancelCancellation, uploadCancellation } from '../service';
import BasicInfo from './BasicInfo';

interface ModalProps {
  closeModal: (refresh?: boolean) => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  detailInfo: DetailInfo;
}

const Page: React.FC<ModalProps> = ({ visible, closeModal, currentUser, record, detailInfo }) => {
  const { orderNo, bizNo } = record;
  const [submitLoading, setSubmitLoading] = useState(false);

  const [formOfWaitProcess] = Form.useForm();
  // 提交
  const handleWaitProcessSubmit = async () => {
    try {
      await formOfWaitProcess.validateFields();
    } catch {
      return Promise.reject();
    }
    // 检查文件是否上传完成
    const fileList = formOfWaitProcess.getFieldValue('fileList')?.fileList || [];
    if (fileList?.length === 0) {
      message.error('请上传文件');
      return;
    }
    console.log('fileList', fileList);
    if (fileList.some((item: any) => item?.status !== 'done')) {
      message.error('文件未上传完成');
      return;
    }
    //
    const formValues = formOfWaitProcess.getFieldsValue();
    console.log('formValues2333', formValues);
    const params: any = {
      bizNo,
      createdBy: currentUser?.accountName,
      cancellationAmount: formValues?.cancellationAmount,
      cancellationRate: formValues?.hasRate === '1' ? formValues?.cancellationRate : undefined,
      actualCancellationAmount: formValues?.actualCancellationAmount,
      expectedPaymentTime: dayjs(formValues?.expectedPaymentTime).format('YYYY-MM-DD'),
      fileList: formValues?.fileList?.fileList?.map((item: any) => {
        return {
          fileName: item?.name,
          filePath: item?.response?.data?.filePath,
        };
      }),
    };
    // 提交表单
    console.log('params', params);
    const res = (await uploadCancellation(params).catch(() => {})) || {};
    setSubmitLoading(false);
    if (res?.success) {
      message.success('操作成功');
      closeModal(true);
    }
  };
  // 撤销二次确认
  const [cancelLoading, setCancelLoading] = useState(false);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const openConfirm = () => setConfirmVisible(true);
  const closeConfirm = () => setConfirmVisible(false);
  // 撤销
  const handleCancelAction = async () => {
    setCancelLoading(true);
    const params = {
      bizNo,
      createdBy: currentUser?.accountName,
    };
    const res: any = (await cancelCancellation(params).catch(() => {})) || {};
    setCancelLoading(false);
    if (res?.success) {
      message.success('撤销成功');
      closeModal(true);
    }
  };
  // 上传预览
  const previewRef = useRef<ImagePreviewInstance>();
  const handleUploadPreview = async (file: any, urlList: string[]) => {
    console.log('urlList', urlList);
    console.log('file', file);
    if (file?.response?.data?.netWorkPath) {
      previewRef.current?.previewFile({
        url: file?.response?.data?.netWorkPath,
        fileName: file?.name,
        urlList,
      });
    }
  };

  return (
    <>
      <Modal
        width={960}
        destroyOnClose
        title={`退保详情`}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Popconfirm
              title="确认撤销？"
              open={confirmVisible}
              onCancel={closeConfirm}
              onConfirm={handleCancelAction}
              okButtonProps={{ loading: cancelLoading }}
            >
              <Button loading={cancelLoading} onClick={openConfirm}>
                撤销
              </Button>
            </Popconfirm>
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            <Button type="primary" onClick={handleWaitProcessSubmit} loading={submitLoading}>
              提交
            </Button>
          </>
        }
      >
        {/*  */}
        <BasicInfo detailInfo={detailInfo} orderStatus={detailInfo?.orderStatus} />
        <Form
          form={formOfWaitProcess}
          labelCol={{ span: 7 }}
          labelAlign="left"
          wrapperCol={{ span: 17 }}
        >
          <Row gutter={[16, 0]}>
            <Col span={12}>
              <Form.Item
                label="批单退保金额"
                name="cancellationAmount"
                rules={[
                  { required: true, message: '请输入' },
                  {
                    // 0-99999的数字，最多两位小数,不能填写除小数点和数字以外的字符
                    validator: (_, val) => {
                      if (isNaN(val) || val?.includes('e')) {
                        return Promise.reject('请输入数字');
                      }
                      if (val !== '' && (val < 0 || val > 99999)) {
                        return Promise.reject('请输入0-99999的数字');
                      }
                      if (val.includes('.') && val.toString().split('.')[1].length > 2) {
                        return Promise.reject('最多两位小数');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item
                label="是否有代扣税"
                name="hasRate"
                initialValue={'0'}
                labelCol={{ span: 12 }}
                wrapperCol={{ span: 12 }}
                rules={[{ required: true, message: '请选择' }]}
              >
                <Radio.Group
                  style={{ width: '100%' }}
                  options={[
                    { label: '是', value: '1' },
                    { label: '否', value: '0' },
                  ]}
                />
              </Form.Item>
            </Col>
            {/* 是否有代扣税: 是, 展示利率 */}
            <Form.Item
              shouldUpdate={(prevValues, curValues) => curValues.hasRate !== prevValues.hasRate}
              noStyle
            >
              {() => {
                const { hasRate } = formOfWaitProcess.getFieldsValue();
                if (hasRate !== '1') {
                  return null;
                }
                return (
                  <Col span={5}>
                    <Form.Item
                      name="cancellationRate"
                      initialValue={'6'}
                      preserve={false}
                      rules={[
                        { required: true, message: '请输入' },
                        {
                          // 1-100的数字，最多两位小数,不能填写除小数点和数字以外的字符
                          validator: (_, val) => {
                            if (isNaN(val) || val?.includes('e')) {
                              return Promise.reject('请输入数字');
                            }
                            if (val !== '' && (val < 1 || val > 100)) {
                              return Promise.reject('请输入1-100的数字');
                            }
                            if (val.includes('.') && val.toString().split('.')[1].length > 2) {
                              return Promise.reject('最多两位小数');
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Input type="number" suffix="%" placeholder="请输入利率" />
                    </Form.Item>
                  </Col>
                );
              }}
            </Form.Item>
            <Col span={12}>
              <Form.Item
                shouldUpdate={(preValues, nextValues) => {
                  return (
                    nextValues.cancellationAmount !== preValues.cancellationAmount ||
                    nextValues.hasRate !== preValues.hasRate ||
                    nextValues.cancellationRate !== preValues.cancellationRate
                  );
                }}
                noStyle
              >
                {() => {
                  console.log('实际退保金额', formOfWaitProcess.getFieldsValue());
                  let actualCancellationAmount = '';
                  const { cancellationAmount, hasRate } = formOfWaitProcess.getFieldsValue();
                  let { cancellationRate } = formOfWaitProcess.getFieldsValue();
                  // 切换是否利率，利率获取不到值(切换是否利率时，可能取不到利率值，为undefined)，设置默认值6
                  if (hasRate === '1' && cancellationRate === undefined) {
                    cancellationRate = '6';
                  }
                  const cancellationRateVal =
                    hasRate === '1'
                      ? BigNumber(cancellationRate || '0')
                          .div('100')
                          .plus('1')
                          .toFixed(4)
                          .toString()
                      : '1';
                  // 无利率
                  if (hasRate === '0') {
                    if (
                      cancellationAmount === undefined ||
                      cancellationAmount === '' ||
                      isNaN(cancellationAmount) ||
                      cancellationAmount > 99999 ||
                      cancellationAmount < 0
                    ) {
                      actualCancellationAmount = '-';
                    } else {
                      actualCancellationAmount = BigNumber(cancellationAmount)
                        .toFixed(2)
                        .toString();
                    }
                  }
                  // 有利率
                  else if (hasRate === '1') {
                    if (
                      // 金额
                      cancellationAmount !== undefined &&
                      cancellationAmount !== '' &&
                      !isNaN(cancellationAmount) &&
                      cancellationAmount >= 0 &&
                      cancellationAmount <= 99999 &&
                      // 利率
                      cancellationRate !== '' &&
                      !isNaN(cancellationRate) &&
                      cancellationRate >= 1 &&
                      cancellationRate <= 100
                    ) {
                      actualCancellationAmount = BigNumber(cancellationAmount)
                        .div(cancellationRateVal)
                        .toFixed(2)
                        .toString();
                    } else {
                      actualCancellationAmount = '-';
                    }
                  }
                  formOfWaitProcess.setFieldValue(
                    'actualCancellationAmount',
                    actualCancellationAmount,
                  );
                  return (
                    <Form.Item
                      label={
                        <>
                          <span>实际退保金额&nbsp;</span>
                          <Tooltip title="计算结果仅供参考">
                            <QuestionCircleOutlined />
                          </Tooltip>
                        </>
                      }
                      name="actualCancellationAmount"
                      rules={[{ required: true, message: '' }]}
                    >
                      <Input variant="borderless" readOnly />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="预计到账日期"
                name="expectedPaymentTime"
                rules={[{ required: true, message: '必填项' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  // disabledDate={(current: any) =>
                  //   current && current < dayjs().endOf('day').subtract(1, 'days')
                  // }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="批单及其他资料"
                name="fileList"
                rules={[{ required: true, message: '必填项' }]}
                // labelCol={{ span: 8 }}
                // wrapperCol={{ span: 16 }}
              >
                <Upload.Dragger
                  name="file"
                  className="upload-area-inline"
                  multiple={true}
                  accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
                  action={getBizadminUploadAction()}
                  headers={{ ...getAuthHeaders(), 'hll-appid': 'bme-finance-bizadmin-svc' }}
                  data={{ acl: 'PRIVATE_ACL', destPath: 'CAR_POLICY_CANCELLATION' }}
                  beforeUpload={(file: any) => {
                    // 限制大小：50m
                    const sizeValid = file.size / 1024 / 1024 <= 50;
                    // 超过限制
                    if (!sizeValid) {
                      message.error('文件大小不能超过50M');
                    }
                    return sizeValid || Upload.LIST_IGNORE;
                  }}
                  onPreview={(file: any) => {
                    console.log('onPreview1', file);
                    console.log('onPreview2', formOfWaitProcess.getFieldValue('fileList'));
                    const urlList =
                      formOfWaitProcess
                        .getFieldValue('fileList')
                        ?.fileList?.map((item: any) => item?.response?.data?.netWorkPath) || [];
                    return handleUploadPreview(file, urlList);
                  }}
                >
                  <p className="ant-upload-text primary-color">点击或拖动文件到此处上传</p>
                </Upload.Dragger>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
      {/* 图片预览 */}
      <ImagePreview ref={previewRef as any} />
    </>
  );
};
export default Page;
