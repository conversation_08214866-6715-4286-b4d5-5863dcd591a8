import TableTransfer from '@/components/TableTransfer';
import { LICENSE_TYPES_MAP } from '@/enums';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import type { TCarInfoTableItem } from '../data.d';

const carMap = {
  1: '新车',
  2: '二手车',
};

const tableColumns = [
  {
    title: '车辆编码',
    dataIndex: 'businessKey',
  },
  {
    title: '车辆类型',
    dataIndex: 'carType',
    render: (val: number) => {
      switch (val) {
        case 1:
          return '新车';
        case 2:
          return '二手车';
        default:
          return '-';
      }
    },
  },
  {
    title: '车辆名称',
    dataIndex: 'name',
  },
  {
    title: '上牌类型',
    dataIndex: 'licenseType',
    render: (val: number[]) => {
      return LICENSE_TYPES_MAP[val?.toString()] || '-';
    },
  },
  {
    title: '价格',
    dataIndex: 'totalPrice',
  },
  {
    title: '库存城市',
    dataIndex: 'carCityList',
    render: (val: string[]) => {
      if (Array.isArray(val) && val.length > 0) {
        return val.join(',');
      }
      return '-';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    render: (val: number) => {
      switch (val) {
        case 0:
          return '禁用';
        case 1:
          return '启用';
        case -1:
          return '删除';
        default:
          return '-';
      }
    },
  },
];

export type TCarInfoTransferProps = {
  notConfigData: TCarInfoTableItem[];
  hasConfigData: TCarInfoTableItem[];
  disabledOptions?: { key: string; val: string };
};

const CarInfoTransfer = forwardRef((props: TCarInfoTransferProps, ref) => {
  const { notConfigData, hasConfigData, disabledOptions } = props;
  /**
   * 存储被勾选的 keys
   */
  const [targetKeys, setTargetKeys] = useState<string[]>([]);

  /**
   * 暴露 targetKeys 给父组件
   */
  useImperativeHandle(ref, () => ({
    targetKeys,
    type: 'car',
  }));

  useEffect(() => {
    setTargetKeys(hasConfigData.map((item) => item.businessKey));
  }, []);

  return (
    <TableTransfer
      dataSource={[...notConfigData, ...hasConfigData]}
      targetKeys={targetKeys}
      showSearch
      onChange={(nextTargetKeys: string[]) => {
        setTargetKeys(nextTargetKeys);
      }}
      filterOption={(inputValue: string, item: TCarInfoTableItem) => {
        const filterList = ['businessKey', 'carType', 'name', 'carCityList'];
        return filterList.some((val) => {
          if (val === 'carType') {
            return carMap[item[val]].indexOf(inputValue) !== -1;
          }
          if (val === 'carCityList') {
            if (Array.isArray(item[val]) && item[val].length > 0) {
              return item[val].join(',').indexOf(inputValue) !== -1;
            }
            return false;
          }
          return item[val].indexOf(inputValue) !== -1;
        });
      }}
      leftColumns={tableColumns}
      rightColumns={tableColumns}
      disabledOptions={disabledOptions}
    />
  );
});

export default CarInfoTransfer;
