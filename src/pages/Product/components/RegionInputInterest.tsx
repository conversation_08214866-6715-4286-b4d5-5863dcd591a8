/*
 * @Date: 2023-08-15 16:12:17
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-08-16 11:32:19
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/RegionInputInterest.tsx
 * @Description:
 */
/*
 * @Author: your name
 * @Date: 2021-10-20 15:19:12
 * @LastEditTime: 2021-11-05 16:29:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/PriceInput.tsx
 */

import React, { useState } from 'react';
import { Input, InputNumber } from 'antd';

// type Currency = 'rmb' | 'dollar';

type RegionItem = 'CLOSE_RANGE' | 'OPEN_RANGE';
interface RegionValue {
  minOpenOrCloseRange?: RegionItem; // 最小值开闭区间
  minValue?: number; // 最小值
  maxOpenOrCloseRange?: RegionItem; // 最大值开闭区间
  maxValue?: number; // 最大值开闭区间
}
interface RegionInputProps {
  value?: RegionValue;
  onChange?: (value: RegionValue) => void;
  disabledForm?: boolean;
  minValueReplace?: string; // 字段替换名
  maxValueReplace?: string; // 字段替换名
  precision?: number;
}

const RegionInput: React.FC<RegionInputProps> = ({
  value = {},
  onChange,
  disabledForm,
  minValueReplace,
  maxValueReplace,
  precision,
}) => {
  // console.log(value, '我是组件value');

  const [minValue, setMinValue] = useState<number>();
  const [maxValue, setMaxValue] = useState<number>();

  const triggerChange = (changedValue: {
    minValue?: number; // 最小值
    maxValue?: number;
  }) => {
    onChange?.({
      [minValueReplace || 'minValue']: minValue,
      [maxValueReplace || 'maxValue']: maxValue,
      ...value,
      ...changedValue,
    });
  };

  // 加载初始值
  const onMinValueChange = (newNumber: any) => {
    if (Number.isNaN(minValue)) {
      return;
    }
    if (!('minValue' in value && `${minValueReplace}` in value)) {
      setMinValue(newNumber);
    }
    triggerChange({ [minValueReplace || 'minValue']: newNumber });
  };

  const onMaxValueChange = (newNumber: any) => {
    if (Number.isNaN(maxValue)) {
      return;
    }
    if (!('maxValue' in value && `${maxValueReplace}` in value)) {
      setMaxValue(newNumber);
    }
    triggerChange({ [maxValueReplace || 'maxValue']: newNumber });
  };

  return (
    <div style={{ display: 'flex' }}>
      <Input.Group compact>
        <InputNumber
          style={{ width: '95%' }}
          placeholder="A"
          value={value?.[minValueReplace || 'minValue']}
          precision={precision}
          min={0}
          addonAfter="%"
          // filedProps={{allowClear:true}}
          disabled={disabledForm}
          onChange={onMinValueChange}
        />
      </Input.Group>
      <span style={{ margin: '0 8px' }}>-</span>
      <Input.Group compact>
        <InputNumber
          style={{ width: '95%' }}
          placeholder="B"
          value={value?.[maxValueReplace || 'maxValue']}
          precision={precision}
          min={0}
          addonAfter="%"
          disabled={disabledForm}
          onChange={onMaxValueChange}
        />
      </Input.Group>
    </div>
  );
};

export default React.memo(RegionInput);
