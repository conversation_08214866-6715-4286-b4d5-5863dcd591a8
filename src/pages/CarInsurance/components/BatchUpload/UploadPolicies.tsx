/** 上传投保单 */
import type { FormInstance } from 'antd';
import React, { memo } from 'react';
import { STEP_FILE_TYPE } from '../../type';
import CarOcrContent from './CarOcrContent';

interface UploadPoliciesIF {
  setLoading: (val: boolean) => void;
  onUploadStatusChange?: (hasUploading: boolean) => void;
  onErrorStatusChange?: (hasError: boolean) => void;
  otdName: string;
  form: FormInstance;
}

const UploadPolicies: React.FC<UploadPoliciesIF> = (props) => {
  const { setLoading, onUploadStatusChange, onErrorStatusChange, form, otdName } = props;

  return (
    <CarOcrContent
      name="policy"
      otdName={otdName}
      form={form}
      variant="policy"
      fileType={STEP_FILE_TYPE.POLICY}
      hasOption
      setLoading={setLoading}
      onUploadStatusChange={onUploadStatusChange}
      onErrorStatusChange={onErrorStatusChange}
    />
  );
};

export default memo(UploadPolicies);
