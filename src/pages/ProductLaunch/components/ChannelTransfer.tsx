/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-12-16 17:35:42
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-18 17:54:11
 * @FilePath: /lala-finance-biz-web/src/pages/ProductLaunch/components/ChannelTransfer.tsx
 * @Description: ChannelTransfer
 */
import TableTransfer from '@/components/TableTransfer';
import { CHANNEL_TYPES_MAP } from '@/enums';
import { transformLicenseTypesToString } from '@/pages/Channel';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import type { TChannelTableItem } from '../data.d';

const tableColumns = [
  {
    title: '渠道编码',
    dataIndex: 'businessKey',
  },
  {
    title: '渠道类型',
    dataIndex: 'channelType',
    render: (val: number) => {
      return CHANNEL_TYPES_MAP[val] || '-';
    },
  },
  {
    title: '渠道名称',
    dataIndex: 'name',
  },
  {
    title: '上牌类型',
    dataIndex: 'licenseTypeList',
    render: (val: number[]) => {
      return transformLicenseTypesToString(val) || '-';
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    render: (val: number) => {
      switch (val) {
        case 0:
          return '禁用';
        case 1:
          return '启用';
        case -1:
          return '删除';
        default:
          return '-';
      }
    },
  },
];

export type TChannelInfoTransferProps = {
  notConfigData: TChannelTableItem[];
  hasConfigData: TChannelTableItem[];
  disabledOptions?: { key: string; val: string };
};

const ChannelTransfer = forwardRef((props: TChannelInfoTransferProps, ref) => {
  const { notConfigData, hasConfigData, disabledOptions } = props;
  /**
   * 存储被勾选的 keys
   */
  const [targetKeys, setTargetKeys] = useState<string[]>([]);

  useEffect(() => {
    setTargetKeys(hasConfigData.map((item) => item.businessKey));
  }, []);

  /**
   * 暴露 targetKeys 给父组件
   */
  useImperativeHandle(ref, () => ({
    targetKeys,
    type: 'channel',
  }));

  return (
    <TableTransfer
      dataSource={[...notConfigData, ...hasConfigData]}
      targetKeys={targetKeys}
      showSearch
      onChange={(nextTargetKeys: string[]) => setTargetKeys(nextTargetKeys)}
      filterOption={(inputValue: string, item: TChannelTableItem) => {
        return (
          item.businessKey.indexOf(inputValue) !== -1 ||
          CHANNEL_TYPES_MAP[item.channelType].indexOf(inputValue) !== -1 ||
          item.name.indexOf(inputValue) !== -1
        );
      }}
      leftColumns={tableColumns}
      rightColumns={tableColumns}
      disabledOptions={disabledOptions}
    />
  );
});

export default ChannelTransfer;
