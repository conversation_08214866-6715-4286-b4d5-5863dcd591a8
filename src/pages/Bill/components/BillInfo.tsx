/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2021-04-28 15:36:57
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import React from 'react';
import { Card, Row, Col } from 'antd';
import globalStyle from '@/global.less';

const BillInfo: React.FC<any> = (props) => {
  const { billInfo } = props;
  const billMap = {
    billNo: '账单ID',
    billCycle: '账单周期',
    billingDate: '出账日',
    confirmationDate: '确认账单日',
    repaymentDate: '还款日',
    actualConfirmationDate: '实际确认账单日',
    actualRepaymentDate: '实际还款日',
    totalAmount: '账单总金额',
    amountPaid: '已还款金额',
    handlingAmount: '还款中金额',
    pendingAmount: '待还款金额',
  };
  return (
    <Card title="账单信息" className={globalStyle.mt30}>
      <Row>
        {Object.keys(billMap).map((item) => {
          const value = (billInfo && billInfo[item]) || '';
          return (
            <Col span={8} key={item}>
              <div className={globalStyle.lineHeight40}>
                <span>{billMap[item]}:</span>
                <span className={globalStyle.ml20}>{value}</span>
              </div>
            </Col>
          );
        })}
      </Row>
    </Card>
  );
};

export default BillInfo;
