/*
 * @Author: your name
 * @Date: 2021-08-04 10:00:42
 * @LastEditTime: 2023-03-28 17:11:07
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/ShowIncomingInfo.tsx
 */
import globalStyle from '@/global.less';
import { Link } from '@umijs/max';
import { Col, Row } from 'antd';
import React from 'react';

const styleShowInfo: { styleText: React.CSSProperties | undefined } = {
  styleText: {
    overflow: 'hidden',
    maxWidth: 240,
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    display: 'inline-block',
    // lineHeight: 1,
    verticalAlign: 'bottom',
    cursor: 'pointer',
  },
};
const ShowIncomingInfo: React.FC<any> = (props) => {
  /**
   * @data 数据
   * @title card标题
   * @infoMap lable-value对应关系
   * @itemMap 当value时数值，需要映射到额外的lable时
   * @linkMap 当有链接的时候单独处理
   * @loading: 加载中
   */
  const { data, infoMap, itemMap, linkMap } = props;

  return (
    <>
      <Row className={globalStyle.pl20}>
        {Object.keys(infoMap).map((item, index) => {
          const value =
            (data &&
              // eslint-disable-next-line no-nested-ternary
              (itemMap && itemMap[item]
                ? itemMap[item][data[item]]
                : data[item] === 0
                ? `${data[item]}`
                : data[item])) ||
            '';
          return (
            <Col span={8} key={item + index}>
              <div className={globalStyle.lineHeight40}>
                <span className={globalStyle.lineHeight40}>{infoMap[item]}:</span>
                {linkMap && linkMap[item] ? (
                  <Link to={linkMap[item]} className={globalStyle.ml20}>
                    {value}
                  </Link>
                ) : (
                  <span className={globalStyle.ml20} title={value} style={styleShowInfo.styleText}>
                    {value}
                  </span>
                )}
              </div>
            </Col>
          );
        })}
      </Row>
      {props.children}
    </>
  );
};

export default ShowIncomingInfo;
