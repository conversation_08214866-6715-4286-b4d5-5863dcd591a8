/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2023-03-08 15:26:27
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2023-03-28 17:24:40
 * @FilePath: /lala-finance-biz-web/src/pages/CallPay/components/RepayAttach.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import ImagePreview from '@/components/ImagePreview';
import { Button, Modal } from 'antd';
import React from 'react';

export type RepayAttachProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  imgList: [];
};

const RepayAttach: React.FC<RepayAttachProps> = ({ visible, close, imgList }) => {
  return (
    <Modal
      width={400}
      // bodyStyle={{ padding: '32px 40px 48px' }}
      destroyOnClose
      centered
      footer={
        <>
          <Button type="primary" onClick={() => close()}>
            确定
          </Button>
        </>
      }
      open={visible}
      onCancel={close}
      title="还款凭证"
    >
      <div style={{ padding: 10 }}>
        {imgList?.length
          ? imgList.reduce(
              (pre: React.ReactNode, cur: { netWorkPath: string; name: string; url?: string }) => {
                return (
                  <>
                    {pre}
                    <ImagePreview
                      url={cur?.netWorkPath || cur?.url || ''}
                      fileName={cur?.name}
                      urlList={imgList.map((item: any) => item?.netWorkPath || item.url || '')}
                    >
                      <a target="_blank">{cur?.name}</a>
                    </ImagePreview>
                  </>
                );
              },
              <></>,
            )
          : '暂无数据'}
      </div>
    </Modal>
  );
};

export default RepayAttach;
