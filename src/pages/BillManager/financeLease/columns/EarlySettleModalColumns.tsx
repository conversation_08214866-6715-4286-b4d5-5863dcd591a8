/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-11-15 14:46:48
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-01-14 16:29:31
 * @FilePath: /code/lala-finance-biz-web/src/pages/BillManager/financeLease/columns/EarlySettleModalColumns.tsx
 * @Description: EarlySettleModalColumns
 */
// @ts-ignore
import type { ProColumns } from '@ant-design/pro-components';
import type { IbillListItem } from '../types';

export const EarlySettleModalColumns: ProColumns<IbillListItem>[] = [
  {
    title: '账单ID',
    dataIndex: 'billNo',
    key: 'billNo',
  },
  {
    title: '订单号',
    dataIndex: 'orderNo',
    key: 'orderNo',
  },
  {
    title: '账单状态',
    dataIndex: 'statusName',
    key: 'statusName',
  },
  {
    title: '车架号',
    dataIndex: 'subjectMatterNo',
    key: 'subjectMatterNo',
  },
  {
    title: '用户姓名',
    dataIndex: 'accountName',
    key: 'accountName',
  },

  {
    title: '未还总额(元)',
    dataIndex: 'totalAmountUnpaid',
    key: 'totalAmountUnpaid',
  },
  {
    title: '未还本金(元)',
    dataIndex: 'totalPrincipalUnpaid',
    key: 'totalPrincipalUnpaid',
  },
  {
    title: '未还利息(元)',
    dataIndex: 'totalInterestUnpaid',
    key: 'totalInterestUnpaid',
  },
  {
    title: '未还罚息(元)',
    dataIndex: 'totalOverduePenaltyUnpaid',
    key: 'totalOverduePenaltyUnpaid',
  },
  {
    title: '未还违约金(元)',
    dataIndex: 'totalBreach',
    key: 'totalBreach',
  },
];
