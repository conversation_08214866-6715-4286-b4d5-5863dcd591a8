/* eslint-disable react-hooks/exhaustive-deps */
import HeaderTab from '@/components/HeaderTab';
import ProCard from '@ant-design/pro-card';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useAccess } from '@umijs/max';
import { Descriptions, Table } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import { getCarInsuranceDetail } from '../CarInsurance/services';
import type { IdetailData } from '../Product/type';

import RepaymentInfo from './components/RepaymentInfo';
import { getCarLoanInfo, getCarRepayment, getIncommingInfo } from './services';
import type { IgetCarLoanInfoItem, IgetIncommingInfo } from './types';
import { loanModelMap, loanStatusMap, loanTypeMap } from './types';

const Detail = () => {
  const { orderNo } = (history as any).location.query;
  const [incomingInfo, setIncomingInfo] = useState<IgetIncommingInfo & IdetailData>({} as any);
  const [repayment, setRepayment] = useState<any>([]);
  const [loanInfo, setLoanInfo] = useState<IgetCarLoanInfoItem[]>([]);
  const access = useAccess();
  // 进件信息
  const incomingColumns = [
    { label: '订单号', value: 'orderNo' },
    { label: '用户ID', value: 'userNo' },
    { label: '用户名称', value: 'userName' },
    { label: '产品名称', value: 'productName' },
    { label: '渠道名称', value: 'channel' },
    { label: '贷款总额', value: 'repayInfo.loanAmount' },
    { label: '期限', value: 'loanTerm' },
    { label: '还款方式', value: 'repayType' },
    { label: '年利率', value: 'interestRate' },
    { label: '进件时间', value: 'createdAt' },
  ];
  // 还款计划
  const repaymentColums = [
    { dataIndex: 'termDetail', title: '期限' },
    { dataIndex: 'amountDue', title: '应还总额' },
    { dataIndex: 'principal', title: '应还本金' },
    { dataIndex: 'interest', title: '应还利息' },
    { dataIndex: 'repayTime', title: '应还日期' },
  ];
  // 放款信息
  const loanColumns = [
    { label: '放款流水号', value: 'lendingNo' },
    {
      label: '放款类型',
      value: 'type',
      render(value: string) {
        return loanTypeMap[value];
      },
    },
    { label: '放款金额', value: 'amount' },
    { label: '放款主体', value: 'lendingMaster' },
    { label: '收款主体', value: 'receiptMaster' },
    {
      label: '是否发生资金流',
      value: 'fundFlow',
      render(value: string) {
        return value ? '是' : '否';
      },
    },
    {
      label: '放款方式',
      value: 'lendingModel',
      render(value: string) {
        return loanModelMap[value];
      },
    },
    { label: '放款周期', value: 'repayType' },
    { label: '放款时间', value: 'lendingTime' },
    {
      label: '放款状态',
      value: 'status',
      render(value: string) {
        return loanStatusMap[value];
      },
    },
    { label: '车牌号', value: 'plateNo' },
    { label: '备注', value: 'createdAt' },
  ];
  function init() {
    Promise.allSettled([getCarInsuranceDetail(orderNo), getIncommingInfo(orderNo)]).then(
      (results) => {
        // 将成功的数组的结果合并
        const data = results.reduce((pre, cur) => {
          return cur?.status === 'fulfilled' ? { ...pre, ...cur.value } : { ...pre };
        }, {});
        setIncomingInfo(data as any);
      },
    );

    getIncommingInfo(orderNo).then((data) => {
      setIncomingInfo((state) => ({ ...state, ...data }));
    });
    getCarInsuranceDetail(orderNo).then((data) => {
      setIncomingInfo((state) => ({ ...state, ...data }));
    });
    getCarRepayment(orderNo).then((data) => {
      const { listRspList } = data;
      const repayment1 = {
        ...listRspList[0],
        children: listRspList.filter((item) => item.id),
      };
      setRepayment([repayment1]);
    });
    if (access.hasAccess('repayment_detail_lending_postLoanMng_detail')) {
      getCarLoanInfo(orderNo).then(setLoanInfo);
    }
  }
  useEffect(() => {
    init();
  }, []);

  console.log('incomingInfo', incomingInfo);

  return (
    <div>
      <HeaderTab />
      <PageContainer>
        <ProCard title="进件信息" collapsible>
          <Descriptions>
            {incomingColumns.map((item) => {
              const { label, value } = item;
              return (
                <Descriptions.Item label={label}>
                  {value.split('.').reduce((pre, cur) => {
                    return pre?.[cur];
                  }, incomingInfo)}
                </Descriptions.Item>
              );
            })}
            <Descriptions.Item label={'还款计划'}>
              <Table
                columns={repaymentColums}
                dataSource={repayment}
                pagination={false}
                size="small"
              />
            </Descriptions.Item>
          </Descriptions>
        </ProCard>
        {access.hasAccess('repayment_detail_lending_postLoanMng_detail') && (
          <ProCard title="放款信息" collapsible defaultCollapsed={true}>
            {loanInfo.map((info, index) => {
              return (
                <Descriptions title={`放款${index + 1}`}>
                  {loanColumns.map((item) => {
                    const { label, value, render } = item;
                    return (
                      <Descriptions.Item label={label}>
                        {render ? render(info[value]) : info[value] || '/'}
                      </Descriptions.Item>
                    );
                  })}
                </Descriptions>
              );
            })}
          </ProCard>
        )}

        <ProCard title="还款信息" collapsible>
          <RepaymentInfo incomingInfo={incomingInfo} />
        </ProCard>
      </PageContainer>
    </div>
  );
};
export default memo(Detail);
