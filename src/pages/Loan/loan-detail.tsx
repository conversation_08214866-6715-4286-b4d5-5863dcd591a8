/*
 * @Author: your name
 * @Date: 2021-04-28 15:49:29
 * @LastEditTime: 2022-01-22 10:49:10
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/loan-detail.tsx
 */
import DetailCards from '@/components/DetailCards';
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import React from 'react';
import LoanInfo from '../BusinessMng/components/LoanInfo';
import { getLendingDataInfo } from './service';

const LoanDetail: React.FC<any> = () => {
  const { orderNo, lendingNo } = history.location.query as {
    orderNo: string;
    lendingNo: string;
  };
  const { data } = useRequest(() => {
    return getLendingDataInfo(lendingNo);
  });
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <DetailCards.IncomingInfo orderNo={orderNo} />
        <LoanInfo dataInfo={data} />
      </PageContainer>
    </>
  );
};

export default LoanDetail;
