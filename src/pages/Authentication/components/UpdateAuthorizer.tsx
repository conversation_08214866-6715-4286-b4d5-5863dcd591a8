/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-10-09 17:51:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-10-09 17:52:28
 * @FilePath: /code/lala-finance-biz-web/src/pages/Authentication/components/UpdateAuthorizer.tsx
 * @Description: UpdateAuthorizer
 */
import { ModalForm } from '@ant-design/pro-form';
import { Button, message } from 'antd';
import React, { memo } from 'react';
import { history } from 'umi';
import { updateAuthorizer } from '../services';
import AddAuthSignatory from './AddAuthSignatory';

const UpdateAuthorizer: React.FC = () => {
  const query = history?.location?.query;
  const onFinish = async (values: any) => {
    await updateAuthorizer({ ...values, epAuthNo: query?.epAuthNo });
    message.success('提交成功,请返回列表查看');
    return true;
  };

  return (
    <ModalForm
      onFinish={onFinish}
      trigger={<Button>编辑</Button>}
      width={1200}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 16 }}
    >
      <AddAuthSignatory />
    </ModalForm>
  );
};
export default memo(UpdateAuthorizer);
