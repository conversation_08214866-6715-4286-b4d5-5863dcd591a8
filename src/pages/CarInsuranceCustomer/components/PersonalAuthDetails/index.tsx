import WalletAccountInfo from '@/components/WalletAccountInfo';
import { ProDescriptions } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Card, Divider } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { CarInsuranceCustomerDetailFields } from '../../data.d';
import { statusMap } from '../../enum';
import { getCarInsuranceCustomerDetail } from '../../services';
import ClaimRechargeModal from '../ClaimRechargeModal';
import ClaimRecordModal from '../ClaimRecordModal';
import TransactionDetailModal from '../TransactionDetailModal';
const PersonalAuthDetails = () => {
  const uri = new URLSearchParams(window.location.search);
  const id = uri.get('id');

  const { initialState } = useModel('@@initialState');
  const isGrayUser = initialState?.currentUser?.isGrayUser;

  const [detail, setDetail] = useState<CarInsuranceCustomerDetailFields | undefined>(undefined);
  const claimRechargeRef = useRef<any>(null);
  const transactionRef = useRef<any>(null);
  const claimRecordRef = useRef<any>(null);

  // 获取个人用户下车险客户认证详情
  useEffect(() => {
    if (id) {
      getCarInsuranceCustomerDetail({ id }).then((res) => {
        setDetail(res?.data ?? {});
      });
    }
  }, [id]);

  return (
    <Card>
      <ProDescriptions title="认证个人详情" column={2} dataSource={detail}>
        <ProDescriptions.Item label="客户名称" dataIndex="name" />
        <ProDescriptions.Item label="当前状态" dataIndex="authStatus" valueEnum={statusMap} />
        <ProDescriptions.Item label="身份证号" dataIndex="idNo" />
        <ProDescriptions.Item label="创建时间" dataIndex="createdAt" />
        <ProDescriptions.Item label="所属渠道" dataIndex="channelName" />
        <ProDescriptions.Item label="认证时间" dataIndex="verifyTime" />
      </ProDescriptions>
      <Divider />
      {/* 专属充值账户信息 */}
      {isGrayUser && (
        <WalletAccountInfo
          data={detail}
          showTransaction={() => {
            transactionRef.current.show();
          }}
          showClaimRecharge={() => {
            claimRechargeRef.current.show();
          }}
          showClaimRecord={() => {
            claimRecordRef.current.show();
          }}
          secondProductCode="0303"
        />
      )}
      {/* 充值 */}
      <ClaimRechargeModal
        targetRemitAccountName={detail?.name ?? ''}
        accountName={detail?.accountName ?? ''}
        externalOwnerId={detail?.externalOwnerId}
        productSecondCode="0303"
        claimRechargeRef={claimRechargeRef}
      />
      {/* 认领记录 */}
      <ClaimRecordModal externalOwnerId={detail?.externalOwnerId} claimRecordRef={claimRecordRef} />
      {/* 流水明细 */}
      <TransactionDetailModal
        externalOwnerId={detail?.externalOwnerId}
        transactionRef={transactionRef}
        secondProductCode="0303"
      />
    </Card>
  );
};

export default PersonalAuthDetails;
