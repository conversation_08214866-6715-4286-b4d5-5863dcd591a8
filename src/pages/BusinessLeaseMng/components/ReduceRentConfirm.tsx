/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-06-30 10:59:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-01-16 17:08:41
 * @FilePath: /code/lala-finance-biz-web/src/pages/BusinessLeaseMng/components/ReduceRentConfirm.tsx
 * @Description: ReduceRentConfirm
 */
import React, { useState } from 'react';
import { ArrowsAltOutlined, ShrinkOutlined } from '@ant-design/icons';
import { Button, Modal } from 'antd';
import ProCard from '@ant-design/pro-card';
//
import styleLease from '../index.less';
import { manualAudit } from '../service';
import type { AuidtInfo, AuditParams } from '../data';
//
const headStyle: React.CSSProperties = {
  background: 'rgb(24, 144, 255)',
  minWidth: 340,
  color: 'white',
};
const bodyStyle: React.CSSProperties = {
  background: 'white',
  overflowY: 'scroll',
  maxHeight: '78vh',
};
//
const ReduceRentConfirm: React.FC<any> = (props: AuidtInfo) => {
  const { orderNo, status, info, confirmCallback, deratingRentPrice } = props;
  const [collapsed, setCollapsed] = useState(true);
  const [loading, setLoading] = useState(false);
  // 订单状态为降额待确认，展示悬浮框
  const showAuditCard = () => {
    return status === 23;
  };
  const processManualAudit = (params: AuditParams) => {
    Modal.confirm({
      title: '提示:',
      content: `确认${params.confirmResult ? '接受' : '拒绝'}？`,
      okText: '确认',
      cancelText: '取消',
      onOk() {
        setLoading(true);
        manualAudit(params).then(
          () => {
            confirmCallback(() => {
              setLoading(false);
            });
          },
          () => {
            setLoading(false);
          },
        );
      },
      onCancel() {},
    });
  };
  return (
    <>
      {showAuditCard() && (
        <ProCard
          title={<div className={styleLease.confirmTitle}>{'是否接受降额通过'}</div>}
          bordered
          headStyle={headStyle}
          bodyStyle={bodyStyle}
          extra={
            collapsed ? (
              <ArrowsAltOutlined
                className={styleLease.confirmTitle}
                onClick={() => {
                  setCollapsed(!collapsed);
                }}
              />
            ) : (
              <ShrinkOutlined
                className={styleLease.confirmTitle}
                onClick={() => {
                  setCollapsed(!collapsed);
                }}
              />
            )
          }
          headerBordered
          collapsed={collapsed}
        >
          <p>授信额度： {info.creditAmount}元</p>
          {deratingRentPrice && <p>降额后月供： {deratingRentPrice}元</p>}
          <div className={styleLease.confirmBtnWrapper}>
            <Button
              type="primary"
              onClick={() => processManualAudit({ confirmResult: false, orderNo })}
              ghost
              danger
            >
              拒绝
            </Button>
            <Button
              type="primary"
              loading={loading}
              onClick={() => processManualAudit({ confirmResult: true, orderNo })}
              ghost
            >
              接受
            </Button>
          </div>
        </ProCard>
      )}
    </>
  );
};
export default React.memo(ReduceRentConfirm);
