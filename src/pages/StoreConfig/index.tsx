/*
 * @Author: your name
 * @Date: 2021-06-28 11:17:53
 * @LastEditTime: 2021-09-22 17:55:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/WithHold/index.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { ExclamationCircleOutlined, UnlockOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Form, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useEffect, useRef, useState } from 'react';
import type { StoreConfigListItem } from './data';
import { addCarStore, delCarStore, getCarStoreList, modifyCarStore } from './service';

const optionMap = {
  EDIT: { label: '编辑' },
  START: { label: '启用', value: 1 },
  FORBID: { label: '禁用', value: 2 },
  DELETE: { label: '删除' },
  ADD: { label: '添加' },
  SHOW: { label: '查看' },
};

const StoreConfig: React.FC<{}> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [curOption, setCurOption] = useState<string>('');
  const [addVisible, handleAddVisible] = useState<boolean>(false);
  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const [disableForm, setDisableForm] = useState<boolean>(false);
  const [curRow, setCurrentRow] = useState<StoreConfigListItem>();
  const [form] = Form.useForm();
  // 编辑查看回显
  useEffect(() => {
    form.setFieldsValue({
      storeName: curRow?.storeName,
    });
  }, [curRow, disableForm]);
  // 操作栏
  const handleOpt = () => {
    const func2 =
      curOption === 'DELETE'
        ? delCarStore(curRow?.id)
        : modifyCarStore({
            id: curRow?.id,
            status: curRow?.status === 1 ? 2 : 1,
            storeName: curRow?.storeName,
          });
    return func2.then(() => true);
  };

  // 确认
  const handleConfirm = (values: { storeName: string }) => {
    const func =
      curOption === 'EDIT'
        ? modifyCarStore({
            id: curRow?.id,
            status: curRow?.status,
            storeName: values?.storeName,
          })
        : addCarStore(values?.storeName);
    return func.then(() => true);
  };
  const columns: ProColumns<StoreConfigListItem>[] = [
    {
      title: '门店编码',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      key: 'storeName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        1: { text: '启用', status: 'Success' },
        2: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 200,
      fixed: 'right',
      valueType: 'option',
      render: (text, row) => (
        <>
          <a
            className={globalStyle?.mr10}
            onClick={() => {
              setCurOption('DELETE');
              setCurrentRow(row);
              handleOptVisible(true);
            }}
          >
            删除
          </a>
          <a
            className={globalStyle?.mr10}
            onClick={() => {
              setCurrentRow(row);
              setCurOption(row?.status === 1 ? 'FORBID' : 'START');
              handleOptVisible(true);
            }}
          >
            {optionMap[row?.status === 1 ? 'FORBID' : 'START']?.label}
          </a>
          <a
            className={globalStyle?.mr10}
            onClick={() => {
              setCurOption('SHOW');
              setCurrentRow(row);
              setDisableForm(true);
              handleAddVisible(true);
            }}
          >
            查看详情
          </a>
        </>
      ),
    },
  ];
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<StoreConfigListItem>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          search={false}
          request={(params) => getCarStoreList(params)}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  handleAddVisible(true);
                  setCurOption('ADD');
                }}
              >
                添加门店
              </Button>,
            ];
          }}
        />
      </PageContainer>
      <ModalForm
        title="提示"
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
        }}
        visible={optVisible}
        onVisibleChange={handleOptVisible}
        onFinish={async () => {
          const success: boolean = await handleOpt();
          if (success) {
            message.success(`${optionMap[curOption].label}成功`);
            actionRef?.current?.reload();
            handleOptVisible(false);
          }
          return true;
        }}
      >
        <div>
          <ExclamationCircleOutlined className={globalStyle.iconCss} />
          是否确认{optionMap[curOption]?.label}该门店?
        </div>
      </ModalForm>
      <ModalForm
        title={`${optionMap[curOption]?.label}门店`}
        width="400px"
        layout="horizontal"
        form={form}
        modalProps={{
          centered: true,
          okText:
            curOption === 'SHOW' ? (
              <>
                <span>编辑</span>
                <UnlockOutlined />
              </>
            ) : (
              '提交'
            ),
          okButtonProps: { disabled: disableForm },
          afterClose: () => {
            setDisableForm(false);
            setCurrentRow(undefined);
          },
          destroyOnClose: true,
        }}
        visible={addVisible}
        onVisibleChange={handleAddVisible}
        onFinish={async (values) => {
          if (curOption === 'SHOW') {
            setDisableForm(false);
            setCurOption('EDIT');
            return false;
          }
          const success: boolean = await handleConfirm(values);
          if (success) {
            message.success(`${optionMap[curOption]?.label}成功`);
            actionRef?.current?.reload();
            handleAddVisible(false);
          }
          return true;
        }}
      >
        <ProFormText
          name="storeName"
          disabled={disableForm}
          rules={[{ required: true }]}
          fieldProps={{ maxLength: 100 }}
          width="sm"
          label="门店名称"
          placeholder="请输入门店名称"
        />
      </ModalForm>
    </>
  );
};

export default StoreConfig;
