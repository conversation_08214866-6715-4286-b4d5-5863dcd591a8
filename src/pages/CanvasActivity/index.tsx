import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { valueEnumMap } from '@/utils/tools';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import React from 'react';
import { KeepAlive } from 'react-activation';
import { awardTypes, awardUnitMap } from '../AwardManager/data.d';
import { getStandardPrizeGrantPage } from './service';
// // 解决日期范围选择器默认值报错
dayjs.extend(weekday);
dayjs.extend(localeData);

const OrderList: React.FC<any> = () => {
  const columns: ProColumns[] = [
    {
      title: '客户ID',
      dataIndex: 'userNo',
    },
    {
      title: '策略ID',
      dataIndex: 'activityId',
    },
    {
      title: '奖品ID',
      dataIndex: 'prizeId',
      search: false,
    },
    {
      title: '奖品名称',
      dataIndex: 'prizeName',
      search: false,
    },
    {
      title: '奖品类型',
      dataIndex: 'prizeType',
      valueEnum: valueEnumMap(awardTypes),
      search: false,
    },
    {
      title: '奖品价值',
      dataIndex: 'prizePrice',
      search: false,
      render: (_, record) => {
        return `${record?.prizePrice}${awardUnitMap[record?.prizeType]?.unit || ''}`;
      },
    },
    {
      title: '实际返现金额',
      dataIndex: 'realSendAmount',
      search: false,
      render: (_, record) => {
        const amount = Number(new BigNumber(record?.realSendAmount as any).div(100));
        return amount ? `${amount}元` : '-';
      },
    },
    {
      title: '发放时间',
      dataIndex: 'grantTime',
      search: false,
    },
    {
      title: '核销时间',
      dataIndex: 'realSendTime',
      search: false,
    },
    {
      title: '支付单号',
      dataIndex: 'payBizNo',
      search: false,
    },
    {
      title: '发送状态',
      dataIndex: 'statusList',
      valueEnum: {
        0: { text: '不发送' },
        1: { text: '待发送' },
        2: { text: '发送中' },
        3: { text: '发放成功' },
        4: { text: '发放失败' },
        5: { text: '已作废' },
        6: { text: '退票' },
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
      },
      hideInTable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '不发送' },
        1: { text: '待发送' },
        2: { text: '发送中' },
        3: { text: '发放成功' },
        4: { text: '发放失败' },
        5: { text: '已作废' },
        6: { text: '退票' },
      },
      search: false,
    },
  ];

  return (
    <>
      <PageContainer className={globalStyle.mt16}>
        <ProTable
          rowKey={'id'}
          scroll={{ x: 'max-content' }}
          request={(params) => {
            return getStandardPrizeGrantPage(params);
          }}
          search={{
            labelWidth: 100,
            defaultCollapsed: false,
          }}
          columns={columns}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'loanActivity/list'}>
      <OrderList />
    </KeepAlive>
  </>
);
