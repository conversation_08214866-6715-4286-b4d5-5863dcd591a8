import { getGlobalUserInfo } from '@/global';
import { history } from '@umijs/max';
import { SENSITIVE_PAGE_MAP } from './config';
import { getPlainInfo, sendSeimData } from './service';

// 组装公共参数
const getCustomParams = () => {
  const { userId, userName, passportName } = getGlobalUserInfo();
  if (!userId && !userName && !passportName) return false;

  return {
    from: 'bme-finbiz-web',
    hller: passportName,
    hller_name: userName,
    ucenter_hller_id: userId,
    url: window.location.href,
  };
};

// 上报日志，接口主动上报需要设置开关：sensitiveSwitch 为 true
export const sendSensitiveData = (props: any) => {
  try {
    const customParams = getCustomParams();
    if (!customParams) return;
    const { url = '', params = {}, data = {}, action } = props;

    const newParams = {
      ...customParams,
      action: action || '敏感数据接口访问',
      route: url,
      gets: params,
      posts: data,
    };
    setTimeout(() => {
      sendSeimData(newParams);
    }, 100);
  } catch (e) {
    console.log(e);
  }
};

// 上报日志-页面层级上报
export const sendSensitiveDataByPage = () => {
  try {
    const action = SENSITIVE_PAGE_MAP[window.location.pathname];
    if (!action) return;
    sendSensitiveData({ action, params: history?.location.query, url: history?.location.pathname });
  } catch (e) {
    console.log(e);
  }
};

// 获取解密信息且上报日志
export const getSensitiveData = async (props: any) => {
  try {
    const customParams = getCustomParams();
    if (!customParams) return;
    const { code, data, action } = props;

    const newParams = {
      ...customParams,
      action: action || '获取解密信息',
      route: '/bizadmin/seim/getPlainInfo',
      gets: {},
      posts: { code, data },
      code,
      data,
    };
    const res = await getPlainInfo(newParams);
    return res?.data?.planData;
  } catch (e) {
    console.log(e);
  }
};
