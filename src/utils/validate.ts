import { LONG_TIME } from '@/enums';
import dayjs from 'dayjs';

export const pattern = {
  number: /^[0-9]*$/,
  email: /^[a-zA-Z0-9_-|.]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
  phone: /^(((13[0-9]{1})|(15[0-9]{1})|(16[0-9]{1})|(17[0-9]{1})|(18[0-9]{1})|(19[0-9]{1})|(14[0-9]{1}))\d{8})$/,
  money: /((^[1-9]\d*)|^0)(\.\d{0,2}){0,1}$/,
  idCard: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  smsCode: /^\d{6}$/,
  ch: /^[\u4e00-\u9fa5·]{2,}$/,
};

export const rules = {
  phone: (_: any, value: string) => {
    if (value && value.length < 11) {
      return Promise.reject(new Error('请输入11位数字'));
    }
    const reg = pattern.phone;
    if (!reg.test(value) && value) {
      return Promise.reject(new Error('格式错误'));
    }
    return Promise.resolve();
  },
  smsCode: (value: string) => {
    const reg = pattern.smsCode;
    return reg.test(value);
  },
  isMoney: (value: string) => {
    const reg = pattern.money;
    return reg.test(value);
  },
  isIdCard: (value: string) => {
    const reg = pattern.idCard;
    return reg.test(value);
  },
  notAllNumber: (value: string) => {
    const reg = pattern.number;
    return !reg.test(value);
  },
  asyncNotAllNumber: (_: any, value: string) => {
    console.log('notAllNumber', _);
    if (pattern.number.test(value)) {
      return Promise.reject(new Error('不能为纯数字'));
    }
    return Promise.resolve();
  },
  idCardEndDate(_: any, value: string) {
    // 校验身份证有效期是否合规
    const endDay = dayjs().add(20, 'year');
    const theDay = dayjs(value);
    if (theDay.isAfter(endDay) && !theDay.isSame(LONG_TIME)) {
      return Promise.reject(new Error('有效期超过20年请选择长期有效'));
    }
    return Promise.resolve();
  },
};

export default rules;
