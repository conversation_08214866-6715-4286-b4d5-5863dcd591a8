/* eslint-disable @typescript-eslint/no-unused-expressions */
import { BASE_PROPORTION_NO_BRACKETS, EXPENSE_ITEMS } from '@/enums';
import { ProFormDependency, ProFormItem, ProFormSelect } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import { Col, Form, InputNumber, Modal, Radio, Row, Select } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import type { IfeeConfigItem } from '../../type';

type Props = {
  visible: boolean;
  setVisible: (val: boolean) => void;
  currentRow: IfeeConfigItem;
};

type Options = { label: string; value: string | number };

const MIN_AMOUNT = 1;
const MAX_AMOUNT = 100000;
const MIN_PROPORTION_NUMBER = 0;
const MIN_STEP_NUMBER = 0.0001;
const MAX_PROPORTION_NUMBER = 1;

const AMOUNT_COMMON_MESSAGE = `请输入${MIN_AMOUNT}-${MAX_AMOUNT}之间的数值`;
const PROPORTION_COMMON_MESSAGE = `请输入${MIN_PROPORTION_NUMBER}-${MAX_PROPORTION_NUMBER}之间的数值`;
const DUPLICATE_NUM_MESSAGE = '后一个数必须大于前一个数';

const InsuranceEditFeeConfig: React.FC<Props> = (props) => {
  const { visible, currentRow, setVisible } = props;
  const { costConfigDTO, payBackData } = useModel('Product.insuranceProduct', (model) => {
    return {
      costConfigDTO: model?.costConfigDTO,
      payBackData: model?.payBackData,
    };
  });
  const { expenseItem } = currentRow;
  const [form] = Form.useForm();
  const [termOptions, setTermOptions] = useState<Options[]>([]); // 未还利息可选期数

  // 利息 逾期罚息 逾期滞纳金 计算方式(按比例) 比例类型(固定比例) 不能更改
  const isDisabled = ['INTEREST', 'OVERDUE_PENALTY_INTEREST', 'OVERDUE_DELAY_AMOUNT'].includes(
    expenseItem,
  );
  useEffect(() => {
    const { regionAmount, regionProportion } = currentRow;
    form.resetFields(); // 由于没有销毁form，所以要resetFields一下 避免有遗留的值与新的值进行了合并
    form.setFieldsValue({
      ...currentRow,
      amountMin: regionAmount?.amountMin,
      amountMax: regionAmount?.amountMax,
      minProportionNumberValue: regionProportion?.minProportionNumberValue,
      maxProportionNumberValue: regionProportion?.maxProportionNumberValue,
    });
  }, [currentRow, form]);

  useEffect(() => {
    //  监听还款期限配置，生成按未还利息-可选期数
    if (payBackData?.repaymentTerms) {
      const limitTerm: string = payBackData?.repaymentTerms?.[0];
      const options: Options[] = [];
      for (let i = 0; i < Number(limitTerm) + 1; i++) {
        options.push({
          label: `${i}`,
          value: i,
        });
      }
      setTermOptions(options);
    }
  }, [payBackData]);

  function renderCalculation() {
    return (
      <Form.Item name="calculationEnum" label="计算方式" required>
        <Radio.Group disabled={isDisabled}>
          {/* 提前结清违约金增加按未还利息选项 */}
          {expenseItem === 'IN_ADVANCE_LIQUIDATED_DAMAGES' && (
            <Radio value="UNPAID_INTEREST">按未还利息</Radio>
          )}
          <Radio value="AMOUNT_PROPORTION">按金额</Radio>
          <Radio value="PROPORTIONALLY">按比例</Radio>
        </Radio.Group>
      </Form.Item>
    );
  }

  // 计算方式-按未还利息
  const renderOutstandingInterest = () => {
    return (
      <ProFormDependency name={['calculationEnum']}>
        {({ calculationEnum }) => {
          if (calculationEnum === 'UNPAID_INTEREST') {
            return (
              <ProFormItem label="未还利息" required>
                <div>
                  <Row align="middle" gutter={[8, 4]}>
                    <Col>当期往后</Col>
                    <Col span={12}>
                      <Form.Item
                        name="term"
                        noStyle
                        rules={[
                          {
                            validator: (_, value) => {
                              if (value > termOptions[termOptions.length - 1].value) {
                                return Promise.reject(new Error('不能超过最大还款期限'));
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <Select options={termOptions} />
                      </Form.Item>
                    </Col>
                    <Col>期剩余未还利息</Col>
                  </Row>
                  <div style={{ color: 'red' }}>
                    当期利息为默认收取，不计入结清违约金。当期往后0期剩余未还利息=结清违约金为0；当期往后2期剩余未还利息=收取结清日往后第2个+第3个还款日剩余未还利息作为结清违约金，依此类推
                  </div>
                </div>
              </ProFormItem>
            );
          }
          return null;
        }}
      </ProFormDependency>
    );
  };

  // 金额类型  比例类型
  function renderAmountOrProportionType() {
    return (
      <ProFormDependency name={['calculationEnum']}>
        {({ calculationEnum }) => {
          if (calculationEnum === 'AMOUNT_PROPORTION') {
            return (
              <Form.Item name="amountTypeEnum" label="金额类型" required>
                <Radio.Group>
                  <Radio value="FIXED_AMOUNT">固定金额</Radio>
                  <Radio value="RANGE_AMOUNT">区间范围</Radio>
                </Radio.Group>
              </Form.Item>
            );
          }
          if (calculationEnum === 'PROPORTIONALLY') {
            return (
              <Form.Item name="proportionNumberEnum" label="比例类型" required>
                <Radio.Group disabled={isDisabled}>
                  <Radio value="BY_FIXED_RATION">固定比例</Radio>
                  <Radio value="BY_RANGE_RATION">区间范围</Radio>
                </Radio.Group>
              </Form.Item>
            );
          }
          return null;
        }}
      </ProFormDependency>
    );
  }
  // 固定金额 // 金额区间
  function renderAmount() {
    return (
      <ProFormDependency name={['calculationEnum', 'amountTypeEnum']}>
        {({ calculationEnum, amountTypeEnum }) => {
          if (calculationEnum === 'AMOUNT_PROPORTION') {
            if (amountTypeEnum === 'FIXED_AMOUNT') {
              return (
                <Form.Item
                  label="固定金额(元)"
                  name="amount"
                  rules={[
                    { required: true, message: AMOUNT_COMMON_MESSAGE },
                    {
                      validator: (_, value, callBack) => {
                        if (Number.isFinite(value) && (value < MIN_AMOUNT || value > MAX_AMOUNT)) {
                          callBack(AMOUNT_COMMON_MESSAGE);
                        }
                        callBack();
                      },
                    },
                  ]}
                >
                  <InputNumber keyboard changeOnWheel precision={2} />
                </Form.Item>
              );
            }
            if (amountTypeEnum === 'RANGE_AMOUNT') {
              return (
                <Form.Item
                  label="金额区间(元)"
                  style={{ marginBottom: 0 }}
                  rules={[{ required: true }]}
                  required
                >
                  <Form.Item
                    name="amountMin"
                    rules={[
                      { required: true, message: AMOUNT_COMMON_MESSAGE },
                      ({ validateFields }) => ({
                        validator: (_, value, callBack) => {
                          if (Number.isFinite(value) && value > MAX_AMOUNT) {
                            callBack(AMOUNT_COMMON_MESSAGE);
                          }

                          callBack();
                          // 异步触发关联字段校验,不影响当前校验流程
                          const timer = setTimeout(() => {
                            validateFields(['amountMax']);
                            clearTimeout(timer);
                          });
                        },
                      }),
                    ]}
                    style={{ display: 'inline-block' }}
                  >
                    <InputNumber keyboard changeOnWheel precision={2} />
                  </Form.Item>
                  <Form.Item style={{ display: 'inline-block' }}>-</Form.Item>
                  <Form.Item
                    name="amountMax"
                    rules={[
                      { required: true, message: AMOUNT_COMMON_MESSAGE },
                      ({ getFieldValue }) => ({
                        validator: (_, value, callBack) => {
                          if (Number.isFinite(value) && value > MAX_AMOUNT) {
                            callBack(AMOUNT_COMMON_MESSAGE);
                          }
                          if (Number.isFinite(value) && value <= getFieldValue('amountMin')) {
                            callBack(DUPLICATE_NUM_MESSAGE);
                          }
                          callBack();
                        },
                      }),
                    ]}
                    style={{ display: 'inline-block' }}
                  >
                    <InputNumber keyboard changeOnWheel precision={2} />
                  </Form.Item>
                </Form.Item>
              );
            }
          }
          return null;
        }}
      </ProFormDependency>
    );
  }
  // 固定比例 比例区间
  function renderProportion() {
    return (
      <ProFormDependency name={['calculationEnum', 'proportionNumberEnum']}>
        {({ calculationEnum, proportionNumberEnum }) => {
          if (calculationEnum === 'PROPORTIONALLY') {
            if (proportionNumberEnum === 'BY_FIXED_RATION') {
              return (
                <Form.Item
                  label="固定比例"
                  name="proportionNumberValue"
                  rules={[
                    { required: true, message: PROPORTION_COMMON_MESSAGE },
                    {
                      validator: (_, value, callBack) => {
                        if (
                          Number.isFinite(value) &&
                          (value < MIN_PROPORTION_NUMBER || value > MAX_PROPORTION_NUMBER)
                        ) {
                          callBack(PROPORTION_COMMON_MESSAGE);
                        }
                        callBack();
                      },
                    },
                  ]}
                >
                  <InputNumber keyboard changeOnWheel step={MIN_STEP_NUMBER} precision={4} />
                </Form.Item>
              );
            }
            if (proportionNumberEnum === 'BY_RANGE_RATION') {
              return (
                <Form.Item
                  label="比例区间"
                  style={{ marginBottom: 0 }}
                  rules={[{ required: true }]}
                  required
                >
                  <Form.Item
                    name="minProportionNumberValue"
                    rules={[
                      { required: true, message: PROPORTION_COMMON_MESSAGE },
                      ({ validateFields }) => ({
                        validator: (_, value, callBack) => {
                          if (
                            Number.isFinite(value) &&
                            (value < MIN_PROPORTION_NUMBER || value > MAX_PROPORTION_NUMBER)
                          ) {
                            callBack(PROPORTION_COMMON_MESSAGE);
                          }
                          // 如果想要触发关联字段校验但又不影响当前字段的校验流程
                          // 可以先执行当前callback,再异步触发关联字段校验
                          callBack();
                          // 异步触发关联字段校验,不影响当前校验流程
                          const timer = setTimeout(() => {
                            validateFields(['maxProportionNumberValue']);
                            clearTimeout(timer);
                          });
                        },
                      }),
                    ]}
                    style={{ display: 'inline-block' }}
                  >
                    <InputNumber step={MIN_STEP_NUMBER} precision={4} />
                  </Form.Item>
                  <Form.Item style={{ display: 'inline-block' }}>-</Form.Item>
                  <Form.Item
                    name="maxProportionNumberValue"
                    rules={[
                      { required: true, message: PROPORTION_COMMON_MESSAGE },
                      ({ getFieldValue }) => ({
                        validator: (_, value, callBack) => {
                          if (Number.isFinite(value) && value > MAX_PROPORTION_NUMBER) {
                            callBack(PROPORTION_COMMON_MESSAGE);
                          }
                          if (
                            Number.isFinite(value) &&
                            value <= getFieldValue('minProportionNumberValue')
                          ) {
                            callBack(DUPLICATE_NUM_MESSAGE);
                          }
                          callBack();
                        },
                      }),
                    ]}
                    style={{ display: 'inline-block' }}
                  >
                    <InputNumber step={MIN_STEP_NUMBER} precision={4} />
                  </Form.Item>
                </Form.Item>
              );
            }
          }
          return null;
        }}
      </ProFormDependency>
    );
  }
  // 比例计算基数
  function renderBaseEnum() {
    return (
      <ProFormDependency name={['calculationEnum']}>
        {({ calculationEnum: calculationEnum1 }) => {
          if (calculationEnum1 === 'PROPORTIONALLY') {
            return (
              <>
                <ProFormSelect
                  options={Object.keys(BASE_PROPORTION_NO_BRACKETS).map((key) => ({
                    value: key,
                    label: BASE_PROPORTION_NO_BRACKETS[key],
                  }))}
                  width="md"
                  rules={[{ required: true }]}
                  disabled
                  name="baseEnum"
                  placeholder="请选择比例计算基数"
                  label="比例计算基数"
                />
              </>
            );
          } else {
            return null;
          }
        }}
      </ProFormDependency>
    );
  }
  return (
    <Modal
      title={`编辑${EXPENSE_ITEMS[expenseItem]}`}
      open={visible}
      onOk={() => {
        form.validateFields().then((values) => {
          const {
            proportionNumberValue,
            amount,
            minProportionNumberValue,
            maxProportionNumberValue,
            amountMin,
            amountMax,
            term,
            calculationEnum,
          } = values;
          costConfigDTO?.map((item: any) => {
            if (item.expenseItem === expenseItem) {
              item.regionAmount;
              item.calculationEnum = values?.calculationEnum;
              item.amountTypeEnum = values?.amountTypeEnum;
              item.proportionNumberEnum = values?.proportionNumberEnum;
              item.proportionNumberValue = proportionNumberValue;
              item.regionAmount = { amountMin, amountMax };
              item.regionProportion = {
                minProportionNumberValue,
                maxProportionNumberValue,
              };
              item.amount = amount;
              item.term = term;
              // 提前结清违约金，当计算基数=按未还利息，需要改掉基数为按未还利息
              if (expenseItem === 'IN_ADVANCE_LIQUIDATED_DAMAGES') {
                if (calculationEnum === 'UNPAID_INTEREST') {
                  item.baseEnum = 'NO_INTEREST_PAID'; // 未还利息
                } else {
                  item.baseEnum = 'SURPLUS_REPAY_PRINCIPAL'; // 剩余应还本金
                }
              }
            }
          });
          setVisible(false);
        });
      }}
      onCancel={() => {
        setVisible(false);
      }}
    >
      <Form form={form}>
        {renderCalculation()}
        {renderOutstandingInterest()}
        {renderAmountOrProportionType()}
        {renderAmount()}
        {renderBaseEnum()}
        {renderProportion()}
      </Form>
    </Modal>
  );
};
export default memo(InsuranceEditFeeConfig);
