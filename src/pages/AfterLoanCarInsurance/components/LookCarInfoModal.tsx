import { carStatusMap } from '@/utils/bankend/enum';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { Descriptions, Modal } from 'antd';
import React, { memo, useState } from 'react';
import { getLookCarInfoInfo } from '../services';
import type { IlookCarInfoItem } from '../types';

type Props = {
  lookCarInfoModalVisible: boolean;
  setLookCarInfoModalVisible: (val: boolean) => void;
  currentTerm: number;
  termDetail: string;
};
const LookCarInfoModal: React.FC<Props> = (props) => {
  const { orderNo } = (history as any).location.query;
  const { lookCarInfoModalVisible, setLookCarInfoModalVisible, currentTerm, termDetail } = props;
  const [carNum, setCarNum] = useState(0);
  const expandeOptions = [
    { label: '已还款总额', value: 'returnedAmountDue' },
    { label: '已还本金', value: 'returnedPrincipal' },
    { label: '已还利息', value: 'returnedInterest' },
    { label: '已还提前结清违约金', value: 'preSettleCost' },
    { label: '剩余应还总额', value: 'remainingAmountDue' },
    { label: '剩余应还本金', value: 'remainingPrincipal' },
    { label: '剩余应还利息', value: 'remainingInterest' },
    { label: '已还退保盈余', value: 'policyCancelFee' },
  ];
  const lookCarInfoColumns: ProColumns<IlookCarInfoItem>[] = [
    { dataIndex: 'licensePlateNo', title: '车牌号' },
    { dataIndex: 'vin', title: '车架号' },
    {
      dataIndex: 'status',
      title: '还款状态',
      render(text: any) {
        return carStatusMap[text];
      },
    },
    { dataIndex: 'repayTime', title: '应还日期' },
    { dataIndex: 'shouldAmountDue', title: '应还总额' },
    { dataIndex: 'shouldPrincipal', title: '应还本金' },
    { dataIndex: 'shouldInterest', title: '应还利息' },
    { dataIndex: 'overdueInterest', title: '逾期罚息' },
    { dataIndex: 'otherCost', title: '其他费用' },
  ];
  return (
    <Modal
      footer={null}
      open={lookCarInfoModalVisible}
      onCancel={() => {
        setLookCarInfoModalVisible(false);
      }}
      destroyOnClose
      width={1000}
    >
      <ProTable
        headerTitle={`分车辆查看信息 (期数:${termDetail};共${carNum}辆车)`}
        search={false}
        bordered
        size="small"
        columns={lookCarInfoColumns}
        pagination={false}
        request={async () => {
          const data = await getLookCarInfoInfo(orderNo);
          setCarNum(data.length);
          return {
            success: true,
            data: data.map((item) => {
              return {
                ...item,
                ...item.carPolicyTermRspBOS.filter((carInfo) => carInfo?.term === currentTerm)?.[0],
              };
            }),
          };
        }}
        expandable={{
          expandRowByClick: true,
          expandedRowRender: (record) => {
            return (
              <Descriptions column={4}>
                {expandeOptions.map((item) => {
                  const { label, value } = item;
                  return (
                    <Descriptions.Item label={label}>{record[value] || '0.00'}</Descriptions.Item>
                  );
                })}
              </Descriptions>
            );
          },
        }}
      />
    </Modal>
  );
};

export default memo(LookCarInfoModal);
