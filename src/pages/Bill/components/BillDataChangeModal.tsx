/*
 * @Author: your name
 * @Date: 2021-09-16 20:04:03
 * @LastEditTime: 2023-03-22 17:37:19
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/AuthorizedChange.tsx
 */
import { DividerTit, ShowInfo } from '@/components';
import { CommonImageUpload } from '@/components/ReleaseCom';
import globalStyle from '@/global.less';
import { convertUploadFileList, getBlob, getUuid, previewAS, saveAs } from '@/utils/utils';
import ProForm, { ProFormDependency, ProFormRadio, ProFormTextArea } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { <PERSON><PERSON>, DatePicker, Form, Input, InputNumber, message, Modal, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import type { LogListItem } from '../data.d';
import enterpriseMngStyle from '../index.less';
import { modifyBillDate, modifyBillLog } from '../service';
// import { getOssPath } from '@/services/global';

interface BillDataChangeModalProps {
  title: string;
  visible: boolean;
  onCancel: () => void;
  refresh: () => void;
  onVisibleChange?: any;
  billInfo: Record<string, any>;
}

const BillDataChangeModal: React.FC<BillDataChangeModalProps> = (props) => {
  const { title, visible, onCancel, billInfo } = props;
  const [mode, setModel] = useState<string>('变更');
  const { billNo, accountNumber } = history.location.query;
  // const { data: formAuth, run } = useRequest(() => {
  //   return getAuthInfo({ enterpriseId: userNo, productSecondTypeCode: '0101' });
  // });

  const previewPDF = (url: string, name: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      const exc = name.substring(name.lastIndexOf('.') + 1);
      if ('.jpg.jpeg.gif'.includes(exc)) {
        previewAS(blob, 'image/jpeg;chartset=UTF-8');
      } else if (exc === 'pdf') {
        previewAS(blob);
      } else {
        saveAs(blob, name);
      }
    });
    // });
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [allFileList, handleFileList] = useState<any>({});
  const [form] = Form.useForm();

  const mapFileList = (allFile: any) => {
    handleFileList(allFile);
  };
  const actionRef = useRef<ActionType>();
  const billInfoMap = {
    accountName: '企业名称',
    accountNumber: '用户ID',
    billNo: '账单ID',
    totalAmount: '账单金额(元)',
    desc: '账单状态',
    billCycle: '账单周期',
    repaymentDate: '还款日',
  };
  const itemTableEnclosure = (_: React.ReactNode, record: LogListItem) => {
    const fileInfo: { url: string; name: string }[] = record?.fileInfo;
    return fileInfo?.length
      ? fileInfo.reduce((pre: React.ReactNode, cur: { url: string; name: string }) => {
          return (
            <>
              {pre}
              <a
                target="_blank"
                onClick={() => {
                  previewPDF(cur?.url, cur?.name);
                }}
              >
                {cur?.name}
              </a>
              <br />
            </>
          );
        }, <></>)
      : '-';
  };

  const columns: ProColumns<LogListItem>[] = [
    {
      title: '变更前',
      dataIndex: 'beforeModifyAt',
    },
    {
      title: '变更后',
      dataIndex: 'afterModifyAt',
    },
    {
      title: '变更时间',
      dataIndex: 'operateAt',
    },
    {
      title: '变更人',
      dataIndex: 'operateBy',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '附件',
      dataIndex: 'fileInfo',
      render: itemTableEnclosure,
    },
  ];
  return (
    <Modal
      destroyOnClose
      centered
      title={title}
      open={visible}
      onCancel={() => onCancel()}
      width={800}
      maskClosable={false}
      // onVisibleChange={onVisibleChange}
      afterClose={() => {
        handleFileList({});
        form.resetFields();
        // run();
        setModel('变更');
      }}
      footer={null}
    >
      <ProFormRadio.Group
        style={{
          margin: 16,
        }}
        radioType="button"
        fieldProps={{
          value: mode,
          onChange: (e) => {
            setModel(e.target.value);
          },
        }}
        options={['变更', '变更日志']}
      />
      {mode === '变更' && (
        <div>
          <DividerTit title="账单信息" style={{ marginTop: 0 }}>
            <ShowInfo
              noCard
              rowSpan={12}
              infoMap={billInfoMap}
              // itemMap={itemBillMap}
              data={billInfo}
            />
          </DividerTit>
          <DividerTit title="变更信息" />
          <ProForm
            className={`${enterpriseMngStyle.formModal} ${globalStyle.mt20}`}
            layout="horizontal"
            initialValues={{ modifyDirection: 0, modifyDays: { type: 1 } }}
            form={form}
            submitter={{
              render: (propsSubmitter) => (
                <div className="buttonCss">
                  <Button
                    type="primary"
                    key="submit"
                    loading={loading}
                    onClick={() => propsSubmitter.form?.submit?.()}
                  >
                    提交
                  </Button>
                  <Button key="rest" onClick={onCancel} className={globalStyle?.ml10}>
                    取消
                  </Button>
                </div>
              ),
            }}
            onFinish={async (values: any) => {
              console.log('00000');
              const mapUploadFile = convertUploadFileList(allFileList, ['enclosure']);
              // await waitTime(2000);
              // console.log({ ...values, ...mapUploadFile });
              const { type, time, day } = values?.modifyDays;
              const lastDateValue =
                type === 1
                  ? values?.modifyDirection === 1
                    ? dayjs(billInfo?.repaymentDate).subtract(day, 'days')
                    : dayjs(billInfo?.repaymentDate).add(day, 'days')
                  : time;
              setLoading(true);
              await modifyBillDate({
                ...values,
                ...mapUploadFile,
                billNo,
                repayBillStatus: billInfo?.desc,
                repaymentDate: billInfo?.repaymentDate,
                accountNumber,
                modifyDate: dayjs(lastDateValue).format('YYYY-MM-DD'),
              })
                .then(() => {
                  setLoading(false);
                  message.success('变更成功');
                  onCancel();
                  props.refresh();
                })
                .catch(() => {
                  setLoading(false);
                });
              // console.log(values);
              handleFileList({});
            }}
          >
            <ProFormRadio.Group
              name="modifyDirection"
              label="变更方向"
              rules={[{ required: true }]}
              options={[
                {
                  label: '延期',
                  value: 0,
                },
                {
                  label: '提前',
                  value: 1,
                },
              ]}
            />

            <Form.Item
              label="变更天数"
              name="modifyDays"
              rules={[
                {
                  validator: (_, value) => {
                    const { type, day, time } = value;
                    const lastDateValue =
                      type === 1
                        ? form?.getFieldValue('modifyDirection') === 1
                          ? dayjs(billInfo?.repaymentDate).subtract(day, 'days')
                          : dayjs(billInfo?.repaymentDate).add(day, 'days')
                        : time;
                    if (
                      !(dayjs(billInfo?.confirmationDate).diff(dayjs(lastDateValue), 'days') < 0)
                    ) {
                      return Promise.reject(new Error('还款日需大于最晚确认账单日'));
                    }
                    return Promise.resolve();
                  },
                },
                { required: true },
              ]}
            >
              <Input.Group compact>
                <Form.Item
                  name={['modifyDays', 'type']}
                  noStyle
                  rules={[{ required: true, message: '类型必填' }]}
                >
                  <Select>
                    <Select.Option value={1}>按天</Select.Option>
                    <Select.Option value={2}>按日期</Select.Option>
                  </Select>
                </Form.Item>
                <ProFormDependency name={['modifyDirection', 'modifyDays', 'type']}>
                  {({ modifyDays, modifyDirection }) => {
                    const { type, time, day } = modifyDays;
                    let lastDes = '';
                    //是按天
                    if (type === 1) {
                      //变更方向是提前就是-，推后就是+
                      const dateValue =
                        modifyDirection === 1
                          ? dayjs(billInfo?.repaymentDate).subtract(day, 'days')
                          : dayjs(billInfo?.repaymentDate).add(day, 'days');
                      lastDes = `变更后还款日：
                      ${dayjs(dateValue).format('YYYY-MM-DD')}`;
                    } else {
                      const dayValue = Math.abs(
                        dayjs(time).diff(dayjs(billInfo?.repaymentDate), 'day'),
                      );
                      //按日期
                      lastDes = `变更天数：${dayValue}天`;
                    }

                    return (
                      <>
                        {modifyDays?.type === 2 ? (
                          <Form.Item name={['modifyDays', 'time']} noStyle>
                            <DatePicker
                              style={{ width: '30%' }}
                              disabledDate={(current: any) => {
                                return (
                                  current && current < dayjs().endOf('day').subtract(1, 'days')
                                );
                              }}
                            />
                          </Form.Item>
                        ) : (
                          <Form.Item name={['modifyDays', 'day']} noStyle>
                            <InputNumber style={{ width: '30%' }} min={1} precision={0} />
                          </Form.Item>
                        )}
                        {day || time ? (
                          <a style={{ marginLeft: 5, marginTop: 3 }}>
                            {/* 变更后还款日：
                            {dayjs(lastDateValue).format('YYYY-MM-DD')} */}
                            {lastDes}
                          </a>
                        ) : null}
                      </>
                    );
                  }}
                </ProFormDependency>
              </Input.Group>
            </Form.Item>

            <CommonImageUpload
              extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
              label="附件"
              name="enclosure"
              max={5}
              listType="text"
              size={10}
              fileListEdit={allFileList?.enclosure || []}
              desPath="EP_AUTH_INFO"
              mapFileList={mapFileList}
              accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
            />
            <ProFormTextArea
              width="lg"
              label="备注"
              name="remark"
              fieldProps={{ maxLength: 500, showCount: true }}
            />
          </ProForm>
        </div>
      )}
      {mode === '变更日志' && (
        <div>
          <ProTable<LogListItem>
            actionRef={actionRef}
            rowKey={getUuid}
            scroll={{ x: 'max-content' }}
            // dataSource={[{ cost: 500, amountDue: 2 }]}
            columns={columns}
            toolBarRender={false}
            search={false}
            request={(params) => {
              return modifyBillLog({
                ...params,
                billNo,
              });
            }}
          />
        </div>
      )}
    </Modal>
  );
};
export default BillDataChangeModal;
