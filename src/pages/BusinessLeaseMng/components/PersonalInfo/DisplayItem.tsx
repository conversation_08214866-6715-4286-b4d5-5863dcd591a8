import CityPicker from '@/components/CityPicker';
import { LONG_TIME } from '@/enums';
import { ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { DatePicker, Form } from 'antd';
import React, { useEffect, useState } from 'react';
import { CONTACT_PERSOAN_NAME, SP_COMPONENT_TYPE, SP_FIELD_CONFIG } from './consts';
import type { ItemProps, Options } from './types';
import Upload from './upload';

function formatOptions(data: Options[] = []) {
  if (!data) return [];
  return data.map((item) => {
    return {
      label: item.optionValue,
      value: item.optionCode,
    };
  });
}

const DisplayItem: React.FC<ItemProps> = (props) => {
  const {
    data,
    form,
    label,
    name,
    renderType,
    rules,
    disabled,
    defaultValue,
    fieldType,
    options,
    onPickerChange,
    onUploadChange,
  } = props;

  let node = null;

  const placeholder = SP_FIELD_CONFIG[name]?.placeholder;
  const maxConfigLength = SP_FIELD_CONFIG[name]?.maxLength;
  const [maxLength, setMaxLength] = useState<number | undefined>();

  const initMaxLength = () => {
    let length;
    if (fieldType === 'PHONE') {
      length = 11;
    } else if (CONTACT_PERSOAN_NAME.includes(name)) {
      // 所有联系人姓名限制长度15个字
      length = 15;
    } else if (maxConfigLength) {
      // 配置有则取配置的
      length = maxConfigLength;
    }
    setMaxLength(length);
  };

  const handleLongTimeSelect = () => {
    props.onLongDateSelect?.(LONG_TIME);
  };

  useEffect(() => {
    initMaxLength();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 需要特殊组件渲染
  if (SP_COMPONENT_TYPE[fieldType]) {
    switch (fieldType) {
      case 'PHONE':
        node = (
          <ProFormText
            label={label}
            name={name}
            allowClear
            disabled={disabled}
            fieldProps={{
              maxLength,
            }}
            validateTrigger="onBlur"
            rules={rules}
            placeholder={placeholder || '请输入'}
          />
        );
        break;
      case 'DATE':
        // const minDate = SP_FIELD_CONFIG[name]?.minDate || new Date('1901/01/01');
        // const maxDate = SP_FIELD_CONFIG[name]?.maxDate || new Date('2050/12/31');
        const showLongTimeOption = SP_FIELD_CONFIG[name]?.showLongTimeOption;
        node = (
          <>
            <Form.Item
              label={label}
              name={name}
              rules={rules}
              disabled={disabled}
              validateTrigger="onBlur"
            >
              <DatePicker
                disabled={disabled}
                placeholder={placeholder}
                renderExtraFooter={() => {
                  return (
                    showLongTimeOption && (
                      <div
                        style={{
                          color: '#1677ff',
                          textAlign: 'center',
                          cursor: 'pointer',
                        }}
                        onClick={handleLongTimeSelect}
                      >
                        长期有效
                      </div>
                    )
                  );
                }}
              />
            </Form.Item>
          </>
        );
        break;
      case 'CITY':
        node = (
          <Form.Item label={label} name={name} rules={rules} disabled={disabled}>
            <CityPicker placeholder={placeholder || '请选择'} disabled={disabled} />
          </Form.Item>
        );

        break;
      case 'IMAGE':
        if (disabled && !defaultValue) {
          node = null;
        } else {
          node = (
            <Form.Item
              name={name}
              label={label}
              rules={rules}
              wrapperCol={{ span: 24 }}
              labelCol={{ span: 6 }}
            >
              <Upload disabled={disabled} onUploadChange={onUploadChange} data={data} form={form} />
            </Form.Item>
          );
        }

        break;
    }
  } else {
    switch (renderType) {
      case 'INPUT':
        node = (
          <ProFormText
            label={label}
            name={name}
            disabled={disabled}
            required={rules?.[0]?.required}
            rules={rules}
            fieldProps={{
              maxLength,
            }}
            validateTrigger="onBlur"
            placeholder={placeholder || '请输入'}
          />
        );
        break;
      case 'SELECT':
        node = (
          <ProFormSelect
            label={label}
            name={name}
            rules={rules}
            disabled={disabled}
            options={formatOptions(options || [])}
            placeholder="请选择"
            fieldProps={{
              onChange: onPickerChange,
            }}
          />
        );
    }
  }

  return node;
};

export default DisplayItem;
