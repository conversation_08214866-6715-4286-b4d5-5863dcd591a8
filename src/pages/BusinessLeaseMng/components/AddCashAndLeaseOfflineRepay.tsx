/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2023-02-06 10:08:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-10-21 15:56:22
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/components/AddCashAndLeaseOfflineReapy.tsx
 * @Description: AddCashAndLeaseOfflineReapy
 */
import { DividerTit, ShowInfo } from '@/components';
import { CommonImageUpload } from '@/components/ReleaseCom';
import { LEASE_LOAN_CHANNEL, PRODUCT_CLASSIFICATION_CODE, RECIEVE_BANK_CHANNEL } from '@/enums';
import globalStyle from '@/global.less';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import ProForm, {
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import { Form, message, Table, Tooltip } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import itemStyle from '../index.less';
import './AddCashAndLeaseOfflineRepay.less';
// import { getRepayInfo } from '@/services/global';
import { BOHAI_BANK_NO, CHANNEL_BANK_NO, REPURCHASE_STATUS } from '@/enums';

import { bigNumberFen2Yuan, convertUploadFileList, disableFutureDate } from '@/utils/utils';
import { useRequest } from 'ahooks';
import BigNumber from 'bignumber.js';
import type { ColumnType } from 'rc-table/lib/interface';
import { COST_TYPE_OPT, mapStatusZh } from '../consts';
import type { RepayEditItem, RepayItem, ShouldRepayItem } from '../data';
import { getEarlyEndShouldMoney, getOfflineRepayInfo, submitOfflineRepay } from '../service';

const randomID = () => Math.random().toString(20).slice(2);

type AddCashAndLeaseOfflineRepayProps = {
  visible: boolean;
  close: () => void;
  productCode: string;
  channelCode: string;
  repurchaseStatus: number;
  orderNo: string;
  accountName: string;
  accountNumber: string;
};

const totalQuotaItemMap = {
  shouldAmountDueAll: '剩余应还款总金额/元',
  shouldPrincipalAll: '剩余应还款总本金/元',
  shouldInterestAll: '剩余应还总利息/元',
  overdueInterestAll: '剩余应还总罚息/元',
  otherCostAll: '剩余应还总其他费用/元',
  should: '剩余应还提前结清违约金/元',
};

//实际还款方
enum ACTUAL_REPAY_ROLE {
  PERSON = '1', //个人
  THIRD_PARTY = '2', //第三方
}
//代偿方式
enum COMPENSATE_TYPE {
  SINGLE = '1', //单期
  MULTIPLE = '2', //多期
}

const MapPeriod = {};
new Array(42).fill('').map((item, index) => {
  MapPeriod[index + 1] = index + 1;
});
// //需要减免的费用项  { title: '应还利息', dataIndex: 'shouldInterest', key: 'shouldInterest' },
// const NEED_REMISSION_ITEM = {
//   shouldInterest: '利息',
//   overdueInterest: '逾期罚息',
//   otherCost: '其他费用',
// };
const AddCashAndLeaseOfflineRepay: React.FC<AddCashAndLeaseOfflineRepayProps> = ({
  visible,
  productCode,
  channelCode,
  repurchaseStatus, // 是否回购
  close,
  orderNo,
  accountName,
  accountNumber,
}) => {
  // console.log(accountName, accountNumber, productCode);
  const [form] = Form.useForm();
  const earlyInfo = useRef(null);
  // 是否识别为上海银行:放款资方是上海银行，且没有被回购
  const isShangHaiYingHang =
    channelCode === LEASE_LOAN_CHANNEL.SHANG_HAI_YING_HANG &&
    ![
      REPURCHASE_STATUS.BUY_CHANNEL,
      REPURCHASE_STATUS.BUY_CHANNEL_ING,
      REPURCHASE_STATUS.BUY_SELF,
      REPURCHASE_STATUS.BUY_SELF_ING,
    ].includes(repurchaseStatus);

  const { data } = useRequest(() => {
    return getOfflineRepayInfo(orderNo).then((res) => {
      return res?.data;
    });
  });

  const getEarlyEndInfo = async () => {
    if (earlyInfo.current) {
      return earlyInfo.current;
    }
    const res = await getEarlyEndShouldMoney(orderNo);
    earlyInfo.current = res.data;
    return earlyInfo.current;
  };

  // const { data: remissionInfo } = useRequest(() => {
  //   return getOfflineRepayRemission(orderNo).then((res) => {
  //     return res?.data;
  //   });
  // });
  const settleIntersetPercent = useMemo(() => {
    return new BigNumber(data?.proportionNumberValue || 0);
  }, [data]);
  // console.log(data);
  const editableFormRef: any = useRef();

  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  // const [computedLoanFee, setComputedLoanFee] = useState<any>(0);
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };
  const [currentPeriodOpt, setCurrentPeriodOpt] = useState([]);

  const actionRef: any = useRef<ActionType>();
  const [dataSource, setDataSource] = useState<readonly RepayEditItem[]>(() => []);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>();
  const [someMount, setRepayingMount] = useState<{
    repayingMount: string | number | BigNumber;
    orderAmount?: string | number | BigNumber;
    shouldRepayRestAmount?: string | number | BigNumber;
  }>({ repayingMount: 0, orderAmount: 0, shouldRepayRestAmount: 0 });
  const [selectRows, setSelectRows] = useState<RepayItem[]>([]);
  const [isFullAmountDisabled, setIsFullAmountDisabled] = useState(false); // 是否禁用足额还款
  // 解决useMemo、useEffect监听复杂对象，监听不到数据变化的情况，依赖当前state，手动触发更新
  const [forceUpdateData, setForceUpdateData] = useState(false);
  const [selectRepayKeys, setSelectRepayKeys] = useState<React.Key[]>([]);
  // 动态计算减免后各项金额
  const [computeRemissionItem, setComputeRemissionItem] = useState<Record<string, number>>();

  //获取还款中额度
  useEffect(() => {
    if (data) {
      const { freezingAmount, orderAmount } = data;
      setRepayingMount({
        repayingMount: freezingAmount || 0,
        orderAmount,
        shouldRepayRestAmount: new BigNumber(orderAmount || 0).toFixed(2),
      });
    }
  }, [data]);

  const handleComputeRemissionItem = async () => {
    // 初始化剩余应还数据和减免数据
    const needRemisssion = form?.getFieldValue('isRemission');
    const hasLiquidatedDamagesRemisssion = dataSource.some((v: any) => v?.costType === 2);
    let shouldRepay: any = {
      principal: 0, //本金
      interest: 0, //利息
      liquidatedDamages: 0, //违约金
      penaltyInterest: 0, //罚息
      otherInterest: 0, //其他费用=违约金
      dueInterest: 0, // 逾期滞纳金
    };
    const remissionMap: any = {
      interestRemission: 0, //减免利息
      liquidatedDamagesRemission: 0, //减免违约金
      penaltyInterestRemission: 0, //减免罚息
      otherInterestRemission: 0, //减免的其他费用=减免违约金
      dueInterestRemission: 0, // 逾期滞纳金
    };

    if (data?.repayInfoList) {
      let isOnlyLastTerm = false;
      // 应还的费用
      const repayList = data?.repayInfoList?.filter((item: ShouldRepayItem) => {
        return (
          mapStatusZh[item.status] === '待还款' &&
          selectRepayKeys.includes(item?.termDetail as string)
        );
      });

      if (
        repayList.length === 1 &&
        repayList[0]?.termDetail === data?.repayInfoList[data?.repayInfoList.length - 1]?.termDetail
      ) {
        isOnlyLastTerm = true;
      }

      repayList?.forEach((current: ShouldRepayItem) => {
        shouldRepay.principal = new BigNumber(current?.shouldPrincipal)
          .minus(current?.paidPrincipal || 0)
          .plus(shouldRepay?.principal);
        shouldRepay.interest = new BigNumber(current?.shouldInterest)
          .minus(current?.paidInterest || 0)
          .plus(shouldRepay?.interest);
        // shouldRepay.liquidatedDamages = new BigNumber(current?.latePaymentFee).plus(
        //   shouldRepay?.liquidatedDamages,
        // );
        shouldRepay.penaltyInterest = new BigNumber(current?.overdueInterest).plus(
          shouldRepay?.penaltyInterest,
        );
        // 其他费用=滞纳金+...
        shouldRepay.otherInterest = new BigNumber(current?.otherCost).plus(
          shouldRepay?.otherInterest,
        );
        // 滞纳金
        shouldRepay.dueInterest = new BigNumber(current?.latePaymentFee as number).plus(
          shouldRepay?.dueInterest,
        );
      });
      // 代偿结清才有违约金
      const isMultiple = form.getFieldValue('compensateType') === COMPENSATE_TYPE.MULTIPLE;
      const originLiquidatedDamages = isMultiple
        ? new BigNumber(shouldRepay?.principal)?.times(settleIntersetPercent).toFixed(2, 0)
        : 0;
      shouldRepay.liquidatedDamages = needRemisssion
        ? hasLiquidatedDamagesRemisssion
          ? new BigNumber(form?.getFieldValue('computeLoanBaseAmount') || shouldRepay?.principal)
              ?.times(settleIntersetPercent)
              .toFixed(2, 0)
          : originLiquidatedDamages
        : originLiquidatedDamages;

      // 上海银行代偿结清需要从接口获取部分金额,
      // 但如果只有最后一期尾款，还是走易人行计算逻辑
      let shyhObj = {};
      const compensateType = form?.getFieldValue('compensateType');
      if (
        !isOnlyLastTerm &&
        isShangHaiYingHang &&
        [COMPENSATE_TYPE.MULTIPLE].includes(compensateType)
      ) {
        const earlyEndData = await getEarlyEndInfo();
        const {
          advanceSettleLiquidatedDamages, // 提前结清违约金
          remainInterest, // 剩余应还利息
          remainPrincipal, // 剩余应还本金
          remainTotal, // 剩余应还总金额 = 实际剩余应还金额+还款中金额
        } = earlyEndData || {};

        shyhObj = {
          needRepayTotal: bigNumberFen2Yuan(remainTotal || 0),
          principal: bigNumberFen2Yuan(remainPrincipal || 0),
          interest: bigNumberFen2Yuan(remainInterest || 0),
          liquidatedDamages: bigNumberFen2Yuan(advanceSettleLiquidatedDamages || 0),
        };
        // 修改剩余应还数据，把上海银行的数据覆盖进去
        shouldRepay = {
          ...shouldRepay,
          ...shyhObj,
        };
      }

      // 需要减免费用项
      dataSource?.forEach((item: any) => {
        if (item?.costType === 1) {
          remissionMap.interestRemission = new BigNumber(item?.remissionAmount || 0);
        } else if (item?.costType === 2) {
          remissionMap.liquidatedDamagesRemission = new BigNumber(item?.remissionAmount || 0);
        } else if (item?.costType === 3) {
          remissionMap.penaltyInterestRemission = new BigNumber(item?.remissionAmount || 0);
        } else if (item?.costType === 5) {
          remissionMap.dueInterestRemission = new BigNumber(item?.remissionAmount || 0);
          remissionMap.otherInterestRemission = new BigNumber(
            remissionMap.otherInterestRemission,
          ).plus(new BigNumber(item?.remissionAmount || 0));
        }
      });
      const needRepayTotal = new BigNumber(shouldRepay.principal)
        .plus(shouldRepay.interest)
        // .plus(shouldRepay.otherInterest)
        .plus(shouldRepay.penaltyInterest)
        .plus(shouldRepay.liquidatedDamages)
        .plus(shouldRepay.dueInterest);

      const remissionTotal = new BigNumber(remissionMap?.interestRemission || 0)
        .plus(remissionMap.penaltyInterestRemission)
        .plus(remissionMap.liquidatedDamagesRemission)
        .plus(remissionMap.dueInterestRemission);

      const leftTotal = needRepayTotal.minus(remissionTotal);
      // 涉及选择减免期数，可填最大值计算
      const maxValueMapBySelectOptPeriods = {};
      dataSource.forEach((item: any) => {
        if (item?.costType === 1) {
          // 利息
          maxValueMapBySelectOptPeriods[1] = data?.repayInfoList.reduce(
            (total: any, current: ShouldRepayItem) => {
              if (item?.remissionPeriod?.includes(current?.term)) {
                return new BigNumber(total)
                  .plus(current?.shouldInterest)
                  .minus(current?.paidInterest || 0);
              }
              return total;
            },
            0,
          );
        } else if (item?.costType === 3) {
          // 逾期罚息
          maxValueMapBySelectOptPeriods[1] = data?.repayInfoList.reduce(
            (total: any, current: ShouldRepayItem) => {
              if (item?.remissionPeriod?.includes(current?.term)) {
                return new BigNumber(total).plus(current?.overdueInterest);
              }
              return total;
            },
            0,
          );
        } else if (item?.costType === 5) {
          // 逾期滞纳金
          maxValueMapBySelectOptPeriods[1] = data?.repayInfoList.reduce(
            (total: any, current: ShouldRepayItem) => {
              if (item?.remissionPeriod?.includes(current?.term)) {
                return new BigNumber(total).plus(current?.latePaymentFee as number);
              }
              return total;
            },
            0,
          );
        }
      });
      if (!needRemisssion) {
        const obj = {
          ...shouldRepay,
          interestRemission: 0, //减免利息
          liquidatedDamagesRemission: 0, //减免违约金
          penaltyInterestRemission: 0, //减免罚息
          otherInterestRemission: 0, //减免的其他费用=减免违约金
          dueInterestRemission: 0, // 逾期滞纳金
          //
          leftPrincipal: new BigNumber(shouldRepay?.principal || 0),
          leftInterest: new BigNumber(shouldRepay?.interest || 0),
          leftLiquidatedDamages: new BigNumber(shouldRepay?.liquidatedDamages || 0),
          leftPenaltyInterest: new BigNumber(shouldRepay?.penaltyInterest || 0),
          leftOtherInterest: new BigNumber(shouldRepay?.otherInterest || 0),
          leftDueInterest: new BigNumber(shouldRepay?.dueInterest || 0),
          //
          needRepayTotal, //应还总额
          remissionTotal: 0, //减免总额
          leftTotal: needRepayTotal, // 减免后实际需要还款总额
          maxValueMapBySelectOptPeriods,
        };
        setComputeRemissionItem(obj);
        return;
      }

      const obj = {
        leftPrincipal: new BigNumber(shouldRepay?.principal || 0),
        leftInterest: new BigNumber(shouldRepay?.interest || 0).minus(
          remissionMap.interestRemission,
        ),
        leftLiquidatedDamages: new BigNumber(shouldRepay?.liquidatedDamages || 0).minus(
          remissionMap.liquidatedDamagesRemission,
        ),
        leftPenaltyInterest: new BigNumber(shouldRepay?.penaltyInterest || 0).minus(
          remissionMap.penaltyInterestRemission,
        ),
        leftOtherInterest: new BigNumber(shouldRepay?.otherInterest || 0).minus(
          remissionMap.otherInterestRemission,
        ),
        leftDueInterest: new BigNumber(shouldRepay?.dueInterest || 0).minus(
          remissionMap.dueInterestRemission,
        ),
        //
        needRepayTotal, //应还总额
        remissionTotal, //减免总额
        leftTotal, // 减免后实际需要还款总额
        //
        ...shouldRepay,
        ...remissionMap,
        maxValueMapBySelectOptPeriods,
      };
      setComputeRemissionItem(obj);
      return;
    } else {
      const obj = {
        leftPrincipal: 0,
        leftInterest: 0,
        leftLiquidatedDamages: 0,
        leftPenaltyInterest: 0,
        leftOtherInterest: 0,
        leftDueInterest: 0,
        //
        needRepayTotal: 0, //应还总额
        remissionTotal: 0, //减免总额
        leftTotal: 0, // 减免后实际需要还款总额
        //
        ...shouldRepay,
        ...remissionMap,
      };
      setComputeRemissionItem(obj);
    }
  };
  useEffect(() => {
    handleComputeRemissionItem();
  }, [dataSource, data?.repayInfoList, form, forceUpdateData, selectRepayKeys]);
  // );

  const [totalQuotaItem, setTotalQuotaItem] = useState<{
    shouldAmountDueAll: number;
    shouldPrincipalAll: number;
    shouldInterestAll: number;
    overdueInterestAll: number;
    otherCostAll: number;
    should?: string;
  }>({
    shouldAmountDueAll: 0,
    shouldPrincipalAll: 0,
    shouldInterestAll: 0,
    overdueInterestAll: 0,
    otherCostAll: 0,
    should: '0',
  });

  const selfDefineTotal = {
    shouldAmountDueAll: (
      <span style={{ color: 'red' }}>
        {new BigNumber(computeRemissionItem?.needRepayTotal).toFixed(2)}{' '}
      </span>
    ),
    should: <span>{new BigNumber(computeRemissionItem?.liquidatedDamages).toFixed(2) || 0} </span>,
    shouldInterestAll: (
      <span>{new BigNumber(computeRemissionItem?.interest).toFixed(2) || 0} </span>
    ),
    shouldPrincipalAll: (
      <span>{new BigNumber(computeRemissionItem?.principal).toFixed(2) || 0} </span>
    ),
  };

  const [hasSomeIsNotRepay, setHasSomeIsNotRepay] = useState<boolean>(false);
  useEffect(() => {
    // 所有期数还款状态=待还款；修改为：剩余未还期数还款状态=待还款
    if (data) {
      const needToRepay = data?.repayInfoList?.filter((item: ShouldRepayItem) => {
        if (
          ['提前结清', '正常还款', '逾期结清', '单期代偿', '代偿结清'].includes(
            mapStatusZh[item.status],
          )
        ) {
          return false;
        }
        return true;
      });

      setCurrentPeriodOpt(
        needToRepay
          ?.filter((item: ShouldRepayItem) => mapStatusZh[item.status] === '待还款')
          ?.map((value: ShouldRepayItem) => {
            return {
              label: value?.term,
              value: value?.term,
            };
          }),
      );
      setHasSomeIsNotRepay(
        needToRepay.length == 0 ||
          needToRepay.some((item: ShouldRepayItem) => {
            return mapStatusZh[item.status] !== '待还款';
          }),
      );
    }
  }, [data]);

  //算应还总额
  const getTotal: any = async (selectedRows: ShouldRepayItem[]) => {
    let shouldAmountDueAll: number | BigNumber | string = 0;
    let shouldPrincipalAll: number | BigNumber | string = 0;
    let shouldInterestAll: number | BigNumber | string = 0;
    let overdueInterestAll: number | BigNumber | string = 0;
    let otherCostAll: number | BigNumber | string = 0;
    const should = new BigNumber(computeRemissionItem?.liquidatedDamages).toFixed(2, 0);
    // console.log(selectedRows);
    selectedRows?.map((item) => {
      shouldAmountDueAll = new BigNumber(item?.remainingAmountDue).plus(shouldAmountDueAll) + ''; //剩余应还总额
      shouldPrincipalAll = new BigNumber(item?.shouldPrincipal).plus(shouldPrincipalAll) + '';
      shouldInterestAll = new BigNumber(item?.shouldInterest).plus(shouldInterestAll) + '';
      overdueInterestAll = new BigNumber(item?.overdueInterest).plus(overdueInterestAll) + '';
      otherCostAll = new BigNumber(item?.otherCost).plus(otherCostAll) + '';
    });
    // console.log(selectedRows, shouldAmountDueAll, shouldPrincipalAll);
    // 上海银行资方，代偿结清，提前结清费用需要从接口获取
    const { compensateType } = form.getFieldsValue();
    console.log('compensateType', compensateType);

    let isOnlyLastTerm = false; // 是否只选择了最后一期
    if (
      selectedRows.length === 1 &&
      selectedRows[0].termDetail === data?.repayInfoList[data?.repayInfoList.length - 1]?.termDetail
    ) {
      isOnlyLastTerm = true;
    }

    if (
      !isOnlyLastTerm &&
      isShangHaiYingHang &&
      [COMPENSATE_TYPE.MULTIPLE].includes(compensateType)
    ) {
      const earlyEndData = await getEarlyEndInfo();
      const {
        advanceSettleLiquidatedDamages, // 提前结清违约金
        extraFeeTotal, // 其他费用总计
        remainInterest, // 剩余应还利息
        remainPrincipal, // 剩余应还本金
        remainTotal, // 剩余应还总金额 = 实际剩余应还金额+还款中金额
      } = earlyEndData || {};

      const obj = {
        shouldAmountDueAll: bigNumberFen2Yuan(remainTotal || 0),
        shouldPrincipalAll: bigNumberFen2Yuan(remainPrincipal || 0),
        shouldInterestAll: bigNumberFen2Yuan(remainInterest || 0),
        overdueInterestAll: Number(overdueInterestAll || 0),
        otherCostAll: bigNumberFen2Yuan(extraFeeTotal || 0),
        should: bigNumberFen2Yuan(advanceSettleLiquidatedDamages),
      };

      setTotalQuotaItem({ ...obj });
    } else {
      setTotalQuotaItem({
        shouldAmountDueAll,
        shouldPrincipalAll,
        shouldInterestAll,
        overdueInterestAll,
        otherCostAll,
        should,
      });
    }
  };

  const selectWithDeduct = (selectedRows: RepayItem[]) => {
    getTotal(selectedRows);
    //减免费用项
    const defaultData: any = [
      {
        id: randomID(),
      },
    ];
    console.log('defaultData11', defaultData);

    setEditableRowKeys(defaultData?.map((v: any) => v.id));
    setDataSource(defaultData);
  };

  const handleIsFullAmount = (values: any, selectRepayKeys: any) => {
    // 上海银行资方: 除了最后一期500元尾款，实际还款方为本人或者第三方还款的单期代偿
    // 只能足额还款，不能部分还款
    const { actualRepayRole, compensateType } = values;
    // 没有选中还款期数不处理
    if (!selectRepayKeys.length) return;
    let isOnlyLastTerm = false; // 是否只选择了最后一期
    if (
      selectRepayKeys.length === 1 &&
      selectRepayKeys[0] === data?.repayInfoList[data?.repayInfoList.length - 1]?.termDetail
    ) {
      isOnlyLastTerm = true;
    }
    if (isShangHaiYingHang) {
      if (!isOnlyLastTerm) {
        if (
          actualRepayRole === ACTUAL_REPAY_ROLE.PERSON ||
          (actualRepayRole === ACTUAL_REPAY_ROLE.THIRD_PARTY &&
            compensateType === COMPENSATE_TYPE.SINGLE)
        ) {
          form.setFieldValue('isFullAmount', true);
          setIsFullAmountDisabled(true);
        } else {
          setIsFullAmountDisabled(false);
        }
      } else {
        setIsFullAmountDisabled(false);
      }
    }
  };

  // 表单数据变化时，处理一些逻辑
  const onFormValueChange = (changedValues: any, values: any) => {
    handleIsFullAmount(values, selectRepayKeys);
  };

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: RepayItem[]) => {
      setSelectRepayKeys(selectedRowKeys);
      setSelectRows(selectedRows);
      //拿到应还总利息/元 应还总罚息/元  应还总其他费用/元
      selectWithDeduct(selectedRows);
      // 还款期数变化时处理一下是否足额还款逻辑
      const formValues = form.getFieldsValue();
      handleIsFullAmount(formValues, selectedRowKeys);
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
    },
    selectedRowKeys: selectRepayKeys,
    getCheckboxProps: (record: RepayItem) => {
      // let itemDisabled = false;

      const compensateType = form.getFieldValue('compensateType');
      // const actualRepayRole = form.getFieldValue('actualRepayRole');
      const itemDisabled =
        compensateType === COMPENSATE_TYPE.MULTIPLE || mapStatusZh[record.status] !== '待还款';
      // if (
      //   compensateType === COMPENSATE_TYPE.MULTIPLE &&
      //   actualRepayRole === ACTUAL_REPAY_ROLE.THIRD_PARTY
      // ) {
      //   itemDisabled = true;
      // } else {
      //   // 单期代偿和用户本人都可以选逾期和待还款
      //   if (mapStatusZh[record.status] === '待还款' || mapStatusZh[record.status] === '逾期') {
      //     itemDisabled = false;
      //   } else {
      //     itemDisabled = true;
      //   }
      // }

      return {
        disabled: itemDisabled, // 不为待还款的不允许选中 ,实际还款人=第三方代偿
      };
    },
  };

  const columns: ProColumns<RepayItem>[] = [
    {
      title: '期数',
      dataIndex: 'termDetail',
    },
    {
      title: '还款状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, row) => {
        return mapStatusZh[row?.status];
      },
    },
    { title: '应还日期', dataIndex: 'repayTime', key: 'repayTime' },
    { title: '应还总额', dataIndex: 'shouldAmountDue', key: 'shouldAmountDue' },
    { title: '应还本金', dataIndex: 'shouldPrincipal', key: 'shouldPrincipal' },
    { title: '应还利息', dataIndex: 'shouldInterest', key: 'shouldInterest' },
    { title: '逾期罚息', dataIndex: 'overdueInterest', key: 'overdueInterest' },
    {
      title: '其他费用',
      dataIndex: 'otherCost',
      key: 'otherCost',
    },
    {
      title: '剩余应还总额',
      dataIndex: 'remainingAmountDue',
      key: 'remainingAmountDue',
    },
  ];
  // //动态部分
  // const columnsDynamic = Object.keys(NEED_REMISSION_ITEM).map((item) => {
  //   return { title: NEED_REMISSION_ITEM[item], dataIndex: item, key: item };
  // });
  // const columns = [...columnsFix, columnsDynamic];
  const editColumns: ProColumns<RepayEditItem>[] = [
    {
      title: '规则序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 80,
    },
    {
      title: '*减免费项',
      key: 'costType',
      dataIndex: 'costType',
      valueType: 'select',
      // valueEnum: COST_TYPE,
      fieldProps: (_, row: any) => {
        const valueChangedEffects = (currentCostType: number) => {
          const res: any = {};
          // const valueMap = editableFormRef?.current?.getRowData(row?.rowIndex);
          // const valueMap = row?.entry;
          const periodOptAvailable = currentPeriodOpt.map((v: any) => v?.value);
          // console.log('currentCostType', currentCostType);

          if ([1, 3, 5].includes(currentCostType)) {
            // 需要期数的减免项
            res.remissionPeriod = periodOptAvailable;

            const resAmount = data?.repayInfoList.reduce(
              (previousValue: number, currentValue: any) => {
                if (
                  res?.remissionPeriod?.includes(currentValue?.term) &&
                  [1, 3, 5].includes(currentCostType)
                ) {
                  const valueType =
                    {
                      '1': 'shouldInterest',
                      '3': 'overdueInterest',
                      '5': 'otherCost',
                    }[currentCostType] || '';
                  if (currentCostType === 1) {
                    return new BigNumber(previousValue)
                      .plus(currentValue[valueType] || 0)
                      .minus(currentValue?.paidInterest || 0);
                  }
                  return new BigNumber(previousValue).plus(currentValue[valueType] || 0);
                }
                return previousValue;
              },
              0,
            );
            res.remissionAmount = resAmount;
            // console.log('res', res);

            editableFormRef?.current?.setRowData(row?.rowIndex, { ...res });
            setTimeout(() => {
              setDataSource((temp: any) => {
                temp[row?.rowIndex] = {
                  ...temp[row?.rowIndex],
                  ...res,
                };
                return temp;
              });
            }, 300);
          }
          if (currentCostType === 2) {
            form.setFieldsValue({
              computeLoanBase: '1',
              computeLoanBaseAmount: computeRemissionItem?.principal.toFixed(2),
            });

            editableFormRef?.current?.setRowData(row?.rowIndex, {
              remissionAmount: computeRemissionItem?.liquidatedDamages,
            });
            setTimeout(() => {
              setDataSource((temp: any) => {
                temp[row?.rowIndex] = {
                  ...temp[row?.rowIndex],
                  remissionAmount: computeRemissionItem?.liquidatedDamages,
                };
                return temp;
              });
              setForceUpdateData(!forceUpdateData);
            }, 300);
          }
        };
        return {
          options: COST_TYPE_OPT.map((value) => {
            const selectedKeys = dataSource?.map((v) => v.costType);
            // 当订单放款资方=上海银行，且回购标识=否时，利息置灰不能选择
            if (isShangHaiYingHang && [1].includes(value?.value)) {
              return {
                ...value,
                disabled: true,
              };
            }
            // 融租禁用滞纳金、罚息
            if ([3, 5].includes(value?.value)) {
              return {
                ...value,
                disabled: true,
              };
            }
            if (
              value?.value === 2 &&
              (!settleIntersetPercent || new BigNumber(settleIntersetPercent).toNumber() === 0)
            ) {
              // 违约金收取比例为0，禁用违约金减免
              return {
                ...value,
                disabled: true,
              };
            }
            return {
              ...value,
              disabled: selectedKeys.includes(value.value),
            };
          }),
          onChange: valueChangedEffects,
          allowClear: false,
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            // whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
    },
    {
      title: '*减免期数',
      key: 'remissionPeriod',
      dataIndex: 'remissionPeriod',
      valueType: 'select',
      fieldProps: (_, row: any) => {
        // const valueMap = editableFormRef?.current?.getRowData(row?.rowIndex);
        const valueMap = row?.entry;
        const changeValueByPeriod = (currentSelectPeriod: any) => {
          const res = data?.repayInfoList.reduce((previousValue: number, currentValue: any) => {
            if (
              currentSelectPeriod.includes(currentValue?.term) &&
              [1, 3, 5].includes(valueMap?.costType)
            ) {
              const valueType = {
                '1': 'shouldInterest',
                '3': 'overdueInterest',
                '5': 'otherCost',
              }[valueMap?.costType];
              if (valueMap?.costType === 1) {
                return new BigNumber(previousValue)
                  .plus(currentValue[valueType] || 0)
                  .minus(currentValue?.paidInterest || 0)
                  .toNumber();
              }
              return new BigNumber(previousValue).plus(currentValue[valueType] || 0).toNumber();
            }
            return previousValue;
          }, 0);
          setTimeout(() => {
            setDataSource((temp: any) => {
              temp[row?.rowIndex] = {
                ...temp[row?.rowIndex],
                remissionPeriod: currentSelectPeriod,
                remissionAmount: res,
              };
              return temp;
            });
          }, 300);
          editableFormRef.current.setRowData(row?.rowIndex, { remissionAmount: res });
        };
        return {
          showSearch: true,
          mode: 'multiple',
          showArrow: true,
          disabled: valueMap?.costType === 2,
          onChange: changeValueByPeriod,
          options: currentPeriodOpt,
          className: valueMap?.costType === 2 ? 'hidden-item' : '',
          maxTagCount: 4,
        };
      },
      // valueEnum: MapPeriod,
      formItemProps: (_, row: any) => {
        return {
          rules: [
            {
              required: row?.entry?.costType !== 2,
              message: '此项是必填项',
            },
          ],
        };
      },
      // render: (_, row) => {
      //   return row.remissionPeriod
      //     ? `${row.remissionPeriod[0]}~${row.remissionPeriod[row.remissionPeriod.length - 1]}`
      //     : '-';
      // },
    },
    {
      title: '*减免金额',
      dataIndex: 'remissionAmount',
      fieldProps: (_, row: any) => {
        const changedValue = (e: any) => {
          const max = {
            '1': computeRemissionItem?.maxValueMapBySelectOptPeriods[1],
            '2': computeRemissionItem?.liquidatedDamages,
            '3': computeRemissionItem?.maxValueMapBySelectOptPeriods[3],
            '5': computeRemissionItem?.maxValueMapBySelectOptPeriods[5],
          }[row?.entry?.costType];
          let value = e?.target?.value;
          if (
            (max != undefined && new BigNumber(value).minus(max).toNumber() > 0) ||
            Number(value) < 0 ||
            Number.isNaN(Number(value))
          ) {
            value = max;
          }
          const res = new BigNumber(value || 0).toFixed(2);
          setTimeout(() => {
            setDataSource((temp: any) => {
              temp[row?.rowIndex] = {
                ...temp[row?.rowIndex],
                remissionAmount: res,
              };
              return temp;
            });
            setForceUpdateData(!forceUpdateData);
          }, 300);
          editableFormRef.current.setRowData(row?.rowIndex, { remissionAmount: res });
        };
        // const changeEvent = () => {
        //   if (row?.entry?.costType === 2) {
        //     // setTimeout(() => {
        //     //   setDataSource((temp: any) => {
        //     //     temp[row?.rowIndex] = {
        //     //       ...temp[row?.rowIndex],
        //     //       remissionAmount: computedLoanFee,
        //     //     };
        //     //     return temp;
        //     //   });
        //     // }, 300);
        //     // editableFormRef.current.setRowData(row?.rowIndex, { remissionAmount: computedLoanFee });
        //   }
        // };
        return {
          onBlur: changedValue,
          // onchange: changedValue,
          // disabled: row?.entry?.costType === '2',
          // value: row?.entry?.costType === '2' ? computedLoanFee : undefined
        };
      },
      // renderFormItem: (_, row)=>{
      //   return <Input value={dataSource[row?.rowIndex]?.remissionAmount}  />
      // },
      formItemProps: (_, row: any) => {
        return {
          rules: [
            {
              required: true,
              whitespace: true,
              message: '此项是必填项',
            },
            {
              message: '必须包含数字',
              pattern: /\d+(\.\d{0,2})?/,
            },
            {
              validator: (vali, value) => {
                const max = {
                  '1': computeRemissionItem?.maxValueMapBySelectOptPeriods[1],
                  '2': computeRemissionItem?.liquidatedDamages,
                  '3': computeRemissionItem?.maxValueMapBySelectOptPeriods[3],
                  '5': computeRemissionItem?.maxValueMapBySelectOptPeriods[5],
                }[row?.entry?.costType];
                if (max !== undefined && new BigNumber(value).minus(max).toNumber() > 0) {
                  return Promise.reject(new Error(`减免金额不能大于应还金额`));
                }
                if (new BigNumber(value).toNumber() < 0) {
                  return Promise.reject(new Error(`减免金额不能小于0`));
                }
                return Promise.resolve();
              },
            },
          ],
        };
      },
    },
    {
      title: '操作',
      valueType: 'option',
      // render: ()=>''
    },
  ];

  const checkArrIsSeque = (arr: number[]) => {
    // console.log(arr, 'arr');
    const arrNew = arr.sort((a, b) => {
      return a - b;
    });
    const first = arrNew[0];
    if (arr.length === 1) return true;
    // console.log(arr.sort(), 'arr.sort');
    //不是有序的
    const isNotSeque = arrNew.some((item, index) => {
      if (index !== 0) {
        return item !== first + index;
      }
    });
    return !isNotSeque;
  };
  const [showLoanBase, setShowLoanBase] = useState(false);
  useEffect(() => {
    // console.log('dataSource', dataSource);
    setEditableRowKeys(dataSource.map((v) => v?.id));
    const isNeedShowLoanAmount = dataSource?.some((v: any) => {
      return v?.costType === 2;
    });
    if (isNeedShowLoanAmount) {
      form.setFieldsValue({
        // computeLoanBase: '1',
        // computeLoanBaseAmount: totalRemissionItem.principalAfterDeduct,
      });
    }
    setShowLoanBase(isNeedShowLoanAmount);
  }, [dataSource]);

  return (
    <>
      <ModalForm
        title="线下还款"
        layout="horizontal"
        className={itemStyle.offLineDefine}
        visible={visible}
        modalProps={{
          centered: true,
          onCancel: close,
          width: 1000,
          okText: '提交',
          // destroyOnClose: true,
        }}
        form={form}
        onValuesChange={onFormValueChange}
        onFinish={async (value) => {
          console.log(123);

          const { compensateType, isFullAmount, actualAmount, isRemission } = value;
          //accountName accountNumber  orderNo productCode repayPlayNos
          const repayPlayNos = selectRows?.map((item: any) => item?.repayPlanNo);
          const mapUploadFile = convertUploadFileList(allFileList, ['attach']);
          const actualAmountTemp = isFullAmount ? computeRemissionItem?.leftTotal : actualAmount;
          const isRemissionTemp = isFullAmount ? isRemission === false : isRemission;
          // if (Number(someMount?.shouldRepayRestAmount) === 0) {
          //   message.error('当前无可应还金额，无法提交!');
          //   return;
          // }
          const repayPlanNoMap = {};
          data?.repayInfoList?.forEach((dataItem: any) => {
            repayPlanNoMap[dataItem?.term] = dataItem?.repayPlanNo;
          });
          if (
            compensateType === COMPENSATE_TYPE.MULTIPLE &&
            !isFullAmount &&
            isRemission &&
            // Number(actualAmountTemp) !== Number(totalRemissionItem.totalAfterDeduct)
            dataSource?.some((v) => !v.costType)
          ) {
            message.error('请调整减免规则，减免费项必填');
            return;
          }
          const costDetail =
            compensateType === COMPENSATE_TYPE.MULTIPLE
              ? dataSource
                  ?.filter((v) => v.costType)
                  ?.map((item: any) => {
                    return {
                      id: item.id,
                      costType: item.costType,
                      remissionAmount: item.remissionAmount,
                      remissionPeriod: item.remissionPeriod?.map((v: number) => {
                        return repayPlanNoMap[v];
                      }),
                    };
                  }) || []
              : [];
          console.log('costDetail', costDetail);

          //检查选中的是否跳期还款
          let selectPeriods = selectRows.map((item) => {
            return Number(item?.termDetail?.split('/')[0]);
          });
          selectPeriods = selectPeriods?.sort((a: number, b: number) => Number(a) - Number(b));
          //当前的还款表格能还期数的数组 逾期的不允许在线下还款操作，如果选了
          const canReapyPeriods = data?.repayInfoList
            .filter((item: RepayItem) => {
              return mapStatusZh[item.status] === '待还款' || mapStatusZh[item.status] === '逾期';
            })
            .map((itemFilter: RepayItem) => {
              // console.log(itemFilter?.termDetail?.split('/')[0]);
              // return Number(itemFilter?.termDetail?.split('/')[0]);
              return Number(itemFilter?.term);
            });
          // console.log(checkArrIsSeque(selectPeriods), selectPeriods, canReapyPeriods);
          if (canReapyPeriods[0] !== selectPeriods[0]) {
            message.error('不允许跳期还款，必须按未还期数依次还款。');
            return;
          }
          if (!checkArrIsSeque(selectPeriods)) {
            message.error('不允许跳期还款，必须按未还期数依次还款。');
            return;
          }
          //代偿结清的时候，减免后应收还款金额 需要等于实际还款金额
          if (
            compensateType === COMPENSATE_TYPE.MULTIPLE &&
            // Number(actualAmountTemp) !== Number(totalRemissionItem.totalAfterDeduct)
            Number(actualAmountTemp) !== Number(computeRemissionItem?.leftTotal.toFixed(2))
          ) {
            message.error('请调整减免规则，确保【减免后应收金额】与【实际还款金额】一致');
            return;
          }
          console.log('costDetail', costDetail);
          //判断是否跳期还款
          // console.log('----', value, mapUploadFile);
          submitOfflineRepay({
            ...value,
            ...mapUploadFile,
            actualRepayRole: value?.actualRepayRole,
            compensateType: value?.compensateType,
            customLiquidatedDamages: value?.computeLoanBase === '2',
            liquidatedDamagesAmount:
              value?.computeLoanBaseAmount?.toFixed(2) ||
              computeRemissionItem?.principal?.toFixed(2),
            accountName,
            accountNumber,
            orderNo,
            productCode,
            repayPlayNos,
            actualAmount: Number(actualAmountTemp).toFixed(2), //如果是足额还款  取应还  不然写实际的
            isRemission: isRemissionTemp,
            costDetail:
              compensateType === COMPENSATE_TYPE.MULTIPLE && !isFullAmount && isRemission
                ? costDetail || []
                : [],
            totalRepayAmount: computeRemissionItem?.needRepayTotal, //应还总额 actualAmount是实际应还总额
            freezeAmount: data?.freezingAmount,
            // totalRemissionAmount: totalRemissionItem.totalRemissionAmount,
            totalRemissionAmount: computeRemissionItem?.remissionTotal,
            proportionNumberValue: data?.proportionNumberValue,
          }).then(() => {
            message.success('操作成功');
            close();
          });
        }}
      >
        <DividerTit title="还款信息" style={{ marginTop: 0 }}>
          <ProForm.Group>
            {/* 实际还款方 */}
            <ProFormSelect
              rules={[{ required: true }]}
              name="actualRepayRole"
              width="sm"
              label="实际还款方"
              placeholder="请选择"
              options={[
                {
                  label: '用户本人',
                  value: '1',
                },
                {
                  label: '第三方',
                  value: '2',
                  disabled: productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN,
                },
              ]}
              fieldProps={{
                onChange: (val) => {
                  if (val === ACTUAL_REPAY_ROLE.PERSON) {
                    //多期代偿不足而还款，需要减免
                    form.setFieldsValue({
                      isFullAmount: true,
                      isRemission: false,
                      compensateType: '',
                    });
                    setSelectRepayKeys([]);
                    getTotal([]);
                  }
                },
              }}
            />
            {/* 还款日期 */}
            <ProFormDatePicker
              name="repayDate"
              width="sm"
              rules={[{ required: true }]}
              fieldProps={{
                disabledDate: disableFutureDate,
              }}
              placeholder="选择日期"
              label="还款日期"
            />
          </ProForm.Group>
          <ProFormDependency name={['actualRepayRole']}>
            {({ actualRepayRole }) => {
              return actualRepayRole == ACTUAL_REPAY_ROLE.THIRD_PARTY ? (
                <ProForm.Group>
                  {/* 代偿方式 */}
                  <ProFormSelect
                    rules={[{ required: true }]}
                    name="compensateType"
                    width="sm"
                    label="代偿方式"
                    // placeholder="请选择"
                    options={[
                      {
                        label: '单期代偿',
                        value: '1',
                      },
                      {
                        label: '代偿结清',
                        value: '2',
                        disabled: hasSomeIsNotRepay, //只有全部为贷款还款才能选中代偿结清
                      },
                    ]}
                    fieldProps={{
                      //如果为代偿结清，待还款的item全部要选中
                      onChange: (val) => {
                        if (val === COMPENSATE_TYPE.MULTIPLE) {
                          // 当配置为实际还款方=第三方，代偿方式选择“代偿结清”时，需要判断当前订单对应的还款计划剩余未还期数还款状态是否=待还款，并且在途未入账的金额=0
                          if (currentPeriodOpt.length === 0 || someMount?.repayingMount != 0) {
                            form.setFieldsValue({ compensateType: '1', isRemission: false });
                            setSelectRepayKeys([]);
                            getTotal([]);
                            message.error('该订单状态不满足第三方代偿结清');
                            return;
                          }
                          //多期代偿不足而还款，需要减免
                          form.setFieldsValue({ isFullAmount: false, isRemission: true });
                          setSelectRepayKeys(
                            data?.repayInfoList.map((item: ShouldRepayItem) => {
                              if (mapStatusZh[item.status] === '待还款') return item?.termDetail;
                            }),
                          );
                          setSelectRows(
                            data?.repayInfoList?.filter((item: ShouldRepayItem) => {
                              return mapStatusZh[item.status] === '待还款';
                            }),
                          );
                          selectWithDeduct(
                            data?.repayInfoList?.filter((item: ShouldRepayItem) => {
                              return mapStatusZh[item.status] === '待还款';
                            }),
                          );
                        } else if (val === COMPENSATE_TYPE.SINGLE) {
                          //单期代偿 足额还款，不默认 不减免默认
                          form.setFieldsValue({ isFullAmount: true, isRemission: false });
                          setSelectRepayKeys([]);
                          getTotal([]);
                        }
                      },
                    }}
                  />
                  {/* 代偿第三方名称 */}
                  <ProFormText
                    rules={[{ required: true }]}
                    name="compensateOrgName"
                    width="sm"
                    label="代偿第三方名称"
                  />
                </ProForm.Group>
              ) : (
                ''
              );
            }}
          </ProFormDependency>
          {/* 是否支持跳期还款 */}
          <ProForm.Group>
            <ProFormText
              rules={[
                {
                  required: true,
                  max: 64,
                  pattern: /^[A-Za-z0-9]+$/,
                  message: '请输入三方还款流水号（支持大小写字母和数字）',
                },
              ]}
              name="thirdFlowId"
              width="sm"
              label="三方还款流水号"
            />
            <ProFormRadio.Group
              name="isAllowJumpTerm"
              disabled
              width="sm"
              rules={[{ required: true }]}
              label="是否支持跳期还款"
              initialValue={false}
              options={[
                {
                  label: '是',
                  value: true,
                },
                {
                  label: '否',
                  value: false,
                },
              ]}
            />
          </ProForm.Group>
          <ProForm.Group>
            <ProFormSelect
              rules={[{ required: true }]}
              name="repayChannel"
              width="sm"
              label="还款渠道"
              options={[
                {
                  label: '微信二维码',
                  value: RECIEVE_BANK_CHANNEL.WEIXIN,
                },
                {
                  label: '对公打款',
                  value: RECIEVE_BANK_CHANNEL.COMPANY,
                },
              ]}
              fieldProps={{
                onChange: (val) => {
                  form.setFieldsValue({ repayBankNo: CHANNEL_BANK_NO[val] });
                },
              }}
            />
            <ProFormDependency name={['repayChannel']}>
              {({ repayChannel }) => {
                if (
                  channelCode === LEASE_LOAN_CHANNEL.BO_HAI_YIN_HANG &&
                  repayChannel === RECIEVE_BANK_CHANNEL.COMPANY
                ) {
                  return (
                    <ProFormSelect
                      rules={[{ required: true }]}
                      name="repayBankNo"
                      width="sm"
                      label="银行卡号"
                      options={[
                        {
                          label: CHANNEL_BANK_NO[RECIEVE_BANK_CHANNEL.COMPANY],
                          value: CHANNEL_BANK_NO[RECIEVE_BANK_CHANNEL.COMPANY],
                        },
                        {
                          label: BOHAI_BANK_NO,
                          value: BOHAI_BANK_NO,
                        },
                      ]}
                    />
                  );
                }

                return (
                  <ProFormText
                    rules={[{ required: true }]}
                    name="repayBankNo"
                    width="sm"
                    label="银行卡号"
                    placeholder="请输入银行卡号"
                    disabled={true}
                    tooltip={{
                      title:
                        '因财务对账所需，需录入我们的收款账户号。（微信二维码：**********、对公打款：***************）',
                      overlayStyle: { minWidth: 300 },
                    }}
                  />
                );
              }}
            </ProFormDependency>
          </ProForm.Group>
        </DividerTit>

        <DividerTit
          title={
            <>
              还款期数{' '}
              <span style={{ color: 'grey', fontSize: 14 }}>
                注：汇总数据是根据选中的期数进行汇总。
              </span>
            </>
          }
        >
          <ShowInfo
            noCard
            infoMap={totalQuotaItemMap}
            data={totalQuotaItem}
            selfDefine={selfDefineTotal}
            // textClassName={globalStyle?.colorRed}
          />
          {/*  */}
          <ProFormDependency name={['isRemission', 'isFullAmount']}>
            {/* 计算基准 */}
            {({ isRemission, isFullAmount }) => {
              return (
                <>
                  {!isFullAmount && isRemission && showLoanBase && (
                    <ProForm.Group>
                      {/* 违约金计算基准 */}
                      <ProFormSelect
                        rules={[{ required: true }]}
                        name="computeLoanBase"
                        width="sm"
                        label={
                          <span>
                            违约金计算基准
                            <Tooltip
                              placement="top"
                              title="违约金计算规则=本金*6%（本金可为剩余未还本金，也可以人工自定义；其中6%为产品管理配置比例）"
                            >
                              <QuestionCircleOutlined />
                            </Tooltip>
                          </span>
                        }
                        disabled={isShangHaiYingHang}
                        options={[
                          {
                            label: '剩余未还本金',
                            value: '1',
                          },
                          {
                            label: '自定义',
                            value: '2',
                          },
                        ]}
                        fieldProps={{
                          //
                          onChange: (val) => {
                            if (val === '1') {
                              form.setFieldsValue({
                                // computeLoanBaseAmount: totalRemissionItem.principalAfterDeduct,
                                computeLoanBaseAmount: computeRemissionItem?.principal,
                                actualAmount: undefined,
                              });
                              const res: any = new BigNumber(
                                // totalRemissionItem.principalAfterDeduct,
                                computeRemissionItem?.principal || 0,
                              )
                                .times(settleIntersetPercent)
                                .toFixed(2, 0);
                              const rowIndex = dataSource.findIndex((v: any) => v.costType === 2);
                              // setComputedLoanFee(res);
                              editableFormRef?.current?.setRowData(rowIndex, {
                                remissionAmount: res,
                              });
                              setTimeout(() => {
                                setDataSource((temp: any) => {
                                  temp[rowIndex] = {
                                    ...temp[rowIndex],
                                    remissionAmount: res,
                                  };
                                  return temp;
                                });
                                setForceUpdateData(!forceUpdateData);
                              }, 300);
                            }
                          },
                          allowClear: false,
                        }}
                      />
                      <ProFormDependency name={['computeLoanBase']}>
                        {({ computeLoanBase }) => {
                          return computeLoanBase === '2' ? (
                            <>
                              <ProFormDigit
                                rules={[
                                  { required: true },
                                  {
                                    validator: (_, value) => {
                                      console.log(value);

                                      if (
                                        value > (computeRemissionItem?.principal.toFixed(2) || 0)
                                      ) {
                                        return Promise.reject(
                                          new Error(
                                            `本金不能大于${computeRemissionItem.principal.toString()}`,
                                          ),
                                        );
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                                name="computeLoanBaseAmount"
                                max={computeRemissionItem?.principal?.toString()}
                                width="sm"
                                label="计算基准本金"
                                disabled={computeLoanBase === '1'}
                                fieldProps={{
                                  // 计算提前结清违约金
                                  onChange: (val) => {
                                    const valToFixd = new BigNumber(val || 0).toFixed(2);
                                    const res: any = new BigNumber(valToFixd)
                                      .times(settleIntersetPercent)
                                      .toFixed(2, 0);
                                    const rowIndex = dataSource.findIndex(
                                      (v: any) => v.costType === 2,
                                    );
                                    // setComputedLoanFee(res);

                                    form.setFieldsValue({ actualAmount: undefined });
                                    // form.setFieldsValue({computeLoanBaseAmount: valToFixd})
                                    setTimeout(() => {
                                      editableFormRef?.current?.setRowData(rowIndex, {
                                        remissionAmount: res,
                                      });
                                      setDataSource((temp: any) => {
                                        temp[rowIndex] = {
                                          ...temp[rowIndex],
                                          remissionAmount: res,
                                        };
                                        return temp;
                                      });
                                      setForceUpdateData(!forceUpdateData);
                                    });
                                  },
                                }}
                              />
                            </>
                          ) : (
                            <></>
                          );
                        }}
                      </ProFormDependency>
                      {/* 代偿第三方名称 */}
                    </ProForm.Group>
                  )}
                </>
              );
            }}
          </ProFormDependency>
          {/*  */}
          <Table
            rowSelection={{
              type: 'checkbox',
              ...rowSelection,
            }}
            pagination={false}
            rowKey="termDetail"
            columns={columns as ColumnType<RepayItem>[]}
            dataSource={data?.repayInfoList}
          />
          <ProForm.Group style={{ marginTop: 10 }}>
            <ProForm.Item label="还款中/元" className={itemStyle.formItemRemission}>
              <div>{someMount?.repayingMount}</div>
            </ProForm.Item>
            {/* 剩余应还金额/元： */}
            <ProForm.Item
              label={
                <>
                  <b>
                    <Tooltip placement="topLeft" title="注释：剩余应还金额=应还款总金额-还款中">
                      <QuestionCircleOutlined className={globalStyle.iconCss} />
                    </Tooltip>
                    剩余应还金额/元：
                  </b>
                </>
              }
              className={itemStyle.formItemRemission}
            >
              <div>
                {new BigNumber(computeRemissionItem?.needRepayTotal)
                  .minus(someMount?.repayingMount || 0)
                  .toFixed(2)}
              </div>

              {/* <div>{new BigNumber(someMount?.orderAmount).minus(someMount?.repayingMount)}</div> */}
            </ProForm.Item>
          </ProForm.Group>
        </DividerTit>
        <DividerTit title="减免规则">
          <ProFormDependency name={['compensateType', 'actualRepayRole']} style={{ marginTop: 10 }}>
            {({ compensateType, actualRepayRole }) => {
              return (
                <>
                  <ProForm.Group>
                    <ProFormRadio.Group
                      name="isFullAmount"
                      width="sm"
                      disabled={isFullAmountDisabled}
                      rules={[{ required: true }]}
                      initialValue={
                        actualRepayRole === ACTUAL_REPAY_ROLE.THIRD_PARTY &&
                        compensateType === COMPENSATE_TYPE.MULTIPLE
                      }
                      label="是否足额还款"
                      fieldProps={{
                        onChange: () => {
                          form.setFieldsValue({ actualAmount: undefined });
                          setForceUpdateData(!forceUpdateData);
                        },
                      }}
                      options={[
                        {
                          label: '是',
                          value: true,
                        },
                        {
                          label: '否',
                          value: false,
                        },
                      ]}
                    />
                    <ProFormDependency name={['isFullAmount']}>
                      {({ isFullAmount }) => {
                        // console.log(isFullAmount);

                        return isFullAmount === false ? (
                          <ProFormDigit
                            name="actualAmount"
                            fieldProps={{ precision: 2 }}
                            rules={[
                              { required: true },
                              {
                                validator: (_, value) => {
                                  // const needRemisssion = form?.getFieldValue('isRemission');
                                  const repayTotal = new BigNumber(
                                    computeRemissionItem?.needRepayTotal,
                                  ).toNumber();
                                  const leftRepayTotal = new BigNumber(
                                    computeRemissionItem?.leftTotal,
                                  ).toNumber();
                                  if (value === 0) {
                                    return Promise.reject(new Error('实际还款金额为0,需大于0'));
                                  }
                                  if (value > repayTotal) {
                                    return Promise.reject(
                                      new Error('实际还款金额不应大于应还款总金额'),
                                    );
                                  }
                                  if (value) {
                                    // console.log(value);
                                    // const phoneReg = /^1\d{10}$/;
                                    if (value > leftRepayTotal) {
                                      return Promise.reject(
                                        new Error('实际还款金额不应大于剩余应还金额'),
                                      );
                                    }
                                  }
                                  return Promise.resolve();
                                },
                              },
                            ]}
                            label="实际还款金额/元"
                          />
                        ) : (
                          <></>
                        );
                      }}
                    </ProFormDependency>
                  </ProForm.Group>
                </>
              );
            }}
          </ProFormDependency>
          <ProForm.Group>
            <ProFormDependency name={['isFullAmount', 'actualRepayRole', 'compensateType']}>
              {({ isFullAmount, actualRepayRole, compensateType }) => {
                return isFullAmount === false ? (
                  <ProFormRadio.Group
                    width="sm"
                    name="isRemission"
                    rules={[{ required: true }]}
                    disabled={
                      // false
                      !(
                        actualRepayRole === ACTUAL_REPAY_ROLE.THIRD_PARTY &&
                        compensateType === COMPENSATE_TYPE.MULTIPLE
                      )
                    }
                    // disabled={true}
                    label="是否需要减免"
                    fieldProps={{
                      onChange: () => {
                        form.setFieldsValue({ actualAmount: undefined });
                        setForceUpdateData(!forceUpdateData);
                      },
                    }}
                    options={[
                      {
                        label: '是',
                        value: true,
                      },
                      {
                        label: '否',
                        value: false,
                      },
                    ]}
                  />
                ) : (
                  <></>
                );
              }}
            </ProFormDependency>
            {/* 需要减免的才展示 */}
            <ProFormDependency name={['isRemission', 'isFullAmount']}>
              {({ isRemission, isFullAmount }) => {
                return !isFullAmount && isRemission ? (
                  <ProForm.Item label="减免后剩余应收金额/元">
                    <div style={{ marginLeft: 7 }}>
                      <span className={globalStyle?.colorRed}>
                        {computeRemissionItem?.leftTotal.toFixed(2)}
                      </span>
                    </div>
                  </ProForm.Item>
                ) : (
                  <></>
                );
              }}
            </ProFormDependency>
          </ProForm.Group>
          <ProFormDependency name={['isRemission', 'isFullAmount']}>
            {({ isRemission, isFullAmount }) => {
              //需要减免和有勾选中
              return !isFullAmount && isRemission && selectRepayKeys.length ? (
                <>
                  <ProForm.Group>
                    <ProForm.Item label="减免后总本金/元" className={itemStyle.formItemRemission}>
                      <div>{computeRemissionItem?.principal.toFixed(2)}</div>
                    </ProForm.Item>
                    <ProForm.Item label="减免后总利息/元" className={itemStyle.formItemRemission}>
                      <div>{computeRemissionItem?.leftInterest.toFixed(2)}</div>
                    </ProForm.Item>
                  </ProForm.Group>
                  <ProForm.Group>
                    <ProForm.Item label="减免后总罚息/元" className={itemStyle.formItemRemission}>
                      <div>{computeRemissionItem?.leftPenaltyInterest.toFixed(2)}</div>
                    </ProForm.Item>
                    <ProForm.Item
                      label="减免后总其他费用/元"
                      className={itemStyle.formItemRemission}
                    >
                      <div>{computeRemissionItem?.leftOtherInterest.toFixed(2)}</div>
                    </ProForm.Item>
                  </ProForm.Group>
                  <ProForm.Group>
                    <ProForm.Item label="减免后违约金/元" className={itemStyle.formItemRemission}>
                      <div>{computeRemissionItem?.leftLiquidatedDamages.toFixed(2)}</div>
                    </ProForm.Item>
                  </ProForm.Group>
                  <EditableProTable
                    rowKey="id"
                    // toolBarRender={false}
                    // toolBarRender={() => {
                    //   return [
                    //     <Button
                    //       type="primary"
                    //       key="save"
                    //       onClick={() => {
                    //         // dataSource 就是当前数据，可以调用 api 将其保存
                    //         console.log(dataSource);
                    //       }}
                    //     >
                    //       保存数据
                    //     </Button>,
                    //   ];
                    // }}
                    value={dataSource}
                    onChange={setDataSource}
                    editableFormRef={editableFormRef}
                    scroll={{ x: 'max-content' }}
                    actionRef={actionRef}
                    columns={editColumns}
                    // controlled
                    recordCreatorProps={{
                      newRecordType: 'dataSource',
                      record: () => ({
                        id: randomID(),
                        remissionAmount: '0.00',
                      }),
                      creatorButtonText: '新增规则',
                    }}
                    editable={{
                      type: 'multiple',
                      editableKeys,
                      onChange: setEditableRowKeys,
                      onValuesChange: (record, recordList) => {
                        // console.log('onValuesChange', recordList);
                        setDataSource(recordList);
                        form.setFieldsValue({ actualAmount: undefined });
                        setTimeout(() => {
                          setForceUpdateData(!forceUpdateData);
                        }, 400);
                      },
                      actionRender: (row: any, config, defaultDom) => {
                        if (row?.index === 0) {
                          return [];
                        }
                        return [defaultDom.delete];
                      },
                    }}
                  />
                </>
              ) : (
                <></>
              );
            }}
          </ProFormDependency>
        </DividerTit>
        <DividerTit title="还款凭证">
          <CommonImageUpload
            extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
            label="上传附件"
            name="attach"
            rules={[{ required: true }]}
            // labelCol={{ span: 5 }}
            max={5}
            listType="text"
            size={10}
            uploadPath="/bizadmin/oss/common/uploadFile"
            fileListEdit={allFileList?.attach || []}
            desPath="EP_AUTH_INFO"
            mapFileList={mapFileList}
            accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
          />
        </DividerTit>
      </ModalForm>
    </>
  );
};

export default AddCashAndLeaseOfflineRepay;
