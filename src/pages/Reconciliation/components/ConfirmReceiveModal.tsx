/*
 * @Date: 2023-07-17 14:32:42
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-07-18 11:43:52
 * @FilePath: /lala-finance-biz-web/src/pages/Reconciliation/components/ConfirmReceiveModal.tsx
 * @Description:
 *
 */
import React from 'react';
import { ModalForm, ProFormText, ProFormDatePicker } from '@ant-design/pro-form';
import { message } from 'antd';
import { confirmReceiveBill } from '../service';
import type { ConfirmParams } from '../data';
import BigNumber from 'bignumber.js';

export type ConfirmReceiveModalProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  reconciliationFlowNo: string;
  remitAmount: number;
};

const ConfirmReceiveModal: React.FC<ConfirmReceiveModalProps> = ({
  visible,
  close,
  onOk,
  reconciliationFlowNo,
  remitAmount,
}) => {
  return (
    <>
      <ModalForm
        title="到账确认"
        width="500px"
        layout="horizontal"
        visible={visible}
        initialValues={{
          reconciliationFlowNo,
          remitAmount: Number(new BigNumber(remitAmount).div(100)),
        }}
        modalProps={{
          centered: true,
          onCancel: close,
        }}
        onFinish={async (values) => {
          delete values.remitAmount;
          confirmReceiveBill({
            ...values,
          } as ConfirmParams).then(() => {
            message.success('操作成功');
            close();
            onOk();
          });
          return true;
        }}
      >
        <ProFormText
          label="调账流水号"
          labelCol={{ span: 5 }}
          width="md"
          name="reconciliationFlowNo"
          disabled
        />
        <ProFormText
          label="汇款金额"
          width="md"
          labelCol={{ span: 5 }}
          fieldProps={{ suffix: '元' }}
          name="remitAmount"
          disabled
        />
        <ProFormText
          name="receiveBankNo"
          labelCol={{ span: 5 }}
          width="md"
          fieldProps={{ maxLength: 100 }}
          rules={[{ required: true }]}
          label="到账流水号"
          placeholder="请输入到账流水号"
        />
        <ProFormDatePicker
          rules={[{ required: true }]}
          labelCol={{ span: 5 }}
          placeholder="请输入到账时间"
          width="md"
          name="receiveTime"
          label="到账时间"
        />
      </ModalForm>
    </>
  );
};

export default ConfirmReceiveModal;
