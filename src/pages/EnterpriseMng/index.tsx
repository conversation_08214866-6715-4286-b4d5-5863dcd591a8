import HeaderTab from '@/components/HeaderTab';
import { SECONDARY_CLASSIFICATION_MAP } from '@/enums';
import globalStyle from '@/global.less';
import { disableFutureDate, downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess } from '@umijs/max';
import { Button, DatePicker, Switch } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import type {
  EnterpriseProductListItem,
  EnterpriseProductParams,
  EnterpriseUserListItem,
  EnterpriseUserListParams,
} from './data';
import {
  getEnterpriseProductList,
  getEnterpriseUserList,
  productExport,
  userExport,
} from './service';
// /quota/user/enterprise/excel

const ComUserList: React.FC<{}> = () => {
  const actionRef = useRef<ActionType>();
  const actionRefProduct = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [checked, setChecked] = useState<boolean>(true);
  const [exportLoading, setExportLoading] = useState(false);
  // const view= useMemo(()=>{

  // },[checked])
  // 当前登陆用户信息
  const access = useAccess();
  const getExport = (form: EnterpriseUserListParams) => {
    setExportLoading(true);
    userExport(form)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };

  const getExportProduct = (form: EnterpriseProductParams) => {
    setExportLoading(true);
    productExport(form)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };

  const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
    return (
      <DatePicker.RangePicker
        {...rest}
        className={globalStyle.w100}
        disabledDate={disableFutureDate}
      />
    );
  };

  const columns: ProColumns<EnterpriseUserListItem>[] = [
    {
      title: '用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
      key: 'enterpriseName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'idNo',
      key: 'idNo',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeCode',
      key: 'productSecondTypeCode',
      valueType: 'select',

      // 如果是企业财务，默认共享应收账款，应收账款
      initialValue: access.hasRole('enterpriseFinance') ? ['0103', '0106'] : [],
      valueEnum: SECONDARY_CLASSIFICATION_MAP,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: access.hasRole('enterpriseFinance') ? true : false,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      hideInTable: true,
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeName',
      key: 'productSecondTypeName',
      search: false,
    },
    {
      title: '授信总额度',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
      search: false,
    },
    {
      title: '已使用额度',
      dataIndex: 'usedAmount',
      key: 'usedAmount',
      search: false,
    },
    {
      title: '冻结额度',
      dataIndex: 'freezeAmount',
      key: 'freezeAmount',
      search: false,
    },
    {
      title: '剩余可用额度',
      dataIndex: 'currentAmount',
      key: 'currentAmount',
      search: false,
    },
    {
      title: '评级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      search: false,
    },
    {
      title: '保理服务费率（%）',
      dataIndex: 'factoringServiceFee',
      key: 'factoringServiceFee',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        1: { text: '激活', status: 'success' },
        0: { text: '冻结', status: 'error' },
      },
    },
    {
      title: '授信时间',
      dataIndex: 'creditEndTime',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.creditEndTime;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          startCreditEndTime: `${value[0].split(' ')[0]} 00:00:00`,
          endCreditEndTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 180,
      fixed: 'right',
      valueType: 'option',
      render: (text, row) => (
        <>
          <Link to={`/userMng/enterpriseMng/com-detail?userNo=${row.userNo}`}>查看详情</Link>
          <Button type="link" className={globalStyle.ml10}>
            {row.status === 1 ? '冻结' : '激活'}
          </Button>
        </>
      ),
    },
  ];

  const columnsProduct: ProColumns<EnterpriseProductListItem>[] = [
    {
      title: '用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
      key: 'enterpriseName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'idNo',
      key: 'idNo',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeCode',
      key: 'productSecondTypeCode',
      initialValue: access.hasRole('enterpriseFinance') ? ['0103', '0106'] : [],
      valueEnum: SECONDARY_CLASSIFICATION_MAP,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: access.hasRole('enterpriseFinance') ? true : false,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '授信时间',
      dataIndex: 'creditEndTime',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.creditEndTime;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          startCreditEndTime: `${value[0].split(' ')[0]} 00:00:00`,
          endCreditEndTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '评级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      search: false,
    },
    {
      title: '授信额度',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
      search: false,
    },
    {
      title: '已用额度',
      dataIndex: 'usedAmount',
      key: 'usedAmount',
      search: false,
    },
    {
      title: '冻结额度',
      dataIndex: 'freezeAmount',
      key: 'freezeAmount',
      search: false,
    },
    {
      title: '剩余可用额度',
      dataIndex: 'currentAmount',
      key: 'currentAmount',
      search: false,
    },
    {
      title: '逾期金额',
      dataIndex: 'overdueAmount',
      key: 'overdueAmount',
      search: false,
    },
    {
      title: '保理服务日费率（%）',
      dataIndex: 'factoringServiceFee',
      key: 'factoringServiceFee',
      search: false,
    },
    {
      title: '溢缴款（元）',
      dataIndex: 'overpaymentsAmount',
      key: 'overpaymentsAmount',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'statusStr',
      key: 'statusStr',
      valueEnum: {
        1: { text: '激活', status: 'success' },
        0: { text: '冻结', status: 'error' },
        '-1': { text: '注销', status: 'Processing' },
      },
      fieldProps: {
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '还款日（账期天数）',
      dataIndex: 'billRepayDate',
      key: 'billRepayDate',
      search: false,
    },
    {
      title: '客户标签',
      dataIndex: 'userTag',
      key: 'userTag',
      search: false,
    },
    {
      title: '操作',
      key: 'option',
      width: 180,
      fixed: 'right',
      valueType: 'option',
      render: (text, row) => (
        <>
          <Link
            to={`/userMng/enterpriseMng/product-detail?subAccountNo=${row.subAccountNo}&userNo=${row.userNo}`}
          >
            查看详情
          </Link>
        </>
      ),
    },
  ];
  //用户页面
  const pageUser = (
    <ProTable<EnterpriseUserListItem>
      columns={columns}
      formRef={formRef}
      actionRef={actionRef}
      scroll={{ x: 'max-content' }}
      // rowKey={getUuid()}
      rowKey="userNo"
      search={{
        labelWidth: 120,
      }}
      form={{
        initialValues: {
          productSecondTypeCode: access.hasRole('enterpriseFinance') ? ['0103', '0106'] : [],
        },
      }}
      toolBarRender={() => {
        return [
          access.hasAccess('biz_download') && (
            <Button
              key="button"
              type="primary"
              onClick={() => {
                const { creditEndTime, ...data } = formRef?.current?.getFieldsValue();
                let newForm = { ...data };
                if (creditEndTime?.length) {
                  const startCreditEndTime = `${creditEndTime[0].format('YYYY-MM-DD')} 00:00:00`;
                  const endCreditEndTime = `${creditEndTime[1].format('YYYY-MM-DD')} 23:59:59`;
                  newForm = { ...data, startCreditEndTime, endCreditEndTime };
                }
                getExport(newForm);
              }}
            >
              导出
            </Button>
          ),
        ];
      }}
      request={(params) =>
        getEnterpriseUserList({
          ...params,
          productSecondTypeCode: access.hasRole('enterpriseFinance')
            ? ['0103', '0106']
            : params.productSecondTypeCode,
        })
      }
    />
  );

  //产品页面
  const pageProduct = (
    <ProTable<EnterpriseUserListItem>
      columns={columnsProduct}
      formRef={formRef}
      actionRef={actionRefProduct}
      scroll={{ x: 'max-content' }}
      // rowKey={getUuid()}
      rowKey="subAccountNo"
      search={{
        labelWidth: 120,
      }}
      form={{
        initialValues: {
          productSecondTypeCode: access.hasRole('enterpriseFinance') ? ['0103', '0106'] : [],
        },
      }}
      toolBarRender={() => {
        return [
          access.hasAccess('biz_download') && (
            <Button
              key="button"
              type="primary"
              loading={exportLoading}
              onClick={() => {
                const { creditEndTime, ...data } = formRef?.current?.getFieldsValue();
                let newForm = { ...data };
                if (creditEndTime?.length) {
                  const startCreditEndTime = `${creditEndTime[0].format('YYYY-MM-DD')} 00:00:00`;
                  const endCreditEndTime = `${creditEndTime[1].format('YYYY-MM-DD')} 23:59:59`;
                  newForm = { ...data, startCreditEndTime, endCreditEndTime };
                }
                getExportProduct(newForm);
              }}
            >
              导出
            </Button>
          ),
        ];
      }}
      request={(params) =>
        getEnterpriseProductList({
          ...params,
          productSecondTypeCode: access.hasRole('enterpriseFinance')
            ? ['0103', '0106']
            : params.productSecondTypeCode,
        })
      }
    />
  );

  return (
    <>
      <PageContainer
        extra={[
          <Switch
            key="switch"
            defaultChecked
            checked={checked}
            onChange={(v) => {
              setChecked(v);
              // console.log(v, actionRef, actionRefProduct);
              setTimeout(() => {
                actionRef?.current?.reloadAndRest();
                actionRefProduct?.current?.reloadAndRest();
              }, 0);
              // if (v) {
              //   actionRef?.current?.reloadAndRest();
              // } else {
              //   actionRefProduct?.current?.reloadAndRest();
              // }
            }}
            checkedChildren={'切换为用户维度视图'}
            unCheckedChildren={'切换为产品维度视图'}
          />,
        ]}
      >
        {checked ? pageProduct : pageUser}
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'userMng/enterpriseMng/com-list'}>
      <ComUserList />
    </KeepAlive>
  </>
);
