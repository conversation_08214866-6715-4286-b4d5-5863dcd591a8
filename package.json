{"name": "lala-finance-biz-web", "version": "1.0.0", "private": true, "description": "小圆金科业务系统", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build && cp dist/index.html dist/200.html", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:dev", "docker-hub:build": "docker build  -f Dockerfile.hub -t  ant-design-pro ./", "docker-prod:build": "docker-compose -f ./docker/docker-compose.yml build", "docker-prod:dev": "docker-compose -f ./docker/docker-compose.yml up", "docker:build": "docker-compose -f ./docker/docker-compose.dev.yml build", "docker:dev": "docker-compose -f ./docker/docker-compose.dev.yml up", "docker:push": "npm run docker-hub:build && npm run docker:tag && docker push antdesign/ant-design-pro", "docker:tag": "docker tag ant-design-pro antdesign/ant-design-pro", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "lint": "max setup && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "precommit": "lint-staged", "prettier": "prettier -c --write \"src/**/*\"", "site": "npm run fetch:blocks && npm run build", "start": "max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none max dev", "start:no-mock": "cross-env MOCK=none max dev", "start:no-ui": "cross-env UMI_UI=none max dev", "start:pre": "cross-env REACT_APP_ENV=pre max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none max dev", "pretest": "node ./tests/beforeTest", "test": "max test", "test:all": "node ./tests/run-tests.js", "test:component": "max test ./src/components", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.0.0", "@ant-design/pro-components": "^2.7.15", "@dnd-kit/core": "^6.3.1", "@hll/bme-caller-react": "^2.4.5-rc.2", "@hll/coupe-sdk": "^3.0.0", "@hll/feishu-at": "^0.0.25-rc.1", "@hll/finance-utils": "^0.0.2", "@sentry/react": "^5.29.2", "@umijs/max": "4.3.6", "@zip.js/zip.js": "^2.7.32", "ahooks": "^2.10.9", "antd": "^5.18.0", "axios": "^1.7.2", "bignumber.js": "^9.0.2", "browser-image-compression": "^2.0.2", "classnames": "^2.2.6", "dayjs": "^1.11.7", "devtools-detector": "^2.0.14", "html2canvas": "^1.4.1", "lodash": "^4.17.11", "lodash-es": "^4.17.21", "mathjs": "^10.0.0", "node-forge": "1.3.1", "omit.js": "^2.0.2", "qrcode.react": "^4.2.0", "qs": "^6.9.0", "ramda": "^0.27.1", "rc-field-form": "^2.2.1", "react": "17.0.2", "react-activation": "^0.12.4", "react-dom": "17.0.2", "react-helmet-async": "^1.0.4", "react-pdf": "5.2.0", "umi-plugin-keep-alive": "^0.0.1-beta.35", "umi-request": "^1.4.0", "use-merge-value": "^1.0.1", "virtuallist-antd": "^0.8.0-beta.1"}, "devDependencies": {"@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/hard-source-webpack-plugin": "^1.0.4", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/qs": "^6.5.3", "@types/react": "17.0.2", "@types/react-dom": "17.0.2", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "2.14.1", "@umijs/yorkie": "^2.0.3", "antd-dayjs-webpack-plugin": "^1.0.6", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-remove-console": "^6.9.4", "carlo": "^0.9.46", "compression-webpack-plugin": "^10.0.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.19.0", "express": "^4.17.1", "gh-pages": "^3.0.0", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "postcss-less": "^6.0.0", "prettier": "2.2.0", "pro-download": "1.0.1", "puppeteer-core": "^5.0.0", "typescript": "^4.0.3"}, "engines": {"node": ">=18.0.0"}}