import { request } from '@umijs/max';

// 1、获取验证码base64、uuid
export async function fetchCaptchaImageInfo() {
  return request('/bizadmin/auth/createGraphCaptcha', {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// 2、验证验证码
export async function verifyCaptcha(data: { uuid: string; code: string }) {
  return request('/bizadmin/auth/validateGraphCaptcha', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}
