import type { IbillInfo } from '../data';

export interface I_baseInfo {
  totalOverdueAmount?: string;
  registerPhone?: string;
  accountName?: string;
  certificateCode?: string;
}

export interface I_collectDetail {
  productClassicCode?: string;
  productSecondaryCode?: string;
  productName?: string;
  productCode?: string;
  orderNo?: string;
  orderAmount?: string;
  bankName?: string;
  bankCode?: string;
  bankPhoneNumber?: string;
  funderChannelName?: string;
  withdrawStatus?: number;
  carUniqueCode?: string;
  licenseCode?: string;
  returnedAmountDue?: string;
  repayDay?: string;
  remainingPrincipal?: string;
  overdueMaxLevel?: string;
  overdueLongestDays?: string;
  leaseChannelName?: string;
  licenseType?: string;
}

export enum IdimensionEnCode {
  TERM_BILL = 1, // 车辆纬度
  ORDER_TERM_BILL = 2, // 订单纬度
  SUBJECT_MATTER_BILL = 3, //总账
}

export interface I_submitCollectRelief {
  billInfoList: IbillInfo[];
  remissionList: [];
  repayAmount: number;
  attach: [];
  remark?: string;
  urgePersonId: string;
  overdueCaseNo: string;
  orderNo: string;
}
