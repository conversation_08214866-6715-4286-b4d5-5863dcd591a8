/*
 * @Author: your name
 * @Date: 2021-05-19 17:47:10
 * @LastEditTime: 2021-11-18 14:03:08
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/OtherInfo.tsx
 */
import type { MutableRefObject } from 'react';
import React, { useEffect, useImperativeHandle } from 'react';
import { Card, Form, Input } from 'antd';
import globalStyle from '@/global.less';

type OtherInfoProps = {
  isAddProduct?: boolean;
  otherInfo?: any;
  name?: string;
  cRef?: MutableRefObject<{ otherInfoSubmit: () => void } | undefined>;
  drawerStatus?: boolean;
};

const OtherInfo: React.FC<OtherInfoProps> = ({
  isAddProduct,
  otherInfo,
  name,
  cRef,
  drawerStatus = true,
}) => {
  // const { otherInfo } = props;
  const [form] = Form.useForm();
  useEffect(() => {
    // console.log(otherInfo);
    form.setFieldsValue(otherInfo);
    // form.validateFields();
  }, [otherInfo]);

  // useEffect(() => {
  //   form.validateFields();
  // }, [drawerStatus, otherInfo]);
  useImperativeHandle(cRef, () => ({
    // changeVal 就是暴露给父组件的方法
    // changeVal: (newVal) => {
    //   setVal(newVal);
    // }
    otherInfoSubmit: () => {
      form.submit();
    },
  }));
  return (
    <Card title="其他信息" className={globalStyle.mt30}>
      {/* {JSON.stringify(otherInfo)} */}
      {/* {otherInfo} */}
      <Form
        form={form}
        labelCol={{ span: 2 }}
        // initialValues={otherInfo}
        wrapperCol={{ span: 20 }}
        name={name}
      >
        <Form.Item label="产品介绍" name="productDesc" rules={[{ required: !drawerStatus }]}>
          <Input.TextArea placeholder="输入描述" rows={4} disabled={!isAddProduct} />
        </Form.Item>

        {/* <Form.Item name="status" label="状态" rules={[{ required: true }]}>
          <Radio.Group disabled={!isAddProduct}>
            <Radio value={1}>启用</Radio>
            <Radio value={0}>禁用</Radio>
          </Radio.Group>
        </Form.Item> */}
      </Form>
    </Card>
  );
};

export default React.memo(OtherInfo);
