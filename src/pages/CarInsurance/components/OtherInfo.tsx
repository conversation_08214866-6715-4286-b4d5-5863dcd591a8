import ImagePreview, { ImagePreviewInstance } from '@/components/ImagePreview';
import { getAuthHeaders } from '@/utils/auth';
import ProForm, { ProFormUploadButton } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import type { FormInstance } from 'antd';
import React, { useEffect, useRef } from 'react';
import { getBaseUploadAction } from '../utils';

type Props = {
  otherInfoForm: FormInstance;
};
const OtherInfo: React.FC<Props> = (props) => {
  const { otherInfoForm } = props;
  const { detailData, isEditable } = useModel('CarInsurance.carInsurance');
  const { otherInformationList } = detailData;
  useEffect(() => {
    otherInfoForm.setFieldsValue({
      otherInformationList: otherInformationList?.map((item, index) => ({
        uid: index,
        status: 'done',
        url: item.netWorkPath,
        name: item.fileName,
        shortOssUrl: item.shortOssUrl,
      })),
    });
  }, [detailData, otherInfoForm, otherInformationList]);
  console.log('isEditable112233', isEditable);
  //
  const previewRef = useRef<ImagePreviewInstance>();
  const handlePreview = async (file: any) => {
    const values = otherInfoForm?.getFieldsValue();
    console.log('values', values);
    console.log('file', file);
    previewRef.current?.previewFile({
      url: file?.url,
      urlList: values?.otherInformationList?.map((item: any) => item?.url),
    });
  };
  return (
    <>
      <h3>其他资料</h3>
      <ProForm
        layout="horizontal"
        form={otherInfoForm}
        submitter={{ render: () => null }}
        disabled={!isEditable}
      >
        <ProFormUploadButton
          name="otherInformationList"
          action={getBaseUploadAction('/base/oss/common/uploadfile')}
          fieldProps={{
            data: {
              attachment: false,
              acl: 'PUBLIC_READ',
              destPath: 'insurance/policy/vehicleLicense/',
            },
            headers: { ...getAuthHeaders() },
            name: 'file',

            multiple: true,
            onPreview: handlePreview,
            onChange: ({ fileList }) => {
              fileList.forEach((file) => {
                if (file?.status === 'done') {
                  const { response = {} } = file;
                  const { code, data = {} } = response;
                  if (code === 200) {
                    const { netWorkPath, filePath } = data;
                    file.url = netWorkPath;
                    (file as any).shortOssUrl = filePath;
                  }
                }
              });
            },
          }}
        />
      </ProForm>
      {/*  */}
      <ImagePreview ref={previewRef} />
    </>
  );
};

export default OtherInfo;
