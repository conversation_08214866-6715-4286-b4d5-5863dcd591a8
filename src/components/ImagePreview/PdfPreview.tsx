import { useMount, useUnmount } from 'ahooks';
import { forwardRef, useEffect, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import './index.less';

export type PdfPreviewProps = {
  fileUrl: string;
  scale?: number;
  onLoadFinish?: (totalPages: number) => void;
  onPageChange?: (pageNum: number) => void;
};

const defaultProps = {
  scale: 1,
};

let IO = null;

const PdfPreview = forwardRef((p: PdfPreviewProps, ref) => {
  const props = Object.assign(defaultProps, p);
  const [url, setUrl] = useState('');
  const [scale, setScale] = useState(props.scale);
  const [numPages, setNumPages] = useState(0);

  useEffect(() => {
    setScale(props.scale);
  }, [props.scale]);

  useEffect(() => {
    if (props.fileUrl) {
      setUrl(props.fileUrl);
    }
  }, [props.fileUrl]);

  const initScrollObserver = () => {
    const options = {
      root: ref?.current,
      rootMargin: '0px',
      threshold: 0.5,
    };
    IO = new IntersectionObserver((entries) => {
      // console.log(entries);
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          console.log(entry.target);
          const { pageNumber } = entry.target.dataset;
          console.log('page', pageNumber);
          props.onPageChange?.(pageNumber);
        }
      });
    }, options);
  };

  const onDocumentLoadSuccess = ({ numPages }: any) => {
    setNumPages(numPages);
    props.onLoadFinish?.(numPages);
    // 监听页数滚动
    initScrollObserver();
  };

  const onPageRenderSuccess = (index) => {
    const target = document.querySelector(`.image-perview-pdf-page-${index}`);
    if (IO) {
      IO.observe(target);
    }
  };

  useMount(() => {
    pdfjs.GlobalWorkerOptions.workerSrc =
      'https://static.huolala.cn/bme-static/335030/pdfjs-dist/2.5.207/es5/build/pdf.worker.js';
  });

  useUnmount(() => {
    if (IO) {
      IO.disconnect();
      IO = null;
    }
  });

  return (
    <Document
      inputRef={ref}
      className="image-perview-pdf"
      file={url}
      onLoadSuccess={onDocumentLoadSuccess}
      options={{
        cMapUrl: `https://static.huolala.cn/trade-static/463761/channel-h5-public/cmaps/`,
        cMapPacked: true,
      }}
      loading={<></>}
      renderMode="canvas"
    >
      {numPages &&
        new Array(numPages).fill(null).map((item, index) => {
          return (
            <Page
              className={`image-perview-pdf-page-${index}`}
              key={index}
              pageNumber={index + 1}
              loading={<></>}
              scale={scale}
              onRenderSuccess={() => {
                onPageRenderSuccess(index);
              }}
              onRenderError={(error) => {
                console.log('page render error', error);
              }}
              onLoadError={(error) => {
                console.log('page load error', error);
              }}
              renderAnnotationLayer={false}
              renderTextLayer={false}
            />
          );
        })}
    </Document>
  );
});

export default PdfPreview;
