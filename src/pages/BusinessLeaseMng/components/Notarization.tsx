// 赋强公证
import DividerTit from '@/components/DividerTit';
import globalStyle from '@/global.less';
import {
  chunkArray,
  getBlob,
  isChannelStoreUser,
  isExternalDesensitization,
  previewAS,
  saveAs,
} from '@/utils/utils';
import { useAccess } from '@umijs/max';
import { Empty, Steps, Table } from 'antd';
import React, { useEffect, useState } from 'react';

type NotarizationProps = {
  data?: any;
};

type ContractItem = {
  contractName: string;
  netWorkPath: string;
};

const Notarization = (props: NotarizationProps) => {
  const { data } = props;
  const access = useAccess();

  const [contractList, setContractList] = useState<ContractItem[]>([]);
  const [stepArr, setStepArr] = useState<any[]>([]);

  useEffect(() => {
    if (data) {
      if (data?.notarialCertificateFile) {
        const contract = [
          {
            contractName: '电子证书',
            netWorkPath: data?.notarialCertificateFile,
          },
        ];
        setContractList(contract);
      }
      if (data?.statusLog) {
        const formatList = data?.statusLog?.map((item) => {
          return {
            title: item.statusDesc,
            description: (
              <div>
                <div>{item.remark}</div>
                <div>{item.time}</div>
              </div>
            ),
            status: 'finish',
          };
        });
        // 分割成5个一组
        const stepList = chunkArray(formatList, 5);
        setStepArr(stepList);
      }
    }
  }, [data]);

  const download = (url: string, filename: string) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
  };

  const previewPDF = (url: string) => {
    getBlob(url, (blob: Blob) => {
      previewAS(blob);
    });
  };

  const columns = [
    {
      title: '合同名称',
      dataIndex: 'contractName',
      key: 'contractName',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      // width: 140,
      render: (_: React.ReactNode, record: ContractItem) =>
        !isExternalDesensitization(access) && (
          <>
            <a
              onClick={() => {
                if (record.netWorkPath) {
                  download(record.netWorkPath, `${record.contractName}.pdf`);
                }
              }}
            >
              下载
            </a>
            <a
              className={globalStyle.ml10}
              onClick={() => {
                if (record.netWorkPath) {
                  previewPDF(record.netWorkPath);
                }
              }}
            >
              预览
            </a>
          </>
        ),
    },
  ];

  return (
    <>
      <DividerTit title="公证节点">
        {stepArr.length > 0 ? (
          <div style={{ padding: 20 }}>
            {stepArr?.map((list, index) => {
              return <Steps key={index} items={list} />;
            })}
          </div>
        ) : (
          <Empty />
        )}
      </DividerTit>
      {!isChannelStoreUser(access) && (
        <DividerTit title="电子证书">
          <div style={{ padding: 20 }}>
            <Table
              style={{ width: 400 }}
              dataSource={contractList}
              columns={columns}
              pagination={false}
            />
          </div>
        </DividerTit>
      )}
    </>
  );
};

export default Notarization;
