import { PlusOutlined } from '@ant-design/icons';
import { Select, Tag, TimePicker } from 'antd';
import dayjs from 'dayjs';
import * as R from 'ramda';
import React, { useState } from 'react';
import './index.css';

function getTimestamp(value) {
  // return value.startOf("day").valueOf();
  // return new Date(value.format('YYYY-MM-DD HH:mm:ss')).getTime()
  return value.format('HH:mm');
}

export default function MultipleDatePicker({
  value: selectedDate = [],
  onChange,
  placeholder = '请选择',
  format = 'HH:mm',
  // format='YYYY-MM-DD',
  selectProps = {},
  timePickerProps = {},
}) {
  const [open, setOpen] = useState(false);

  const onValueChange = (date) => {
    const t = getTimestamp(date);
    const index = selectedDate.indexOf(t);
    const clone = R.clone(selectedDate);
    if (index === -1) {
      // clone.splice(index, 1);
      clone.push(t);
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    onChange && onChange(clone);
  };

  const dateRender = (currentDate) => {
    const isSelected = selectedDate.indexOf(getTimestamp(currentDate)) > -1;
    return (
      <div
        className="ant-picker-cell-inner"
        style={
          isSelected
            ? {
                position: 'relative',
                zIndex: 2,
                display: 'inlineBlock',
                width: '24px',
                height: '22px',
                lineHeight: '22px',
                backgroundColor: '#1677ff',
                color: '#fff',
                margin: 'auto',
                borderRadius: '2px',
                transition: 'background 0.3s, border 0.3s',
              }
            : {}
        }
      >
        {currentDate.date()}
      </div>
    );
  };

  const renderTag = ({ value, onClose }) => {
    const handleClose = () => {
      onClose();
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      onChange && onChange(selectedDate.filter((t) => t !== value));
    };
    return (
      <Tag onClose={handleClose} closable>
        {/* {dayjs(value).format(format)} */}
        {value}
      </Tag>
    );
  };

  return (
    <Select
      allowClear
      showArrow
      placeholder={placeholder}
      {...selectProps}
      mode="multiple"
      value={selectedDate}
      onClear={() => onChange && onChange([])}
      tagRender={renderTag}
      open={open}
      onFocus={() => setOpen(true)}
      onBlur={() => setOpen(false)}
      dropdownMatchSelectWidth={false}
      suffix={<PlusOutlined style={{ color: 'rgba(0,0,0,.45)' }} />}
      // inputIcon={}
      // suffixIcon={}
      // iconRender={<PlusOutlined />}
      popupClassName="multipleDropdownClassName"
      dropdownStyle={{ height: '270px', width: '300px', minWidth: '0' }}
      dropdownRender={() => {
        return (
          //   <Table
          //   columns={columns}
          //   // dataSource={data}
          //   pagination={false}
          //   rowKey={(record) => {
          //     return record.bankSerialNo + record.repayTime;
          //   }}
          // />
          <TimePicker
            {...timePickerProps}
            format={format}
            onChange={onValueChange}
            // value=""
            showToday={false}
            open
            dateRender={dateRender}
            // style={{ ...timePickerProps.style, visibility: "hidden" }}
            getPopupContainer={() =>
              document.getElementsByClassName('multipleDropdownClassName')[0]
            }
            defaultOpenValue={dayjs('00:00:00', 'HH:mm')}
          />
        );
      }}
    />
  );
}
