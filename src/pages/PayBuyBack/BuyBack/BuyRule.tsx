// 回购规则
import { LEASE_LOAN_CHANNEL_NAME } from '@/enums';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { <PERSON><PERSON>, Card, Switch } from 'antd';
import React, { useRef, useState } from 'react';
import { changePayConfigStatus, getBuyRuleList } from '../service';
import type { BuyRuleListItem } from '../types';
import AddBuyRuleModal from './AddBuyRuleModal';

enum STATUS {
  OPEN = 1,
  CLOSE = 2,
}
const BuyRule = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [switchLoading, setSwitchLoading] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<BuyRuleListItem>();
  const actionRef = useRef<ActionType>();
  const handleEdit = (record: BuyRuleListItem) => {
    setCurrentRecord({ ...record });
    setShowAddModal(true);
  };
  const onSwitchChange = async (record: BuyRuleListItem) => {
    const { configId, status } = record;
    const params = {
      id: configId,
      status: status === STATUS.OPEN ? STATUS.CLOSE : STATUS.OPEN,
    };
    setSwitchLoading(true);
    try {
      await changePayConfigStatus(params);
      actionRef.current?.reload();
      setSwitchLoading(false);
    } catch {
      setSwitchLoading(false);
    }
  };

  const columns = [
    {
      title: '放款资方',
      dataIndex: 'capital',
      valueEnum: LEASE_LOAN_CHANNEL_NAME,
    },
    {
      title: '合作业务线',
      dataIndex: 'productCode',
      valueEnum: {
        '0201': '融资租赁',
      },
    },
    {
      title: '回购场景',
      dataIndex: 'rule',
    },
    {
      title: '资方回购规则',
      dataIndex: 'capitalRule',
    },
    {
      title: '操作',
      render: (dom, record) => {
        return (
          <Button
            type="link"
            onClick={() => {
              handleEdit(record);
            }}
          >
            编辑
          </Button>
        );
      },
    },
    {
      title: '开关',
      render: (dom, record) => {
        return (
          <Switch
            checked={record?.status === STATUS.OPEN}
            onChange={() => {
              onSwitchChange(record);
            }}
            loading={switchLoading}
          />
        );
      },
    },
  ];

  return (
    <Card
      title="回购规则"
      extra={
        <Button
          type="primary"
          onClick={() => {
            setShowAddModal(true);
          }}
        >
          新增
        </Button>
      }
      style={{ marginBottom: 20 }}
    >
      <ProTable<BuyRuleListItem>
        search={false}
        actionRef={actionRef}
        columns={columns}
        request={(params) => getBuyRuleList(params)}
      />
      <AddBuyRuleModal
        open={showAddModal}
        data={currentRecord}
        onSuccess={() => {
          actionRef.current?.reload();
          setShowAddModal(false);
        }}
        onCancel={() => setShowAddModal(false)}
      />
    </Card>
  );
};

export default BuyRule;
