import { request } from '@umijs/max';
import { CancelReasonEnum, CancelStatusEnum, CarTypeEnum, CHANNEL_LEVEL } from './const';
// 模糊查询银行列表
export async function queryBank(subBranchName: string) {
  return request('/bizadmin/premium/company/queryBank', {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    params: {
      subBranchName,
    },
  });
}

export async function getChannelInfo({
  channelCode,
  channelLevel,
}: {
  channelLevel?: number;
  channelCode?: string;
  parentChannelCode?: string;
} = {}): Promise<any[]> {
  let params = {};
  //channelLevel存在则为渠道用户,否则默认为运营  运营角色需要load出所有渠道数据（一级，二级）
  if (channelLevel && channelLevel === CHANNEL_LEVEL.FIRST_CHANNEL) {
    params = {
      channelCode,
      levelList: [CHANNEL_LEVEL.FIRST_CHANNEL, CHANNEL_LEVEL.SECOND_CHANNEL],
    };
    //因为二级是禁用的，同时没有下级，该channelList返回[],默认选中的功能给下拉设置channelCode,会翻译失败，所以传父亲code查出下拉，让select选中
  } else if (channelLevel && channelLevel === CHANNEL_LEVEL.SECOND_CHANNEL) {
    params = { channelCode, levelList: [CHANNEL_LEVEL.SECOND_CHANNEL] };
  } else {
    params = { levelList: [CHANNEL_LEVEL.FIRST_CHANNEL, CHANNEL_LEVEL.SECOND_CHANNEL] };
  }
  const data = await request(`/bizadmin/channel/list`, {
    method: 'GET',
    params: {
      pageNumber: 1,
      pageSize: 10000,
      // status=2(表示有效+无效全部渠道)。只针对车险分期退保结项，其他不动
      status: 2,
      ...params,
    },
    ifTrimParams: true,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data?.data;
}

interface QueryParams {
  current: number;
  pageSize: number;
  plateNo?: string;
  vin?: string;
  cancellationReasonStatus?: CancelReasonEnum;
  actualPaymentTimeStart?: string;
  actualPaymentTimeEnd?: string;
  yirenxingRefundTimeStart?: string;
  yirenxingRefundTimeEnd?: string;
  userRefundTimeStart?: string;
  userRefundTimeEnd?: string;
  userName?: string;
  companyName?: string;
  orderStatus?: CancelStatusEnum;
  userChannelCode?: string;
  channelCode?: string;
}
// 查询退保单列表
export async function queryCancellationList(data: QueryParams) {
  return request('/bizadmin/policy/cancellation/query/order/list', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
    ifTrimParams: true,
  });
}

// 导出退保单列表
export async function exportCancellationList(data: QueryParams) {
  return request('/bizadmin/policy/cancellation/export/order/list', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
    ifTrimParams: true,
  });
}

// 查询退保单详情
export async function queryCancellationDetail(params: { bizNo: string }) {
  return request('/bizadmin/policy/cancellation/query/detail', {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    params,
  });
}
interface PreCheckParams {
  carPolicyType: CarTypeEnum;
  carPolicyNoList: string[];
  channelCode?: string;
  createdBy: string;
}
// 创建退保单前置校验
export async function createCancellationCheck(data: PreCheckParams) {
  return request('/bizadmin/policy/cancellation/create/check', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}

export interface CreateParams {
  channelCode?: string;
  createdBy?: string;
  cancellationInfoList: {
    vin: string;
    expectedCancellationTime: string;
    cancellationReasonStatus: string;
  };
}
// 创建退保单
export async function createCancellation(data: CreateParams) {
  return request('/bizadmin/policy/cancellation/create', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}

// 撤销退保单
export async function cancelCancellation(data: { bizNo: string; createdBy: string }) {
  return request('/bizadmin/policy/cancellation/cancelled', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}
// 提交-待保司受理
export async function uploadCancellation(data: any) {
  return request('/bizadmin/policy/cancellation/upload/cancellation', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}

// 提交-回款待确认
export async function uploadBankInfo(data: any) {
  return request('/bizadmin/policy/cancellation/upload/bank', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}
// 提交-分配退款
export async function cancellationAllocation(data: any) {
  return request('/bizadmin/policy/cancellation/allocation', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}
// 退款审核
export async function cancellationAudit(data: any) {
  return request('/bizadmin/policy/cancellation/audit', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}

// 退款失败-重新发起
export async function cancellationRelaunch(data: any) {
  return request('/bizadmin/policy/cancellation/relaunch', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}

//
interface UploadFileData {
  bizNo: string;
  fileList: any[];
}
// 修改退保单附件
export async function cancellationUploadFile(data: UploadFileData) {
  return request('/bizadmin/policy/cancellation/upload/file', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data,
  });
}
