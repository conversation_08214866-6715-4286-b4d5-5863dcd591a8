/*
 * @Date: 2023-07-17 11:16:23
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-07-17 16:08:02
 * @FilePath: /lala-finance-biz-web/src/pages/Reconciliation/service.ts
 * @Description:
 */

import { bizadminHeader } from '@/services/consts';
import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type {
  ReconciliationDetailList,
  ReconciliationDetailParams,
  ReconciliationRecordList,
  ReconciliationRecordParams,
} from './data';
// import { getUuid } from '@/utils/utils';

// const dataSource = Array(10000).fill(1).map(item => {
//   return {
//     reconciliationStatus: 1,
//     reconciliationFlowNo: "*************",
//     repayTime: "*************",
//     bankSerialNo: "*************",
//     receiveCardNo: "*************",
//     bankNumber: "*************",
//     actualRepaymentAmount: "*************",
//     accountBank: "*************",
//     bankName: "*************",
//     bills: "*************",
//     accountName: "*************",
//     accountNumber: "*************",
//     uid: getUuid(),
//     remitAmount: "*************",
//     createdAt: "*************",
//     receiveStatus: "*************",
//     receiveTime: "*************",
//   }
// })

export async function getDetailList(
  data: ReconciliationDetailParams,
): Promise<{ data: ReconciliationDetailList[]; total: number }> {
  return request(`/bizadmin/repayment/reconciliation/record`, {
    method: 'POST',
    data,
    headers,
    ifTrimParams: true,
  });

  console.log('getDetailListdatadata', data);

  // const { current = 1, pageSize = 20 } = data
  // const tableData: any = []

  // dataSource.forEach((item, index) => {
  //   if (index >= (current - 1)*pageSize && index < current * pageSize) {
  //     tableData.push(item)
  //   }
  // })

  // console.log("tableData",tableData)
  // return new Promise((res) => {
  //   setTimeout(() => {
  //     res({
  //       data: tableData,
  //       total: 10000
  //     })
  //   }, 1000)
  // })
}

export async function getDetailListExport(data: ReconciliationDetailParams) {
  return request(`/bizadmin/repayment/reconciliation/export`, {
    responseType: 'blob',
    method: 'POST',
    data,
    headers,
    getResponse: true,
  });
}

export async function getRecordList(
  data: ReconciliationRecordParams,
): Promise<ReconciliationRecordList> {
  return request(`/bizadmin/repayment/reconciliation/detail`, {
    method: 'POST',
    data,
    headers,
    ifTrimParams: true,
  });
}

//确认到账记录
export async function confirmReceiveBill(
  data: ReconciliationRecordParams,
): Promise<ReconciliationRecordList> {
  return request(`/bizadmin/repayment/reconciliation/confirm`, {
    method: 'POST',
    data,
    headers,
  });
}

//申请调账
export async function applySomeAdjustment(data: {
  applyNum: number | any;
  uids: string[];
  operator: string;
  applyAmount?: number;
}) {
  return request(`/bizadmin/repayment/reconciliation/apply`, {
    method: 'POST',
    data,
    headers,
  });
}

//申请全部到账
export async function applySomeAdjustmentAll() {
  return request(`/bizadmin/repayment/reconciliation/apply/all`, {
    method: 'POST',
    headers,
  });
}

// 调账明细导出-异步
export async function reconciliationExportAsync(params: any) {
  return request(`/bizadmin/repayment/reconciliation/exportAsync`, {
    headers: bizadminHeader,
    method: 'POST',
    data: params,
    ifTrimParams: true,
  });
}
