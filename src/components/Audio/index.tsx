import {
  DownloadOutlined,
  LoadingOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import React, { useState, useRef, useEffect } from 'react';
import { useThrottleFn } from 'ahooks';
import { message } from 'antd';
import styles from './index.module.less';

interface IAudioProps {
  src?: string;
  downloadShow?: boolean;
  downloadHandle?: () => Promise<any>;
  controls?: boolean;
}
const Audio: React.FC<IAudioProps> = (props) => {
  // props
  const { src, downloadShow = false, controls = false, downloadHandle = undefined } = props;

  // state
  const [audioStatus, setAudioStatus] = useState('pause'); // 音频播放状态
  const [maxTime, setMaxTime] = useState(0); // 音频时长
  const [curTime, setCurTime] = useState(0); // 当前播放进度
  const [timeUpdatePercent, setTimeUpdatePercent] = useState(''); // 当前播放进度百分比
  const audioRef = useRef<HTMLAudioElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(false); // 是否下载中
  const [audioUri, setAudioUri] = useState(''); // 音频资源地址

  // watch
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    pause();
  }, [src]);

  // mounted
  useEffect(() => {
    if (src) {
      setAudioUri(src);
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      handleLoadedmetadata();
    } else if (downloadShow) {
      if (downloadHandle === undefined) {
        throw new Error('AudioComponent: downloadShow 为 true, downloadHandle 必传');
      }
    } else {
      throw new Error('AudioComponent: src 必传');
    }
  }, []);

  // methods

  // 音频结束时，暂停播放
  const handleEnded = () => {
    setAudioStatus('pause');
    setTimeUpdatePercent('100%');
  };
  // 音频播放时，进度条跟踪音频进度
  const handleTimeUpdate = () => {
    if (audioRef.current) {
      const { currentTime } = audioRef.current;
      setCurTime(currentTime);
      setTimeUpdatePercent(`${((currentTime / maxTime) * 100).toFixed(0)}%`);
    }
  };
  // 音频下载结束后，获取音频总时长
  const handleLoadedmetadata = () => {
    if (audioRef.current && audioRef.current.duration > 0) {
      setMaxTime(audioRef.current.duration);
    }
  };
  // 播放
  const play = () => {
    if (maxTime === 0) return;
    if (audioRef.current) {
      audioRef.current.play();
      setAudioStatus('play');
    }
  };
  // 暂停
  const pause = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setAudioStatus('pause');
    }
  };
  // 格式化秒为分
  const formatTime = (second: number) => {
    // eslint-disable-next-line radix
    return [parseInt(String((second / 60) % 60)), parseInt(String(second % 60))]
      .join(':')
      .replace(/\b(\d)\b/g, '0$1');
  };
  // 下载音频文件
  const { run: downloadClick } = useThrottleFn(
    async (fn: Function | undefined) => {
      if (fn !== undefined) {
        setLoading(true);
        try {
          const res = await fn();

          if (res.success) {
            const uri = res.data;
            setAudioUri(uri);
            handleLoadedmetadata();
            message.success('下载成功，点击开始按钮播放');
          } else {
            message.error('下载失败');
          }
        } finally {
          setLoading(false);
        }
      }
    },
    { wait: 3000, trailing: false },
  );
  // 播放跳转
  const handleGoto = (ev: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    ev.persist();
    const { pageX: end } = ev;

    if (progressRef.current && audioRef.current) {
      const start = progressRef.current.getBoundingClientRect().left;
      const percent = (end - start) / progressRef.current.offsetWidth;
      const theCurTime = percent * maxTime;
      setTimeUpdatePercent(`${(percent * 100).toFixed(0)}%`);

      audioRef.current.currentTime = theCurTime;
      play();
    }
  };

  return (
    <div className="audio-container">
      <div className={styles.audioBox}>
        <div className={styles['audioBox-button']}>
          {audioStatus === 'pause' && (
            <PlayCircleOutlined
              className={`el-icon-video-play ${styles['audioBox-button-icon']}`}
              onClick={play}
            />
          )}
          {audioStatus === 'play' && (
            <PauseCircleOutlined
              className={`el-icon-video-pause ${styles['audioBox-button-icon']}`}
              onClick={pause}
            />
          )}
        </div>
        {controls && (
          <div className={styles['audioBox-progress']}>
            <div className={styles['audioBox-progress-time']}>
              {formatTime(curTime)}/{formatTime(maxTime)}
            </div>
            <div
              ref={progressRef}
              className={styles['audioBox-progress-bar']}
              onClick={(ev) => handleGoto(ev)}
            >
              <div
                className={styles['audioBox-progress-now']}
                style={{ width: timeUpdatePercent }}
              />
            </div>
          </div>
        )}
        {downloadShow && (
          <div
            className={styles['audioBox-download']}
            onClick={() => downloadClick(downloadHandle)}
          >
            {loading && <LoadingOutlined />}
            {/* TODO: 临时屏蔽，后续会放开 */}
            {/* {!audioUri && ( */}
            <DownloadOutlined className={`el-icon-download ${styles['audioBox-download-icon']}`} />
            {/* )} */}
          </div>
        )}
      </div>
      <audio
        ref={audioRef}
        src={audioUri}
        onEnded={handleEnded}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedmetadata}
      >
        <track kind="captions" />
      </audio>
    </div>
  );
};

export default Audio;
