import { Modal } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

interface ConfirmModalProps {
  key?: string;
  mode?: 'normal' | 'diff';
  diffData?: Record<string, { old: string; new: string }> | undefined;
  data?: Record<string, string> | (() => Promise<Record<string, string>>) | undefined;
  onOk: () => void;
  onCancel?: () => void;
  children?: React.ReactNode;
}
/**
 * 弹窗确认组件
 * @param mode 模式 normal|diff, 默认normal,diff时将需要依赖diffData
 * @param diffData 对比数据 当mode为diff时需要依赖, 或者通过ref.current.setDiffData设置, diffData value格式为{old: string, new: string}
 * @param data 数据 存在时可不填description, 或者通过ref.current.setConfirmData设置
 * @returns Modal
 */
const ConfirmModal = forwardRef((props: ConfirmModalProps, ref) => {
  const { data, key, diffData: oldData, mode, onOk, onCancel, children, ...rest } = props;

  const [open, setOpen] = useState(false);

  const [confirmData, setConfirmData] = useState<Record<string, string>>({});
  const [diffData, setDiffData] = useState<Record<string, string>>({});

  // 获取数据
  useEffect(() => {
    if (!data) return;
    if (typeof data === 'function') {
      data().then((res) => {
        setConfirmData(res);
      });
    } else {
      setConfirmData(data ?? {});
    }
  }, [data]);

  // 获取旧数据
  useEffect(() => {
    if (!oldData) return;
    setDiffData(oldData);
  }, [oldData]);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    show: () => {
      setOpen(true);
    },
    hide: () => {
      setOpen(false);
    },
    setOpen: (state: boolean) => {
      setOpen(state);
    },
    setConfirmData: (newData: Record<string, string>) => {
      setConfirmData(newData);
    },
    setDiffData: (newData: Record<string, string>) => {
      setDiffData(newData);
    },
  }));

  // 获取节点
  const isFnReturnNode = (node: any): React.ReactElement | string => {
    return typeof node === 'function' ? node() : node;
  };

  return (
    <Modal
      zIndex={10000}
      key={key || 'confirmModal'}
      title="变更确认"
      open={open}
      onOk={onOk}
      centered
      closeIcon={null}
      onCancel={onCancel || (() => setOpen(false))}
      cancelText="返回修改"
      okButtonProps={{
        type: 'primary',
        variant: 'solid',
        color: 'danger',
      }}
      cancelButtonProps={{
        type: 'primary',
        variant: 'solid',
      }}
      {...rest}
    >
      {children ?? (
        <div style={{ padding: '12px 6px', marginLeft: '-6px', overflow: 'hidden', width: '100%' }}>
          {Object.entries(confirmData ?? {}).map(
            ([k, v]: [string, string | React.ReactElement | (() => React.ReactElement)]) => (
              <div
                key={k}
                style={{ padding: '8px 0', display: 'flex', alignItems: 'center', width: '100%' }}
              >
                <div style={{ minWidth: '100px', color: '#959593' }}>{k}</div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ color: `${mode !== 'diff' ? '#ff0923' : ''}` }}>
                    {mode === 'diff' ? (
                      <span>{isFnReturnNode(diffData?.[k]?.old)}</span>
                    ) : (
                      isFnReturnNode(v)
                    )}
                  </div>
                  {mode === 'diff' &&
                    // 对比新旧数据
                    diffData?.[k]?.old !== diffData?.[k]?.new && (
                      <div style={{ color: '#ff0923', marginLeft: 8 }}>
                        -&gt; {isFnReturnNode(diffData?.[k]?.new)}
                      </div>
                    )}
                </div>
              </div>
            ),
          )}
        </div>
      )}
    </Modal>
  );
});

export default React.memo(ConfirmModal);
