/*
 * @Author: your name
 * @Date: 2021-01-18 16:33:12
 * @LastEditTime: 2023-03-28 17:19:52
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/components/Contract.tsx
 */
import globalStyle from '@/global.less';
import { getBlob, isExternalNetwork, previewAS, saveAs } from '@/utils/utils';
import { useRequest } from '@umijs/max';
import { Card, Table } from 'antd';
import React from 'react';
import { contractList } from '../service';

interface ContractProps {
  orderNo?: string;
  dataList?: any[];
  billNo?: string;
  userName?: string;
}
interface ContractItems {
  fileDesc: string;
  filePath: string;
  netWorkPath: string;
}

const Contract: React.FC<ContractProps> = (props) => {
  const { data } = useRequest(() => {
    return props.orderNo ? contractList(props.orderNo) : Promise.resolve();
  });
  // const { billNo, userName } = props;

  const download = (url: string, filename: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
    // });
  };
  const previewPDF = (url: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      previewAS(blob);
    });
    // });
  };

  const columns = [
    {
      title: '合同名称',
      dataIndex: 'fileDesc',
      key: 'fileDesc',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      // width: 140,
      render: (_: React.ReactNode, record: ContractItems) =>
        !isExternalNetwork() && (
          <>
            {record.netWorkPath && (
              <>
                <a
                  onClick={() => {
                    download(record.netWorkPath, `${record.fileDesc}${props.orderNo}.pdf`);
                  }}
                >
                  下载
                </a>
                <a
                  className={globalStyle.ml10}
                  onClick={() => {
                    previewPDF(record.netWorkPath);
                  }}
                >
                  预览
                </a>
              </>
            )}
          </>
        ),
    },
  ];
  const arr = data ? [{ ...data }] : props?.dataList || [];

  return (
    <Card title="合同" className={globalStyle.mt30}>
      <Table
        rowKey={(record) => {
          return record.filePath;
        }}
        style={{ width: 400 }}
        dataSource={arr}
        columns={columns}
        pagination={false}
      />
    </Card>
  );
};

export default Contract;
