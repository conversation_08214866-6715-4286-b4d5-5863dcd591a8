/**
 * 还款登记
 */
import { COST_TYPE } from '@/pages/BusinessCashMng/const';
import { BigNumber } from 'bignumber.js';
export const repaymentRegistColumns = [
  {
    title: '还款流水号',
    dataIndex: 'recordingNo',
    key: 'recordingNo',
  },
  {
    title: '实际还款总金额',
    dataIndex: 'actualRepaymentAmount',
    key: 'actualRepaymentAmount',
  },
  {
    title: '实际还款本金',
    dataIndex: 'principal',
    key: 'principal',
  },
  {
    title: '实际还款利息',
    dataIndex: 'interest',
    key: 'interest',
  },
  {
    title: '实际还款罚息',
    dataIndex: 'penaltyInterest',
    key: 'penaltyInterest',
  },
  {
    title: '实际还款费用',
    dataIndex: 'cost',
    key: 'cost',
  },
  {
    title: '还款方式',
    dataIndex: 'repayType',
    key: 'repayType',
  },
  {
    title: '还款时间',
    dataIndex: 'repayTime',
    key: 'repayTime',
  },
];

export const remissionColumns = [
  {
    title: '减免费项',
    dataIndex: 'costType',
    render: (_: React.ReactNode, record: any) => {
      return COST_TYPE[record?.costType] || '-';
    },
  },
  {
    title: '减免金额/元',
    dataIndex: 'remissionAmount',
    render: (_: React.ReactNode, record: any) => {
      return new BigNumber(record?.remissionAmount).div(100).toFixed(2) || '-';
    },
  },
  {
    title: '车牌号',
    dataIndex: 'licensePlateNo',
  },
  {
    title: '车架号',
    dataIndex: 'vin',
  },
];
