import type { ProColumns } from '@ant-design/pro-table';
import { history } from '@umijs/max';
import React from 'react';
import type { IlistItem } from '../types';
const columns: ProColumns<IlistItem>[] = [
  {
    dataIndex: 'orderNo',
    title: '订单号',
    fixed: true,
    render(text) {
      return (
        <a
          onClick={() => {
            history.push(`/businessMng/postLoanMng/car-insurance/detail?orderNo=${text}`);
          }}
        >
          {text}
        </a>
      );
    },
  },
  {
    dataIndex: 'accountName',
    title: '用户名称',
  },
  {
    dataIndex: 'productName',
    title: '产品名称',
    search: false,
  },
  {
    dataIndex: 'repayMode',
    title: '还款方式',
    valueType: 'select',
    valueEnum: {
      1: '一次本息',
      2: '等额本息',
    },

    search: false,
  },
  {
    dataIndex: 'repayTerm',
    title: '还款期限',
    search: false,
  },
  {
    dataIndex: 'annualInterestRate',
    title: '借款利率',
    search: false,
  },
  {
    dataIndex: 'lendingTime',
    title: '放款日期',
    search: false,
  },
  {
    dataIndex: 'createTime',
    title: '创建日期',
    render(text, record) {
      return record?.createTime;
    },
    valueType: 'dateRange',
    search: {
      transform(value) {
        return {
          createStartTime: value[0],
          createEndTime: value[1],
        };
      },
    },
  },
  {
    dataIndex: 'lastRepayTime',
    title: '应结清日期',
    search: false,
  },
  {
    dataIndex: 'lastClearDate',
    title: '实际结清日期',
    valueType: 'dateRange',
    render(_, record) {
      return record?.lastClearDate || '-';
    },
    search: {
      transform(value) {
        return {
          clearStartTime: value[0],
          clearEndTime: value[1],
        };
      },
    },
  },
  {
    dataIndex: 'amountDue',
    title: '应还总额',
    search: false,
  },
  {
    dataIndex: 'principal',
    title: '应还本金',
    search: false,
  },
  {
    dataIndex: 'interest',
    title: '应还利息',
    search: false,
  },
  {
    dataIndex: 'overdueInterest',
    title: '应还逾期罚息',
    search: false,
  },
  {
    dataIndex: 'overdueLatePaymentFee',
    title: '应还逾期滞纳',
    search: false,
  },
  {
    dataIndex: 'advanceSettleLiquidatedDamages',
    title: '应还结清违约金',
    search: false,
  },
  {
    dataIndex: 'amountPaid',
    title: '已还总额',
    search: false,
  },
  {
    dataIndex: 'principalPaid',
    title: '已还本金',
    search: false,
  },
  {
    dataIndex: 'interestPaid',
    title: '已还利息',
    search: false,
  },
  {
    dataIndex: 'overdueInterestPaid',
    title: '已还逾期罚息',
    search: false,
  },
  {
    dataIndex: 'overdueLatePaymentFeePaid',
    title: '已还逾期滞纳',
    search: false,
  },
  {
    dataIndex: 'advanceSettleLiquidatedDamagesPaid',
    title: '已经还结清违约金',
    search: false,
  },
  {
    dataIndex: 'remainingAmountDue',
    title: '剩余未还',
    search: false,
  },
  {
    dataIndex: 'remainingPrincipal',
    title: '剩余本金',
    search: false,
  },
  {
    dataIndex: 'remainingInterest',
    title: '剩余利息',
    search: false,
  },
  {
    dataIndex: 'remainingOverdueInterest',
    title: '剩余逾期罚息',
    search: false,
  },
  {
    dataIndex: 'remainingOverdueLatePaymentFee',
    title: '剩余逾期滞纳',
    search: false,
  },
  {
    dataIndex: 'remissionAmount',
    title: '逾期减免',
    search: false,
  },
  {
    dataIndex: 'cancelProfitAmount',
    title: '退保盈余',
    search: false,
  },
  {
    dataIndex: 'overDueDay',
    title: '逾期天数',
    search: false,
  },
  {
    dataIndex: 'licensePlateNo',
    title: '车牌号',
    hideInTable: true,
  },
  {
    dataIndex: 'vin',
    title: '车架号',
    hideInTable: true,
  },
];
export default columns;
