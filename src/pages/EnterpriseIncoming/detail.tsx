import { DividerTit, StepProgress } from '@/components';
import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { ClockCircleOutlined, ExclamationCircleOutlined, MessageOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import { ModalForm } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable from '@ant-design/pro-table';
import { Button, Col, message, Modal, Row, Tooltip } from 'antd';
import React from 'react';
import { history, useRequest } from 'umi';
import {
  getInputDetailBsae,
  getInputDetailRecord,
  getMotherAndSon,
  getProcessStatus,
  logoutDark,
  unRegistration,
} from './service';

const EnterpriseMngDetail: React.FC = () => {
  const item = {
    color: '#5e6d82',
    fontSize: '14px',
    fontWeight: 500,
    padding: '10px 0',
  };

  const stateMap = {
    // '0': '用户创建成功',
    // '10': '待风控',
    // '11': '风控中',
    // '12': '企业三要素认证成功',
    // '14': '授权人三要素认证成功',
    // '16': '银行卡信息校验成功 ',
    // '30': '待初审',
    // '31': '待终审',
    // '40': '审核通过',
    // '41': '审核拒绝',
    // '42': '撤销',
    '0': '用户创建成功',
    '11': '风控中',
    '30': '待初审',
    '31': '待终审',
    '40': '审核通过',
    '41': '审核拒绝',
    '42': '撤销',
    '44': '签约成功',
    '12': '企业三要素认证成功',
    '14': '授权人三要素认证成功',
    '16': '银行卡信息校验成功',
    '-2': '注销',
  };

  enum STATUS_NUMBER {
    enterpriseVerified = 12, // 企业认证
    listenorVerified = 14, //
    bankAccountVerified = 16, // 银行卡信息比对成功
    remittanceVeritrified = 17, // 银行卡随机金额校验成功，认证已完成
  }

  const { orderNo } = history.location.query as any;

  // 获取进件详情---基础信息
  const { data: baseData, loading: baseLoading } = useRequest(() => {
    return getInputDetailBsae(orderNo);
  });

  // 获取进件详情---风控进件记录
  const { data: recordData, loading: recordLoading, run: refresh } = useRequest(() => {
    return getInputDetailRecord(orderNo);
  });

  function renderDarkLogOff() {
    const { secondClassificationCode: productSecondCode, userNo } = baseData || {};
    let jsx;
    if (recordData?.creditStatus === -2) {
      jsx = (
        <Row gutter={24}>
          <Col className="gutter-row" style={item} span={8}>
            <div>注销人：{recordData?.unRegistrationOperator}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>注销时间：{recordData?.unRegistrationTime}</div>
          </Col>
        </Row>
      );
    } else {
      jsx = (
        <ModalForm
          title="进件注销"
          width={1000}
          trigger={<Button type="primary">注销</Button>}
          onFinish={async () => {
            try {
              await logoutDark({ productSecondCode, userNo });
              message.success('注销成功');
              refresh();
              return true;
            } catch (error) {
              // message.error('注销失败');
              return false;
            }
          }}
        >
          <ProTable
            indentSize={0}
            search={false}
            columns={[
              { title: '企业属性', dataIndex: 'attribute', width: 100 },
              { title: '企业用户ID', dataIndex: 'userNo' },
              { title: '社会统一信用代码', dataIndex: 'idNo' },
              { title: '企业名称', dataIndex: 'name' },
              { title: '总额度（元）', dataIndex: 'totalAmount' },
              { title: '剩余可用额度（元）', dataIndex: 'currentAmount' },
              { title: '授信时间', dataIndex: 'createTime' },
            ]}
            rowSelection={{}}
            request={async () => {
              const data = await getMotherAndSon({ productSecondCode, userNo });
              if (!data?.child?.length) {
                data.attribute = '-';
              } else {
                data.attribute = '母企业';
                // eslint-disable-next-line @typescript-eslint/no-shadow
                data?.child?.forEach((item: any) => {
                  item.attribute = '子企业';
                });
              }

              data.children = data.child;
              return {
                total: 1,
                data: [data],
              };
            }}
          />
        </ModalForm>
      );
    }

    return <DividerTit title="进件注销">{jsx}</DividerTit>;
  }
  const { data: processStatus } = useRequest(() => {
    return getProcessStatus(orderNo);
  });

  const oldDom = (
    <>
      {processStatus?.length ? (
        <StepProgress
          stepStatus={processStatus?.map(
            (itemTemp: {
              time: string;
              statusDesc: string;
              result: number;
              status: number;
              subDesc: Record<string, any>;
            }) => {
              let descTempTool: React.ReactNode = '';

              const { statusDesc, time, result, status, subDesc } = itemTemp;

              switch (status) {
                case STATUS_NUMBER.enterpriseVerified:
                  descTempTool = <div>法人姓名:{subDesc?.name}</div>;
                  break;
                case STATUS_NUMBER.listenorVerified:
                  descTempTool = (
                    <>
                      <div>授权人姓名:{subDesc?.name}</div>
                      <div>身份证号码:{subDesc?.idCardNo}</div>
                      <div> 手机号码:{subDesc?.phone} </div>
                      <div> 邮箱:{subDesc?.email}</div>
                    </>
                  );
                  break;
                case STATUS_NUMBER.bankAccountVerified:
                  descTempTool = (
                    <div style={{ display: 'inlineBlock' }}>
                      <div style={{ display: 'inlineBlock' }}>
                        对公银行账户:{subDesc?.bankNumber}
                      </div>
                      <div>开户行:{subDesc?.bank}</div>
                      <div>开户行支行:{subDesc?.subBankName}</div>
                    </div>
                  );
                  break;
                case STATUS_NUMBER.remittanceVeritrified:
                  descTempTool = <div>填写金额（元）:{subDesc?.amount}</div>;
                  break;
              }
              const mapIcon = {
                0: '',
                1: <ExclamationCircleOutlined style={{ color: 'red', fontSize: 32 }} />,
                2: <ClockCircleOutlined style={{ color: 'grey', fontSize: 32 }} />,
              };
              return {
                bol: false,
                desc: (
                  <>
                    {statusDesc}
                    {descTempTool && (
                      <Tooltip title={descTempTool} placement="bottom">
                        <MessageOutlined style={{ color: 'grey', fontSize: 14 }} />
                      </Tooltip>
                    )}
                  </>
                ),
                localDate: time,
                icon: mapIcon[result],
              };
            },
          )}
        />
      ) : null}
      <ProCard
        title="基础信息"
        bordered
        headerBordered
        direction="column"
        loading={baseLoading}
        gutter={[0, 24]}
        style={{ marginTop: 8, paddingLeft: 30 }}
      >
        <Row gutter={24}>
          <Col className="gutter-row" style={item} span={8}>
            <div>进件流水号：{baseData?.orderNo}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>用户ID：{baseData?.userNo}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>企业名称：{baseData?.enterpriseName}</div>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col className="gutter-row" style={item} span={8}>
            <div>产品一级分类：{baseData?.classification}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>产品二级分类：{baseData?.secondClassification}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>渠道商户：{baseData?.channel}</div>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col className="gutter-row" style={item} span={8}>
            <div>统一社会信用代码：{baseData?.orgCode}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>申请额度：{baseData?.applyAmount}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>申请时间：{baseData?.applyTime}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>客户标签：{baseData?.userTag}</div>
          </Col>
        </Row>
      </ProCard>

      <ProCard
        title="风控进件记录"
        bordered
        headerBordered
        direction="column"
        loading={recordLoading}
        gutter={[0, 24]}
        style={{ marginTop: 8, paddingLeft: 30 }}
      >
        <Row gutter={24}>
          <Col className="gutter-row" style={item} span={8}>
            <div>风控进件状态：{stateMap[recordData?.creditStatus]}</div>
          </Col>
          {recordData?.creditStatus === 41 && (
            <Col className="gutter-row" style={item} span={8}>
              <div>拒绝原因：{recordData?.rejectReason}</div>
            </Col>
          )}
          <Col className="gutter-row" style={item} span={8}>
            <div>授信额度：{recordData?.creditAmount}</div>
          </Col>
          <Col className="gutter-row" style={item} span={8}>
            <div>授信时间：{recordData?.creditTime}</div>
          </Col>
        </Row>

        {/* 明保注销功能 */}
        {baseData?.secondClassificationCode === '0101' ? (
          <DividerTit title="进件注销">
            {recordData?.creditStatus !== 41 &&
              recordData?.creditStatus !== 42 &&
              recordData?.creditStatus !== -2 && ( // 只有明保才有注销功能
                <Button
                  className={globalStyle.ml10}
                  onClick={() => {
                    Modal.confirm({
                      title: '进件注销',
                      icon: <ExclamationCircleOutlined />,
                      content: '注销后，该用户将不可使用账期支付功能；',
                      okText: '确认注销',
                      cancelText: '取消',
                      onOk() {
                        return unRegistration({
                          enterpriseId: baseData?.userNo,
                          productSecondTypeCode: baseData?.secondClassificationCode,
                          orderNo: baseData?.orderNo,
                        }).then(() => {
                          refresh();
                        });
                      },
                    });
                  }}
                >
                  注销
                </Button>
              )}
            {recordData?.creditStatus === -2 && (
              <Row gutter={24}>
                <Col className="gutter-row" style={item} span={8}>
                  <div>注销人：{recordData?.unRegistrationOperator}</div>
                </Col>
                <Col className="gutter-row" style={item} span={8}>
                  <div>注销时间：{recordData?.unRegistrationTime}</div>
                </Col>
              </Row>
            )}
          </DividerTit>
        ) : (
          renderDarkLogOff() //         {/* 暗保注销功能 有一定的权限配置*/}
        )}
      </ProCard>
    </>
  );

  return (
    <>
      <HeaderTab />
      <PageContainer>{oldDom}</PageContainer>
    </>
  );
};

export default EnterpriseMngDetail;
