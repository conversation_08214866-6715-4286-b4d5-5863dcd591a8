import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl, getBlob, saveAs } from '@/utils/utils';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import type { STEP_FILE_TYPE } from '../../type';

import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InboxOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import type { FormInstance, UploadProps } from 'antd';
import { Col, Divider, Form, message, Progress, Radio, Row, Tag, Tooltip, Upload } from 'antd';
import { useCarInsuranceBulkOcrContext } from '../../context/CarInsuranceBulkOcrContext';
import { getFileNameFromUrl } from '../../utils';
import './index.less';

const { Dragger } = Upload;

interface FileItem {
  name: string;
  url: string;
  // type: string;
  status?: 'uploading' | 'done' | 'error';
  percent?: number;
  uid?: string;
  id?: string;
  filePath?: string;
  ossUrl?: string;
  isFromContext?: boolean; // 标识是否来自 context 的回显文件
}

interface CarOcrContentProps {
  name: string;
  otdName: string;
  form: FormInstance;
  fileType: STEP_FILE_TYPE;
  hasOption?: boolean;
  variant: 'policy' | 'purchase' | 'credit' | 'license';
  setLoading: (val: boolean) => void;
  onUploadStatusChange?: (hasUploading: boolean) => void; // 新增：通知父组件上传状态
  onErrorStatusChange?: (hasError: boolean) => void; // 新增：通知父组件错误状态
}

const variantTitleMap = {
  policy: {
    title: '投保单',
    description: '是否团单（团单车辆将共用投保单文件）',
  },
  purchase: {
    title: '购车发票',
    description: '如果没有该类型文件，可以点击下方【下一步】跳过',
  },
  credit: {
    title: '合格证',
    description: '如果没有该类型文件，可以点击下方【下一步】跳过',
  },
  license: {
    title: '行驶证',
    description: '如果没有该类型文件，可以点击下方【下一步】跳过',
  },
};

const CarOcrContent: React.FC<CarOcrContentProps> = ({
  form,
  name,
  otdName,
  fileType,
  hasOption = false,
  variant = 'policy',
  setLoading,
  onUploadStatusChange,
  // onFileListChange,
  onErrorStatusChange,
}) => {
  const {
    batchOcrData,
    batchFileDTO,
    dispatchBatchFiles,
    updateBatchOcrData,
  } = useCarInsuranceBulkOcrContext();

  const [radio, setRadio] = useState<number | string>(batchOcrData.groupFlag || 2);

  // 统一的文件状态管理 - 合并原来的 files 和 uploadFileList
  const [fileState, setFileState] = useState<{
    files: FileItem[];
    uploadFileList: any[];
    uploadingFiles: Set<string>;
  }>({
    files: [],
    uploadFileList: [],
    uploadingFiles: new Set(),
  });

  const previewRef = useRef<ImagePreviewInstance>(null);

  // 存储上传请求的引用，用于取消上传
  const uploadRequestsRef = useRef<Map<string, XMLHttpRequest>>(new Map());

  // 防抖更新 context 的引用
  const updateContextTimeoutRef = useRef<NodeJS.Timeout>();

  // 防抖更新 context 的函数
  const debouncedUpdateContext = useCallback(
    (files: FileItem[]) => {
      if (updateContextTimeoutRef.current) {
        clearTimeout(updateContextTimeoutRef.current);
      }

      updateContextTimeoutRef.current = setTimeout(() => {
        dispatchBatchFiles({
          type: 'add_' + variant,
          payload: {
            fileList: files,
          },
        });
      }, 100); // 100ms 防抖
    },
    [dispatchBatchFiles, variant],
  );

  // 统一的文件状态更新函数
  const updateFileState = useCallback(
    (updater: (prevState: typeof fileState) => typeof fileState) => {
      setFileState((prevState) => {
        const newState = updater(prevState);

        // 自动同步到 context（防抖）
        if (newState.files !== prevState.files) {
          debouncedUpdateContext(newState.files);
        }

        return newState;
      });
    },
    [debouncedUpdateContext],
  );

  // 删除文件的辅助函数
  const removeFile = useCallback(
    (fileUid: string) => {
      updateFileState((prevState) => {
        const newUploadingFiles = new Set(prevState.uploadingFiles);
        if (fileUid) {
          newUploadingFiles.delete(fileUid);
        }

        // 过滤掉要删除的文件
        const filteredFiles = prevState.files.filter((item) => (item.uid || item.url) !== fileUid);
        const filteredUploadFileList = prevState.uploadFileList.filter(
          (item) => (item.uid || item.url) !== fileUid,
        );

        return {
          ...prevState,
          files: filteredFiles,
          uploadFileList: filteredUploadFileList,
          uploadingFiles: newUploadingFiles,
        };
      });

      // 立即更新 context
      dispatchBatchFiles({
        type: 'delete_' + variant,
        payload: {
          uid: fileUid,
        },
      });
    },
    [updateFileState, dispatchBatchFiles, variant],
  );

  // 统一的错误处理函数
  const handleUploadError = useCallback(
    (file: any, error: Error) => {
      console.error(`Upload error for file ${file.name}:`, error);

      // 显示用户友好的错误信息
      const errorMessage = error.message || '上传失败，请重试';
      // message.error(`${file.name}: ${errorMessage}`);

      // 更新文件状态
      updateFileState((prevState) => {
        const newUploadingFiles = new Set(prevState.uploadingFiles);
        newUploadingFiles.delete(file.uid);

        const newFiles = prevState.files.map((f) =>
          f.uid === file.uid ? { ...f, status: 'error' as const, error: errorMessage } : f,
        );

        return {
          ...prevState,
          files: newFiles,
          uploadingFiles: newUploadingFiles,
        };
      });
    },
    [updateFileState],
  );

  // 初始化radio
  useEffect(() => {
    if (hasOption) {
      const defultRadio = batchOcrData?.groupFlag || 2;
      setRadio(defultRadio);
      form.setFieldsValue({
        groupFlag: defultRadio,
      });
    }
  }, [batchOcrData, form, hasOption]);

  // 优化的文件回显逻辑 - 只在初始化或 context 数据变化时更新
  useEffect(() => {
    const contextFiles = batchFileDTO?.[otdName] || [];

    // 格式化 context 中的文件数据，添加标识来区分回显文件
    const formatFiles = contextFiles.map(
      (item: any) =>
        ({
          uid: item?.uid || item?.id,
          name: item?.name || getFileNameFromUrl(item.url) || '',
          url: item.url || '',
          status: 'done' as const,
          percent: 100,
          filePath: item?.filePath || '',
          fileType: item?.fileType || fileType,
          isFromContext: true, // 标识这是来自 context 的回显文件
        } as FileItem & { isFromContext?: boolean }),
    );

    // 只有在文件数量或内容发生变化时才更新状态
    setFileState((prevState) => {
      const currentContextFiles = prevState.files.filter((f) => (f as any).isFromContext);
      const hasChanges =
        currentContextFiles.length !== formatFiles.length ||
        formatFiles.some(
          (file: FileItem & { isFromContext?: boolean }) =>
            !currentContextFiles.find((f) => f.uid === file.uid && f.url === file.url),
        );

      if (hasChanges) {
        // 保留当前的上传文件，只更新回显文件
        const currentUploadFiles = prevState.files.filter((f) => !(f as any).isFromContext);

        // 使用 Map 来去重合并文件
        const fileMap = new Map<string, FileItem>();

        // 先添加回显文件
        formatFiles.forEach((file: FileItem) => {
          const key = file.uid || file.url || file.name;
          if (key) {
            fileMap.set(key, file);
          }
        });

        // 再添加上传文件
        currentUploadFiles.forEach((file: FileItem) => {
          const key = file.uid || file.url || file.name;
          if (key) {
            fileMap.set(key, file);
          }
        });

        const mergedFiles = Array.from(fileMap.values());

        return {
          ...prevState,
          files: mergedFiles,
        };
      }

      return prevState;
    });
  }, [batchFileDTO, otdName, fileType]);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (updateContextTimeoutRef.current) {
        clearTimeout(updateContextTimeoutRef.current);
      }
    };
  }, []);

  // 检查是否有文件正在上传
  useEffect(() => {
    const { files, uploadingFiles } = fileState;
    const hasUploading =
      uploadingFiles.size > 0 || files.some((file) => file.status === 'uploading');
    setLoading(hasUploading);
    onUploadStatusChange?.(hasUploading);
  }, [fileState, setLoading, onUploadStatusChange]);

  // 检查是否有错误文件
  useEffect(() => {
    const { files } = fileState;
    const hasError = files.some((file) => file.status === 'error');
    onErrorStatusChange?.(hasError);
  }, [fileState, onErrorStatusChange]);

  // 组件卸载时取消所有正在进行的上传请求
  useEffect(() => {
    return () => {
      uploadRequestsRef.current.forEach((xhr, uid) => {
        xhr.abort();
        console.log(`Cancelled upload on unmount for uid: ${uid}`);
      });
      uploadRequestsRef.current.clear();
    };
  }, [uploadRequestsRef]);

  // 预览事件监听
  useEffect(() => {
    const handlePreviewFile = (event: CustomEvent) => {
      const { url, fileName } = event.detail;
      if (previewRef.current) {
        previewRef.current.previewFile({
          url,
          fileName,
        });
      } else {
        console.error('预览组件引用不存在');
      }
    };

    window.addEventListener('step-item-preview-file', handlePreviewFile as EventListener);

    return () => {
      window.removeEventListener('step-item-preview-file', handlePreviewFile as EventListener);
    };
  }, []);

  // 文件分两列
  const { files } = fileState;
  const mid = Math.ceil(files.length / 2);
  const leftList = files.slice(0, mid);
  const rightList = files.slice(mid);

  // 自定义上传函数，支持取消功能和更好的错误处理
  const customUpload = (options: any) => {
    const { file, onProgress, onSuccess, onError } = options;

    const formData = new FormData();
    formData.append('file', file);
    formData.append('acl', 'PUBLIC_READ');
    formData.append('destPath', 'insurance/policy/vehicleLicense/');

    const xhr = new XMLHttpRequest();

    // 存储请求引用
    uploadRequestsRef.current.set(file.uid, xhr);

    // 上传进度处理
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const percent = Math.round((event.loaded / event.total) * 100);
        onProgress({ percent });
      }
    });

    // 上传完成处理
    xhr.addEventListener('load', () => {
      // 移除请求引用
      uploadRequestsRef.current.delete(file.uid);

      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          console.log('response', response);
          if (response.code === 200) {
            onSuccess(response);
          } else {
            const error = new Error(response.message || '上传失败，请重试');
            handleUploadError(file, error);
            onError(error);
          }
        } catch (error) {
          const parseError = new Error('响应解析失败');
          handleUploadError(file, parseError);
          onError(parseError);
        }
      } else {
        const statusError = new Error(`上传失败，状态码: ${xhr.status}`);
        handleUploadError(file, statusError);
        onError(statusError);
      }
    });

    // 网络错误处理
    xhr.addEventListener('error', () => {
      uploadRequestsRef.current.delete(file.uid);
      const networkError = new Error('网络错误，请检查网络连接');
      handleUploadError(file, networkError);
      onError(networkError);
    });

    // 上传取消处理
    xhr.addEventListener('abort', () => {
      uploadRequestsRef.current.delete(file.uid);
      console.log(`Upload cancelled for file: ${file.name}`);

      // 更新文件状态为已取消
      updateFileState((prevState) => ({
        ...prevState,
        uploadingFiles: new Set([...prevState.uploadingFiles].filter((uid) => uid !== file.uid)),
      }));
    });

    // 超时处理
    xhr.timeout = 60000; // 60秒超时
    xhr.addEventListener('timeout', () => {
      uploadRequestsRef.current.delete(file.uid);
      const timeoutError = new Error('上传超时，请重试');
      handleUploadError(file, timeoutError);
      onError(timeoutError);
    });

    // 使用修复后的URL
    const uploadUrl = getBaseUrl() + '/base/oss/common/uploadfile';
    xhr.open('POST', uploadUrl);

    // 设置请求头
    const headers = {
      ...getAuthHeaders(),
    };
    Object.keys(headers).forEach((key) => {
      if (headers[key]) {
        // 只设置有值的请求头
        xhr.setRequestHeader(key, headers[key]);
      }
    });

    xhr.send(formData);
  };

  const acceptedExtensions = [
    '.doc',
    '.docx',
    '.txt',
    '.zip',
    '.pdf',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.xls',
    '.xlsx',
    '.bmp',
    '.rar',
  ];
  // 上传配置
  const uploadProps: UploadProps = {
    multiple: true,
    name,
    accept: acceptedExtensions.join(','),
    customRequest: customUpload, // 使用自定义上传函数
    fileList: fileState.uploadFileList, // 使用统一状态中的 fileList
    beforeUpload: (file, fileList) => {
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        message.error('文件大小不能超过50M');
        return Upload.LIST_IGNORE;
      }
      // 如果上传了不符合 accept 的文件，则不允许通过
      const fileName = file.name?.toLowerCase() || '';
      const fileExtension = fileName.substring(fileName.lastIndexOf('.'));

      if (!acceptedExtensions.includes(fileExtension)) {
        message.error(
          `不支持的文件格式"${fileExtension}"，支持格式：${acceptedExtensions.join(', ')}`,
        );
        return Upload.LIST_IGNORE;
      }

      if (fileList.length > 100) {
        message.error('文件个数不能超过100个');
        return Upload.LIST_IGNORE;
      }

      // 检查文件名是否包含特殊字符
      if ([',', ':', '，', '：'].some((item) => file.name?.includes(item))) {
        message.error('文件名称不能含有中英文逗号和冒号');
        return Upload.LIST_IGNORE;
      }

      return true;
    },
    onChange: (info) => {
      const { fileList: c_fileList, file } = info;
      console.log('onChange fileList length:', c_fileList, file);

      // 更新上传中的文件集合
      const newUploadingFiles = new Set(fileState.uploadingFiles);
      if (file.status === 'uploading') {
        newUploadingFiles.add(file.uid);
      } else if (file.status === 'done' || file.status === 'error') {
        newUploadingFiles.delete(file.uid);
      }

      // 处理新上传的文件，转换为业务文件格式
      const uploadFiles = c_fileList.reduce((acc: any[], item) => {
        console.log('item', item);
        if (item.status !== 'removed') {
          acc.push({
            uid: item.uid,
            name: item.name || '',
            url: item.response?.data?.netWorkPath || '',
            status: item.status as 'uploading' | 'done' | 'error',
            percent: item.percent || 0,
            filePath: item.response?.data?.filePath || '',
            fileType,
          });
        }
        return acc;
      }, []);

      // 使用统一的状态更新函数，合并回显文件和新上传文件
      updateFileState((prevState) => {
        // 获取当前已有的回显文件（来自 context 的文件）
        const contextFiles = prevState.files.filter((f) => (f as any).isFromContext);

        // 创建一个 Map 来去重，使用 uid 作为 key
        const fileMap = new Map<string, FileItem>();

        // 先添加回显文件
        contextFiles.forEach((file: FileItem) => {
          const key = file.uid || file.url || file.name;
          if (key) {
            fileMap.set(key, file);
          }
        });

        // 再添加上传文件，如果 key 相同则覆盖（更新状态）
        uploadFiles.forEach((file: FileItem) => {
          const key = file.uid || file.url || file.name;
          if (key) {
            fileMap.set(key, file);
          }
        });

        // 转换回数组
        const mergedFiles = Array.from(fileMap.values());

        console.log('contextFiles:', contextFiles);
        console.log('uploadFiles:', uploadFiles);
        console.log('mergedFiles:', mergedFiles);

        return {
          ...prevState,
          files: mergedFiles,
          uploadFileList: c_fileList,
          uploadingFiles: newUploadingFiles,
        };
      });
    },
    onRemove: (file) => {
      // 如果文件正在上传，取消上传请求
      if (file.status === 'uploading' && file.uid) {
        const xhr = uploadRequestsRef.current.get(file.uid);
        if (xhr) {
          xhr.abort(); // 取消上传请求
          uploadRequestsRef.current.delete(file.uid);
          console.log(`Cancelled upload for file: ${file.name}`);
        }
      }

      // 使用统一的删除函数
      removeFile(file.uid);

      return true;
    },
    showUploadList: false, // 我们使用自定义的文件列表显示
  };

  console.log('fileState', fileState);

  // 文件下载
  const onDownload = (file: FileItem) => {
    const url = file?.url;
    if (url && file?.name) {
      message.loading({ content: '下载文件中...', key: 'download_file' });
      getBlob(url, (blob: Blob) => {
        saveAs(blob, file?.name);
        message.destroy('download_file');
      });
    } else {
      message.error('下载参数错误！');
    }
  };

  // 处理预览
  const handlePreview = (e: React.MouseEvent, file: FileItem) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('file', file);
    if (file.url) {
      // 使用全局的预览方法，通过事件或回调传递给父组件
      const event = new CustomEvent('step-item-preview-file', {
        detail: {
          url: file.url,
          fileName: file.name,
        },
      });
      console.log('发送预览事件:', event.detail);
      window.dispatchEvent(event);
    } else {
      console.warn('文件没有URL，无法预览:', file);
    }
  };

  return (
    <Form form={form} layout="vertical" style={{ height: '100%' }}>
      <Row style={{ height: '100%' }}>
        <Divider />
        <Col span={2}>
          {/* 左侧标题 */}
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            <span style={{ fontWeight: 600, marginRight: 8 }}>
              {variantTitleMap[variant].title}
            </span>
          </div>
        </Col>
        <Col span={22} style={{ height: '100%' }}>
          {hasOption ? (
            <>
              <Form.Item
                name="groupFlag" // todo：name待补充
                required
                layout="horizontal"
                style={{ marginBottom: '12px' }}
                label={variantTitleMap[variant].description}
              >
                <Radio.Group
                  value={radio}
                  onChange={(e) => {
                    setRadio?.(e.target.value);
                    updateBatchOcrData((prev) => ({
                      ...prev,
                      groupFlag: e.target.value,
                    }));
                  }}
                  style={{ marginRight: 8 }}
                >
                  <Radio value={1}>是</Radio>
                  <Radio value={2}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </>
          ) : (
            <span style={{ color: '#888', fontWeight: 400 }}>
              {variantTitleMap[variant].description}
            </span>
          )}
          {/* 文件类型说明 */}
          <div style={{ color: '#888', fontSize: 12, margin: '8px 0' }}>
            支持格式：.doc .docx .txt .zip .pdf .png .jpg .jpeg .gif .xls .xlsx .bmp
            .rar，单个文件最大50M
          </div>
          {/* 拖拽上传 */}
          <Form.Item name={name}>
            <Dragger {...uploadProps} style={{ marginBottom: 16, width: '100%', height: '300px' }}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此处上传</p>
            </Dragger>
          </Form.Item>
          {/* 上传状态统计 */}
          {fileState.files.length > 0 && (
            <div
              style={{
                margin: '16px 0',
                padding: '12px',
                backgroundColor: fileState.files.some((file) => file.status === 'error')
                  ? '#fff2f0'
                  : '#f5f5f5',
                borderRadius: '6px',
                fontSize: '14px',
                color: '#666',
                border: fileState.files.some((file) => file.status === 'error')
                  ? '1px solid #ffccc7'
                  : 'none',
              }}
            >
              <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap', alignItems: 'center' }}>
                <span>
                  已成功上传{' '}
                  <strong style={{ color: '#52c41a' }}>
                    {fileState.files.filter((file) => file.status === 'done').length}
                  </strong>{' '}
                  个文件
                </span>
                {fileState.files.filter((file) => file.status === 'uploading').length > 0 && (
                  <span>
                    上传中{' '}
                    <strong style={{ color: '#1890ff' }}>
                      {fileState.files.filter((file) => file.status === 'uploading').length}
                    </strong>{' '}
                    个文件
                  </span>
                )}
                {fileState.files.filter((file) => file.status === 'error').length > 0 && (
                  <span>
                    上传失败{' '}
                    <strong style={{ color: '#ff4d4f' }}>
                      {fileState.files.filter((file) => file.status === 'error').length}
                    </strong>{' '}
                    个文件
                  </span>
                )}
              </div>
              {/* 错误提示 */}
              {fileState.files.some((file) => file.status === 'error') && (
                <div
                  style={{
                    marginTop: '8px',
                    color: '#ff4d4f',
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  <ExclamationCircleOutlined />
                  <span>存在上传失败的文件，请删除失败文件后重新上传才能进行下一步</span>
                </div>
              )}
            </div>
          )}

          {/* 文件列表 */}
          <Row
            style={{
              display: 'flex',
              justifyContent: 'center',
              width: '100%',
              gap: 120,
              maxHeight: '500px',
              overflow: 'scroll',
              scrollbarWidth: 'none',
            }}
          >
            {[leftList, rightList].map((list, idx) => (
              <Col span={12} key={idx} style={{ flex: 1, minWidth: 200, overflow: 'hidden' }}>
                {list.map((file) => (
                  <div
                    key={file.uid || file.url}
                    style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}
                  >
                    {/* 文件状态图标 */}
                    <div style={{ marginRight: 8, minWidth: 16 }}>
                      {file.status === 'uploading' && (
                        <LoadingOutlined style={{ color: '#1890ff' }} />
                      )}
                      {file.status === 'done' && (
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                      )}
                      {file.status === 'error' && (
                        <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                      )}
                    </div>

                    <div style={{ flex: 1, minWidth: 0 }}>
                      <Tooltip title={file.name}>
                        <div
                          style={{
                            color: file.status === 'error' ? '#ff4d4f' : '#1890ff',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            fontSize: 14,
                            cursor: 'pointer',
                          }}
                          onClick={(e) => handlePreview(e, file)}
                        >
                          {file.name}
                        </div>
                      </Tooltip>

                      {/* 上传进度条 */}
                      {file.status === 'uploading' && (
                        <Progress
                          percent={file.percent}
                          size="small"
                          showInfo={false}
                          style={{ marginTop: 4 }}
                        />
                      )}

                      {/* 状态标签 */}
                      {file.status && (
                        <div style={{ marginTop: 4 }}>
                          <Tag
                            color={
                              file.status === 'uploading'
                                ? 'processing'
                                : file.status === 'done'
                                ? 'success'
                                : 'error'
                            }
                          >
                            {file.status === 'uploading'
                              ? '上传中'
                              : file.status === 'done'
                              ? '已完成'
                              : '上传失败'}
                          </Tag>
                        </div>
                      )}
                    </div>

                    <div style={{ marginLeft: 8, display: 'flex', gap: 8 }}>
                      {file.status === 'done' && (
                        <a className="file-action dowload" onClick={() => onDownload?.(file)}>
                          下载
                        </a>
                      )}
                      <a
                        className="file-action delete"
                        onClick={() => {
                          // 如果文件正在上传，取消上传请求
                          if (file.status === 'uploading' && file.uid) {
                            const xhr = uploadRequestsRef.current.get(file.uid);
                            if (xhr) {
                              xhr.abort(); // 取消上传请求
                              uploadRequestsRef.current.delete(file.uid);
                              console.log(`Cancelled upload for file: ${file.name}`);
                            }
                          } else if (file.status === 'error') {
                            message.info('已删除失败文件，请重新上传');
                          }

                          // 使用统一的删除函数
                          removeFile(file.uid || file.url);
                        }}
                      >
                        {file.status === 'uploading' ? '取消' : '删除'}
                      </a>
                    </div>
                  </div>
                ))}
              </Col>
            ))}
          </Row>
        </Col>
      </Row>
      <ImagePreview ref={previewRef} />
    </Form>
  );
};

export default CarOcrContent;
