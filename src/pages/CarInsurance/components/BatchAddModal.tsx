import { getAuthHeaders } from '@/utils/auth';
import { downLoadExcel } from '@/utils/utils';
import { useModel } from '@umijs/max';
import { Button, Form, message, Modal, Popconfirm, Space, Spin, Steps, Tag, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/lib/upload';
import dayjs from 'dayjs';
import type { ReactElement } from 'react';
import React, {
  lazy,
  memo,
  Suspense,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { downLoadCarInfoTemplate, submitBulkUpload } from '../services';
import type { IcarInfoItem, IuploadCarInfoItem, UcarInsuranceOperate } from '../type';
import { BULK_UPLOAD_EVENT_ACTION, carInfoMap, ECARINSURANCE_OPERATE_ACTION } from '../type';
import { getBaseUploadAction, getRandomUUID, verifyCarInfo } from '../utils';

// 上传步骤组件 异步加载
const UploadExcel = lazy(() => import('./BatchUpload/UploadExcel'));
const UploadPolicies = lazy(() => import('./BatchUpload/UploadPolicies'));
const UploadPurchaseInvoice = lazy(() => import('./BatchUpload/UploadPurchaseInvoice'));
const UploadCertificateOfConformity = lazy(
  () => import('./BatchUpload/UploadCertificateOfConformity'),
);
const UploadDriverLicense = lazy(() => import('./BatchUpload/UploadDriverLicense'));
const DragDropZones = lazy(() => import('@/pages/CarInsurance/components/DragDropZones'));

// 骨架屏组件
import { useCarInsuranceBulkOcrContext } from '../context/CarInsuranceBulkOcrContext';
import UploadStepSkeleton from './BatchUpload/UploadStepSkeleton';

type Props = {
  children: ReactElement;
  // 是否有ocr任务
  isHaveOcrTask?: boolean;
  currentStep?: number;
  doAction: (action: UcarInsuranceOperate, params?: any) => Promise<string>;
};
const BatchAddModal: React.FC<Props> = (props) => {
  const [current, setCurrent] = useState<number>(0);
  const { setCarInfo, carInfo, detailData } = useModel('CarInsurance.carInsurance');
  const { orderNo } = detailData;
  const [visible, setVisible] = useState(false);
  const [excelErrorList, setExcelErrorList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [carInfoList, setCarInfoList] = useState<IuploadCarInfoItem[]>([]);
  const [disabledIgnoreError, setDisabledIgnoreError] = useState(false);
  const [hasUploadingFiles, setHasUploadingFiles] = useState(false); // 新增：跟踪是否有文件正在上传
  const [hasErrorFiles, setHasErrorFiles] = useState(false); // 新增：跟踪是否有错误文件
  const [downloading, setDownloading] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [dragDropData, setDragDropData] = useState<any>();

  console.log('dragDropData', dragDropData, uploading);
  const {
    actionLoading,
    batchOperationStatus,
    batchOcrData,
    checkMatchProcessCallback,
    handleGiveUpOrReAction,
    updateBatchOcrData,
    batchFileDTO,
    dispatchBatchFiles,
    batchAddModalRef,
  } = useCarInsuranceBulkOcrContext();
  // form
  const [form] = Form.useForm();

  const { children, currentStep, doAction, isHaveOcrTask } = props;

  console.log('carInfoList', carInfoList);
  console.log('isMatchComplete', batchOperationStatus.isMatchComplete);
  console.log('isHaveOcrTask', isHaveOcrTask);
  console.log('batchOcrData', batchOcrData);

  useImperativeHandle(batchAddModalRef, () => ({
    show: () => {
      setVisible(true);
      stepChange();
    },
    hide: () => {
      setVisible(false);
    },
  }));

  useEffect(() => {
    setExcelErrorList([]);
    setCarInfoList([]);
    // 关闭重置文件列表
    if (!visible) {
      setHasUploadingFiles(false); // 重置上传状态
      setHasErrorFiles(false); // 重置错误状态
      // dispatchBatchFiles({
      //   type: 'reset',
      // });
      updateBatchOcrData((prev) => ({ ...prev, groupFlag: 2 })); // 重置flag
    } else {
      if (batchOperationStatus.isMatchComplete) {
        setCurrent(5);
      }
    }
  }, [visible, batchOperationStatus.isMatchComplete, currentStep, dispatchBatchFiles]);

  // 每次打开modal都同步一次数据
  // useEffect(() => {
  //   if (batchOperationStatus.isProcessing && visible) {
  //     checkMatchProcessCallback();
  //   }
  // }, [visible, batchOperationStatus.isProcessing]);

  // 处理上传状态变化
  const handleUploadStatusChange = (hasUploading: boolean) => {
    setHasUploadingFiles(hasUploading);
  };

  // 下载模版
  function downLoadTemplate() {
    setDownloading(true);
    downLoadCarInfoTemplate().then((res) => {
      downLoadExcel(res);
      setDownloading(false);
    });
  }

  // 表格识别车牌车架号
  function onExcelChange(info: UploadChangeParam) {
    if (info.file.status === 'uploading') {
      setLoading(true);
    }
    if (info.file.status === 'done') {
      setLoading(false);
      // 说明网络 状态码正常
      if (info.file.response?.ret === 0) {
        const carList: IuploadCarInfoItem[] = info?.file?.response?.data;
        // 把有错误的过滤出来
        const errCarList = carList.filter((item) => Object.keys(item.errMsgMap).length !== 0);
        let disabledIgnoreErrorNum = 0;
        const errs = errCarList.map((item) => {
          const { id, errMsgMap } = item;
          if (errMsgMap?.vin === '缺失' && errMsgMap?.vehicleNumber === '缺失') {
            disabledIgnoreErrorNum++;
          }
          return {
            id: id,
            error: Object.keys(errMsgMap).map((key) => (
              <Tag color="error" key={key}>
                {carInfoMap[key] + '-' + errMsgMap[key]}
              </Tag>
            )),
          };
        });
        if (errs.length) {
          // 如果 错误中 所有数据 车架号和车牌号都不存在 则 不能点击忽略错误
          setDisabledIgnoreError(disabledIgnoreErrorNum === carList.length);
          setExcelErrorList(errs);
        } else {
          message.success('上传成功');
          setExcelErrorList([]);
          setCurrent(1);
        }
        setCarInfoList(
          carList.map((item) => ({
            ...item,
            insuranceSlipUrlList: [], // 投保单
            vehicleLicenseUrlList: [], // 车辆证件
            insuranceSlipOssUrlList: [], // 投保单oss短路径
            vehicleLicenseOssUrlList: [], // 车辆证件oss短路径
          })),
        );
      } else {
        // 自定义未知错误
        message.error(`上传失败-${info.file.response?.msg}`);
        setLoading(false);
      }
    } else if (info.file.status === 'error') {
      // 一般为网络错误
      message.error(`上传失败`);
      setLoading(false);
    }
  }

  // 识别excel后，点击下一步先存一遍订单草稿
  async function submitUpdateOrderDraft(): Promise<string> {
    const carInfo1 = carInfoList.map((item, index) => {
      const {
        insuredName,
        commercialInsuranceEndDate,
        commercialInsuranceStartDate,
        engineCode,
        vehicleNumber,
        ...rest
      } = item;

      const commercialInsurancePeriod = dayjs(commercialInsuranceEndDate).diff(
        commercialInsuranceStartDate,
        'day',
      );
      const result: IcarInfoItem = {
        commercialInsurancePeriod,
        assuredName: insuredName,
        engineNumber: engineCode,
        plateNo: vehicleNumber,
        uuid: getRandomUUID(),
        inspected: index === 0 ? 1 : 0,
        commercialInsuranceEndDate,
        commercialInsuranceStartDate,
        ...rest,
        id: undefined,
      };
      return result;
    });
    // 如果再次批量新增 有id的要把id传给后端 没有id的直接删除
    const carInfo2 = carInfo?.filter((item) => {
      if (item?.id) {
        // 批量新增是全量覆盖
        item.deleteFlag = true; // 有id的要把id传给后端
        return true;
      } else {
        return false;
      }
    });
    const carInfo3 = verifyCarInfo([...carInfo2, ...carInfo1]);
    setCarInfo(carInfo3);

    if (!isHaveOcrTask) {
      // 直接调用 doAction 并等待完成，传递最新的 carInfo3 数据
      try {
        const data = await doAction(ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT, {
          noLoading: true,
          customCarInfo: carInfo3,
        });
        console.log('data', data);
        // 保存草稿后，返回当前的订单号（可能是新生成的）
        return data;
      } catch (error) {
        console.error('保存草稿失败:', error);
        throw error;
      }
    }
    return orderNo; // 如果已有OCR任务，直接返回当前订单号
  }

  // 初始化进入modal时触发
  function stepChange() {
    setCurrent(currentStep || 0);
  }

  // 下一步/上一步
  function changeStep(step: number) {
    setCurrent((prev) => {
      const newStep = prev + step;
      if (newStep < 0) {
        return 0;
      }
      if (newStep > 4) {
        return 4;
      }
      return newStep;
    });
  }

  console.log('filelist', batchFileDTO, form.getFieldsValue());

  const formatFiles = (files: any[]) =>
    files.map((file: any) => ({ fileType: file.fileType, filePath: file.filePath, url: file.url }));

  // 每一步都存草稿，成功后再跳上/下一步
  async function saveOcrDraftAndNext(step: number): Promise<string> {
    try {
      const policyLen = batchFileDTO?.policyFiles?.length || 0;
      const purchaseLen = batchFileDTO?.purchaseFiles?.length || 0;
      const creditLen = batchFileDTO?.creditFiles?.length || 0;
      const licenseLen = batchFileDTO?.licenseFiles?.length || 0;
      const counts = policyLen + purchaseLen + creditLen + licenseLen;

      const currentStep = step + current;

      // 保留文件总数不超过500的校验
      if (counts > 500) {
        message.warning('上传文件不能超过500个');
        return Promise.reject(new Error('上传文件不能超过500个'));
      }

      console.log('current', current);

      // 如果是最后一步且四个步骤都为空，则不能提交
      if (currentStep === 5 && counts === 0) {
        message.warning('请至少上传一个文件');
        return Promise.reject(new Error('请至少上传一个文件'));
      }

      // 如果当前步骤没有文件，允许跳过（不调用接口），直接跳转到下一步
      if (counts === 0) {
        changeStep(step);
        return Promise.reject(null);
      }

      // 只有当有文件时才调用接口
      const params = {
        groupFlag: form.getFieldValue('groupFlag') || batchOcrData?.groupFlag,
        vehicleBatchOcrTaskExtendList: [
          [...formatFiles(batchFileDTO.policyFiles)],
          [...formatFiles(batchFileDTO.purchaseFiles)],
          [...formatFiles(batchFileDTO.creditFiles)],
          [...formatFiles(batchFileDTO.licenseFiles)],
        ],
        orderNo,
        businessNo: batchOcrData?.businessNo,
        event: BULK_UPLOAD_EVENT_ACTION.UPDATE_TASK, // 默认为更新任务
      };
      setUploading(true);

      // 根据是否初始化过来决定事件类型
      if (!batchOperationStatus.isProcessing) {
        params.event = BULK_UPLOAD_EVENT_ACTION.SAVE_TASK;
        // 初次保存任务时，先存一遍订单草稿，获取新生成的订单号
        const newOrderNo = await submitUpdateOrderDraft();
        // 使用新生成的订单号，优先使用非复用的订单号
        params.orderNo = newOrderNo;
      }
      console.log('params', params);

      const res = await submitBulkUpload(params);
      console.log('res', res);
      if (res?.ret === 0) {
        // 重新获取当前ocr内容
        changeStep(currentStep >= 5 ? 4 : step); // 第五步是隐藏的拖拽，所以不跳第五步
        console.log('touched in modal 331');
        checkMatchProcessCallback?.(params.orderNo);
        return res?.data?.businessNo;
      } else {
        // 接口调用失败时抛出异常，让上层函数能够捕获
        throw new Error(res?.msg || '保存失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '操作失败';
      message.error(errorMessage);
      // 重新抛出异常，让调用方能够感知到失败
      throw error;
    } finally {
      setUploading(false);
    }
  }

  const buttonText = useMemo(() => {
    if (hasUploadingFiles) {
      return '文件上传中，请稍候...';
    }

    if (hasErrorFiles) {
      return '存在上传失败文件，请处理后继续';
    }
    return '下一步';
  }, [hasUploadingFiles, hasErrorFiles]);

  // 开始匹配
  async function startMatching() {
    try {
      // 先保存数据，只有成功后才开始匹配
      const businessNo = await saveOcrDraftAndNext(+1);

      // 第一个接口调用成功后，再开始匹配
      if (handleGiveUpOrReAction) {
        handleGiveUpOrReAction(BULK_UPLOAD_EVENT_ACTION.START_MATCH, businessNo);
      }
    } catch (error) {
      console.error('保存数据或开始匹配失败:', error);
    }
  }

  // 拖拽组件按钮完成方法
  async function handleComplete() {
    // 触发完成匹配并已使用
    handleGiveUpOrReAction?.(BULK_UPLOAD_EVENT_ACTION.COMPLETE_MATCH_USED);

    // 回填carInfo
    const carInfo1 = batchOcrData?.carInfoDTOList.map((item: any) => {
      const {
        insuredName,
        commercialInsuranceEndDate,
        commercialInsuranceStartDate,
        engineCode,
        vehicleNumber,
        ...rest
      } = item;

      const commercialInsurancePeriod = dayjs(commercialInsuranceEndDate).diff(
        commercialInsuranceStartDate,
        'day',
      );
      const result: IcarInfoItem = {
        commercialInsurancePeriod,
        assuredName: insuredName,
        engineNumber: engineCode,
        plateNo: vehicleNumber,
        uuid: getRandomUUID(),
        inspected: 0,
        commercialInsuranceEndDate,
        commercialInsuranceStartDate,
        ...rest,
        id: undefined,
      };
      return result;
    });
    setCarInfo(carInfo1);
  }

  // 根据不同步骤渲染底部按钮
  function renderFooter() {
    const actionFooter = {
      0: (
        <Space>
          <Button loading={downloading} type="link" onClick={downLoadTemplate}>
            下载表格模版
          </Button>
          <Upload
            action={getBaseUploadAction('/bizadmin/insurance/policy/order/uploadExcel')}
            onChange={onExcelChange}
            showUploadList={false}
            headers={{
              'hll-appid': 'bme-finance-bizadmin-svc',
              ...getAuthHeaders(),
            }}
            name="file"
          >
            <Button type="primary">{excelErrorList?.length ? '重新上传' : '上传表格'}</Button>
          </Upload>

          {excelErrorList?.length ? (
            <Button
              loading={downloading}
              type="primary"
              disabled={disabledIgnoreError}
              danger
              onClick={() => {
                setCurrent(1);
              }}
            >
              忽略错误
            </Button>
          ) : null}
        </Space>
      ),
      1: (
        <Space>
          <Button
            loading={uploading}
            type="primary"
            disabled={hasUploadingFiles || hasErrorFiles}
            onClick={() => {
              form.validateFields().then(() => {
                saveOcrDraftAndNext(+1);
              });
            }}
          >
            {buttonText}
          </Button>
        </Space>
      ),
      2: (
        <Space>
          <Button loading={uploading} onClick={() => saveOcrDraftAndNext(-1)}>
            上一步
          </Button>
          <Button
            type="primary"
            loading={uploading}
            disabled={hasUploadingFiles || hasErrorFiles}
            onClick={() => saveOcrDraftAndNext(+1)}
          >
            {buttonText}
          </Button>
        </Space>
      ),
      3: (
        <Space>
          <Button loading={uploading} onClick={() => saveOcrDraftAndNext(-1)}>
            上一步
          </Button>
          <Button
            type="primary"
            loading={uploading}
            disabled={hasUploadingFiles || hasErrorFiles}
            onClick={() => saveOcrDraftAndNext(+1)}
          >
            {buttonText}
          </Button>
        </Space>
      ),
      4: (
        <Space>
          <Button loading={uploading} onClick={() => saveOcrDraftAndNext(-1)}>
            上一步
          </Button>
          <Button
            type="primary"
            loading={actionLoading}
            disabled={hasUploadingFiles || hasErrorFiles}
            onClick={() => {
              startMatching();
            }}
          >
            {hasUploadingFiles
              ? '文件上传中，请稍候...'
              : hasErrorFiles
                ? '存在上传失败文件，请处理后继续'
                : '开始匹配'}
          </Button>
        </Space>
      ),
      // 这一步不显示顶部step，只是专属批量操作的最终流程
      5: (
        <Space>
          <Popconfirm
            title="是否放弃本次操作？"
            onConfirm={() => {
              handleGiveUpOrReAction?.(BULK_UPLOAD_EVENT_ACTION.GIVE_UP_MATCH);
            }}
            okText="确认"
            cancelText="取消"
          >
            <Button loading={actionLoading}>放弃本次操作</Button>
          </Popconfirm>

          <Popconfirm
            title="确认提交将会忽略所有未关联到车辆的文件，是否继续？"
            onConfirm={() => {
              handleComplete();
            }}
            onOpenChange={(open) => {
              // 如果拖拽后的数据里，存在未分配的数据，则需要弹窗二次确认
              if (dragDropData?.globalSourceFiles?.length) {
                setConfirmOpen(open);
                return;
              }
              handleComplete();
            }}
            open={confirmOpen}
            okText="确认提交"
            cancelText="返回"
          >
            <Button type="primary" loading={actionLoading} disabled={!dragDropData}>
              确认
            </Button>
          </Popconfirm>
        </Space>
      ),
    };

    return actionFooter[current] ?? null;
  }

  console.log('current', current);

  // step list map
  const steps = [
    {
      title: '上传表格',
      key: 'upload-excel',
      component: <UploadExcel excelErrorList={excelErrorList} />,
    },
    {
      title: '上传投保单',
      key: 'upload-policies',
      component: (
        <UploadPolicies
          form={form}
          otdName="policyFiles"
          setLoading={setUploading}
          onUploadStatusChange={handleUploadStatusChange}
          onErrorStatusChange={setHasErrorFiles}
        />
      ),
    },
    {
      title: '上传购车发票',
      key: 'upload-purchase-invoice',
      component: (
        <UploadPurchaseInvoice
          form={form}
          otdName="purchaseFiles"
          setLoading={setUploading}
          onUploadStatusChange={handleUploadStatusChange}
          onErrorStatusChange={setHasErrorFiles}
        />
      ),
    },
    {
      title: '上传合格证',
      key: 'upload-certificate-of-conformity',
      component: (
        <UploadCertificateOfConformity
          form={form}
          otdName="creditFiles"
          setLoading={setUploading}
          onUploadStatusChange={handleUploadStatusChange}
          onErrorStatusChange={setHasErrorFiles}
        />
      ),
    },
    {
      title: '上传行驶证',
      key: 'upload-driver-license',
      component: (
        <UploadDriverLicense
          form={form}
          otdName="licenseFiles"
          setLoading={setUploading}
          onUploadStatusChange={handleUploadStatusChange}
          onErrorStatusChange={setHasErrorFiles}
        />
      ),
    },
    {
      title: '拖拽上传',
      key: 'drag-drop-zones',
      hidden: !batchOperationStatus.isMatchComplete,
      component: (
        <DragDropZones
          onDataChange={(data) => {
            console.log('拖拽数据变更:', data);
            setDragDropData(data);
          }}
        />
      ),
    },
  ];

  // steps
  const stepItems = steps
    .filter((step) => !step.hidden)
    .map((step) => ({ key: step.key, title: step.title }));

  return (
    <>
      <div
        onClick={() => {
          setVisible(true);
          stepChange();
        }}
      >
        {children}
      </div>
      <Modal
        maskClosable={false} // 禁止失焦关闭modal
        keyboard={false} // 禁止键盘esc关闭modal
        open={visible}
        destroyOnClose // 关闭销毁弹窗
        onCancel={() => {
          setVisible(false);
        }}
        footer={renderFooter()}
        title="批量新增"
        width={1000}
      >
        <Spin spinning={loading}>
          {!batchOperationStatus.isMatchComplete && (
            <Steps current={current} style={{ marginBottom: 10 }} items={stepItems} />
          )}
          <Suspense fallback={<UploadStepSkeleton />}>{steps[current].component}</Suspense>
        </Spin>
      </Modal>
    </>
  );
};

export default memo(BatchAddModal);
