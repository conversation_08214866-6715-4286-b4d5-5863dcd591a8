/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-09-13 15:55:05
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-09-21 11:41:25
 * @FilePath: /code/lala-finance-biz-web/src/pages/Collection/components/BatchSendOverdueSMSCon.tsx
 * @Description: batchSendOverdueSMSCon, 批量发送逾期案件短信组件
 */
import type { ProColumns } from '@ant-design/pro-components';
import { EditableFormInstance, EditableProTable, ModalForm } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { useMount } from 'ahooks';
import { Button, message } from 'antd';
import React, { useRef, useState } from 'react';
import type { IbatchOverdueSMSDataItem, SmsPageItemProp } from '../data';
import { batchSendOverdueSMS, getBatchOverdueSMSData } from '../service';
const defaultData = [{ id: Date.now().toString() }];
const BatchSendOverdueSMSCon = () => {
  const editableFormRef = useRef<EditableFormInstance<SmsPageItemProp>>();
  const access = useAccess();
  const [batchOverdueSMSData, setBatchOverdueSMSData] = useState<IbatchOverdueSMSDataItem[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(
    defaultData.map((item) => item.id),
  );
  const columns: ProColumns<SmsPageItemProp>[] = [
    {
      title: '产品一级类别',
      dataIndex: 'productName',
      valueType: 'select',
      valueEnum: {
        '01': '商业保理',
        '02': '融资租赁',
        '03': '小额贷款',
      },
      fieldProps(formInstance, rowConfig) {
        const { rowIndex = 0 } = rowConfig;
        //  和 form 配合后 rowKey 的取值有点问题 取值为 -- ['smsData', '1']
        return {
          onChange() {
            // 每次切换不同的类型 需要清空 模版
            editableFormRef.current?.setRowData?.(rowIndex, {
              smsTemplateId: undefined,
            });
          },
        };
      },
      width: 120,
      align: 'center',
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>* </span>面向客群
        </>
      ),
      dataIndex: 'smsTemplateId',
      width: 300,
      align: 'center',
      valueType: 'select',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
          hasFeedback: false,
        };
      },
      fieldProps(formInstance, rowConfig) {
        // 要根据不同的产品选出不同的模版选项

        const { rowIndex = 0 } = rowConfig;

        // 获取当前行的数据
        const { productName } = editableFormRef.current?.getRowData?.(rowIndex) || {};

        const currentOptions = batchOverdueSMSData
          .filter((item) => item.secondaryClassification.substring(0, 2) === productName)
          .map((item) => {
            const { smsTemplateName, smsTemplateId } = item;
            // true 说明 smsTemplateId 已经选过了 fasle 没有选过
            const smsData = editableFormRef.current?.getRowsData?.();
            const disabled = smsData?.some((item) => item.smsTemplateId === smsTemplateId);
            return {
              label: smsTemplateName,
              value: smsTemplateId,
              disabled,
            };
          });
        return {
          options: currentOptions,
          onChange(_: any, options: any) {
            editableFormRef.current?.setRowData?.(rowIndex, {
              smsTemplateName: options?.label,
            });
          },
        };
      },
    },
    // {
    //     title: '名称',
    //     dataIndex: 'smsTemplateName',
    //     hideInTable: true,
    // },
    {
      title: '客户个数',
      dataIndex: 'customerNum',
      width: 100,
      align: 'center',
      renderFormItem: (_, currentProps) => {
        const { record: { smsTemplateId } = {} } = currentProps;
        return (
          <>
            {batchOverdueSMSData.find((item) => item.smsTemplateId === smsTemplateId)?.customerNum}
          </>
        );
      },
    },
    {
      title: '短信模版',
      dataIndex: 'smsModal',
      width: 300,
      align: 'center',
      renderFormItem: (_, currentProps) => {
        const { record: { smsTemplateId } = {} } = currentProps;
        const curData = batchOverdueSMSData.find((item) => item.smsTemplateId === smsTemplateId);
        const { smsTemplateName } = curData || {};
        return (
          <>
            <div>{smsTemplateName}</div>
            <div>{smsTemplateId}</div>
          </>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      width: 100,
      align: 'center',
    },
  ];

  const initBatchOverdueSMSData = async () => {
    const data = await getBatchOverdueSMSData();
    setBatchOverdueSMSData(data);
  };

  useMount(() => {
    initBatchOverdueSMSData();
  });

  const onFinish = async (values: { smsData: SmsPageItemProp[] }) => {
    try {
      const { smsData } = values;
      if (
        smsData?.some((item) => {
          return !!item.smsTemplateId === false;
        })
      ) {
        message.warning('请选择催收短信客群模板！');
        return;
      }

      const res = await batchSendOverdueSMS({
        urgePersonName: access.userName as string,
        urgePersonId: access.userId as string,
        smsTemplateList: smsData?.map((item) => {
          const { smsTemplateId, smsTemplateName } = item;
          return {
            smsTemplateId,
            smsTemplateName,
          };
        }),
      });
      if (res?.data?.failedSmsTemplateNameList?.length) {
        const msg = res.data.failedSmsTemplateNameList.map((item: string) => `「${item}」`).join();
        message.warning(`该${msg}已存在进行中催收短信下发任务，暂不可重复发起!`);
      } else if (res.data === null) {
        message.warning('点击过于频繁, 请稍后重试！');
      } else {
        message.success('短信批量发送成功！');
        return true;
      }
      return false;
    } catch (error) {
      message.error('发送失败');
      return false;
    }
  };

  return (
    <>
      <ModalForm
        title="批量手动下发催收短信"
        trigger={
          <Button type="primary" style={{ backgroundColor: '#e6a23c', borderColor: '#e6a23c' }}>
            批量发送催收短信
          </Button>
        }
        width={1200}
        modalProps={{
          destroyOnClose: true,
          okText: '确定发送',
        }}
        onFinish={onFinish}
        initialValues={{ smsData: defaultData }}
      >
        <EditableProTable<SmsPageItemProp>
          scroll={{ y: 350 }}
          columns={columns}
          name="smsData"
          editableFormRef={editableFormRef}
          rowKey="id"
          recordCreatorProps={{
            newRecordType: 'dataSource',
            record: () => ({
              id: Date.now().toString(),
            }),
          }}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: setEditableRowKeys,
            actionRender: (_, config, defaultDoms) => {
              const { index = 0 } = config;
              return index > 0 ? [defaultDoms.delete] : [];
            },
          }}
        />
      </ModalForm>
    </>
  );
};

export default React.memo(BatchSendOverdueSMSCon);
