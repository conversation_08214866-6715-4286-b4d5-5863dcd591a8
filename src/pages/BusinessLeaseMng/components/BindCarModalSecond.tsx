/*
 * @Author: your name
 * @Date: 2021-04-12 15:18:02
 * @LastEditTime: 2024-04-16 13:47:46
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/BindCarModalSecond.tsx
 */
import { DividerTit } from '@/components';
import globalStyle from '@/global.less';
import ProForm, {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import { history, useAccess, useModel } from '@umijs/max';
import { Button, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
// import dayjs from 'dayjs';
// import { validateCarCodeUnique } from '@/services/validate';
import { CommonDraggleUpload } from '@/components/ReleaseCom';
import { CHANNEL_TYPES_LABEL_MAP } from '@/enums';
import { convertUploadFileList } from '@/utils/utils';
import type { BindCarParams } from '../data';
import {
  bindingCar,
  getAllChannelByChannelType,
  getApplyStoreList,
  getCompanyList,
  getPromotionByCarNoAndApplyCityCode,
  validateCarCodeEqual,
} from '../service';

export type BindCarModelProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  data?: any;
  orderDetail?: any;
  formCur?: {
    otherMsg?: string;
    carNo?: string;
    totalPrice?: string;
    channelType?: number;
    licenseType: number;
    licenseCompany: {
      label: string;
      value: string;
    };
    channelDetail?: Record<string, any>;
    colorCityDetail?: { cityCode: string; cityName: string }[];
    applyCityDetail?: SelItem;
    carRegistration: {
      filePath: string;
      name: string;
      uid: string;
      url: string;
    }[];
    drivingLicense: {
      filePath: string;
      name: string;
      uid: string;
      url: string;
    }[];
  };
  disableForm?: boolean;
  // deadLine: string;
};

type SelItem = {
  label: string;
  value: number | string;
};

enum LICENSE_TYPE {
  COMPANY = 1,
  PERSONAL = 2,
}

const BindCarModalSecond: React.FC<BindCarModelProps> = (props) => {
  const { orderNo } = history.location.query;
  // const { userList } = useModel('userList');
  // const isRandom = Math.ceil(Math.random()*10)
  const { formCur, modalVisible, onVisibleChange } = props;
  const [form] = Form.useForm();
  const [showMoreDetail, handleShowMoreDetail] = useState<boolean>(false);
  const [formReset, setFormReset] = useState<Record<string, any>>({});
  const [newFormData, setNewForm] = useState({});

  const [channelNameList, setChannelNameList] = useState<SelItem[]>([]);
  const [licenseList, setLicenseList] = useState<SelItem[]>([]);
  const [licenseCityList, setLicenseCityList] = useState([]);
  const [storeList, setStoreList] = useState<SelItem[]>([]);
  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const { cityList } = useModel('cityList');
  // 库存城市下拉
  const [libraryCarOptions, handleLibraryCarOptions] = useState<SelItem[]>([]);
  const [isFirst, setIsFirst] = useState<boolean>(false);
  const access = useAccess();

  // 处理渠道类型联动渠道名称
  const handleSetOptionChannelName = (val: number) => {
    getAllChannelByChannelType(val).then((res) => {
      // console.log(res.data);
      // 联动
      const channelName = res.data.map((item: { id: string; channelName: string }) => {
        return { label: item?.channelName, value: item?.id };
      });
      setChannelNameList(channelName);
    });
  };
  //设置上牌城市
  // 设置上牌城市
  const handleSetOptionLicenseName = (channelId: string | number, licenseType: number) => {
    getCompanyList(channelId, licenseType).then((res) => {
      const list =
        res?.data?.map((item: { licenseCompany: string; id: string }) => {
          return { ...item, value: item.id, label: item.licenseCompany };
        }) || [];
      // 个户上牌需要组装可选上牌城市
      if (formCur?.licenseType === LICENSE_TYPE.PERSONAL) {
        const cityList = list[0]?.licenseCityList.map((item) => {
          return {
            label: item.licenseCity,
            value: item.licenseCityCode,
          };
        });
        // 设置上牌城市选项
        setLicenseCityList(cityList);
      }

      // 如果上牌公司只有一个，默认填中上牌公司，上牌城市
      if (list.length === 1) {
        // 只有挂靠的上牌类型才可以修改上牌方
        if (formCur?.licenseType === LICENSE_TYPE.COMPANY) {
          form.setFieldsValue({
            licenseCompany: list[0],
            carCityDetail: {
              label: list[0].licenseCity,
              value: list[0].licenseCityCode,
            },
          });
        }
      }
      // 设置上牌公司选项
      setLicenseList(list);
    });
  };
  //设置门店
  const handleSetOptionStoreName = (val: string | number) => {
    getApplyStoreList(val).then((res) => {
      const temp =
        res?.data?.map((item: { storeName: string; id: string }) => {
          return { ...item, value: item.id, label: item.storeName };
        }) || [];
      //如果门店只有一个，默认选中申请门店，申请城市
      if (temp.length === 1) {
        form.setFieldsValue({
          applyStore: temp[0],
          applyCityDetail: {
            label: temp?.[0]?.saleCity,
            value: temp?.[0]?.saleCityCode,
          },
        });
      }
      setStoreList(temp);
    });
  };

  // 处理库存城市
  const handleSetCarCity = (list: any[] = []) => {
    const listCarLibrary = list?.map((item: { cityName: string; cityCode: string }) => {
      return {
        label: item?.cityName,
        value: item?.cityName,
      };
    });

    handleLibraryCarOptions(listCarLibrary);
  };
  const [allFileList, handleFileList] = useState(formCur || {});
  useEffect(() => {
    form.setFieldsValue({
      ...formReset,
      licenseCompany:
        formCur?.licenseType === LICENSE_TYPE.PERSONAL
          ? formCur?.licenseCompany.label
          : formCur?.licenseCompany,
    });
  }, [formReset]);

  useEffect(() => {
    form.setFieldsValue({
      ...formCur,
      licenseCompany:
        formCur?.licenseType === LICENSE_TYPE.PERSONAL
          ? formCur?.licenseCompany.label
          : formCur?.licenseCompany,
    });

    if (formCur?.channelType !== undefined) {
      handleSetOptionChannelName(formCur?.channelType);
    }

    if (formCur?.channelDetail?.value !== undefined) {
      handleSetOptionLicenseName(formCur?.channelDetail?.value, formCur.licenseType);
      handleSetOptionStoreName(formCur?.channelDetail?.value);
    }

    if (formCur?.colorCityDetail) {
      handleSetCarCity(formCur?.colorCityDetail);
    }

    handleFileList({
      drivingLicense: formCur?.drivingLicense,
      carRegistration: formCur?.carRegistration,
    });
  }, [formCur]);

  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };
  return (
    <>
      <ModalForm
        title="绑定车辆"
        // width={600}
        className={globalStyle.formModal}
        form={form}
        layout="horizontal"
        visible={modalVisible}
        onVisibleChange={onVisibleChange}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            handleShowMoreDetail(false);
            setNewForm({});
            // setDisableForm(false);
            // setCurrentRow(undefined);
          },
        }}
        submitter={{
          render: (_, defaultDoms) => {
            return [
              !showMoreDetail && access.hasAccess('btn_change_carinfo_businessMng_leaseList') ? (
                <Button
                  key="update"
                  type="primary"
                  ghost
                  onClick={() => {
                    // props.submit();
                    handleShowMoreDetail(true);
                    setIsFirst(true);
                  }}
                >
                  变更车辆信息
                </Button>
              ) : null,
              ...defaultDoms,
            ];
          },
        }}
        onFinish={async (values) => {
          // 二次弹窗提示
          if (!showMoreDetail) {
            handleOptVisible(true);
            setFormReset({
              ...formCur,
              ...values,
            });
            return false;
          }
          const mapUploadFile = convertUploadFileList(allFileList, [
            'drivingLicense',
            'carRegistration',
          ]);

          let licenseCompany = values?.licenseCompany?.label;
          let licenseCompanyId = values?.licenseCompany?.value;
          if (formCur?.licenseType === LICENSE_TYPE.PERSONAL) {
            licenseCompany = formCur?.licenseCompany?.label;
            licenseCompanyId = formCur?.licenseCompany?.value;
          }

          await bindingCar({
            orderNo,
            ...values,
            ...mapUploadFile,
            totalPrice: values?.totalPrice * 100,
            preferentialPrice: values?.totalPrice * 100,
            channelId: values?.channelDetail?.value,
            licenseType: formCur?.licenseType,
            licenseCompany,
            licenseCompanyId,
            applyStore: values?.applyStore?.label,
            applyStoreId: values?.applyStore?.value,
          } as BindCarParams);
          message.success('添加成功');
          props.onOk();
          return true;
        }}
      >
        <ProForm.Group>
          <ProFormText
            name="remark"
            // labelCol={{ span: 5 }}
            width="sm"
            fieldProps={{ maxLength: 20 }}
            label="其他"
            placeholder="请输入"
            // rules={[{ required: true }]}
          />
          <ProFormDatePicker
            width="sm"
            name="registrationDateOfDrivingLicense"
            label="行驶证登记日期"
          />
        </ProForm.Group>
        <div style={{ display: 'flex', justifyContent: 'space-around' }}>
          <CommonDraggleUpload
            // extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
            label="行驶证"
            width="sm"
            title="点击或拖拽上传文件"
            description=""
            labelCol={{ span: 7 }}
            name="drivingLicense"
            max={5}
            listType="text"
            accept=".pdf,.png,.jpg,.jpeg,.bmp,.pdf"
            mapFileList={mapFileList}
            size={50}
            multiple={true}
            fileListEdit={formCur?.drivingLicense}
          />
          <CommonDraggleUpload
            // extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
            label="车辆登记证"
            width="sm"
            title="点击或拖拽上传文件"
            description=""
            labelCol={{ span: 7 }}
            name="carRegistration"
            max={5}
            listType="text"
            accept=".pdf,.png,.jpg,.jpeg,.bmp,.pdf"
            mapFileList={mapFileList}
            fileListEdit={formCur?.carRegistration}
            size={50}
            multiple={true}
          />
        </div>

        {showMoreDetail && (
          <DividerTit title="变更车辆信息">
            <ProForm.Group>
              <ProFormSelect
                rules={[{ required: true }]}
                // request={getAllChannelEnum}
                options={CHANNEL_TYPES_LABEL_MAP}
                placeholder="请选择渠道类型"
                disabled={true}
                name="channelType"
                fieldProps={{
                  onChange: (val) => {
                    form.setFieldsValue({ channelDetail: '' });
                    if (val !== undefined) {
                      // 判空
                      handleSetOptionChannelName(val);
                    } else {
                      setChannelNameList([]);
                    }
                  },
                }}
                width="sm"
                label="渠道类型"
              />
              <ProFormSelect
                rules={[{ required: true }]}
                options={channelNameList}
                placeholder="请选择渠名称"
                disabled={true}
                name="channelDetail"
                width="sm"
                label="渠道名称"
                fieldProps={{
                  showSearch: true,
                  labelInValue: true,
                  optionFilterProp: 'label',
                  onChange: (item) => {
                    // console.log(val, aa, 'hahaha');
                    //清空上牌公司，上牌城市，申请门店，申请城市
                    form.setFieldsValue({
                      licenseCompany: null,
                      applyStore: null,
                      carCityDetail: null,
                      applyCityDetail: null,
                    });
                    if (item !== undefined) {
                      // 判空
                      // handleSetOptionChannelName(val);
                      // handleSetOptionChannelName(val);
                      handleSetOptionLicenseName(item?.value, formCur?.licenseType);
                      handleSetOptionStoreName(item?.value);
                    } else {
                      //设置上牌公司，申请门店为空
                      // setChannelNameList([]);
                      setStoreList([]);
                      setLicenseList([]);
                    }
                  },
                }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                name="carNo"
                rules={[
                  {
                    required: true,
                    validator: async (_, value) => {
                      if (value && form.getFieldValue('applyCityDetail')?.value) {
                        const mesg = await validateCarCodeEqual(
                          form.getFieldValue('applyCityDetail')?.value,
                          value,
                          orderNo,
                        )
                          .then((res) => {
                            const { channelType, channelDetail, ...data } = res.data;
                            // 首次展开，信息无变更，也不用走校验接口
                            const noValidate =
                              isFirst &&
                              JSON.stringify(newFormData) === JSON.stringify({}) &&
                              formCur?.carNo === value;
                            // 对比上一次的数据是否和当前返回的数据完全一致，newFormData上一次返回的数据
                            if (
                              !(
                                JSON.stringify({ ...data, cityName: '' }) ===
                                JSON.stringify(newFormData)
                              ) &&
                              !noValidate
                            ) {
                              // 联动库存城市
                              handleSetCarCity(data?.colorCityDetail);
                              // 给最新的
                              setNewForm({
                                ...data,
                                cityName: '',
                              });
                              // 重新赋值
                              setFormReset({
                                ...data,
                                cityName: '',
                              });
                              setIsFirst(false);
                            }
                            return '';
                          })
                          .catch((err) => {
                            // setNewForm({});
                            return err.message;
                            // return Promise.reject(new Error(err));
                          });
                        // console.log(mesg);
                        return mesg ? Promise.reject(new Error(mesg)) : Promise.resolve();
                      }
                      // return Promise.reject(new Error('请输入车辆编码'));
                      if (!value) {
                        return Promise.reject(new Error('请输入车辆编码'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                fieldProps={{
                  maxLength: 20,
                  // onBlur: () => {
                  //   setFormReset(newFormData);
                  // },
                }}
                width="sm"
                label="车辆编码"
                placeholder="请输入车辆编码"
              />
              <ProFormText
                name="carModel"
                disabled
                width="sm"
                fieldProps={{ maxLength: 100 }}
                label="车型名称"
                placeholder="请输入车型名称"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                name="carUniqueCode"
                disabled
                fieldProps={{ maxLength: 20 }}
                width="sm"
                label="车辆识别代码"
                placeholder="请输入车辆识别代码"
              />
              <ProFormSelect
                name="carType"
                disabled
                options={[
                  { value: 1, label: '新车' },
                  { value: 2, label: '二手车' },
                ]}
                // fieldProps={{ maxLength: 20 }}
                width="sm"
                label="车辆类型"
                placeholder="请选择车辆类型"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                disabled
                options={[
                  { value: 1, label: '货车' },
                  { value: 2, label: '乘用车' },
                ]}
                placeholder="请选择车辆种类"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                }}
                width="sm"
                name="carCategory"
                label="车辆种类"
              />
              <ProFormSelect
                name="energyType"
                disabled
                options={[
                  { value: 1, label: '燃油' },
                  { value: 2, label: '纯电动' },
                  { value: 3, label: '油电混合' },
                  { value: 4, label: '插电式混动' },
                  { value: 5, label: '柴油' },
                ]}
                width="sm"
                label="能源类型"
                fieldProps={{
                  onChange: (val: number) => {
                    if (val === 2) {
                      form.setFieldsValue({ environmentalType: '' });
                    }
                  },
                }}
                placeholder="请选择能源类型"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                disabled
                options={[
                  { value: '4', label: '国四' },
                  { value: '5', label: '国五' },
                  { value: '6', label: '国六' },
                ]}
                placeholder="请选择环保标准"
                fieldProps={{
                  // disabled: disableEnvironmental,
                  // showSearch: true,
                  optionFilterProp: 'label',
                }}
                width="sm"
                name="environmentalType"
                label="环保标准"
              />
              <ProFormText
                name="manufacturer"
                disabled
                width="sm"
                fieldProps={{ maxLength: 20 }}
                label="厂商"
                placeholder="请输入厂商"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                name="carYears"
                disabled
                width="sm"
                options={[
                  { value: 2017, label: '2017' },
                  { value: 2018, label: '2018' },
                  { value: 2019, label: '2019' },
                  { value: 2020, label: '2020' },
                  { value: 2021, label: '2021' },
                  { value: 2022, label: '2022' },
                  { value: '-', label: '-' },
                ]}
                placeholder="请选择年代款"
                label="年代款"
              />
              <ProFormText
                name="carEmission"
                disabled
                width="sm"
                fieldProps={{ maxLength: 20 }}
                label="排量"
                placeholder="请输入排量"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormDigit
                label="座位数"
                disabled
                name="seatsNum"
                width="sm"
                placeholder="请输入座位数"
                min={1}
              />
              <ProFormText
                name="color"
                label="颜色"
                disabled
                width="sm"
                placeholder="请输入颜色"
                fieldProps={{ maxLength: 20 }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                name="otherMsg"
                disabled
                width="sm"
                fieldProps={{ maxLength: 20 }}
                label="其他"
                placeholder="请输入其他"
              />
              <ProFormText
                name="carModelCode"
                disabled
                fieldProps={{ maxLength: 20 }}
                width="sm"
                label="车型码"
                placeholder="请输入车型码"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                label="发动机号"
                disabled
                name="engineCode"
                // fieldProps={{ maxLength: 20 }}
                width="sm"
                placeholder="请输入发动机号"
              />
              <ProFormDatePicker
                name="firstRegisterDate"
                disabled
                width="sm"
                placeholder="请输入首次上牌日期"
                label="首次上牌日期"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormDigit
                name="mileage"
                width="sm"
                label="里程数（KM）"
                disabled
                placeholder="请输入里程数"
                fieldProps={{ min: 0, precision: 2 }}
              />
              <ProFormText
                label="车牌"
                disabled
                name="licenseCode"
                fieldProps={{ maxLength: 20 }}
                width="sm"
                placeholder="请输入车牌"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormDigit
                name="totalPrice"
                disabled
                width="sm"
                label="全包价（元）"
                placeholder="请输入全包价"
                fieldProps={{ min: 0, precision: 2 }}
              />
              <ProFormSelect
                rules={[{ required: true }]}
                // request={async () => cityListNew}
                options={libraryCarOptions}
                // disabled={disableForm}
                name="cityName"
                placeholder="请选择库存城市"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                  // labelInValue: true,
                }}
                width="sm"
                label="库存城市"
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormText
                disabled
                name="preferentialPrice"
                width="sm"
                label="优惠金额（元）"
                placeholder="请输入优惠金额"
                // fieldProps={{ min: 0, precision: 2 }}
              />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormSelect
                // disabled={disableForm}
                rules={[{ required: true }]}
                options={storeList}
                name="applyStore"
                width="sm"
                placeholder="请选择申请门店"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                  labelInValue: true,
                  onChange: (item) => {
                    if (item?.value) {
                      form.setFieldsValue({
                        applyCityDetail: {
                          label: item?.saleCity,
                          value: item?.saleCityCode,
                        },
                      });
                    } else {
                      form.setFieldsValue({
                        applyStore: null,
                        applyCityDetail: null,
                      });
                    }
                  },
                  // mode: 'multiple',
                }}
                label="申请门店"
              />
              <ProFormSelect
                rules={[{ required: true }]}
                request={async () => cityList}
                placeholder="请选择申请城市"
                // disabled={disableForm}
                name="applyCityDetail"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                  labelInValue: true,
                  // mode: 'multiple',
                  onChange: (item) => {
                    if (item?.value && form?.getFieldValue('carNo')) {
                      getPromotionByCarNoAndApplyCityCode({
                        applyCityCode: item.value,
                        carNo: form?.getFieldValue('carNo'),
                      }).then((res) => {
                        form?.setFieldsValue({
                          preferentialPrice: res.data?.discount || '-',
                        });
                        form.validateFields(['carNo']);
                      });
                    }
                  },
                }}
                width="sm"
                label="申请城市"
              />
            </ProForm.Group>
            <ProForm.Group>
              {formCur?.licenseType === LICENSE_TYPE.PERSONAL && (
                <ProFormText name="licenseCompany" label="上牌方" disabled />
              )}
              {formCur?.licenseType === LICENSE_TYPE.COMPANY && (
                <ProFormSelect
                  // disabled={disableForm}
                  rules={[{ required: true }]}
                  options={licenseList}
                  name="licenseCompany"
                  width="sm"
                  fieldProps={{
                    showSearch: true,
                    optionFilterProp: 'label',
                    labelInValue: true,
                    onChange: (item) => {
                      if (item?.value) {
                        form.setFieldsValue({
                          carCityDetail: {
                            label: item?.licenseCity,
                            value: item?.licenseCityCode,
                          },
                        });
                      } else {
                        form.setFieldsValue({
                          carCityDetail: null,
                          licenseCompany: null,
                        });
                      }
                    },
                    // mode: 'multiple',
                  }}
                  placeholder="请选择上牌公司"
                  label="上牌公司"
                />
              )}
              <ProFormSelect
                rules={[{ required: true }]}
                name="carCityDetail"
                width="sm"
                label="上牌城市"
                placeholder="请选择上牌城市"
                disabled={formCur?.licenseType === LICENSE_TYPE.COMPANY}
                fieldProps={{
                  optionFilterProp: 'label',
                  labelInValue: true,
                  options: licenseCityList,
                }}
              />
            </ProForm.Group>
          </DividerTit>
        )}
      </ModalForm>

      <ModalForm
        title="是否确认车辆信息无误？"
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
          okText: '我已确认无误',
        }}
        visible={optVisible}
        onVisibleChange={handleOptVisible}
        onFinish={async () => {
          const mapUploadFile = convertUploadFileList(allFileList, [
            'drivingLicense',
            'carRegistration',
          ]);
          let licenseCompany = formReset?.licenseCompany?.label;
          let licenseCompanyId = formReset?.licenseCompany?.value;
          if (formCur?.licenseType === LICENSE_TYPE.PERSONAL) {
            licenseCompany = formCur?.licenseCompany?.label;
            licenseCompanyId = formCur?.licenseCompany?.value;
          }
          await bindingCar({
            orderNo,
            ...formReset,
            ...mapUploadFile,
            channelId: formReset?.channelDetail?.value,
            licenseType: formCur?.licenseType,
            licenseCompany,
            licenseCompanyId,
            applyStore: formReset?.applyStore?.label,
            applyStoreId: formReset?.applyStore?.value,
          } as BindCarParams);
          message.success('操作成功');
          props.onOk();
          return true;
        }}
      >
        <div>
          <p>如正确无误，车辆信息将录入电子合同</p>
          <p>
            如有变更，请
            <a
              onClick={() => {
                handleOptVisible(false);
                handleShowMoreDetail(true);
                setIsFirst(true);
              }}
            >
              变更车辆信息
            </a>
          </p>
        </div>
      </ModalForm>
    </>
  );
};

export default BindCarModalSecond;
