/*
 * @Author: your name
 * @Date: 2021-03-20 15:48:31
 * @LastEditTime: 2022-11-29 16:35:09
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalMng/components/quota-detail.tsx
 */
import React from 'react';
import ProTable from '@ant-design/pro-table';
import type { QuotaDetailItem } from '../data';
import { getQuotaDetail } from '../service';

interface PropsItem {
  userNo: string;
  subAccountNo: string;
}

const QuotaDetail: React.FC<PropsItem> = (props) => {
  const columns = [
    {
      title: '总额度',
      width: 150,
      dataIndex: 'creditAmount',
      key: 'creditAmount',
    },
    {
      title: '调整额度',
      width: 150,
      dataIndex: 'operatorAmount',
      key: 'operatorAmount',
    },
    {
      title: '操作类型',
      width: 150,
      dataIndex: 'operatorType',
      key: 'operatorType',
      valueEnum: {
        0: { text: '冻结' },
        1: { text: '解冻' },
        2: { text: '提额' },
        3: { text: '降额' },
        4: { text: '开户' },
        62: { text: '再次授信' },
      },
    },
    // {
    //   title: '冻结额度',
    //   width: 150,
    //   dataIndex: 'creditAmount',
    //   key: 'operatorAmount',
    // },
    {
      title: '创建时间',
      width: 200,
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '更新时间',
      width: 200,
      dataIndex: 'createTime',
      key: 'createTime',
    },
  ];

  return (
    <ProTable<QuotaDetailItem>
      columns={columns}
      rowKey="createTime"
      search={false}
      scroll={{ y: 300 }}
      toolBarRender={false}
      request={(params) =>
        getQuotaDetail({ ...params, userNo: props.userNo, subAccountNo: props.subAccountNo })
      }
    />
  );
};

export default QuotaDetail;
