/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-02-19 15:59:20
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-02-19 18:15:05
 * @FilePath: /code/lala-finance-biz-web/src/components/QrViewer/index.tsx
 * @Description: QrViewer
 */
import globalStyle from '@/global.less';
import { getBlob, saveAs } from '@/utils/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-form';
import { message, Image } from 'antd';
import React from 'react';

export const QrViewer = (props: {
  qrVisible: boolean;
  handleQRVisible: (status: boolean) => void;
  title?: string;
  data: any;
}) => {
  const { data, qrVisible, handleQRVisible, title } = props;
  const qrCodeAddress = data?.signQrCodeUrl || '';
  return (
    <>
      <ModalForm
        title={title || '获取二维码'}
        width="400px"
        modalProps={{
          centered: true,
          okText: (
            <>
              <span>下载</span>
              <DownloadOutlined />
            </>
          ),
          destroyOnClose: true,
        }}
        visible={qrVisible}
        onVisibleChange={handleQRVisible}
        onFinish={async () => {
          await getBlob(qrCodeAddress, (blob: Blob) => {
            saveAs(
              blob,
              `${data?.userName}（手机尾号${data?.phone?.substring(
                data.phone.length - 4,
              )})签约二维码`,
            );
          });
          message.success(`下载成功成功`);
          handleQRVisible(false);
        }}
      >
        <div className={globalStyle?.textCenter}>
          <Image
            width={300}
            preview={false}
            crossOrigin="anonymous"
            height={300}
            src={qrCodeAddress}
            placeholder={<Image preview={false} src={qrCodeAddress} width={300} />}
          />
          <div>
            仅限用于：{data?.userName}（手机尾号{data?.phone?.substring(data.phone.length - 4)}
            ）签约
          </div>
          <div style={{ color: 'red' }}>其他客户使用会签约失败</div>
          <div style={{ color: 'gray', opacity: 0.5, marginTop: 10 }}>签约后及时刷新该页面</div>
        </div>
      </ModalForm>
    </>
  );
};
