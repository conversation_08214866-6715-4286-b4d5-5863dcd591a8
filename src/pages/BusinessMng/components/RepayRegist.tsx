import React from 'react';
import ProTable from '@ant-design/pro-table';
import type { RepayRegistItem } from '../data';
import { getRepayRegist } from '../service';

interface PropsItem {
  planNo: string;
}

const RepayRegist: React.FC<PropsItem> = (props) => {
  const columns = [
    {
      title: '还款流水号',
      dataIndex: 'recordingNo',
      key: 'recordingNo',
    },
    {
      title: '实际还款总金额',
      dataIndex: 'actualRepaymentAmount',
      key: 'actualRepaymentAmount',
    },
    {
      title: '实际还款本金',
      dataIndex: 'principal',
      key: 'principal',
    },
    {
      title: '实际还款利息',
      dataIndex: 'interest',
      key: 'interest',
    },
    {
      title: '实际还款罚息',
      dataIndex: 'penaltyInterest',
      key: 'penaltyInterest',
    },
    {
      title: '实际还款费用',
      dataIndex: 'cost',
      key: 'cost',
    },
    {
      title: '还款方式',
      dataIndex: 'repayType',
      key: 'repayType',
    },
    {
      title: '还款时间',
      dataIndex: 'repayTime',
      key: 'repayTime',
    },
  ];

  return (
    <ProTable<RepayRegistItem>
      columns={columns}
      rowKey="recordingNo"
      search={false}
      scroll={{ x: 'max-content' }}
      toolBarRender={false}
      request={() => getRepayRegist(props.planNo)}
      pagination={false}
    />
  );
};

export default RepayRegist;
