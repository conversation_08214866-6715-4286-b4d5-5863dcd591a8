import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
import globalStyle from '@/global.less';
import LeaseIncoming from './ProcessDetail/LeaseIncoming';
import Loan from './ProcessDetail/Loan';
import Overdue from './ProcessDetail/Overdue';
import SettleEarly from './ProcessDetail/SettleEarly';
import PayBack from './ProcessDetail/PayBack';
import Claim from './ProcessDetail/Claim';
import Bill from './ProcessDetail/Bill';

type ProLeaseProcessConfigProps = {
  isAddProduct?: boolean;
  data?: any;
  type?: any;
  handleProcessTabsChanges?: (
    tabItemKey?: string,
    changedFormValues?: any,
    allFormValues?: any,
  ) => void;
  finalErrorTab?: string;
  drawerStatus?: boolean;
};
const ProLeaseProcessConfig: React.FC<ProLeaseProcessConfigProps> = ({
  data,
  isAddProduct,
  type,
  // handleProcessTabsChanges,
  finalErrorTab,
  drawerStatus = true,
}) => {
  const [activeKey, setActiveTabKey] = useState<string>('leaseIncoming');
  useEffect(() => {
    setActiveTabKey(finalErrorTab || 'leaseIncoming');
  }, [finalErrorTab]);
  return (
    <Card title="产品流程配置" className={globalStyle.mt30}>
      <Tabs
        activeKey={activeKey}
        onTabClick={(key) => {
          setActiveTabKey(key);
        }}
      >
        <Tabs.TabPane tab="进件" key="leaseIncoming" forceRender>
          <LeaseIncoming
            name="leaseIncoming"
            drawerStatus={drawerStatus}
            activateDTO={data?.activateDTO}
            isAddProduct={isAddProduct}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="放款" key="loan" forceRender>
          <Loan name="loan" loanData={data?.loanDTO} drawerStatus={drawerStatus} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="账单" key="bill" forceRender>
          <Bill name="bill" billData={data?.billDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="债权转让" key="claim" forceRender>
          <Claim name="claim" claimData={data?.debentureTransferDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="还款" key="payback" forceRender>
          <PayBack
            payBackData={data?.repaymentDTO}
            isLease
            drawerStatus={drawerStatus}
            name="payback"
            type={type}
            isAddProduct={isAddProduct}
            repayDateCalculateEnum={data?.billDTO?.repayDateCalculateEnum}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="提前结清" key="settleEarly" forceRender>
          <SettleEarly settleEarlyData={data?.inAdvanceRepaymentDTO} name="settleEarly" />
        </Tabs.TabPane>
        <Tabs.TabPane tab="逾期" key="overdue" forceRender>
          <Overdue overDueData={data?.overdueDTO} isLease name="overdue" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};

export default React.memo(ProLeaseProcessConfig);
