import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-components';
import { Radio } from 'antd';
import React, { useState } from 'react';
import BuyBack from './BuyBack';
import Pay from './Pay';

enum TAB_KEYS {
  PAY = 'pay',
  BUY_BACK = 'buyBack',
}
const PayBuyBack = () => {
  const [currentTab, setCurrentTab] = useState(TAB_KEYS.PAY);
  const tabItems = [
    {
      key: TAB_KEYS.PAY,
      label: '垫付',
    },
    {
      key: TAB_KEYS.BUY_BACK,
      label: '回购',
    },
  ];

  const onChange = (e: any) => {
    setCurrentTab(e.target.value);
  };

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Radio.Group
          onChange={onChange}
          value={currentTab}
          buttonStyle="solid"
          style={{ marginBottom: 16 }}
        >
          {tabItems.map((item) => {
            return (
              <Radio.Button key={item.key} value={item.key}>
                {item.label}
              </Radio.Button>
            );
          })}
        </Radio.Group>
        <Pay show={currentTab === TAB_KEYS.PAY} />
        <BuyBack show={currentTab === TAB_KEYS.BUY_BACK} />
      </PageContainer>
    </>
  );
};

export default PayBuyBack;
