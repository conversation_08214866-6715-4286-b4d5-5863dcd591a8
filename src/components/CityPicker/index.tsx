import { getCityListEnum } from '@/services/map';
import { mergeProps } from '@/utils/with-default-props';
import type { CascaderProps } from 'antd';
import { Cascader } from 'antd';
import { useEffect, useState } from 'react';

export type CascadePickerOption = {
  label: string;
  value: string;
  children?: CascadePickerOption[];
};

export type CityPickerProps = Omit<CascaderProps, 'options'> & {
  level?: number;
};

const defaultProps = {
  placeholder: '请选择',
  level: 3,
};

const CityPicker = (p: CityPickerProps) => {
  const props = mergeProps(defaultProps, p);
  const [cityOptions, setCityOptions] = useState<CascadePickerOption[]>([]);

  const initCityOptions = async () => {
    const data = await getCityListEnum({ inLabel: true, level: props.level });
    setCityOptions(data);
  };

  useEffect(() => {
    initCityOptions();
  }, []);

  return <Cascader {...props} options={cityOptions} />;
};

export default CityPicker;
