/* eslint-disable react-hooks/exhaustive-deps */
import HeaderTab from '@/components/HeaderTab';
import { LENDING_MODEL_ENUM } from '@/enums';
import { downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import {
  Button,
  Card,
  Descriptions,
  Drawer,
  Form,
  List,
  message,
  Popconfirm,
  Spin,
  Table,
} from 'antd';
import React, { memo, useEffect, useRef, useState } from 'react';
import LoanCashInfo from './components/LoanCashInfo';
import LoanFlowDescription from './components/LoanFlowDescription/LoanFlowDescription';
import {
  exportCarLendingInfo,
  getAuditLogs,
  getCarIncommingInfo,
  getCarLendingDetail,
  getCarLendingInfo,
  getCarRepayment,
  passCarLendingInfo,
  rejectCarLendingInfo,
  submitCarLendingInfo,
} from './service';
import { loanModelMap, loanStatusMap, loanTypeMap } from './types/map';

const LoanCarDetail = () => {
  const [vinList, setVinList] = useState<string[]>([]);
  const [vinModalVisible, setVinModalVisible] = useState<boolean>(false);

  const actionRef = useRef();

  const { orderNo, lendingNo, productCode } = history.location.query as {
    orderNo: string;
    lendingNo: string;
    productCode: string;
  };
  const columns = [
    { dataIndex: 'termDetail', title: '期数' },
    { dataIndex: 'amountDue', title: '应还总额' },
    { dataIndex: 'principal', title: '应还本金' },
    { dataIndex: 'interest', title: '应还利息' },
    { dataIndex: 'cost', title: '费用' },
    { dataIndex: 'repayTime', title: '应还日期' },
  ];
  const incomingOptions = [
    { dataIndex: 'orderNo', label: '订单号' },
    { dataIndex: 'userNo', label: '用户ID' },
    { dataIndex: 'userName', label: '用户名称' },
    { dataIndex: 'productName', label: '申请产品' },
    { dataIndex: 'channel', label: '渠道商户' },
    { dataIndex: 'applyAmount', label: '申请金额' },
    { dataIndex: 'loanTerm', label: '期限' },
    { dataIndex: 'repayType', label: '还款方式' },
    {
      dataIndex: 'interestRate',
      label: '年利率',
      render(value: number) {
        return value * 100 + '%';
      },
    },
    { dataIndex: 'createdAt', label: '进件时间', span: 3 },
  ];
  const loanOptions = [
    { dataIndex: 'lendingNo', label: '放款流水号' },
    {
      dataIndex: 'type',
      label: '放款类型',
      render: (value: string) => loanTypeMap[value],
    },
    { dataIndex: 'amount', label: '放款金额' },
    { dataIndex: 'lendingMaster', label: '放款主体' },
    { dataIndex: 'receiptMaster', label: '收款主体' },
    // { dataIndex: 'channel', label: '放款周期' },
    {
      dataIndex: 'lendingModel',
      label: '放款方式',
      render: (value: string) => loanModelMap[value],
    },
    {
      dataIndex: 'fundFlow',
      label: '是否发生资金流',
      render(value: any) {
        return value == '1' || value === true ? '是' : '否';
      },
    },
    { dataIndex: 'lendingTime', label: '放款时间' },
    {
      dataIndex: 'status',
      label: '放款状态',
      render: (value: string) => loanStatusMap[value],
    },
    {
      dataIndex: 'vin',
      label: '车架号',
      render: (value: string) => {
        if (!value) return '-';
        const vins = value?.split(',');
        if (vins?.length === 1) return vins?.[0] || '-';
        if (vins?.length > 1) {
          return (
            <div>
              {vins?.[0]}
              <span
                style={{ color: '#1677ff', cursor: 'pointer' }}
                onClick={() => {
                  setVinList(vins);
                  setVinModalVisible(true);
                }}
              >
                ...查看更多
              </span>
            </div>
          );
        }
        return '-';
      },
    },
  ];
  // 获取请款进度
  const { data: auditLogs, run: runAuditLogs } = useRequest(() => {
    return getAuditLogs(lendingNo);
  });
  const refresh = () => {
    runAuditLogs();
  };
  const [inCommingInfo, setInCommingInfo] = useState<any>({});
  const [dataLending, setDataLending] = useState<any>({});
  const [repayment, setRepayment] = useState<any>([]);
  const [loanDetail, setLoanDetail] = useState<any>([]);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  const [lendingLoading, setLendingLoading] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false); // 提交放款信息
  const [uploadLoading, setUploadLoading] = useState<boolean>(false); // 上传放款凭证
  const [editForm] = Form.useForm();

  async function init() {
    setLoading(true);
    setLendingLoading(true);
    // 不用await 为了同时发送
    getCarIncommingInfo(orderNo).then(setInCommingInfo);
    getCarLendingInfo(lendingNo)
      .then(setDataLending)
      .finally(() => {
        setLoading(false);
      });
    getCarRepayment(orderNo).then((data) => {
      const { listRspList } = data;
      const normalListRspList = Array.isArray(listRspList) ? listRspList : [];
      const repayment1 = {
        ...normalListRspList.find((item) => item.id === null),
        children: normalListRspList.filter((item) => item.id),
      };
      setRepayment([repayment1]);
    });
    getCarLendingDetail(lendingNo, orderNo)
      .then((data) => {
        setLoanDetail(data?.data ?? []);
      })
      .finally(() => {
        setLendingLoading(false);
      });
  }

  // 导出车险分期放款明细
  function exportLoanDetail() {
    setExportLoading(true);
    exportCarLendingInfo(lendingNo, orderNo)
      .then((data) => {
        downLoadExcel(data);
      })
      .finally(() => {
        setExportLoading(false);
      });
  }

  const loanDetailColumns = [
    {
      dataIndex: 'vin',
      title: '车架号',
    },
    {
      dataIndex: 'amount',
      title: '放款金额',
    },
  ];

  // 放款方式为线上时不展示按钮
  const hiddenButton = dataLending?.lendingModel !== LENDING_MODEL_ENUM.OFFLINE;
  // 是否展示放款信息 线下还款且当放款类型为委托投保且金融放款成功或放款类型为金融放款时，展示放款信息
  const isShowLoanInfo =
    !hiddenButton &&
    ((dataLending?.type === 6 && dataLending?.isFinLendingSuccess) || dataLending?.type === 5);

  const loanFlowDescriptionRef = useRef<any>(null);

  // 放款信息extra按钮
  const loanDetailExtraButtonMap = {
    1: [
      // 待放款状态展示按钮
      <Button
        key="submit"
        loading={loading}
        type="primary"
        hidden={hiddenButton}
        onClick={() => {
          // 如果正在提交或上传放款凭证，则不进行提交, 以防多次提交
          if (loading || uploadLoading) return;
          editForm
            .validateFields()
            .then((values) => {
              const formatValues = {
                ...values,
                lendingTime: values?.lendingTime?.format('YYYY-MM-DD HH:mm:ss'),
                extend: JSON.stringify({
                  lendingCertificateList: values?.extend,
                }),
                orderNo,
                lendingNo,
              };
              setLoading(true);
              submitCarLendingInfo(formatValues)
                .then(() => {
                  message.success('提交成功');
                  // 更新页面
                  init();
                  loanFlowDescriptionRef.current?.reset();
                })
                .finally(() => {
                  setLoading(false);
                });
            })
            .catch((error) => {
              console.log('error', error);
            });
        }}
      >
        提交
      </Button>,
    ],
    14: [
      // 待审核状态展示按钮
      <Popconfirm
        key="reject"
        title="确定驳回吗？"
        onConfirm={() => {
          if (loading) return;
          setLoading(true);
          rejectCarLendingInfo({
            orderNo,
            lendingNo,
          })
            .then(() => {
              message.success('操作成功');
              // 调用驳回接口 并更新页面数据
              init();
              loanFlowDescriptionRef.current?.reset();
            })
            .finally(() => {
              setLoading(false);
            });
        }}
      >
        <Button type="primary" danger hidden={hiddenButton} loading={loading}>
          驳回
        </Button>
      </Popconfirm>,
      <Popconfirm
        key="pass"
        title="确定通过吗？"
        onConfirm={() => {
          if (loading) return;
          setLoading(true);
          passCarLendingInfo({
            orderNo,
            lendingNo,
          })
            .then(() => {
              message.success('操作成功');
              // 调用通过接口 并更新页面数据
              init();
              loanFlowDescriptionRef.current?.reset();
            })
            .finally(() => {
              setLoading(false);
            });
        }}
      >
        <Button
          type="primary"
          style={{ marginLeft: '10px' }}
          hidden={hiddenButton}
          loading={loading}
        >
          通过
        </Button>
      </Popconfirm>,
    ],
  };

  useEffect(() => {
    if (orderNo) {
      init();
    } else {
      message.error('订单号不存在');
    }
  }, [orderNo]);
  return (
    <Spin spinning={loading}>
      <HeaderTab />
      <PageContainer>
        <Card title="进件信息" style={{ marginBottom: 20 }}>
          <Descriptions>
            {incomingOptions.map((item) => {
              const { label, dataIndex } = item;
              return (
                <Descriptions.Item label={label} key={dataIndex}>
                  {inCommingInfo[dataIndex]}
                </Descriptions.Item>
              );
            })}
            <Descriptions.Item label={'还款计划'} span={3}>
              <Table columns={columns} dataSource={repayment} pagination={false} />
            </Descriptions.Item>
          </Descriptions>
        </Card>
        {/* Todo： 当金融放款未走到放款成功前，需屏蔽按钮和新增三字段 */}
        <Card
          title="放款信息"
          extra={isShowLoanInfo ? loanDetailExtraButtonMap[dataLending?.status] : null}
        >
          <Descriptions>
            {loanOptions.map((item) => {
              const { label, dataIndex, render } = item;
              return (
                <Descriptions.Item label={label} key={dataIndex}>
                  {render ? render(dataLending[dataIndex]) : dataLending[dataIndex]}
                </Descriptions.Item>
              );
            })}
          </Descriptions>
          {/* 提交放款流水信息 */}
          <LoanFlowDescription
            ref={loanFlowDescriptionRef}
            actionRef={actionRef}
            dataLending={dataLending}
            editForm={editForm}
            updateLoading={setUploadLoading}
            show={isShowLoanInfo} // 委托投保时，需要金融放款成功才能展示，金融放款不影响展示
          />

          <LoanCashInfo
            productCode={productCode}
            isCarInsurance={true}
            dataLending={dataLending}
            userNo={inCommingInfo?.userNo}
            auditLogs={auditLogs}
            lendingNo={lendingNo}
            refresh={refresh}
          />
        </Card>
        <Card
          hidden={!lendingLoading && !loanDetail.length} // 加个loading数据加载会丝滑一点
          title="放款明细"
          extra={
            <Button loading={exportLoading} type="primary" onClick={exportLoanDetail}>
              导出
            </Button>
          }
        >
          <Table key="vin" columns={loanDetailColumns} dataSource={loanDetail} />
        </Card>
        <Drawer
          title="车架号详情"
          open={vinModalVisible}
          onClose={() => {
            setVinModalVisible(false);
            setVinList([]);
          }}
        >
          <List
            header={<strong>数量：{vinList?.length || 0}</strong>}
            dataSource={vinList}
            renderItem={(item) => <List.Item>{item}</List.Item>}
          />
        </Drawer>
      </PageContainer>
    </Spin>
  );
};
export default memo(LoanCarDetail);
