export type BeforeUploadFileType = File | Blob | boolean | string;

export type Action = string | ((file: RcFile) => string | PromiseLike<string>);
export interface UploadProgressEvent extends Partial<ProgressEvent> {
  percent?: number;
}
export type UploadRequestMethod = 'POST' | 'PUT' | 'PATCH' | 'post' | 'put' | 'patch';

export type UploadRequestHeader = Record<string, string>;

export interface UploadRequestError extends Error {
  status?: number;
  method?: UploadRequestMethod;
  url?: string;
}

export interface UploadRequestOption<T = any> {
  onProgress?: (event: UploadProgressEvent) => void;
  onError?: (event: UploadRequestError | ProgressEvent, body?: T) => void;
  onSuccess?: (body: T, xhr?: XMLHttpRequest) => void;
  data?: Record<string, unknown>;
  filename?: string;
  file: Exclude<BeforeUploadFileType, File | boolean> | RcFile;
  withCredentials?: boolean;
  action: string;
  headers?: UploadRequestHeader;
  method: UploadRequestMethod;
}

export interface RcFile extends File {
  uid: string;
}

export function getError(option: UploadRequestOption, xhr: XMLHttpRequest) {
  const msg = `cannot ${option.method} ${option.action} ${xhr.status}'`;
  const err = new Error(msg) as UploadRequestError;
  err.status = xhr.status;
  err.method = option.method;
  err.url = option.action;
  return err;
}

function getBody(xhr: XMLHttpRequest) {
  const text = xhr.responseText || xhr.response;
  if (!text) {
    return text;
  }

  try {
    return JSON.parse(text);
  } catch (e) {
    return text;
  }
}

export default function customUpload(
  option: UploadRequestOption,
  callback?: (file: RcFile | Blob, response: any) => Promise<{ res: any }>,
  customAbort?: () => void,
) {
  const xhr = new XMLHttpRequest();

  if (option.onProgress && xhr.upload) {
    xhr.upload.onprogress = function progress(e: UploadProgressEvent) {
      if (e.total && e.loaded && e.total > 0) {
        if (callback) {
          e.percent = (e.loaded / e.total) * 50;
        } else {
          e.percent = (e.loaded / e.total) * 100;
        }
      }
      if (option.onProgress) {
        option.onProgress(e);
      }
    };
  }

  // eslint-disable-next-line no-undef
  const formData = new FormData();

  if (option.data) {
    Object.keys(option.data).forEach((key) => {
      const value = option.data?.[key];
      // support key-value array data
      if (Array.isArray(value)) {
        value.forEach((item) => {
          formData.append(`${key}[]`, item);
        });
        return;
      }

      formData.append(key, value as string | Blob);
    });
  }

  if (option.file instanceof Blob) {
    formData.append(option.filename!, option.file, (option.file as any).name);
  } else {
    formData.append(option.filename!, option.file);
  }

  xhr.onerror = function error(e) {
    option.onError?.(e);
  };

  xhr.onload = async function onload() {
    // allow success when 2xx status
    // see https://github.com/react-component/upload/issues/34
    if (xhr.status < 200 || xhr.status >= 300) {
      return option.onError?.(getError(option, xhr), getBody(xhr));
    } else if (callback) {
      const { res } = await callback(option.file as RcFile, getBody(xhr));
      // 如果ocr识别失败，则返回错误
      if (!res?.success) {
        return option.onError?.(new Error(res?.message || 'OCR识别失败'), res);
      }
      // 如果ocr识别成功，则返回成功
      return option.onSuccess?.(getBody(xhr), xhr);
    }
    return option.onSuccess?.(getBody(xhr), xhr);
  };

  xhr.open(option.method, option.action, true);

  // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179
  if (option.withCredentials && 'withCredentials' in xhr) {
    xhr.withCredentials = true;
  }

  const headers = option.headers || {};

  // when set headers['X-Requested-With'] = null , can close default XHR header
  // see https://github.com/react-component/upload/issues/33
  if (headers['X-Requested-With'] !== null) {
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
  }

  Object.keys(headers).forEach((h) => {
    if (headers[h] !== null) {
      xhr.setRequestHeader(h, headers[h]);
    }
  });

  xhr.send(formData);

  // 返回一个对象，包含一个abort方法，用于取消请求, 提供给antd-pro的upload组件使用
  return {
    abort() {
      xhr.abort();
      customAbort?.();
    },
  };
}
