/*
 * @Author: your name
 * @Date: 2021-04-12 15:18:02
 * @LastEditTime: 2024-04-01 17:54:03
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/BindNewCar.tsx
 */
import { CommonDraggleUpload } from '@/components/ReleaseCom';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import type { OtherDeliveryParams } from '../data';
import { addOtherDeliveryInfo } from '../service';

export type AddOtherDeliveryModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  otherRemark?: string;
  formEdit?: Record<string, any>;
};

// 其他交车资料上传路径
enum DELIVER_OTHER_TYPE {
  H5 = 1,
  BIZ_ADMIN = 2,
}

const AddOtherDeliveryModal: React.FC<AddOtherDeliveryModalProps> = (props) => {
  const { orderNo }: any = history.location.query;
  const { formEdit } = props;
  const [form] = Form.useForm();
  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  const [fileListEdit, setFileListEidt] = useState<[]>([]);

  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  useEffect(() => {
    const filterList =
      formEdit?.otherDeliveryDataPhotoList?.filter(
        (item: { type: DELIVER_OTHER_TYPE }) => item.type === DELIVER_OTHER_TYPE.BIZ_ADMIN,
      ) || [];
    const fileList = filterList?.map((item: { otherDeliveryDataPhoto: []; name: string }) => {
      // console.log(item);
      return {
        url: item.otherDeliveryDataPhoto,
        name: item.name,
      };
    });
    handleFileList({ otherDeliveryDataPhotoList: fileList });
    setFileListEidt(fileList);
    // console.log(fileList, formEdit);
    form.setFieldsValue({
      otherDeliveryDataPhotoList: fileList,
    });
    // console.log(formEdit);
    return () => {
      handleFileList({});
    };
  }, [formEdit]);

  return (
    <>
      <ModalForm
        title="交车资料"
        width={800}
        layout="horizontal"
        visible={props.modalVisible}
        onVisibleChange={props.onVisibleChange}
        form={form}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            handleFileList({});
          },
        }}
        onFinish={async (values) => {
          try {
            const mapUploadFile = allFileList.otherDeliveryDataPhotoList.map((item: any) => {
              return {
                otherDeliveryDataPhoto: item.response?.data?.netWorkPath || item?.url || '',
                type: 2, //1. 用户上传 2.后台上传
                name: item.name,
              };
            });
            // console.log(mapUploadFile);
            // 组合所有类型图片
            const h5FileList =
              formEdit?.otherDeliveryDataPhotoList?.filter(
                (item: { type: DELIVER_OTHER_TYPE }) => item.type === DELIVER_OTHER_TYPE.H5,
              ) || [];
            const allFile = [...h5FileList, ...mapUploadFile];
            await addOtherDeliveryInfo({
              orderNo,
              remark: values.remark,
              otherDeliveryDataPhotoList: allFile,
            } as OtherDeliveryParams);
            message.success('添加成功');
            props.onOk();

            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <CommonDraggleUpload
          extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx.bmp.rar单个文件最大50M"
          label="其他交车资料"
          labelCol={{ span: 4 }}
          name="otherDeliveryDataPhotoList"
          max={5}
          size={50}
          fileListEdit={fileListEdit}
          listType="text"
          multiple={true}
          mapFileList={mapFileList}
          accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
          rules={[{ required: true, message: '其他交车资料' }]}
        />
        <ProFormText
          name="remark"
          labelCol={{ span: 4 }}
          width="md"
          fieldProps={{ maxLength: 20 }}
          label="其他"
          placeholder="请输入"
          // rules={[{ required: true }]}
        />
      </ModalForm>
    </>
  );
};

export default AddOtherDeliveryModal;
