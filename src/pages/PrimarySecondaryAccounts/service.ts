/*
 * @Date: 2023-08-29 16:45:21
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-09-05 16:40:56
 * @FilePath: /lala-finance-biz-web/src/pages/PrimarySecondaryAccounts/service.ts
 * @Description:
 */

import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { PrimarySecondaryAccountsParams } from './data';

export async function getPrimarySecondaryAccountsList(data: PrimarySecondaryAccountsParams) {
  return request(`/bizadmin/config/share/enterprise/queryList`, {
    method: 'POST',
    data,
    headers,
  });
}

export async function addPrimarySecondaryAccounts(
  data: Omit<PrimarySecondaryAccountsParams, 'current' | 'pageSize'>,
) {
  return request(`/bizadmin/config/share/enterprise/add `, {
    method: 'POST',
    data,
    headers,
  });
}

//冻结
export async function freezePrimarySecondaryAccounts(id: string) {
  return request(`/bizadmin/config/share/enterprise/freeze`, {
    method: 'POST',
    data: { id },
    headers,
  });
}

//解冻
export async function unfreezePrimarySecondaryAccounts(id: string) {
  return request(`/bizadmin/config/share/enterprise/unfreeze`, {
    method: 'POST',
    data: { id },
    headers,
  });
}
