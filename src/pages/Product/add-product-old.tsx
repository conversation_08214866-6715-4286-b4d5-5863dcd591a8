/* eslint-disable react-hooks/exhaustive-deps */
import globalStyle from '@/global.less';
import { UnlockOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Button, Form, message } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import BasicInfo from './components/BasicInfo';
import LeaseFeeConfig from './components/LeaseFeeConfig';
import OtherInfo from './components/OtherInfo';
import ProLeaseProcessConfig from './components/ProLeaseProcessConfig';
import { addProduct } from './service';

enum FORM_NAME_DATA_NAME {
  leaseIncoming = 'activateDTO',
  loan = 'loanDTO',
  bill = 'billDTO',
  claim = 'debentureTransferDTO',
  payback = 'repaymentDTO',
  settleEarly = 'inAdvanceRepaymentDTO',
  overdue = 'overdueDTO',
}

type AddProductProps = {
  data: any;
  defaultConfig: any;
};

const AddProductOld: React.FC<any> = (props: AddProductProps) => {
  const { fromDraft } = history.location.query;
  const { data, defaultConfig } = props;
  const [inComeConfig, setLeaseFeeTableDataConfig] = useState();
  const [isAddProduct, setIsAddProduct] = useState<boolean>(() => !fromDraft);
  const [finalErrorTab, setFinalErrorTab] = useState<string>('');
  const [repaymentTerm, setRepaymentTerm] = useState<[]>([]);
  const [drawerStatus, setDrawerStatus] = useState<boolean>(true);

  const otherInfoRef = useRef<{ otherInfoSubmit: () => void }>();
  const baseInfoInitial = useMemo(
    () => ({
      classification: data?.classification || '',
      productName: data?.productName || '',
      secondaryClassification: data?.secondaryClassification || '',
      userType: data?.userType || '',
      selfType: data?.selfType || '',
      quotaType: data?.quotaType || '',
      guaranteeType: data?.guaranteeType || null,
      guaranteePeriods: data?.guaranteePeriods || null,
      registerLicenceType: data?.registerLicenceType || null,
    }),
    [data],
  );
  const otherInfoInitial = useMemo(
    () => ({
      status: data?.status,
      productDesc: data?.productDesc,
    }),
    [data],
  );

  useEffect(() => {
    setRepaymentTerm(data?.repaymentDTO?.repaymentTerms);
  }, [data]);
  const repayConfig = {
    costConfigDTO: data?.costConfigDTO,
    defaultCostConfigDTO: defaultConfig?.costConfigDTO, // 默认产品配置全量费用配置表
    costParameterDTO: data?.costParameterDTO,
    leaseBailCostParameterDTO: data?.leaseBailCostParameterDTO,
    leaseInterestCostParameterDTO: data?.leaseInterestCostParameterDTO,
    classification: data?.classification,
  };

  // 修改后的全量费用配置项数据，草稿传全量
  const [newConfigData, setNewFeeConfigData] = useState([]);
  // 修改后的费用配置项数据，提交只传当前项
  const [newConfigTableData, setNewFeeConfigTableData] = useState([]);

  // 监听整个页面的表单数据改变
  const handleProcessTabsChanges = (tabName?: string, changeFormValue?: any) => {
    switch (tabName) {
      // 处理进件选择费用配置表的联动
      case 'leaseIncoming':
        setLeaseFeeTableDataConfig(changeFormValue);
        break;
      // 如果是还款模块，--> 还款期限，联动费用配置的分离定价表格
      case 'payback':
        setRepaymentTerm(changeFormValue?.repaymentTerms);
        break;
      default:
        break;
    }
  };

  const submitProductConfig = (formName: string, allFormValues: { values: any; forms: any }) => {
    // 触发多表单校验
    // 展示到校验失败的form,设置finalErrorTab,setFinalErrorTab
    const errorTabName = Object.keys(allFormValues?.forms).find((itemFormName: string) => {
      // 表单校验是否有错误
      const itemFormHasError = allFormValues?.forms[itemFormName]
        .getFieldsError()
        .some((item: { name: []; errors: [] }) => {
          return item?.errors.length;
        });
      return itemFormHasError;
    });

    if (errorTabName) {
      // 是否在产品流程tab中
      const isItemTabName = [
        'leaseIncoming',
        'loan',
        'bill',
        'claim',
        'payback',
        'settleEarly',
        'overdue',
      ].includes(errorTabName);
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      isItemTabName && setFinalErrorTab(errorTabName);
      // 校验不通过，暂停提交
      return;
    }
    //

    // 然后再补充数据，allFormValues没有的数据，则用初始数据
    let submitData: any = {};

    Object.keys(allFormValues.forms).forEach((item) => {
      let itemData = {};
      if (item === 'otherInfo' || item === 'basicInfo') {
        itemData = allFormValues?.forms[item]?.getFieldsValue();
      } else {
        itemData = {
          [FORM_NAME_DATA_NAME[item]]: allFormValues?.forms[item]?.getFieldsValue(),
        };
      }
      submitData = { ...data, ...submitData, ...itemData }; // 初始数据，之前submitData, 每个item项的数据
    });

    // 最终章----处理还款期限联动分离定价表的,当前只保证金联动还款期限
    // 全量数据
    const newConfigDataTemp = newConfigData.map((item: any) => {
      if (item?.expenseItem === 'BAIL') {
        const priceTableListTemp = item?.priceTableList?.filter((itemPriceTableList: any) => {
          return submitData?.repaymentDTO?.repaymentTerms?.includes(itemPriceTableList?.instalment);
        });
        return {
          ...item,
          priceTableList: priceTableListTemp,
        };
      }
      return item;
    });
    // 只修改的项
    const newConfigTableDataTemp = newConfigTableData.map((item: any) => {
      if (item?.expenseItem === 'BAIL') {
        const priceTableListTemp = item?.priceTableList?.filter((itemPriceTableList: any) => {
          return submitData?.repaymentDTO?.repaymentTerms?.includes(itemPriceTableList?.instalment);
        });
        return {
          ...item,
          priceTableList: priceTableListTemp,
        };
      }
      return item;
    });

    // 如果是草稿，费用配置项数据为全量，status为-1
    const dependByDrawerData = drawerStatus
      ? { costConfigDTO: newConfigDataTemp, status: -1 }
      : { costConfigDTO: newConfigTableDataTemp, status: 0 };
    submitData = { ...submitData, ...dependByDrawerData };
    // 最后提交
    addProduct(submitData)
      .then(() => {
        message.success('操作成功');
        // 草稿
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        drawerStatus
          ? history.push('/businessMng/draft-list')
          : history.push('/businessMng/product-list');
      })
      .catch(() => {
        // message.error(err?.message);
      });
  };
  const triggerNewFeeConfigDataToAddProduct = (newFeeConfigData: [], newFeeConfigTableData: []) => {
    // 修改费用配置的全部最新数据
    setNewFeeConfigData([...newFeeConfigData]);
    // 修改当前费用配置展示的数据
    setNewFeeConfigTableData([...newFeeConfigTableData]);
  };
  return (
    <>
      <Form.Provider
        onFormChange={(formName: string, info: { changedFields: any; forms: any }) => {
          handleProcessTabsChanges(formName, info?.forms?.[formName]?.getFieldsValue());
        }}
        onFormFinish={(formName: string, info: { values: any; forms: any }) => {
          if (formName) {
            // 触发多表单校验
            Object.keys(info?.forms).forEach((itemFormName) => {
              info?.forms[itemFormName].validateFields();
            });

            // 然后再提交,触发表单校验，不能马上拿到错误提示
            setTimeout(() => {
              submitProductConfig(formName, info);
            }, 100);
          }
        }}
      >
        <BasicInfo
          baseInfo={baseInfoInitial}
          drawerStatus={drawerStatus}
          isAddProduct={isAddProduct}
          name="basicInfo"
        />
        <ProLeaseProcessConfig
          data={data}
          drawerStatus={drawerStatus}
          isAddProduct={isAddProduct}
          handleProcessTabsChanges={handleProcessTabsChanges}
          finalErrorTab={finalErrorTab}
        />
        <LeaseFeeConfig
          data={repayConfig}
          inComeConfig={inComeConfig || data?.activateDTO}
          isAddProduct={isAddProduct}
          repaymentTerm={repaymentTerm}
          initialRepaymentTerm={data?.repaymentDTO?.repaymentTerms || []}
          triggerNewFeeConfigDataToAddProduct={triggerNewFeeConfigDataToAddProduct}
        />
        <OtherInfo
          otherInfo={otherInfoInitial}
          cRef={otherInfoRef}
          drawerStatus={drawerStatus}
          isAddProduct={isAddProduct}
          name="otherInfo"
        />
        {/* 整个创建产品Form提交操作 */}
        <div style={{ display: 'flex', marginTop: 10, justifyContent: 'flex-end' }}>
          <Button
            onClick={() => {
              // eslint-disable-next-line @typescript-eslint/no-unused-expressions
              fromDraft
                ? history.push('/businessMng/draft-list')
                : history.push('/businessMng/product-list');
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            className={globalStyle?.ml10}
            onClick={() => {
              // 清空之前的错误errorTabName
              setFinalErrorTab('');
              // 设置草稿的状态
              setDrawerStatus(true);
              // 提交子表单,  会触发 Form.Provide onFormFinish
              otherInfoRef?.current?.otherInfoSubmit();
            }}
          >
            保存为草稿
          </Button>
          <Button
            type="primary"
            className={globalStyle?.ml10}
            // htmlType="submit"
            onClick={() => {
              // 解锁lock
              if (!isAddProduct) {
                setIsAddProduct(!isAddProduct);
                return;
              }
              // 设置草稿状态为false;
              setDrawerStatus(false);
              // 清空之前的错误errorTabName
              setFinalErrorTab('');
              // 提交子表单, 触发 Form.Provide onFormFinish
              otherInfoRef?.current?.otherInfoSubmit();
            }}
          >
            {isAddProduct ? (
              '提交'
            ) : (
              <>
                <UnlockOutlined />
                <span>编辑</span>
              </>
            )}
          </Button>
        </div>
      </Form.Provider>
    </>
  );
};

export default React.memo(AddProductOld);
