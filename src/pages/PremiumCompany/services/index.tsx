/*
 * @Date: 2023-07-12 17:15:16
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2024-10-16 10:41:10
 * @FilePath: /lala-finance-biz-web/src/pages/PremiumCompany/services/index.tsx
 * @Description:
 */

import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';
export type TlistItem = {
  id?: number; // 主键id，编辑时携带，新增不携带
  companyName: string; // 收款公司名称
  paymentAccountNo: string; // 收款公司账号
  bankNo: string; // 支行号
  subBranchName: string; // 支行名称
  bankName?: string; // 银行名称，通过【支行列表】接口获取
  channelList?: { id: string; channelCode: string; channelName: string }[]; //渠道列表
};

export async function queryBank(subBranchName: string) {
  return request('/bizadmin/premium/company/queryBank', {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    params: {
      subBranchName,
    },
  });
}

/**
 * 新增 编辑保费公司
 */
export async function add(params: TlistItem) {
  return request('/bizadmin/premium/company', {
    method: 'POST',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    data: params,
  });
}

/**
 * 查询收款公司列表
 */
export async function getList(params: { pageNumber: number; pageSize: number }) {
  return request('/bizadmin/premium/company/list', {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    params,
    ifTrimParams: true,
  });
}

/**
 * 删除保费公司
 */
export async function remove(id: number) {
  return request(`/bizadmin/premium/company/${id}`, {
    method: 'DELETE',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
export interface IcarChannelItem {
  id: number;
  channelCode: string; // 渠道编码
  channelType: string; // 渠道类型
  channelShortName: string; // 渠道简称
  channelName: string; // 渠道全称
  orgCode: string; // 统一信用代码
  contactName: string; // 联系人姓名
  contactPhone: string; // 联系人电话
  contactAddress: string; // 联系人地址
  channelAccountNo: number; // 渠道账号ID
  channelUserAccount: number; // 渠道账号，展示用
  relatedProduct: number[]; // 关联的产品列表
  relatedEnterprise: number[]; // 关联的企业列表
  relatedPayAccount: number[]; // 关联的收款公司列表
  personalRelatedProduct: number[]; // 关联的产品列表（个人）
  personalRelatedPayAccount: number[]; // 关联的收款公司列表（个人）
}

//渠道列表
export async function getChannelInfo(): Promise<IcarChannelItem[]> {
  const data = await request(`/bizadmin/channel/list`, {
    method: 'GET',
    params: {
      pageNumber: 1,
      pageSize: 10000,
    },
    ifTrimParams: true,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data?.data;
}

// 导出加减保单
interface IExportCompanyList {
  companyName?: string;
  paymentAccountNo?: string;
  subBranchName?: string;
  bankNo?: string;
}
export const exportCompanyList = (
  data: IExportCompanyList,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<{ data: Record<string, unknown> }> => {
  return request('/bizadmin/premium/company/list/export', {
    method: 'POST',
    data,
    headers: bizadminHeader,
    ...options,
  });
};
