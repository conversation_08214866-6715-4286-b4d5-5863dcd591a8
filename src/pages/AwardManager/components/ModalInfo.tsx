import ConfirmModal from '@/components/ConfirmModal';
import { optionsMap } from '@/utils/tools';
import { Form, Input, InputNumber, message, Modal, Select } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { modalProps } from '../data.d';
import { AwardTypeEnum, awardTypes, awardUnitMap } from '../data.d';
import { createStandardPrize, updateStandardPrize } from '../service';

export const ModalInfoCon = ({ editformRef, modalData, setModalData, onCallback }: modalProps) => {
  let defaultDate: any = modalData?.prizeType?.toString() || AwardTypeEnum.QUOTA_BAG;
  const [currentAwardType, setCurrentAwardType] = useState(defaultDate);
  const [loading, setLoading] = useState(false);
  const confirmModalRef = useRef<any>(null);

  // 奖品限制 返回结果
  const rewardGenerStr = (value: number, awardType: string) => {
    return awardType.toString() === AwardTypeEnum.QUOTA_BAG.toString()
      ? `发放条件为申请用信/放款成功/放款成功并还款1期时，用信金额需 > ${value}元`
      : `最高${value}元`;
  };
  // 表单提交
  const modalSubmitHandler = (e: any) => {
    e.preventDefault();
    editformRef.validateFields().then((values) => {
      // 需要经过表单校验
      // 需要确认的数据模版，处于新增模式下使用
      const dataTpm = {
        奖品名称: values.prizeName,
        奖品类型: awardTypes[values.prizeType],
        奖品价值: `${values.prizePrice}${awardUnitMap[values.prizeType]?.unit || '元'}`,
        奖品限制: `${rewardGenerStr(values.grantLimitValue, currentAwardType)}`,
        有效期: `发放后${values.prizeValidDays}天内有效`,
        奖品数量: `0/${values.prizeTotalNum}`,
      };
      confirmModalRef.current.setConfirmData(dataTpm);

      if (modalData !== 'new') {
        // 需要diff的数据模版，在编辑模式下使用
        const diffDataTmp = {
          奖品名称: {
            old: modalData?.prizeName,
            new: values.prizeName,
          },
          奖品类型: {
            old: awardTypes[modalData?.prizeType],
            new: awardTypes[values.prizeType],
          },
          奖品价值: {
            old: `${modalData?.prizePrice}${awardUnitMap[modalData?.prizeType]?.unit || '元'}`,
            new: `${values.prizePrice}${awardUnitMap[values.prizeType]?.unit || '元'}`,
          },
          奖品限制: {
            old: rewardGenerStr(modalData?.grantLimitValue, modalData?.prizeType),
            new: rewardGenerStr(values.grantLimitValue, currentAwardType),
          },
          有效期: {
            old: `发放后${modalData?.prizeValidDays}天内有效`,
            new: `发放后${values.prizeValidDays}天内有效`,
          },
          奖品数量: {
            old: `${modalData?.prizeOccupyNum ?? 0}/${modalData?.prizeTotalNum}`,
            new: `${modalData.prizeOccupyNum ?? 0}/${values.prizeTotalNum}`,
          },
        };
        confirmModalRef.current.setDiffData(diffDataTmp);
      }
      confirmModalRef.current.show();
    });
  };

  useEffect(() => {
    if (modalData?.id) {
      defaultDate = modalData?.prizeType?.toString() || AwardTypeEnum.QUOTA_BAG;
      setCurrentAwardType(defaultDate);
      setTimeout(() => {
        editformRef.setFieldsValue({
          prizeName: modalData?.prizeName,
          prizePrice: modalData?.prizePrice,
          grantLimitValue: modalData?.grantLimitValue,
          prizeValidDays: modalData?.prizeValidDays,
          prizeTotalNum: modalData?.prizeTotalNum,
        });
        // console.log('Edit info now!', data, editformRef.getFieldsValue())
      });
    }
  }, [modalData]);

  return (
    <Modal
      onCancel={() => {
        setModalData('');
      }}
      cancelButtonProps={{ style: { display: 'none' } }}
      open={modalData !== ''}
      title={modalData === 'new' ? '新增奖品' : '编辑奖品'}
      centered={true}
      confirmLoading={loading}
      okText="保存"
      destroyOnClose={true}
      afterClose={() => {
        editformRef.resetFields();
        setCurrentAwardType(defaultDate);
      }}
      width={600}
      onOk={(e) => {
        modalSubmitHandler(e);
      }}
    >
      <>
        <Form
          preserve={false}
          form={editformRef}
          requiredMark={false}
          onFinish={async (params) => {
            setLoading(true);
            let res: any;
            try {
              if (modalData === 'new') {
                res = await createStandardPrize(params);
              } else {
                res = await updateStandardPrize({ id: modalData?.id, ...params });
              }
            } catch (e) {
              setLoading(false);
              console.log(e);
            }
            if (res?.success) {
              setModalData('');
              onCallback();
              message.success(`${modalData === 'new' ? '新增' : '编辑'}奖品成功！`);
            }
            setLoading(false);
          }}
          labelCol={{ span: 4 }}
          labelAlign="left"
          style={{ maxWidth: 600, marginTop: 25 }}
        >
          <Form.Item
            name="prizeName"
            label="奖品名称"
            rules={[
              {
                required: true,
                message: '请输入奖品名称（20个字以内）',
                max: 20,
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="prizeType"
            label="奖品类型"
            initialValue={defaultDate}
            rules={[{ required: true }]}
          >
            <Select
              onChange={(value) => {
                editformRef.resetFields([
                  'prizeName',
                  'prizePrice',
                  'grantLimitValue',
                  'prizeValidDays',
                  'prizeTotalNum',
                ]);
                setCurrentAwardType(value);
              }}
              allowClear
              options={optionsMap(awardTypes)}
            />
          </Form.Item>
          <Form.Item label="奖品价值">
            <Form.Item
              name="prizePrice"
              style={{ display: 'inline-block', marginBottom: 0 }}
              rules={[
                {
                  required: true,
                  message: awardUnitMap[currentAwardType]?.msgValue,
                  min: awardUnitMap[currentAwardType]?.minNum,
                  max: awardUnitMap[currentAwardType]?.maxNum,
                  type: 'number',
                },
              ]}
            >
              <InputNumber
                style={{ minWidth: 200 }}
                precision={awardUnitMap[currentAwardType]?.precision}
                placeholder={awardUnitMap[currentAwardType]?.placeholderValue}
              />
            </Form.Item>
            <span className="form-item-inline">{awardUnitMap[currentAwardType]?.unit || '元'}</span>
          </Form.Item>
          <Form.Item label="奖品限制">
            {currentAwardType === AwardTypeEnum.QUOTA_BAG ? (
              <>
                <div style={{ marginBottom: 8 }}>
                  发放条件为申请用信/放款成功/放款成功并还款1期时
                </div>
                <span>用信金额需 &gt;</span>
                <Form.Item
                  name="grantLimitValue"
                  className="form-item-inline"
                  rules={[
                    {
                      required: true,
                      message: '请输入1000 - 30000之间的整数',
                      min: 1000,
                      max: 30000,
                      type: 'number',
                    },
                  ]}
                >
                  <InputNumber style={{ minWidth: 200 }} precision={0} />
                </Form.Item>
                <span className="form-item-inline">元</span>
              </>
            ) : (
              <>
                <span>最高</span>
                <Form.Item
                  name="grantLimitValue"
                  className="form-item-inline"
                  rules={[
                    {
                      required: true,
                      message: '请输入1 - 10000之间的整数',
                      min: 1,
                      max: 10000,
                      type: 'number',
                    },
                  ]}
                >
                  <InputNumber
                    style={{ minWidth: 200 }}
                    precision={0}
                    placeholder={awardUnitMap[currentAwardType]?.placeholderLimit}
                  />
                </Form.Item>
                <span className="form-item-inline">元</span>
              </>
            )}
          </Form.Item>
          <Form.Item label="有效期">
            <span>发放后</span>
            <Form.Item
              name="prizeValidDays"
              className="form-item-inline"
              rules={[
                {
                  required: true,
                  message: '请输入1 - 365之间的整数',
                  min: 1,
                  max: 365,
                  type: 'number',
                },
              ]}
            >
              <InputNumber style={{ minWidth: 200 }} precision={0} placeholder="在此填写有效天数" />
            </Form.Item>
            <span className="form-item-inline">天内有效</span>
          </Form.Item>
          <Form.Item
            name="prizeTotalNum"
            label="奖品数量"
            rules={[
              {
                required: true,
                message: '请输入1 - 1000000之间的整数',
                min: 1,
                max: 1000000,
                type: 'number',
              },
            ]}
          >
            <InputNumber style={{ width: 460 }} precision={0} placeholder="在此填写奖品数量" />
          </Form.Item>
        </Form>
      </>
      <ConfirmModal
        key={modalData === 'new' ? 'newAwardModal' : 'editAwardModal'}
        mode={modalData === 'new' ? 'normal' : 'diff'}
        ref={confirmModalRef}
        onOk={() => {
          editformRef.submit();
          confirmModalRef.current.hide();
        }}
      />
    </Modal>
  );
};
