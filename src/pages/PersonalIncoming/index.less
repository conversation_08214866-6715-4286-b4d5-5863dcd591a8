.personal-list {
  // border-top: 1px solid #eee;
  // border-left: 1px solid #eee;
  // display: flex;
  // width: 100%;
  // .border-col{
  //   background-color: grey;
  // }
  .border-col {
    padding: 4px;
    padding-left: 15px;
    text-align: left;
    border: 1px solid #eee;
    // border-right: 1px solid #eee;
    // border-bottom: 1px solid #eee;
  }
  .border-col.header {
    font-weight: bold;
    background-color: #ddd;
  }

  .personal-img {
    object-fit: cover;
  }
}

.col_item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
  padding: 10px 6px;
  // :nth-of-type(3):border-le
}
