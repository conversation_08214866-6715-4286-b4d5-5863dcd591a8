import { ModalForm, ProFormText } from '@ant-design/pro-form';
import React, { memo, useEffect } from 'react';
import { changePassword } from '../services';
import { Form, message } from 'antd';
import type { ActionType } from '@ant-design/pro-table';

type Props = {
  userAccount: number;
  visible: boolean;
  setVisible: (val: boolean) => void;
  action: ActionType | undefined;
  pid: string;
};
const ChangePassword: React.FC<Props> = (props) => {
  const { userAccount, visible, setVisible, action, pid } = props;
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue({ userAccount });
  }, [userAccount, form]);
  return (
    <ModalForm<{ userAccount: string; newPassword: string }>
      visible={visible}
      title="修改关联"
      form={form}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 14 }}
      layout="horizontal"
      initialValues={{ userAccount }}
      modalProps={{
        onCancel: () => {
          setVisible(false);
        },
      }}
      onFinish={async (values) => {
        await changePassword({
          ...values,
          pid,
        });
        message.success('提交成功');
        setVisible(false);
        action?.reload();
        return true;
      }}
    >
      <ProFormText
        name="userAccount"
        disabled
        label="账号"
        placeholder="请输入渠道类型"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="newPassword"
        label="新密码"
        placeholder="请输入新密码"
        required
        rules={[{ required: true }]}
      />
    </ModalForm>
  );
};
export default memo(ChangePassword);
