/*
 * @Author: your name
 * @Date: 2021-04-12 15:18:02
 * @LastEditTime: 2023-03-28 17:05:06
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/BindNewCar.tsx
 */
import { CommonImageUpload } from '@/components/ReleaseCom';
import { convertUploadFileList } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import type { DeliveryCarParams } from '../data';
import { deliveryCarAdd } from '../service';

export type HandleCarResourceModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  formEdit?: Record<string, any>;
  otherRemark?: string;
  // deadLine: string;
};

const HandleCarResourceModal: React.FC<HandleCarResourceModalProps> = (props) => {
  const { orderNo } = history.location.query;
  const { formEdit, otherRemark } = props;
  const [form] = Form.useForm();
  const [allFileList, handleFileList] = useState<Record<string, any> | undefined>({});
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };
  useEffect(() => {
    handleFileList(formEdit);
    form.setFieldsValue({
      ...formEdit,
      remark: otherRemark,
    });
    // console.log(formEdit);
    return () => {
      handleFileList({});
    };
  }, [formEdit]);
  return (
    <>
      <ModalForm
        title="交车资料"
        width={600}
        layout="horizontal"
        visible={props.modalVisible}
        onVisibleChange={props.onVisibleChange}
        form={form}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            handleFileList({});
          },
        }}
        onFinish={async (values) => {
          try {
            // console.log(allFileList);
            const mapUploadFile = convertUploadFileList(allFileList, [
              'carDeliveryReceipt',
              'groupPhoto',
            ]);
            // console.log(mapUploadFile);
            await deliveryCarAdd({
              orderNo,
              userNo: 789,
              ...values,
              ...mapUploadFile,
            } as DeliveryCarParams);
            message.success('添加成功');
            props.onOk();

            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <CommonImageUpload
          extra="支持扩展名：.png.jpg.jpeg"
          label="车辆交接单"
          labelCol={{ span: 4 }}
          name="carDeliveryReceipt"
          max={5}
          size={10}
          fileListEdit={formEdit?.carDeliveryReceipt}
          icon={<PlusOutlined />}
          buttonProps={{ type: 'text' }}
          listType="picture-card"
          mapFileList={mapFileList}
          accept=".png,.jpg,.jpeg,.gif"
          rules={[{ required: true, message: '请上传车辆交接单' }]}
        />
        <CommonImageUpload
          extra="支持扩展名：.png.jpg.jpeg"
          label="人车合影"
          icon={<PlusOutlined />}
          labelCol={{ span: 4 }}
          name="groupPhoto"
          max={5}
          mapFileList={mapFileList}
          fileListEdit={formEdit?.groupPhoto}
          buttonProps={{ type: 'text' }}
          listType="picture-card"
          accept=".png,.jpg,.jpeg,.gif"
          rules={[{ required: true, message: '请上传人车合影' }]}
          size={10}
        />
        <ProFormText
          name="remark"
          labelCol={{ span: 4 }}
          width="md"
          fieldProps={{ maxLength: 20 }}
          label="其他"
          placeholder="请输入"
          // rules={[{ required: true }]}
        />
      </ModalForm>
    </>
  );
};

export default HandleCarResourceModal;
