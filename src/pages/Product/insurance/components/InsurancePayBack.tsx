import HllSelect from '@/components/Module/Select';
import { REPAYMENT_CYCLESTART_TIME_ENUM, WITH_HOLDING_TIME } from '@/enums';
import { useModel } from '@umijs/max';
import { Col, Form, Radio, Row, Select } from 'antd';
import React, { useEffect } from 'react';
import { getIntArray, isIntNumber } from '../../utils';
import RepaymentDayInput from './RepaymentDayInput';

type PayBackProps = {
  type?: boolean;
};
const InsurancePayBackPayBack: React.FC<PayBackProps> = () => {
  const { detailData, setPayBackData } = useModel('Product.insuranceProduct');
  const [form] = Form.useForm();
  useEffect(() => {
    const { repaymentDTO: payBackData } = detailData;
    form.setFieldsValue({
      ...payBackData,
      repaymentTerms: payBackData?.repaymentTerms?.length ? payBackData?.repaymentTerms : [],
      repaymentTermEnums:
        payBackData?.repaymentTermEnums ||
        (payBackData?.repaymentTermEnum &&
          payBackData?.repaymentTermEnum
            .replace('FIXED_REPAYMENT_', '')
            .split('_')
            .map((num: string) => `FIXED_REPAYMENT_${num}`)),
    });
  }, [form, detailData]);

  const handleRepeaymentTermsChange = (value: any) => {
    setPayBackData((preData) => {
      return {
        ...preData,
        repaymentTerms: value,
      };
    });
  };

  return (
    <Form
      form={form}
      name={'insurancePayBack'}
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      labelAlign="right"
    >
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item name="repaymentModeEnum" label="还款方式">
            <Select disabled>
              <Select.Option value="ALL_AT_ONCE">一次本息</Select.Option>
              <Select.Option value="PRINCIPAL_EQUALS_INTEREST">等额本息</Select.Option>
            </Select>
          </Form.Item>
        </Col>

        <Col span={10} offset={1}>
          <Form.Item name="repaymentTerms" label="还款期限" rules={[{ required: true }]}>
            <HllSelect
              options={getIntArray(36).map((item) => ({
                label: item,
                value: item,
              }))}
              onChange={handleRepeaymentTermsChange}
              dataFormat="array"
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item
            name="carInsuranceDefaultRepaymentDate"
            label="默认还款日期"
            rules={[
              { required: true },
              {
                validator(_, value, callBack) {
                  if (!Array.isArray(value)) {
                    callBack('value值必须是数组');
                  }
                  for (let i = 0; i < 2; i++) {
                    const item = value[i];
                    if (!isIntNumber(item)) {
                      // 假如有一个不是整形数字
                      callBack('格式必须是整形数字');
                    }
                    // 只能设置1-28之间的数值
                    if (value[i] > 28 || value[i] <= 0) {
                      callBack('请输入1-28之间的数值');
                    }
                    // 两个数值不能相同
                    if (value[i] === value[i + 1]) {
                      callBack('两个数值不能相同');
                    }
                  }
                  callBack();
                },
              },
            ]}
          >
            <RepaymentDayInput />
            {/* <Select disabled>
              <Select.Option value={0}>{type ? '放款' : '合同'}对日</Select.Option>
            </Select> */}
          </Form.Item>
        </Col>

        <Col span={10} offset={1}>
          <Form.Item
            name="repaymentCycleStartTimeEnum"
            labelCol={{ offset: 0 }}
            label="还款周期起始时间"
          >
            <Select disabled>
              {Object.keys(REPAYMENT_CYCLESTART_TIME_ENUM).map((key) => (
                <Select.Option key={key} value={key}>
                  {REPAYMENT_CYCLESTART_TIME_ENUM[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item name="partialRepayment" labelCol={{ offset: 0 }} label="是否允许部分还款">
            <Radio.Group value={1} disabled>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={10} offset={1}>
          <Form.Item name="withholdingTime" label="代扣时间">
            <Select disabled mode="multiple">
              {Object.keys(WITH_HOLDING_TIME).map((key) => (
                <Select.Option key={key} value={key}>
                  {WITH_HOLDING_TIME[key]}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default InsurancePayBackPayBack;
