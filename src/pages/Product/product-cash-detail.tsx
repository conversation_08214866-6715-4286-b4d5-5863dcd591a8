/*
 * @Author: your name
 * @Date: 2021-04-14 18:38:09
 * @LastEditTime: 2024-01-27 16:03:16
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/product-cash-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import React from 'react';
import BasicInfoCash from './components/CashProductConfig/BasicInfoCash';
import ProCashProcessConfig from './components/CashProductConfig/ProCashProcessConfig';
import LeaseFeeConfig from './components/LeaseFeeConfig';
import OtherInfo from './components/OtherInfo';
import { queryProductDetail } from './service';

const ProductDetail: React.FC<any> = () => {
  const { productCode } = history.location.query;
  const { data } = useRequest(() => {
    return queryProductDetail(productCode);
  });
  // console.log(data);
  const baseInfo = {
    classification: data?.classification || '',
    productName: data?.productName || '',
    secondaryClassification: data?.secondaryClassification || '',
    userType: data?.userType || '',
    selfType: data?.selfType || '',
    quotaType: data?.quotaType || '',
  };
  const otherInfo = {
    status: data?.status,
    productDesc: data?.productDesc,
  };
  const repayConfig = {
    costConfigDTO: data?.costConfigDTO,
    costParameterDTO: data?.costParameterDTO,
    leaseBailCostParameterDTO: data?.leaseBailCostParameterDTO,
    leaseInterestCostParameterDTO: data?.leaseInterestCostParameterDTO,
  };
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <BasicInfoCash baseInfo={baseInfo} />
        <ProCashProcessConfig data={data} />
        {/* <RepaymentConfig data={repayConfig} /> */}
        {/* 是自营才展示 */}
        {baseInfo?.selfType === 1 && <LeaseFeeConfig data={repayConfig} />}
        <OtherInfo otherInfo={otherInfo} />
      </PageContainer>
    </>
  );
};

export default ProductDetail;
