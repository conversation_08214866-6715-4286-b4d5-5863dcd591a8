/*
 * @Date: 2023-07-12 17:15:16
 * @Author: elisa.z<PERSON>
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-09 10:42:05
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/BasicInfo.tsx
 * @Description:
 */
/* eslint-disable react-hooks/exhaustive-deps */

import { CLASSIFICATION, GUARANTEE_TYPES, SECONDARY_CLASSIFICATION } from '@/enums';
import globalStyle from '@/global.less';
import { ProFormDependency } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Card, Col, Form, Input, InputNumber, Radio, Row, Select } from 'antd';
import React, { useEffect } from 'react';
interface BaseInfoProps {
  baseInfo?: any;
  isAddProduct?: boolean; //
  name?: string;
  drawerStatus?: boolean;
  type?: string;
}

const BasicInfo: React.FC<BaseInfoProps> = (props) => {
  const { fromDraft, isAddProduct: queryIsAddProduct } = history.location.query;
  const { baseInfo, isAddProduct = false, name, drawerStatus = true, type } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(baseInfo);
  }, [baseInfo]);

  return (
    <Card title="基础信息" className={globalStyle.mt30}>
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }} name={name}>
        <Row gutter={20}>
          <Col span={10} offset={1}>
            <Form.Item label="产品一级分类" name="classification">
              <Select placeholder="保理" disabled>
                {Object.keys(CLASSIFICATION).map((key) => (
                  <Select.Option key={key} value={key}>
                    {CLASSIFICATION[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="secondaryClassification" label="产品二级分类">
              <Select placeholder="明保" disabled>
                {Object.keys(SECONDARY_CLASSIFICATION).map((key) => (
                  <Select.Option key={key} value={key}>
                    {SECONDARY_CLASSIFICATION[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="productName" rules={[{ required: !drawerStatus }]} label="产品名称">
              <Input disabled={!isAddProduct} maxLength={50} />
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="userType" label="用户类型">
              <Select placeholder="企业" disabled>
                <Select.Option value="PERSONAGE">个人</Select.Option>
                <Select.Option value="COMPANY">企业</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item label="产品类型" name="selfType">
              <Radio.Group disabled>
                <Radio value={1}>自营</Radio>
                <Radio value={2}>非自营</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item label="额度类型" name="quotaType">
              <Radio.Group disabled>
                <Radio value={2}>循环额度</Radio>
                <Radio value={1}>一次性额度</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item
              label="上牌类型"
              name="registerLicenceType"
              rules={[{ required: !drawerStatus, message: '请选择上牌类型' }]}
            >
              {/* 编辑模式和查看模式禁止修改， 从草稿箱进入，创建产品入口进入允许修改 */}
              <Radio.Group
                disabled={
                  !isAddProduct ||
                  (baseInfo?.registerLicenceType && queryIsAddProduct != '1' && fromDraft != '1')
                }
              >
                <Radio value="AFFILIATE">挂靠</Radio>
                <Radio value="CUSTOMER">个户</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          {type !== 'cash' ? (
            <>
              <Col span={10} offset={1}>
                <ProFormDependency name={['registerLicenceType']}>
                  {({ registerLicenceType }) => {
                    if (registerLicenceType == 'CUSTOMER') {
                      //  个户类型下，只允许非担保类型
                      setTimeout(() => {
                        form.setFieldValue('guaranteeType', 'NON_GUARANTEED');
                        form.resetFields(['guaranteePeriods']);
                      }, 100);
                    }
                    return (
                      <Form.Item
                        label="担保类型"
                        rules={[{ required: !drawerStatus, message: '请选择业务类型' }]}
                        name="guaranteeType"
                      >
                        <Radio.Group
                          disabled={!isAddProduct || registerLicenceType == 'CUSTOMER'} //  个户类型下，禁止编辑担保类型
                          onChange={(e) => {
                            form.validateFields(['guaranteePeriods']);
                            if (e.target.value === 'ALL_PART') {
                              form.setFieldValue('guaranteePeriods', 999);
                            } else {
                              form.setFieldValue('guaranteePeriods', null);
                            }
                          }}
                        >
                          {Object.keys(GUARANTEE_TYPES).map((key) => (
                            <Radio key={key} value={key}>
                              {GUARANTEE_TYPES[key]}
                            </Radio>
                          ))}
                        </Radio.Group>
                      </Form.Item>
                    );
                  }}
                </ProFormDependency>
              </Col>
              <Col span={10} offset={1}>
                <ProFormDependency name={['guaranteeType']}>
                  {({ guaranteeType }) => {
                    return (
                      <Form.Item
                        label="担保期限"
                        // value={guaranteeType === GUARANTEE_TYPE.ALL_GUARANTEE?999}
                        rules={
                          guaranteeType === 'PART_GUARANTEED'
                            ? [
                                {
                                  pattern: /^(?:[1-9]|[1-9][0-9]|99)$/,
                                  message: '1-99之间的整数',
                                },
                                {
                                  required: !drawerStatus,
                                },
                              ]
                            : []
                        } //草稿
                        name="guaranteePeriods"
                      >
                        {/* 不担保隐藏 */}
                        <InputNumber
                          disabled={guaranteeType !== 'PART_GUARANTEED' || !isAddProduct}
                        />
                      </Form.Item>
                    );
                  }}
                </ProFormDependency>
              </Col>
            </>
          ) : (
            ''
          )}
        </Row>
      </Form>
    </Card>
  );
};

export default React.memo(BasicInfo);
