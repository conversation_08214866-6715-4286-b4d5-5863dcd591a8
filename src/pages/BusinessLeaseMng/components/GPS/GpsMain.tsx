//GPS信息tab
import { getGpsOrderByOrderNo } from '@/pages/GpsMng/service';
import { history, useRequest } from '@umijs/max';
import { Spin } from 'antd';
import { useState } from 'react';
import { ORDER_STATUS } from '../../consts';
import { queryOrderExtend } from '../../service';
import AddNewOrder from './AddNewOrder';
import GpsOrderDetail from './GpsOrderDetail';

const GpsMain = () => {
  const { orderNo } = history.location.query as {
    orderNo: string;
  };

  const [refreshing, setRefreshing] = useState(false);

  const { data: dataList, run: runDataList, loading } = useRequest(() => {
    const params = {
      orderNo,
      orderType: '1', //工单类型 1. 安装 2.拆机 3.检修
    };
    return getGpsOrderByOrderNo(params);
  });

  const { data: dataExtend, run: runDataExtend } = useRequest(() => {
    return queryOrderExtend(orderNo);
  });

  const loopRefresh = (start: number) => {
    setRefreshing(true);
    const loopId = setTimeout(() => {
      runDataList()
        .then(() => {
          clearTimeout(loopId);
          setRefreshing(false);
        })
        .catch(() => {
          const now = Date.now();
          if (now - start > 10e3) {
            setRefreshing(false);
            clearTimeout(loopId);
          } else {
            // 有问题就继续查询
            loopRefresh(start);
          }
        });
    }, 1500);
  };

  const refresh = () => {
    const start = Date.now();
    runDataExtend();
    loopRefresh(start);
  };

  return (
    <Spin spinning={loading || refreshing} tip="数据查询中...">
      {dataList?.length > 0 ? (
        <GpsOrderDetail orderNo={orderNo} dataList={dataList} refresh={refresh} />
      ) : [ORDER_STATUS.SIGN_SUCCESS, ORDER_STATUS.WAIT_LOAN].includes(dataExtend?.orderStatus) ? (
        <AddNewOrder orderNo={orderNo} refresh={refresh} />
      ) : (
        '暂无数据'
      )}
    </Spin>
  );
};

export default GpsMain;
