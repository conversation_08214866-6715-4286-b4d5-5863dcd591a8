/*
 * @Author: your name
 * @Date: 2021-01-12 17:01:50
 * @LastEditTime: 2024-09-26 17:56:58
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/data.d.ts
 */

export interface CallPayItem {
  createdAt: string; // 创建时间
  amount: number; // 回款金额
  status: number; // 状态
  overdueId: string; // 催收单号
  status: number; // 状态
  urgePerson: string; // 催收员
  paymentId: string;
  reviewPerson: string;
  reviewTime: string;
  reason: string;
  remitApplyOrderNo: string;
  productCode?: string;
  overdueCaseNo?: string;
  orderNo?: string;
  accountName?: string; //借款人姓名
  orderAmount?: string; //订单金额
  overdueAmount?: string; //逾期金额
  paymentTime?: string; //回款日期
  secondaryName?: string; //产品一级分类
  productCode: string;
  compensateNo: string;
  attachList: [];
}

export interface CallPayParams {
  createdAtEnd?: string; // 创建结束时间
  createdAtStart?: string; // 开始时间
  overdueId?: string; // 统一社会信用代码
  startApplyTime?: string; // 创建开始时间
  paymentId?: string;
  status?: number; // 状态
  urgePerson?: string; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 页大小
}

export interface CallPayReviewParams {
  errorMsg?: string;
  paymentId?: string;
  status?: number;
}

export interface CollectReviewParams {
  errorMsg?: string;
  paymentId?: string;
  status?: number;
}

export interface OfflineRemitList {
  createBy?: string;
  current?: number;
  endCreatedAt?: string;
  pageSize?: number;
  startCreatedAt?: string;
  applyName?: string;
  orderNo?: string;
  remitApplyOrderNo?: string;
}

export interface OffLineRemitAuditParams {
  auditResult?: number;
  offlineRemitOrderNo?: string;
  errorMsg?: string;
}

export interface CallPayItemOffLine {
  createBy?: string;
  current?: number;
  endCreatedAt?: string;
  pageSize?: number;
  startCreatedAt?: string;
  applyName?: string;
  orderNo?: string;
  remitApplyOrderNo?: string;
  compensateNo?: string;
  repayTerms?: number[];
  productCode: string;
  termDetail?: string;
  attachList: [];
  status: number;
  paymentId: string;
}

export interface OffLineCompensateAuditParams {
  auditResult?: number;
  compensateNo?: string;
  reason?: string;
}
