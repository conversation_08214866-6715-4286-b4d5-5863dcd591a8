import { Typography } from 'antd';
import React from 'react';
import './index.less';

export type ColumnsItem = {
  key?: string;
  title: string;
  dataIndex: string;
  render?: (key: string, value: any, data: Record<string, any>) => React.ReactNode;
  showCopy?: boolean;
};

export type ShowMapProps = {
  columns: ColumnsItem[];
  data: Record<string, any>;
  children?: React.ReactNode;
};
const classPrefix = `showmap`;
const ShowMap: React.FC<ShowMapProps> = (props) => {
  const { columns, data } = props;
  if (!data) return null;

  return (
    <div className={`${classPrefix}-wrap`}>
      {columns.map((item) => {
        const value = data[item.dataIndex] || '-';
        return (
          <div className={`${classPrefix}-item`} key={item.key || item.dataIndex}>
            <span className={`${classPrefix}-label`}>{item.title}:</span>
            <span className={`${classPrefix}-value`}>
              {item.render ? item.render(item.dataIndex, value, data) : value}
              {item.showCopy && <Typography.Text copyable={{ text: value }} />}
            </span>
          </div>
        );
      })}
    </div>
  );
};
export default ShowMap;
