.release-com-modal {
  width: auto !important;
  max-width: 800px !important;
  .ant-carousel .slick-prev,
  .ant-carousel .slick-next,
  .ant-carousel .slick-prev:hover,
  .ant-carousel .slick-next:hover {
    top: 150px;
    width: 50px;
    height: 50px;
    padding: 9px;
    color: currentColor;
    color: #fff;
    font-size: inherit;
    font-size: 32px;
    opacity: 0.4;
  }
  .ant-carousel .slick-prev:hover,
  .ant-carousel .slick-next:hover {
    background: gray;
    border-radius: 50%;
    opacity: 0.8;
  }
  .ant-carousel .slick-prev {
    left: -100px;
  }
  .ant-carousel .slick-next {
    right: -100px;
  }
}

.release-com-img-wrap {
  display: flex;
  .ant-image {
    margin: auto;
    .release-com-img {
      display: block;
      width: auto !important;
      max-width: 750px;
      margin: auto;
    }
  }
}
