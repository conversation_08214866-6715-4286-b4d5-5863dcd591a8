/*
 * @Date: 2024-09-11 17:56:01
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-09-11 17:56:01
 * @FilePath: /lala-finance-biz-web/src/pages/CarInsuranceChannel/util.ts
 * @Description:
 */
const checkItemsList = {
  personal: ['personalRelatedProduct'],
  enterprise: ['relatedProductAndEnterpriseFlat'],
};

//判断是否有值
const hasValue = (obj) => {
  // 检查对象的每个属性
  for (const key in obj) {
    const value = obj[key];
    // 如果属性值是数组，检查数组是否至少有一个非空元素
    if (Array.isArray(value) && value.some((item) => item != null)) {
      return true;
    }
    // 如果属性值不是数组，但不是null或空字符串，也算作有值
    if (value != null && value !== '') {
      return true;
    }
  }
  return false;
};

export const getPersonalOrEnterpriseHasVal = (values) => {
  const personal = checkItemsList.personal.every((key) => {
    return values[key]?.length;
  });
  const enterprise = checkItemsList.enterprise.every((key) => {
    return values[key]?.some(hasValue);
  });

  return { personal, enterprise };
};
