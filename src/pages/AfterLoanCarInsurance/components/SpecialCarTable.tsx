/*
 * @Date: 2023-11-22 18:12:22
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-12-04 14:59:38
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoanCarInsurance/components/SpecialCarTable.tsx
 * @Description:
 */
/* eslint-disable react-hooks/exhaustive-deps */
import { EcarStatus } from '@/utils/bankend/enum';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { InputNumber, message } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useEffect, useRef, useState } from 'react';
import { getLookCarInfoInfo } from '../services';
import styles from '../styles/index.less';
import type {
  IlookCarInfoItem,
  IrepaymentInfoItem,
  IspecialCarPolicyRepayItem,
  TcarPolicyRepayMode,
} from '../types';
import { EcarPolicyRepayMode, isPreSettleMap } from '../types';
import LookPreSettleDetail from './LookPreSettleDetail';
import RemissionModal from './RemissionModal';

type Props = {
  value?: any[];
  onChange?: (value: any[]) => void;
  setRepayAmount: (amount: number) => void;
  carPolicyRepayMode: TcarPolicyRepayMode;
  setAllCarRepayInfo: (val: any) => void;
  setSpecialCarRepayInfo: (val: any) => void;
  formRef: any;
  repayDate: string; // 还款日期
};

const SpecialCarTable: React.FC<Props> = (props) => {
  const actionRef = useRef();
  const { orderNo } = (history as any).location.query;
  const [carInfoValueEnum, setCarInfoValueEnum] = useState<any>({});
  const [carInfo, setCarInfo] = useState<IlookCarInfoItem[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<IspecialCarPolicyRepayItem[]>([]);
  const { setAllCarRepayInfo, setSpecialCarRepayInfo } = props;
  // 判断减免信息是否发生了变化 后续优化 直接监听 remissionDetailList 会一直重复渲染 不得已这样做 当减免金额modal 点击确定时 set 标识减免金额发生了变化
  const [flush, setFlush] = useState({});
  // 算出全部车辆默认的还款金额 以及计算明细
  function getAllDefaultAmount(data: IlookCarInfoItem[]) {
    const allCariInfo = data?.map((car) => {
      const { carPolicyTermRspBOS } = car;
      let index = 0;
      const repayAmount = carPolicyTermRspBOS?.reduce((termPre: number, termCur: any) => {
        const {
          shouldPrincipal,
          shouldInterest,
          overdueInterest,
          overdueLatePaymentFee,
          repayTime,
          status,
        } = termCur;

        // termAmount 当前期总共应该还多少
        // eslint-disable-next-line no-multi-assign
        const termAmount = (termCur.termAmount =
          Number(shouldPrincipal || 0) +
          Number(shouldInterest || 0) +
          Number(overdueInterest || 0) +
          Number(overdueLatePaymentFee || 0));

        // 只有往期逾期和待还款的 和王后延迟一期的 才需要计算

        if (dayjs().diff(repayTime, 'day') >= 0) {
          // 往期的 当前选择的时间 比应还时间大 说明当前期是往期的
          if ([EcarStatus.PENDING_REPAYMENT, EcarStatus.OVERDUE].includes(status)) {
            return termPre + termAmount;
          }
        } else {
          // 时间戳几乎不可能相等的
          // 往后延迟一期
          index++;
          if (index === 1) {
            // 期数是按照顺序排列的 第一次走到这里的就是 往后延一期的
            if ([EcarStatus.PENDING_REPAYMENT, EcarStatus.OVERDUE].includes(status)) {
              return termPre + termAmount;
            }
          }
        }
        return termPre;
      }, 0);
      return {
        // 当前车辆应该的还款金额
        repayAmount,
        ...car,
      };
    });
    return allCariInfo;
  }

  // 算出全部车辆每辆车应该的还款金额
  useEffect(() => {
    const allCarRepayInfo = getAllDefaultAmount(carInfo);
    setAllCarRepayInfo?.(allCarRepayInfo);
  }, [carInfo, setAllCarRepayInfo]);

  // 获取全部的车辆信息
  async function getCarInfo(orderNo1: string) {
    const data = await getLookCarInfoInfo(orderNo1);
    setCarInfo(data);
  }

  useEffect(() => {
    const specialCarRepayInfo = dataSource.map((item) => {
      const { repaySchemaItemNo } = item;
      return {
        ...item,
        licensePlateNo: carInfoValueEnum[repaySchemaItemNo!]?.licensePlateNo,
      };
    });
    setSpecialCarRepayInfo?.(specialCarRepayInfo);
  }, [setSpecialCarRepayInfo, dataSource, carInfoValueEnum]);

  function getLatestTerm(carPolicyTermRspBOS: IrepaymentInfoItem[]) {
    carPolicyTermRspBOS.sort((a, b) => Number(a.term) - Number(b.term));
    // 总共需要的计算的数据 最新一期的数据
    const latestTerm = [];

    // 当正好等于还款日时 最新一期的数据就是当前还款日 当处于还款日之间 最新一期就是往后一期的
    for (let i = 0; i < carPolicyTermRspBOS.length; i++) {
      const car = carPolicyTermRspBOS[i];
      const { repayTime } = car;
      const num = dayjs(`${dayjs().format('YYYY-MM-DD')} 00:00:00`).diff(
        `${repayTime} 00:00:00`,
        'day',
      );
      if (num > 0) {
        // 往期的数据 不包含正好数还款日
        latestTerm.push(car);
      } else {
        // 最新一期的的数据 正好的是还款日
        latestTerm.push(car);
        break;
      }
    }

    return latestTerm.filter((item) =>
      [EcarStatus.PENDING_REPAYMENT, EcarStatus.OVERDUE].includes(item.status),
    );
  }
  // 获取最新一期 每辆车的金额
  function getLatestTermAmount(latestTerm: IrepaymentInfoItem[]): number {
    return latestTerm.reduce((pre, cur) => {
      const {
        remainingPrincipal,
        remainingInterest,
        remainingOverdueInterest,
        remainingOverdueLatePaymentFee,
      } = cur;
      return (
        pre +
        Number(remainingPrincipal || 0) +
        Number(remainingInterest || 0) +
        Number(remainingOverdueInterest || 0) +
        Number(remainingOverdueLatePaymentFee || 0)
      );
    }, 0);
  }
  // 计算总的还款金额
  useEffect(() => {
    const { carPolicyRepayMode, setRepayAmount } = props;
    if (!carPolicyRepayMode || !carInfo?.length) {
      return;
    }
    // 只用计算选了车辆的
    const selectedCars = dataSource.filter((item) => item.repaySchemaItemNo); // 特殊处理的车辆
    const selectedPlatNo = selectedCars.map((item) => item.repaySchemaItemNo); // 特殊处理的车牌号

    // 除了选中的车辆的 剩余车辆
    const residueCars = carInfo.filter((item) => !selectedPlatNo.includes(item.schemaItemNo)); // 剩余车辆
    // 拿到往期和延后一期的 状态是未还款和逾期的 每一期的数据
    const latestTermCars = residueCars.map((item) => {
      return {
        latestCarPolicyTermRspBOS: getLatestTerm(item.carPolicyTermRspBOS),
      };
    });

    const otherAmount = latestTermCars?.reduce((pre, cur) => {
      return pre + getLatestTermAmount(cur?.latestCarPolicyTermRspBOS);
    }, 0);

    // 计算选中车辆的还款金额
    const selectedAmount = selectedCars?.reduce(
      (pre, cur) => pre + Number(cur.repayAmount || 0),
      0,
    );
    // 拿到选中车辆的减免金额
    const remissionAmount = selectedCars?.reduce((pre, cur) => {
      const remissionCarAmount = cur.remissionDetailList?.reduce(
        (pre1, cur1) => pre1 + cur1.remissionAmount! || 0,
        0,
      );
      return pre + Number(remissionCarAmount || 0);
    }, 0);
    // 得到还款总金额

    let repayAmount = 0;
    if (carPolicyRepayMode === String(EcarPolicyRepayMode.DEFAULT)) {
      // 全部车辆还款至最新一期 dataSource是空的
      repayAmount = otherAmount;
    } else if (carPolicyRepayMode === String(EcarPolicyRepayMode.ALL_CAR_NOT_REPAY_OTHER)) {
      // 其他车辆不还款，部分车辆特殊处理
      repayAmount = selectedAmount + remissionAmount;
    } else {
      // 其他车辆还款至最新一期，部分车辆特殊处理
      repayAmount = otherAmount + selectedAmount + remissionAmount;
    }
    setRepayAmount(repayAmount);
    props?.onChange?.(dataSource);
  }, [dataSource, props.carPolicyRepayMode, carInfo]);
  useEffect(() => {
    // 获取全部的车辆信息
    getCarInfo(orderNo);
  }, [orderNo]);

  useEffect(() => {
    // 当还款方式发生变化的时候 需要重置数据
    setDataSource([]);
  }, [props.carPolicyRepayMode]);

  const columns: ProColumns<IspecialCarPolicyRepayItem>[] = [
    {
      title: <div className={styles.required}>需要特殊处理的车辆</div>,
      key: 'repaySchemaItemNo',
      dataIndex: 'repaySchemaItemNo',
      valueType: 'select',
      className: styles.repaySchemaItemNo,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
      fieldProps: {
        onChange(value: string) {
          console.log('value123', value);
        },
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        options: Object.keys(carInfoValueEnum).map((item: string) => ({
          label: (
            <div style={{ height: 50 }}>
              {carInfoValueEnum[item]?.licensePlateNo}
              <br />
              <div style={{ color: '#aaa' }}>
                {carInfoValueEnum[item]?.vin}
                {/* {
                carInfoValueEnum[item]?.isAllPreSettle &&  <Tooltip title="当前车辆所有的期数都是提前结清">
                ?
             </Tooltip>
              } */}
              </div>
            </div>
          ),
          value: item,
          disabled:
            carInfoValueEnum[item]?.isAllPreSettle ||
            dataSource
              .map((item1) => item1.repaySchemaItemNo)
              .includes(carInfoValueEnum[item]?.schemaItemNo),
        })),
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: any; value: string }) => {
          const dom = option.label?.props?.children;
          // dom?.[0] 车牌号
          // dom?.[1] <br/>
          // dom?.[2] <div>车架号</div> dom?.[2].props?.children 车架号
          const labelTemp = dom?.[0] + dom?.[2].props?.children;
          return labelTemp.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
      // valueEnum: carInfoValueEnum,
    },
    {
      title: <div className={styles.required}>是否提前结清</div>,
      key: 'isPreSettle',
      dataIndex: 'isPreSettle',
      valueType: 'select',
      formItemProps: {
        initialValue: '0',
      },
      valueEnum: isPreSettleMap,
    },
    {
      title: (
        <div className={styles.required}>
          实际还款金额
          <div style={{ color: '#ccc' }}>(即客户实际打款到账金额)</div>
        </div>
      ),
      dataIndex: 'repayAmount',
      width: 200,
      valueType: 'money',
      renderFormItem: (_, { record }) => {
        const { repaySchemaItemNo, remissionDetailList = [] } = record || {};
        const noJsx = <InputNumber style={{ width: '100%' }} />;
        const { preSettleAmount, preSettleDamages, carPolicyTermRspBOS = [] } =
          carInfoValueEnum?.[repaySchemaItemNo!] || {};
        const yesJsx = (
          <LookPreSettleDetail
            preSettleAmount={preSettleAmount}
            preSettleDamages={preSettleDamages}
            carPolicyTermRspBOS={carPolicyTermRspBOS}
            remissionDetailList={remissionDetailList}
            flush={flush}
          />
        );
        return record?.isPreSettle === '1' ? yesJsx : noJsx;
      },
    },
    {
      title: '减免总金额',
      dataIndex: 'remissionTodalAmount',
      renderFormItem(_, { record = {} }) {
        const { remissionDetailList } = record as any;
        const amount = remissionDetailList?.reduce(
          (pre: number, cur: any) => pre + cur.remissionAmount,
          0,
        );
        return <a>¥{amount || 0}</a>;
      },
    },
    {
      title: '操作',
      dataIndex: 'remissionDetailList',
      renderFormItem(_, { record }) {
        const { repaySchemaItemNo } = record || {};
        const licensePlateNo = carInfoValueEnum?.[repaySchemaItemNo!]?.text;
        const vin = carInfoValueEnum?.[repaySchemaItemNo!]?.vin;
        return (
          <RemissionModal
            licensePlateNo={licensePlateNo}
            vin={vin}
            carInfo={carInfo}
            setFlush={setFlush}
          >
            <a
              onClick={(e) => {
                if (!repaySchemaItemNo) {
                  message.error('请先选择车辆');
                  e.stopPropagation();
                }
              }}
            >
              设置减免
            </a>
          </RemissionModal>
        );
      },
    },
    {
      title: <a>删除</a>,
      dataIndex: 'delete',
      valueType: 'option',
    },
  ];

  function getCarInfoValueEnum() {
    const valueEnum: any = {};
    // console.log(carInfo);
    carInfo.forEach((item) => {
      const {
        licensePlateNo,
        schemaItemNo,
        preSettleAmount,
        vin,
        carPolicyTermRspBOS,
        preSettleDamages,
      } = item;

      // 如果该车辆的所有期数都是提前结清，那这辆车紧要禁用掉
      const isAllPreSettle = carPolicyTermRspBOS.every(
        (item) => item.status === EcarStatus?.SETTLE_EARLY,
      );

      valueEnum[schemaItemNo] = {
        text: licensePlateNo,
        preSettleAmount,
        preSettleDamages,
        vin,
        disabled: dataSource.map((item1) => item1.repaySchemaItemNo).includes(schemaItemNo),
        isAllPreSettle,
        licensePlateNo,
        schemaItemNo,
        carPolicyTermRspBOS,
      };
    });
    // debugger;
    // console.log(valueEnum, '---');
    setCarInfoValueEnum(valueEnum);
  }
  useEffect(() => {
    // dataSource 变化 无论是删除增加行 还是表单值变化 都重新 setDataSource
    // 增加删除行触发EditableProTable.onChange
    // 值变化触发 onValuesChange
    // dataSource 是最新的数据
    getCarInfoValueEnum();
    // console.log('getCarInfoValueEnum');
  }, [dataSource]);

  return (
    <EditableProTable<IspecialCarPolicyRepayItem>
      actionRef={actionRef}
      bordered
      rowKey="id"
      toolBarRender={false}
      value={dataSource}
      onChange={(values) => {
        console.log('values123', values);
        setDataSource(values);
      }}
      columns={columns}
      recordCreatorProps={{
        newRecordType: 'dataSource',
        position: 'top',
        // 新增一行的数据

        record: () => ({
          id: Date.now(),
        }),
      }}
      editable={{
        type: 'multiple',
        onValuesChange: (record, recordList) => {
          // 删除不会触发此函数
          console.log('record', record);
          console.log('recordList', recordList);

          setDataSource(recordList);
        },
        editableKeys,
        onChange: (keys) => {
          console.log('keys', keys);
          setEditableRowKeys(keys);
        },
        actionRender: (row, _, dom) => {
          return [dom.delete];
        },
      }}
    />
  );
};

export default memo(SpecialCarTable);
