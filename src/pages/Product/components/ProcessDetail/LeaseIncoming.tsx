import React, { useEffect } from 'react';
import { Form, Row, Input, Col, Radio, Checkbox } from 'antd';

type LeaseIncomingProps = {
  activateDTO?: any;
  isAddProduct?: boolean;
  baseInfoChange?: any;
  name?: string;
  drawerStatus?: boolean;
};
const LeaseIncoming: React.FC<LeaseIncomingProps> = ({
  activateDTO,
  isAddProduct,
  // baseInfoChange,
  drawerStatus,
  name,
}) => {
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue(activateDTO);
  }, [form, activateDTO]);
  return (
    <Form
      form={form}
      name={name}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      onValuesChange={(changedValues) => {
        if (changedValues?.whetherConfigLeadCost) {
          form.resetFields(['expenseItems']);
        }
      }}
    >
      <Row>
        <Col span={12}>
          <Form.Item
            name="activateFormula"
            label="进件金额公式"
            labelCol={{ span: 7 }}
            rules={[{ required: !drawerStatus }]}
          >
            <Input placeholder="" disabled={!isAddProduct} />
          </Form.Item>
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          <Form.Item
            name="defaultLevel"
            label="默认方案等级"
            labelCol={{ span: 7 }}
            rules={[{ required: !drawerStatus }]}
          >
            <Radio.Group disabled={!isAddProduct}>
              <Radio value="A">A</Radio>
              <Radio value="B">B</Radio>
              <Radio value="C">C</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="whetherConfigDeposit"
            label="是否配置定金"
            rules={[{ required: !drawerStatus }]}
          >
            <Radio.Group disabled={!isAddProduct}>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="whetherConfigLeadCost"
            label="是否配置缴纳前置费用"
            labelCol={{ span: 7 }}
            rules={[{ required: !drawerStatus }]}
          >
            <Radio.Group disabled={!isAddProduct}>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        <Form.Item noStyle shouldUpdate>
          {(formCur) => {
            const whetherConfigLeadCost = formCur.getFieldValue('whetherConfigLeadCost');
            return whetherConfigLeadCost ? (
              <Col span={12}>
                <Form.Item
                  name="expenseItems"
                  label="前置费用"
                  rules={[{ required: !drawerStatus }]}
                >
                  <Checkbox.Group disabled={!isAddProduct}>
                    <Checkbox value="BAIL" style={{ lineHeight: '32px' }}>
                      保证金
                    </Checkbox>
                    <Checkbox value="COMMISSION" style={{ lineHeight: '32px' }}>
                      手续费
                    </Checkbox>
                    <Checkbox value="LOW_DOWN_PAYMENT" style={{ lineHeight: '32px' }}>
                      首付
                    </Checkbox>
                  </Checkbox.Group>
                </Form.Item>
              </Col>
            ) : (
              ''
            );
          }}
        </Form.Item>
      </Row>
    </Form>
  );
};

export default React.memo(LeaseIncoming);
