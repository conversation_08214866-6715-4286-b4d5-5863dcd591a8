/*
 * @Author: your name
 * @Date: 2021-04-13 13:56:13
 * @LastEditTime: 2025-05-08 10:35:43
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/ModelLibrary/components/SecondCar.tsx
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { LICENSE_TYPES, LICENSE_TYPES_OPTIONS } from '@/enums';
import globalStyle from '@/global.less';
import { queryChannel } from '@/pages/Channel/service';
import LicenseTabsCom from '@/pages/Product/components/LicenseTabsCom';
import { validateCarCodeUnique } from '@/services/validate';
import { filterProps } from '@/utils/tools';
import { ExclamationCircleOutlined, UnlockOutlined } from '@ant-design/icons';
import { ProFormDependency } from '@ant-design/pro-components';
import ProForm, {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Button, Form, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useEffect, useRef, useState } from 'react';
import type { CarListItem } from '../data';
import {
  addCar,
  carExportAsync,
  delCarLibrary,
  modifyCarModelLibrary,
  queryCar,
  updateStatus,
} from '../service';
import styles from './carLibrary.less';

interface SecondCarTableProps {
  carNo?: string;
  carTypeFromQuery?: string | number;
}
const optionMap = {
  ADD: '新增',
  EDIT: '编辑',
  SHOW: '查看',
  DELETE: '删除',
  FORBID: '禁用',
  START: '启用',
};
const SecondCarTable: React.FC<SecondCarTableProps> = ({ carNo, carTypeFromQuery }) => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const [addVisible, handleAddVisible] = useState<boolean>(false);
  const [curRow, setCurrentRow] = useState<CarListItem>();
  const [disableEnvironmental, setDisableEnvironmental] = useState<boolean>(false);
  const [disableForm, setDisableForm] = useState<boolean>(false);
  const [curOptionType, setCurOptionType] = useState<string>('');
  const { cityList } = useModel('cityList');
  const licenseTabsRef = useRef<any>(null);
  const [licenseType, setLicenseType] = useState<string>(LICENSE_TYPES.AFFILIATE);

  const carType = 2;
  const transformLabel = (color: string, city: { value: string; label: string }) => {
    return [{ color, cityCode: city.value, cityName: city.label }];
  };
  const transformChannel = (channelList: []) => {
    return channelList.map(
      (item: { value: string; label: string | { props: { label: string } } }) => {
        return {
          value: item.value,
          label: item.label instanceof Object ? item.label?.props?.label : item.label,
        };
      },
    );
  };
  const handleConfirm = () => {
    const func =
      curOptionType === 'DELETE'
        ? delCarLibrary(curRow?.id)
        : updateStatus({
            id: curRow?.id,
            status: curRow?.statusFlag === 1 ? 2 : 1,
          });
    return func.then(() => true);
  };
  const [form] = Form.useForm();
  // 编辑和查看
  useEffect(() => {
    if (curRow?.energyType === 2) {
      setDisableEnvironmental(true);
    }
    form.setFieldsValue({
      ...curRow,
      city: {
        value: curRow?.colorCityDetail[0]?.cityCode,
        label: curRow?.colorCityDetail[0]?.cityName,
      },
      channelDetail: curRow?.channelDetail || [],
    });
  }, [curRow, addVisible, disableForm]);

  //针对跳转弹出弹窗
  useEffect(() => {
    if (carTypeFromQuery == carType) {
      queryCar({ carType, carNo }).then((res) => {
        if (res?.data?.length === 1) {
          setCurrentRow(res.data[0]);
          setCurOptionType('SHOW');
          handleAddVisible(true);
          setDisableForm(true);
        } else {
          message.error('车辆编号有误！');
        }
      });
    }
  }, []);
  const columns: ProColumns<CarListItem>[] = [
    {
      title: '车辆编号',
      dataIndex: 'carNo',
    },
    {
      title: '车型名称',
      dataIndex: 'carModel',
      search: false,
    },
    {
      title: '车型识别代码',
      dataIndex: 'carUniqueCode',
    },
    {
      title: '车辆类型',
      dataIndex: 'carType',
      search: false,
      valueEnum: {
        1: '新车',
        2: '二手车',
      },
    },
    {
      title: '车辆种类',
      dataIndex: 'carCategory',
      search: false,
      valueEnum: {
        1: '货车',
        2: '乘用车',
      },
    },
    {
      title: '能源类型',
      dataIndex: 'energyType',
      search: false,
      valueEnum: {
        1: '燃油',
        2: '纯电动',
        3: '油电混合',
        4: '插电式混动',
        5: '柴油',
      },
    },
    {
      title: '环保标准',
      dataIndex: 'environmentalType',
      search: false,
      valueEnum: {
        '4': '国四',
        '5': '国五',
        '6': '国六',
      },
    },
    {
      title: '厂商',
      dataIndex: 'manufacturer',
      search: false,
    },
    {
      title: '年代款',
      dataIndex: 'carYears',
      search: false,
    },
    {
      title: '排量',
      dataIndex: 'carEmission',
      search: false,
    },
    {
      title: '座位数',
      dataIndex: 'seatsNum',
      search: false,
    },
    {
      title: '颜色',
      dataIndex: 'color',
      search: false,
    },
    {
      title: '其他',
      dataIndex: 'otherMsg',
      search: false,
    },
    {
      title: '车型码',
      dataIndex: 'carModelCode',
      search: false,
    },
    {
      title: '发动机号',
      dataIndex: 'engineCode',
      search: false,
    },
    {
      title: '首次上牌日期',
      dataIndex: 'firstRegisterDate',
      search: false,
    },
    {
      title: '里程数',
      dataIndex: 'mileage',
      search: false,
    },
    {
      title: '车牌',
      dataIndex: 'licenseCode',
      search: false,
    },
    {
      title: '库存城市',
      dataIndex: 'city',
      search: false,
    },
    {
      title: '渠道配置',
      dataIndex: 'channel',
      search: false,
    },
    {
      title: '全包价',
      dataIndex: 'totalPrice',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'statusFlag',
      search: false,
      valueEnum: {
        1: { text: '启用', status: 'success' },
        2: { text: '禁用', status: 'error' },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 180,
      render: (_, record) => (
        <>
          <a
            onClick={() => {
              handleOptVisible(true);
              setCurOptionType('DELETE');
              setCurrentRow(record);
            }}
          >
            删除
          </a>
          <a
            style={{ marginLeft: 5 }}
            onClick={() => {
              handleOptVisible(true);
              setCurrentRow(record);
              setCurOptionType(record.statusFlag === 1 ? 'FORBID' : 'START');
            }}
          >
            {record.statusFlag === 1 ? '禁用' : '启用'}
          </a>
          <a
            style={{ marginLeft: 5 }}
            onClick={() => {
              setCurrentRow(record);
              setCurOptionType('SHOW');
              setDisableForm(true);
              handleAddVisible(true);
              // console.log(form, form.setFieldsValue, form.getFieldsValue());
            }}
          >
            查看详情
          </a>
          <a
            style={{ marginLeft: 5, color: '#fa8c16' }}
            onClick={() => {
              setCurrentRow({ ...record, otherMsg: '', totalPrice: undefined });
              handleAddVisible(true);
              setCurOptionType('ADD');
            }}
          >
            复制
          </a>
        </>
      ),
    },
  ];

  return (
    <>
      <ProTable<CarListItem>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        request={(params) => queryCar({ carType: 2, ...params })}
        search={{
          labelWidth: 120,
        }}
        columns={columns}
        params={{ licenseType }}
        headerTitle={
          <LicenseTabsCom
            defaultVisible={true}
            tabsRef={licenseTabsRef}
            onChange={(val) => {
              setLicenseType(val as string);
              formRef?.current?.submit();
            }}
          />
        }
        toolBarRender={() => {
          return [
            <AsyncExport
              getSearchDataTotal={async () => {
                const values = formRef.current?.getFieldsValue?.();
                const params = {
                  ...values,
                  carType: 2,
                  licenseType,
                };
                const data = await queryCar(filterProps(params));
                return data?.total;
              }}
              getSearchParams={() => {
                const values = formRef.current?.getFieldsValue?.();
                const params = {
                  ...values,
                  carType: 2,
                  licenseType,
                };
                return filterProps(params);
              }}
              exportAsync={carExportAsync}
              taskCode={[ItaskCodeEnValueEnum.CAR_MODEL_MGR]}
            />,
            <Button
              key="button"
              type="primary"
              onClick={() => {
                handleAddVisible(true);
                setCurOptionType('ADD');
              }}
            >
              添加车辆
            </Button>,
          ];
        }}
      />
      <ModalForm
        title="提示"
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
          afterClose: () => {
            setCurrentRow(undefined);
          },
        }}
        visible={optVisible}
        onVisibleChange={handleOptVisible}
        onFinish={async () => {
          const success: boolean = await handleConfirm();
          if (success) {
            message.success(`${optionMap[curOptionType]}成功`);
            actionRef?.current?.reload();
            handleOptVisible(false);
          }
          return true;
        }}
      >
        <div>
          <ExclamationCircleOutlined className={globalStyle.iconCss} />
          是否确认{optionMap[curOptionType]}该车型?
        </div>
      </ModalForm>
      <ModalForm
        title={`${optionMap[curOptionType]}车辆-二手车`}
        className={styles.formModal}
        layout="horizontal"
        form={form}
        modalProps={{
          centered: true,
          okText:
            curOptionType === 'SHOW' ? (
              <>
                <span>编辑</span>
                <UnlockOutlined />
              </>
            ) : (
              '提交'
            ),
          okButtonProps: { disabled: disableForm },
          afterClose: () => {
            setDisableEnvironmental(false);
            setDisableForm(false);
            setCurrentRow(undefined);
          },
          destroyOnClose: true,
        }}
        open={addVisible}
        onOpenChange={handleAddVisible}
        onFinish={async (values) => {
          if (curOptionType === 'SHOW') {
            setDisableForm(false);
            setCurOptionType('EDIT');
            return false;
          }
          const colorCity = transformLabel(values.color, values.city);
          const channelDetail = transformChannel(values.channelDetail);
          const success: boolean =
            curOptionType === 'ADD'
              ? await addCar({
                  ...values,
                  colorCity,
                  carType,
                  channelDetail,
                  totalPrice: values.totalPrice * 100,
                }).then(() => true)
              : await modifyCarModelLibrary({
                  ...values,
                  carNo: curRow?.carNo,
                  colorCity,
                  carType,
                  channelDetail,
                  totalPrice: values.totalPrice * 100,
                }).then(() => true);
          if (success) {
            handleAddVisible(false);
            message.success(`${optionMap[curOptionType]}成功`);
            // 刷新列表
            actionRef?.current?.reload();
          }
          // resetForm();
          return true;
        }}
      >
        <ProForm.Group>
          <ProFormText
            name="carModel"
            disabled={disableForm}
            rules={[{ required: !disableForm }]}
            width="sm"
            fieldProps={{ maxLength: 100 }}
            label="车型名称"
            placeholder="请输入车型名称"
          />
          <ProFormText
            name="carUniqueCode"
            disabled={disableForm}
            rules={[
              { required: !disableForm },
              {
                validator: async (_, value) => {
                  if (value) {
                    return curOptionType === 'ADD'
                      ? validateCarCodeUnique(value, carType, 1)
                      : true; // 编辑的时候不校验
                  }
                  return true;
                },
              },
            ]}
            fieldProps={{ maxLength: 20 }}
            width="sm"
            label="车辆识别代码"
            placeholder="请输入车辆识别代码"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: !disableForm }]}
            disabled={disableForm}
            options={[
              { value: 1, label: '货车' },
              { value: 2, label: '乘用车' },
            ]}
            placeholder="请选择车辆种类"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
            }}
            width="sm"
            name="carCategory"
            label="车辆种类"
          />
          <ProFormSelect
            name="energyType"
            disabled={disableForm}
            rules={[{ required: !disableForm }]}
            options={[
              { value: 1, label: '燃油' },
              { value: 2, label: '纯电动' },
              { value: 3, label: '油电混合' },
              { value: 4, label: '插电式混动' },
              { value: 5, label: '柴油' },
            ]}
            width="sm"
            label="能源类型"
            fieldProps={{
              onChange: (val: number) => {
                if (val === 2) {
                  setDisableEnvironmental(true);
                  form.setFieldsValue({ environmentalType: '' });
                } else {
                  setDisableEnvironmental(false);
                }
              },
            }}
            placeholder="请选择能源类型"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: !disableEnvironmental && !disableForm }]}
            disabled={disableForm || disableEnvironmental}
            options={[
              { value: '4', label: '国四' },
              { value: '5', label: '国五' },
              { value: '6', label: '国六' },
            ]}
            placeholder="请选择环保标准"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
            }}
            width="sm"
            name="environmentalType"
            label="环保标准"
          />
          <ProFormText
            disabled={disableForm}
            name="manufacturer"
            rules={[{ required: !disableForm }]}
            width="sm"
            fieldProps={{ maxLength: 20 }}
            label="厂商"
            placeholder="请输入厂商"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            name="carYears"
            disabled={disableForm}
            rules={[{ required: !disableForm }]}
            width="sm"
            placeholder="请输入年代款"
            label="年代款"
          />
          <ProFormText
            name="carEmission"
            disabled={disableForm}
            rules={[{ required: !disableForm }]}
            width="sm"
            fieldProps={{ maxLength: 20 }}
            label="排量"
            placeholder="请输入排量"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormDigit
            label="座位数"
            rules={[{ required: !disableForm }]}
            name="seatsNum"
            placeholder="请输入座位数"
            width="sm"
            disabled={disableForm}
            min={1}
          />
          <ProFormText
            name="color"
            rules={[{ required: !disableForm }]}
            disabled={disableForm}
            width="sm"
            label="颜色"
            placeholder="请输入颜色"
            fieldProps={{ maxLength: 20 }}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            label="其他"
            rules={[{ required: !disableForm }]}
            disabled={disableForm}
            name="otherMsg"
            fieldProps={{ maxLength: 20 }}
            width="sm"
            placeholder="请输入其他"
          />
          <ProFormText
            name="carModelCode"
            disabled={disableForm}
            rules={[{ required: !disableForm }]}
            width="sm"
            label="车型码"
            placeholder="请输入车型码"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            label="发动机号"
            disabled={disableForm}
            rules={[{ required: !disableForm }]}
            name="engineCode"
            // fieldProps={{ maxLength: 20 }}
            width="sm"
            placeholder="请输入发动机号"
          />
          <ProFormDatePicker
            name="firstRegisterDate"
            disabled={disableForm}
            rules={[{ required: !disableForm }]}
            width="sm"
            placeholder="请输入首次上牌日期"
            label="首次上牌日期"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormDigit
            name="mileage"
            width="sm"
            label="里程数（KM）"
            disabled={disableForm}
            placeholder="请输入里程数"
            fieldProps={{ min: 0, precision: 2 }}
            rules={[{ required: !disableForm }]}
          />
          <ProFormText
            label="车牌"
            rules={[{ required: !disableForm }]}
            disabled={disableForm}
            name="licenseCode"
            fieldProps={{ maxLength: 20 }}
            width="sm"
            placeholder="请输入车牌"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: !disableForm }]}
            request={async () => cityList}
            disabled={disableForm}
            name="city"
            placeholder="请选择库存城市"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
              labelInValue: true,
            }}
            width="sm"
            label="库存城市"
          />
          <ProFormDigit
            name="totalPrice"
            disabled={disableForm}
            width="sm"
            label="全包价（元）"
            placeholder="请输入全包价"
            fieldProps={{ min: 0, precision: 2 }}
            rules={[{ required: !disableForm }]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: !disableForm }]}
            options={LICENSE_TYPES_OPTIONS}
            placeholder="请选择上牌类型"
            disabled={disableForm}
            name="licenseType"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
            }}
            onChange={() => {
              form.setFieldsValue({ channelDetail: [] });
            }}
            width="sm"
            label="上牌类型"
            convertValue={(value) => value?.toString()}
          />
          <ProFormDependency name={['licenseType']}>
            {({ licenseType }) => {
              return (
                <ProFormSelect
                  key={licenseType}
                  rules={[{ required: !disableForm }]}
                  params={{ licenseType }}
                  request={async () => {
                    // 挂靠/个户渠道数据
                    if (
                      licenseType?.toString() === LICENSE_TYPES.AFFILIATE ||
                      licenseType?.toString() === LICENSE_TYPES.CUSTOMER
                    ) {
                      const { data: channelData } = await queryChannel({
                        status: 1,
                        licenseType,
                      }).catch(() => {});
                      return (
                        channelData?.map((item: { channelName: string; id: string }) => {
                          return { label: item.channelName, value: item.id };
                        }) || []
                      );
                    }
                    return [];
                  }}
                  placeholder="请选择渠道配置"
                  disabled={disableForm}
                  name="channelDetail"
                  debounceTime={60000}
                  fieldProps={{
                    showSearch: true,
                    optionFilterProp: 'label',
                    labelInValue: true,
                    mode: 'multiple',
                  }}
                  width="sm"
                  label="渠道配置"
                />
              );
            }}
          </ProFormDependency>
        </ProForm.Group>
      </ModalForm>
    </>
  );
};

export default SecondCarTable;
