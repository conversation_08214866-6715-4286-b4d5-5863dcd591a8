/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-20 16:06:52
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-11-13 17:50:38
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/index.tsx
import OrderPaymentTermList from './carInsurance/OrderPaymentTermList';
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import HeaderTab from '@/components/HeaderTab/index';
import { CLASSIFICATION } from '@/enums';
import { isCarInsuranceStoreUser, isChannelStoreUser } from '@/utils/utils';
import { ProFormSelect } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { useAccess } from '@umijs/max';
import { Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { KeepAlive } from 'react-activation';
import DetailList from './repay-detail';
import RepayManage from './repay-manage';
import RepayRegist from './repay-regist';
import { CLASSIFICATION_TYPE, firstTosecondaryClassificationMap } from './types';

const { TabPane } = Tabs;

const AfterLoanList: React.FC<any> = () => {
  const [classification, setClassification] = useState<CLASSIFICATION_TYPE>('FINANCE_LEASE'); // 一级分类
  const [secondaryClassification, setSecondaryClassification] = useState<string | undefined>(
    'FINANCE_LEASE',
  ); // 二级分类

  const access = useAccess();
  const [isShowExtra, setIsShowExtra] = useState(true); // 是否要展示Extra

  useEffect(() => {
    //如果是车险用户
    if (isCarInsuranceStoreUser(access)) {
      setClassification('SMALL_LOAN');
      setSecondaryClassification('CAR_INSURANCE');
    }
  }, [access]);

  function getExtra() {
    if (!isShowExtra) {
      return null;
    }

    return (
      <div style={{ display: 'flex', gap: 20, alignItems: 'center', flex: 1 }}>
        <ProFormSelect
          formItemProps={{
            style: { margin: 0 },
          }}
          fieldProps={{
            value: classification,
            onChange: (val) => {
              setClassification(val);
              setSecondaryClassification(undefined);
            },
            allowClear: false,
            disabled: isChannelStoreUser(access) || isCarInsuranceStoreUser(access),
          }}
          label="产品一级分类"
          valueEnum={CLASSIFICATION}
        />
        <ProFormSelect
          // 二级分类的切换 会导致table 的params变化 导致request请求
          formItemProps={{
            style: { margin: 0 },
          }}
          fieldProps={{
            style: { width: 200 },
            value: secondaryClassification,
            onChange: (val) => {
              setSecondaryClassification(val);
            },
            allowClear: false,
            disabled: isChannelStoreUser(access) || isCarInsuranceStoreUser(access),
          }}
          label="产品二级分类"
          valueEnum={firstTosecondaryClassificationMap[classification]}
        />
      </div>
    );
  }
  console.log(
    'classification',
    classification,
    'secondaryClassifcation二级分类',
    secondaryClassification,
  );
  return (
    <>
      <PageContainer extra={getExtra()}>
        <Tabs
          animated={{ inkBar: true, tabPane: true }}
          style={{ background: '#fff', paddingLeft: 10 }}
          onChange={(activeKey) => {
            if (['4', '5', '6'].includes(activeKey)) {
              setIsShowExtra(false);
            } else {
              setIsShowExtra(true);
            }
          }}
        >
          {/* {!isChannelUser && ( */}
          <>
            {access.hasAccess('bill_cms_repay_list_postLoanMng_afterLoanList') && (
              <TabPane tab="还款管理" key="1">
                <RepayManage
                  classification={classification}
                  secondaryClassification={secondaryClassification}
                />
              </TabPane>
            )}
            {access.hasAccess('repayment_detail_list_postLoanMng_afterLoanList') && (
              <TabPane tab="还款明细" key="2">
                <DetailList
                  classification={classification}
                  secondaryClassification={secondaryClassification}
                />
              </TabPane>
            )}
            {access.hasAccess('repay_recording_postLoanMng_afterLoanList') && (
              <TabPane tab="还款登记" key="3">
                <RepayRegist
                  classification={classification}
                  secondaryClassification={secondaryClassification}
                />
              </TabPane>
            )}
          </>
          {/* )} */}

          {/* {access.hasAccess('bill_order_list_postLoanMng_afterLoanList') && (
            <>
              <TabPane tab="车险-订单期账" key="4">
                <OrderPaymentTermList />
              </TabPane>
              <TabPane tab="车险-车辆期账" key="5">
                <CarPaymentTermList />
              </TabPane>
              <TabPane tab="车险-车辆总账" key="6">
                <CarTotalBillList />
              </TabPane>
            </>
          )} */}
        </Tabs>
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name="businessMng/postLoanMng/after-loan-list">
      <AfterLoanList />
    </KeepAlive>
  </>
);
