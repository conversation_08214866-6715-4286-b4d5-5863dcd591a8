/*
 * @Author: your name
 * @Date: 2021-06-28 11:17:53
 * @LastEditTime: 2021-07-29 14:02:49
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/WithHold/index.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { ExclamationCircleOutlined, UnlockOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Form, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useEffect, useRef, useState } from 'react';
import type { LicensedCitiesListItem } from './data';
import {
  addLicensedCities,
  delLicensedCities,
  getLicenseCityList,
  modifyLicensedCities,
} from './service';

const optionMap = {
  EDIT: { label: '编辑' },
  START: { label: '启用', value: 1 },
  FORBID: { label: '禁用', value: 0 },
  DELETE: { label: '删除' },
  ADD: { label: '添加' },
  SHOW: { label: '查看' },
};

const LicensedCities: React.FC<{}> = () => {
  const [curOption, setCurOption] = useState<string>('');
  const [addVisible, handleAddVisible] = useState<boolean>(false);
  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const [disableForm, setDisableForm] = useState<boolean>(false);
  const [curRow, setCurrentRow] = useState<LicensedCitiesListItem>();
  const [form] = Form.useForm();
  // 编辑查看回显
  useEffect(() => {
    form.setFieldsValue({
      cityName: curRow?.cityName,
    });
  }, [curRow, disableForm, addVisible]);
  const columns: ProColumns<LicensedCitiesListItem>[] = [
    {
      title: '上牌城市编码',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '上牌城市名称',
      dataIndex: 'cityName',
      key: 'cityName',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },

    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        1: { text: '启用', status: 'Success' },
        0: { text: '禁用', status: 'Error' },
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 200,
      fixed: 'right',
      valueType: 'option',
      render: (text, row) => (
        <>
          <a
            className={globalStyle?.mr10}
            onClick={() => {
              setCurrentRow(row);
              setCurOption('DELETE');
              handleOptVisible(true);
            }}
          >
            删除
          </a>
          <a
            className={globalStyle?.mr10}
            onClick={() => {
              setCurrentRow(row);
              setCurOption(row?.status ? 'FORBID' : 'START');
              handleOptVisible(true);
            }}
          >
            {optionMap[row?.status ? 'FORBID' : 'START']?.label}
          </a>
          <a
            className={globalStyle?.mr10}
            onClick={() => {
              setCurOption('SHOW');
              setCurrentRow(row);
              setDisableForm(true);
              handleAddVisible(true);
            }}
          >
            查看详情
          </a>
        </>
      ),
    },
  ];

  // 操作栏
  const handleOpt = () => {
    return curOption === 'DELETE'
      ? delLicensedCities(curRow?.id).then(() => true)
      : modifyLicensedCities({
          id: curRow?.id,
          status: curRow?.status ? 0 : 1,
          cityName: curRow?.cityName,
        }).then(() => true);
  };

  // 确认
  const handleConfirm = (values: { cityName: string }) => {
    return curOption === 'EDIT'
      ? modifyLicensedCities({
          id: curRow?.id,
          status: curRow?.status,
          cityName: values?.cityName,
        }).then(() => {
          return true;
        })
      : addLicensedCities(values?.cityName).then(() => {
          return true;
        });
  };
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<LicensedCitiesListItem>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="userNo"
          search={false}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  handleAddVisible(true);
                  setCurOption('ADD');
                  setCurrentRow(undefined);
                }}
              >
                添加上牌城市
              </Button>,
            ];
          }}
          request={(params) => getLicenseCityList(params)}
        />
      </PageContainer>
      <ModalForm
        title="提示"
        width="400px"
        modalProps={{
          centered: true,
        }}
        visible={optVisible}
        onVisibleChange={handleOptVisible}
        onFinish={async () => {
          const success: boolean = await handleOpt();
          if (success) {
            message.success(`${optionMap[curOption].label}成功`);
            actionRef?.current?.reload();
            handleOptVisible(false);
          }
          return true;
        }}
      >
        <div>
          <ExclamationCircleOutlined className={globalStyle.iconCss} />
          是否确认{optionMap[curOption]?.label}该上牌城市?
        </div>
      </ModalForm>
      <ModalForm
        title={`${optionMap[curOption]?.label}上牌城市`}
        width="400px"
        layout="horizontal"
        form={form}
        modalProps={{
          centered: true,
          okText:
            curOption === 'SHOW' ? (
              <>
                <span>编辑</span>
                <UnlockOutlined />
              </>
            ) : (
              '提交'
            ),
          okButtonProps: { disabled: disableForm },
          afterClose: () => {
            setDisableForm(false);
          },
          destroyOnClose: true,
        }}
        visible={addVisible}
        onVisibleChange={handleAddVisible}
        onFinish={async (values) => {
          if (curOption === 'SHOW') {
            setDisableForm(false);
            setCurOption('EDIT');
            return false;
          }
          const success: boolean = await handleConfirm(values);
          if (success) {
            message.success(`${optionMap[curOption].label}成功`);
            actionRef?.current?.reload();
            handleOptVisible(false);
          }
          return true;
        }}
      >
        <ProFormText
          name="cityName"
          disabled={disableForm}
          rules={[{ required: true }]}
          fieldProps={{ maxLength: 100 }}
          width="sm"
          label="上牌城市名称"
          placeholder="请输入上牌城市名称"
        />
      </ModalForm>
    </>
  );
};

export default LicensedCities;
