import type { IbillListItem } from '@/pages/AfterLoan/carInsurance/types';
import type { ProColumns } from '@ant-design/pro-components';

export const CarOfflineRepayColumns: ProColumns<IbillListItem>[] = [
  {
    title: '还款金额（元）',
    dataIndex: 'expectRepayAmount',
  },
  {
    title: '还款顺序',
    dataIndex: 'index',
    valueType: 'indexBorder',
  },
  {
    title: '账单ID',
    dataIndex: 'billNo',
  },
  {
    title: '期数',
    dataIndex: 'termNumber',
  },
  {
    title: '订单号',
    dataIndex: 'orderNo',
  },
  {
    title: '车架号',
    dataIndex: 'subjectMatterNo',
  },
  {
    title: '客户名称',
    dataIndex: 'accountName',
  },

  {
    title: '未还总额',
    dataIndex: 'totalAmountUnpaid',
  },
  {
    title: '未还本金',
    dataIndex: 'totalPrincipalUnpaid',
  },
  {
    title: '未还利息',
    dataIndex: 'totalInterestUnpaid',
  },
  {
    title: '未还罚息',
    dataIndex: 'totalOverduePenaltyUnpaid',
  },
  {
    title: '未还违约金',
    dataIndex: 'totalBreach',
  },
  {
    title: '未还滞纳金',
    dataIndex: 'totalLate',
  },

  {
    title: '应还款总额',
    dataIndex: 'totalAmountDue',
  },
  {
    title: '应还本金',
    dataIndex: 'totalPrincipalDue',
  },
  {
    title: '应还利息',
    dataIndex: 'totalInterestDue',
  },

  {
    title: '应还罚息',
    dataIndex: 'totalOverduePenaltyDue',
  },

  {
    title: '应还违约金',
    dataIndex: 'totalBreachDue',
  },

  {
    title: '应还滞纳金',
    dataIndex: 'totalLateDue',
  },
  {
    title: '更新日期',
    dataIndex: 'updateAt',
  },
  {
    title: '放款日期',
    dataIndex: 'lendingTime',
  },
  {
    title: '应还日期',
    dataIndex: 'dueDate',
  },

  {
    title: '账单状态',
    dataIndex: 'statusName',
  },
  {
    title: '逾期天数',
    dataIndex: 'overdueDateNumber',
  },
];

export const OrderOfflineRepayColumns: ProColumns<IbillListItem>[] = [
  {
    title: '账单ID',
    dataIndex: 'billNo',
  },
  {
    title: '期数',
    dataIndex: 'termNumber',
  },
  {
    title: '订单号',
    dataIndex: 'orderNo',
  },

  {
    title: '未还总额',
    dataIndex: 'totalAmountUnpaid',
  },
  {
    title: '未还本金',
    dataIndex: 'totalPrincipalUnpaid',
  },
  {
    title: '未还利息',
    dataIndex: 'totalInterestUnpaid',
  },
  {
    title: '未还罚息',
    dataIndex: 'totalOverduePenaltyUnpaid',
  },
  {
    title: '未还违约金',
    dataIndex: 'totalBreach',
  },
  {
    title: '未还滞纳金',
    dataIndex: 'totalLate',
  },

  {
    title: '应还款总额',
    dataIndex: 'totalAmountDue',
  },
  {
    title: '应还本金',
    dataIndex: 'totalPrincipalDue',
  },
  {
    title: '应还利息',
    dataIndex: 'totalInterestDue',
  },

  {
    title: '应还罚息',
    dataIndex: 'totalOverduePenaltyDue',
  },

  {
    title: '应还违约金',
    dataIndex: 'totalBreachDue',
  },

  {
    title: '应还滞纳金',
    dataIndex: 'totalLateDue',
  },
  {
    title: '更新日期',
    dataIndex: 'updateAt',
  },
  {
    title: '放款日期',
    dataIndex: 'lendingTime',
  },
  {
    title: '应还日期',
    dataIndex: 'dueDate',
  },
  {
    title: '实际结清日期',
    dataIndex: 'clearTime',
  },

  {
    title: '账单状态',
    dataIndex: 'statusName',
  },
  {
    title: '逾期天数',
    dataIndex: 'overdueDateNumber',
  },
];
