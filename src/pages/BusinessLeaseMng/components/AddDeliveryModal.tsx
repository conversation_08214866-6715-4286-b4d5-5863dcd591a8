import { CommonImageUpload } from '@/components/ReleaseCom';
import { PlusOutlined } from '@ant-design/icons'; // @ts-ignore
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { Form, message } from 'antd';
import React, { useState } from 'react';
import { getCarInsuranceFrameNumberOcr, saveDeliveryCarInfo } from '../service';

export type AddDeliveryModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  otherRemark?: string;
  licenseCode?: string;
  // formEdit?: Record<string, any>;
  orderNo: string;
};

const AddDeliveryModal: React.FC<AddDeliveryModalProps> = (props) => {
  const [form] = Form.useForm();
  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  const [ocrInfo, setOcrInfo] = useState<{
    vinMatched: boolean;
    vinCode: string;
    validVin: boolean;
  } | null>(null);

  console.log('ocrInfo', ocrInfo);

  const [ocrFetching, setOcrFetching] = useState<boolean>(false);

  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  // const mapFileListData = (data: any) => {
  //   let index = 0;
  //   const results = {
  //     groupPhotoOfPeopleAndVehicles: [
  //       {
  //         uid: 'groupPhotoOfPeopleAndVehicles',
  //         name: 'groupPhotoOfPeopleAndVehicles',
  //         status: 'done',
  //         url: data.groupPhotoOfPeopleAndVehicles,
  //       },
  //     ],
  //     frameNumberPhoto: [
  //       {
  //         uid: 'frameNumberPhoto',
  //         name: 'frameNumberPhoto',
  //         status: 'done',
  //         url: data.frameNumberPhoto,
  //       },
  //     ],
  //     otherDeliveryDataPhotoList: data.otherDeliveryDataPhoto?.map((item: any) => {
  //       index++;
  //       return {
  //         uid: item.name + index,
  //         name: item.name,
  //         status: 'done',
  //         url: item.otherDeliveryDataPhoto,
  //       };
  //     }),
  //   };
  //   return results;
  // };

  // ocr 车架号
  const ocrHandler = (files: any[]) => {
    console.log('files', files);
    setOcrFetching(true);
    getCarInsuranceFrameNumberOcr({
      orderNo: props.orderNo,
      filePath: files[0]?.response?.data?.filePath || '',
    })
      .then((vinOcrRes) => {
        setOcrInfo({
          ...vinOcrRes?.data,
        });
        if (!vinOcrRes?.data?.vinMatched) {
          message.error('车架号不一致，请检查上传图片是否正确或清晰', 3);
          return;
        }

        if (!vinOcrRes?.data?.validVin) {
          message.error('车架号不一致', 3);
          return;
        }
      })
      .catch((e) => {
        message.error('OCR识别失败');
        setOcrInfo(null);
      })
      .finally(() => setOcrFetching(false));
  };

  return (
    <>
      <ModalForm
        title="交车资料"
        width={800}
        layout="horizontal"
        open={props.modalVisible}
        onOpenChange={props.onVisibleChange}
        form={form}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            handleFileList({});
          },
        }}
        onFinishFailed={(e) => {
          // 文件上传过程中是弱提示，不强制设置error文案，所以error字符串置空
          const needCheckfileNames = [
            'frameNumberPhoto',
            'groupPhotoOfPeopleAndVehicles',
            'otherDeliveryDataPhotoList',
          ];
          if (
            e?.errorFields?.some((field: any) => {
              return needCheckfileNames.includes(field?.name?.[0]) && field?.errors?.[0] === ' ';
            })
          ) {
            message.warning('文件正在上传，请稍等！');
          }
        }}
        onFinish={async (values) => {
          // ocr时不能直接提交
          if (ocrFetching) return;
          try {
            const params = {
              orderNo: props.orderNo,
              frameNumberPhoto:
                allFileList.frameNumberPhoto[0]?.response?.data?.netWorkPath ||
                allFileList.frameNumberPhoto[0]?.url ||
                '',
              groupPhotoOfPeopleAndVehicles:
                allFileList.groupPhotoOfPeopleAndVehicles[0]?.response?.data?.netWorkPath ||
                allFileList.groupPhotoOfPeopleAndVehicles[0]?.url ||
                '',
              otherDeliveryDataPhotoList: allFileList.otherDeliveryDataPhotoList?.map(
                (item: any) => {
                  return {
                    otherDeliveryDataPhoto: item.response?.data?.netWorkPath || item?.url || '',
                    type: 2, //1. 用户上传 2.后台上传
                    name: item.name,
                  };
                },
              ),
              licenseCode: values?.licenseCode,
              vinOcrInfo: ocrInfo,
            };

            await saveDeliveryCarInfo(params);
            message.success('添加成功');
            props.onOk();
          } catch (e) {
            console.log(e);
          }
          // return true;
        }}
      >
        <CommonImageUpload
          extraTrack={false}
          extra="请将车辆处于启动状态，打开双闪，主驾车门打开，司机站在车门后，照片要求能看清楚车牌号码及车辆正面。"
          label="人车合影照片"
          icon={<PlusOutlined />}
          labelCol={{ span: 4 }}
          name="groupPhotoOfPeopleAndVehicles"
          max={1}
          mapFileList={mapFileList}
          buttonProps={{ type: 'text' }}
          listType="picture-card"
          accept=".png,.jpg,.jpeg,.gif"
          rules={[{ required: true, message: '请上传人车合影照片' }]}
          size={10}
        />
        <CommonImageUpload
          extraTrack={false}
          needOcr={true}
          ocrHandler={ocrHandler}
          extra="请于车辆前挡风玻璃的下方，拍摄清晰的车架号。"
          label="车架号照片"
          labelCol={{ span: 4 }}
          name="frameNumberPhoto"
          max={1}
          size={10}
          icon={<PlusOutlined />}
          buttonProps={{ type: 'text' }}
          listType="picture-card"
          mapFileList={mapFileList}
          accept=".png,.jpg,.jpeg,.gif"
          rules={[{ required: true, message: '请上传车架号照片' }]}
        />
        <CommonImageUpload
          extraTrack={false}
          label="其他交车资料"
          labelCol={{ span: 4 }}
          name="otherDeliveryDataPhotoList"
          max={10}
          size={10}
          icon={<PlusOutlined />}
          buttonProps={{ type: 'text' }}
          listType="picture-card"
          mapFileList={mapFileList}
          accept=".png,.jpg,.jpeg,.gif"
        />
        {/* <CommonDraggleUpload
          extra="请将车辆处于启动状态，打开双闪，主驾车门打开，司机站在车门后，照片要求能看清楚车牌号码及车辆正面。"
          extraTrack={false}
          // extra="支持扩展名：.png,.jpg,.jpeg,.gif,.bmp单个文件最大50M"
          label="人车合影照片"
          labelCol={{ span: 4 }}
          name="groupPhotoOfPeopleAndVehicles"
          max={1}
          size={50}
          listType="text"
          fileListEdit={formEdit?.groupPhotoOfPeopleAndVehicles}
          mapFileList={mapFileList}
          accept=".png,.jpg,.jpeg,.gif,.bmp"
          rules={[{ required: true, message: '请上传人车合影照片' }]}
        />
        <CommonDraggleUpload
          extra="请于车辆前挡风玻璃的下方，拍摄清晰的车架号。"
          extraTrack={false}
          // extra="支持扩展名：.png,.jpg,.jpeg,.gif,.bmp单个文件最大50M"
          label="车架号照片"
          labelCol={{ span: 4 }}
          name="frameNumberPhoto"
          max={1}
          size={50}
          listType="text"
          fileListEdit={formEdit?.frameNumberPhoto}
          mapFileList={mapFileList}
          accept=".png,.jpg,.jpeg,.gif,.bmp"
          rules={[{ required: true, message: '请上传车架号照片' }]}
        />
        <CommonDraggleUpload
          // extra="支持扩展名：.png,.jpg,.jpeg,.gif,.bmp单个文件最大50M"
          label="其他交车资料"
          labelCol={{ span: 4 }}
          name="otherDeliveryDataPhotoList"
          max={10}
          size={50}
          listType="text"
          multiple={true}
          fileListEdit={formEdit?.otherDeliveryDataPhotoList}
          mapFileList={mapFileList}
          previewType="multiple"
          accept=".png,.jpg,.jpeg,.gif,.bmp"
          // rules={[{ required: true, message: '其他交车资料' }]}
        /> */}
        <ProFormText
          name="licenseCode"
          labelCol={{ span: 4 }}
          initialValue={props.licenseCode}
          width="md"
          fieldProps={{ maxLength: 20 }}
          disabled={!!props.licenseCode}
          label="车牌号"
          placeholder="请输入车牌号"
        />
      </ModalForm>
    </>
  );
};

export default AddDeliveryModal;
