/*
 * @Author: your name
 * @Date: 2022-02-18 17:52:36
 * @LastEditTime: 2022-11-16 10:46:24
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/Product/product-bill-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import React from 'react';
import BasicInfo from './components/BasicInfo';
import OtherInfo from './components/OtherInfo';
import ProProcessConfig from './components/ProProcessConfig';
import RepaymentConfig from './components/RepaymentConfig';
import { queryProductDetail } from './service';

const ProductDetail: React.FC<any> = () => {
  const { productCode } = history.location.query;
  const { data } = useRequest(() => {
    return queryProductDetail(productCode);
  });
  // console.log(data);
  const baseInfo = {
    classification: data?.classification || '',
    productName: data?.productName || '',
    secondaryClassification: data?.secondaryClassification || '',
    userType: data?.userType || '',
    selfType: data?.selfType || '',
    quotaType: data?.quotaType || '',
  };
  const otherInfo = {
    status: data?.status,
    productDesc: data?.productDesc,
  };
  const repayConfig = {
    costConfigDTO: data?.costConfigDTO,
    costParameterDTO: data?.costParameterDTO,
  };
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <BasicInfo baseInfo={baseInfo} />
        <ProProcessConfig data={data} />
        <RepaymentConfig data={repayConfig} />
        <OtherInfo otherInfo={otherInfo} />
      </PageContainer>
    </>
  );
};

export default ProductDetail;
