// 垫付黑名单
import { getAllChannelNameEnum } from '@/services/enum';
import type { ActionType } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { <PERSON><PERSON>, Card } from 'antd';
import React, { useMemo, useRef, useState } from 'react';
import { changePayConfigStatus, getPayBlackList } from '../service';
import type { PayBlackListItem } from '../types';
import AddPayBlackModal from './AddPayBlackListModal';

enum STATUS {
  DEL = 9,
}
const PayBlackList = () => {
  const [showModal, setShowModal] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const actionRef = useRef<ActionType>();

  const getAllChannelNameEnumMemo = useMemo(async () => {
    return await getAllChannelNameEnum();
  }, []);
  const handleDel = async (id: string) => {
    const params = {
      id,
      status: STATUS.DEL,
    };
    setBtnLoading(true);
    try {
      await changePayConfigStatus(params);
      actionRef.current?.reloadAndRest?.();
      setBtnLoading(false);
    } catch {
      setBtnLoading(false);
    }
  };

  const columns = [
    {
      title: '业务线',
      dataIndex: 'productCode',
      valueEnum: {
        '0201': '融资租赁',
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channel',
      valueType: 'select',
      request: () => {
        return getAllChannelNameEnumMemo;
      },
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
    },
    {
      title: '操作',
      render: (dom, record) => {
        return (
          <Button
            type="link"
            onClick={() => {
              handleDel(record.configId);
            }}
            loading={btnLoading}
          >
            删除
          </Button>
        );
      },
    },
  ];

  return (
    <Card
      title="垫付黑名单"
      extra={
        <Button
          type="primary"
          onClick={() => {
            setShowModal(true);
          }}
        >
          加入黑名单
        </Button>
      }
      style={{ marginBottom: 20 }}
    >
      <ProTable<PayBlackListItem>
        actionRef={actionRef}
        search={false}
        rowKey="configId"
        columns={columns}
        request={(params) => getPayBlackList(params)}
      />
      <AddPayBlackModal
        open={showModal}
        onSuccess={() => {
          setShowModal(false);
          actionRef.current?.reload();
        }}
        onCancel={() => {
          setShowModal(false);
        }}
      />
    </Card>
  );
};

export default PayBlackList;
