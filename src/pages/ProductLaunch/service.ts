/*
 * @Author: your name
 * @Date: 2022-01-05 15:25:17
 * @LastEditTime: 2023-10-23 17:03:20
 * @LastEditors: oak.yang <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/ProductLaunch/service.ts
 */
import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type {
  TCarListParams,
  TChannelListParams,
  TInventoryCityParams,
  TProductParams,
  TSaveOrDelConfigInfoParams,
} from './data.d';

/**
 * 配置渠道/配置车辆/配置库存城市 || 删除
 */
export async function saveOrDelConfigInfo(data: TSaveOrDelConfigInfoParams) {
  return request('/bizadmin/lease/productlaunch/saveOrDelProductLaunch', {
    method: 'POST',
    data,
    headers,
  });
}

/**
 * 产品投放查询车列表
 */
export async function getCarList(data: TCarListParams) {
  return request('/bizadmin/lease/productlaunch/getCarByPage', {
    method: 'POST',
    data,
    headers,
  });
}

/**
 * 产品投放查询渠道列表
 */
export async function getChannelList(data: TChannelListParams) {
  return request('/bizadmin/lease/productlaunch/getChannelByPage', {
    method: 'POST',
    data,
    headers,
  });
}

/**
 * 产品投放-库存城市信息列表
 */
export async function getInventoryCityList(data: TInventoryCityParams) {
  return request('/bizadmin/lease/productlaunch/getByPage', {
    method: 'POST',
    data,
    headers,
  });
}

/**
 * 产品投放查询产品列表
 */
export async function getProductList(data: TProductParams) {
  return request('/bizadmin/lease/productlaunch/getProductByPage', {
    method: 'POST',
    data,
    headers,
  });
}

export async function findQuery(params: Record<string, string>) {
  return (
    request('/bizadmin/lease/productlaunch/findQuery', {
      method: 'get',
      params,
      headers,
    }).then((res) => {
      const mapDate = params?.businessKey ? res.data.bussinessKey : res.data.carName;
      // console.log(mapDate,params?.businessKey)
      return mapDate.map((item: { label: string; value: string }, key: number) => {
        return { key, ...item };
      });
    }) || []
  );
}
