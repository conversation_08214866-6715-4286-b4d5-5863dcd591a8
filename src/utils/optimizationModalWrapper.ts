/*
 * @Author: your name
 * @Date: 2022-02-11 17:28:53
 * @LastEditTime: 2023-03-28 14:48:21
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/utils/optimizationModalWrapper.ts
 */
import type { ComponentClass, FunctionComponent } from 'react';
import React from 'react';
import ReactDOM from 'react-dom';

type ComponentTypes = string | FunctionComponent<any> | ComponentClass<any>;

const optimizationModalWrapper = (component: ComponentTypes) => {
  const container = document.createDocumentFragment();
  const destroy = () => {
    //删除挂载的React组件
    ReactDOM.unmountComponentAtNode(container);
  };
  // 渲染组件
  const render = (restProps: Record<string, any>) => {
    //将modal组件内容挂载到fragement上,添加一些close,visible公共的属性
    const comModalVDom = React.createElement(component, {
      ...restProps,
      close: destroy,
      visible: true,
    });
    //将统一处理添加close和visible的属性，挂载在container上
    ReactDOM.render(comModalVDom, container);
  };

  return (restProps: Record<string, any>) => {
    render(restProps);
  };
};

//这里的主要目的就是，统一处理了下modal框自己的显示和隐藏。
//其他的modal需要的数据或者属性，利用函数调用的方式，通过restProps传入
export default optimizationModalWrapper;
