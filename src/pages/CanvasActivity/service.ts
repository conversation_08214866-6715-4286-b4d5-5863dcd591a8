/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-07-19 18:08:43
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-07-19 18:14:10
 * @FilePath: /code/lala-finance-biz-web/src/pages/CanvasActivity/service.ts
 * @Description: /CanvasActivity/service
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';

export async function getStandardPrizeGrantPage(data?: any) {
  return request('/bizadmin/marketing/standard/prize/grant/page', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
    ifTrimParams: true,
  });
}
