/*
 * @Author: alan771.tu
 * @Date: 2024-11-27 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-27 09:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/context/CarInsuranceDetailContext.tsx
 * @Description: 车险保单详情的context，用于存储交换上下文内容
 */

import { message } from 'antd';
import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';
import { PolicyEvent } from '../enum';
import {
  getCarInsurancePolicyDetail,
  recordCarInsurancePolicyException,
  recordCarInsurancePolicyManualApprove,
  recordCarInsurancePolicyReject,
  updateCarInsurancePolicyFile,
} from '../services';

export const CarInsuranceDetailContext = createContext<{
  carInsurancePolicyDetail: any;
  setCarInsurancePolicyDetail: (detail: any) => void;
  loading: boolean;
  updateLoading: (loading: boolean) => void;
  request: (event?: string, params?: any) => Promise<any>;
}>({
  carInsurancePolicyDetail: {},
  setCarInsurancePolicyDetail: () => {},
  loading: false,
  updateLoading: () => {},
  request: () => Promise.resolve(null),
});

export const useCarInsuranceDetailContext = () => {
  return useContext(CarInsuranceDetailContext);
};

export const CarInsuranceDetailProvider = (props: { children: React.ReactNode }) => {
  const { children } = props;
  const [carInsurancePolicyDetail, setCarInsurancePolicyDetail] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const uri = new URLSearchParams(window.location.search);
  const id = uri.get('id');

  const changePolicyStatus = useCallback(
    async (
      queryParams: { event: PolicyEvent; [key: string]: any },
      needReason = false,
      refreshFn?: () => void,
    ) => {
      if (needReason && !queryParams?.reasonMsg) {
        return;
      }
      setLoading(true);
      const params = {
        id: Number(id),
        // ...queryParams, // 透传参数，涵盖事件类型、修改的保单参数等，常用操作为保单的驳回、异常、审核，资料修改、批单修改
        ...(needReason ? { ...queryParams?.reasonMsg } : {}),
      };

      const eventFnMap = {
        [PolicyEvent.APPROVE_POLICY]: recordCarInsurancePolicyManualApprove, // 手动审核通过
        [PolicyEvent.REJECT_POLICY]: recordCarInsurancePolicyReject, // 手动审核驳回
        [PolicyEvent.POLICY_EXCEPTION]: recordCarInsurancePolicyException, // 手动审核保单异常
      };

      if (!eventFnMap[queryParams.event]) {
        return;
      }

      eventFnMap[queryParams.event](params)
        .then(() => {
          message.success('操作成功');
          refreshFn?.();
        })
        .catch(() => {
          message.error('操作失败');
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [id],
  );

  // 更新保单相关文件（批单、资料）
  const updatePolicyFile = useCallback(async (params: any, refreshFn?: () => void) => {
    setLoading(true);
    updateCarInsurancePolicyFile(params)
      .then(() => {
        message.success('操作成功');
        refreshFn?.();
      })
      .catch(() => {
        message.error('操作失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // 请求保单详情, params 可以通过订阅函数透传参数
  const request = useCallback(
    async (event?: string, params?: any) => {
      switch (event) {
        case 'approve':
          changePolicyStatus({ event: PolicyEvent.APPROVE_POLICY }, false, () => {
            request();
          });
          break;
        case 'reject':
          changePolicyStatus(
            { event: PolicyEvent.REJECT_POLICY, reasonMsg: { reason: params?.rejectMsg } },
            true,
            () => {
              request();
            },
          );
          break;
        case 'markAbnormal':
          changePolicyStatus(
            {
              event: PolicyEvent.POLICY_EXCEPTION,
              reasonMsg: { reason: params?.abnormalMsg },
            },
            true,
            () => {
              request();
            },
          );
          break;
        case 'uploadOtherMaterial':
          updatePolicyFile({ id, extendInfo: JSON.stringify(params.extendInfo) }, () => {
            request();
          });
          break;
        case 'reUploadEndorsement':
          updatePolicyFile({ id, extendInfo: JSON.stringify(params.extendInfo) }, () => {
            request();
          });
          break;
        case 'deleteEndorsement':
          updatePolicyFile({ id, extendInfo: JSON.stringify(params.extendInfo) }, () => {
            request();
          });
          break;
        case 'deleteOtherMaterial':
          updatePolicyFile({ id, extendInfo: JSON.stringify(params.extendInfo) }, () => {
            request();
          });
          break;
        default:
          // 默认请求保单详情
          if (!id) return;
          setLoading(true);
          getCarInsurancePolicyDetail({ id })
            .then((res) => {
              if (res) {
                setCarInsurancePolicyDetail(res?.data || {});
              }
            })
            .catch(() => {
              message.error('获取保单详情失败');
            })
            .finally(() => {
              setLoading(false);
            });
          break;
      }
    },
    [id, changePolicyStatus, updatePolicyFile],
  );

  useEffect(() => {
    request();
  }, [request]);

  return (
    <CarInsuranceDetailContext.Provider
      value={{
        loading,
        updateLoading: setLoading,
        request,
        carInsurancePolicyDetail,
        setCarInsurancePolicyDetail,
      }}
    >
      {children}
    </CarInsuranceDetailContext.Provider>
  );
};
