/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2023-08-31 17:10:35
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/bill-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab/index';
import { MenuUnfoldOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { <PERSON><PERSON>, Drawer, Modal } from 'antd';
import React, { useState } from 'react';
import {
  BasicInfo,
  BillDataChangeModal,
  BillDetailInfo,
  BillInfo,
  ManualRepayModal,
  RepayList,
  StepProgress,
} from './components';
import comDetailTitle from './index.less';
import { getBillDetailInfo } from './service';

const BillDetail: React.FC<any> = () => {
  const { accountNumber, billNo } = history.location.query;
  const { data, run } = useRequest(() => {
    return getBillDetailInfo(accountNumber, billNo);
  });

  const refresh = () => {
    run();
  };
  // const childRef: any = useRef();
  const [visibleDrawer, setVisibleDrawer] = useState<boolean>(false);
  const [visibleChangeManual, setVisibleChangeManual] = useState<boolean>(false);
  const [visibleChangeBill, setVisibleChangeBillDate] = useState<boolean>(false);
  return (
    <>
      <HeaderTab />
      <PageContainer
        extra={
          <MenuUnfoldOutlined
            style={{ color: '#1a87fe', fontSize: '20px' }}
            onClick={() => {
              setVisibleDrawer(!visibleDrawer);
            }}
          />
        }
      >
        <StepProgress stepStatus={data?.statusDTO} index={data?.index} />
        <BasicInfo basicInfo={data?.billDetailBaseCmsDTO} />
        <BillInfo billInfo={data?.billDetailInfoCmsDTO} />
        <RepayList repayList={data?.billDetailRepayCmsDTO} />
        <BillDetailInfo />
        <Drawer
          width={250}
          onClose={() => {
            setVisibleDrawer(false);
          }}
          getContainer={false}
          closable={false}
          open={visibleDrawer}
          bodyStyle={{ paddingBottom: 80 }}
        >
          <p className={comDetailTitle?.drawerTit}>功能面板</p>
          <p className={comDetailTitle?.drawerItem}>
            <Button
              type="primary"
              size="large"
              onClick={() => {
                const statusDes =
                  data?.statusDTO?.[data?.index + 1]?.desc || data?.statusDTO?.[data?.index]?.desc;
                const flag = ['待还款', '已逾期'].includes(statusDes);
                if (flag) {
                  setVisibleChangeBillDate(true);
                  setVisibleDrawer(false);
                } else {
                  Modal.error({
                    title: '提示',
                    content: <>当前账单状态为{statusDes},不支持变更还款日</>,
                    okText: '好的',
                  });
                }
              }}
            >
              变更账单还款日
            </Button>
          </p>
          <p className={comDetailTitle?.drawerItem}>
            <Button
              type="primary"
              size="large"
              onClick={() => {
                const statusDes =
                  data?.statusDTO?.[data?.index + 1]?.desc || data?.statusDTO?.[data?.index]?.desc;
                const flag = ['待还款', '已逾期', '已结清'].includes(statusDes);
                // console.log(flag, !flag);
                if (flag) {
                  setVisibleChangeManual(true);
                  setVisibleDrawer(false);
                } else {
                  Modal.error({
                    title: '提示',
                    content: <>当前账单状态为{statusDes},不支持人工后台还款</>,
                    okText: '好的',
                  });
                }
              }}
            >
              人工后台还款
            </Button>
          </p>
        </Drawer>
        <BillDataChangeModal
          title="变更还款日"
          visible={visibleChangeBill}
          onCancel={() => {
            setVisibleChangeBillDate(false);
          }}
          onVisibleChange={setVisibleChangeBillDate}
          billInfo={{
            ...data?.billDetailInfoCmsDTO,
            ...data?.billDetailBaseCmsDTO,
            desc: data?.statusDTO?.[data?.index + 1]?.desc || data?.statusDTO?.[data?.index]?.desc,
          }}
          refresh={() => {
            refresh();
            // childRef?.current?.reload();
          }}
        />
        <ManualRepayModal
          title="人工后台还款"
          visible={visibleChangeManual}
          onCancel={() => {
            setVisibleChangeManual(false);
          }}
          onVisibleChange={setVisibleChangeManual}
          billInfo={{
            ...data?.billDetailInfoCmsDTO,
            ...data?.billDetailBaseCmsDTO,
            desc: data?.statusDTO?.[data?.index + 1]?.desc || data?.statusDTO?.[data?.index]?.desc,
          }}
          refresh={() => {
            refresh();
            // childRef?.current?.reload();
          }}
        />
      </PageContainer>
    </>
  );
};

export default BillDetail;
