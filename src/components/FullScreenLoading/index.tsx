import { LoadingOutlined } from '@ant-design/icons';
import { Modal, Spin } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import styles from './index.less';

export type FullScreenLoadingInstance = {
  open: (params?: { tip: React.ReactNode }) => void;
  close: () => void;
};

type Props = {};
// 全屏loading
// antd5.0 Spin 组件 fullscreen 直接可以支持
// 由于组件库升级还在灰度中,完成时间未知
// 所以利用modal + spin 实现
const FullScreenLoading = forwardRef<FullScreenLoadingInstance, Props>((props, ref) => {
  const [loading, setLoading] = useState(false);
  const [tip, setTip] = useState<React.ReactNode>('加载中');

  useImperativeHandle(
    ref,
    () => {
      return {
        open: (params?: { tip: React.ReactNode }) => {
          if (params?.tip) {
            setTip(params?.tip);
          }
          setLoading(true);
        },
        close: () => {
          setLoading(false);
        },
      };
    },
    [],
  );

  return (
    <Modal
      open={loading}
      footer={false}
      centered
      zIndex={10000}
      className={styles['full-screen-loading']}
      closeIcon={<></>}
    >
      <div className={styles['full-screen-loading-content']}>
        <Spin spinning={loading} indicator={<LoadingOutlined style={{ color: 'white' }} />} />
        <div className={styles['full-screen-loading-text']}>{tip}</div>
      </div>
    </Modal>
  );
});

export default FullScreenLoading;
