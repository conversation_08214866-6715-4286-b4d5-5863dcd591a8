/**
 * 融租-提前结清
 */
import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import LoadingButton from '@/components/LoadingButton'; // @ts-ignore
import { RePaymentQrViewer } from '@/components/QrViewer/RepaymentQrViewer';
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { rules } from '@/utils/validate';
import {
  ActionType,
  ModalForm,
  ProFormDependency,
  ProFormRadio,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components'; // @ts-ignore
import { Descriptions, Form, message } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import type { PropsWithChildren } from 'react';
import React, { memo, useRef, useState } from 'react';
import { useAccess } from 'umi';
import { EarlySettleModalColumns } from '../columns/EarlySettleModalColumns';
import { billSettleInfo, submitBillSettleRepay } from '../services';
import type { IbillInfo, IbillListItem, Idimension } from '../types';
import './index.less';
import RemissionEditTable from './RemissionEditTable';

type Props = {
  selectedRows: IbillListItem[];
  dimension: Idimension;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};
const EarlySettle = (props: PropsWithChildren<Props>) => {
  const { selectedRows, dimension, actionRef } = props;

  const [billInfoData, setBillInfoData] = useState<IbillInfo>();
  const [open, setOpen] = useState(false);
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const access = useAccess();
  const [form] = Form.useForm();
  const [qrVisible, setQrVisible] = useState(false);
  const [qrData, setQrData] = useState({});

  // 计算减免总金额
  function calculateRemissionAmount(remission: any) {
    return (
      remission?.reduce((pre: any, cur: any) => {
        const {
          remissionPrincipal = 0,
          remissionInterest = 0,
          remissionPenalty = 0,
          remissionAdvanceSettleLiquidatedDamages = 0,
          remissionDelayAmount = 0,
        } = cur;

        const amount = new BigNumber(remissionPrincipal)
          .plus(remissionInterest)
          .plus(remissionPenalty)
          .plus(remissionAdvanceSettleLiquidatedDamages)
          .plus(remissionDelayAmount)
          .toNumber();
        return new BigNumber(pre).plus(amount).toNumber();
      }, 0) || 0
    );
  }

  const remissionEditRef: any = useRef();
  // console.log('asdasd', getAmount());
  return (
    <>
      <LoadingButton
        type="primary"
        onClick={async () => {
          try {
            if (!selectedRows?.length) {
              message.error('还未勾选任何数据');
              return;
            }
            fullScreenLoadingRef.current?.open();
            const billNoList = selectedRows.map((item) => item.billNo);
            const data = await billSettleInfo({
              billNoList,
              dimension,
              secondaryClassification: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
              settleType: true,
              onlineRepay: true,
            });

            // console.log(JSON.stringify(data));
            // const data = JSON.parse(data1);
            // const data = { billRspDTOList: [{ subjectMatterNo: '111' }] };
            setBillInfoData(data);
            setOpen(true);
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        提前结清
      </LoadingButton>
      <ModalForm
        open={open}
        // open={true}
        // form={form}
        layout="horizontal"
        labelCol={{ span: 3 }}
        title={<div className="modal_tit">提前结清</div>}
        modalProps={{
          onCancel: () => {
            setOpen(false);
          },
          destroyOnClose: true,
          okText: '二维码还款',
          // open: open
        }}
        width={1000}
        initialValues={{
          remission: [
            {
              id: 1,
              // remissionPrincipal: 0,
              // remissionInterest: 0,
              // remissionPenalty: 0,
              // remissionAdvanceSettleLiquidatedDamages: 0,
              // remissionDelayAmount: 0,
            },
          ],
        }}
        onFinish={async (values) => {
          try {
            console.log('values', values);
            const { remission, attach, isRemission, ...rest } = values;
            console.log(isRemission);
            if (isRemission) {
              await remissionEditRef?.current?.validatorEditRemission(); //校验不通过不执行
            }
            // 是否有权限提交
            if (!access.hasAccess('biz_billList_earlySettlement_commit_button')) {
              message.error('没有权限提交');
              return;
            }

            // const billNoList = billInfoData?.billRspDTOList.map((item) => item.billNo) || [];
            // const {
            //   remissionPrincipal,
            //   remissionInterest,
            //   remissionPenalty,
            //   remissionAdvanceSettleLiquidatedDamages,
            //   remissionDelayAmount,
            // } = remission?.[0];
            const remissionTotalAmount = calculateRemissionAmount(values.remission);
            const remissionT = remission?.map(({ billNo, ...rest }: any) => {
              const billNoT = billNo?.split('-')[0]; //账单号
              const remissionItemNo = billNo?.split('-')[1]; //车架号
              return { billNo: billNoT, remissionItemNo, ...rest };
            });
            const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              .minus(remissionTotalAmount)
              .toNumber();
            fullScreenLoadingRef.current?.open();
            await submitBillSettleRepay({
              // remissionPrincipal,
              // remissionInterest,
              // remissionPenalty,
              // remissionAdvanceSettleLiquidatedDamages,
              // remissionDelayAmount,
              remissionList: remissionT,
              billInfoList: billInfoData?.billRspDTOList,
              attach,
              // billNoList,
              repayAmount: repayTotalAmount,
              dimension,
              settleType: true,
              bankAmount: repayTotalAmount, //  兼容融租
              bankAmountDate: dayjs().format('YYYY-MM-DD'), //  兼容融租
              remitType: 4, //  此次默认收银台支付
              //
              onlineRepay: true,
              //
              ...rest,
            }).then((res) => {
              if (res?.data?.h5RepayUrl) {
                setQrData({
                  h5RepayUrl: res?.data?.h5RepayUrl,
                  businessNo: res?.data?.businessNo,
                  accountName: billInfoData?.accountName,
                });
                setQrVisible(true);
              }
            });
            message.success('提交成功');
            setOpen(false);
            actionRef.current?.reload();
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        <div style={{ paddingTop: 10 }}>
          <Descriptions
            bordered
            column={1}
            size={'small'}
            style={{ marginBottom: 20, paddingLeft: 20 }}
          >
            <Descriptions.Item label={<div>渠道名称</div>}>
              {billInfoData?.channelName}
            </Descriptions.Item>
            <Descriptions.Item label={<div>客户名称</div>}>
              {billInfoData?.accountNameList?.join(',')}
            </Descriptions.Item>
          </Descriptions>
          <ProFormRadio.Group
            rules={[{ required: true, message: '请选择实际还款方' }]}
            name="actualRepayRole"
            width="sm"
            labelCol={{ span: 4 }}
            label={<div className="label_css">实际还款方</div>}
            options={[
              {
                label: '用户本人',
                value: '1',
              },
              {
                label: '第三方',
                value: '2',
              },
            ]}
          />
          <ProFormText
            rules={[{ validator: rules.phone }]}
            tooltip="该手机号用于接收二维码链接"
            placeholder={'选填'}
            name="customerPhone"
            width="sm"
            labelCol={{ span: 4 }}
            label={<div className="label_css">发送手机号</div>}
          />
        </div>
        <div className="remission_sel">
          <ProFormRadio.Group
            name="isRemission"
            labelCol={{ span: 14 }}
            label={<div className="settle_tit">减免订单还款金额</div>}
            initialValue={false}
            className="item_is_remission"
            fieldProps={{
              style: { display: 'flex', alignItems: 'center', justifyContent: 'center' },
            }}
            options={[
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ]}
          />
          <span style={{ color: 'red' }}>申请减免需审批后才生效，请与运营人员提前确认减免金额</span>
        </div>
        <ProFormDependency name={['isRemission']}>
          {({ isRemission }) => {
            return (
              isRemission && (
                <div style={{ display: isRemission ? 'block' : 'none' }}>
                  <ProFormDependency name={['remission']}>
                    {(values) => {
                      const amount = calculateRemissionAmount(values.remission);
                      return (
                        <div style={{ marginBottom: 20, marginTop: 16 }}>
                          减免总金额：
                          <span style={{ color: 'red' }}>{isNaN(amount) ? '-' : amount}</span>元
                        </div>
                      );
                    }}
                  </ProFormDependency>

                  {/* <ProForm.Item name="remission"> */}
                  <RemissionEditTable
                    billInfoData={billInfoData}
                    cRef={remissionEditRef}
                    formRef={form}
                  />
                  {/* </ProForm.Item> */}
                </div>
              )
            );
          }}
        </ProFormDependency>
        <div className="tit">请确认账单信息无误后提交: </div>
        <ProFormDependency name={['remission', 'isRemission']}>
          {(values) => {
            let repayTotalAmount = 0;
            const remissionTotalAmount = calculateRemissionAmount(values.remission);
            const repayAmount = new BigNumber(billInfoData?.repayAmount || '').toNumber(); //提前结清的额度

            if (values?.isRemission) {
              repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
                .minus(remissionTotalAmount)
                .toNumber();
            } else {
              repayTotalAmount = repayAmount;
            }

            return (
              <>
                <div style={{ marginTop: 16, marginBottom: 20 }}>
                  提前结清还款总金额:
                  <span style={{ color: 'red' }}>{isNaN(repayAmount) ? '-' : repayAmount}</span>
                  元，减去减免总金额后，实际还款金额:
                  <span style={{ color: 'red' }}>
                    {isNaN(repayTotalAmount) ? '-' : repayTotalAmount}
                  </span>
                  元
                </div>
              </>
            );
          }}
        </ProFormDependency>
        <ProTable
          search={false}
          options={false}
          columns={EarlySettleModalColumns}
          dataSource={billInfoData?.billRspDTOList as any}
          scroll={{ x: 'max-content' }}
          pagination={false}
        />
      </ModalForm>
      <RePaymentQrViewer
        data={qrData}
        qrVisible={qrVisible}
        handleQRVisible={setQrVisible}
        title="还款二维码"
      />
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};

export default memo(EarlySettle);
