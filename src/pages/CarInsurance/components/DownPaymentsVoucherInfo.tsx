import { useModel } from '@umijs/max';
import React, { memo } from 'react';

import { Image, Space } from 'antd';

const DownPaymentsVoucherInfo = () => {
  const { detailData } = useModel('CarInsurance.carInsurance');
  const { downPaymentsDTO } = detailData;
  const { paymentDate, paymentDocumentUrlList } = downPaymentsDTO;
  return (
    <div>
      <h3>首付汇款</h3>
      <div> 汇款成功时间:{paymentDate} </div>
      <div>
        <div>首付凭证</div>
        <Space size={10} wrap>
          {paymentDocumentUrlList?.map((url) => {
            return <Image src={url} width={400} height={300} />;
          })}
        </Space>
      </div>
    </div>
  );
};

export default memo(DownPaymentsVoucherInfo);
