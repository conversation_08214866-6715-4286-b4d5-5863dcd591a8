import BigNumber from 'bignumber.js';
import { RefundTypeEnum } from './const';

export const processRefundAmount = (
  type: RefundTypeEnum,
  info: {
    // 退保金额（总）
    total: string;
    // 结清金额
    settle: string;
  },
) => {
  const res = {
    // 易人行回款金额
    yirenxingAmount: '',
    // 客户回款金额
    userAmount: '',
  };
  const total = new BigNumber(info.total);
  const settle = new BigNumber(info.settle);
  // 结清金额退款至易人行，盈余退款至客户
  if (type === RefundTypeEnum.ALLOCATION_1) {
    if (total.isLessThan(settle)) {
      return {
        yirenxingAmount: info.total,
        userAmount: '0',
      };
    }
    return {
      yirenxingAmount: info.settle,
      userAmount: total.minus(info.settle).toString(),
    };
  }
  // 结清金额退款至客户，盈余退款至易人行
  if (type === RefundTypeEnum.ALLOCATION_2) {
    if (total.isLessThan(settle)) {
      return {
        yirenxingAmount: '0',
        userAmount: info.total,
      };
    }
    return {
      yirenxingAmount: total.minus(info.settle).toString(),
      userAmount: info.settle,
    };
  }
  // 全部退款至易人行
  if (type === RefundTypeEnum.ALLOCATION_3) {
    return {
      yirenxingAmount: info.total,
      userAmount: '0',
    };
  }
  // 全部退款至客户
  if (type === RefundTypeEnum.ALLOCATION_4) {
    return {
      yirenxingAmount: '0',
      userAmount: info.total,
    };
  }
  return res;
};
