import React, { useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import { Upload } from 'antd';
import type { UploadProps } from 'antd/es/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import { getBaseUploadAction } from '../utils';
import { getAuthHeaders } from '@/utils/auth';

type Props = {
  value?: string; //
  onChange?: (val: string) => void;
  form?: FormInstance;
  name?: string; // setFieldsValue的字段
};
const UploadVoucher: React.FC<Props> = ({ value, onChange, form, name }) => {
  const [fileList, setFileList] = useState<UploadFile[]>(
    value
      ? [
          {
            uid: '-1',
            name: 'image.png',
            status: 'done',
            url: value,
          },
        ]
      : [],
  );

  const handleChange: UploadProps['onChange'] = ({ file, fileList: newFileList }) => {
    setFileList(newFileList);
    if (file?.status === 'done') {
      const { response = {} } = file;
      const { code, data = {} } = response;
      if (code === 200) {
        const { filePath, netWorkPath } = data;
        onChange!(netWorkPath);
        if (name) form?.setFieldsValue({ [name]: filePath });
      }
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );
  return (
    <>
      <Upload
        action={getBaseUploadAction('/base/oss/common/uploadfile')}
        listType="picture-card"
        fileList={fileList}
        data={{
          attachment: false,
          acl: 'PUBLIC_READ',
          destPath: 'insurance/policy/vehicleLicense/',
        }}
        headers={{ ...getAuthHeaders() }}
        name="file"
        maxCount={1}
        onChange={handleChange}
      >
        {fileList.length >= 1 ? null : uploadButton}
      </Upload>
    </>
  );
};

export default UploadVoucher;
