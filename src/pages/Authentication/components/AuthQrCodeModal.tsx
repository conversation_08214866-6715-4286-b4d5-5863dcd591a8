import { Empty, Modal, Spin } from 'antd';
import { QRCodeCanvas } from 'qrcode.react';
import type { ReactElement } from 'react';
import React, { memo, useState } from 'react';
import { getAuthUrl } from '../services';

type Props = {
  children: ReactElement;
  epAuthNo: number;
  certiName: string;
};
const AuthQrCodeModal: React.FC<Props> = (props) => {
  const { children, epAuthNo, certiName } = props;
  const [url, setUrl] = useState('');
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  function getUrl() {
    setVisible(true);
    setLoading(true);
    getAuthUrl(epAuthNo)
      .then((data) => {
        setUrl(data?.authUrl);
      })
      .finally(() => {
        setLoading(false);
      });
  }
  return (
    <>
      <div
        onClick={() => {
          getUrl();
        }}
      >
        {children}
      </div>
      <Modal
        footer={null}
        open={visible}
        title="认证二维码"
        onCancel={() => {
          setVisible(false);
        }}
      >
        <div>
          <Spin spinning={loading}>
            {url ? (
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: 10,
                }}
              >
                <QRCodeCanvas
                  value={url} //生成二维码的链接
                  size={200} //二维码尺寸
                  fgColor="#000000" //二维码颜色
                />
                <div>仅限用于: -{certiName}-企业授权认证</div>
              </div>
            ) : (
              <Empty />
            )}
          </Spin>
        </div>
      </Modal>
    </>
  );
};
export default memo(AuthQrCodeModal);
