import ImagePreview from '@/components/ImagePreview';
import { LENDING_MODEL_ENUM, LOAN_MANAGE_CODE } from '@/enums';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { CloseCircleOutlined, UploadOutlined } from '@ant-design/icons';
import type { FormInstance } from '@ant-design/pro-components';
import { Button, DatePicker, Form, Input, Upload } from 'antd';
import React, { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import FormItem from './FormItem';
import './index.less';

interface LFDInterface {
  dataLending: Record<string, any>;
  editForm: FormInstance<any>;
  actionRef: any;
  show?: boolean;
  updateLoading: (loading: boolean) => void;
  ref: any;
}

const LoanFlowDescription: React.FC<LFDInterface> = forwardRef((props, ref) => {
  const { dataLending, editForm, show = true, updateLoading } = props;

  // 上传回显的文件列表
  const [fileList, setFileList] = useState<any[]>([]);

  const readonly = useMemo(() => {
    // 状态为待放款且放款方式为线下时，需要编辑
    return !(
      dataLending?.status === LOAN_MANAGE_CODE.WAIT_LENDERS &&
      dataLending?.lendingModel === LENDING_MODEL_ENUM.OFFLINE
    );
  }, [dataLending]);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    reset: () => {
      setFileList([]);
      editForm.resetFields();
    },
  }));

  const handleUpload = (info: any) => {
    const { fileList: newFileList } = info;
    if (info.file.status === 'uploading') {
      updateLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      const values = newFileList
        ?.map((item: any) => {
          const {
            name,
            response: { data = {} },
          } = item;
          return {
            fileName: data?.fileName || name,
            filePath: data?.filePath,
            netWorkPath: data?.netWorkPath,
          };
        })
        .filter((f) => !!f.filePath);

      if (!info.file.response?.data) {
        info.file.status = 'error';
      }

      setFileList(values);
      editForm.setFieldsValue({
        extend: values,
      });
      updateLoading(false);
    } else if (info.file.status === 'error') {
      editForm.setFieldsValue({
        extend: null,
      });
      updateLoading(false);
    }
  };

  if (!show) {
    return null;
  }

  return (
    <Form form={editForm} layout="inline" style={{ width: '100%' }}>
      <FormItem
        label="银行流水号"
        name="tranceFlowNo"
        readonly={readonly}
        value={dataLending?.tranceFlowNo}
        formProps={{
          rules: [
            { required: true },
            { max: 50, message: '限50个字符以内' },
            { pattern: /^[a-zA-Z0-9]+$/, message: '只能填写数字和字母' },
          ],
        }}
      >
        <Input placeholder="请输入银行流水号" autoComplete="off" />
      </FormItem>
      <FormItem
        label="放款到帐时间"
        name="lendingTime"
        readonly={readonly}
        value={dataLending?.lendingTime}
        formProps={{
          rules: [{ required: true }],
        }}
      >
        <DatePicker showTime />
      </FormItem>
      <FormItem
        label="放款凭证"
        name="extend"
        readonly={readonly}
        value={dataLending?.lendingCertificateList}
        formProps={{
          rules: [{ required: true, message: '请上传放款凭证' }],
        }}
        isFile={true}
      >
        {fileList.length > 0 ? (
          <div className="upload-file-show-list">
            {fileList.map((item: any) => (
              <div key={item?.filePath}>
                <ImagePreview url={item?.netWorkPath}>
                  <div className="upload-file-form-item">
                    <span style={{ color: '#1677ff' }}>{item?.fileName}</span>
                    <CloseCircleOutlined
                      style={{ color: 'red', marginLeft: 10 }}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setFileList(fileList.filter((i: any) => i?.filePath !== i?.filePath));
                        editForm.setFieldsValue({
                          extend: [],
                        });
                      }}
                    />
                  </div>
                </ImagePreview>
              </div>
            ))}
          </div>
        ) : (
          <Upload
            accept=".jpg,.png,.jpeg,.bmp,.pdf"
            action={`${getBaseUrl()}/bizadmin/oss/common/uploadFile`}
            headers={{
              ...getAuthHeaders(),
              'hll-appid': 'bme-finance-bizadmin-svc',
            }}
            data={{
              acl: 'PUBLIC_READ',
              destPath: 'CAR_POLICY_LENDING_CERTIFICATE',
            }}
            onRemove={(file) => {
              updateLoading(false);
              setTimeout(() => {
                editForm.setFieldsValue({
                  extend: null,
                });
              }, 0);
            }}
            onChange={handleUpload}
            maxCount={1}
          >
            <Button icon={<UploadOutlined />}>点击上传</Button>
          </Upload>
        )}
      </FormItem>
    </Form>
  );
});

export default LoanFlowDescription;
