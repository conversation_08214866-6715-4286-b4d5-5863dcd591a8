/*
 * @Date: 2023-09-04 16:46:33
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-09-11 10:49:37
 * @FilePath: /lala-finance-biz-web/src/pages/FirstAudit/service.ts
 * @Description:
 */
import { bizAdminHeader, headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { ExportParams, FirstAuditListParams } from './data';

export async function getFirstAuditList(data: FirstAuditListParams) {
  return request(`/bizadmin/first/reported/queryList`, {
    method: 'POST',
    data,
    headers,
    ifTrimParams: true,
  });
}

// 列表导出
export async function exportList(params: ExportParams) {
  return request(`/bizadmin/first/reported/export`, {
    method: 'POST',
    responseType: 'blob',
    data: params,
    getResponse: true,
    ifTrimParams: true,
    headers: {
      ...bizAdminHeader,
    },
  });
}
