/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-09-15 17:24:31
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-09-18 17:54:07
 * @FilePath: /code/lala-finance-biz-web/src/pages/SmsRecordMsg/data.d.ts
 * @Description: SmsRecordMsg data.d.ts
 */

export interface OverdueSMSRecordPageItem {
  urgeName?: string;
  urgePersonId?: string;
  urgeStatus?: number;
  productSecondCode?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  current: number | undefined;
  pageSize: number | undefined;
}

export interface OverdueSMSRecordResultItem {
  id?: number; // 表id
  urgeId?: string; // 催收id（批次id）
  bizId?: string; // 业务id
  urgeName?: string; // 催收标识
  urgeType?: number; // 催收类型 1：电话催收 2:信鸽短信催收
  productSecondCode?: string; // 二级产品code
  productName?: string; // 二级产品名称
  urgePersonId?: string; // 操作人员id，-1表示系统触发
  urgePersonName?: string; // 操作人员
  urgeStatus?: number; // 操作状态 1：未处理 2：处理中 3：处理完成 4：处理失败
  createdAt?: string; // 操作时间
}
