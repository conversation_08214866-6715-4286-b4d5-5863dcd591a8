import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import LoadingButton from '@/components/LoadingButton';
import { SECONDARY_CLASSIFICATION_MAP_ALL } from '@/enums'; // @ts-ignore
import { ExclamationCircleOutlined } from '@ant-design/icons'; // @ts-ignore
import type { ActionType } from '@ant-design/pro-components'; // @ts-ignore
import { ModalForm } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Descriptions, Form, Input, message, Space, Spin, Table } from 'antd';
import type { MutableRefObject } from 'react';
import React, { memo, useRef, useState } from 'react';
import { IstatusEnCode, repayStatusMap } from '../../carInsurance/types';
import { PAY_TYPE } from '../../const';
import {
  applyOrderRemissionInfo,
  submitRepayApplyOrderRemissionApprovalPass,
  submitRepayApplyOrderRemissionApprovalRefuse,
} from '../services';
import type { IapplyOrderInfo, IofflineRepayReviewItem } from '../types';
import './index.less';

type Props = {
  record?: IofflineRepayReviewItem;
  remissionReviewOpen: boolean;
  setRemissionReviewOpen: (val: boolean) => void;
  setOnlineRepayReviewOpen?: (val: boolean) => void;
  actionRef: MutableRefObject<ActionType | undefined>;
};
const RemissionRepayReviewModal: React.FC<Props> = (props) => {
  const {
    record,
    remissionReviewOpen,
    setRemissionReviewOpen,
    setOnlineRepayReviewOpen,
    actionRef,
  } = props;
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const [loading, setLoading] = useState(false);
  const [applyOrderInfoData, setApplyOrderInfoData] = useState<IapplyOrderInfo>();
  const access = useAccess();
  const imagePreviewRef = useRef<ImagePreviewInstance>(null);
  const [reject, setReject] = useState(false);

  async function getApplyOrderInfo() {
    try {
      if (record?.businessNo) {
        setLoading(true);
        const data = await applyOrderRemissionInfo({ businessNo: record?.businessNo });
        setApplyOrderInfoData(data);
        setRemissionReviewOpen(true);
      }
    } finally {
      setLoading(false);
    }
  }
  // 展示车架号
  const showRemissionItemNo =
    applyOrderInfoData?.remissionList?.every((val: any) => {
      return val?.remissionItemNo !== null && val?.remissionItemNo !== undefined;
    }) || false;
  return (
    <>
      <ModalForm
        title={<div className="modal_tit">减免审核</div>}
        open={remissionReviewOpen}
        onOpenChange={(val) => {
          console.log('valvalval', val);

          if (!val) {
            // 当弹窗被关闭的时候 清空数据
            setApplyOrderInfoData(undefined);
          } else {
            getApplyOrderInfo();
          }
        }}
        width={1000}
        modalProps={{
          maskClosable: true,
          onCancel: () => {
            setRemissionReviewOpen(false);
          },
        }}
        submitter={{
          render() {
            return (
              // 只有待审批 才展示
              applyOrderInfoData?.remissionApprovalStatus === IstatusEnCode.PendingApproval &&
              access.hasAccess('bill_apply_remission_info_postLoanMng_callpay_approval') && (
                <Space>
                  <LoadingButton
                    type="primary"
                    danger
                    onClick={async () => {
                      setReject(true);
                    }}
                  >
                    驳回
                  </LoadingButton>
                  <LoadingButton
                    type="primary"
                    onClick={async () => {
                      try {
                        if (record?.businessNo) {
                          fullScreenLoadingRef.current?.open();
                          await submitRepayApplyOrderRemissionApprovalPass({
                            auditStatus: 1, //  1审核通过，2驳回
                            businessNo: record?.businessNo,
                          });
                          message.success('提交成功！');
                          setRemissionReviewOpen(false);
                          actionRef.current?.reload();
                        }
                      } catch (error) {
                      } finally {
                        fullScreenLoadingRef.current?.close();
                      }
                    }}
                  >
                    审核通过
                  </LoadingButton>
                </Space>
              )
            );
          },
        }}
      >
        <Spin spinning={loading}>
          <div className="block_area">
            <Descriptions bordered column={2} size={'small'}>
              <Descriptions.Item span={2} label={<div className="label_width">减免审核ID</div>}>
                {applyOrderInfoData?.businessNo || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">渠道名称</div>}>
                {applyOrderInfoData?.channelName || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">客户名称</div>}>
                {applyOrderInfoData?.accountNameList?.join(',') || '-'}
              </Descriptions.Item>
              {applyOrderInfoData?.remissionApprovalReason && (
                <Descriptions.Item span={2} label="驳回原因">
                  <span>{applyOrderInfoData?.remissionApprovalReason}</span>
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
          <div className="tit">请审核减免金额：</div>
          <div className="block_area">
            <div className="line no_border">
              <span className="repay_label">减免总金额：</span>
              <span className="repay_amount">{applyOrderInfoData?.exemptionAmount || '-'}</span>元
            </div>

            <Table
              pagination={false}
              dataSource={applyOrderInfoData?.remissionList}
              columns={[
                {
                  title: '标的物编号',
                  dataIndex: 'remissionItemNo',
                  hidden: !showRemissionItemNo,
                },
                {
                  title: '减免本金(元)',
                  dataIndex: 'remissionPrincipal',
                },
                {
                  title: '减免利息(元)',
                  dataIndex: 'remissionInterest',
                },
                {
                  title: '减免罚息(元)',
                  dataIndex: 'remissionPenalty',
                },
                {
                  title: '减免违约金(元)',
                  dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
                },
                {
                  title: '减免滞纳金(元)',
                  dataIndex: 'remissionDelayAmount',
                },
              ]}
            />
          </div>
          <div className="tit">还款单信息：</div>
          <div className="block_area pt_0">
            <Descriptions bordered column={2} size={'small'} style={{ marginTop: 10 }}>
              <Descriptions.Item span={2} label="还款单ID">
                <a
                  onClick={() => {
                    setRemissionReviewOpen(false);
                    setOnlineRepayReviewOpen?.(true);
                  }}
                >
                  {applyOrderInfoData?.businessNo || '-'}
                </a>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="业务线">
                <span>
                  {SECONDARY_CLASSIFICATION_MAP_ALL[
                    applyOrderInfoData?.secondProductCode as string
                  ] || '-'}
                </span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="应还款金额">
                <span>{applyOrderInfoData?.planRepayAmount || '-'} 元</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="还款单金额">
                <span>{applyOrderInfoData?.repayAmount || '-'} 元</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="支付方式">
                <span>
                  {PAY_TYPE?.[applyOrderInfoData?.remitType as keyof typeof PAY_TYPE] || '-'}
                </span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="创建时间">
                <span>{applyOrderInfoData?.createdAt || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="还款状态">
                <span>{repayStatusMap[applyOrderInfoData?.repayStatus as number] || '-'}</span>
              </Descriptions.Item>
            </Descriptions>
          </div>
        </Spin>
      </ModalForm>
      <ModalForm
        title={
          <>
            <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: 18, marginRight: 5 }} />
            是否确认驳回该笔催收减免？
          </>
        }
        layout="vertical"
        width={500}
        modalProps={{
          centered: true,
          destroyOnClose: true,
          onCancel: () => {
            setReject(false);
          },
        }}
        open={reject}
        onFinish={async ({ auditRemark }) => {
          try {
            if (record?.businessNo && auditRemark) {
              fullScreenLoadingRef.current?.open();
              await submitRepayApplyOrderRemissionApprovalRefuse({
                auditStatus: 2, //  1审核通过，2驳回
                businessNo: record?.businessNo,
                auditRemark,
              });
              setReject(false);
              setRemissionReviewOpen(false);
              actionRef.current?.reload();
            }
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        <Form.Item
          label="驳回原因："
          name="auditRemark"
          layout="vertical"
          rules={[{ required: true, message: '请输入驳回原因!' }]}
          style={{ marginTop: 5 }}
        >
          <Input.TextArea placeholder="请输入" />
        </Form.Item>
      </ModalForm>
      <ImagePreview ref={imagePreviewRef} />
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};
export default memo(RemissionRepayReviewModal);
