/*
 * @Author: your name
 * @Date: 2021-09-16 20:04:03
 * @LastEditTime: 2024-11-19 17:49:05
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/AuthorizedChangeModal.tsx
 */
import { DividerTit } from '@/components';
import { CommonImageUpload } from '@/components/ReleaseCom';
import globalStyle from '@/global.less';
import { emailValidate } from '@/services/validate';
import { convertUploadFileList, getBlob, getUuid, previewAS, saveAs } from '@/utils/utils';
import ProForm, { ProFormRadio, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, useRequest } from '@umijs/max';
import { Button, Form, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { LogListItem } from '../data.d';
import enterpriseMngStyle from '../index.less';
import { getAuthInfo, getAuthInfoLog, getEnterpriseProduct, updateAuthInfo } from '../service';

interface CreateFormProps {
  title: string;
  visible: boolean;
  onCancel: () => void;
  refresh: () => void;
  onVisibleChange?: any;
  productCode?: string;
  userNo?: string;
}

const AuthorizedChangeModal: React.FC<CreateFormProps> = (props) => {
  const { title, visible, onCancel, productCode } = props;
  const [mode, setModel] = useState<string>('变更');
  const userNo = props?.userNo || history.location.query?.userNo; //次账号的从外面传入自己的userNo，其他的url取

  const [form] = Form.useForm();

  //根据产品code获取授权人信息
  const getAuthInfoFunc = (productCodeT: string) => {
    return getAuthInfo({
      enterpriseId: userNo,
      productSecondTypeCode: productCodeT.substring(0, 4),
    });
  };

  //获取
  const { run } = useRequest(getAuthInfoFunc, {
    manual: true,
    onSuccess: (result, params) => {
      // console.log(result);
      form.setFieldsValue({
        ...result,
        enclosure: [],
        productCode: params[0], //如果产品下拉只有一个则默认选中
      });
    },
  });

  //产品列表
  const { data: dataList, run: getEnterpriseRun } = useRequest(() => {
    return getEnterpriseProduct(userNo);
  });

  useEffect(() => {
    getEnterpriseRun();
  }, [visible]);

  useEffect(() => {
    if (productCode) {
      run(productCode as string);
    }
  }, [productCode]);

  const previewPDF = (url: string, name: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      const exc = name.substring(name.lastIndexOf('.') + 1);
      if ('.jpg.jpeg.gif'.includes(exc)) {
        previewAS(blob, 'image/jpeg;chartset=UTF-8');
      } else if (exc === 'pdf') {
        previewAS(blob);
      } else {
        saveAs(blob, name);
      }
      // });
    });
  };
  const [loading, setLoading] = useState<boolean>(false);
  const [allFileList, handleFileList] = useState({});
  // console.log(allFileList);
  useEffect(() => {
    if (dataList?.length === 1) {
      run(dataList?.[0].value);
    }
    return () => {
      handleFileList({});
      form.setFieldsValue({});
    };
  }, [dataList]);

  const mapFileList = (allFile: any) => {
    handleFileList(allFile);
  };
  const actionRef = useRef<ActionType>();

  const itemTableMap = (val: { key: string; type: number; value: string }[] = []) => {
    return val.length
      ? val.reduce((pre: React.ReactNode, cur: any) => {
          return (
            <>
              {pre}
              <div>
                {cur?.type === 2 ? (
                  <a
                    target="_blank"
                    onClick={() => {
                      previewPDF(cur?.value, cur?.key);
                    }}
                  >
                    {cur?.key}
                  </a>
                ) : (
                  <span>
                    {cur?.key}：{cur?.value}
                  </span>
                )}
              </div>
            </>
          );
        }, <></>)
      : '-';
  };
  const columns: ProColumns<LogListItem>[] = [
    {
      title: '产品名称',
      dataIndex: 'productSecondTypeName',
      // render: () => {
      //   return '明保';
      // },
    },
    {
      title: '变更前',
      // dataIndex: 'beforeValue',
      className: enterpriseMngStyle.textTop,
      render: (_, record) => {
        return itemTableMap(record?.beforeValue);
      },
    },
    {
      title: '变更后',
      // dataIndex: 'afterValue',
      className: enterpriseMngStyle.textTop,
      render: (_, record) => {
        return itemTableMap(record?.afterValue);
      },
    },
    {
      title: '变更内容',
      dataIndex: 'modifyItem',
    },
    {
      title: '变更时间',
      dataIndex: 'createdAt',
    },
    {
      title: '变更人',
      dataIndex: 'operatorBy',
    },
  ];
  return (
    <Modal
      destroyOnClose
      centered
      title={title}
      open={visible}
      onCancel={() => onCancel()}
      width={900}
      // onVisibleChange={onVisibleChange}
      afterClose={() => {
        handleFileList({});
        // run(form.getFieldValue('productCode'));
        form.resetFields();
        setModel('变更');
      }}
      footer={null}
    >
      <ProFormRadio.Group
        style={{
          margin: 16,
        }}
        radioType="button"
        fieldProps={{
          value: mode,
          onChange: (e) => {
            setModel(e.target.value);
          },
        }}
        options={['变更', '变更日志']}
      />
      {mode === '变更' && (
        <div>
          <DividerTit
            title={
              <div>
                请填写新授权人信息{'  '}
                <span style={{ color: 'grey', fontWeight: 'normal' }}>
                  请确保该授权人已取得企业的全部授权
                </span>
              </div>
            }
            style={{ marginTop: 0 }}
          />
          <ProForm
            className={`${enterpriseMngStyle.formModal} ${globalStyle.mt20}`}
            layout="horizontal"
            // form={formAuth}
            form={form}
            initialValues={{ productCode }}
            submitter={{
              render: (propsSubmitter) => (
                <div className="buttonCss">
                  <Button
                    type="primary"
                    key="submit"
                    loading={loading}
                    onClick={() => propsSubmitter.form?.submit?.()}
                  >
                    提交
                  </Button>
                  <Button key="rest" onClick={onCancel} className={globalStyle?.ml10}>
                    取消
                  </Button>
                </div>
              ),
            }}
            onFinish={async (values: any) => {
              // console.log(allFileList);
              const mapUploadFile = convertUploadFileList(allFileList, ['enclosure']);
              // await waitTime(2000);
              // console.log({ ...values, ...mapUploadFile, enterpriseId: userNo });
              setLoading(true);
              // console.log(values?.productCode?.substring(0, 4));
              await updateAuthInfo({
                ...values,
                authorizedName: values?.authorizedName?.trim(),
                ...mapUploadFile,
                enterpriseId: userNo,
                productSecondTypeCode: values?.productCode.substring(0, 4),
              })
                .then(() => {
                  setLoading(false);
                  message.success('变更成功');
                  onCancel();
                  props.refresh();
                })
                .catch(() => {
                  setLoading(false);
                });
              // console.log(values);
            }}
          >
            <ProForm.Group size={10}>
              <ProFormSelect
                rules={[{ required: true }]}
                // disabled
                options={dataList}
                placeholder="请选择产品ID"
                fieldProps={{
                  showSearch: true,
                  optionFilterProp: 'label',
                  onSelect: (val) => {
                    // console.log(val);
                    run(val);
                  },
                }}
                width="sm"
                name="productCode"
                label="产品ID"
              />
              {/* <ProFormSelect
                name="productSecondTypeCode"
                disabled
                options={[{ value: '0101', label: '明保' }]}
                width="sm"
                label="产品名称"
                placeholder="请选择产品名称"
              /> */}
              <ProFormText
                name="authorizedName"
                rules={[{ required: true }]}
                width="sm"
                label="姓名"
                placeholder="请输入姓名"
              />
            </ProForm.Group>
            <ProForm.Group size={10}>
              <ProFormSelect
                name="idType"
                rules={[{ required: true }]}
                options={[{ value: 1, label: '身份证' }]}
                width="sm"
                label="证件类型"
                placeholder="请选择证件类型"
              />
              <ProFormText
                name="authorizedIdCard"
                rules={[{ required: true }]}
                width="sm"
                label="证件号"
                fieldProps={{ maxLength: 50 }}
                placeholder="请输入证件号"
              />
            </ProForm.Group>
            <ProForm.Group size={10}>
              <ProFormText
                name="authorizedPhone"
                rules={[
                  { required: true },
                  { pattern: /^1[3456789]\d{9}$/, message: '格式不正确' },
                ]}
                width="sm"
                label="手机号"
                placeholder="请输入手机号"
              />
              <ProFormText
                name="email"
                rules={[{ required: true }, emailValidate]}
                width="sm"
                fieldProps={{ maxLength: 50 }}
                label="邮箱"
                placeholder="请输入邮箱"
              />
            </ProForm.Group>
            <ProForm.Group>
              <CommonImageUpload
                extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
                label="附件"
                name="enclosure"
                max={5}
                listType="text"
                size={10}
                fileListEdit={allFileList?.enclosure || []}
                desPath="EP_AUTH_INFO"
                mapFileList={mapFileList}
                accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
              />
            </ProForm.Group>
          </ProForm>
        </div>
      )}
      {mode === '变更日志' && (
        <div>
          <ProTable<LogListItem>
            actionRef={actionRef}
            rowKey={getUuid}
            scroll={{ x: 'max-content' }}
            // dataSource={[{ cost: 500, amountDue: 2 }]}
            columns={columns}
            toolBarRender={false}
            search={false}
            request={(params) =>
              getAuthInfoLog({
                ...params,
                enterpriseId: userNo,
              })
            }
          />
        </div>
      )}
    </Modal>
  );
};
export default AuthorizedChangeModal;
