import React, { useEffect, useState } from 'react';
import '../index.less';
import { Modal, Form, Input, TreeSelect, Radio, message, Tooltip } from 'antd';
import { METHOD_TYPE, methodType, SYS_TYPE, sysType } from './const';
import { createFeature, queryFeatureTreeList, updateFeature } from '../service';
import { QuestionCircleOutlined } from '@ant-design/icons';

type PostModalProps = {
  visible: boolean;
  closeModal: () => void;
  isEdit: boolean;
  listItem?: any;
  refreshTable: () => void;
};

export default (props: PostModalProps) => {
  const { visible, isEdit, listItem, closeModal, refreshTable } = props;
  console.log('props', props);

  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const handleCancel = () => {
    closeModal();
  };
  const submitEditForm = async (values: any) => {
    console.log('Received values of Edit form: ', values);
    const params: any = {
      ...values,
      parentId: values?.parentId === undefined || values?.parentId === null ? 0 : values?.parentId,
    };
    setConfirmLoading(true);
    updateFeature(params)
      .then((res) => {
        console.log('updateFeature res', res);
        setConfirmLoading(false);
        message.success('编辑成功');
        refreshTable();
        closeModal();
      })
      .catch(() => {
        setConfirmLoading(false);
      });
  };
  const submitCreateForm = async (values: any) => {
    console.log('Received values of Create form: ', values);
    setConfirmLoading(true);
    createFeature(values)
      .then((res) => {
        console.log('createFeature res', res);
        message.success('新建成功');
        setConfirmLoading(false);
        refreshTable();
        closeModal();
      })
      .catch(() => {
        setConfirmLoading(false);
      });
  };
  const handleSave = () => {
    form
      .validateFields()
      .then((values) => {
        // form.resetFields();
        // onCreate(values);
        submitEditForm(values);
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  };
  const handleCreate = () => {
    form
      .validateFields()
      .then((values) => {
        // form.resetFields();
        // onCreate(values);
        submitCreateForm(values);
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  };
  //
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>();
  const onExpand = (expandedKeysValue: React.Key[]) => {
    console.log('onExpand', expandedKeysValue);
    setExpandedKeys(expandedKeysValue);
  };
  const [privilegeTreeList, setPrivilegeTreeList] = useState<any>([]);
  const getPrivilegeTreeList = async () => {
    const treeData = await queryFeatureTreeList().catch(() => []);
    console.log('treeData', treeData);
    setPrivilegeTreeList(treeData?.data);
  };
  //
  useEffect(() => {
    console.log('listItem', listItem);
    form.resetFields();
    if (isEdit) {
      form.setFieldsValue(listItem);
      if (listItem?.parentPrivilegeCode) {
        setExpandedKeys([listItem?.parentPrivilegeCode]);
      }
    }
  }, [listItem, visible, isEdit]);
  useEffect(() => {
    if (visible) {
      getPrivilegeTreeList();
    }
  }, [visible]);

  return (
    <>
      <Modal
        title={isEdit ? '编辑功能' : '新建功能'}
        open={visible}
        width={650}
        okText={isEdit ? '保存' : '确定'}
        confirmLoading={confirmLoading}
        onOk={isEdit ? handleSave : handleCreate}
        onCancel={handleCancel}
      >
        <Form
          form={form}
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          autoComplete="off"
        >
          <Form.Item
            label="权限名称"
            name="privilegeName"
            rules={[
              { required: true, message: '必填项' },
              { max: 128, message: '最多128个字符' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            // label="权限编码"
            label={
              <>
                权限编码&nbsp;
                <Tooltip placement="top" title="权限编码唯一，不可重复，不可修改">
                  <QuestionCircleOutlined />
                </Tooltip>
              </>
            }
            name="privilegeCode"
            rules={[
              { required: true, message: '必填项' },
              { max: 128, message: '最多128个字符' },
            ]}
          >
            <Input disabled={isEdit} />
          </Form.Item>
          <Form.Item label="父级权限" name="parentId">
            <TreeSelect
              dropdownStyle={{ maxHeight: 800, overflow: 'auto' }}
              treeExpandedKeys={expandedKeys}
              onTreeExpand={onExpand}
              allowClear
              showSearch={true}
              // treeLine={true}
              filterTreeNode={true}
              treeNodeFilterProp="privilegeNameAndCode"
              treeData={privilegeTreeList}
              fieldNames={{ label: 'privilegeNameAndCode', value: 'id' }}
            />
          </Form.Item>
          <Form.Item
            // label="接口URL"
            label={
              <>
                接口URL&nbsp;
                <Tooltip placement="top" title="如：/bizadmin/1/2, 无url填-">
                  <QuestionCircleOutlined />
                </Tooltip>
              </>
            }
            initialValue="-"
            name="realUrl"
            rules={[
              { required: true, message: '必填项' },
              { max: 128, message: '最多128个字符' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="请求方式"
            name="method"
            rules={[{ required: true, message: '必填项' }]}
            initialValue={METHOD_TYPE.GET}
          >
            <Radio.Group>
              {methodType.map((item) => (
                <Radio value={item.value}>{item.label}</Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label="系统标识"
            name="mark"
            rules={[{ required: true, message: '必填项' }]}
            initialValue={SYS_TYPE.BIZ}
          >
            <Radio.Group>
              {sysType.map((item) => (
                <Radio value={item.value}>{item.label}</Radio>
              ))}
            </Radio.Group>
          </Form.Item>

          {/* <Form.Item label="备注" name="remark" rules={[{ max: 50, message: '最多50个字符' }]}>
            <Input.TextArea />
          </Form.Item> */}
        </Form>
      </Modal>
    </>
  );
};
