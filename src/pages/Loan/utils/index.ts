import { useModel } from 'umi';

export function useIsLeaseChannelUser() {
  const { initialState = {} } = useModel<any>('@@initialState');
  const { currentUser = {} } = initialState;
  const { role = [] } = currentUser;
  // 业务系统_融租渠道门店用户 -> leaseStoreUser
  // 业务系统_融租渠道用户 -> leaseChannelUser
  const roles = ['leaseStoreUser', 'leaseChannelUser'];
  return roles.some((_role) =>
    role.map((item: { roleCode: any }) => item.roleCode).includes(_role),
  );
}
