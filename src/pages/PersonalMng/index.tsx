import HeaderTab from '@/components/HeaderTab';
import { SECONDARY_CLASSIFICATION_CODE_LABEL } from '@/enums';
import globalStyle from '@/global.less';
import { disableFutureDate, downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess } from '@umijs/max';
import { Button, DatePicker } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef } from 'react';
import { KeepAlive } from 'react-activation';
import type { EnterpriseUserListItem, EnterpriseUserListParams } from './data';
import { getUserList, userExport } from './service';
// /quota/user/enterprise/excel
const getExport = (form: EnterpriseUserListParams) => {
  userExport(form).then((res) => {
    downLoadExcel(res);
  });
};

const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
  return (
    <DatePicker.RangePicker
      {...rest}
      className={globalStyle.w100}
      disabledDate={disableFutureDate}
    />
  );
};
const columns: ProColumns<EnterpriseUserListItem>[] = [
  {
    title: '用户ID',
    dataIndex: 'userNo',
    key: 'userNo',
  },
  {
    title: '用户名称',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '证件号码',
    dataIndex: 'idNo',
    key: 'idNo',
  },
  {
    title: '产品二级分类',
    dataIndex: 'productSecondTypeName',
    key: 'productSecondTypeName',
    search: false,
  },
  {
    title: '产品二级分类',
    dataIndex: 'productSecondTypeCode',
    key: 'productSecondTypeCode',
    order: 1,
    valueEnum: SECONDARY_CLASSIFICATION_CODE_LABEL,
    hideInTable: true,
  },
  {
    title: '授信总额度',
    dataIndex: 'creditAmount',
    key: 'creditAmount',
    search: false,
  },
  {
    title: '已使用额度',
    dataIndex: 'usedAmount',
    key: 'usedAmount',
    search: false,
  },
  {
    title: '冻结额度',
    dataIndex: 'freezeAmount',
    key: 'freezeAmount',
    search: false,
  },
  {
    title: '剩余额度',
    dataIndex: 'currentAmount',
    key: 'currentAmount',
    search: false,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    valueEnum: {
      1: { text: '正常使用' },
      0: { text: '冻结' },
      '-1': { text: '注销' },
      2: { text: '人工冻结' },
      3: { text: '永久冻结' },
      4: { text: '失效' },
    },
  },
  {
    title: '授信时间',
    dataIndex: 'creditEndTime',
    valueType: 'dateRange',
    fieldProps: {
      disabledDate: disableFutureDate,
    },
    render(dom, row) {
      return row?.creditEndTime;
    },
    search: {
      // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
      transform: (value: any) => ({
        startCreditEndTime: `${value[0].split(' ')[0]} 00:00:00`,
        endCreditEndTime: `${value[1].split(' ')[0]} 23:59:59`,
      }),
    },
  },
  {
    title: '操作',
    key: 'option',
    width: 180,
    fixed: 'right',
    valueType: 'option',
    render: (text, row) => (
      <>
        <Link
          to={`/userMng/personalMng/com-detail?userNo=${row.userNo}&productSecondCode=${row.productSecondTypeCode}`}
        >
          查看详情
        </Link>
        {/* <Button type="link" className={globalStyle.ml10}>
          {row.status === 1 ? '冻结' : '激活'}
        </Button> */}
      </>
    ),
  },
];

const ComUserList: React.FC<{}> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  // 当前登陆用户信息
  const access = useAccess();
  return (
    <>
      <PageContainer>
        <ProTable<EnterpriseUserListItem>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="userNo"
          search={{
            labelWidth: 120,
            defaultCollapsed: false,
          }}
          toolBarRender={() => {
            return [
              access.hasAccess('biz_download') && (
                <Button
                  key="button"
                  type="primary"
                  onClick={() => {
                    const { creditEndTime, ...data } = formRef?.current?.getFieldsValue();
                    let newForm = { ...data };
                    if (creditEndTime?.length) {
                      const startCreditEndTime = `${creditEndTime[0].format(
                        'YYYY-MM-DD',
                      )} 00:00:00`;
                      const endCreditEndTime = `${creditEndTime[1].format('YYYY-MM-DD')} 23:59:59`;
                      newForm = {
                        ...data,
                        startCreditEndTime,
                        endCreditEndTime,
                      };
                    }
                    getExport(newForm);
                  }}
                >
                  导出
                </Button>
              ),
            ];
          }}
          request={(params) => getUserList(params)}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'userMng/personalMng/list'}>
      <ComUserList />
    </KeepAlive>
  </>
);
