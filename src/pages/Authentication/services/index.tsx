// 接口文档
// https://finance-api-stg.lalafin.net/doc.html#/default/%E8%BD%A6%E9%99%A9%E5%88%86%E6%9C%9F%E8%B4%A6%E6%88%B7%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3/queryAccountListUsingPOST
// https://micro-stg.huolala.work/ldoc/view#/ldoc/view/je24ozLJ/r2joNk8G/7zLMj62Q/mzWokbWz
// import { request } from '@umijs/max';

import { request } from '@umijs/max';

// interface IgetListParams {
//   pageNumber: number;
//   pageSize: number;
// }

export const statusMap = {
  '-20': '待预审',
  '-10': '预审驳回',

  //
  '1': '待认证',
  '4': '待授权',
  '5': '已授权',

  // 前端不关注:
  // '2': '企业认证成功',
  // '3': '企业认证失败',
  // '6': '中止', //删除后的，不关注
};

export type TauthStatus = keyof typeof statusMap;

export interface IauthenticationItem {
  epAuthNo: number; // 用户id
  productSecondCode: string; // 二级分类
  certiName: string; // 企业名称
  certiNo: string; // 统一社会信用代码
  authorizerName: string; // 授权人
  authStatus: TauthStatus; // 待认证/待授权/已授权
  createdAt: string; // 创建时间
  epAccreditDate: string; // 授权时间

  authorizerCertiNo: string; // 身份证号
}
export interface IgetListParams {
  authStatus?: string; // 认证状态
  certiName?: string; // 企业名称
  startCreatedAt?: string; // 创建时间
  endCreatedAt?: string; // 创建时间
  startEpAuthDate?: string; // 认证时间
  endEpAuthDate?: string; // 认证时间
  epAuthNo: string; // 用户id
  page?: number;
  size?: number;
  productSecondCode: string; // 产品二级code 默认查 0303 车险
}

export async function getList(params: IgetListParams) {
  return request('/bizadmin/insurance/policy/account/query/account/list', {
    method: 'POST',
    data: params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    ifTrimParams: true,
  });
}

/**
 * 获取授权二维码
 * @param epAuthNo
 * @returns
 */
type TgetAuthUrl = {
  authUrl: string; // 机构认证授权长链接
  authShortUrl: string; // 机构认证授权短链接
  authFlowId: string; // 本次认证授权流程ID
};
export async function getAuthUrl(epAuthNo: number): Promise<TgetAuthUrl> {
  const data = await request(`/bizadmin/insurance/policy/account/query/authUrl/${epAuthNo}`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data?.data;
}

/**
 * 新增
 */
interface IajaxAddParams {
  epAuthInfo: {
    certiNo: string; // 统一信用代码
    certiName: string; // 公司名称
    legalPerson: string; // 借款企业法人名称
    legalPersonCertiNo: string; // 法人证件号
    enterpriseAddress: string; // 企业地址
    tradingCertificateFilePath: string; // 企业营业执照原件oss相对路径
  };
  epAuthorizerAuthInfo: IepAuthorizerAuthInfo;
}

export interface IepAuthorizerAuthInfo {
  authorizerCertiNo: string; // 授权人证件号码
  authorizerName: string; // 授权人姓名
  authorizerTel: string; // 授权人电话
  authorizerEmail: string; // 授权人邮箱
  frontFilePath: string; // 身份证正面oss相对路径
  unFrontFilePath: string; // 身份证反面oss相对路径
  idCardFrontUrl: string;
  idCardUnFrontUrl: string;
}
export async function ajaxAdd(params: IajaxAddParams) {
  return request('/bizadmin/insurance/policy/account/create/account', {
    method: 'POST',
    data: params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

export async function updateEmail(params: {
  epAuthNo: string;
  authorizerEmail: string;
  stakeholderId: string;
}) {
  return request('/bizadmin/insurance/policy/account/update/email', {
    method: 'POST',
    data: params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

interface IupdateAuthorizerParams extends IepAuthorizerAuthInfo {
  epAuthNo: string; // 企业认证编号
}

export interface IauthInfo {}
export async function updateAuthorizer(params: IupdateAuthorizerParams) {
  return request('/bizadmin/insurance/policy/account/replace/authorizer', {
    method: 'POST',
    data: params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

export async function ajaxUpload() {}

// Generated by https://quicktype.io

export interface IauthInfo {
  epAuthInfo: EpAuthInfo;
  epAuthorizerAuthInfo: EpAuthorizerAuthInfo;
}

export interface EpAuthInfo {
  certiNo: string;
  certiName: string;
  legalPerson: string;
  legalPersonCertiNo: string;
  enterpriseAddress: string;
  tradingCertificateFilePath: null;
  enterpriseInfo: EnterpriseInfo;
  epAuthNo: string;
  productSecondCode: string;
  epAccreditDate: string;
  epAuthDate: string;
  createdAt: string;
  authStatus: number;
  businessLicenseUrl: string;
  extraInfo: ExtraInfo;
}

export interface EnterpriseInfo {
  imageInfo: string;
}

export interface ExtraInfo {
  epBankNo: string;
  accountName: string;
  bankName: string;
}

export interface EpAuthorizerAuthInfo {
  authorizerCertiNo: string;
  authorizerName: string;
  authorizerTel: string;
  authorizerEmail: string;
  frontFilePath: null;
  unFrontFilePath: null;
  enterpriseAuthorizerInfo: EnterpriseInfo;
  idCardFrontUrl: string;
  idCardUnFrontUrl: string;
}

// 认证详情
export async function getAuthDetail(params: { authenticationId: string }): Promise<TgetAuthUrl> {
  return await request(`/bizadmin/insurance/policy/account/query/detail`, {
    method: 'GET',
    params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    sensitiveSwitch: true,
  });
}
// 认证审核
export async function authReview(data) {
  return await request(`/bizadmin/insurance/policy/account/pretrial/audit`, {
    method: 'POST',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// 营业执照ocr
export async function bizLicenseOcr(params) {
  return await request(`/bizadmin/base/ocr/enterpriseLicenseOCR`, {
    method: 'GET',
    params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// 身份证ocr
export async function idCardOcr(params) {
  return await request(`/bizadmin/base/ocr/identityOcr`, {
    method: 'GET',
    params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// 删除认证记录
export async function deleteAuthRecord(id) {
  return await request(
    `/bizadmin/insurance/policy/account/delete/authentication?authenticationId=${id}`,
    {
      method: 'GET',
      headers: {
        'hll-appid': 'bme-finance-bizadmin-svc',
      },
    },
  );
}
// 异步导出
export async function asyncExportList(data) {
  return await request(`/bizadmin/insurance/policy/account/query/account/export`, {
    method: 'POST',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// 更新渠道
export async function updateChannelCode(data) {
  return await request(`/bizadmin/insurance/policy/account/replace/channelCode`, {
    method: 'POST',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// // 更新授权人
// export async function updateAuthorizer(data) {
//   return await request(`/bizadmin/insurance/policy/account/replace/channelCode`, {
//     method: 'POST',
//     data,
//     headers: {
//       'hll-appid': 'bme-finance-bizadmin-svc',
//     },
//   });
// }
