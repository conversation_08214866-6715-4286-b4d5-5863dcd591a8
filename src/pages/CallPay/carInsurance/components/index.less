.modal_tit {
  font-size: 24px;
}

.label_width {
  width: 70px;
  min-width: 70px;
}

.block_area {
  padding-top: 10px;
  padding-left: 20px;

  &.pt_0 {
    padding-top: 0px;
  }

  &.pl_0 {
    padding-left: 0px;
  }

  &.mb10 {
    margin-bottom: 10px;
  }
}

.tit {
  margin-top: 40px;
  font-weight: bold;
  font-size: 18px;

  &.mr5 {
    margin-right: 5px;
  }
}

.line {
  line-height: 40px;

  .repay_amount {
    display: inline-block;
    margin-right: 10px;
    margin-left: 10px;
    color: red;
    font-size: 18px;
    text-align: center;
  }

  .repay_label {
    display: inline-block;
    width: 110px;
    font-size: 18px;
    text-align: right;
  }

  &.mt10 {
    margin-bottom: 10px;
  }

  /* stylelint-disable */
  &::after {
    display: block;
    height: 0.5px;
    /* 设置分割线的高度为0.5px */
    margin: 10px auto;
    /* 上下外边距，以及自动左右外边距来居中 */
    background: #eee;
    /* 分割线的颜色 */
    content: '';
  }
  /* stylelint-enable */

  &.no_border {
    margin-bottom: 10px;
  }

  &.no_border::after {
    content: none;
  }
}

.remission_sel {
  display: flex;
  align-items: baseline;
}

.settle_mon {
  margin: 5px;
  color: red;
}

.bill_info_wrao {
  .ant-table-title {
    padding-top: 0;
  }
}

.reject_tip_wrap {
  padding: 10px 20px;
}

.relief_tit {
  padding-left: 20px;
  font-weight: 500;
  font-size: 16px;
}

.relief_detail_row {
  margin-bottom: 10px;
}

.attachment {
  margin-top: 20px;
}

.relief_amount_label {
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
}

.relief_amount_value {
  color: red;
  font-size: 14px;
}

.reject_reason_wrap {
  display: flex;
  margin-top: 20px;
}

.relief_detail_account_info {
  margin-top: 20px;
}

.relief_detail_table {
  thead {
    & tr th:nth-of-type(2) {
      background-color: #face92;
    }
  }

  tbody {
    & tr:nth-of-type(2) {
      & td:nth-child(1) {
        font-weight: 500;
      }
    }
  }
}
