import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Col, Drawer, message, Row, Space, Tree, Typography } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useEffect, useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import { useAccess } from 'umi';
import { statusMap } from '../const';
import '../index.less';
import { queryFeatureList, queryPrivilegeRoleList, queryRoleList } from '../service';
import { sysTypeMap } from './const';
import PostModal from './PostModal';
import SyncModal from './SyncModal';

const { Paragraph } = Typography;

// 树形列表
// enum TreeModeEnum {
//   OPEN = '1',
//   CLOSE = '0',
// }
interface ListItem {
  id: string;
  privilegeCode: string;
  privilegeName: string;
  parentPrivilegeCode: string;
  mark: string;
  url?: string;
  status?: string;
  createBy?: string;
  createdAt?: string;
}
const Page: React.FC<any> = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();

  // 弹窗
  const [isEdit, setIsEdit] = useState(false);
  const [visible, setVisible] = useState(false);
  const [listItem, setListItem] = useState<ListItem>();
  // 当前抽屉展示的行数据
  const [currentDrawerRecord, setCurrentDrawerRecord] = useState<ListItem>();
  // 获取所有角色
  const [roleList, setRoleList] = useState<any[]>([]);
  //
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const onExpand = (expandedKeysValue: string[]) => {
    console.log('onExpand', expandedKeysValue);
    setExpandedKeys(expandedKeysValue);
  };
  //
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const onCheck = (checkedKeysValue: string[]) => {
    console.log('onCheck', checkedKeysValue);
    setCheckedKeys((prev) => prev);
  };
  const getRoleInfo = async (privilegeCode: string) => {
    const list = await queryRoleList({ current: 1, pageSize: 200 }).catch(() => []);
    setRoleList(list?.data);
    const checkedList = await queryPrivilegeRoleList({ privilegeCode }).catch(() => []);
    const checkedRoleList = checkedList?.data?.map((value: any) => value.roleCode);
    setCheckedKeys(checkedRoleList);
    setExpandedKeys(checkedRoleList);
  };

  // 关闭抽屉
  const [open, setOpen] = useState(false);
  const handleClose = () => {
    setOpen(false);
    setCheckedKeys([]);
    setCurrentDrawerRecord(undefined);
  };
  const handleOpen = async (record: ListItem) => {
    console.log(record);
    setCurrentDrawerRecord(record);
    await getRoleInfo(record.privilegeCode);
    setOpen(true);
  };
  //
  const closeModal = () => {
    setVisible(false);
  };
  const openModal = (isEdit: boolean, item?: ListItem) => {
    console.log('打开弹窗', isEdit, item);
    setIsEdit(isEdit);
    if (isEdit) {
      setListItem(item);
    }
    //
    setVisible(true);
  };
  // 是否开启树形列表
  // 获取列表数据
  const getTableData = async (params: any) => {
    //
    // mock
    console.log('获取列表数据', params);
    return queryFeatureList(params);
  };
  // 同步批量操作
  // key
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  // record
  const [selectedRows, setSelectedRows] = useState<ListItem[]>([]);
  // 弹窗
  const [syncVisible, setSyncVisible] = useState(false);
  const openSyncModal = () => {
    if (!selectedRows || selectedRows?.length === 0) {
      message.error('请选择需要同步的功能');
      return;
    }
    setSyncVisible(true);
  };
  const closeSyncModal = () => {
    setSyncVisible(false);
  };
  //
  const columns: ProColumns<ListItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      search: false,
      width: 100,
    },
    {
      title: '权限名称',
      dataIndex: 'privilegeName',
      width: 300,
      search: {
        transform: (value: string) => {
          return {
            privilegeNameLike: value?.trim(),
          };
        },
      },
    },
    {
      title: '权限编码',
      dataIndex: 'privilegeCode',
      search: {
        transform: (value: string) => {
          return {
            privilegeCodeLike: value?.trim(),
          };
        },
      },
    },
    {
      title: '父级权限编码',
      dataIndex: 'parent',
      search: false,
      render(_, record: any) {
        return record?.parent?.privilegeCode || '-';
      },
    },
    {
      title: '系统标识',
      dataIndex: 'mark',
      valueEnum: sysTypeMap,
      width: 100,
      fieldProps: {
        mode: 'multiple',
      },
      search: {
        transform: (value: string) => {
          return {
            markList: value,
          };
        },
      },
    },
    {
      title: '资源ID',
      dataIndex: 'url',
      search: {
        transform: (value: string) => {
          return {
            url: value?.trim(),
          };
        },
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: statusMap,
      search: false,
      width: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 160,
      hideInTable: !(
        access.hasAccess('edit_privilege_manage') || access.hasAccess('bind_privilege_manage')
      ),
      render: (_, record) => {
        return (
          <>
            <Row>
              {access.hasAccess('edit_privilege_manage') && (
                <Col span={8}>
                  <Button type="link" onClick={() => openModal(true, record)}>
                    编辑
                  </Button>
                </Col>
              )}
              {access.hasAccess('bind_privilege_manage') && (
                <Col span={8}>
                  <Button type="link" onClick={() => handleOpen(record)}>
                    查看角色
                  </Button>
                </Col>
              )}
            </Row>
          </>
        );
      },
    },
  ];
  //

  //
  useEffect(() => {
    // getRoleInfo();
  }, []);

  return (
    <>
      <PageContainer className={globalStyle.mt16}>
        <ProTable<ListItem>
          actionRef={actionRef}
          rowSelection={{
            selectedRowKeys: selectedRowKeys,
            onChange: (_selectedRowKeys, _selectedRows) => {
              setSelectedRowKeys(_selectedRowKeys);
              setSelectedRows(_selectedRows);
            },
          }}
          // tableAlertOptionRender={false}
          // tableAlertRender={false}
          search={{
            defaultCollapsed: false,
            labelWidth: 90,
          }}
          formRef={formRef}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          request={(params) => {
            console.log('请求了', params);
            try {
              // 切页、刷新重置多选框为空
              setSelectedRowKeys([]);
              setSelectedRows([]);
            } catch (e) {
              console.log(e);
            }
            return getTableData(params);
          }}
          columns={columns}
          toolBarRender={() => {
            return [
              access.hasAccess('sync_privilege_manage') && (
                <Button
                  key="button"
                  // disabled={selectedRowKeys?.length === 0}
                  type="primary"
                  danger
                  onClick={openSyncModal}
                >
                  同步
                </Button>
              ),
              access.hasAccess('create_privilege_manage') && (
                <Button key="button" type="primary" onClick={() => openModal(false)}>
                  新建
                </Button>
              ),
            ];
          }}
        />
        <PostModal
          isEdit={isEdit}
          visible={visible}
          listItem={{
            ...listItem,
            parentId:
              listItem?.parentId && listItem?.parentId !== 0 ? listItem?.parentId : undefined,
          }}
          closeModal={closeModal}
          refreshTable={() => actionRef.current?.reload()}
        />
      </PageContainer>
      {/* 抽屉 */}
      <Drawer
        title={`已绑定角色`}
        placement="right"
        size="large"
        onClose={handleClose}
        open={open}
        extra={
          <Space>
            <Button onClick={handleClose}>关闭</Button>
          </Space>
        }
      >
        <div className="tree-wrapper">
          <div className="tree-header">
            <span className="tree-title">当前权限：</span>
            {currentDrawerRecord?.privilegeName}({currentDrawerRecord?.privilegeCode})
          </div>
          {/* <div className='tree-header'>展开所有层级：<Switch checked={isExpanded} onChange={(checked) => setIsExpanded(checked)} /></div> */}
          <div className="tree-body disabled-checkbox">
            <Tree
              checkable
              // disabled={true}
              selectable={false}
              checkStrictly={true}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              onCheck={onCheck}
              checkedKeys={checkedKeys}
              treeData={roleList}
              fieldNames={{ title: 'roleName', key: 'roleCode', children: 'children' }}
              // titleRender={(node) => `${node.roleName}(${node.roleCode})`}
              titleRender={(node: any) => {
                return (
                  <Paragraph copyable={{ text: node?.roleCode }} style={{ marginBottom: 0 }}>
                    {`${node?.roleName}(${node?.roleCode})`}
                  </Paragraph>
                );
              }}
            />
          </div>
        </div>
      </Drawer>
      {/* 同步弹窗 */}
      {syncVisible && (
        <SyncModal visible={syncVisible} selectedRows={selectedRows} closeModal={closeSyncModal} />
      )}
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'/authMng/feature'}>
      <Page />
    </KeepAlive>
  </>
);
