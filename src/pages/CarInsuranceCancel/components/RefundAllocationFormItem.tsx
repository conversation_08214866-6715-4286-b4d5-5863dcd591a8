import { QuestionCircleOutlined } from '@ant-design/icons';
import { Col, Divider, Form, Input, Row, Select, Tooltip } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useEffect, useMemo, useState } from 'react';
import {
  CancelStatusEnum,
  DetailInfo,
  RefundStatusEnum,
  refundStatusEnumMap,
  RefundTypeEnum,
} from '../const';
import '../index.less';
import { processRefundAmount } from '../utils';

interface ModalProps {
  refundAllocation: any;
  bankSearchProps: any;
  detailInfo: DetailInfo;
  formInstance: any;
  showRefundResult?: boolean;
  disabledInput?: boolean;
}

const Page: React.FC<ModalProps> = ({
  refundAllocation,
  bankSearchProps,
  detailInfo,
  formInstance,
  showRefundResult = false,
  disabledInput = false,
}) => {
  //
  const { handleSearchBank, fetchBankListLoading, asyncBankList } = bankSearchProps;
  //
  const [showYIRENXING, setShowYIRENXING] = useState(false);
  const [showUser, setShowUser] = useState(false);
  const [userInputRequired, setUserInputRequired] = useState(true);
  useEffect(() => {
    // 回款金额计算
    let res = {
      yirenxingAmount: '',
      userAmount: '',
    };
    if (refundAllocation !== RefundTypeEnum.ALLOCATION_5) {
      res = processRefundAmount(refundAllocation, {
        total: detailInfo?.actualPaymentAmount || '0',
        settle: detailInfo?.actualSettleAmount,
      });
    }
    formInstance?.setFieldsValue({
      yirenxingRefundAmount: res?.yirenxingAmount,
      userRefundAmount: res?.userAmount,
    });
    //
    setShowYIRENXING(() => {
      return [
        RefundTypeEnum.ALLOCATION_1,
        RefundTypeEnum.ALLOCATION_2,
        RefundTypeEnum.ALLOCATION_3,
        RefundTypeEnum.ALLOCATION_5,
      ].includes(refundAllocation);
    });
    //
    setShowUser(() => {
      return [
        RefundTypeEnum.ALLOCATION_1,
        RefundTypeEnum.ALLOCATION_2,
        RefundTypeEnum.ALLOCATION_4,
        RefundTypeEnum.ALLOCATION_5,
      ].includes(refundAllocation);
    });
    // 客户收款信息是否必填
    setUserInputRequired(() => {
      if (refundAllocation === RefundTypeEnum.ALLOCATION_5) {
        return true;
      }
      if (refundAllocation === RefundTypeEnum.ALLOCATION_4) {
        return true;
      }
      if (refundAllocation === RefundTypeEnum.ALLOCATION_3) {
        return false;
      }
      // return BigNumber(res?.userAmount).isGreaterThan(0);
      return true;
    });
  }, [refundAllocation, detailInfo?.actualPaymentAmount, detailInfo?.actualPaymentAmount]);
  //
  // 回款金额输入框，禁用状态
  const orderStatus: CancelStatusEnum = detailInfo?.orderStatus;
  const inputValid = useMemo(() => {
    // true: 允许修改回款金额
    // false: 不允许修改回款金额
    //
    // 退款失败、展示退款结果的情况下，不允许修改回款金额
    if (orderStatus === CancelStatusEnum.REFUND_FAIL && showRefundResult) {
      return false;
    }
    // 非退款失败的情况
    return refundAllocation === RefundTypeEnum.ALLOCATION_5;
  }, [refundAllocation, orderStatus]);
  // 客户退款成功，禁止修改账号信息
  const userRefundStatus = detailInfo?.userBankInfo?.refundStatus;
  const isUserRefundSuccess = useMemo(() => {
    return (
      detailInfo?.userBankInfo?.refundStatus &&
      detailInfo?.userBankInfo?.refundStatus === RefundStatusEnum.REFUND_SUCCESS
    );
  }, [userRefundStatus]);

  //
  if (!refundAllocation) {
    return null;
  }
  return (
    <>
      <Row gutter={[0, 0]} className="mt-14">
        <Col span={2} className="gray-text">
          收款方
        </Col>
        <Col span={3} className="gray-text">
          <span className="red-text">*</span>回款金额&nbsp;
          {!inputValid && !showRefundResult && (
            <Tooltip title="计算结果仅供参考">
              <QuestionCircleOutlined style={{ fontSize: '12px' }} />
            </Tooltip>
          )}
        </Col>
        <Col span={showRefundResult ? 5 : 6} className="gray-text">
          <span className="red-text">*</span>收款账户名
        </Col>
        <Col span={showRefundResult ? 5 : 6} className="gray-text">
          <span className="red-text">*</span>收款账号
        </Col>
        <Col span={showRefundResult ? 5 : 7} className="gray-text">
          <span className="red-text">*</span>收款银行
        </Col>
        {showRefundResult && (
          <Col span={4} className="gray-text">
            退款结果
          </Col>
        )}
      </Row>
      {/* 分割线 */}
      <div className="row-divider-wrap">
        <Divider className="row-divider" />
      </div>
      {/* 易人行 */}
      {showYIRENXING && (
        <>
          <Row gutter={[0, 0]} className="table-row-wrap">
            <Col span={2}>{detailInfo?.yiRenXingBankInfo?.userName || '-'}</Col>
            <Col span={3}>
              <Form.Item
                name="yirenxingRefundAmount"
                rules={[
                  { required: true, message: '请输入' },
                  {
                    //
                    validator: (_, val) => {
                      if (isNaN(val) || val?.includes('e')) {
                        return Promise.reject('请输入数字');
                      }
                      if (val !== '' && BigNumber(val).isLessThan('0')) {
                        return Promise.reject('不能小于0');
                      }
                      if (
                        val !== '' &&
                        BigNumber(val).isGreaterThan(detailInfo?.actualPaymentAmount)
                      ) {
                        return Promise.reject('超过实际退保金额');
                      }
                      if (val.includes('.') && val.toString().split('.')[1].length > 2) {
                        return Promise.reject('最多两位小数');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  type="number"
                  readOnly={!inputValid}
                  disabled={disabledInput}
                  prefix={!inputValid ? '￥' : ''}
                  bordered={inputValid}
                  // width={'100%'}
                  style={{ width: '100px' }}
                />
              </Form.Item>
            </Col>
            <Col span={showRefundResult ? 5 : 6}>
              {detailInfo?.yiRenXingBankInfo?.accountName || '-'}
            </Col>
            <Col span={showRefundResult ? 5 : 6}>
              {detailInfo?.yiRenXingBankInfo?.bankCardNo || '-'}
            </Col>
            <Col span={showRefundResult ? 5 : 7}>
              {detailInfo?.yiRenXingBankInfo?.subBranchName || '-'}
            </Col>
            {/* 退款结果 */}
            {showRefundResult && (
              <Col span={4}>
                <div>
                  {refundStatusEnumMap[
                    detailInfo?.yiRenXingBankInfo?.refundStatus as RefundStatusEnum
                  ] || '-'}
                </div>
                <div>
                  {detailInfo?.yiRenXingBankInfo?.refundStatus ===
                    RefundStatusEnum.REFUND_SUCCESS && (
                    <>{detailInfo?.yirenxingRefundTime || '-'}</>
                  )}
                </div>
              </Col>
            )}
          </Row>
          {/* 分割线 */}
          <div className="row-divider-wrap">
            <Divider className="row-divider" />
          </div>
        </>
      )}
      {/* 客户-待分配退款、退款失败 */}
      {showUser && !isUserRefundSuccess && (
        <>
          <Row gutter={[0, 0]} className="table-row-wrap">
            <Col span={2}>客户</Col>
            <Col span={3}>
              <Form.Item
                name="userRefundAmount"
                rules={[
                  { required: true, message: '请输入' },
                  {
                    //
                    validator: (_, val) => {
                      if (isNaN(val) || val?.includes('e')) {
                        return Promise.reject('请输入数字');
                      }
                      if (val !== '' && BigNumber(val).isLessThan('0')) {
                        return Promise.reject('不能小于0');
                      }
                      if (
                        val !== '' &&
                        BigNumber(val).isGreaterThan(detailInfo?.actualPaymentAmount)
                      ) {
                        return Promise.reject('不能超过实际退保金额');
                      }
                      if (val.includes('.') && val.toString().split('.')[1].length > 2) {
                        return Promise.reject('最多两位小数');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  type="number"
                  readOnly={!inputValid}
                  prefix={!inputValid ? '￥' : ''}
                  disabled={disabledInput}
                  bordered={inputValid}
                  // width={'100%'}
                  style={{ width: '100px' }}
                />
              </Form.Item>
            </Col>
            <Col span={showRefundResult ? 5 : 6}>
              <Form.Item
                name="accountName"
                rules={[
                  { required: userInputRequired, message: '请输入' },
                  { max: 60, message: '最多60个字符' },
                ]}
              >
                <Input disabled={disabledInput} />
              </Form.Item>
            </Col>
            <Col span={showRefundResult ? 5 : 6}>
              <Form.Item
                name="bankCardNo"
                rules={[
                  { required: userInputRequired, message: '请输入' },
                  { max: 25, message: '最多25个字符' },
                ]}
              >
                <Input disabled={disabledInput} />
              </Form.Item>
            </Col>
            <Col span={showRefundResult ? 5 : 7}>
              <Form.Item name="bankNo" rules={[{ required: userInputRequired, message: '请选择' }]}>
                <Select
                  showSearch
                  disabled={disabledInput}
                  filterOption={false}
                  onSearch={handleSearchBank}
                  loading={fetchBankListLoading}
                  options={asyncBankList || []}
                  dropdownStyle={{ width: '320px' }}
                />
              </Form.Item>
            </Col>
            {/* 退款结果 */}
            {showRefundResult && (
              <Col span={4}>
                <div>
                  {refundStatusEnumMap[detailInfo?.userBankInfo?.refundStatus as RefundStatusEnum]}
                </div>
                <div>
                  {detailInfo?.userBankInfo?.refundStatus === RefundStatusEnum.REFUND_SUCCESS && (
                    <>{detailInfo?.userRefundTime || '-'}</>
                  )}
                </div>
              </Col>
            )}
          </Row>
          {/* 分割线 */}
          <div className="row-divider-wrap">
            <Divider className="row-divider" />
          </div>
        </>
      )}
      {/* 客户-退款成功 */}
      {showUser && isUserRefundSuccess && (
        <>
          <Row gutter={[0, 0]} className="table-row-wrap">
            <Col span={2}>客户</Col>
            <Col span={3}>
              <Form.Item name="userRefundAmount" rules={[{ required: true, message: '请输入' }]}>
                <Input
                  readOnly={!inputValid}
                  disabled={disabledInput}
                  prefix={!inputValid ? '￥' : ''}
                  bordered={inputValid}
                  // width={'100%'}
                  style={{ width: '100px' }}
                />
              </Form.Item>
            </Col>
            <Col span={showRefundResult ? 5 : 6}>
              {detailInfo?.userBankInfo?.accountName || '-'}
            </Col>
            <Col span={showRefundResult ? 5 : 6}>{detailInfo?.userBankInfo?.bankCardNo || '-'}</Col>
            <Col span={showRefundResult ? 5 : 7}>
              {detailInfo?.userBankInfo?.subBranchName || '-'}
            </Col>
            {/* 退款结果 */}
            {showRefundResult && (
              <Col span={4}>
                <div>
                  {refundStatusEnumMap[
                    detailInfo?.userBankInfo?.refundStatus as RefundStatusEnum
                  ] || '-'}
                </div>
                <div>
                  {detailInfo?.userBankInfo?.refundStatus === RefundStatusEnum.REFUND_SUCCESS && (
                    <>{detailInfo?.userRefundTime || '-'}</>
                  )}
                </div>
              </Col>
            )}
          </Row>
          {/* 分割线 */}
          <div className="row-divider-wrap">
            <Divider className="row-divider" />
          </div>
        </>
      )}
    </>
  );
};

export default React.memo(Page);
