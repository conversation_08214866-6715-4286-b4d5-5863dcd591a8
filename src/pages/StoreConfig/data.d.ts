/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-07-22 11:14:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Incoming/data.d.ts
 */
// 获取企业用户管理列表的请求数据类型
export interface StoreConfigListParams {
  salesName?: string; //
  salesNo: string; //
  storeKey: string; //
  current?: number; // 当前页
  pageSize?: number; // 页大小
}

// 获取企业用户管理列表的响应数据类型
export interface StoreConfigListItem {
  salesName: string; //
  salesNo: string; //
  storeKey: string; //
  status?: number;
  storeName?: string;
  createTime?: string;
  id?: string;
}
