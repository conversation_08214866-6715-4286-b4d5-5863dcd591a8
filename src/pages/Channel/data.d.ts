/*
 * @Author: your name
 * @Date: 2021-07-20 17:03:08
 * @LastEditTime: 2025-05-07 15:20:47
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Channel/data.d.ts
 */
export interface ChannelListParams {
  channelName?: string; // 申请单号
  id?: string;
  pageNumber?: number;
  pageSize?: number;
  status?: number;
  licenseType?: string;
}

export interface ChannelListItem {
  channelName: string;
  channelType: string;
  channelTypeDesc: number;
  createdAt: string;
  id: string;
  receiveMaster: string;
  receiverAccount: string;
  receiverBank: string;
  status: number;
  updatedAt: string;
  qrCodeAddress: string;
  licenseTypeList?: number[];
}

export interface AddParams {
  channelName: string;
  channelType: number;
  dealerProvince: string;
  dealerCity: string;
  receiveMaster: string;
  receiverAccount: string;
  receiverBank: string;
  payBankNumber: string;
  licenseType: string;
  licenseTypeList?: number[];
  id?: string;
}

export interface LicenseParams {
  channelId: string;
}

export interface AddLicense {
  channelId?: number | string;
  licenseCity?: string;
  licenseCityCode?: string;
  licenseCompany?: string;
  orgCode?: string;
}

type ModifyLicenseInfo = Omit<AddLicense, 'channelId'> & { id?: string };

export interface AreaParams {
  channelId: string;
}

//添加区域
export interface AddArea {
  channelId?: number;
  saleCity?: string;
  saleCityCode?: string;
  storeAddress?: string;
  storeName?: string;
}

type ModifyAreaInfo = Omit<AddArea, 'channelId'> & { id?: string };

export interface AccountListItem {
  channelId: string;
  channelName: string;
  channelType: string;
  pid: string;
  status: string;
  storeName: string;
  storeId?: string;
  accountName: string;
}

export interface AccountListProps {
  current?: number;
  pageSize?: number;
  roleCode?: string;
  storeIds?: string[];
  channelId?: string[];
}

export interface AccountEnableProps {
  enable: boolean;
  pid: string;
  channelId: string;
  storeId?: string;
}

export interface AccountUpdateProps {
  password: string;
  pid: string;
}

export type GpsChannelConfigParams = {
  channelIds: string[];
  gpsSupplier: string;
};

export interface CarAssetsListItem {
  orderNo: string;
  vin: string;
  engineCode: string;
  status: string;
  updatedBy: string;
  updatedAt: string;
  distanceLendingDays: string;
}

export interface CarAssetsDetailItem {
  carModelCode?: string;
  vin?: string;
  channelId?: string;
  affiliatedCompany?: string;
  userName?: string;
  orderNo?: string;
  engineCode: string;
  vehicleRegisterCode: string;
  licenseCode: string;
  mortgagorName: string;
  mortgagorType: string;
  mortgagorCode: string;
  mortgagorDate: string;
  mortgagorCity: string;
  licenseProvince: string;
  licenseCity: string;
  registrationPage: string;
  registrationColumnPage: string;
}
