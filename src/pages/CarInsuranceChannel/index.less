.dynamic-button {
  // margin-bottom: 24px;
  margin-left: 2px;
  padding: 6px;
  font-weight: bold;
  font-size: 20px;
  background-color: #eee;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;

  &.add {
    color: #1890ff;
  }

  &.delete {
    color: #f00;
  }
}

.split-line {
  margin: 0px 6px 0px 6px;
  color: #d9d9d9;
}

.fee-company {
  color: #1890ff;
  cursor: pointer;

  .num {
    margin-right: 30px;
  }
}

.compensation-customers {
  .c_num_txt {
    margin-left: 28px;
  }

  .c_end {
    margin-left: 28px;
  }

  span:last-child {
    color: #1890ff;
    cursor: pointer;

    &:hover {
      color: #18baff;
    }
  }
}

.blance-container {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: flex-start;

  .blance_amount {
    margin-left: 28px;
  }

  .blance_btnlist {
    display: flex;
    gap: 14px;
    align-items: center;
    justify-content: flex-start;

    span {
      color: #1890ff;
      cursor: pointer;

      &:hover {
        color: #18baff;
      }
    }
  }
}

.account-info {
  .ant-form-item {
    margin-bottom: 12px;
  }
}
