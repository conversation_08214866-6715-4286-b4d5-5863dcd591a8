import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tag } from 'antd';
import React, { memo, useState } from 'react';
import { exportCommit, queryCommitList, queryCommitStatistics } from '../services';
import type { IcommitRecordItem, IfileType } from '../types';
import {
  downloadFileGenerateStatusLabelMap,
  EdownloadFileGenerateStatus,
  fileTypeMap,
  statusMap,
} from '../types';

type CommitRecordProps = {
  fileType: IfileType;
};
const CommitRecord: React.FC<CommitRecordProps> = (props) => {
  const { fileType } = props;

  const [totalText, setTotalText] = useState('');
  const columns: ProColumns<IcommitRecordItem>[] = [
    {
      title: '序号',
      key: 'id',
      dataIndex: 'id',
      search: false,
      // width: 150,
    },
    {
      title: '提交时间',
      key: 'createdAt',
      dataIndex: 'createdAt',
      search: false,
      // width: 150,
    },
    {
      title: '报数期数',
      key: 'dataMonth',
      dataIndex: 'dataMonth',
      valueType: 'dateMonth',
      fieldProps: {
        multiple: true,
      },
      // width: 150,
      // renderFormItem: () => {
      //   return <MultipleMonth />;
      // },
      formItemProps: {
        name: 'dataMonthList',
      },
    },
    {
      title: '报表类型',
      // width: 150,
      key: 'fileType',
      dataIndex: 'fileType',
      valueType: 'select',
      valueEnum: fileTypeMap,
      search: false,
    },
    {
      title: '文件名称',
      // width: 300,
      key: 'fileName',
      dataIndex: 'fileName',
      search: false,
    },
    {
      title: '报数状态',
      key: 'reportData',
      // width: 150,
      dataIndex: 'reportData',
      search: false,
      render(_, record) {
        const { successTotalAmount, failTotalAmount, successCount, failCount, status } = record;
        if (status === 2) {
          // 提交完成
          return (
            <div>
              <div>
                <Tag color="success">
                  已成功提交 {successCount || 0} 条数据, 总金额 {successTotalAmount || 0} 元
                </Tag>
              </div>
              <div>
                <Tag color="error">
                  失败 {failCount || 0} 条数据, 总金额 {failTotalAmount || 0} 元
                </Tag>
              </div>
            </div>
          );
        } else {
          return <div>{statusMap[status]}</div>;
        }
      },
    },
    {
      title: '文件生成状态',
      // width: 150,
      key: 'downloadFileGenerateStatus',
      dataIndex: 'downloadFileGenerateStatus',
      valueType: 'select',
      valueEnum: downloadFileGenerateStatusLabelMap, // 虽然返回的是数字，map中的是字符串，但是也能对上
      search: false,
    },
    {
      title: '操作',
      key: 'options',
      search: false,
      // width: 150,
      fixed: 'right',
      dataIndex: 'options',
      render(_, record, _1, action) {
        const { status, downloadFileNetworkUrl, downloadFileGenerateStatus } = record;
        // status 只有数据传输完成才能点击生成或者导出
        return (
          status === 2 && (
            <Space>
              <Button
                type="link"
                href={downloadFileNetworkUrl}
                disabled={downloadFileGenerateStatus !== EdownloadFileGenerateStatus.DONE}
              >
                导出
              </Button>
              <Button
                disabled={downloadFileGenerateStatus === EdownloadFileGenerateStatus.GENERATING}
                // 生成中不可以再次点击生成
                onClick={() => {
                  exportCommit(record.id)
                    .then(() => {
                      // downLoadExcel(res)
                      Modal.success({
                        content: '后端异步生成,请稍后点击刷新按钮查看生成状态。',
                      });
                      action?.reload();
                    })
                    .catch((err) => {
                      console.log('err', err);
                      action?.reload();
                      message.error('生成失败');
                    });
                }}
              >
                生成
              </Button>
            </Space>
          )
        );
      },
    },
  ];
  return (
    <ProTable
      columns={columns}
      scroll={{ x: 1300 }}
      headerTitle={<div style={{ color: 'red' }}>{totalText}</div>}
      request={async (values) => {
        const { dataMonthList, current = 1, pageSize = 10 } = values;
        if (dataMonthList?.length) {
          const tatalData = await queryCommitStatistics({ dataMonthList, fileType });
          const { successCount, totalAmount } = tatalData?.data;
          setTotalText(`合计：成功提交 ${successCount} 条数据,总金额 ${totalAmount} 元`);
        } else {
          setTotalText('');
        }
        const data = await queryCommitList({
          pageNo: current,
          pageSize,
          dataMonthList,
          fileType,
        });
        const { data: list, total } = data || {};
        return {
          data: list,
          total,
        };
      }}
    />
  );
};

export default memo(CommitRecord);
