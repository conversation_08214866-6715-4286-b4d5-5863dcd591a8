import globalStyle from '@/global.less';
import { exportRepayDetail } from '@/services/global';
import { downLoadExcel } from '@/utils/utils';
import type { ProColumns } from '@ant-design/pro-table';
import { Link, useRequest } from '@umijs/max';
import { Button, Card, Col, Row, Table } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useState } from 'react';
import type { RepayItem } from '../data';
import { getRepayInfo } from '../service';
import CreateForm from './CreateForm';
import RepayRegist from './RepayRegist';

interface RepayInfoProps {
  orderNo: string;
}

const mapStatus = {
  '1': {
    // 待还款
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    returnedCost: '已还费用',
    remainingAmountDue: '剩余应还总额',
    remainingPrincipal: '剩余应还本金',
    remainingInterest: '剩余应还利息',
    remainingCost: '剩余应还费用',
  },
  '2': {
    // 提前结清
    actualRepayTime: '实际结清时间',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    // :"提前结清违约金",
    returnedCost: '已还费用',
  },
  '3': {
    // 结清
    actualRepayTime: '实际结清时间',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    // :"提前结清违约金",
    returnedCost: '已还费用',
  },
  '4': {
    // 逾期
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    // :"已还罚息",
    returnedCost: '已还费用',
    remainingAmountDue: '剩余应还总额',
    remainingPrincipal: '剩余应还本金',
    remainingInterest: '剩余应还利息',
    // :'剩余应还罚息',
    remainingCost: '剩余应还费用',
  },
  '5': {
    // 逾期结清
    // :"实际结清时间",
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    // :'已还罚息',
    returnedCost: '已还费用',
  },
  '6': {
    // 坏账
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    // :'已还罚息',
    returnedCost: '已还费用',
  },
};
const expandLine = (record: RepayItem) => {
  // if(record.status==='待还款'){
  return (
    <Row>
      {Object.keys(mapStatus[record?.status]).map((item) => {
        return (
          <Col span={6} offset={2} key={item}>
            <div style={{ lineHeight: '40px' }}>
              <span style={{ marginRight: 20 }}>{mapStatus[record?.status][item]}:</span>
              <span>{record[item]}</span>
            </div>
          </Col>
        );
      })}
    </Row>
  );
  //  }
};

const RepayInfo: React.FC<RepayInfoProps> = (props) => {
  const { data } = useRequest(() => {
    return getRepayInfo(props.orderNo);
  });
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [repayPlanNo, setPlanNo] = useState<string>('');
  const [loadingExport, setExportLoading] = useState<boolean>(false);

  const columns: ProColumns<RepayItem>[] = [
    {
      title: '还款状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, row) => {
        const mapStatusZh = {
          1: '待还款',
          2: '提前结清',
          3: '结清',
          4: '逾期',
          5: '逾期结清',
          6: '坏账',
        };
        return mapStatusZh[row.status];
      },
    },
    {
      title: '应还日期',
      dataIndex: 'repayTime',
      key: 'repayTime',
      render: (_, row) => (row.repayTime ? row.repayTime : '待确定'),
    },
    { title: '应还总额', dataIndex: 'shouldAmountDue', key: 'shouldAmountDue' },
    { title: '应还本金', dataIndex: 'shouldPrincipal', key: 'shouldPrincipal' },
    { title: '应还利息', dataIndex: 'shouldInterest', key: 'shouldInterest' },
    { title: '费用', dataIndex: 'shouldCost', key: 'shouldCost' },
    {
      title: '操作',
      dataIndex: '',
      key: 'repayPlanNo',
      render: (_: React.ReactNode, record, dataIndex: number) => (
        <>
          {(record.status === 4 || record.status === 5) && (
            // <Button
            //   type="link"
            //   onClick={() => {
            //     if (!record.overdueId) {
            //       message.warning('催收单号有误！');
            //       return;
            //     }
            //     history.push(
            //       createDetailPath({
            //         productCode: history.location.query.productCode,
            //         overdueId: record.overdueId,
            //       }),
            //     );
            //   }}
            // >
            //   查看逾期详情
            // </Button>
            <Link
              to={{
                pathname: `/businessMng/postLoanMng/collection-detail`,
                search: `?overdueCaseNo=${record?.overdueCaseNo}`,
              }}
              state={{
                curList: [],
                curItemIndex: dataIndex,
              }}
              style={{ display: 'block' }}
            >
              查看逾期详情
            </Link>
          )}
          <a
            // type="link"
            onClick={() => {
              setPlanNo(record?.repayPlanNo);
              handleModalVisible(true);
            }}
            style={{ display: 'block' }}
          >
            查看还款登记
          </a>
        </>
      ),
    },
  ];
  const getExport = () => {
    setExportLoading(true);
    exportRepayDetail(props.orderNo)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  return (
    <Card
      title="还款信息"
      className={globalStyle.mt30}
      extra={
        <Button loading={loadingExport} onClick={getExport} type="primary">
          导出
        </Button>
      }
    >
      <Table
        columns={columns as ColumnsType<RepayItem> | undefined}
        rowKey="repayPlanNo"
        expandable={{
          expandedRowRender: (record) => {
            return expandLine(record);
          },
          // rowExpandable: (record) => record.name !== 'Not Expandable',
        }}
        dataSource={data}
        pagination={false}
      />
      <CreateForm
        onCancel={() => {
          handleModalVisible(false);
        }}
        modalVisible={createModalVisible}
        title="还款登记"
      >
        <RepayRegist planNo={repayPlanNo} />
      </CreateForm>
    </Card>
  );
};

export default RepayInfo;
