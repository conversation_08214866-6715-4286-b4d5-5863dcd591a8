import globalStyle from '@/global.less';
import { history, useModel } from '@umijs/max';
import { Button, Form, message } from 'antd';
import React, { memo, useRef, useState } from 'react';
import { addProduct } from '../service';
import InsuranceBasicInfo from './InsuranceBasicInfo';
import InsuranceFeeConfig from './InsuranceFeeConfig';
import InsuranceOtherInfo from './InsuranceOtherInfo';
import InsuranceProcessConfig from './InsuranceProcessConfig';

const InsuranceAddProduct: React.FC = () => {
  const otherInfoRef = useRef<{ otherInfoSubmit: () => void }>();
  const [currentKey, setCurrentKey] = useState('insuranceIncoming');
  const { costConfigDTO } = useModel('Product.insuranceProduct', (model) => {
    return {
      costConfigDTO: model.costConfigDTO,
    };
  });

  const ref = useRef({
    isSaveDraft: 0,
  });

  /**
   * 校验所有表单错误 并且滚动到第一个有错误的位置
   * 只需要校验所有表单,把有错误的push到数组中
   * 然后延迟执行promise 以阻止后续代码执行
   */
  async function handleError(forms: any) {
    const formNames = Object.keys(forms); // form name
    const errNameList: string[] = [];
    const tabsKeys = [
      'insuranceIncoming',
      'insuranceLoan',
      'insuranceOverdue',
      'insurancePayBack',
      'insuranceSettleEarly',
    ];
    return new Promise((reslove, reject) => {
      formNames.map((instName: any) => {
        const inst = forms[instName];
        inst
          .validateFields()
          .then()
          .catch((err: any) => {
            const { errorFields = [] } = err;
            errNameList.push(errorFields[0]?.name?.[0]);
            if (errNameList.length === 1) {
              // 滚动到第一个有错误的位置 当length === 2 时 还是要滚动到第一个位置 所以干脆不需要滚动
              inst.scrollToField(errNameList[0], {
                block: 'end',
              });
              if (tabsKeys.includes(instName)) {
                setCurrentKey(instName);
              }
            }
          });
      });

      setTimeout(() => {
        if (errNameList?.length) {
          reject();
        } else {
          reslove('ok');
        }
      }, 100);
    });
  }
  return (
    <Form.Provider
      onFormFinish={async (name: string, { forms }: any) => {
        const { isSaveDraft } = ref.current;
        if (!isSaveDraft) {
          await handleError(forms);
        }
        const {
          basicInfo,
          insuranceLoan,
          insuranceIncoming,
          insuranceOverdue,
          insurancePayBack,
          insuranceSettleEarly,
          otherInfo,
        } = forms;

        // 看是否需要额外的处理 不需要直接改用循环
        const basicInfoValues = basicInfo.getFieldsValue();
        const insuranceLoanValues = insuranceLoan.getFieldsValue();
        const insuranceIncomingValues = insuranceIncoming.getFieldsValue();
        const insuranceOverdueValues = insuranceOverdue.getFieldsValue();
        const insurancePayBackValues = insurancePayBack.getFieldsValue();
        const insuranceSettleEarlyValues = insuranceSettleEarly.getFieldsValue();
        const otherInfoValues = otherInfo.getFieldsValue();

        const { id, productCode } = history.location.query as {
          id: string;
          productCode: string;
        };
        const params = {
          ...basicInfoValues,
          ...otherInfoValues,
          activateDTO: insuranceIncomingValues, // 进件
          loanDTO: insuranceLoanValues, // 放款
          repaymentDTO: insurancePayBackValues, // 还款
          inAdvanceRepaymentDTO: insuranceSettleEarlyValues, // 提前结清
          overdueDTO: insuranceOverdueValues, // 逾期
          status: isSaveDraft ? -1 : 1,
          costConfigDTO: costConfigDTO,
        };
        if (id) {
          // 查看详情
          params.id = id;
          params.productCode = productCode;
        }
        console.log('params', params);
        addProduct(params).then(() => {
          if (isSaveDraft) {
            message.success('保存草稿成功');
            history.push('/businessMng/draft-list');
          } else {
            message.success('提交成功');
            history.push('/businessMng/product-list');
          }
        });
      }}
    >
      <InsuranceBasicInfo />
      <InsuranceProcessConfig currentKey={currentKey} setCurrentKey={setCurrentKey} />
      <InsuranceFeeConfig />
      <InsuranceOtherInfo otherInfoRef={otherInfoRef} />
      {/* 整个创建产品Form提交操作 */}
      <div style={{ display: 'flex', marginTop: 10, justifyContent: 'flex-end' }}>
        <Button
          onClick={() => {
            history.push('/businessMng/product-list');
          }}
        >
          取消
        </Button>
        <Button
          type="primary"
          className={globalStyle?.ml10}
          onClick={() => {
            ref.current.isSaveDraft = 1;
            otherInfoRef?.current?.otherInfoSubmit();
          }}
        >
          保存为草稿
        </Button>
        <Button
          type="primary"
          className={globalStyle?.ml10}
          onClick={() => {
            ref.current.isSaveDraft = 0;
            otherInfoRef?.current?.otherInfoSubmit();
          }}
        >
          提交
        </Button>
      </div>
    </Form.Provider>
  );
};

export default memo(InsuranceAddProduct);
