/*
 * @Author: your name
 * @Date: 2021-11-15 16:40:35
 * @LastEditTime: 2023-12-29 15:53:48
 * @LastEditors: oak.yang <EMAIL>
 * @Description: EnterpriseIncoming
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseIncoming/data.d.ts
 */
// 获取进件记录列表的响应数据类型
export interface UserInputRecordListItem {
  applyAmount: number; // 申请额度
  startApplyTime: string;
  endApplyTime: string;
  applyTime: string; // 申请时间
  classification: string; // 产品一级分类
  enterpriseName: string; // 企业名称
  orderNo: string; // 进件流水号
  secondClassification: string; // 产品二级分类
  status: number; // 状态
  userNo: string; // 用户编号
  orgCode: string; // 统一社会信用代码
  applyNo: string;
}

// 获取进件记录列表的请求数据类型
export interface UserInputRecordListParams {
  endApplyTime?: string; // 申请结束时间
  enterPriseName?: string; // 企业名称
  orgCode?: string; // 统一社会信用代码
  startApplyTime?: string; // 申请起始时间
  status?: number; // 状态
  userNo?: number; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 页大小
}

export interface CreateFactoryData {
  applyAmount?: number;
  authorEmail?: string;
  authorIdType?: number;
  authorizedIdNo?: string;
  authorizedName?: string;
  authorizedPhone?: string;
  channelId?: string;
  epName?: string;
  legalMan?: string;
  orgCode?: string;
  productCode?: string;
}
export interface UnRegistrationParams {
  enterpriseId: string;
  productSecondTypeCode: string;
  orderNo: string;
}

export type PromoteListParams = {
  current?: number;
  endApplyTime?: string;
  enterpriseName?: string;
  orgCode?: string;
  pageSize?: number;
  secondClassification?: string;
  startApplyTime?: string;
  status?: number;
  userNo?: string;
  userTag?: string;
  applyNo?: string;
};

export type PromoteListItem = {
  applyAmount?: number;
  applyTime?: string;
  enterpriseFlow?: number;
  beforeAmount?: number;
  certNo?: string;
  creditAmount?: string;
  creditEndTime?: string;
  extendInfo?: Record<string, any>;
  flag?: number;
  flowNo?: string;
  orderNo?: string;
  productCode?: string;
  productSecondCode?: string;
  rejectReason?: string;
  riskOrderNo?: string;
  riskStartTime?: string; //风控进件时间
  status?: number;
  updatedAt?: string;
  userFlag?: string;
  userName?: string;
  userNo?: string;
  userType?: number;
};
