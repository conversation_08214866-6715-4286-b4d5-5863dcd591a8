import ImagePreview from '@/components/ImagePreview';
import globalStyle from '@/global.less';
import { getCompensationCustomerList } from '@/pages/CarInsuranceChannel/services';
import { ProTable } from '@ant-design/pro-components';
import { Card } from 'antd';
import React, { memo } from 'react';
import './index.less';

interface CompensationRelationshipProps {
  channelCode: string | undefined;
}

const statusEnum = {
  init: '待审核',
  submit: '审核中',
  bind: '审核通过',
  reject: '审核拒绝',
  unbind: '解绑',
};

const CompensationRelationship: React.FC<CompensationRelationshipProps> = ({ channelCode }) => {
  const columns = [
    {
      title: '序号',
      valueType: 'index',
    },
    {
      title: '用户ID',
      dataIndex: 'entityTee',
    },
    {
      title: '用户姓名',
      dataIndex: 'entityTeeName',
    },
    {
      title: '身份证号',
      dataIndex: 'entityTeeIdNo',
    },
    {
      title: '审核状态',
      dataIndex: 'authStatus',
      valueEnum: statusEnum,
    },
    {
      title: '附件',
      dataIndex: 'relativePath',
      render: (_, record) => {
        if (!record?.relativePath) {
          return '-';
        }
        return (
          <div className="compensation-attach">
            <ImagePreview url={record?.relativePath}>
              <span>{record?.entityTeeName}担保合同</span>
            </ImagePreview>
          </div>
        );
      },
    },
  ];

  // 请求代偿客户列表
  const request = async (params) => {
    const newParams = {
      ...params,
      channelCode,
      productSecondCode: '0201',
    };
    console.log('newParams', newParams);
    const data = await getCompensationCustomerList(newParams);
    return {
      data: data?.data,
      success: true,
      total: data?.total,
    };
  };

  return (
    <Card title="代偿关系" className={globalStyle.mt20}>
      <ProTable
        columns={columns}
        request={request}
        search={false}
        toolBarRender={false}
        pagination={{
          defaultPageSize: 20,
          defaultCurrent: 1,
          showQuickJumper: true,
          showSizeChanger: true,
        }}
      />
    </Card>
  );
};

export default memo(CompensationRelationship);
