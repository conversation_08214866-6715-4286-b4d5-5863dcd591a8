/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2021-01-28 14:59:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/PassModel.ts
 */
import {
  ModalForm,
  ProFormDateTimePicker,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { history, useModel, useRequest } from '@umijs/max';
import React from 'react';
// import { message } from 'antd';
import { Form, message } from 'antd';
import type { IActionEnum, ICallDetail } from '../data';
import { getActionList, postCaller } from '../service';

export type AddCallPayModalProps = {
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  source: {
    isDialOuter: boolean;
    callRecord: string;
    dialTime: string | null;
    dialNumber: string;
    urgePersonName: string;
    urgePersonId: string;
    productCode: string;
    productName: string;
    actionSign?: number;
    actionResults?: number;
    remarks?: string;
    actionResultList?: [];
  };
};

const AddCallPayModal: React.FC<AddCallPayModalProps> = (props) => {
  const { overdueId } = history.location.query;
  const {
    source,
    source: { urgePersonId, callRecord, productCode, productName },
    onOk,
    modalVisible,
    onVisibleChange,
  } = props;
  const { data: actionList } = useRequest(() => {
    // 获取标识枚举
    return getActionList(productCode);
  });
  // 更新 outId
  const { setCallOutId } = useModel('caller');
  setCallOutId(overdueId);
  // formRef
  const [formRef] = Form.useForm();

  return (
    <>
      <ModalForm
        title="添加催收记录"
        width={600}
        form={formRef}
        layout="horizontal"
        visible={modalVisible}
        onVisibleChange={(visible) => onVisibleChange(visible)}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          maskClosable: false,
        }}
        initialValues={source}
        onFinish={async (values: ICallDetail) => {
          const params = {
            ...values,
            overdueId,
            urgePersonId,
            callRecord,
            productName,
            productCode,
          };

          // 添加催收记录
          const res = await postCaller(params);
          if (res.success) {
            message.success('添加成功');
            onOk();
          } else {
            message.error(res.msg);
          }
        }}
      >
        <ProFormText
          name="urgePersonName"
          labelCol={{ span: 4 }}
          width="md"
          label="催收员"
          readonly
        />
        <ProFormText
          name="dialNumber"
          labelCol={{ span: 4 }}
          width="md"
          label="拨打号码"
          rules={[
            { required: true, message: '请输入号码' },
            {
              validator: (_, value) => {
                if (value) {
                  const phoneReg = /^1\d{10}$/;
                  if (!phoneReg.test(value)) {
                    return Promise.reject(new Error('号码格式不正确'));
                  }
                }
                return Promise.resolve();
              },
            },
          ]}
          readonly={source.isDialOuter}
        />
        <ProFormDateTimePicker
          name="dialTime"
          labelCol={{ span: 4 }}
          width="md"
          label="拨打时间"
          rules={[{ required: true, message: '请选择拨打时间' }]}
          readonly={source.isDialOuter}
        />
        <ProFormSelect<IActionEnum>
          name="actionSign"
          fieldProps={{
            options: actionList,
            onChange: (_, { actionResultList }) => {
              formRef.setFieldsValue({
                actionResults: actionResultList[0].value,
              });
              formRef.setFieldsValue({ actionResultList });
            },
          }}
          labelCol={{ span: 4 }}
          width="md"
          label="行动标识"
          rules={[{ required: true, message: '请选择行动标识' }]}
        />
        <ProFormDependency name={['actionResultList']}>
          {({ actionResultList }) => {
            return (
              <ProFormSelect
                name="actionResults"
                fieldProps={{
                  options: actionResultList,
                }}
                labelCol={{ span: 4 }}
                width="md"
                label="行动结果"
                rules={[{ required: true, message: '请选择行动结果' }]}
              />
            );
          }}
        </ProFormDependency>
        <ProFormTextArea
          name="remarks"
          labelCol={{ span: 4 }}
          width="md"
          placeholder="请输入备注"
          fieldProps={{ maxLength: 100, showCount: true }}
          label="备注"
        />
      </ModalForm>
    </>
  );
};

export default AddCallPayModal;
