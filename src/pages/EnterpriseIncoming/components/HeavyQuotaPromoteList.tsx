/*
 * @Date: 2023-08-31 17:14:01
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-10-19 13:39:47
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseIncoming/components/HeavyQuotaPromoteList.tsx
 * @Description:
 */
import { CLASSIFICATION_CODE_LABEL, SECONDARY_CLASSIFICATION_MAP } from '@/enums';
import { downLoadExcel } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess } from '@umijs/max';
import { Button, Tooltip } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import type { UserInputRecordListItem } from '../data';
import { exporHeavyQuota, getPromoteList } from '../service';

export const statusMap = {
  1: '申请单创建成功',
  2: '已提交用户信息',
  9: '拒绝（基础前筛）',
  10: '风控中',
  19: '拒绝（风控前筛）',
  30: '待初审',
  31: '待终审',
  40: '审核通过',
  41: '审核拒绝',
  '-1': '撤销',
};
const HeavyQuotaPromotionList: React.FC<{}> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();

  const [exportLoading, setExportLoading] = useState(false);
  // 当前登陆用户信息
  const access = useAccess();
  // const [visibleCreate, handleVisibleCreate] = useState<boolean>(false);

  const transformDateRange = (startKey: string, endKey: string, values: any) => {
    if (!values) return values;
    if (typeof values[0] !== 'string') {
      return {
        [startKey]: `${values[0].format('YYYY-MM-DD')} 00:00:00`,
        [endKey]: `${values[1].format('YYYY-MM-DD')} 23:59:59`,
      };
    }
    return {
      [startKey]: `${values[0].split(' ')[0]} 00:00:00`,
      [endKey]: `${values[1].split(' ')[0]} 23:59:59`,
    };
  };

  const columns: ProColumns<UserInputRecordListItem>[] = [
    {
      title: '提额流水号',
      dataIndex: 'applyNo',
      key: 'applyNo',
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
    },
    {
      title: '企业名称',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'certNo',
      key: 'certNo',
    },
    {
      title: '进件渠道',
      dataIndex: 'creditChannelType',
      key: 'creditChannelType',
      valueEnum: {
        0: 'APP',
        1: '轩辕',
      },
    },
    {
      title: '产品一级分类',
      dataIndex: 'productFirstCode',
      key: 'productFirstCode',
      valueEnum: CLASSIFICATION_CODE_LABEL,
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeCode',
      key: 'productSecondTypeCode',
      valueEnum: SECONDARY_CLASSIFICATION_MAP,
      // fieldProps: {
      //   showSearch: true,
      //   mode: 'multiple',
      //   disabled: access.hasRole('enterpriseFinance') ? true : false,
      //   showArrow: true,
      //   optionFilterProp: 'label',
      //   filterOption: (input: string, option: { label: string }) =>
      //     option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      // },
      search: false,
    },

    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeCodeList',
      key: 'productSecondTypeCodeList',
      valueType: 'select',
      hideInTable: true,
      // 如果是企业财务，默认共享应收账款，应收账款
      // initialValue: access.hasRole('enterpriseFinance') ? ['0103', '0106'] : [],
      valueEnum: SECONDARY_CLASSIFICATION_MAP,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: access.hasRole('enterpriseFinance') ? true : false,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      // search: false,
    },
    {
      title: '提额时间',
      dataIndex: 'applyTime',
      key: 'applyTime',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(6, 'month'), dayjs()],
      search: {
        transform: (values: any) => {
          return transformDateRange('startApplyTime', 'endApplyTime', values);
        },
      },
      render(_, record) {
        return record?.applyTime;
      },
    },
    {
      title: '申请额度',
      dataIndex: 'applyAmount',
      key: 'applyAmount',
      search: false,
    },
    {
      title: '提额状态',
      dataIndex: 'statusList',
      key: 'statusList',
      valueEnum: statusMap,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      hideInTable: true,
    },
    {
      title: '提额状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: statusMap,
      search: false,
    },
    {
      title: '拒绝原因',
      dataIndex: 'rejectReason',
      key: 'rejectReason',
      search: false,
      width: 200,
      render: (rejectReason) => {
        // const rejectReason = '资格类资格类';
        return rejectReason?.length > 6 ? (
          <Tooltip placement="topLeft" title={rejectReason}>
            <div
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                width: 200,
                textOverflow: 'ellipsis',
              }}
            >
              {rejectReason}
            </div>
          </Tooltip>
        ) : (
          rejectReason
        );
      },
    },
    {
      title: '提额授信额度',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
      search: false,
    },
    {
      title: '客户标签',
      dataIndex: 'userTag',
      key: 'userTag',
      search: false,
      valueEnum: {
        GKA: 'GKA',
      },
    },
    {
      title: '审核完成时间',
      dataIndex: 'creditEndTime',
      key: 'creditEndTime',
      valueType: 'dateRange',
      search: {
        transform: (values: any) => {
          return transformDateRange('creditEndGeTime', 'creditEndLtTime', values);
        },
      },
      render(_, record) {
        return record?.creditEndTime;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateRange',
      search: {
        transform: (values: any) => {
          return transformDateRange('createStartTime', 'createEndTime', values);
        },
      },
      render(_, record) {
        return record?.createdAt;
      },
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (text, row) => (
        <>
          <Link to={`/userMng/enterpriseMng/heavy-promote-detail?orderNo=${row.applyNo}`}>
            查看详情
          </Link>
        </>
      ),
    },
  ];

  return (
    <>
      <ProTable<UserInputRecordListItem>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        scroll={{ x: 'max-content' }}
        rowKey="applyNo"
        search={{ labelWidth: 120 }}
        toolBarRender={() => {
          return [
            <Button
              type="primary"
              loading={exportLoading}
              onClick={() => {
                setExportLoading(true);
                const {
                  applyTime,
                  creditEndTime,
                  createdAt,
                  ...data
                } = formRef?.current?.getFieldsValue();
                const applyTimeObj = transformDateRange(
                  'startApplyTime',
                  'endApplyTime',
                  applyTime,
                );
                const creditEndTimeObj = transformDateRange(
                  'creditEndGeTime',
                  'creditEndLtTime',
                  creditEndTime,
                );
                const createdAtObj = transformDateRange(
                  'createStartTime',
                  'createEndTime',
                  createdAt,
                );
                const newForm = {
                  ...data,
                  ...applyTimeObj,
                  ...creditEndTimeObj,
                  ...createdAtObj,
                };
                console.log(newForm);
                exporHeavyQuota(newForm)
                  .then((res) => {
                    downLoadExcel(res);
                    setExportLoading(false);
                  })
                  .catch(() => {
                    setExportLoading(false);
                  });
              }}
            >
              导出
            </Button>,
          ];
        }}
        request={(params) => getPromoteList(params)}
      />
      {/*
      <BackStageIncomeModal
        modalVisible={visibleCreate}
        onOk={async () => {
          handleVisibleCreate(false);

          actionRef?.current?.reload();
        }}
        // isNewCar={}
        onCancel={() => {
          handleVisibleCreate(false);
        }}
        // isNewCar={props.deadLine}
        onVisibleChange={handleVisibleCreate}
      /> */}
    </>
  );
};

export default HeavyQuotaPromotionList;
