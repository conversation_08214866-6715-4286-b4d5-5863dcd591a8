/*
 * @Author: your name
 * @Date: 2021-09-16 20:04:03
 * @LastEditTime: 2023-12-25 17:12:24
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/EnterpriseChangeModal.tsx
 */
import { getUuid } from '@/utils/utils';
import { ProFormRadio } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { Modal } from 'antd';
import React, { useRef } from 'react';
import type { LogListItem } from '../data';
import { getAuthInfoLog } from '../service';

interface EnterpriseChangeModalProps {
  title: string;
  visible: boolean;
  onCancel: () => void;
  // refresh: () => void;
  onVisibleChange?: any;
  productSecondTypeCode?: string;
}

const EnterpriseChangeModal: React.FC<EnterpriseChangeModalProps> = (props) => {
  const { title, visible, onCancel, productSecondTypeCode } = props;
  // const [mode, setModel] = useState<string>('变更');
  const { userNo } = history.location.query;

  const actionRef = useRef<ActionType>();

  const itemTableMap = (val: { key: string; type: number; value: string }[] = []) => {
    return val.length
      ? val.reduce((pre: React.ReactNode, cur: any) => {
          return (
            <>
              {pre}
              <div>
                {cur?.type === 2 ? (
                  <a
                    target="_blank"
                    onClick={() => {
                      previewPDF(cur?.value, cur?.key);
                    }}
                  >
                    {cur?.key}
                  </a>
                ) : (
                  <span>
                    {cur?.key}：{cur?.value}
                  </span>
                )}
              </div>
            </>
          );
        }, <></>)
      : '-';
  };
  const columns: ProColumns<LogListItem>[] = [
    {
      title: '产品名称',
      dataIndex: 'productSecondTypeName',
      // render: () => {
      //   return '明保';
      // },
    },
    {
      title: '变更前',
      // dataIndex: 'beforeValue',
      // className: enterpriseMngStyle.textTop,
      render: (_, record) => {
        return itemTableMap(record?.beforeValue);
      },
    },
    {
      title: '变更后',
      // dataIndex: 'afterValue',
      // className: enterpriseMngStyle.textTop,
      render: (_, record) => {
        return itemTableMap(record?.afterValue);
      },
    },
    {
      title: '变更时间',
      dataIndex: 'createdAt',
    },
    {
      title: '变更人',
      dataIndex: 'operatorBy',
    },
  ];
  return (
    <Modal
      destroyOnClose
      centered
      title={title}
      open={visible}
      onCancel={() => onCancel()}
      width={900}
      // onVisibleChange={onVisibleChange}
      footer={null}
    >
      <ProFormRadio.Group
        style={{
          margin: 16,
        }}
        radioType="button"
        fieldProps={{
          value: '变更日志',
        }}
        options={['变更日志']}
      />
      {
        <div>
          <ProTable<LogListItem>
            actionRef={actionRef}
            rowKey={getUuid}
            scroll={{ x: 'max-content' }}
            // dataSource={[{ cost: 500, amountDue: 2 }]}
            columns={columns}
            toolBarRender={false}
            search={false}
            request={(params) =>
              getAuthInfoLog({
                ...params,
                enterpriseId: userNo,
                productSecondTypeCode,
                bizType: 'MODIFY_ENTERPRISE',
              })
            }
          />
        </div>
      }
    </Modal>
  );
};
export default EnterpriseChangeModal;
