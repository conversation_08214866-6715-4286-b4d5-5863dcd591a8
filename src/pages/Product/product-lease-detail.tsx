/*
 * @Author: your name
 * @Date: 2021-04-14 18:38:09
 * @LastEditTime: 2025-04-24 15:36:46
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/product-lease-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import BasicInfo from './components/BasicInfo';
import LeaseFeeConfig from './components/LeaseFeeConfig';
import OtherInfo from './components/OtherInfo';
import ProLeaseProcessConfig from './components/ProLeaseProcessConfig';
import { queryProductDetail } from './service';

const ProductDetail: React.FC<any> = () => {
  const { productCode } = history.location.query;
  const [repaymentTerm, setRepaymentTerm] = useState<[]>([]);
  const { data } = useRequest(() => {
    return queryProductDetail(productCode);
  });
  // console.log(data);
  const baseInfo = {
    classification: data?.classification || '',
    productName: data?.productName || '',
    secondaryClassification: data?.secondaryClassification || '',
    userType: data?.userType || '',
    selfType: data?.selfType || '',
    quotaType: data?.quotaType || '',
    guaranteeType: data?.guaranteeType || null,
    guaranteePeriods: data?.guaranteePeriods || null,
    registerLicenceType: data?.registerLicenceType || null,
  };
  const otherInfo = {
    status: data?.status,
    productDesc: data?.productDesc,
  };
  const repayConfig = {
    id: data?.id,
    classification: data?.classification,
    costConfigDTO: data?.costConfigDTO,
    costParameterDTO: data?.costParameterDTO,
    leaseBailCostParameterDTO: data?.leaseBailCostParameterDTO,
    leaseInterestCostParameterDTO: data?.leaseInterestCostParameterDTO,
  };
  const inComeConfig = data?.activateDTO;
  useEffect(() => {
    setRepaymentTerm(data?.repaymentDTO?.repaymentTerms);
  }, [data]);
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <BasicInfo baseInfo={baseInfo} />
        <ProLeaseProcessConfig data={data} />
        {/* <RepaymentConfig data={repayConfig} /> */}
        {/* 自营展示费用配置表 1，自营，2非自营 */}
        {baseInfo.selfType === 1 && (
          <LeaseFeeConfig
            data={repayConfig}
            inComeConfig={inComeConfig}
            initialRepaymentTerm={data?.repaymentDTO?.repaymentTerms || []}
            repaymentTerm={repaymentTerm}
          />
        )}
        <OtherInfo otherInfo={otherInfo} />
      </PageContainer>
    </>
  );
};

export default ProductDetail;
