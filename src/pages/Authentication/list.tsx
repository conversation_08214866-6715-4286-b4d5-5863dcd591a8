import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import PageContainerHeaderTab from '@/components/PageContainerHeaderTab';
import { SECONDARY_CLASSIFICATION_CODE_LABEL } from '@/enums';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import { isCarInsuranceStoreUser } from '@/utils/utils';
import { ProFormInstance } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useAccess, useModel } from '@umijs/max';
import { Button } from 'antd';
import React, { memo, useRef, useState } from 'react';
import { getChannelInfo } from '../CarInsurance/services';
import AuthQrCodeModal from './components/AuthQrCodeModal';
import PostModal from './components/PostModal';
import type { IauthenticationItem } from './services';
import { asyncExportList, getList, statusMap } from './services';

const List = () => {
  const { setAuthList } = useModel('Authentication.authentication');
  const access = useAccess();
  //
  const { initialState = {} } = useModel('@@initialState') as any;
  const { currentUser = {} } = initialState;
  //
  const actionRef = useRef();
  const formRef = useRef<ProFormInstance>();
  //
  const init = async (params: any) => {
    const { current, pageSize } = params;
    const params1 = { page: current, size: pageSize, ...params };
    const result = await getList(
      removeBlankFromObject(filterProps(params1, ['current', 'pageSize'])),
    );
    const { data, total } = result;
    setAuthList(data);
    return {
      success: true,
      total,
      data: data.map((item: any) => {
        const {
          epAuthorizerAuthInfo: { authorizerName, authorizerCertiNo },
          epAuthInfo,
        } = item;
        return {
          ...epAuthInfo,
          authorizerCertiNo,
          authorizerName,
          //
          recordData: { ...item },
        };
      }),
    };
  };
  // 新增、编辑弹窗
  const [currentRecordOfEdit, setCurrentRecordOfEdit] = useState<any>({});
  //
  const [postModalVisible, setPostModalVisible] = useState(false);
  // 新增
  const openAddModal = () => {
    setCurrentRecordOfEdit(undefined);
    setPostModalVisible(true);
  };
  // 编辑，参数为列表项

  const openEditModal = (record: any) => {
    setCurrentRecordOfEdit(record);
    setPostModalVisible(true);
  };

  //
  const columns: ProColumns<IauthenticationItem>[] = [
    {
      title: '用户ID',
      dataIndex: 'epAuthNo',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondCode',
      search: false,
      formItemProps: {
        labelCol: { span: 8 },
      },
      valueType: 'select',
      initialValue: '0303', //车险
      valueEnum: SECONDARY_CLASSIFICATION_CODE_LABEL,
      fieldProps: {
        // 车险渠道用户只能选择车险产品
        disabled: isCarInsuranceStoreUser(access),
      },
    },
    {
      title: '企业名称',
      dataIndex: 'certiName',
    },
    {
      title: '社会统一信用代码',
      dataIndex: 'certiNo',
      // search: false,
      formItemProps: {
        labelCol: { span: 9 },
        wrapperCol: { span: 16 },
      },
    },
    {
      title: '所属渠道',
      dataIndex: 'channelNameList',
      search: false,
      width: 220,
      render: (text, record) => {
        const { recordData } = record as any;
        return recordData?.channelNameList?.join('，');
      },
    },
    {
      title: '授权人',
      dataIndex: 'authorizerName',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'authStatus',
      valueType: 'select',
      valueEnum: statusMap,
      render(text, record) {
        return (
          <>
            {text}
            {[1, 4].includes(record?.authStatus) ? (
              <AuthQrCodeModal epAuthNo={record.epAuthNo} certiName={record?.certiName}>
                <a>认证二维码</a>
              </AuthQrCodeModal>
            ) : null}
          </>
        );
      },
    },
    {
      title: '所属渠道',
      dataIndex: 'channelCode',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
      search: currentUser?.channelLevel === 2 ? false : true, // undefined 相当于 true  二级渠道不展示
      // 渠道用户只有一个渠道 不需要搜索
      request: async () => {
        const data = await getChannelInfo({
          channelLevel: currentUser?.channelLevel,
          channelCode: currentUser?.channelCode,
        });
        return data.map((item) => {
          const { channelName, channelCode } = item;
          return {
            label: channelName,
            value: channelCode,
            key: channelCode,
          };
        });
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      render(text, record) {
        return record?.createdAt;
      },
      search: {
        transform: (value: any) => {
          return {
            startCreatedAt: `${value?.[0]} 00:00:00`,
            endCreatedAt: `${value?.[1]} 23:59:59`,
          };
        },
      },
    },
    {
      title: '认证时间',
      dataIndex: 'epAuthDate',
      valueType: 'dateRange',
      render(text, record) {
        return record?.epAuthDate ?? '/';
      },
      // formItemProps: {
      //   label: '认证时间',
      // },
      search: {
        transform: (value: any) => {
          return {
            startEpAuthDate: `${value?.[0]} 00:00:00`,
            endEpAuthDate: `${value?.[1]} 23:59:59`,
          };
        },
      },
    },
    {
      title: '操作',
      dataIndex: 'options',
      search: false,
      render(_, record) {
        return (
          <>
            {access?.hasAccess('detail_verify_manager_enterpriseMng') && (
              <a
                onClick={() => {
                  // const { epAuthNo, authorizerCertiNo } = record;
                  console.log('record', record);
                  openEditModal(record?.recordData as any);
                  // history.push(
                  //   `/userMng/enterpriseMng/authentication/detail?epAuthNo=${epAuthNo}&authorizerCertiNo=${authorizerCertiNo}`,
                  // );
                }}
              >
                查看详情
              </a>
            )}
          </>
        );
      },
    },
  ];

  return (
    <PageContainerHeaderTab>
      <>
        <ProTable<IauthenticationItem>
          scroll={{ x: 'max-content' }}
          rowKey="id"
          actionRef={actionRef}
          request={init}
          columns={columns}
          formRef={formRef}
          toolBarRender={() => {
            return [
              // <Button
              //   key="button"
              //   type="primary"
              //   onClick={() => history.push(`/userMng/enterpriseMng/authentication/add`)}
              // >
              //   新增
              // </Button>,

              // ocr弹窗新增
              access?.hasAccess('add_verify_manager_enterpriseMng') && (
                <Button
                  key="button"
                  type="primary"
                  onClick={() => {
                    openAddModal();
                  }}
                >
                  新增
                </Button>
              ),
              // 导出
              access?.hasAccess('export_verify_manager_enterpriseMng') && (
                <AsyncExport
                  key="export"
                  getSearchDataTotal={async () => {
                    const values = formRef.current?.getFieldsFormatValue?.();
                    const params: any = removeBlankFromObject(
                      filterProps(
                        {
                          ...values,
                          page: 1,
                          size: 10,
                        },
                        ['current', 'pageSize'],
                      ),
                    );
                    console.log('params', params);
                    const res: any = await getList(params).catch(() => {});
                    console.log('res', res);
                    return res?.total;
                  }}
                  getSearchParams={() => {
                    const values = formRef.current?.getFieldsFormatValue?.();
                    const params: any = {
                      ...values,
                      page: 1,
                      size: 10,
                    };
                    return removeBlankFromObject(filterProps(params, ['current', 'pageSize']));
                  }}
                  exportAsync={asyncExportList}
                  taskCode={[ItaskCodeEnValueEnum.ENTERPRISE_AUTH_EXPORT]}
                />
              ),
            ];
          }}
        />
        {/* 弹窗 */}
        {postModalVisible && (
          <PostModal
            visible={postModalVisible}
            closeModal={() => {
              setPostModalVisible(false);
              setCurrentRecordOfEdit({});
            }}
            recordInfo={currentRecordOfEdit}
            refreshTable={() => {
              actionRef.current?.reload();
            }}
            currentUser={currentUser}
          />
        )}
        {/*  */}
      </>
    </PageContainerHeaderTab>
  );
};
export default memo(List);
