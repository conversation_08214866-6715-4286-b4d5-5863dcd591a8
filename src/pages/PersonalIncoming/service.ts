/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2025-06-05 15:37:50
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalIncoming/service.ts
 */
/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2021-02-26 11:04:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
import { bizadminHeader } from '@/services/consts';
import { desensitizationBankAndIdCard, isExternalDesensitization } from '@/utils/utils';
import { request } from '@umijs/max';
import type { IlogoffParams, UserInputRecordListParams } from './data';

// 获取进件记录列表
export async function getInputRecordList(params?: UserInputRecordListParams) {
  return request('/loan/into/user/list', {
    method: 'POST',
    data: { ...params },
    ifTrimParams: true,
  });
}

// 获取进件详情---基础信息
export async function getInputDetailBsae(orderNo: string) {
  return request(`/loan/riskOrder/baseInfo/${orderNo}`, {
    method: 'GET',
  });
}

// 获取进件详情---风控记录
export async function getInputDetailRecord(orderNo: string) {
  return request(`/loan/riskOrder/getRiskOrderCreditData/${orderNo}`, {
    method: 'GET',
  });
}

// 还款计划
// export async function getRepayPlan(orderNo: string) {
//   return request(`/repayment/cms/bill/repay/plan/${orderNo}`);
// }

// 进件订单详情
export async function getIntoDetail(orderNo: string) {
  return request(`/bizadmin/active/order/getIntoDetail/${orderNo}`, {
    headers: bizadminHeader,
    method: 'get',
  });
}

// 进件导出 incomeExport
export async function incomeExport(params: UserInputRecordListParams) {
  return request(`/loan/into/user/export`, {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
    sensitiveSwitch: true,
  });
}

//
export async function incomeDetail({
  orderNo,
  overdueCaseNo,
  access,
}: {
  orderNo?: string;
  overdueCaseNo?: string;
  access: any;
}) {
  const res = await request(`/risk/order/personal/cash/biz/personalInfo`, {
    method: 'post',
    data: { bizOrderNo: orderNo, overdueCaseNo },
  });
  // 脱敏判断
  if (isExternalDesensitization(access) && res?.data) {
    const {
      data: { relativeInfo = [], workInfo = [], baseInfo = [], imagingInfo = [] },
    } = res;
    baseInfo?.forEach((item: any) => {
      // if (['0103', '0104', '0105', '0106', "0124"].includes(item?.fieldCode)) {
      //   item.fieldValue = desensitizationPhone(item?.fieldValue);
      // }
      if (['0107', '0123'].includes(item?.fieldCode)) {
        item.fieldValue = desensitizationBankAndIdCard(item.fieldValue);
      }
      if (['0114', '0119'].includes(item?.fieldCode)) {
        item.fieldValue = '*';
      }
    });
    relativeInfo.forEach(() => {
      // if (['0403', '040301', '0406', '0409', '0412', '0415', '0451'].includes(item?.fieldCode)) {
      //   item.fieldValue = desensitizationPhone(item?.fieldValue);
      // }
    });
    workInfo.forEach(() => {
      // if (['0208', '040301', '0406', '0409', '0412', '0415', '0451'].includes(item?.fieldCode)) {
      //   item.fieldValue = desensitizationPhone(item?.fieldValue);
      // }
    });
    imagingInfo.forEach((item: any) => {
      item.fieldValue =
        'https://static.huolala.cn/image/9bd72ae3cdaf2dc24cb4ab4d0264c62945f87470.png';
    });

    // 其他全部为 *
  }
  return res;
}

//订单详情-订单明细列表
export async function getOrderDetail(params: {
  userId?: string;
  activateOrderNo?: string;
  current?: number;
  pageSize?: number;
}) {
  return request('/loan/into/user/getIntoDetail/orderDetail', {
    params,
  });
}

//订单详情-订单明细列表
export async function aiBankDetail({
  orderNo,
  overdueCaseNo,
}: {
  orderNo?: string;
  overdueCaseNo?: string;
}) {
  return request(`/bizadmin/active/order/prod/personalInfo`, {
    method: 'post',
    headers: bizadminHeader,
    data: { orderNo, overdueCaseNo },
  });
}

// 撤销订单
export async function revokeOrder(orderNo: string) {
  return request(`/loan/into/user/revoke/${orderNo}`, {
    method: 'POST',
    skipErrorHandler: true,
  });
}

// 查询是否可以注销
export async function isLogoff(userNo: string) {
  return request(`/bizadmin/cash/order/isLogoff`, {
    method: 'POST',
    headers: bizadminHeader,
    data: {
      userNo,
    },
  });
}

// 注销操作
export async function logoff(params: IlogoffParams) {
  return request(`/bizadmin/cash/order/logoff`, {
    method: 'POST',
    headers: bizadminHeader,
    data: params,
  });
}

// 注销记录
export async function logoffRecord(params: { page: number; size: number }) {
  return request(`/bizadmin/cash/order/query/logoff`, {
    method: 'POST',
    headers: bizadminHeader,
    data: params,
  });
}
