/*
 * @Author: your name
 * @Date: 2021-04-13 11:20:11
 * @LastEditTime: 2021-04-21 16:28:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/ModelLibrary/data.d.ts
 */

export interface AddPromotion {
  applyCityCode?: string;
  applyCityName?: string;
  carIdCode?: string;
  carType?: number;
  cityName?: string;
  createdAt?: string;
  discount?: number;
  id?: number;
  statusFlag?: number;
  updatedAt?: string;
}

export interface PromotionListParams {
  carModelCode?: string;
  carType?: number;
  cityName?: string;
  current?: number;
  pageSize?: number;
}

export interface CarListPromotionItem {
  applyCityCode?: string;
  applyCityName?: string;
  carIdCode?: string;
  carType?: number;
  cityName?: string;
  createdAt?: string;
  discount?: number;
  id?: number;
  statusFlag?: number;
  updatedAt?: string;
}
