/*
 * @Date: 2024-09-19 14:05:43
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-09-30 16:27:14
 * @FilePath: /lala-finance-biz-web/src/pages/MailList/index.tsx
 * @Description:
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { useAccess } from '@umijs/max';
import { Button, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import qs from 'qs';
import React, { useRef } from 'react';
import { KeepAlive } from 'react-activation';
import type { IbillListItem } from '../AfterLoan/carInsurance/types';
import './index.less';
import { mailExport, queryMailList } from './service';

const MailList: React.FC<any> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();

  const columns: ProColumns[] = [
    {
      title: 'ID',
      dataIndex: 'flowNo',
    },
    {
      title: '邮件场景',
      dataIndex: 'touchEvent',
      valueEnum: {
        2: '退保邮件',
        3: '逾期邮件',
        4: '还款日提醒邮件',
        5: '还款日前提醒邮件',
      },
    },
    {
      title: '收件人',
      dataIndex: 'receiver',
    },
    {
      title: '邮件内容',
      dataIndex: 'content',
      search: false,
      render: (_, record) => {
        let dom: React.ReactNode = '-';
        if (record?.content) {
          dom = (
            <Tooltip title={record?.content} placement="bottomRight">
              <div className="clamp-line"> {record?.content}</div>
            </Tooltip>
          );
        }
        return dom;
      },
    },
    {
      title: '发送时间',
      dataIndex: 'actualSendTime',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(1, 'month'), dayjs()],
      search: {
        transform: (value: any) => {
          return {
            actualSendTimeStart: `${value[0]} 00:00:00`,
            actualSendTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record?.actualSendTime || '-';
      },
    },
    {
      title: '发送状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '未发送' },
        1: { text: '发送成功' },
        '-1': { text: '发送失败' },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render(_, record) {
        try {
          const { attachmentUrl } = record;
          let Expires = dayjs().valueOf() / 1000 + '10';
          if (attachmentUrl) {
            const url = new URL(record.attachmentUrl);
            const query = qs.parse(url.search.slice(1));
            Expires = query.Expires + '';
          }
          return (
            <Button
              download
              href={record.attachmentUrl}
              type="link"
              disabled={dayjs().valueOf() / 1000 > Number(Expires)}
            >
              下载附件
            </Button>
          );
        } catch (error) {
          return <Tag color="error">ossPath有误</Tag>;
        }
      },
    },
  ];
  async function getSearchDataTotal() {
    const searchParams = formRef?.current?.getFieldsFormatValue?.();
    const { flowNo, touchEvent } = searchParams || {};

    const params: IbillListItem = {
      ...searchParams,
      flowNo: flowNo ? [flowNo] : undefined,
      touchEvent: touchEvent ? [touchEvent] : undefined,
    };
    const data = await queryMailList(removeBlankFromObject(filterProps(params)));
    return data?.total;
  }
  return (
    <>
      <PageContainer className={globalStyle.mt16}>
        <ProTable
          actionRef={actionRef}
          formRef={formRef}
          rowKey="flowNo"
          scroll={{ x: 'max-content' }}
          request={(params) => {
            const { flowNo, touchEvent } = params || {};
            return queryMailList({
              ...params,
              flowNo: flowNo ? [flowNo] : undefined,
              touchEvent: touchEvent ? [touchEvent] : undefined,
            });
          }}
          search={{
            labelWidth: 100,
            defaultCollapsed: false,
          }}
          columns={columns}
          toolBarRender={() => {
            return [
              access.hasAccess('biz_businessMng_postLoanMng_mailList_export') && (
                <AsyncExport
                  getSearchDataTotal={getSearchDataTotal}
                  getSearchParams={() => {
                    const values = formRef?.current?.getFieldsFormatValue?.(true);
                    const { flowNo, touchEvent } = values || {};
                    const params: IbillListItem = {
                      ...values,
                      flowNo: flowNo ? [flowNo] : undefined,
                      touchEvent: touchEvent ? [touchEvent] : undefined,
                    };
                    return removeBlankFromObject(filterProps(params));
                  }}
                  exportAsync={mailExport}
                  taskCode={[ItaskCodeEnValueEnum.SEND_MAIL_RECORD]}
                />
              ),
            ];
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'/businessMng/postLoanMng/mailList'}>
      <MailList />
    </KeepAlive>
  </>
);
