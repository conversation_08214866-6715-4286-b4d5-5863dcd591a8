import { Button, ButtonProps } from 'antd';
import React, { useState } from 'react';

const LoadingButton: React.FC<ButtonProps> = (props) => {
  const { onClick, children, ...rest } = props;
  const [loading, setLoading] = useState(false);
  return (
    <Button
      onClick={async (e) => {
        try {
          setLoading(true);
          await onClick?.(e);
        } catch (error) {
          // 这里需要原封不动将错误交给外面处理
          // 否则外面将catch不到
          return Promise.reject(error);
        } finally {
          setLoading(false);
        }
      }}
      loading={loading}
      {...rest}
    >
      {children}
    </Button>
  );
};

export default LoadingButton;
