import { EcarInsuranceStatus } from '@/utils/bankend/enum';
import { getBlob, saveAs } from '@/utils/utils';
import ProTable from '@ant-design/pro-table';
import { history, useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Descriptions } from 'antd';
import React from 'react';
import { getCarRepayment } from '../services';

const LoanInfo = () => {
  const { detailData } = useModel('CarInsurance.carInsurance');
  const { orderNo } = (history as any).location.query;
  // 解构时 赋值初始值只能是undefined 时生效 null是不生效的
  const { contractInfo, repayInfo, productInfo, orderStatus } = detailData;
  const { netWorkPath, fileDesc } = contractInfo || {};
  const download = (url: string, filename: string = '合同') => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
  };

  const loanInfo = {
    ...(repayInfo || {}),
    ...(productInfo || {}),
    repaymentMode: '等额本息',
  };
  // 还款计划
  const { data: repayment } = useRequest(async () => {
    const data = await getCarRepayment(orderNo);
    const { listRspList, repayTerm } = data;
    const repayment1 = {
      ...listRspList?.[0],
      children: listRspList?.filter((item) => item.id),
    };
    return repayTerm ? [repayment1] : [];
  });
  const repaymentColums = [
    { dataIndex: 'termDetail', title: '期限' },
    { dataIndex: 'amountDue', title: '应还总额' },
    { dataIndex: 'principal', title: '应还本金' },
    { dataIndex: 'interest', title: '应还利息' },
    {
      dataIndex: 'repayTime',
      title: '应还日期',
      hideInTable: orderStatus === EcarInsuranceStatus.LOAN_PENDING,
    },
  ];

  const loanInfoColumns = [
    { label: '还款期限', value: 'term' },
    { label: '年利率', value: 'interestRate' },
    { label: '还款方式', value: 'repaymentMode' },
    { label: '还款总额', value: 'repayAmount' },
    { label: '借款总额', value: 'loanAmount' },
    { label: '利息总额', value: 'interestAmount' },
    { label: '实际首付', value: 'actualDownPaymentAmount' },
  ];
  return (
    <>
      <h3>借款信息</h3>
      <Descriptions>
        {loanInfoColumns.map((item) => {
          const { label, value } = item;
          return <Descriptions.Item label={label}>{loanInfo[value] || '-'}</Descriptions.Item>;
        })}
        <Descriptions.Item label={'合同'}>
          {netWorkPath ? (
            <div>
              <a style={{ marginRight: 10 }} href={netWorkPath} target="_blank" rel="noreferrer">
                {fileDesc}
              </a>
              <a
                onClick={() => {
                  download(netWorkPath, fileDesc);
                }}
              >
                下载
              </a>
            </div>
          ) : (
            '-'
          )}
        </Descriptions.Item>
        <Descriptions.Item label={'还款计划'}>
          {repayment?.length ? (
            <ProTable
              search={false}
              columns={repaymentColums}
              dataSource={repayment}
              pagination={false}
              toolbar={{ settings: [] }}
              size="small"
            />
          ) : (
            '-'
          )}
        </Descriptions.Item>
      </Descriptions>
    </>
  );
};

export default LoanInfo;
