// 新增回购黑名单
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { getAllChannelNameEnum } from '@/services/enum';
import {
  ModalForm,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import React, { useMemo, useState } from 'react';
import { editBuyBlackList } from '../service';

type ItemType = {
  userId?: string;
  channel?: string;
};

type FormValues = {
  configId?: string;
  type: string;
  item: ItemType[] | [];
  productCode: string;
  productType: string;
  userId?: string[];
  channel?: string[];
};
type AddBuyRuleModalProps = {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
};

enum BLACK_LIST_TYPE {
  USER = '1',
  CHANNEL = '2',
}

const AddBuyBlackModal: React.FC<AddBuyRuleModalProps> = (props) => {
  const [loading, setLoading] = useState(false);

  const getAllChannelNameEnumMemo = useMemo(async () => {
    return await getAllChannelNameEnum();
  }, []);
  const handleFinish = async (values: FormValues) => {
    const { productCode, type, userId, channel } = values;
    const productType = productCode.substring(0, 2);
    let item: any = [];
    if (type === BLACK_LIST_TYPE.USER) {
      item = userId?.map((val) => {
        return {
          userId: val,
        };
      });
    } else if (type === BLACK_LIST_TYPE.CHANNEL) {
      item = channel?.map((val) => {
        return {
          channel: val,
        };
      });
    }
    const params = {
      type,
      productType,
      productCode,
      item,
    };
    setLoading(true);
    try {
      await editBuyBlackList(params);
      props.onSuccess();
      setLoading(false);
      return true;
    } catch {
      setLoading(false);
      // 报错的时候关闭弹窗刷新下页面
      props.onSuccess();
      return true;
    }
  };

  return (
    <ModalForm<FormValues>
      title="加入回购黑名单"
      open={props.open}
      layout="horizontal"
      modalProps={{
        destroyOnClose: true,
        onCancel: props.onCancel,
        confirmLoading: loading,
      }}
      labelCol={{ span: 4 }}
      onFinish={handleFinish}
    >
      <ProFormText name="configId" hidden />
      <ProFormSelect
        required
        width="md"
        options={[
          {
            value: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
            label: '融资租赁',
          },
        ]}
        name="productCode"
        label="合作业务线"
        rules={[{ required: true, message: '请选择合作业务线' }]}
      />
      <ProFormSelect
        required
        width="md"
        options={[
          {
            value: BLACK_LIST_TYPE.USER,
            label: '用户维度',
          },
          {
            value: BLACK_LIST_TYPE.CHANNEL,
            label: '渠道维度',
          },
        ]}
        name="type"
        label="黑名单维度"
        rules={[{ required: true, message: '请选择黑名单维度' }]}
      />
      <ProFormDependency name={['type']}>
        {(values) => {
          const { type } = values;
          if (type === BLACK_LIST_TYPE.USER) {
            return (
              <ProFormSelect
                name="userId"
                label="用户ID"
                width="md"
                required
                fieldProps={{
                  mode: 'tags',
                  maxCount: 5,
                }}
                placeholder="请输入"
                rules={[{ required: true, message: '请输入用户ID' }]}
              />
            );
          }
          if (type === BLACK_LIST_TYPE.CHANNEL) {
            return (
              <ProFormSelect
                name="channel"
                label="融租渠道"
                width="md"
                required
                request={() => getAllChannelNameEnumMemo}
                fieldProps={{
                  mode: 'multiple',
                }}
                rules={[{ required: true, message: '请选择融租渠道' }]}
              />
            );
          }
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default AddBuyBlackModal;
