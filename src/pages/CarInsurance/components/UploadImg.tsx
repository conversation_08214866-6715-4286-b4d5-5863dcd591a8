import { getAuthHeaders } from '@/utils/auth';
import { PlusOutlined } from '@ant-design/icons';
import { FormInstance, Modal, Upload } from 'antd';
import type { UploadProps } from 'antd/es/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import type { RcFile } from 'antd/lib/upload';
import React, { useState } from 'react';
import { getBaseUploadAction } from '../utils';

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
type Props = {
  value?: string; //
  onChange?: (val: string) => void;
  form?: FormInstance;
  name?: string; // setFieldsValue的字段
};
const UploadImg: React.FC<Props> = ({ value, onChange, form, name }) => {
  const [fileList, setFileList] = useState<UploadFile[]>(
    value
      ? [
          {
            uid: '-1',
            name: 'image.png',
            status: 'done',
            url: value,
          },
        ]
      : [],
  );
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const handleChange: UploadProps['onChange'] = ({ file, fileList: newFileList }) => {
    setFileList(newFileList);
    if (file?.status === 'done') {
      const { response = {} } = file;
      const { code, data = {} } = response;
      if (code === 200) {
        const { filePath, netWorkPath } = data;
        onChange!(netWorkPath);
        if (name) form?.setFieldsValue({ [name]: filePath });
      }
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!?.substring(file.url!.lastIndexOf('/') + 1));
  };
  return (
    <>
      <Upload
        action={getBaseUploadAction('/base/oss/common/uploadfile')}
        listType="picture-card"
        fileList={fileList}
        onPreview={handlePreview}
        data={{
          attachment: false,
          acl: 'PUBLIC_READ',
          destPath: 'insurance/policy/vehicleLicense/',
        }}
        headers={{ ...getAuthHeaders() }}
        name="file"
        maxCount={1}
        onChange={handleChange}
      >
        {fileList.length >= 1 ? null : uploadButton}
      </Upload>
      <Modal
        open={previewOpen}
        title={previewTitle}
        footer={null}
        onCancel={() => {
          setPreviewOpen(false);
        }}
      >
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </>
  );
};

export default UploadImg;
