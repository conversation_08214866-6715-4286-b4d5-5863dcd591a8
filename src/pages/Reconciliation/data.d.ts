/*
 * @Date: 2023-07-17 11:32:26
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-07-25 09:45:10
 * @FilePath: /lala-finance-biz-web/src/pages/Reconciliation/data.d.ts
 * @Description:
 */
export interface ReconciliationDetailParams {
  accountNumber?: string;
  accountName?: string;
  bankNumber?: string;
  bankName?: string;
  receiveCardNo?: string;
  bankSerialNo?: string;
  repayTimeStart?: string;
  repayTimeEnd?: string;
  reconciliationFlowNo?: string;
  reconciliationStatus?: string;
  pageSize?: number;
  current?: number;
}

export interface ReconciliationDetailList {
  reconciliationStatus?: number;
  reconciliationFlowNo?: string;
  repayTime?: string;
  bankSerialNo?: string;
  receiveCardNo?: string;
  bankNumber?: string;
  actualRepaymentAmount?: number;

  accountBank?: string;
  bankName?: string;
  bills?: string;
  accountName?: string;
  accountNumber?: string;
  uid?: string;
}

export interface ReconciliationRecordParams {
  accountNumber?: string;
  accountName?: string;
  bankNumber?: string;
  bankName?: string;
  receiveCardNo?: string;
  bankSerialNo?: string;
  repayTimeStart?: string;
  repayTimeEnd?: string;
  reconciliationFlowNo?: string;
  reconciliationStatus?: string;
  pageSize?: number;
  current?: number;
  remitAmount?: number;
}

export interface ReconciliationRecordList {
  reconciliationStatus?: number;
  reconciliationFlowNo?: string;
  repayTime?: string;
  bankSerialNo?: string;
  receiveCardNo?: string;
  bankNumber?: string;
  actualRepaymentAmount?: number;
  accountBank?: string;
  bankName?: string;
  bills?: string;
  accountName?: string;
  accountNumber?: string;
  uid?: string;
  remitAmount?: number;
  createdAt?: string;
  receiveStatus?: number;
  receiveTime?: string;
}

export interface ConfirmParams {
  reconciliationFlowNo?: string;
  receiveBankNo?: string;
  receiveTime?: string;
}
