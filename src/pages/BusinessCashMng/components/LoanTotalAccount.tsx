// 账单管理 - 圆易借 - 订单总账
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
// import { getProductNameEnum } from '@/services/enum';
// import { disableFutureDate } from '@/utils/utils';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
// import { history } from '@umijs/max';
// import { message } from 'antd';
import React, { memo, useEffect, useRef, useState } from 'react';
// import type { OrderListItem } from '../data';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import dayjs from 'dayjs';
// import { FUNDER_CHANNEL_MAP } from '../const';
import { VList } from 'virtuallist-antd';
import { billList } from '../service';
import type { NewIbillListItem, NewIbillListParams } from '../types';
import { IdimensionEnCode, statusMap } from '../types';

const LoanTotalAccount: React.FC = () => {
  // const [productCodeValueEnum, setProductCodeValueEnum] = useState<Record<string, string>>({});
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const secondaryClassification = SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY;

  const [allSelectedRows, setAllSelectedRows] = useState<NewIbillListItem[]>([]);
  const [allSelectedRowKeys, setAllSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<string, NewIbillListItem[]>>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<Record<string, string[]>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [isBigData, setIsBigData] = useState(false); // 是否是大数据量 大数据量下会卡顿 所以启用虚拟列表

  const columns: ProColumns<NewIbillListItem>[] = [
    {
      title: '小贷账单ID',
      dataIndex: 'billNo',
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
      key: 'userName',
      hideInTable: true,
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
      key: 'accountName',
      search: false,
    },
    // {
    //   title: '产品名称',
    //   dataIndex: 'productCode',
    //   valueEnum: () => {
    //     const secondTypeCode = formRef.current?.getFieldValue('secondTypeCode');

    //     // 由于产品是多选，所以不做反显二级分类
    //     const codeMap = {};
    //     for (const value in productCodeValueEnum) {
    //       if (value.substring(0, 4) === secondTypeCode) {
    //         // 圆易借
    //         codeMap[value] = productCodeValueEnum[value];
    //       }
    //     }
    //     // 二级不存在 则展示全部
    //     return secondTypeCode ? codeMap : productCodeValueEnum;
    //   },
    //   valueType: 'select',
    //   hideInTable: true,
    //   fieldProps: {
    //     showSearch: true,
    //     mode: 'multiple',
    //     showArrow: true,
    //     optionFilterProp: 'label',
    //     filterOption: (input: string, option: { label: string }) =>
    //       option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
    //   },
    // },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    // {
    //   title: '资金渠道',
    //   dataIndex: 'funderChannelCodeName',
    //   valueEnum: FUNDER_CHANNEL_MAP,
    //   hideInTable: true,
    // },
    {
      title: '资金渠道',
      dataIndex: 'funderChannelCodeName',
      search: false,
    },
    {
      title: '账单状态',
      dataIndex: 'statusName',
      search: false,
    },
    {
      title: '账单状态',
      dataIndex: 'statusList',
      key: 'statusList',
      hideInTable: true,
      valueType: 'select',
      valueEnum: statusMap,
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '已还期数',
      dataIndex: 'settleTermNumber',
      search: false,
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDateNumber',
      search: false,
    },
    {
      title: '未还总额（元）',
      dataIndex: 'totalAmountUnpaid',
      search: false,
    },
    {
      title: '未还本金（元）',
      dataIndex: 'totalPrincipalUnpaid',
      search: false,
    },
    {
      title: '未还利息（元）',
      dataIndex: 'totalInterestUnpaid',
      search: false,
    },
    {
      title: '未还罚息（元）',
      dataIndex: 'totalOverduePenaltyUnpaid',
      search: false,
    },
    {
      title: '已还总额（元）',
      dataIndex: 'totalAmountPaid',
      search: false,
    },
    {
      title: '已还本金（元）',
      dataIndex: 'totalPrincipalPaid',
      search: false,
    },
    {
      title: '已还利息（元）',
      dataIndex: 'totalInterestPaid',
      search: false,
    },
    {
      title: '已还罚息（元）',
      dataIndex: 'totalOverduePenaltyPaid',
      search: false,
    },
    {
      title: '未还滞纳金（元）',
      dataIndex: 'totalLate',
      search: false,
    },
    {
      title: '应还滞纳金（元）',
      dataIndex: 'totalLateDue',
      search: false,
    },
    {
      title: '应结清日期',
      dataIndex: 'dueDate',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(2, 'month'), dayjs().add(1, 'month')],
      search: {
        transform: (value: any) => {
          return {
            dueDateStart: `${value[0]} 00:00:00`,
            dueDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.dueDate || '-';
      },
    },
    {
      title: '实际结清日期',
      dataIndex: 'clearTime',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            clearTimeStart: `${value[0]} 00:00:00`,
            clearTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.clearTime || '-';
      },
    },
    {
      title: '入账日期',
      dataIndex: 'updatedAt',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            updateAtStart: `${value[0]} 00:00:00`,
            updateAtEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.updatedAt || '-';
      },
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            lendingTimeStart: `${value[0]} 00:00:00`,
            lendingTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.lendingTime || '-';
      },
    },
  ];

  useEffect(() => {
    setAllSelectedRows(
      Array.from(
        new Map(
          Object.values(selectedRows)
            .flat(2)
            ?.map((item) => [item.id, item]),
        ).values(),
      ) as any,
    );
    setAllSelectedRowKeys([...new Set(Object.values(selectedRowKeys).flat(2))] as any);
  }, [selectedRows, selectedRowKeys]);

  // useEffect(() => {
  //   getProductNameEnum(PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN).then((data) => {
  //     setProductCodeValueEnum(
  //       data.reduce((pre, cur) => {
  //         if (
  //           [
  //             SECONDARY_CLASSIFICATION_CODE.MERCHANT,
  //             SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY,
  //           ].includes(cur.value.substring(0, 4) as any)
  //         ) {
  //           // 只有圆易借 和 圆商贷 弄进来
  //           return {
  //             ...pre,
  //             [cur.value]: cur.label,
  //           };
  //         } else {
  //           return {
  //             ...pre,
  //           };
  //         }
  //       }, {}),
  //     );
  //   });
  // }, []);

  return (
    <ProTable<NewIbillListItem>
      actionRef={actionRef}
      formRef={formRef}
      rowKey="orderNo"
      scroll={isBigData ? { y: window.innerHeight - 224 } : { x: 'max-content' }}
      components={
        isBigData
          ? VList({
              height: window.innerHeight - 224,
            })
          : null
      }
      request={async (values) => {
        const { current = 1, pageSize = 20, billNo, termList } = values;
        const params: NewIbillListParams = {
          ...values,
          dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
          current,
          pageSize,
          secondaryClassification,
          billNoList: billNo ? [billNo] : undefined,
          termList: termList ? [termList] : undefined,
        };
        return billList(removeBlankFromObject(filterProps(params)));
      }}
      search={{
        labelWidth: 90,
        defaultCollapsed: false,
      }}
      pagination={{
        showSizeChanger: true,
        onShowSizeChange: (cPage, pageSize) => {
          setCurrentPage(cPage);
          if (pageSize >= 500) {
            setIsBigData(true);
          } else {
            setIsBigData(false);
          }
        },
      }}
      rowSelection={{
        selectedRowKeys: allSelectedRowKeys,
        onChange: (_selectedRowKeys, _selectedRowsArg) => {
          setSelectedRowKeys({ ...selectedRowKeys, [currentPage]: _selectedRowKeys });
          setSelectedRows({ ...selectedRows, [currentPage]: _selectedRowsArg });
        },
      }}
      columns={columns}
      toolBarRender={() => {
        return [
          // <AsyncExport
          //   key="loan-period-account"
          //   getSearchDataTotal={async () => {
          //     const values = formRef.current?.getFieldsFormatValue?.();
          //     const { billNo, termList } = values;
          //     const params: NewIbillListParams = {
          //       ...values,
          //       billNoList: billNo ? [billNo] : undefined,
          //       termList: termList ? [termList] : undefined,
          //       dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
          //       current: 1,
          //       pageSize: 1,
          //       secondaryClassification,
          //     };
          //     const data = await billList(removeBlankFromObject(filterProps(params)));
          //     return data?.total;
          //   }}
          //   getSearchParams={() => {
          //     const values = formRef.current?.getFieldsFormatValue?.();
          //     const { current = 1, pageSize = 20, billNo, termList } = values;
          //     const params: NewIbillListParams = {
          //       ...values,
          //       billNoList: billNo ? [billNo] : undefined,
          //       termList: termList ? [termList] : undefined,
          //       dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
          //       current,
          //       pageSize,
          //       secondaryClassification,
          //     };
          //     return removeBlankFromObject(filterProps(params));
          //   }}
          //   getSelectedParams={() => {
          //     const values = formRef.current?.getFieldsFormatValue?.();
          //     return {
          //       billNoList: allSelectedRows.map((item) => item.billNo),
          //       dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
          //       secondaryClassification,
          //       channelCode: values?.channelCode,
          //     };
          //   }}
          //   getSelectedTotal={() => {
          //     return allSelectedRows.length;
          //   }}
          //   exportAsync={billExport}
          //   taskCode={[ItaskCodeEnValueEnum.REPAY_LEASE_BILL]}
          // />,
        ];
      }}
    />
  );
};

export default memo(LoanTotalAccount);
