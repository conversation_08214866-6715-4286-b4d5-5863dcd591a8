/** 上传行驶证 */
import type { FormInstance } from 'antd';
import React, { memo } from 'react';
import { STEP_FILE_TYPE } from '../../type';
import CarOcrContent from './CarOcrContent';

interface UploadDriverLicenseIF {
  setLoading: (val: boolean) => void;
  onUploadStatusChange?: (hasUploading: boolean) => void;
  onErrorStatusChange?: (hasError: boolean) => void;
  form: FormInstance;
  otdName: string;
}

const UploadDriverLicense: React.FC<UploadDriverLicenseIF> = (props) => {
  const { setLoading, onUploadStatusChange, onErrorStatusChange, form, otdName } = props;
  return (
    <CarOcrContent
      name="driverlicense"
      otdName={otdName}
      fileType={STEP_FILE_TYPE.LICENSE}
      form={form}
      variant="license"
      setLoading={setLoading}
      onUploadStatusChange={onUploadStatusChange}
      onErrorStatusChange={onErrorStatusChange}
    />
  );
};

export default memo(UploadDriverLicense);
