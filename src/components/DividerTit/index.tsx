/*
 * @Author: your name
 * @Date: 2021-04-20 15:18:11
 * @LastEditTime: 2021-09-18 14:04:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/components/DividerTit/index.tsx
 */

import { Divider } from 'antd';
import React from 'react';

const styleInline = {
  divider: { borderLeft: '3px solid #1677ff', height: '1.5em' },
  tit: {
    display: 'inline-block',
    marginTop: 25,
    marginRight: 10,
    fontWeight: 500,
  },
};
interface DividerTitProp {
  title: React.ReactNode;
  style?: any;
}
const DividerTit: React.FC<DividerTitProp> = (props) => {
  const { title, style } = props;
  return (
    <>
      <Divider type="vertical" style={styleInline.divider} />
      <div style={{ ...styleInline.tit, ...style }}>{title}</div>
      {props.children}
    </>
  );
};
export default DividerTit;
