import HeaderTab from '@/components/HeaderTab/index';
import { PRODUCT_CLASSIFICATION_CODE } from '@/enums';
import { getProductNameEnum } from '@/services/enum';
import { disableFutureDate, downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import { Button } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import type { OrderListItem, OrderListParams } from './data';
import { orderExport, queryOrder } from './service';
// // 解决日期范围选择器默认值报错
dayjs.extend(weekday);
dayjs.extend(localeData);

const OrderList: React.FC<any> = () => {
  const currentDate = dayjs();
  const defaultStartTime = dayjs().subtract(1, 'month');
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [exportLoading, setExportLoading] = useState(false);
  const getExport = (form: OrderListParams) => {
    setExportLoading(true);
    orderExport(form)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  // 默认日期选择器快捷选项
  const defaultTimeRangeShortCut = {
    最近半年: [currentDate.subtract(6, 'month'), currentDate],
    最近一年: [currentDate.subtract(1, 'year'), currentDate],
    最近两年: [currentDate.subtract(2, 'year'), currentDate],
  };
  const columns: ProColumns<OrderListItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      render: (_, record) => (
        <>
          <Link to={`/userMng/enterpriseMng/com-detail?userNo=${record.userNo}`}>
            {record?.userNo}
          </Link>
        </>
      ),
      // search: false,
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
    },
    {
      title: '手机号',
      width: 120,
      dataIndex: 'phone',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      valueType: 'select',
      request: () => getProductNameEnum(PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING),
      hideInTable: true,
    },
    {
      title: '申请金额',
      dataIndex: 'orderAmount',
      search: false,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '待放款' },
        1: { text: '待还款' },
        2: { text: '提前结清' },
        3: { text: '结清' },
        4: { text: '逾期' },
        5: { text: '逾期结清' },
        6: { text: '坏账' },
      },
    },
    {
      title: '进件时间',
      dataIndex: 'orderTime',
      initialValue: [defaultStartTime, currentDate],
      valueType: 'dateRange',
      fieldProps: {
        ranges: defaultTimeRangeShortCut,
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.orderTime || '-';
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => {
          if (typeof value[0] !== 'string') {
            return {
              startApplyTime: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
              endApplyTime: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
            };
          }
          return {
            startApplyTime: `${value[0].split(' ')[0]} 00:00:00`,
            endApplyTime: `${value[1].split(' ')[0]} 23:59:59`,
          };
        },
      },
    },
    {
      title: '渠道订单号',
      dataIndex: 'orderDisplayId',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <>
          <Link to={`/businessMng/detail?orderNo=${record.orderNo}`}>查看详情</Link>
        </>
      ),
    },
  ];

  return (
    <>
      <PageContainer>
        <ProTable<OrderListItem>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="orderNo"
          scroll={{ x: 'max-content' }}
          request={(params) => {
            return queryOrder(params).then((res) => {
              const { startApplyTime, endApplyTime } = res?.queryParam || {};
              if (startApplyTime && endApplyTime) {
                // 回显表单的时间
                formRef?.current?.setFieldsValue({
                  orderTime: [dayjs(startApplyTime), dayjs(endApplyTime)],
                });
              } else {
                // 清空表单的时间
                formRef?.current?.setFieldsValue({
                  orderTime: undefined,
                });
              }
              return res;
            });
          }}
          search={{
            labelWidth: 90,
          }}
          columns={columns}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                loading={exportLoading}
                onClick={() => {
                  const { orderTime, ...data } = formRef?.current?.getFieldsValue();
                  let newForm = { ...data };
                  if (orderTime?.length) {
                    const startApplyTime = `${orderTime[0].format('YYYY-MM-DD')} 00:00:00`;
                    const endApplyTime = `${orderTime[1].format('YYYY-MM-DD')} 23:59:59`;
                    newForm = { ...data, startApplyTime, endApplyTime };
                  }
                  getExport(newForm);
                }}
              >
                导出
              </Button>,
            ];
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'businessMng/list'}>
      <OrderList />
    </KeepAlive>
  </>
);
