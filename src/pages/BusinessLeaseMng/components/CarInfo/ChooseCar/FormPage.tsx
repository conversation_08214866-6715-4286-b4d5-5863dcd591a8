/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
// 选择车辆
import { INCOME_STATUS, ORDER_STATUS } from '@/pages/BusinessLeaseMng/consts';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Empty, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { getCarInfoByIdV2 } from '../../../service';
import { CAR, NEW_TREE_NAME, OLD_TREE_NAME } from './data';
import type { CarNode, FormPageProps, ItemPropsType } from './types';
import { formatData } from './utils';

const DisplayItem: React.FC<ItemPropsType> = (props) => {
  const {
    type,
    name,
    label,
    extra,
    readOnly,
    isView,
    disableView,
    options = [],
    onInputChange,
    onInputBlur,
    onPickerChange,
  } = props;

  let node = null;

  switch (type) {
    case 'input':
      node = (
        <ProFormText
          name={name}
          label={label}
          readonly={readOnly}
          fieldProps={{
            onBlur: (e) => {
              onInputBlur(e.target.value);
            },
            // onChange: (e) => {
            //   console.log(e);
            //   onInputChange(e.target.value);
            // },
          }}
          extra={extra}
          rules={[{ required: true }]}
          allowClear
        />
      );
      break;
    case 'select':
      node = (
        <ProFormSelect
          name={name}
          label={label}
          readonly={readOnly}
          disabled={!options?.length}
          options={options}
          fieldProps={{ onChange: onPickerChange }}
          rules={[{ required: true }]}
        />
      );
  }

  if (isView && !disableView) {
    node = <ProFormText name={name} label={label} disabled />;
  }

  return node;
};

let formCache: Record<string, any> = {};
// 内部记录的车辆列表
let innerCarList: CarNode[] = [];

const FormPage: React.FC<FormPageProps> = (props) => {
  const {
    form,
    readOnly,
    viewMode,
    orderNo,
    carList,
    formData,
    carType,
    channelId,
    formTree,
    preCarCode,
    preCarType,
    orderStatus,
    incomeStatus,
    onCarCodeConfirm,
  } = props;
  console.log(preCarCode, preCarType);
  // const [form] = Form.useForm();

  const [pickerData, setPickerData] = useState<Record<string, any>>({});

  const [defaultPickerData, setDefaultPickerData] = useState<Record<string, any>>({});

  const [isView, setIsView] = useState(viewMode); // 车型码查询时用查看模式

  const [extras, setExtras] = useState<Record<string, any>>({});
  const resetFields = () => {
    const resetList = carType === CAR.NEW ? NEW_TREE_NAME : OLD_TREE_NAME;
    form.resetFields(resetList);
  };

  const initPickerData = () => {
    const obj = {};
    const values = form.getFieldsValue();
    let preList = [];
    formTree.forEach((key, index) => {
      if (index === 0) {
        const firstList = formatData(carList);
        obj[key] = firstList;
        setDefaultPickerData(firstList);
        preList = carList;
      }
      if (values[key]) {
        const nextKey = formTree[index + 1];
        if (nextKey) {
          const nextNode = preList.find((item) => item.code === String(values[key]));
          const nextList = nextNode?.nodeList;
          if (nextList && nextList.length) {
            obj[nextKey] = formatData(nextList);
            preList = nextList;
          }
        }
      }
    });

    setPickerData({ ...obj });
  };

  useEffect(() => {
    if (!form.getFieldValue('carCode')) {
      setIsView(false);
    }
    // console.log(carList, 'carList');
    if (!carList.length) {
      resetFields();
      formCache = {};
      setPickerData({});
      setDefaultPickerData({});
    } else {
      // const data = formatData(carList);
      if (!Object.keys(pickerData).length) {
        initPickerData();
        // setPickerData({ ...{ [formTree[0]]: data } });
        // setDefaultPickerData(data);
      }
    }

    innerCarList = carList;

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [carList]);

  useEffect(() => {
    setIsView(viewMode);
  }, [viewMode]);

  // 渠道变化时要清空数据
  useEffect(() => {
    form.resetFields();
    formCache = {};
    setPickerData([]);
    setDefaultPickerData([]);
    setExtras({});
    setIsView(false);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [channelId]);

  const clearFormData = () => {
    setExtras({});
    if (carType === CAR.NEW) {
      form.resetFields(NEW_TREE_NAME);
    } else {
      form.resetFields(OLD_TREE_NAME);
    }
    setPickerData(() => {
      return { [formTree[0]]: defaultPickerData };
    });
    formCache = {};
  };
  // 选择数据变化，更新下一层选项值
  const handlePickerChange = (val: string, name: string) => {
    if (val === formCache[name]) return;
    const curLevel: number = formTree.indexOf(name);
    const nextLevel: number = curLevel + 1;
    let index = 0;
    let node = null;
    let curNodeList = innerCarList;
    // console.log('innerCarList', innerCarList)
    // 清除当前层级往后的所有层级数据
    const clearData: any = {};
    const clearNames: string[] = formTree.slice(nextLevel);
    for (let i = nextLevel; i < formTree.length; i++) {
      const name = formTree[i];
      clearData[name] = null;
    }
    // 清除表单数据
    form.resetFields([...clearNames]);

    // 清除表单记录数据
    formCache = {
      ...formCache,
      [name]: val,
      ...clearData,
    };

    while (index < nextLevel) {
      const name = formTree[index];
      const value = form.getFieldValue(name) || val;
      const curIndex = curNodeList.findIndex((item: any) => item.code === String(value));
      if (curIndex !== -1) {
        node = curNodeList[curIndex].nodeList;
        curNodeList = node;
        //判断是否选择最后一层，获取车型码
        if (curLevel === formTree.length - 1) {
          const curNode = curNodeList.find((item) => item.code === String(val));
          const carCode = curNode && curNode.carCode;
          const carNo = curNode && curNode.carNo;
          if (carCode) {
            setIsView(true);
            form.setFieldsValue({ carCode, carNo });
            if (
              carCode !== preCarCode &&
              (orderStatus === ORDER_STATUS.PRE_AUDIT_PASS ||
                incomeStatus === INCOME_STATUS.RESELECT_VEHICLE) &&
              carType == preCarType
            ) {
              // console.log('031', carCode);
              //车型码变更
              setExtras({ ...extras, carCode: '您选择的车型已变更，请留意最新的预授信额度' });
            } else {
              //置空carCode
              setExtras({ ...extras, carCode: '' });
            }
            // 确认车型码后通知父组件
            onCarCodeConfirm({ carCode: carCode || '' });
          }
        }
      } else {
        node = null;
      }
      index++;
    }

    if (node && node.length) {
      const formatNode = formatData(node);

      setPickerData((data) => {
        return {
          ...data,
          ...clearData,
          [formTree[nextLevel]]: formatNode,
        };
      });

      if (formatNode.length === 1) {
        // 如果只有一个选项，则直接选中
        const name = formTree[nextLevel];
        const value = formatNode[0].value;

        form.setFieldsValue({ [name]: value });

        // 继续检查下一层
        handlePickerChange(value, name);
      }
    }
  };

  //  请求接口车辆信息
  const requestCarInfo = (carCode: string) => {
    if (!carCode) return;
    if (!channelId) {
      message.warning({ content: '请先选择渠道！' });
      return;
    }
    const params = {
      orderNo,
      carId: carCode,
      channelId,
      type: carType,
    };
    getCarInfoByIdV2(params).then((res: any) => {
      const { data } = res || {};
      clearFormData();
      const msg = carType === CAR.NEW ? '车型码不存在' : '车辆识别代码不存在';
      if (!data?.carNodeList?.length) {
        message.warning({ content: msg });
        return;
      }

      const newData = formatData(data.carNodeList || []);
      setPickerData((orgin) => {
        return { ...orgin, [formTree[0]]: newData };
      });
      setDefaultPickerData(newData);
      // 更新车辆总列表
      innerCarList = data.carNodeList;
      // 设置为选择模式
      setIsView(false);
      // console.log('preCarCode', preCarCode);
      if (
        carCode !== preCarCode &&
        (orderStatus === ORDER_STATUS.PRE_AUDIT_PASS ||
          incomeStatus === INCOME_STATUS.RESELECT_VEHICLE) &&
        carType == preCarType
      ) {
        // console.log('011');
        //车型码变更
        setExtras({ ...extras, carCode: '您选择的车型已变更，请留意最新的预授信额度' });
      }
      // 如果只有一条，直接选中
      if (data.carNodeList.length === 1) {
        form.setFieldsValue({ cityName: newData[0].value });
        handlePickerChange(newData[0].value, 'cityName');
      }

      console.log('pickerData', pickerData);
    });
  };

  const handleInputBlur = (val: string, name: string) => {
    if (!val) {
      clearFormData();
      setIsView(false);
      onCarCodeConfirm({ carCode: val });
      return;
    }
    if (name === 'carCode') {
      requestCarInfo(val);
    }
  };

  // const handleInputChange = (val: string, name: string) => {
  //   console.log('val', val, preCarCode, orderStatus, carType, preCarType, name);
  //   if (
  //     name === 'carCode' &&
  //     val &&
  //     val !== preCarCode &&
  //     (orderStatus === ORDER_STATUS.PRE_AUDIT_PASS ||
  //       incomeStatus === INCOME_STATUS.RESELECT_VEHICLE) &&
  //     carType == preCarType
  //   ) {
  //     // console.log('021', val);
  //     //车型码变更
  //     setExtras({ ...extras, carCode: '您选择的车型已变更，请留意最新的预授信额度' });
  //   } else {
  //     setExtras({ ...extras, carCode: '' });
  //   }
  // };
  return (
    <div className={props.className}>
      {!carList.length ? (
        <Empty description={carType === CAR.NEW ? `该渠道暂无新车` : `该渠道暂无二手车`} />
      ) : (
        <Form
          form={form}
          disabled={readOnly}
          style={{ width: 800 }}
          wrapperCol={{ span: 12 }}
          labelCol={{ span: 4 }}
        >
          <div style={{ display: 'none' }}>
            <Form.Item name="carNo">
              <input hidden={true} />
            </Form.Item>
          </div>
          {formData.child?.map((child: ItemPropsType) => {
            return (
              <DisplayItem
                {...child}
                readOnly={readOnly}
                isView={isView}
                disableView={child.disableView}
                options={pickerData[child.name]}
                onPickerChange={(v: any) => handlePickerChange(v, child.name)}
                onInputBlur={(v: string) => {
                  handleInputBlur(v, child.name);
                }}
                // onInputChange={(v: string) => {
                //   handleInputChange(v, child.name);
                // }}
                extra={extras[child.name]}
              />
            );
          })}
        </Form>
      )}
    </div>
  );
};

export default React.memo(FormPage);
