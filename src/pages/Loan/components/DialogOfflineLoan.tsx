import { CommonImageUpload } from '@/components/ReleaseCom';
import { convertUploadFileList } from '@/utils/utils';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Button, message, Popconfirm } from 'antd';
import React, { useState } from 'react';
import { applymoneyOfflineSubmit } from '../service';

export type DialogOfflineLoanProps = {
  status: number | undefined;
  visible: boolean;
  onVisibleChange: (visible: boolean, isSubmit?: boolean) => void;
  userNo: string;
  productCode: string;
  refresh: () => void;
};

const DialogOfflineLoan: React.FC<DialogOfflineLoanProps> = ({
  status,
  visible,
  onVisibleChange,
  userNo,
  productCode,
  refresh,
}) => {
  const { lendingNo, orderNo } = history.location.query as {
    lendingNo: string;
    orderNo: string;
  };
  const [allFileList, handleFileList] = useState({});
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };
  const contentSty = {
    fontSize: '14px',
  };

  return (
    <ModalForm
      title={status === 0 ? '线下放款' : '提示'}
      layout="horizontal"
      visible={visible}
      width={520}
      onVisibleChange={onVisibleChange}
      onFinish={async (values) => {
        console.log('values: ', values);
        try {
          const mapUploadFile = convertUploadFileList(allFileList, ['otherEnclosure']);
          await applymoneyOfflineSubmit(
            {
              ...values,
              ...mapUploadFile,
              orderNo,
              lendingNo,
              userNo,
            },
            productCode,
          );
          refresh();
          message.success('放款操作成功');
          onVisibleChange(false, true);
        } catch (err) {
          message.info('稍后重试');
        }

        return true;
      }}
      submitter={{
        render: (props) => {
          const actions =
            status === 0
              ? [
                  <Popconfirm
                    key="ok"
                    placement="topRight"
                    title="是否确认提交线下放款"
                    onConfirm={() => {
                      props.submit();
                    }}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button type="primary">确定</Button>
                  </Popconfirm>,
                  <Button
                    key="cancel"
                    onClick={() => {
                      onVisibleChange(false);
                    }}
                  >
                    取消
                  </Button>,
                ]
              : [
                  <Button
                    key="cancel"
                    type="primary"
                    onClick={() => {
                      onVisibleChange(false);
                    }}
                  >
                    确定
                  </Button>,
                ];

          return actions;
        },
      }}
    >
      {status === 0 && (
        <>
          <CommonImageUpload
            extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx"
            desPath="CASH_ORDER_OFFLINE_APPLY_MONEY"
            orderNo={orderNo}
            label="附件"
            name="otherEnclosure"
            max={5}
            listType="text"
            size={10}
            mapFileList={mapFileList}
            accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
          />
          <ProFormTextArea
            name="otherMsg"
            label="备注"
            placeholder="请输入备注"
            fieldProps={{ maxLength: 500 }}
          />
        </>
      )}
      {status !== 0 && (
        <>
          <p style={contentSty}>仅放款失败时支持线下放款</p>
        </>
      )}
    </ModalForm>
  );
};

export default DialogOfflineLoan;
