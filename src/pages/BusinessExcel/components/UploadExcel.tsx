import { getAuthHeaders } from '@/utils/auth';
import type { IrepaymentUploadfileRes } from '@/utils/types';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ModalForm, ProFormUploadDragger } from '@ant-design/pro-components';
import type { UploadFile } from 'antd';
import { Button, message, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/lib/upload';
import dayjs from 'dayjs';
import React, { memo, useRef } from 'react';
import { uploadExcel } from '../services';
import type { IfileInfoListItem } from '../types';
import { getBizadminUploadAction } from '../utils';

export interface IshowFileListItem {
  name: string;
  url?: string;
  id: number;
}

type UploadExcelProps = {
  setPersonalFileList: (val: IshowFileListItem[]) => void;
  setEnterpriseFileList: (val: IshowFileListItem[]) => void;
  form: ProFormInstance;
};
const UploadExcel: React.FC<UploadExcelProps> = (props) => {
  const { setPersonalFileList, setEnterpriseFileList, form } = props;
  const formRef = useRef<ProFormInstance>();

  const handleFile = ({ file }: UploadChangeParam<UploadFile<IrepaymentUploadfileRes>>) => {
    const { status } = file;
    if (status === 'done') {
      const { response } = file;
      const { data } = response || {};
      const { netWorkPath, filePath } = data || {};
      file.url = netWorkPath;
      (file as any).ossUrl = filePath;
    }
  };
  return (
    <ModalForm<{ enterpriseFileList: UploadFile[]; personalFileList: UploadFile[] }>
      title="上传"
      formRef={formRef}
      trigger={<Button type="primary">上传</Button>}
      // modalProps={{destroyOnClose: true}}
      submitter={{
        render: (props1) => {
          return [
            <Button
              key="ok"
              type="primary"
              onClick={() => {
                if (!form.getFieldValue('dataMonth')) {
                  message.error('请先选择上报期数');
                  return;
                }
                props1.submit();
              }}
            >
              上传
            </Button>,
          ];
        },
      }}
      onFinish={async (values) => {
        const { enterpriseFileList = [], personalFileList = [] } = values;
        if (!enterpriseFileList.length && !personalFileList?.length) {
          message.error('请至少上传一个报表');
          return;
        }
        const personalFiles: IfileInfoListItem[] = personalFileList.map((item) => {
          const { type, name, ossUrl } = item as any;
          return {
            fileSuffix: type,
            fileType: 1,
            fileUrl: ossUrl,
            fileName: name,
          };
        });
        const enterpriseFiles: IfileInfoListItem[] = enterpriseFileList.map((item) => {
          const { type, name, ossUrl } = item as any;
          return {
            fileSuffix: type,
            fileType: 2,
            fileUrl: ossUrl,
            fileName: name,
          };
        });
        const fileInfoList: IfileInfoListItem[] = [...enterpriseFiles, ...personalFiles];
        const data = await uploadExcel({
          fileInfoList,
          dataMonth: dayjs(form.getFieldValue('dataMonth')).format('YYYY-MM'),
        });
        setPersonalFileList(
          data
            .filter((item) => item.fileType === 1)
            .map((item) => {
              const { fileName, fileNetworkUrl, id } = item;
              return {
                name: fileName,
                url: fileNetworkUrl,
                id: id,
              };
            }),
        );
        setEnterpriseFileList(
          data
            .filter((item) => item.fileType === 2)
            .map((item) => {
              const { fileName, fileNetworkUrl, id } = item;
              return {
                name: fileName,
                url: fileNetworkUrl,
                id: id,
              };
            }),
        );
        formRef.current?.resetFields();
        message.success('提交成功');
        return true;
      }}
    >
      <ProFormUploadDragger
        label="A55_小额贷款业务(个人贷款)信息表"
        accept=".xlsx,.xls"
        name="personalFileList"
        action={getBizadminUploadAction()}
        onChange={handleFile}
        fieldProps={{
          data: {
            attachment: false,
            acl: 'PUBLIC_READ',
            destPath: 'COMMON_FILE',
          },
          headers: { ...getAuthHeaders(), 'hll-appid': 'bme-finance-bizadmin-svc' },
          name: 'file',
          beforeUpload: (file) => {
            const reg = /A55/i;
            if (!reg.test(file.name)) {
              message.warning('文件必须是A55(个人贷款)');
              return false || Upload.LIST_IGNORE;
            }
            return true;
          },
          multiple: true,
        }}
      />
      <ProFormUploadDragger
        label="A56_小额贷款业务(企业贷款)信息表"
        accept=".xlsx,.xls"
        name="enterpriseFileList"
        action={getBizadminUploadAction()}
        onChange={handleFile}
        fieldProps={{
          data: {
            attachment: false,
            acl: 'PUBLIC_READ',
            destPath: 'COMMON_FILE',
          },
          beforeUpload: (file) => {
            const reg = /A56/i;
            if (!reg.test(file.name)) {
              message.warning('文件必须是A56(企业贷款)');
              return false || Upload.LIST_IGNORE;
            }
            return true;
          },
          headers: { ...getAuthHeaders(), 'hll-appid': 'bme-finance-bizadmin-svc' },
          name: 'file',
          multiple: true,
        }}
      />
    </ModalForm>
  );
};

export default memo(UploadExcel);
