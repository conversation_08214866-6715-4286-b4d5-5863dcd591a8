/*
 * @Author: your name
 * @Date: 2021-11-05 18:10:28
 * @LastEditTime: 2025-06-05 11:41:12
 * @LastEditors: oak.yang <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalIncoming/data.d.ts
 */
// 获取进件记录列表的响应数据类型
export interface UserInputRecordListItem {
  applyAmount: number; // 申请额度
  startApplyTime: string;
  endApplyTime: string;
  applyTime: string; // 申请时间
  classification: string; // 产品一级分类
  enterpriseName: string; // 企业名称
  orderNo: string; // 进件流水号
  secondClassification: string; // 产品二级分类
  status: number; // 状态
  userNo: string; // 用户编号
  orgCode: string; // 统一社会信用代码
  secondTypeCode?: string; //产品二级分类
  interestRate?: number;
  history030101Income: boolean;
  creditEndTime: string; // 风控审核时间
  phone: string; //  手机号
  idNo: string; //  证件号
  createAt: string; //  进件时间
  registerTime: string; //  注册时间
  riskStartTime: string; //  风控开始时间
  checkCode: string; //  验证码
  idNoEncrypted: string; //  证件号加密
  phoneEncrypted: string; //  手机号加密
}

// 获取进件记录列表的请求数据类型
export interface UserInputRecordListParams {
  endApplyTime?: string; // 申请结束时间
  enterPriseName?: string; // 企业名称
  orgCode?: string; // 统一社会信用代码
  startApplyTime?: string; // 申请起始时间
  status?: number; // 状态
  userNo?: number; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 页大小
  ref?: string;
}

export interface IlogoffParams {
  operator: string;
  userNo: string;
  phone: string;
}
