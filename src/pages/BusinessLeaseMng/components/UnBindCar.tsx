/*
 * @Date: 2023-06-13 20:08:18
 * @Author: elisa.<PERSON><PERSON>
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-12-27 10:40:28
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/UnBindCar.tsx
 * @Description:
 */

import { DividerTit } from '@/components';
import { CHANNEL_TYPES_MAP } from '@/enums';
import { Alert, message, Modal } from 'antd';
import React from 'react';
import { unbindCar } from '../service';

export type UnBindCarProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  refresh: () => void;
  data: {};
};
const UnBindCar: React.FC<UnBindCarProps> = ({ close, visible, data, refresh }) => {
  const styleItem: Record<string, React.CSSProperties | undefined> = {
    defaultItemLabel: {
      display: 'inline-block',
      width: 100,
      textAlign: 'right',
      marginRight: 10,
      marginBottom: 10,
    },
    defaultItemRedLabel: {
      display: 'inline-block',
      width: 100,
      textAlign: 'right',
      marginRight: 10,
      marginBottom: 10,
      color: 'red',
    },
  };

  const orderInfo = {
    orderNo: '订单号',
    userName: '用户姓名',
    creditAmount: '申请金额',
    channelType: '渠道类型',
    channelName: '渠道名称',
  };

  const mapChannelTypeItem = CHANNEL_TYPES_MAP;

  const mapCarType = {
    1: '新车',
    2: '二手车',
  };

  const carInfo = {
    carUniqueCode: '车型码',
    carType: '车辆类型',
  };
  const redCarLabelInfo = {
    carUniqueCode: '车辆识别代码',
    carFabricateDate: '车辆制造日期',
    engineCode: '发动机号',
  };
  return (
    <Modal
      destroyOnClose
      centered
      okText="确定解绑"
      onOk={async () => {
        const res = await unbindCar(data?.orderNo);
        if (res) {
          refresh();
          message.success('操作成功');
          close();
        }
      }}
      open={visible}
      onCancel={close}
      title="车辆解绑"
    >
      <DividerTit title="订单信息" />
      {Object.keys(orderInfo).map((item) => {
        return (
          <div>
            <span style={styleItem.defaultItemLabel}> {orderInfo[item]}： </span>
            {item === 'channelType' ? mapChannelTypeItem[data?.[item]] : data?.[item]}
          </div>
        );
      })}
      <DividerTit title="车辆信息" />
      {Object.keys(carInfo).map((item) => {
        return (
          <div>
            <span style={styleItem.defaultItemLabel}> {carInfo[item]}： </span>
            {item === 'carType' ? mapCarType[data?.[item]] : data?.[item]}
          </div>
        );
      })}
      {Object.keys(redCarLabelInfo).map((item) => {
        return (
          <div>
            <span style={styleItem.defaultItemRedLabel}> {redCarLabelInfo[item]}： </span>
            {data?.[item]}
          </div>
        );
      })}
      <Alert
        message={
          <div>
            解绑说明：点击车辆解绑之后，融租订单状态从当前状态回退至：
            <span style={{ color: 'blue' }}>审核通过；</span>放款订单状态更新为：<span>失效</span>;
            并将“审核通过”之后的流程
            <span style={{ color: 'blue' }}> 提交的资料和信息数据置为失效，请慎重！</span>
          </div>
        }
        type="warning"
      />
    </Modal>
  );
};

export default UnBindCar;
