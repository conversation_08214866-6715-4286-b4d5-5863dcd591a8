import { ShowInfo } from '@/components';
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { MenuUnfoldOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { Button, Card, Drawer, Table } from 'antd';
import React, { useRef, useState } from 'react';
import AuthorizedChangeModal from './components/AuthorizedChangeModal';
import CreateForm from './components/CreateForm';
import EnterpriseChangeModal from './components/EnterpriseChangeModal';
import InputTable from './components/inputTable';
import PaybackList from './components/PaybackList';
import ProductData from './components/ProductData';
import QuotaDetail from './components/quota-detail';
import { getBaseIofo, getRiskManagementData } from './service';

import comDetailTitle from './index.less';

const ComDetail: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [subAccountNo, setSubAccountNo] = useState<string>('');
  const [visibleDrawer, setVisibleDrawer] = useState<boolean>(false);
  const [visibleChangeAuthroized, setVisibleChangeAuthorized] = useState<boolean>(false);
  const [visibleUpdateCompany, setVisibleUpdateCompany] = useState<boolean>(false);
  const { userNo } = history.location.query as { userNo: string };
  const childRef: any = useRef();
  // 获取企业用户详情---基础信息
  const { data: baseData } = useRequest(() => {
    return getBaseIofo(userNo);
  });

  // 获取企业用户详情---风控数据
  const { data: riskManagementData, loading: riskManagementLoading } = useRequest(() => {
    return getRiskManagementData(userNo);
  });

  const statusMap = {
    status: {
      '1': '激活',
      '2': '冻结',
    },
  };

  const baseInfoMap = {
    accountNo: '账号ID',
    enterpriseName: '企业名称',
    orgCode: '统一社会信用代码',
    legalMan: '法人姓名',
    channel: '渠道商户',
    status: '状态',
  };
  // 风控数据的列表
  const columns = [
    {
      title: '产品一级分类',
      dataIndex: 'productFirstTypeName',
      key: 'productFirstTypeName',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeName',
      key: 'productSecondTypeName',
    },
    {
      title: '评级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
    },
    {
      title: '授信额度（元）',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
    },
    {
      title: '授信时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: { subAccountNo: string }) => (
        <Button
          type="link"
          onClick={() => {
            setSubAccountNo(record.subAccountNo);
            handleModalVisible(true);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer
        extra={
          <MenuUnfoldOutlined
            style={{ color: '#1a87fe', fontSize: '20px' }}
            onClick={() => {
              setVisibleDrawer(!visibleDrawer);
            }}
          />
        }
      >
        <div className={globalStyle?.drawerLabel}>
          <ShowInfo title="基础信息" infoMap={baseInfoMap} data={baseData} itemMap={statusMap} />
          <Card
            title="风控数据"
            bordered
            loading={riskManagementLoading}
            className={globalStyle.mt20}
          >
            <div>总授信额度（元）：{riskManagementData?.totalCreditAmount}</div>
            <Table
              columns={columns}
              dataSource={riskManagementData?.dataDetails}
              pagination={false}
              rowKey="subAccountNo"
            />
          </Card>
          <Card title="进件记录" bordered className={globalStyle?.mt20}>
            <InputTable userNo={userNo} />
          </Card>
          <ProductData userNo={userNo} cRef={childRef} />
          <PaybackList userNo={userNo} />
          <CreateForm
            onCancel={() => {
              handleModalVisible(false);
            }}
            modalVisible={createModalVisible}
            title="额度详情"
          >
            <QuotaDetail userNo={userNo} subAccountNo={subAccountNo} />
          </CreateForm>
          <Drawer
            width={250}
            onClose={() => {
              setVisibleDrawer(false);
            }}
            getContainer={false}
            rootStyle={
              {
                // position: 'absolute',
                // width: 'calc(100% + 48px)',
                // height: 'calc(100% + 170px)',
                // left: '-24px',
                // top: '-29px',
                // zIndex: 2,
              }
            }
            closable={false}
            open={visibleDrawer}
            bodyStyle={{ paddingBottom: 80 }}
          >
            <p className={comDetailTitle?.drawerTit}>功能面板</p>
            <p className={comDetailTitle?.drawerItem}>
              <Button
                type="primary"
                size="large"
                onClick={() => {
                  setVisibleChangeAuthorized(true);
                  setVisibleDrawer(false);
                }}
              >
                变更授权人信息
              </Button>
            </p>
            <p className={comDetailTitle?.drawerItem}>
              <Button
                type="primary"
                size="large"
                onClick={() => {
                  setVisibleUpdateCompany(true);
                  setVisibleDrawer(false);
                }}
              >
                变更企业信息
              </Button>
            </p>
          </Drawer>
        </div>
        <AuthorizedChangeModal
          title="变更授权人信息"
          visible={visibleChangeAuthroized}
          onCancel={() => {
            setVisibleChangeAuthorized(false);
          }}
          onVisibleChange={setVisibleChangeAuthorized}
          refresh={() => {
            // refreshBase();
            // refreshRisk();
            childRef?.current?.reload();
          }}
        />
        <EnterpriseChangeModal
          title="变更企业信息"
          visible={visibleUpdateCompany}
          onCancel={() => {
            setVisibleUpdateCompany(false);
          }}
          onVisibleChange={setVisibleUpdateCompany}
        />
      </PageContainer>
    </>
  );
};

export default ComDetail;
