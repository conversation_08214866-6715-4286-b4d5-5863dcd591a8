import { getPublicK<PERSON> } from '@/services/user';
import forge from 'node-forge';

// rsa加密,异步方法
const publicKeyPaddingStart = `-----BEGIN PUBLIC KEY-----\n`;
const publicKeyPaddingEnd = '\n-----<PERSON><PERSON> PUBLIC KEY-----';
// 返回undefined表示加密失败
export async function asyncEncryptWithRSA(val: string): Promise<string | undefined> {
  if (!val) {
    return;
  }
  const res: any = (await getPublicKey().catch(() => {})) || {};
  console.log('publicKey', res);
  if (res?.ret === 0 && res?.data) {
    const publicKeyPem = publicKeyPaddingStart + res?.data + publicKeyPaddingEnd;
    try {
      // forge publicKey实例
      const publicKeyInstance = forge.pki.publicKeyFromPem(publicKeyPem);
      // utf8编码目标字符串
      const plaintextBytes = forge.util.encodeUtf8(val);
      // rsa加密:
      // 前端：RSAES-OAEP/SHA-256/MGF1-SHA-1
      // 对应java服务端：RSA/ECB/OAEPWithSHA-256AndMGF1Padding
      const encrypted: string = publicKeyInstance.encrypt(plaintextBytes, 'RSA-OAEP', {
        md: forge.md.sha256.create(),
        mgf1: {
          md: forge.md.sha1.create(),
        },
      });
      // base64编码加密结果
      const encryptedBase64 = forge.util.encode64(encrypted);
      return encryptedBase64;
    } catch (e) {
      console.error(e);
      return;
    }
  } else {
    console.error('加密错误');
    return;
  }
}
// MD5 32位16进制摘要
export function md5(val: string = ''): string | undefined {
  try {
    const md5Instance = forge.md.md5.create();
    md5Instance.update(val);
    return md5Instance.digest().toHex();
  } catch (e) {
    console.error(e);
    return '';
  }
}
