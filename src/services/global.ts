/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2022-10-19 17:14:51
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/services/global.ts
 */
import { bizAdminHeader, headers } from '@/utils/constant';
import { isStandbyLogin } from '@/utils/utils';
import { request } from '@umijs/max';
import type { DownZips } from './API';

// 全局枚举
export async function getGlobalEnum() {
  return request('/loan/audit/getAllCreditEnum', {});
}

// 获取ossPath
export async function getOssPath(ossPath: string) {
  return request(`/repayment/getNetUrl`, {
    method: 'GET',
    params: { ossPath },
  });
}

// 还款信息
export async function getRepayInfo(orderNo: string) {
  return request(`/repayment/cms/bill/repay/info/${orderNo}`);
}

// 根据还款计划查详情
export async function getRepayRegist(planNo: string) {
  return request(`/repayment/cms/bill/record/${planNo}`);
}

// 还款计划
export async function getRepayPlan(orderNo: string) {
  return request(`/repayment/cms/bill/repay/plan/${orderNo}`);
}

// 城市选择器
export async function getCities() {
  return request(`/risk/cityList`);
}

// 放款信息
export async function getLoanInfo(orderNo: string) {
  return request(`/quota/order/getLendingInfo/${orderNo}`);
}

// 订单详情
export async function queryOrderDetail(orderNo: string) {
  return request(`/bizadmin/lease/order/detail/${orderNo}`, {
    headers: bizAdminHeader,
  });
}

// 进件信息
export async function getIncomingInfo(orderNo: string) {
  return request(`/quota/order/getOrderInfo/${orderNo}`);
}

//导出
export async function exportRepayDetail(orderNo: string) {
  return request(`/repayment/cms/bill/repay/detail/excel/${orderNo}`, {
    responseType: 'blob',
    getResponse: true,
  });
}

//多文件下载
export async function fileZips(data: DownZips) {
  return request(`/loan/user/order/zip/files`, {
    method: 'POST',
    responseType: 'blob',
    data,
    getResponse: true,
  });
}

interface QueryUserParams {
  pageSize?: number;
  current?: number;
  pidList?: string[];
  employeeName?: string; // 模糊匹配
  employeeType?: number; // 9运营| 10车险渠道| 11委外
  notInEmployeeType?: number[]; // 排除的员工类型
  phone?: string;
  email?: string;
  status?: number;
  // 传roleCodes时，表示角色筛选，符合其中一个角色就会返回
  roleCodes?: string[]; // 角色code
}
export async function queryAllUserList(
  data: QueryUserParams = {
    pageSize: 1000,
    current: 1,
  },
) {
  if (isStandbyLogin()) {
    const value = await request('/auth/operator/privilege/getUserList', {
      skipErrorHandler: true,
    });
    // console.log(value, 'value');
    const res: any = {
      ...value,
      data: value?.data?.map((item: any) => {
        return {
          ...item,
          id: item?.id?.toString(),
        };
      }),
    };
    // console.log('res', res);
    return res;
  }
  const res: any = await request(`/bizadmin/employee/queryEmployeeListByPage`, {
    method: 'POST',
    data,
    headers,
  });
  // 兼容原来的代码
  const result: any = {
    data: res?.data?.map((item: any) => ({
      ...item,
      id: item?.pid,
      phoneNum: item?.phone,
      username: item?.employeeName,
    })),
  };
  console.log('result', result);
  return result;
}

// 获取当前用户灰度状态
export async function getCurrentGrayStatus(pid: string, employeeType: number) {
  return request(`/bizadmin/insurance/policy/account/person/Gray`, {
    method: 'POST',
    data: {
      pid,
      employeeType,
    },
    headers: {
      ...bizAdminHeader,
    },
  });
}
