/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-09-15 17:12:58
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-10-12 14:53:24
 * @FilePath: /code/lala-finance-biz-web/src/pages/SmsRecordMsg/index.tsx
 * @Description: SmsRecordMsg，手动催收短信记录
 */
import HeaderTab from '@/components/HeaderTab';
import { SECONDARY_CLASSIFICATION_MAP_ALL } from '@/enums';
import { getUserListEnum } from '@/services/enum';
import { downLoadExcel } from '@/utils/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useRequest } from 'ahooks';
import { Button } from 'antd';
import React, { useRef, useState } from 'react';
import { getBatchOverdueSMSData } from '../Collection/service';
import type { OverdueSMSRecordResultItem } from './data';
import { exportSMSRecordByUrgeId, getOverdueSMSRecordPage } from './service';

const SmsRecordMsgCon: React.FC<any> = () => {
  const [loading, setLoading] = useState({});

  const { data: smsData } = useRequest(getBatchOverdueSMSData);
  const firstReq = useRef(true);
  const columns: ProColumns<OverdueSMSRecordResultItem>[] = [
    {
      title: '批次ID',
      dataIndex: 'urgeId',
      search: false,
    },
    {
      title: '产品二级名称',
      dataIndex: 'productName',
      valueType: 'select',
      search: false,
    },
    {
      title: '产品二级名称',
      dataIndex: 'productSecondCode',
      valueType: 'select',
      hideInTable: true,
      valueEnum: SECONDARY_CLASSIFICATION_MAP_ALL,
      initialValue: '0301',
    },
    {
      title: '面向客群',
      dataIndex: 'urgeName',
      valueType: 'select',
      fieldProps(form) {
        if (form) {
          const productSecondCode = form.getFieldValue('productSecondCode');
          const options = smsData
            ?.filter((item) => item.secondaryClassification === productSecondCode)
            .map((item: any) => {
              return {
                lable: item.smsTemplateId,
                value: item.smsTemplateName,
              };
            });
          return {
            options,
          };
        }
        return [];
      },

      render: (_, record: any) => {
        return <>{record.urgeName}</>;
      },
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      // valueType: 'date',
      search: false,
    },
    {
      title: '操作时间',
      dataIndex: 'callDateDetail',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '操作人员',
      dataIndex: 'urgePersonName',
      search: false,
    },
    {
      title: '操作人员',
      dataIndex: 'urgePersonId',
      valueType: 'select',
      hideInTable: true,
      // valueEnum: userListDataSelect.current,
      request: getUserListEnum,
    },
    {
      title: '操作状态',
      dataIndex: 'urgeStatus',
      valueType: 'select',
      valueEnum: {
        1: '未处理',
        2: '处理中',
        3: '处理成功',
        4: '部份处理成功',
        5: '处理失败',
      },
    },
    {
      title: '客户明细',
      key: 'id',
      search: false,
      render: (_, record: any) => {
        if ([3, 4].includes(record.urgeStatus)) {
          return (
            <Button
              type="primary"
              loading={loading[record.urgeId]}
              shape="round"
              icon={<DownloadOutlined />}
              onClick={() => {
                setLoading({ ...loading, [record.urgeId]: true });
                exportSMSRecordByUrgeId(record.urgeId as string)
                  .then((res: any) => {
                    downLoadExcel(res);
                  })
                  .finally(() => {
                    setLoading({ ...loading, [record.urgeId]: false });
                  })
                  .catch((e) => {
                    console.log(e);
                  });
              }}
            >
              下载
            </Button>
          );
        }
        return <></>;
      },
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<OverdueSMSRecordResultItem>
          rowKey="id"
          search={{
            labelWidth: 100,
          }}
          columns={columns}
          request={async (params: any) => {
            // params 有可能不会携带初始值，formref 也获取不到
            const { callDateDetail, productSecondCode, ...rest } = params;
            const callDateParam = callDateDetail
              ? {
                  createTimeStart: `${callDateDetail[0]} 00:00:00`,
                  createTimeEnd: `${callDateDetail[1]} 23:59:59`,
                }
              : null;
            const data = await getOverdueSMSRecordPage({
              ...rest,
              ...callDateParam,
              productSecondCode: firstReq.current ? '0301' : productSecondCode,
            });
            firstReq.current = false;
            return data;
          }}
        />
      </PageContainer>
    </>
  );
};

export default React.memo(SmsRecordMsgCon);
