/* eslint-disable react-hooks/exhaustive-deps */
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import React, { memo, useEffect, useRef, useState } from 'react';
import type { IlookCarInfoItem, IremissionDetailItem } from '../types';
import { reduceFeeMap } from '../types';
import ProForm, { ModalForm } from '@ant-design/pro-form';
import styles from '../styles/index.less';
import { message } from 'antd';
import { EcarStatus } from '@/utils/bankend/enum';
type EditTableProps = {
  onChange?: (value: any[]) => void;
  value?: any[];

};
const EditTable: React.FC<EditTableProps> = (props) => {
  const { onChange, value = [] } = props;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(value.map((item) => item?.id));
  const [dataSource, setDataSource] = useState<any[]>(value);
  const actionRef = useRef();
  const [costTypeEnum, setCostTypeEnum] = useState({});

  // 同步父元素的表单值
  useEffect(() => {
    onChange?.(dataSource);
  }, [dataSource]);

  // dataSource 发生变化 重置 减免费项 的禁用
  useEffect(() => {
    const costTypeEnum1: any = {};
    for (const key in reduceFeeMap) {
      costTypeEnum1[key] = {
        text: reduceFeeMap[key],
        disabled: dataSource.map((item) => item.costType).includes(key),
      };
    }
    setCostTypeEnum(costTypeEnum1);
  }, [dataSource]);

  const columns: ProColumns<any>[] = [
    {
      title: <div className={styles.required}>减免费项</div>,
      key: 'costType',
      dataIndex: 'costType',
      valueType: 'select',
      valueEnum: costTypeEnum,
    },
    {
      title: <div className={styles.required}>减免金额</div>,
      key: 'remissionAmount',
      dataIndex: 'remissionAmount',
      valueType: 'money',
      fieldProps: {
        min: 0,
      },
    },
    {
      title: '操作',
      dataIndex: 'delete',
      valueType: 'option',
    },
  ];
  return (
    <EditableProTable<IremissionDetailItem>
      bordered
      rowKey="id"
      value={dataSource}
      toolBarRender={false}
      actionRef={actionRef}
      onChange={(values) => {
        setDataSource(values);
      }}
      columns={columns}
      recordCreatorProps={{
        newRecordType: 'dataSource',
        position: 'top',
        // 新增一行的数据
        record: () => ({
          id: Date.now(), // 不写用 index index无法触发editable.onChange
        }),
      }}
      editable={{
        type: 'multiple',
        onValuesChange: (record, recordList) => {
          console.log('record', record);
          console.log('recordList', recordList);
          setDataSource(recordList);
        },
        editableKeys,
        onChange: (keys) => {
          console.log('keys', keys);
          setEditableRowKeys(keys);
        },
        actionRender: (row, _, dom) => {
          return [dom.delete];
        },
      }}
    />
  );
};

type Props = {
  value?: any[];
  onChange?: (value: any[]) => void;
  children: any;
  licensePlateNo: string; // 车牌号
  vin: string; // 车架号
  carInfo: IlookCarInfoItem[];
  setFlush: (val: any) => void
};
const RemissionModal: React.FC<Props> = (props) => {
  const { licensePlateNo, vin, onChange, carInfo, value: remissInfo } = props;

  /**
   * 校验减免信息
   */
  function verifyRemission(remissionDetailList: IremissionDetailItem[]): boolean {
    // if (!remissionDetailList?.length) {
    //   message.error('减免信息是必填的');
    //   return false;
    // }
    const oneCar = carInfo.filter((item) => item.licensePlateNo === licensePlateNo)[0];
    const oneCarAmountMap = {
      1: {
        text: '利息',
        max: 0,
      },
      2: {
        text: '提前结清违约金',
        max: 0,
      },
      3: {
        text: '逾期罚息',
        max: 0,
      },
      4: {
        text: '本金',
        max: 0,
      },
      5: {
        text: '逾期滞纳金',
        max: 0,
      },
    };
    const { carPolicyTermRspBOS } = oneCar;
    for (let i = 0; i < carPolicyTermRspBOS?.length; i++) {
      const item = carPolicyTermRspBOS[i];
      const { status } = item || {};
      if (status === EcarStatus.PENDING_REPAYMENT || status === EcarStatus.OVERDUE) {
        // 待还款 和 逾期的
        oneCarAmountMap['1'].max = oneCarAmountMap['1'].max + Number(item.remainingInterest);
        oneCarAmountMap['2'].max = oneCarAmountMap['2'].max + Number(item.advanceLiquidatedDamages);
        oneCarAmountMap['3'].max = oneCarAmountMap['3'].max + Number(item.overdueInterest);
        oneCarAmountMap['4'].max = oneCarAmountMap['4'].max + Number(item.remainingPrincipal);
        oneCarAmountMap['5'].max = oneCarAmountMap['5'].max + Number(item.overdueLatePaymentFee);
      }
    }
    for (let i = 0; i < remissionDetailList?.length; i++) {
      const item = remissionDetailList[i];
      if (!item.costType) {
        message.error('减免费项是必填的');
        return false;
      }
      if (!item.remissionAmount && item.remissionAmount !== 0) {
        message.error('减免金额是必填的');
        return false;
      }
      const max = oneCarAmountMap[item.costType!].max;
      const text = oneCarAmountMap[item.costType!].text;
      console.log('item.remissionAmount', item.remissionAmount);
      console.log('max', max);
      if (item.remissionAmount! > max) {
        message.error(`${text}的减免金额范围在0-${max.toFixed(2)}`);
        return false;
      }
    }
    return true;
  }
  const onFinish = async (values: any) => {
    if (verifyRemission(values?.remissionDetailList)) {
      onChange?.(values?.remissionDetailList);
      props.setFlush({})
      return true;
    } else {
      return false;
    }
  };
  return (
    <ModalForm
      trigger={<div>{props.children}</div>}
      title={
        <div>
          {licensePlateNo}-{vin}
        </div>
      }
      //   width={1000}
      layout="horizontal"
      initialValues={{ remissionDetailList: remissInfo }}
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={onFinish}
    >
      <ProForm.Item name="remissionDetailList">
        <EditTable />
      </ProForm.Item>
    </ModalForm>
  );
};

export default memo(RemissionModal);
