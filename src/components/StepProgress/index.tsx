/*
 * @Author: your name
 * @Date: 2020-11-24 11:10:13
 * @LastEditTime: 2024-07-08 14:18:17
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/components/StepProgress/index.tsx
 */
// import { useRequest } from '@umijs/max';
import globalStyle from '@/global.less';
import { sliceArray } from '@/utils/utils';
import { Steps, Typography } from 'antd';
import React from 'react';

interface StepProgressProps {
  stepStatus: StatusItem[];
  index?: number;
  title?: string;
  progressDot?: boolean;
  lineLength?: number;
  className?: string;
  rejectMsg?: string;
}
interface StatusItem {
  bol: boolean;
  desc: string;
  localDate: string;
  remark?: string;
  subStatusDesc?: string;
  status?: number;
  icon?: React.ReactNode;
  rejectMsg?: string;
  funderRejectReason?: string; // 资方驳回原因
}

const StepProgress: React.FC<StepProgressProps> = (props) => {
  const { stepStatus, title, progressDot, lineLength } = props;
  const { Step } = Steps;
  const { Text } = Typography;
  const newArr = sliceArray(stepStatus, lineLength || 5);
  return (
    <div
      className={`${globalStyle.mt20}`}
      style={{ background: '#fff', padding: 20 }}
      title={title}
    >
      {newArr.map((sonArr, index) => (
        <Steps
          progressDot={progressDot}
          current={sonArr.length}
          className={`${globalStyle.stepsInitial} ${globalStyle.mb20}`}
          key={index}
        >
          {sonArr &&
            sonArr.map((item: StatusItem) => {
              return (
                <Step
                  title={item.desc}
                  icon={item.icon}
                  description={
                    <>
                      {item.funderRejectReason && (
                        <div>
                          <Text
                            ellipsis={{ tooltip: `${item.funderRejectReason}` }}
                            style={{ opacity: 0.6, fontWeight: 500, marginBottom: 2 }}
                          >
                            {item.funderRejectReason}
                          </Text>
                          <br />
                        </div>
                      )}
                      {item.localDate}
                      <br />
                      {
                        // 展示撤销原因
                        [-2].includes(item.status!) ? (
                          <div>
                            <Text
                              ellipsis={{
                                tooltip: `原因: ${item.subStatusDesc}`,
                              }}
                              style={{ opacity: 0.45 }}
                            >
                              原因: {item.subStatusDesc}
                            </Text>
                          </div>
                        ) : (
                          item.subStatusDesc
                        )
                      }
                      {
                        // 展示驳回原因
                        item.rejectMsg && (
                          <div>
                            <Text
                              ellipsis={{ tooltip: `原因: ${item.rejectMsg}` }}
                              style={{ opacity: 0.45 }}
                            >
                              原因: {item.rejectMsg}
                            </Text>
                          </div>
                        )
                      }
                    </>
                  }
                  subTitle={item.remark}
                  key={item.localDate}
                />
              );
            })}
        </Steps>
      ))}
    </div>
  );
};

export default StepProgress;
