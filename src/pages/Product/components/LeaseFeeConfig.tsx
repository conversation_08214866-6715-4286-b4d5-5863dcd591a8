/*
 * @Author: your name
 * @Date: 2021-04-14 18:43:09
 * @LastEditTime: 2021-11-18 18:13:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/ReleaseFeeConfig.tsx
 */
import {
  BASE_PROPORTION_NO_BRACKETS,
  EXPENSE_ITEMS,
  MAX_OPEN_CLOSE,
  MIN_OPEN_CLOSE,
} from '@/enums';
import globalStyle from '@/global.less';
import ProTable from '@ant-design/pro-table';
import { But<PERSON>, Card } from 'antd';
import React, { useEffect, useState } from 'react';
// import { getUuid } from '@/utils/utils';
import FeeConfigModal from './FeeConfigModal';

interface LeaseFeeConfigProps {
  data: {
    id?: string;
    classification?: string;
    costConfigDTO?: any;
    defaultCostConfigDTO?: any;
    leaseBailCostParameterDTO?: any;
    leaseInterestCostParameterDTO?: any;
  };
  isAddProduct?: boolean; //
  inComeConfig?: any;
  repaymentTerm?: []; // 分期数
  initialRepaymentTerm?: []; // 初始化分期数
  triggerNewFeeConfigDataToAddProduct?: (newTableAllData: [], newTableShowData: []) => void;
}

const LeaseFeeConfig: React.FC<LeaseFeeConfigProps> = ({
  data,
  inComeConfig,
  repaymentTerm,
  initialRepaymentTerm,
  triggerNewFeeConfigDataToAddProduct,
}) => {
  const {
    costConfigDTO,
    defaultCostConfigDTO,
    leaseBailCostParameterDTO,
    leaseInterestCostParameterDTO,
  } = data;

  const [curRow, setCurRow] = useState({});

  const [optVisibleConfigFee, setOptVisibleConfigFee] = useState<boolean>(false);
  const [curOptionType, setOptionType] = useState<string>('SHOW');

  const [costConfigTableNewData, handleCostConfigTableNewData] = useState([]); // table筛选展示的数据
  const [costConfigDTONew, handleCostConfigDTONew] = useState(costConfigDTO);

  useEffect(() => {
    // 这两个数据没有，说明接口还没有返回完整
    if (!(defaultCostConfigDTO && costConfigDTO)) {
      return;
    }
    // DEPOSIT 定金 ，COMMISSION手续费，BAIL 保证金，LOW_DOWN_PAYMENT 手续费
    // 根据进件信息展示不展示费用配置表
    const staticShow = [
      'IN_ADVANCE_LIQUIDATED_DAMAGES',
      'OVERDUE_PENALTY_INTEREST',
      'OVERDUE_FACTORING_INTEREST',
      'FACTORING_SERVICE_FEE',
      'LEASE_INTEREST',
      'INTEREST',
      'LEASE_IN_ADVANCE_LIQUIDATED_DAMAGES',
      'LEASE_OVERDUE_PENALTY_INTEREST',
      'OVERDUE_DELAY_AMOUNT',
    ];

    // 是否配置定金
    const depositStr = inComeConfig?.whetherConfigDeposit ? 'DEPOSIT' : '';

    // 是否选中前置费用
    const expenseItem = inComeConfig?.whetherConfigLeadCost ? inComeConfig?.expenseItems : [];

    const noIncludeItem = [depositStr, ...staticShow].concat(expenseItem || []);

    // 给表格重新赋值
    // defaultCostConfigDTO-初始配置，没有修改后的参数
    // costConfigDTO-修改后的配置，配置项可能<=defaultCostConfigDTO
    // costConfigDTONew-经过弹窗编辑之后的全部配置
    const newData = [];
    defaultCostConfigDTO.forEach((item) => {
      if (noIncludeItem.includes(item.expenseItem)) {
        const findItem = (costConfigDTONew || costConfigDTO)?.find(
          (el) => el.expenseItem === item.expenseItem,
        );
        if (findItem) {
          newData.push(findItem);
        } else {
          newData.push(item);
        }
      }
    });

    console.log('newData', newData);

    // 重新赋值
    handleCostConfigTableNewData(newData);

    triggerNewFeeConfigDataToAddProduct?.(
      costConfigDTONew || costConfigDTO || defaultCostConfigDTO || [],
      newData || [],
    );
  }, [inComeConfig, costConfigDTO, defaultCostConfigDTO]);

  const getStaticItemObj = (item) => {
    const {
      expenseItem,
      receivingNodeEnum,
      fundersEnum,
      receiverEnum,
      collectionMethodEnum,
      chargeMethodEnum,
      returnNodeEnum,
      deductionEnum,
    } = item;
    return {
      expenseItem,
      receivingNodeEnum,
      fundersEnum,
      receiverEnum,
      collectionMethodEnum,
      chargeMethodEnum,
      returnNodeEnum,
      deductionEnum,
    };
  };

  // 从弹窗提交后修改数据，
  const modifyLeaseFeeConfigDataFromPop = (tableItem: {
    expenseItem: string;
    expenseItemData: any;
  }) => {
    const modifyTableItem = costConfigTableNewData.map((item: any) => {
      // 检查修改项，组装筛选展示的表格数据
      if (item.expenseItem === tableItem.expenseItem) {
        // 表格里面固定不变的字段单独取出来，再把弹窗编辑后的字段重新放进去
        const obj = getStaticItemObj(item);
        return {
          ...obj,
          ...tableItem.expenseItemData,
        };
      }
      return item;
    });
    // 更新筛选表格数据
    handleCostConfigTableNewData(Object.assign([], modifyTableItem) || []); //

    // 修改全量的表格数据，
    const costConfigDTONewModify = defaultCostConfigDTO.map((item: any) => {
      // 找到在修改过的数据里面的配置项
      const findItem = modifyTableItem?.find((el) => el.expenseItem === item.expenseItem);
      // 取值逻辑：已经存在于老数据里面的配置项，就用老数据里的，否则用默认的配置项
      const curItem = findItem ?? item;
      if (curItem.expenseItem === tableItem.expenseItem) {
        // 表格里面固定不变的字段单独取出来，再把弹窗编辑后的字段重新放进去
        const obj = getStaticItemObj(curItem);
        return {
          ...obj,
          ...tableItem.expenseItemData,
        };
      }
      return curItem;
    });
    handleCostConfigDTONew(costConfigDTONewModify);
    // 并且将修改的全量数据回传到add-product组件
    triggerNewFeeConfigDataToAddProduct?.(costConfigDTONewModify, modifyTableItem || []);
  };

  const columnsConfig = [
    {
      title: '费用项',
      dataIndex: 'expenseItem',
      valueEnum: EXPENSE_ITEMS,
    },
    {
      title: '收取节点',
      dataIndex: 'receivingNodeEnum',
      valueEnum: {
        AUDIT_BEFORE: '审核前',
        SIGN_AFTER: '签署合同后',
        REPAYMENT: '还款时',
        INADVANCE_REPAYMENT: '提前结清时',
      },
    },
    {
      title: '出资方',
      dataIndex: 'fundersEnum',
      valueEnum: {
        INCOMING_USER: '进件用户',
        CHANNEL_MERCHANT: '渠道商户',
      },
    },
    {
      title: '收取方',
      dataIndex: 'receiverEnum',
      valueEnum: {
        INCOMING_USER: '进件用户',
        CHANNEL_MERCHANT: '渠道商户',
        LA_LA_TIAN_JIN_TAXI: '啦啦（天津）汽车科技有限公司',
        GUANG_ZHOU_YI_REN: '广州易人行商业保理有限公司',
        GUANG_ZHOU_YI_REN_LEASE: '广州易人行融资租赁有限公司',
        GUANG_ZHOU_YI_REN_LOAN: '广州易人行小额贷款有限公司',
      },
    },
    {
      title: '计收方式',
      dataIndex: 'collectionMethodEnum',
      valueEnum: {
        ACCUMULATED_BY_DAY: '按天累积',
        TWENTY_EIGHT_DAY: '28天',
        REPAYMENT_PERIOD: '还款期限',
        STROKE: '单笔',
        SINGLE: '按笔',
        DAY: '按天',
      },
    },
    {
      title: '计算方式',
      dataIndex: 'calculationEnum',
      valueEnum: {
        PROPORTIONALLY: '按比例',
        AMOUNT_PROPORTION: '按金额',
      },
    },
    {
      title: '金额范围',
      dataIndex: 'amountMax',
      render: (_, record) => {
        let rangeAmount = '-';
        if (record.regionAmount?.amountMax) {
          rangeAmount = `${MIN_OPEN_CLOSE[record?.regionAmount?.minOpenOrCloseRange] || '['}${
            record?.regionAmount?.amountMin
          },${record?.regionAmount?.amountMax}${
            MAX_OPEN_CLOSE[record?.regionAmount?.maxOpenOrCloseRange] || ']'
          }`;
        }
        const mapAmount = {
          FIXED_AMOUNT: record?.amount ?? '-',
          RANGE_AMOUNT: rangeAmount,
        };

        const finAmountValue = mapAmount[record?.amountTypeEnum] ?? '-';
        // console.log(
        //   record?.expenseItem,
        //   record?.calculationEnum,
        //   record?.amountTypeEnum,
        //   finAmountValue,
        // );
        // 不是分离定价展示
        return !record?.whetherByPriceTable ? finAmountValue : '-';
      },
    },
    {
      title: '基数',
      dataIndex: 'baseEnum',
      valueEnum: BASE_PROPORTION_NO_BRACKETS,
    },
    {
      title: '比例数',
      dataIndex: 'proportionNumberEnum',
      render: (_, record) => {
        let rangeProportion = '-';
        if (record?.regionProportion?.maxProportionNumberValue) {
          rangeProportion = `${
            MIN_OPEN_CLOSE[record?.regionProportion?.minOpenOrCloseRange] || '['
          }${record?.regionProportion?.minProportionNumberValue},${
            record?.regionProportion?.maxProportionNumberValue
          }${MAX_OPEN_CLOSE[record?.regionProportion?.maxOpenOrCloseRange] || ']'}`;
        }
        const mapProportion = {
          BY_PRICE_TABLE: '分离定价',
          BY_PARAMETER_TABLE: '按参数表',
          BY_FIXED_RATION:
            record.proportionNumberValue !== null ? record.proportionNumberValue : '-',
          BY_RANGE_RATION: rangeProportion,
        };
        const finValue =
          record?.calculationEnum === 'PROPORTIONALLY'
            ? mapProportion[record?.proportionNumberEnum]
            : rangeProportion;
        // 不是分离定价展示
        return !record?.whetherByPriceTable ? finValue : '-';
      },
    },
    {
      title: '收取方式',
      dataIndex: 'chargeMethodEnum',
      valueEnum: {
        ON_LINE: '线上',
        OFF_LINE: '线下',
      },
    },
    {
      title: '返还节点',
      dataIndex: 'returnNodeEnum',
      valueEnum: {
        NO: '不返还',
      },
    },
    {
      title: '抵扣项',
      dataIndex: 'deductionEnum',
      valueEnum: {
        NOT_DEDUCTIBLE: '不抵扣',
        PER_TERM_REPAY_DESC: '每期应还(倒序)',
        LEASE_INTEREST_AND_ALL: '手续费，保证金，每期应还（顺序）',
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      render: (_, row: { expenseItem: string }) => {
        return (
          <Button
            onClick={() => {
              // console.log(row, '当前行数据', costConfigTableNewData, curRowTemp);
              // 编辑的一些字段不在表格中存在，但是详情需要展示，所以不用row,直接从源数据取
              setCurRow(row);
              setOptionType('SHOW');
              setOptVisibleConfigFee(true);
            }}
            type="link"
            // disabled={row?.expenseItem === 'LEASE_IN_ADVANCE_LIQUIDATED_DAMAGES'} // 提前结清时
          >
            查看详情
          </Button>
        );
      },
    },
  ];

  return (
    <Card title="费用配置表" className={globalStyle.mt30}>
      <ProTable
        rowKey="expenseItem"
        columns={columnsConfig}
        dataSource={costConfigTableNewData}
        scroll={{ x: 'max-content' }}
        pagination={false}
        toolBarRender={false}
        search={false}
      />
      {/* {JSON.stringify(costConfigTableNewData)} */}
      {/* 配置表格 */}
      <FeeConfigModal
        modalVisible={optVisibleConfigFee}
        curRow={curRow}
        curOptionType={curOptionType}
        setOptionType={(type) => {
          setOptionType(type);
        }}
        initialWhetherByPriceTableData={{
          BAIL: leaseBailCostParameterDTO,
          LEASE_INTEREST: leaseInterestCostParameterDTO,
        }}
        //  formCur={formEdit}
        onOk={async () => {
          setOptVisibleConfigFee(false);
          //  props.refresh();
        }}
        id={data.id}
        classification={data.classification}
        repaymentTerm={repaymentTerm}
        initialRepaymentTerm={initialRepaymentTerm}
        onVisibleChange={setOptVisibleConfigFee}
        modifyLeaseFeeConfigData={modifyLeaseFeeConfigDataFromPop}
      />
    </Card>
  );
};

export default React.memo(LeaseFeeConfig);
