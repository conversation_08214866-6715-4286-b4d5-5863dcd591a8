import React, { useEffect } from 'react';
import { Form, Row, Col, Input, Radio, Tooltip, Select } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ProFormDependency } from '@ant-design/pro-form';

type BillProps = {
  billData?: any;
  name?: string;
};

const Bill: React.FC<BillProps> = ({ billData, name }) => {
  // const { billData } = props;
  const [form] = Form.useForm();

  const iconStyle = { marginLeft: '20px' };

  useEffect(() => {
    // console.log(billData);
    form.setFieldsValue(billData);
  }, [billData]);
  return (
    <>
      <Form
        form={form}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
        labelAlign="right"
        name={name}
      >
        <Row gutter={40}>
          <Col span={12}>
            <Form.Item name="generateBill" label="是否生成账单">
              <Radio.Group disabled>
                <Radio value>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          {billData?.generateBill && (
            <>
              {/* <Col span={2} /> */}
              <Col span={12}>
                <Form.Item name="billCycle" label="账单周期">
                  {/* <DatePicker  picker="month"/> */}
                  {/* <Input /> */}
                  <Select
                    placeholder="自然月"
                    disabled
                    // onChange={onGenderChange}
                  >
                    <Select.Option value="NATURAL_MOON">自然月</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              {/* <Col span={2} /> */}
              <Col span={12}>
                {/* <Form.Item > */}
                {/* <div style={{ display: 'inline-block', marginTop: '5px' }}>T+</div> */}
                <Form.Item
                  label="账单日"
                  name="billDay"
                  // style={{ display: 'inline-block', width: '80%', marginLeft: 20 }}
                >
                  <Input
                    addonBefore="T+"
                    placeholder=""
                    disabled
                    suffix={
                      <>
                        <span>日</span>
                        <Tooltip
                          placement="topLeft"
                          title="T为默认账单周期的最后一天，例如31日为账单周期的最后一天，则次月1日"
                        >
                          <QuestionCircleOutlined style={iconStyle} />
                        </Tooltip>
                      </>
                    }
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="latestBillConfirmationDate"
                  label="最晚确认账单日"
                  // style={{ display: 'inline-block', width: '80%', marginLeft: 20 }}
                >
                  <Input
                    addonBefore="T+"
                    placeholder=""
                    disabled
                    suffix={
                      <>
                        <span>日</span>
                        <Tooltip
                          placement="topLeft"
                          title="T为默认账单周期的最后一天，例如31日为账单周期的最后一天，则次月8日"
                        >
                          <QuestionCircleOutlined style={iconStyle} />
                        </Tooltip>
                      </>
                    }
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="账单起息日" name="billInterestDay">
                  <Input
                    addonBefore="T+"
                    placeholder=""
                    disabled
                    suffix={
                      <>
                        <span>日</span>
                        <Tooltip
                          placement="topLeft"
                          title="T为默认账单周期的最后一天，例如31日违章单周期的最后一天，则次月8日"
                        >
                          <QuestionCircleOutlined style={iconStyle} />
                        </Tooltip>
                      </>
                    }
                  />
                </Form.Item>
              </Col>

              <ProFormDependency name={['repayDateCalculateEnum']}>
                {({ repayDateCalculateEnum }) => {
                  return repayDateCalculateEnum === 'CUSTOMIZE_REPAY_DATE' ? (
                    <Col span={12}>
                      <Form.Item label="默认还款日期" name="repaymentDay">
                        <Input
                          placeholder=""
                          disabled
                          addonBefore="D+"
                          suffix={
                            <>
                              <span>日</span>
                              <Tooltip
                                placement="topLeft"
                                title="T为默认账单周期的最后一天，例如31日为账单周期的最后一天，则次月28日"
                              >
                                <QuestionCircleOutlined style={iconStyle} />
                              </Tooltip>
                            </>
                          }
                        />
                      </Form.Item>
                    </Col>
                  ) : (
                    ''
                  );
                }}
              </ProFormDependency>
              <Col span={12}>
                <Form.Item label="账单还款日计算方式" name="repayDateCalculateEnum">
                  <Radio.Group disabled>
                    <Radio value="FIX_REPAY_DATE">固定还款日</Radio>
                    <Radio value="CUSTOMIZE_REPAY_DATE">自定义</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="confirm" label="账单是否需要用户确认">
                  <Radio.Group disabled>
                    <Radio value>是</Radio>
                    <Radio value={false}>否</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </>
          )}
        </Row>
      </Form>
    </>
  );
};

export default Bill;
