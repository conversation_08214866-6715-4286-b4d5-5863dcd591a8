import {
  BASE_PROPORTION_NO_BRACKETS,
  EXPENSE_ITEMS,
  MAX_OPEN_CLOSE,
  MIN_OPEN_CLOSE,
} from '@/enums';
import globalStyle from '@/global.less';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { <PERSON><PERSON>, Card } from 'antd';
import React, { memo, useState } from 'react';
import type { IfeeConfigItem } from '../type';
import {
  EcalculationEnum,
  EcollectionMethodEnum,
  EfundersEnum,
  EreceiverEnum,
  EreceivingNode,
} from '../type';
import InsuranceEditFeeConfig from './components/InsuranceEditFeeConfig';

const InsuranceFeeConfig = () => {
  const { costConfigDTO } = useModel('Product.insuranceProduct', (model) => {
    return {
      costConfigDTO: model?.costConfigDTO,
    };
  });
  const [visible, setVisible] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>({});
  const columnsConfig: ProColumns<IfeeConfigItem>[] = [
    {
      title: '费用项',
      dataIndex: 'expenseItem',
      valueEnum: EXPENSE_ITEMS,
    },
    {
      title: '收取节点',
      dataIndex: 'receivingNodeEnum',
      valueEnum: EreceivingNode,
    },
    {
      title: '出资方',
      dataIndex: 'fundersEnum',
      valueEnum: EfundersEnum,
    },
    {
      title: '收取方',
      dataIndex: 'receiverEnum',
      valueEnum: EreceiverEnum,
    },
    {
      title: '计收方式',
      dataIndex: 'collectionMethodEnum',
      valueEnum: EcollectionMethodEnum,
    },
    {
      title: '计算方式',
      dataIndex: 'calculationEnum',
      valueEnum: EcalculationEnum,
    },
    {
      title: '金额范围',
      dataIndex: 'amountMax',
      render: (_: any, record: any) => {
        const { regionAmount = {}, amount, term } = record;
        console.log('amount', amount);
        const { amountMin, amountMax } = regionAmount || {};
        if (amount) {
          return amount;
        }
        if (term !== undefined) {
          return term;
        }
        return (
          <div>
            {amountMin}-{amountMax}
          </div>
        );
      },
    },
    {
      title: '基数',
      dataIndex: 'baseEnum',
      valueEnum: BASE_PROPORTION_NO_BRACKETS,
    },
    {
      title: '比例数',
      dataIndex: 'proportionNumberEnum',
      render: (_: any, record: any) => {
        let rangeProportion = '';
        if (record?.regionProportion?.maxProportionNumberValue) {
          rangeProportion = `${
            MIN_OPEN_CLOSE[record?.regionProportion?.minOpenOrCloseRange] || '['
          }${record?.regionProportion?.minProportionNumberValue},${
            record?.regionProportion?.maxProportionNumberValue
          }${MAX_OPEN_CLOSE[record?.regionProportion?.maxOpenOrCloseRange] || ']'}`;
        }
        const mapProportion = {
          BY_PRICE_TABLE: '分离定价',
          BY_PARAMETER_TABLE: '按参数表',
          BY_FIXED_RATION:
            record.proportionNumberValue !== null ? record.proportionNumberValue : '-',
          BY_RANGE_RATION: rangeProportion || '-',
        };
        const finValue =
          record?.calculationEnum === 'PROPORTIONALLY'
            ? mapProportion[record?.proportionNumberEnum]
            : '-';
        // 不是分离定价展示
        return !record?.whetherByPriceTable ? finValue : '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      render: (_, record) => {
        return (
          <Button
            onClick={() => {
              console.log('record', record);
              setCurrentRow({ ...record });
              setVisible(true);
            }}
            type="link"
          >
            查看详情
          </Button>
        );
      },
    },
  ];
  return (
    <Card title="费用配置表" className={globalStyle.mt30}>
      <ProTable
        rowKey="expenseItem"
        columns={columnsConfig}
        dataSource={costConfigDTO || []}
        scroll={{ x: 'max-content' }}
        pagination={false}
        toolBarRender={false}
        search={false}
      />
      <InsuranceEditFeeConfig visible={visible} setVisible={setVisible} currentRow={currentRow} />
    </Card>
  );
};

export default memo(InsuranceFeeConfig);
