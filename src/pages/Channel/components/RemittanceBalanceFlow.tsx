import { getTransationList } from '@/pages/CarInsuranceCustomer/services';
import { EyeInvisibleTwoTone, EyeTwoTone } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-table';
import { Button, Card, message, Tooltip } from 'antd';
import React, { memo, useRef, useState } from 'react';
import './index.less';

import { getWalletAccountInfo } from '@/components/WalletAccountInfo/services';
import ClaimRechargeModal from '@/pages/CarInsuranceCustomer/components/ClaimRechargeModal';
import ClaimRecordModal from '@/pages/CarInsuranceCustomer/components/ClaimRecordModal';

interface IRemittanceBalanceFlowProps {
  channelName: string;
  accountName: string | undefined;
  accountInfo: {
    mappingAccountS: AccountInfoIF;
    [key: string]: any;
  } | null;
}

interface RemittanceFlowColumns {
  id: number;
  type: number;
  amount: number;
  updateTime: string;
  remark: string;
  bizTypeName: string;
  subBizTypeName: string;
}

interface AccountInfoIF {
  accountId: string | undefined;
  externalOwnerId: string | undefined;
  externalAccountId: string | undefined;
  accountName: string | undefined;
  bankName: string | undefined;
  bankNo: string | undefined;
}

const RemittanceBalanceFlow: React.FC<IRemittanceBalanceFlowProps> = ({
  channelName,
  accountName,
  accountInfo,
}) => {
  const claimRechargeRef = useRef<any>(null);
  const claimRecordRef = useRef<any>(null);

  const [showEyeIcon, setShowEyeIcon] = useState(false);
  const [isGetBalance, setIsGetBalance] = useState(false);
  const [loading, setLoading] = useState(false);
  const [welletInfo, setWelletInfo] = useState<any>(null);

  const columns: ProColumns<RemittanceFlowColumns>[] = [
    {
      title: '类型',
      dataIndex: 'bizTypeName',
      render: (_, record) => {
        return <span>{`${record?.bizTypeName}-${record?.subBizTypeName}`}</span>;
      },
    },
    {
      title: '金额（元）',
      dataIndex: 'amount',
      valueType: 'money',
    },
    {
      title: '更新时间',
      dataIndex: 'createdAt',
      valueType: 'dateTime',
    },
    {
      title: '银行流水号/还款单号',
      dataIndex: 'flowNo',
    },
  ];

  const request = async (params: any) => {
    if (!accountInfo?.mappingAccountS?.externalOwnerId) {
      return {
        data: [],
        total: 0,
        success: true,
      };
    }
    // 获取流水
    const res = await getTransationList({
      ...params,
      externalOwnerId: accountInfo?.mappingAccountS?.externalOwnerId,
      secondProductCode: '0201',
    });
    return {
      data: res.data,
      success: true,
      total: res.total,
    };
  };

  // 获取余额
  const getBalance = () => {
    if (!accountInfo?.mappingAccountS?.externalOwnerId) {
      message.error('专属充值账户不存在');
      return;
    }
    setLoading(true);
    getWalletAccountInfo({
      externalOwnerId: accountInfo?.mappingAccountS?.externalOwnerId,
      secondProductCode: '0201',
    })
      .then((res) => {
        setWelletInfo(res?.data);
        setIsGetBalance(true);
        setShowEyeIcon(true);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Card
      title="汇款余额流水"
      extra={[
        <Button
          key="claimRecord"
          type="primary"
          onClick={() => {
            claimRecordRef.current?.show();
          }}
        >
          认领记录
        </Button>,
        <Button
          key="recharge"
          type="primary"
          style={{ marginLeft: 20 }}
          onClick={() => {
            claimRechargeRef.current?.show();
          }}
        >
          认领充值
        </Button>,
      ]}
      tabBarExtraContent={
        <Button
          type="primary"
          onClick={() => {
            claimRecordRef.current?.show();
          }}
        >
          认领记录
        </Button>
      }
      className="claim-record-card"
    >
      <ProTable<RemittanceFlowColumns>
        columns={columns}
        request={request}
        search={false}
        toolBarRender={false}
        rowKey="flowNo"
        scroll={{ x: 'max-content' }}
        pagination={{
          defaultPageSize: 10,
          defaultCurrent: 1,
          showQuickJumper: true,
          showSizeChanger: true,
        }}
      />
      {/* 专属信息 */}
      <div className="claim-record-info">
        <div className="claim-record-info-item">
          <div>专属充值账号：</div>
          <Tooltip
            title={`${accountInfo?.mappingAccountS?.accountName}-${accountInfo?.mappingAccountS?.bankNo}-${accountInfo?.mappingAccountS?.bankName}`}
          >
            {accountInfo?.mappingAccountS?.accountName
              ? `${accountInfo?.mappingAccountS?.accountName}...`
              : '-'}
          </Tooltip>
        </div>
        <div className="claim-record-info-item">
          钱包ID：{accountInfo?.mappingAccountS?.externalOwnerId || '-'}
        </div>
        <div className="claim-record-info-item">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>
              账户余额: {isGetBalance && showEyeIcon ? `¥${welletInfo?.totalAmount}` : '******'}
            </span>
            <Button
              type="link"
              loading={loading}
              className="eye-icon"
              onClick={() => {
                if (!isGetBalance) {
                  getBalance();
                }
                if (isGetBalance) {
                  setShowEyeIcon(!showEyeIcon);
                }
              }}
            >
              {showEyeIcon ? <EyeTwoTone /> : <EyeInvisibleTwoTone />}
            </Button>
          </div>
        </div>
      </div>
      {/* 认领充值 */}
      <ClaimRechargeModal
        targetRemitAccountName={channelName}
        claimRechargeRef={claimRechargeRef}
        externalOwnerId={accountInfo?.mappingAccountS?.externalOwnerId}
        accountName={accountName ?? ''}
        productSecondCode="0201"
      />
      {/* 认领记录 */}
      <ClaimRecordModal
        claimRecordRef={claimRecordRef}
        externalOwnerId={accountInfo?.mappingAccountS?.externalOwnerId}
      />
    </Card>
  );
};

export default memo(RemittanceBalanceFlow);
