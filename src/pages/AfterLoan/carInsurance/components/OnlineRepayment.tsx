/**
 * 订单纬度 - 线下还款
 */
import type { ExportExcelParams } from '@/utils/xlsx-export';
import { exportExcelByXLSX, getTableData } from '@/utils/xlsx-export';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProFormDependency, ProTable } from '@ant-design/pro-components';
import { useDebounceFn } from 'ahooks';
import { Button, Col, Collapse, Divider, Form, Input, message, Modal, Radio, Row, Tag } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import type { PropsWithChildren } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { useAccess } from 'umi';
import {
  billInfo,
  billSettleInfo,
  getExpectRepayAmountListWithTip,
  getReferenceAmount,
  submitBillRepay,
  submitBillSettleRepay,
} from '../services';
import type { IbillInfo, IbillListItem, Idimension } from '../types';
import { onlineExportTableHeader } from './data';
import './OnlineRepayment.less';
import RedText from './RedText';
import RemissionEditTable from './RemissionEditTable';

type Props = {
  selectedRows: IbillListItem[];
  dimension: Idimension;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  //
  open: boolean;
  setOpen: (visible: boolean) => void;
  onlineBillInfoData: any;
  planAmount: any;
  //
  setQrData: (data: any) => void;
  setQrVisible: (visible: boolean) => void;
};

enum REPAY_TYPE {
  NORMAL_REPAY = 1, // 普通还款
  EARLY_SETTLE = 2, // 提前结清
}

const Page = (props: PropsWithChildren<Props>) => {
  const {
    selectedRows,
    dimension,
    actionRef,
    open,
    setOpen,
    onlineBillInfoData,
    planAmount,
    setQrData,
    setQrVisible,
  } = props;

  const [billInfoData, setBillInfoData] = useState<IbillInfo>({});
  // 普通还款下，计划还款金额参考值
  const [planRepayAmount, setPlanRepayAmount] = useState({
    // 还款至下一期结清
    repayNextTermAmount: 0,
    // 还款至逾期结清
    repayOverdueAmount: 0,
  });
  const [exportLoading, setExportLoading] = useState(false);

  const access = useAccess();
  const [form] = Form.useForm();

  useEffect(() => {
    setBillInfoData(onlineBillInfoData);
    setPlanRepayAmount(planAmount);
  }, []);
  // 计算减免总金额
  function calculateRemissionAmount() {
    const formValues = form.getFieldsValue();
    const {
      remissionPrincipal = 0,
      remissionInterest = 0,
      remissionPenalty = 0,
      remissionAdvanceSettleLiquidatedDamages = 0,
      remissionDelayAmount = 0,
    } = formValues;

    const amount = new BigNumber(remissionPrincipal)
      .plus(remissionInterest)
      .plus(remissionPenalty)
      .plus(remissionAdvanceSettleLiquidatedDamages)
      .plus(remissionDelayAmount)
      .toNumber();
    return amount;
  }

  // 还款方式
  const [repayType, setRepayType] = useState(REPAY_TYPE.NORMAL_REPAY);
  const [needRemission, setNeedRemission] = useState(0);
  const [initInfoLoading, setInitInfoLoading] = useState(false);
  //
  const remissionEditRef = useRef();

  //
  const [expectRepayAmountListLoading, setExpectRepayAmountListLoading] = useState(false);
  // 计算预计入账金额列表
  const initExpectRepayAmountList = async () => {
    console.log('initExpectRepayAmountList');
    if (repayType === REPAY_TYPE.EARLY_SETTLE) {
      // 提前结清
      // setBillInfoData((prev)=>{
      //   return {
      //     ...prev,
      //     billRspDTOList: prev?.billRspDTOList.map((item, index) => {
      //       return {
      //        ...item,
      //         expectRepayAmount: data.billInfoList[index].expectRepayAmount,
      //       }
      //     })
      //   }
      // });
      setExpectRepayAmountListLoading(false);
      return;
    }
    const formValues = form.getFieldsValue();
    if (formValues?.repayAmount === undefined) {
      // message.error('还款金额不能为空');
      setExpectRepayAmountListLoading(false);
      return;
    }
    // 监测表单
    try {
      const errors = form.getFieldsError([
        'repayAmount',
        'remissionPrincipal',
        'remissionInterest',
        'remissionPenalty',
        'remissionAdvanceSettleLiquidatedDamages',
        'remissionDelayAmount',
      ]);
      if (errors && errors.some((item) => item?.errors?.length > 0)) {
        console.log('errors', errors);
        // 清除
        setBillInfoData((prev) => {
          const { billRspDTOList } = prev;
          const tempList = billRspDTOList?.map((item, index) => {
            return {
              ...item,
              normalExpectRepayAmount: undefined,
            };
          });
          return {
            ...prev,
            billRspDTOList: tempList,
          };
        });
        setExpectRepayAmountListLoading(false);
        return;
      }
    } catch (e) {}
    setExpectRepayAmountListLoading(true);
    const data = await getExpectRepayAmountListWithTip({
      billInfoList: billInfoData?.billRspDTOList || [],
      remissionList: [
        {
          // remissionItemNo: [],
          id: 1,
          remissionPrincipal: formValues?.remissionPrincipal || 0,
          remissionInterest: formValues?.remissionInterest || 0,
          remissionPenalty: formValues?.remissionPenalty || 0,
          remissionAdvanceSettleLiquidatedDamages:
            formValues?.remissionAdvanceSettleLiquidatedDamages || 0,
          remissionDelayAmount: formValues?.remissionDelayAmount || 0,
        },
      ],
      repayAmount: formValues?.repayAmount,
    }).catch(() => {
      // 清除
      setBillInfoData((prev) => {
        const { billRspDTOList } = prev;
        const tempList = billRspDTOList?.map((item, index) => {
          return {
            ...item,
            normalExpectRepayAmount: undefined,
          };
        });
        return {
          ...prev,
          billRspDTOList: tempList,
        };
      });
    });
    setExpectRepayAmountListLoading(false);
    console.log('data11111', data);
    try {
      if (data && data?.billRspDTOList) {
        setBillInfoData((prev) => {
          const { billRspDTOList } = prev;
          console.log('billRspDTOList', billRspDTOList);
          const tempList = billRspDTOList?.map((item, index) => {
            console.log('item111', item, index, data?.billRspDTOList[index]?.expectRepayAmount);
            console.log('normalExpectRepayAmount', data?.billRspDTOList[index]?.expectRepayAmount);
            return {
              ...item,
              normalExpectRepayAmount: data?.billRspDTOList[index]?.expectRepayAmount || 0,
            };
          });
          console.log('tempList', tempList);
          return {
            ...prev,
            billRspDTOList: tempList,
          };
        });
      }
    } catch (e) {}
  };
  const {
    run,
    cancel: cancelInitExpectRepayAmountListDebounce,
  } = useDebounceFn(initExpectRepayAmountList, { wait: 1000 });
  const initExpectRepayAmountListDebounce = async () => {
    if (repayType === REPAY_TYPE.NORMAL_REPAY) {
      setExpectRepayAmountListLoading(true);
    }
    run();
  };

  // const repayAmount = Form
  // 还款金额参考值
  const [referAmountLoading, setReferAmountLoading] = useState(false);
  const initReferenceAmount = async (rowList: any) => {
    //
    const list = rowList || billInfoData?.billRspDTOList;
    // 普通还款还需要请求计划还款金额推荐值
    const billNoList = list?.map((item) => item.billNo);
    // const orderNoList = list?.map((item) => item?.orderNo);
    // const subjectMatterNoList = list?.map((item) => item?.subjectMatterNo);
    // 订单号对应的车辆号
    const order2subjectMatterNoMap = {};
    list?.forEach((item) => {
      // order2subjectMatterNoMap[item?.orderNo] = item?.subjectMatterNo;
      if (order2subjectMatterNoMap[item?.orderNo]) {
        order2subjectMatterNoMap[item?.orderNo]?.push(item?.subjectMatterNo);
      } else {
        order2subjectMatterNoMap[item?.orderNo] = [item?.subjectMatterNo];
      }
    });
    //
    setReferAmountLoading(true);
    const planData = await getReferenceAmount({
      billNoList,
      // orderNoList,
      // subjectMatterNoList,
      order2subjectMatterNoMap,
    }).catch(() => {});
    setReferAmountLoading(false);
    console.log('planData', planData);
    if (planData && planData?.data) {
      const { repayNextTermAmount, repayOverdueAmount } = planData?.data || {};
      setPlanRepayAmount({ repayNextTermAmount, repayOverdueAmount });
    }
  };
  // 普通还款，初始化接口信息
  const initGeneralRepayInfo = async () => {
    if (!selectedRows?.length) {
      message.error('还未勾选任何数据');
      return;
    }
    const billNoList = selectedRows?.map((item) => item.billNo);
    setInitInfoLoading(true);
    const data = await billInfo({
      billNoList,
      dimension,
      secondaryClassification: '0303',
      settleType: false,
      // 线上还款
      onlineRepay: true,
    }).catch(() => {});

    if (data) {
      setBillInfoData(data);
      form.setFieldsValue({
        repayAmount: undefined,
      });
      await initReferenceAmount(data?.billRspDTOList).catch(() => {});
    }
    await initExpectRepayAmountListDebounce().catch(() => {});
    setInitInfoLoading(false);
    return data;
  };
  // 提前结清，初始化接口信息
  const initEarlySettleInfo = async () => {
    if (!selectedRows?.length) {
      message.error('还未勾选任何数据');
      return;
    }
    setInitInfoLoading(true);
    const billNoList = selectedRows.map((item) => item.billNo);
    const data = await billSettleInfo({
      billNoList,
      dimension,
      secondaryClassification: '0303',
      settleType: true,
      // 线上还款
      onlineRepay: true,
    }).catch(() => {});
    setInitInfoLoading(false);
    if (data) {
      setBillInfoData(data);
      // 提前结清，返显计划还款金额，且不可修改
      // 计算列表未还总额加总
      const allTotalAmountUnpaid = data?.billRspDTOList?.reduce((pre, cur) => {
        // return pre + cur?.totalAmountUnpaid;
        console.log('pre', pre);
        return pre?.plus(cur?.totalAmountUnpaid);
      }, BigNumber(0));
      form.setFieldsValue({
        repayAmount: allTotalAmountUnpaid?.toString(),
      });
    }
    return data;
  };
  // 还款
  function getAmount() {
    const init = {
      expectRepayAmount: 0,
      // 列表未还总额加总，为普通还款下，计划还款金额最大值
      totalAmountUnpaid: 0,
      //
      totalPrincipalUnpaid: 0,
      totalInterestUnpaid: 0,
      totalOverduePenaltyUnpaid: 0,
      totalBreach: 0,
      totalLate: 0,
    };
    if (billInfoData?.billRspDTOList?.length) {
      return billInfoData?.billRspDTOList?.reduce((pre: any, cur) => {
        const {
          expectRepayAmount,
          totalAmountUnpaid,
          totalPrincipalUnpaid,
          totalInterestUnpaid,
          totalOverduePenaltyUnpaid,
          totalBreach,
          totalLate,
        } = cur;
        const _expectRepayAmount = new BigNumber(pre.expectRepayAmount)
          .plus(expectRepayAmount)
          .toNumber();
        const _totalAmountUnpaid = new BigNumber(pre.totalAmountUnpaid)
          .plus(totalAmountUnpaid)
          .toNumber();
        const _totalPrincipalUnpaid = new BigNumber(pre.totalPrincipalUnpaid)
          .plus(totalPrincipalUnpaid)
          .toNumber();
        const _totalInterestUnpaid = new BigNumber(pre.totalInterestUnpaid)
          .plus(totalInterestUnpaid)
          .toNumber();
        const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
          .plus(totalOverduePenaltyUnpaid)
          .toNumber();
        const _totalBreach = new BigNumber(pre.totalBreach).plus(totalBreach).toNumber();
        const _totalLate = new BigNumber(pre.totalLate).plus(totalLate).toNumber();
        return {
          expectRepayAmount: isNaN(_expectRepayAmount) ? 0 : _expectRepayAmount,
          totalAmountUnpaid: isNaN(_totalAmountUnpaid) ? 0 : _totalAmountUnpaid,
          totalPrincipalUnpaid: isNaN(_totalPrincipalUnpaid) ? 0 : _totalPrincipalUnpaid,
          totalInterestUnpaid: isNaN(_totalInterestUnpaid) ? 0 : _totalInterestUnpaid,
          totalOverduePenaltyUnpaid: isNaN(_totalOverduePenaltyUnpaid)
            ? 0
            : _totalOverduePenaltyUnpaid,
          totalBreach: isNaN(_totalBreach) ? 0 : _totalBreach,
          totalLate: isNaN(_totalLate) ? 0 : _totalLate,
        };
      }, init);
    } else {
      return init;
    }
  }

  //
  //
  const setRepayTypeWithEffect = (val: any) => {
    setRepayType(val);
    if (val === 1) {
      initGeneralRepayInfo();
    } else if (val === 2) {
      initEarlySettleInfo();
    }
  };

  //
  const [deleteLoading, setDeleteLoading] = useState(false);
  const handleDeleteAction = async (record) => {
    const { id } = record;
    //
    setDeleteLoading(true);
    try {
      const list = billInfoData?.billRspDTOList;
      const temp = list?.filter((item) => item.id !== id);
      setBillInfoData((prev) => {
        return {
          ...prev,
          billRspDTOList: temp,
        };
      });
      await initReferenceAmount(temp).catch(() => {});
    } catch (e) {}
    setDeleteLoading(false);
  };

  // 提交状态
  const [submitLoading, setSubmitLoading] = useState(false);
  // 处理表单提交
  const handleRepaySubmit = async () => {
    // 还款方式: 普通还款
    if (repayType === REPAY_TYPE.NORMAL_REPAY) {
      if (!access.hasAccess('bill_submit_repay_info_postLoanMng_afterLoanList')) {
        message.error('没有权限提交');
        return;
      }
    }
    // 还款方式: 提前结清
    else if (repayType === REPAY_TYPE.EARLY_SETTLE) {
      // 是否有权限提交
      if (!access.hasAccess('biz_billList_earlySettlement_commit_button')) {
        message.error('没有权限提交');
        return;
      }
    }
    //
    // 检查表单
    try {
      await form?.validateFields();
    } catch {
      message.error('请检查表单');
      return Promise.reject();
    }
    // 参数组装
    const formValues = form?.getFieldsValue() || {};
    // 还款金额最小值
    if (
      BigNumber(formValues?.repayAmount).isLessThan(0) ||
      BigNumber(formValues?.repayAmount).isEqualTo(0)
    ) {
      return message.error('计划还款金额必须为>0的数字，可支持到小数点后2位');
    }
    // 计划还款金额+减免总金额 ≦ 所选所有车辆未还金额之和
    const remissionTotalAmount = new BigNumber(formValues?.remissionPrincipal)
      .plus(formValues?.remissionInterest)
      .plus(formValues?.remissionPenalty)
      .plus(formValues?.remissionAdvanceSettleLiquidatedDamages)
      .plus(formValues?.remissionDelayAmount)
      .toNumber();
    if (
      BigNumber(formValues?.repayAmount)
        .plus(remissionTotalAmount)
        ?.isGreaterThan(getAmount()?.totalAmountUnpaid)
    ) {
      return message.error('计划还款金额+减免总金额 需 ≦ 所选所有车辆未还金额之和');
    }
    // 4.没有任何一台车辆预期入账金额为0
    const hasAnyZeroAmount: boolean = billInfoData?.billRspDTOList?.some((item) => {
      if (repayType === REPAY_TYPE.NORMAL_REPAY) {
        return (
          item?.normalExpectRepayAmount === undefined ||
          BigNumber(item?.normalExpectRepayAmount).isEqualTo(0)
        );
      } else if (repayType === REPAY_TYPE.EARLY_SETTLE) {
        return (
          item?.totalAmountUnpaid === undefined || BigNumber(item?.totalAmountUnpaid).isEqualTo(0)
        );
      }
    });
    if (hasAnyZeroAmount) {
      return message.error('存在预期入账金额为0的车辆，请调整计划还款金额或删除对应车辆');
    }
    // 5. 所有车辆还款状态必须为：还款中 / 逾期
    // const hasAnyNotRepayingOrOverdue: boolean = billInfoData?.billRspDTOList?.some((item) => {
    //   return item?.repayStatus !== 1 && item?.repayStatus !== 2;
    // });
    // if (hasAnyNotRepayingOrOverdue) {
    //   return message.error('部分车辆存在待审核的线下还款，当前无法操作还款');
    // }
    // 还款金额最大值
    if (BigNumber(formValues?.repayAmount).isGreaterThan(50000)) {
      return message.error('单次扫码支付仅支持50000以内金额');
    }
    //

    //
    // 计划还款金额减去减免金额
    const data: any = {
      remissionList: [
        {
          remissionPrincipal: formValues?.remissionPrincipal || 0,
          remissionInterest: formValues?.remissionInterest || 0,
          remissionPenalty: formValues?.remissionPenalty || 0,
          remissionAdvanceSettleLiquidatedDamages:
            formValues?.remissionAdvanceSettleLiquidatedDamages || 0,
          remissionDelayAmount: formValues?.remissionDelayAmount || 0,
        },
      ],
      billInfoList: billInfoData?.billRspDTOList,
      // 普通还款为表单值，提前结清为repayAmount减去减免（减免已在表单中计算）
      repayAmount: formValues?.repayAmount,
      bankAmount: formValues?.repayAmount,
      // 取当天
      bankAmountDate: dayjs().format('YYYY-MM-DD'),
      dimension,
      //
      settleType: repayType === REPAY_TYPE.EARLY_SETTLE,
      //
      remitType: 4,
      //
      customerPhone: formValues?.customerPhone,
      //
      onlineRepay: true,
    };
    //
    let res;
    setSubmitLoading(true);
    if (repayType === REPAY_TYPE.NORMAL_REPAY) {
      res = (await submitBillRepay(data).catch(() => {})) || {};
    } else if (repayType === REPAY_TYPE.EARLY_SETTLE) {
      const remissionT = formValues?.remission?.map(({ billNo, ...rest }) => {
        const billNoT = billNo?.split('-')[0]; //账单号
        const remissionItemNo = billNo?.split('-')[1]; //车架号
        return { billNo: billNoT, remissionItemNo, ...rest };
      });
      data.remissionList = needRemission ? remissionT : [];
      res = (await submitBillSettleRepay(data).catch(() => {})) || {};
    }
    setSubmitLoading(false);
    console.log('res', res);
    if (res?.ret === 0) {
      message.success('提交成功');
      if (res?.data?.h5RepayUrl) {
        setQrData({
          h5RepayUrl: res?.data?.h5RepayUrl,
          businessNo: res?.data?.businessNo,
          accountName: billInfoData?.accountName,
        });
        setQrVisible(true);
      }
      setOpen(false);
      actionRef.current?.reload();
    }
  };

  const OnlineRepaymentColumns: ProColumns[] = [
    {
      title: '车辆',
      dataIndex: 'billNo',
      render: (_: any, record: any) => {
        return (
          <>
            <div>{record?.plateNo}</div>
            <div>{record?.subjectMatterNo}</div>
          </>
        );
      },
    },
    {
      title: '客户名称',
      dataIndex: 'accountName',
    },
    {
      title: '预期入账金额',
      dataIndex: 'expectRepayAmount',
      render: (_: any, record: any) => {
        console.log('record', record.normalExpectRepayAmount);
        const temp =
          repayType === REPAY_TYPE.NORMAL_REPAY
            ? record?.normalExpectRepayAmount
            : record?.totalAmountUnpaid;
        return (
          <>
            <div className={BigNumber(temp).isEqualTo(0) ? 'red-text' : 'primary-text'}>
              {temp ? `￥${temp}` : '-'}
            </div>
          </>
        );
      },
    },
    {
      title: '未还总额',
      dataIndex: 'totalAmountUnpaid',
      render: (_: any, record: any) => {
        return <>￥{record?.totalAmountUnpaid || 0}</>;
      },
    },
    {
      title: '未还本金',
      dataIndex: 'totalPrincipalUnpaid',
      render: (_: any, record: any) => {
        return <>￥{record?.totalPrincipalUnpaid || 0}</>;
      },
    },
    {
      title: '未还利息',
      dataIndex: 'totalInterestUnpaid',
      render: (_: any, record: any) => {
        return <>￥{record?.totalInterestUnpaid || 0}</>;
      },
    },
    {
      title: '未还罚息',
      dataIndex: 'totalOverduePenaltyUnpaid',
      render: (_: any, record: any) => {
        return <>￥{record?.totalOverduePenaltyUnpaid || 0}</>;
      },
    },
    {
      title: '未还违约金',
      dataIndex: 'totalBreach',
      render: (_: any, record: any) => {
        return <>￥{record?.totalBreach || 0}</>;
      },
    },
    {
      title: '未还滞纳金',
      dataIndex: 'totalLate',
      render: (_: any, record: any) => {
        return <>￥{record?.totalLate || 0}</>;
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_: any, record: any) => {
        const temp =
          repayType === REPAY_TYPE.NORMAL_REPAY
            ? record?.normalExpectRepayAmount
            : record?.totalAmountUnpaid;
        const showDeleteBtn: boolean = BigNumber(temp).isEqualTo(0);
        return (
          <>
            {showDeleteBtn ? (
              <Button
                type="link"
                disabled={deleteLoading}
                onClick={() => {
                  handleDeleteAction(record);
                }}
              >
                删除
              </Button>
            ) : (
              <p className="text-center">-</p>
            )}
          </>
        );
      },
    },
  ];

  // 客户名称
  const userNameList = billInfoData?.billRspDTOList?.map((item) => item?.accountName) || [];
  // 去重
  const uniqueUserNameList = Array.from(new Set(userNameList));
  //
  //
  useEffect(() => {
    console.log('open', open);
    if (open === false) {
      // initGeneralRepayInfo();
      form?.resetFields();
      setExpectRepayAmountListLoading(false);
      setInitInfoLoading(false);
      setSubmitLoading(false);
    }
  }, [open]);
  //
  // 提前结清-计算减免总金额
  function calculateRemissionAmountOfEditTable(remission: any) {
    return (
      remission?.reduce((pre, cur) => {
        const {
          remissionPrincipal = 0,
          remissionInterest = 0,
          remissionPenalty = 0,
          remissionAdvanceSettleLiquidatedDamages = 0,
          remissionDelayAmount = 0,
        } = cur;

        const amount = new BigNumber(remissionPrincipal)
          .plus(remissionInterest)
          .plus(remissionPenalty)
          .plus(remissionAdvanceSettleLiquidatedDamages)
          .plus(remissionDelayAmount)
          .toNumber();
        return new BigNumber(pre).plus(amount).toNumber();
      }, 0) || 0
    );
  }

  // 导出
  const handleExport = async () => {
    setExportLoading(true);
    const tableData = getTableData(onlineExportTableHeader, billInfoData?.billRspDTOList || []);
    const fileType = repayType === REPAY_TYPE.NORMAL_REPAY ? '还款' : '结清';
    const params: ExportExcelParams = {
      header: onlineExportTableHeader,
      fileName: `${fileType}账单-${dayjs().format('YYYY.MM.DD HH:mm:ss')}`,
      data: tableData,
    };
    try {
      exportExcelByXLSX(params);
    } catch {
      message.error('导出失败');
    } finally {
      // 等待一段时间再放开按钮
      setTimeout(() => {
        setExportLoading(false);
      }, 2e3);
    }
  };

  return (
    <>
      {/* 弹窗 */}
      <Modal
        open={open}
        title="还款/结清"
        onCancel={() => {
          setOpen(false);
        }}
        key={1}
        destroyOnClose={true}
        width={1000}
        footer={[
          <Button
            type="primary"
            disabled={initInfoLoading || expectRepayAmountListLoading}
            loading={submitLoading}
            key={1}
            onClick={() => {
              handleRepaySubmit().catch(() => {
                setSubmitLoading(false);
              });
            }}
          >
            二维码还款
          </Button>,
        ]}
      >
        <Form
          labelCol={{ span: 8 }}
          labelAlign="left"
          form={form}
          initialValues={{
            remissionPrincipal: '0',
            remissionInterest: '0',
            remissionPenalty: '0',
            remissionAdvanceSettleLiquidatedDamages: '0',
            remissionDelayAmount: '0',
            remission: [{ id: 1 }],
          }}
          onValuesChange={(changedValues) => {
            console.log('changedValues', changedValues);
            const changedKeys = Object.keys(changedValues || {});
            if (changedKeys?.length === 1 && changedKeys?.includes('customerPhone')) {
              return;
            }
            initExpectRepayAmountListDebounce();
          }}
        >
          <Divider className="divider-margin" />
          {/* 渠道名称 */}
          <Row>
            <Col span={3}>
              <div className="text-right">
                <span className="item-required-icon">*</span>渠道名称：
              </div>
            </Col>
            <Col span={19}>{billInfoData?.channelName}</Col>
          </Row>
          {/* 客户名称 */}
          <Row>
            <Col span={3}>
              <div className="text-right">
                <span className="item-required-icon">*</span>客户名称：
              </div>
            </Col>
            <Col span={19}>
              {uniqueUserNameList?.map((item, index) => {
                return (
                  <Tag color="processing" className="mbt-6" key={`${item}-${index}`}>
                    {item}
                  </Tag>
                );
              })}
            </Col>
          </Row>
          {/* 还款方式 */}
          <Row>
            <Col span={3}>
              <div className="text-right">
                <span className="item-required-icon">*</span>还款方式：
              </div>
            </Col>
            <Col span={9}>
              <Radio.Group
                value={repayType}
                disabled={initInfoLoading || expectRepayAmountListLoading}
                onChange={(e) => {
                  console.log(e?.target?.value);
                  setRepayTypeWithEffect(e?.target?.value);
                }}
                options={[
                  {
                    label: '普通还款',
                    value: REPAY_TYPE.NORMAL_REPAY,
                  },
                  {
                    label: '提前结清',
                    value: REPAY_TYPE.EARLY_SETTLE,
                  },
                ]}
              />
            </Col>
          </Row>
          {/* 是否减免 */}
          {repayType === REPAY_TYPE.EARLY_SETTLE && (
            <Row>
              <Col span={3}>
                <div className="text-right">
                  <span className="item-required-icon">*</span>是否减免：
                </div>
              </Col>
              <Col span={9}>
                <Form.Item name="isRemission" noStyle>
                  <Radio.Group
                    value={needRemission}
                    onChange={(e) => {
                      console.log(e?.target?.value);
                      setNeedRemission(e?.target?.value);
                      // 在提前结清模式下，要减去减免总额
                      form.setFieldsValue({
                        remission: [{ id: 1 }],
                      });
                    }}
                    options={[
                      {
                        label: '是',
                        value: 1,
                      },
                      {
                        label: '否',
                        value: 0,
                      },
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
          {/* 通知手机号 */}
          <Row>
            <Col span={9}>
              <Form.Item
                name="customerPhone"
                label="通知手机号"
                rules={[
                  {
                    validator: (_, value) => {
                      if (value && (value?.length !== 11 || !/^[0-9]*$/.test(value))) {
                        return Promise.reject(new Error('请输入11位数字'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <div className="phone-tip-text">选填，填写后会给该手机号短信发送还款二维码</div>
            </Col>
          </Row>
          {/* 计划还款金额 */}
          <Row>
            <Col span={9}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => {
                  if (repayType === REPAY_TYPE.NORMAL_REPAY) {
                    return false;
                  }
                  return (
                    calculateRemissionAmountOfEditTable(currentValues?.remission) !==
                    calculateRemissionAmountOfEditTable(prevValues?.remission)
                  );
                }}
              >
                {({ getFieldsValue }) => {
                  const { remission } = getFieldsValue();
                  console.log('remission', remission);
                  const remissionTotalAmount = calculateRemissionAmountOfEditTable(remission);

                  // 在提前结清模式下，要减去减免总额
                  if (repayType === REPAY_TYPE.EARLY_SETTLE) {
                    const temp = BigNumber(getAmount()?.totalAmountUnpaid)
                      .minus(remissionTotalAmount)
                      ?.toString();
                    form.setFieldsValue({
                      repayAmount: temp,
                    });
                  }
                  return (
                    <>
                      <Form.Item
                        name="repayAmount"
                        label="计划还款金额"
                        rules={[
                          { required: true, message: '必填项' },
                          {
                            //
                            validator: (_, val) => {
                              if (repayType === REPAY_TYPE.EARLY_SETTLE) {
                                return Promise.resolve();
                              }
                              if (isNaN(val) || val === undefined) {
                                return Promise.reject('请输入数字');
                              }
                              if (
                                val !== '' &&
                                (BigNumber(val).isLessThan('0') ||
                                  (repayType === REPAY_TYPE.NORMAL_REPAY &&
                                    BigNumber(val).isEqualTo('0')))
                              ) {
                                return Promise.reject('请输入大于0的数字');
                              }
                              if (
                                val !== '' &&
                                BigNumber(val).isGreaterThan(getAmount()?.totalAmountUnpaid || 0)
                              ) {
                                return Promise.reject('超出最大金额');
                              }
                              if (
                                val?.toString()?.includes('.') &&
                                val?.toString()?.split('.')[1].length > 2
                              ) {
                                return Promise.reject('最多两位小数');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <Input
                          min={0}
                          type="number"
                          readOnly={repayType === REPAY_TYPE.EARLY_SETTLE}
                          variant={
                            repayType === REPAY_TYPE.NORMAL_REPAY ? 'outlined' : 'borderless'
                          }
                        />
                      </Form.Item>
                    </>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={24}></Col>
            <Col span={3}></Col>
            {repayType === REPAY_TYPE.NORMAL_REPAY && (
              <Col span={21}>
                <div className="amount-tip-text">
                  {/* 下一期结清 */}
                  <span>
                    已选车辆全部还款至
                    <span className="text-bold">下一期结清</span>:￥
                    {planRepayAmount?.repayNextTermAmount || 0}
                    <Button
                      type="link"
                      size="small"
                      disabled={referAmountLoading}
                      loading={referAmountLoading}
                      onClick={() => {
                        form?.setFieldsValue({
                          repayAmount: planRepayAmount?.repayNextTermAmount || '0',
                        });
                        initExpectRepayAmountListDebounce();
                      }}
                    >
                      <div className="amount-tip-text">使用此金额</div>
                    </Button>
                  </span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  {/* 逾期结清 */}
                  {planRepayAmount?.repayOverdueAmount &&
                    BigNumber(planRepayAmount?.repayOverdueAmount).isGreaterThan(0) && (
                      <span>
                        已选车辆全部还款至
                        <span className="text-bold">逾期结清</span>:￥
                        {planRepayAmount?.repayOverdueAmount || 0}
                        <Button
                          type="link"
                          size="small"
                          disabled={referAmountLoading}
                          loading={referAmountLoading}
                          onClick={() => {
                            form?.setFieldsValue({
                              repayAmount: planRepayAmount?.repayOverdueAmount || '0',
                            });
                            initExpectRepayAmountListDebounce();
                          }}
                        >
                          <div className="amount-tip-text">使用此金额</div>
                        </Button>
                      </span>
                    )}
                </div>
              </Col>
            )}
          </Row>
          <Divider className="divider-margin" />

          {/* 减免 */}
          {(repayType === 1 || (repayType === REPAY_TYPE.EARLY_SETTLE && needRemission === 1)) && (
            <>
              <Collapse
                ghost
                size="small"
                defaultActiveKey={
                  repayType === REPAY_TYPE.EARLY_SETTLE && needRemission === 1 ? ['1'] : undefined
                }
                // expandIconPosition="end"
                items={[
                  {
                    key: '1',
                    label: (
                      <>
                        <span className="text-bold">申请减免：</span>
                        <span className="red-text">
                          申请减免需要审批通过才会生效，请提前与运营人员沟通一致后再申请
                        </span>
                      </>
                    ),
                    children: (
                      <>
                        {/* 普通还款 */}
                        {repayType === REPAY_TYPE.NORMAL_REPAY && (
                          <>
                            <div style={{ marginBottom: 20 }}>
                              减免总金额：
                              <Form.Item
                                noStyle
                                shouldUpdate={(prevValues, curValues) => {
                                  return (
                                    prevValues.repayAmount !== curValues.repayAmount ||
                                    //
                                    prevValues.remissionPrincipal !==
                                      curValues.remissionPrincipal ||
                                    prevValues.remissionInterest !== curValues.remissionInterest ||
                                    prevValues.remissionPenalty !== curValues.remissionPenalty ||
                                    prevValues.remissionAdvanceSettleLiquidatedDamages !==
                                      curValues.remissionAdvanceSettleLiquidatedDamages ||
                                    prevValues.remissionDelayAmount !==
                                      curValues.remissionDelayAmount
                                  );
                                }}
                              >
                                {({ getFieldsValue }) => {
                                  const {
                                    remissionPrincipal,
                                    remissionInterest,
                                    remissionPenalty,
                                    remissionAdvanceSettleLiquidatedDamages,
                                    remissionDelayAmount,
                                  } = getFieldsValue();

                                  const remissionTotalAmount =
                                    new BigNumber(remissionPrincipal || 0)
                                      .plus(remissionInterest || 0)
                                      .plus(remissionPenalty || 0)
                                      .plus(remissionAdvanceSettleLiquidatedDamages || 0)
                                      .plus(remissionDelayAmount || 0)
                                      .toNumber() || 0;
                                  return (
                                    <span style={{ color: 'red' }}>{remissionTotalAmount}元</span>
                                  );
                                }}
                              </Form.Item>
                            </div>
                            <Divider className="divider-margin-low" />
                            {/* 减免列头 */}
                            <Row gutter={[25, 0]}>
                              <Col span={4} className="remission-item-name">
                                减免本金(元)
                              </Col>
                              <Col span={4} className="remission-item-name">
                                减免利息(元)
                              </Col>
                              <Col span={4} className="remission-item-name">
                                减免罚息(元)
                              </Col>
                              <Col span={4} className="remission-item-name">
                                减免违约金(元)
                              </Col>
                              <Col span={4} className="remission-item-name">
                                减免滞纳金(元)
                              </Col>
                            </Row>
                            <Divider className="divider-margin-low" />
                            <Row gutter={[25, 0]}>
                              {/* 减免本金(元) */}
                              <Col span={4}>
                                <Form.Item
                                  name="remissionPrincipal"
                                  rules={[
                                    { required: true, message: '必填项' },
                                    {
                                      //
                                      validator: (_, val) => {
                                        if (isNaN(val) || val === undefined) {
                                          return Promise.reject('请输入数字');
                                        }
                                        if (val !== '' && BigNumber(val).isLessThan('0')) {
                                          return Promise.reject('不能小于0');
                                        }
                                        if (
                                          val !== '' &&
                                          BigNumber(val).isGreaterThan(
                                            getAmount()?.totalPrincipalUnpaid || 0,
                                          )
                                        ) {
                                          return Promise.reject('超出最大金额');
                                        }
                                        if (
                                          val?.toString()?.includes('.') &&
                                          val.toString().split('.')[1].length > 2
                                        ) {
                                          return Promise.reject('最多两位小数');
                                        }
                                        return Promise.resolve();
                                      },
                                    },
                                  ]}
                                >
                                  <Input type="number" />
                                </Form.Item>
                                <div className="remission-item-title">合计未还本金：</div>
                                <div className="remission-item-amount">
                                  {`￥${getAmount()?.totalPrincipalUnpaid || 0}`}
                                  <Button
                                    className="remission-button-text"
                                    type="link"
                                    size="small"
                                    onClick={() => {
                                      form?.setFieldsValue({
                                        remissionPrincipal:
                                          getAmount()?.totalPrincipalUnpaid || '0',
                                      });
                                      initExpectRepayAmountListDebounce();
                                    }}
                                  >
                                    使用此金额
                                  </Button>
                                </div>
                              </Col>
                              {/* 减免利息(元) */}
                              <Col span={4}>
                                <Form.Item
                                  name="remissionInterest"
                                  rules={[
                                    { required: true, message: '必填项' },
                                    {
                                      //
                                      validator: (_, val) => {
                                        if (isNaN(val) || val === undefined) {
                                          return Promise.reject('请输入数字');
                                        }
                                        if (val !== '' && BigNumber(val).isLessThan('0')) {
                                          return Promise.reject('不能小于0');
                                        }
                                        if (
                                          val !== '' &&
                                          BigNumber(val).isGreaterThan(
                                            getAmount()?.totalInterestUnpaid || 0,
                                          )
                                        ) {
                                          return Promise.reject('超出最大金额');
                                        }
                                        if (
                                          val?.toString()?.includes('.') &&
                                          val?.toString().split('.')[1].length > 2
                                        ) {
                                          return Promise.reject('最多两位小数');
                                        }
                                        return Promise.resolve();
                                      },
                                    },
                                  ]}
                                >
                                  <Input type="number" />
                                </Form.Item>
                                <div className="remission-item-title">合计未还利息：</div>
                                <div className="remission-item-amount">
                                  {`￥${getAmount()?.totalInterestUnpaid || 0}`}
                                  <Button
                                    className="remission-button-text"
                                    type="link"
                                    size="small"
                                    onClick={() => {
                                      form?.setFieldsValue({
                                        remissionInterest: getAmount()?.totalInterestUnpaid || '0',
                                      });
                                      initExpectRepayAmountListDebounce();
                                    }}
                                  >
                                    使用此金额
                                  </Button>
                                </div>
                              </Col>
                              {/* 减免罚息(元) */}
                              <Col span={4}>
                                <Form.Item
                                  name="remissionPenalty"
                                  rules={[
                                    { required: true, message: '必填项' },
                                    {
                                      //
                                      validator: (_, val) => {
                                        if (isNaN(val) || val === undefined) {
                                          return Promise.reject('请输入数字');
                                        }
                                        if (val !== '' && BigNumber(val).isLessThan('0')) {
                                          return Promise.reject('不能小于0');
                                        }
                                        if (
                                          val !== '' &&
                                          BigNumber(val).isGreaterThan(
                                            getAmount()?.totalOverduePenaltyUnpaid || 0,
                                          )
                                        ) {
                                          return Promise.reject('超出最大金额');
                                        }
                                        if (
                                          val?.toString()?.includes('.') &&
                                          val?.toString()?.split('.')[1].length > 2
                                        ) {
                                          return Promise.reject('最多两位小数');
                                        }
                                        return Promise.resolve();
                                      },
                                    },
                                  ]}
                                >
                                  <Input type="number" />
                                </Form.Item>
                                <div className="remission-item-title">合计未还罚息：</div>
                                <div className="remission-item-amount">
                                  {`￥${getAmount()?.totalOverduePenaltyUnpaid || 0}`}
                                  <Button
                                    className="remission-button-text"
                                    type="link"
                                    size="small"
                                    onClick={() => {
                                      form?.setFieldsValue({
                                        remissionPenalty:
                                          getAmount()?.totalOverduePenaltyUnpaid || '0',
                                      });
                                      initExpectRepayAmountListDebounce();
                                    }}
                                  >
                                    使用此金额
                                  </Button>
                                </div>
                              </Col>
                              {/* 减免违约金(元) */}
                              <Col span={4}>
                                <Form.Item
                                  name="remissionAdvanceSettleLiquidatedDamages"
                                  rules={[
                                    { required: true, message: '必填项' },
                                    {
                                      //
                                      validator: (_, val) => {
                                        if (isNaN(val) || val === undefined) {
                                          return Promise.reject('请输入数字');
                                        }
                                        if (val !== '' && BigNumber(val).isLessThan('0')) {
                                          return Promise.reject('不能小于0');
                                        }
                                        if (
                                          val !== '' &&
                                          BigNumber(val).isGreaterThan(
                                            getAmount()?.totalBreach || 0,
                                          )
                                        ) {
                                          return Promise.reject('超出最大金额');
                                        }
                                        if (
                                          val?.toString()?.includes('.') &&
                                          val?.toString()?.split('.')[1].length > 2
                                        ) {
                                          return Promise.reject('最多两位小数');
                                        }
                                        return Promise.resolve();
                                      },
                                    },
                                  ]}
                                >
                                  <Input type="number" />
                                </Form.Item>
                                <div className="remission-item-title">合计未还违约金：</div>
                                <div className="remission-item-amount">
                                  {`￥${getAmount()?.totalBreach || 0}`}
                                  <Button
                                    className="remission-button-text"
                                    type="link"
                                    size="small"
                                    onClick={() => {
                                      form?.setFieldsValue({
                                        remissionAdvanceSettleLiquidatedDamages:
                                          getAmount()?.totalBreach || '0',
                                      });
                                      initExpectRepayAmountListDebounce();
                                    }}
                                  >
                                    使用此金额
                                  </Button>
                                </div>
                              </Col>
                              {/* 减免滞纳金(元) */}
                              <Col span={4}>
                                <Form.Item
                                  name="remissionDelayAmount"
                                  rules={[
                                    { required: true, message: '必填项' },
                                    {
                                      //
                                      validator: (_, val) => {
                                        if (isNaN(val) || val === undefined) {
                                          return Promise.reject('请输入数字');
                                        }
                                        if (val !== '' && BigNumber(val).isLessThan('0')) {
                                          return Promise.reject('不能小于0');
                                        }
                                        if (
                                          val !== '' &&
                                          BigNumber(val).isGreaterThan(getAmount()?.totalLate || 0)
                                        ) {
                                          return Promise.reject('超出最大金额');
                                        }
                                        if (
                                          val?.toString()?.includes('.') &&
                                          val?.toString()?.split('.')[1].length > 2
                                        ) {
                                          return Promise.reject('最多两位小数');
                                        }
                                        return Promise.resolve();
                                      },
                                    },
                                  ]}
                                >
                                  <Input type="number" />
                                </Form.Item>
                                <div className="remission-item-title">合计未还滞纳金：</div>
                                <div className="remission-item-amount">
                                  {`￥${getAmount()?.totalLate || 0}`}
                                  <Button
                                    className="remission-button-text"
                                    type="link"
                                    size="small"
                                    onClick={() => {
                                      form?.setFieldsValue({
                                        remissionDelayAmount: getAmount()?.totalLate || '0',
                                      });
                                      initExpectRepayAmountListDebounce();
                                    }}
                                  >
                                    使用此金额
                                  </Button>
                                </div>
                              </Col>
                            </Row>
                          </>
                        )}
                        {/*  */}
                        {repayType === REPAY_TYPE.EARLY_SETTLE && (
                          <>
                            <RemissionEditTable
                              billInfoData={billInfoData}
                              cRef={remissionEditRef}
                              formRef={form}
                            />
                          </>
                        )}
                      </>
                    ),
                  },
                ]}
              />
              <Divider className="divider-margin" />
            </>
          )}

          {/* 车辆入明细 */}
          <div className="text-bold">车辆入明细</div>
          <ProFormDependency name={['repayType', 'remission', 'isRemission']}>
            {(values) => {
              let repayTotalAmount = 0;
              const remissionTotalAmount = calculateRemissionAmountOfEditTable(values.remission);
              const repayAmount = new BigNumber(billInfoData?.repayAmount || '').toNumber(); //提前结清的额度

              if (values?.isRemission) {
                repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
                  .minus(remissionTotalAmount)
                  .toNumber();
              } else {
                repayTotalAmount = repayAmount;
              }

              return (
                <>
                  {repayType === REPAY_TYPE.EARLY_SETTLE && (
                    <div style={{ marginTop: 16 }}>
                      提前结清还款总金额:
                      <RedText text={isNaN(repayAmount) ? '-' : repayAmount} />
                      元，减去减免总金额后，实际还款金额:
                      <RedText text={isNaN(repayTotalAmount) ? '-' : repayTotalAmount} />元
                    </div>
                  )}
                  <div style={{ marginTop: 16 }}>
                    合计未还本金:
                    <RedText text={billInfoData?.totalPrincipalNotRepaid || '-'} />
                    元，未还利息：
                    <RedText text={billInfoData?.totalInterestNotRepaid || '-'} />
                    元，未还罚息：
                    <RedText text={billInfoData?.totalOverduePenaltyNotRepaid || '-'} />元
                    {repayType === REPAY_TYPE.EARLY_SETTLE && (
                      <>
                        ，提前结清违约金:
                        <RedText text={billInfoData?.totalBreachNotRepaid || '-'} />元
                      </>
                    )}
                  </div>
                </>
              );
            }}
          </ProFormDependency>
          <ProTable
            rowKey={'id'}
            search={false}
            options={false}
            columns={OnlineRepaymentColumns}
            dataSource={billInfoData?.billRspDTOList as any}
            scroll={{ x: 'max-content' }}
            toolBarRender={() => {
              return [
                <Button
                  key="exportBtn"
                  type="primary"
                  onClick={handleExport}
                  loading={exportLoading}
                >
                  导出
                </Button>,
              ];
            }}
            loading={initInfoLoading || expectRepayAmountListLoading}
            pagination={{
              position: ['topRight'],
              pageSize: 10,
              defaultCurrent: 1,
              showTotal: (total) => `总共 ${total} 条`,
              // pageSizeOptions: ['10', '20', '50', '100'],
            }}
          />
          {/* <Table
            search={false}
            options={false}
            key={'id'}
            size="small"
            columns={OnlineRepaymentColumns}
            dataSource={billInfoData?.billRspDTOList as any}
            scroll={{ x: 'max-content' }}
            pagination={{
              position: ['topRight'],
              pageSize: 10,
              defaultCurrent: 1,
              showTotal: (total) => `总共 ${total} 条`,
              // pageSizeOptions: ['10', '20', '50', '100'],
            }}
            loading={initInfoLoading || expectRepayAmountListLoading}
          /> */}
        </Form>
      </Modal>
    </>
  );
};

export default Page;
