import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { UploadOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { Button, message, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/es/upload/interface';
import React from 'react';
import type { IattachItem } from '../types';

type Props = {
  value?: IattachItem[];
  onChange?: (value: IattachItem[]) => void;
};
const UploadVoucher: React.FC<Props> = (props) => {
  const { orderNo } = (history as any).location.query;

  const onChange = (info: UploadChangeParam) => {
    const { fileList } = info;
    if (info.file.status === 'uploading') {
      return;
    }
    if (info.file.status === 'done') {
      const value = fileList.map((item) => {
        const {
          name,
          response: { data = {} },
        } = item;
        const { filePath } = data;
        return {
          name,
          filePath,
        };
      });
      props?.onChange?.(value);
      message.success(`${info.file.name} 上传成功`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传有误`);
    }
  };
  return (
    <Upload
      multiple
      onChange={onChange}
      accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
      name="file"
      action={`${getBaseUrl()}/repayment/oss/common/uploadfile`}
      headers={{ ...getAuthHeaders() }}
      data={{
        acl: 'PUBLIC_READ',
        destPath: 'EP_AUTH_INFO' || 'USER_ORDER_APPLY_MONEY',
        orderNo,
      }}
    >
      <Button icon={<UploadOutlined />}>Click to Upload</Button>
    </Upload>
  );
};

export default UploadVoucher;
