import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import HeaderTab from '@/components/HeaderTab';
import {
  CHANNEL_TYPES_MAP,
  GUARANTEE_TYPES_CODE,
  leaseStatusMap,
  LEASE_LOAN_FILE_TYPE,
  LENDING_MODEL_MAP,
  LICENSE_TYPES_MAP,
  loanCarInsuranceStatusMap,
  loanStatusMap,
  NOTARIZATION_STATUS,
  NOTARIZATION_STATUS_MAP,
  PRODUCT_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_CODE_LABEL,
} from '@/enums';
import globalStyle from '@/global.less';
import { getAllChannelNameEnum, getProductNameEnum } from '@/services/enum';
import { queryOrderDetail } from '@/services/global';
import { filterProps, optionsMap, removeBlankFromObject } from '@/utils/tools';
import { disableFutureDate, isChannelStoreUser } from '@/utils/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess, useLocation } from '@umijs/max';
import { Button, Drawer, List, message, Modal, Popover, Space, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { KeepAlive, useActivate } from 'react-activation';
import type {
  LeanListParams,
  LeaseLendingParams,
  LendingParams,
  LoanListItem,
  PopParams,
} from './data';
import './index.less';
import { doLending, leaseAudit, loanExport, queryLoan } from './service';

type SelectType = {
  value: string;
  label: string;
};

const MAP_TYPE = {
  1: '进件',
  2: '债转',
  // 3: '代偿'
  5: '金融放款',
  6: '委托投保',
  9: '资方放款',
};

enum LEASE_ACTION_STATUS {
  PASS = 1,
  REJECT = 2,
  HANG_UP = 3,
  UN_HANG_UP = 4,
}

const LEASE_ACTION_TEXT = {
  [LEASE_ACTION_STATUS.PASS]: '放款',
  [LEASE_ACTION_STATUS.REJECT]: '驳回',
  [LEASE_ACTION_STATUS.HANG_UP]: '挂起',
  [LEASE_ACTION_STATUS.UN_HANG_UP]: '解除挂起',
};

enum LICENSE_TYPE {
  COMPANY = 1, // 挂靠
  PERSONAL = 2, // 个户
}

enum LEASE_ORDER_STATUS {
  HANG_UP = 7,
  FIRST_APPROVE = 10,
  SECOND_APPROVE = 11,
}

// 渠道门店帐号限制渠道
const channelSearchFilter = ({ access, formRef }: any) => {
  if (isChannelStoreUser(access) && access?.currentUser?.channelCode) {
    formRef?.current?.setFieldValue('channelIds', access.currentUser.channelCode);
  }
};

const { Text } = Typography;

const LoanList: React.FC<any> = () => {
  const location = useLocation();
  // const [exportLoading, setExportLoading] = useState(false);
  // const getExport = (form: LeanListParams) => {
  //   setExportLoading(true);
  //   loanExport(form)
  //     .then((res) => {
  //       downLoadExcel(res);
  //       setExportLoading(false);
  //     })
  //     .finally(() => {
  //       setExportLoading(false);
  //     });
  // };

  enum LOAN_TYPE {
    INCOME = 1, // 进件
    THIRD_LOAN = 9, //资方放款
  }

  // 小贷放款类型
  const smallLoanTypeMap = {
    1: '进件',
  };
  // 融租放款类型
  const leaseLoanTypeMap = {
    1: '进件', // 2: '债转',
    9: '资方放款',
  };
  const loanTypeMap = {
    1: '进件',
    2: '债转',
  };
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>(); // useEffect(() => {

  const [showHide, setHide] = useState<boolean>(true);
  const [showTableHide, setTableHide] = useState<boolean>(false);
  const [debtModalVisible, handleModalVisible] = useState<boolean>(false);
  const [popParams, setPopParams] = useState<PopParams>({
    curAmount: 0,
    items: 0,
    tips: '',
  });
  const [params, setParams] = useState<LendingParams>({
    token: 0,
    lendingNo: [],
    type: -1,
  });
  const [leaseParams, setLeaseParams] = useState<LeaseLendingParams>({
    auditNo: -1,
    auditType: 0,
    lendingNos: [],
    auditResult: -1,
    productCode: '',
    remark: '',
    gpsLessOrderNos: [],
    passOrderNos: [],
  });
  const [statusOptions, setStatusOption] = useState<SelectType[]>(optionsMap(leaseStatusMap));
  const [loanTypeOptions, setLoanTypeOption] = useState<SelectType[]>(optionsMap(smallLoanTypeMap));
  const [leaseModalVisible, handleLeaseModalOpen] = useState<boolean>(false);
  const [curLeaseAction, setCurLeaseAction] = useState<number>(); // 融租操作

  const [cleanSelectRow, handleSelectRow] = useState<[]>([]);
  const [clearCreateTime] = useState<boolean>(false);
  const [multipleMode, setMultipleMode] = useState<boolean>(false);

  const [vinList, setVinList] = useState<string[]>([]);
  const [vinModalVisible, setVinModalVisible] = useState<boolean>(false);

  console.log('clearCreateTimeclearCreateTime', clearCreateTime);

  useEffect(() => {
    console.log('useEffectuseEffectuseEffect', clearCreateTime);
  }, []);

  const [productCodeValueEnum, setProductCodeValueEnum] = useState<Record<string, string>>({});
  const access = useAccess();
  const getAllChannelNameEnumMemo = useMemo(async () => {
    return await getAllChannelNameEnum();
  }, []);

  useEffect(() => {
    getProductNameEnum().then((data) => {
      setProductCodeValueEnum(
        data.reduce((pre, cur) => {
          return {
            ...pre,
            [cur.value]: cur.label,
          };
        }, {}),
      );
    });
  }, []);

  const onLoanTypeChange = (loanType: string | string[]) => {
    //  处理多套页面 进件=1 金融放款=5 委托投保=6 资方放款=9 不需要展示债转流水号
    if (
      loanType?.includes('1') ||
      loanType?.includes('5') ||
      loanType?.includes('6') ||
      loanType?.includes('9') ||
      !loanType?.length ||
      !loanType
    ) {
      formRef.current?.setFieldsValue({
        debtNo: '',
      });
      if (!showHide) setHide(true);
    } else {
      formRef.current?.setFieldsValue({
        orderNo: '',
      });
      if (showHide) setHide(false);
    }
  };

  // 产品二级分类选择时
  const onProductSecondCodeChange = (val: string) => {
    let options: any = [];
    let optionsLoanType: any = [];
    let isMultiple = false;
    let typeList = '';

    switch (val) {
      case SECONDARY_CLASSIFICATION_CODE.SELLER_FACTORING_SECOND: // 保理
        options = optionsMap(loanStatusMap);
        optionsLoanType = optionsMap(loanTypeMap);
        typeList = '1';
        isMultiple = false;
        break;
      case SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE: //车险分期
        // 车险的状态新增 待审核
        options = optionsMap(loanCarInsuranceStatusMap);
        // setTableHide(false); // 车险要隐藏 债转流水号 一列
        optionsLoanType = optionsMap({ 5: '金融放款', 6: '委托投保' });
        typeList = '5';
        isMultiple = true;
        break;
      case SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND: //融租
        options = optionsMap(leaseStatusMap);
        optionsLoanType = optionsMap(leaseLoanTypeMap);
        typeList = '1';
        isMultiple = false;
        formRef.current?.setFieldValue('applyTime', undefined);
        break;
      case SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY: // 小贷
      case SECONDARY_CLASSIFICATION_CODE.MERCHANT: // 圆商贷
        options = optionsMap(leaseStatusMap);
        optionsLoanType = optionsMap(smallLoanTypeMap);
        typeList = '1';
        isMultiple = false;
        break;
      default:
        break;
    }

    formRef.current?.setFieldsValue({
      status: null,
      productCodeList: undefined,
      typeList,
    });
    setStatusOption(options);
    setLoanTypeOption(optionsLoanType);
    setMultipleMode(isMultiple);
    const loanType = formRef.current?.getFieldValue('typeList');
    onLoanTypeChange(loanType);
  };

  // 初始化值，分组件加载和被激活useActivate两种情况
  const initParamsData = (type: string) => {
    // @ts-ignore
    const loanStatus = location.state?.loanStatus; // @ts-ignore
    const startTime = location.state?.startTime; // @ts-ignore
    const endTime = location.state?.endTime; // @ts-ignore
    const fromHeaderTabs = location.state?.fromHeaderTabs;
    console.log('fromHeaderTabs', fromHeaderTabs);
    if (fromHeaderTabs) return; //  headerTabs点击过来的链接不做额外处理，沿用缓存
    if (loanStatus) {
      formRef?.current?.resetFields();
      //  小圆车融订单
      const fieldValues: any = {};
      if (loanStatus) {
        fieldValues.status = loanStatus;
        fieldValues.productSecondCode = SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND;
        onProductSecondCodeChange(SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND);
      }
      if (startTime && endTime) {
        fieldValues.applyTime = [dayjs(startTime), dayjs(endTime)];
      }
      console.log(type, fieldValues);
      formRef?.current?.setFieldsValue(fieldValues);

      setTimeout(() => {
        formRef?.current?.submit();
        // actionRef?.current?.reload();
        // history.location.state = null;
        window.history.replaceState(null, '', window.location.href);
      }, 500);
    }
  };

  useActivate(() => {
    initParamsData('useActivate:');
  });

  useLayoutEffect(() => {
    initParamsData('useLayoutEffect:');
  }, []);

  const handleOk = () => {
    handleModalVisible(false);
    doLending(params).then(() => {
      message.success('操作成功');
      actionRef?.current?.reload();
      // actionRef?.current?.reset?.();
      actionRef?.current?.clearSelected?.();
    });
  };

  const leaseHandleOK = (formValues: any) => {
    handleLeaseModalOpen(false);
    leaseAudit({ ...leaseParams, ...formValues })
      .then(() => {
        message.success('操作成功');
        actionRef?.current?.reload();
        // actionRef?.current?.reset?.();
        actionRef?.current?.clearSelected?.();
      })
      .catch((e) => {
        if (e?.ret === 10016) {
          //  部份订单待一审GPS截图资料未上传，不予通过
          // message.error(e?.msg);
          actionRef?.current?.reload();
          // actionRef?.current?.reset?.();
          actionRef?.current?.clearSelected?.();
        }
      });
  }; // typeProduct 1:保理账期 2：融租  typeLease 1-通过 2-驳回 3-挂起 4-解除挂起

  const popData = async (data: any, typeProduct: number, typeLease?: number) => {
    if (Array.isArray(data)) {
      const allAmount = data.reduce((prev, current) => {
        return prev + Number(current.amount);
      }, 0);
      const lendingArray = data.map((item) => {
        return item.lendingNo;
      }); // 批量操作

      if (typeProduct === 1) {
        setParams({
          type: data[0].type,
          lendingNo: lendingArray,
          token: new Date().getTime(),
        });
      } else if (typeProduct === 2) {
        setLeaseParams({
          auditNo: new Date().getTime(),
          auditType: data[0].status === 10 ? 1 : 2,
          auditResult: typeLease,
          remark: '',

          lendingNos: lendingArray as [],
          productCode: data[0].productCode,
          gpsLessOrderNos: data.flatMap((item) => {
            return ['2670373103096406016'].includes(item.orderNo) ? [item.orderNo] : [];
          }) as [], // 有未上传GPS信息的待一审订单
          passOrderNos: data.flatMap((item) => {
            return item.orderNo !== '2670373103096406016' ? [item.orderNo] : [];
          }) as [], //  可通过的订单
        });
      }

      setPopParams({
        curAmount: allAmount,
        items: data.length,
        tips: '批量放款中包含线下放款，请注意核对放款金额',
      });
    } else {
      // 每行操作
      if (typeProduct === 1) {
        setParams({
          type: data.type,
          lendingNo: [data.lendingNo],
          token: new Date().getTime(),
        });
      } else if (typeProduct === 2) {
        setLeaseParams({
          auditNo: new Date().getTime(),
          auditType: data.status === 10 ? 1 : 2,
          auditResult: typeLease,
          remark: '',
          productCode: data.productCode,
          lendingNos: [data.lendingNo],
          gpsLessOrderNos: [data.orderNo],
        });
      }

      let orderDetailRes: any = {};
      try {
        if (data.licenseType === LICENSE_TYPE.PERSONAL) {
          orderDetailRes = await queryOrderDetail(data.orderNo);
        }
      } finally {
        setPopParams({
          curAmount: data.amount,
          items: 1,
          tips: '该笔放款为线下放款，请注意核对放款金额',
          extraInfo: {
            licenseType: data.licenseType, // 上牌类型
            notarizationStatus: orderDetailRes?.data?.notarizationStatus,
          },
        });
      }
    }
  }; // 放款
  // 保理
  const handleOpen = (data: any) => {
    handleModalVisible(true);
    popData(data, 1);
  }; // 融租 type 同意。驳回
  // 融租
  const handleLeaseOpen = async (val: any, type: number) => {
    await popData(val, 2, type);
    handleLeaseModalOpen(true);
  };

  const renderFormItem = (item: any, { defaultRender }: any, form: any) => {
    // console.log(form?.getFieldValue('type'));
    //  保险的放款类型是多选，是数组需要处理。
    const loanType = form?.getFieldValue('typeList');
    //  处理多套页面 进件 金融放夸 委托投保 不需要展示债转流水号
    if (
      loanType?.includes('1') ||
      loanType?.includes('5') ||
      loanType?.includes('6') ||
      !loanType?.length ||
      !loanType
    ) {
      formRef.current?.setFieldsValue({
        debtNo: '',
      });
      if (!showHide) setHide(true);
    } else {
      formRef.current?.setFieldsValue({
        orderNo: '',
      });
      if (showHide) setHide(false);
    }
    // console.log(item, defaultRender);
    return defaultRender(item);
  };

  // 通过/驳回文案提示组合
  const WaitAuditingPassTextItem = () => {
    const locked = false;
    let currentText = <></>;
    // 待一审(auditType: 1)状态运营未上传GPS截图时点击通过(curType: 1), 暂时停用该逻辑，由后端接口返回文案
    if (
      locked &&
      leaseParams.gpsLessOrderNos?.length &&
      curLeaseAction === 1 &&
      leaseParams.auditType === 1
    ) {
      if (popParams.items === 1) {
        //  单选
        currentText = <>请补充GPS截图后，重新操作。</>;
      } else {
        //  批量
        currentText = (
          <>
            {leaseParams.passOrderNos?.length ? (
              <>
                【
                {/* <Tooltip title={leaseParams.passOrderNos.toString()}>
                  <span className="wait-auditing-text-div">
                    {leaseParams.passOrderNos.toString()}
                  </span>
                </Tooltip> */}
                <Text
                  ellipsis={{ tooltip: leaseParams.passOrderNos.length > 1 }}
                  className={globalStyle.ml20}
                  style={{
                    width: 205,
                    color: 'gray',
                    margin: 0,
                    textAlign: 'center',
                  }}
                >
                  {leaseParams.passOrderNos.toString()}
                </Text>
                】一审通过，
              </>
            ) : (
              ''
            )}
            {
              <>
                【
                {/* <Tooltip title={leaseParams.gpsLessOrderNos.toString()} placement="bottom">
                  <span className="wait-auditing-text-div">
                    {leaseParams.gpsLessOrderNos.toString()}
                  </span>
                </Tooltip> */}
                <Text
                  ellipsis={{
                    tooltip: leaseParams.gpsLessOrderNos?.length > 1,
                  }}
                  className={globalStyle.ml20}
                  style={{
                    width: 205,
                    color: 'gray',
                    margin: 0,
                    textAlign: 'center',
                  }}
                >
                  {leaseParams.gpsLessOrderNos.toString()}
                </Text>
                】需补充GPS信息后重新操作
              </>
            }
          </>
        );
      }
    } else {
      currentText = (
        <>
          总金额【
          <span
            style={{
              color: 'red',
            }}
          >
            {Number(popParams.curAmount).toFixed(2)}
          </span>
          】元
        </>
      );
    }

    return (
      <>
        <p
          style={{
            fontSize: 16,
          }}
        >
          本次放款【
          <span
            style={{
              color: 'red',
            }}
          >
            {popParams.items}
          </span>
          】条，
          {currentText}
        </p>
      </>
    );
  };

  const initialStartTime = dayjs().subtract(1, 'months').format('YYYY-MM-DD');
  const initialEndTime = dayjs().format('YYYY-MM-DD');

  const actionProductSecondCode = formRef?.current?.getFieldValue('productSecondCode');
  console.log('actionProductSecondCode', actionProductSecondCode);
  // console.log(clearCreateTime);
  const columns: ProColumns<LoanListItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      hideInTable: showTableHide,
      search: showHide ? undefined : false,
      fieldProps: {
        onBlur: (e: any) => {
          if (actionProductSecondCode === '0201') {
            // 假如是融租 不做时间的处理
            return;
          }

          if (e?.target?.value) {
            // setClearCreateTime(true);
            formRef?.current?.setFieldsValue({ applyTime: undefined });
          } else {
            formRef?.current?.setFieldsValue({
              applyTime: [initialStartTime, initialEndTime],
            });
          }
        },
      },
    },
    {
      title: '债转流水号',
      dataIndex: 'debtNo',
      hideInTable: !showTableHide,
      search: !showHide ? undefined : false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondCode',
      valueType: 'select',
      initialValue: SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY,
      valueEnum: SECONDARY_CLASSIFICATION_CODE_LABEL,
      fieldProps: {
        onChange: (val: string) => {
          onProductSecondCodeChange(val);
        },
        allowClear: false,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productCodeList',
      valueEnum: () => {
        const productCodeMap: any = {};
        for (const productCode in productCodeValueEnum) {
          const secondCode = productCode.substring(0, 4);
          if (actionProductSecondCode === secondCode) {
            productCodeMap[productCode] = productCodeValueEnum[productCode];
          }
        }
        return actionProductSecondCode ? productCodeMap : productCodeValueEnum;
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        onChange(val: string) {
          if (val?.length)
            formRef.current?.setFieldValue('productSecondCode', val?.[0]?.substring(0, 4));
        },
      },
      // initialValue: '020101',
      valueType: 'select',
      // request: getProductNameEnum,
      hideInTable: true,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '上牌类型',
      dataIndex: 'licenseType',
      valueEnum: LICENSE_TYPES_MAP,
      search: false,
    },
    {
      title: '放款类型',
      dataIndex: 'typeList',
      initialValue: '1',
      valueType: 'select',
      // valueEnum: MAP_TYPE,
      fieldProps: {
        options: loanTypeOptions,
        mode: multipleMode ? 'multiple' : undefined,
        optionFilterProp: 'label',
        onChange: (val: string[] | string) => {
          onLoanTypeChange(val);
        },
      },
      // renderFormItem: (item1, item2, item3) => {
      //   return renderFormItem(item1, item2, item3);
      // },
      render: (_, record) => {
        return MAP_TYPE[record?.type] || '-';
      },
    },
    {
      title: '放款流水号',
      dataIndex: 'lendingNo',
      fieldProps: {
        onBlur: (e: any) => {
          if (actionProductSecondCode === '0201') {
            // 假如是融租 不做时间的处理
            return;
          }

          if (e?.target?.value) {
            // setClearCreateTime(true);
            formRef?.current?.setFieldsValue({ applyTime: undefined });
          } else {
            formRef?.current?.setFieldsValue({
              applyTime: [initialStartTime, initialEndTime],
            });
          }
        },
      },
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      search: false,
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
      // search: false,
      fieldProps: {
        onBlur: (e: any) => {
          if (actionProductSecondCode === '0201') {
            // 假如是融租 不做时间的处理
            return;
          }

          if (e?.target?.value) {
            // setClearCreateTime(true);
            formRef?.current?.setFieldsValue({ applyTime: undefined });
          } else {
            formRef?.current?.setFieldsValue({
              applyTime: [initialStartTime, initialEndTime],
            });
          }
        },
      },
    },
    {
      title: '放款金额',
      dataIndex: 'amount',
      search: false,
    },
    {
      title: '放款主体',
      dataIndex: 'lendingMaster',
    },
    {
      title: '收款主体',
      dataIndex: 'receiptMaster',
    },
    {
      title: '借款合同类型',
      dataIndex: 'funderChannelCode',
      valueType: 'select',
      valueEnum: LEASE_LOAN_FILE_TYPE,
      search: actionProductSecondCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
      render: (_, record) => {
        return (
          LEASE_LOAN_FILE_TYPE[record.funderChannelCode as keyof typeof LEASE_LOAN_FILE_TYPE] || '-'
        );
      },
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      valueType: 'select',
      valueEnum: CHANNEL_TYPES_MAP,
      hideInTable: true,
      search: actionProductSecondCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelIds',
      valueType: 'select',
      request: () => {
        return getAllChannelNameEnumMemo.then((res: any) => {
          // 渠道帐号限制渠道
          // channelSearchFilter({ access, formRef });
          return res;
        });
      },
      debounceTime: 600000,
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.channelCode,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      search: actionProductSecondCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
    },
    {
      title: '担保类型',
      dataIndex: 'guaranteeType',
      valueType: 'select',
      valueEnum: GUARANTEE_TYPES_CODE,
      hideInTable: true,
      search: actionProductSecondCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
    },
    {
      title: '是否发生资金流',
      dataIndex: 'fundFlow',
      valueType: 'select',
      valueEnum: {
        false: {
          text: '否',
          status: 'Error',
        },
        true: {
          text: '是',
          status: 'Success',
        },
      },
    },
    {
      title: '放款方式',
      dataIndex: 'lendingModel',
      // valueType: 'dateTime',
      valueEnum: LENDING_MODEL_MAP,
    },
    {
      title: '创建时间',
      dataIndex: 'applyTime',
      valueType: 'dateRange',
      // renderFormItem: renderFormItemDate,

      // initialValue 缓存激活 触发了 antd componentDidMount 导致 initialValue 重新被设置
      // 为何 react-activation 会触发 componentDidMount
      // initialValue: clearCreateTime
      //   ? []
      //   : [dayjs().subtract(1, 'months').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      render(text, record) {
        return record?.applyTime;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => {
          const start = value?.[0];
          const end = value?.[1];
          return {
            startApplyTime: start ? `${start} 00:00:00` : undefined,
            endApplyTime: end ? `${end} 23:59:59` : undefined,
          };
        },
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      fieldProps: {
        options: statusOptions,
      },
      render: (_, record) => {
        if (
          record?.productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE ||
          record?.productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN
        ) {
          return leaseStatusMap[record.status] || loanCarInsuranceStatusMap[record.status];
        }

        if (record?.productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING) {
          return loanStatusMap[record.status];
        }

        return '-';
      },
    },
    {
      title: '放款时间',
      dataIndex: 'lendingTime',
      // search: false,
      // renderFormItem: renderFormItemLoanDate,
      valueType: 'dateRange',
      search: {
        transform: (value: any) => ({
          startLendingTime: `${value[0].split(' ')[0]} 00:00:00`,
          endLendingTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(_, record: any) {
        return record.lendingTime;
      },
    },
    {
      title: '车架号',
      dataIndex: 'vin',
      render: (_, record) => {
        if (!record?.vin) return '-';
        const vins = record?.vin?.split(',');
        if (vins?.length === 1) return vins?.[0] || '-';
        if (vins?.length > 1) {
          return (
            <div>
              {vins?.[0]}
              <span
                style={{ color: '#1677ff', cursor: 'pointer' }}
                onClick={() => {
                  setVinList(vins);
                  setVinModalVisible(true);
                }}
              >
                ...查看更多
              </span>
            </div>
          );
        }
      },
    },
    {
      title: '期限',
      dataIndex: 'term',
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      hideInTable: true,
    },

    {
      title: '证件号码',
      dataIndex: 'certNo',
      hideInTable: true,
    },
    {
      title: '上牌类型',
      dataIndex: 'licenseType',
      valueType: 'select',
      valueEnum: LICENSE_TYPES_MAP,
      hideInTable: true,
    },
    {
      title: '支付流水号',
      dataIndex: 'lendingFlowNo',
      search: false,
    },
    {
      title: '银行流水号',
      dataIndex: 'tranceFlowNo',
      search: false,
      hideInTable: formRef.current?.getFieldValue('productSecondCode') !== '0303',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 220,
      render: (_, record) => {
        let renderOpt;
        const { productCode, productSecondCode } = record;
        const firstProductCode = productCode.substring(0, 2);

        if (firstProductCode === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING) {
          //   SELLER_FACTORING = '商业保理' = "01"
          renderOpt =
            record.type !== 1 ? (
              <>
                <Link to={`/businessMng/debt-detail?debtNo=${record.debtNo}`}>查看详情</Link>
                {record.status === 1 ? (
                  <Button
                    type="link"
                    onClick={() => {
                      handleOpen(record);
                    }}
                  >
                    放款
                  </Button>
                ) : null}
              </>
            ) : (
              <Link
                to={`/businessMng/loan-detail?lendingNo=${record.lendingNo}&orderNo=${record.orderNo}`}
              >
                查看详情
              </Link>
            );
        } else if (productSecondCode === '0303') {
          // 如果二级分类是 小圆车险分期
          renderOpt = (
            <Link
              to={`/businessMng/loan-car-detail?lendingNo=${record.lendingNo}&orderNo=${record.orderNo}`}
            >
              查看详情
            </Link>
          );
        } else {
          const jumpUrl =
            firstProductCode === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE
              ? 'loan-lease-detail'
              : 'loan-cash-detail';
          const lookDetailBtn = (
            <Link
              to={`/businessMng/${jumpUrl}?lendingNo=${record.lendingNo}&orderNo=${record.orderNo}&productCode=${productCode}&loanType=${record.type}`}
            >
              <span
                style={{
                  color:
                    firstProductCode === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE &&
                    record.unsubmittedAlert === 2
                      ? 'red'
                      : 'unset',
                }}
              >
                查看详情
              </span>
            </Link>
          );
          // 资方放款只展示查看详情
          if (record.type === LOAN_TYPE.THIRD_LOAN) {
            renderOpt = lookDetailBtn;
          } else {
            renderOpt = (
              <>
                {lookDetailBtn}
                {record.status === 10 || record.status === 11 ? (
                  <>
                    <a // type="link"
                      style={{
                        margin: 10,
                      }}
                      onClick={() => {
                        setCurLeaseAction(LEASE_ACTION_STATUS.PASS);
                        handleLeaseOpen(record, LEASE_ACTION_STATUS.PASS);
                      }}
                    >
                      通过
                    </a>
                    <a // type="link"
                      onClick={() => {
                        setCurLeaseAction(LEASE_ACTION_STATUS.REJECT);
                        handleLeaseOpen(record, LEASE_ACTION_STATUS.REJECT);
                      }}
                    >
                      驳回
                    </a>
                  </>
                ) : null}
                {record.status === LEASE_ORDER_STATUS.FIRST_APPROVE && (
                  <Button
                    type="link"
                    onClick={() => {
                      setCurLeaseAction(LEASE_ACTION_STATUS.HANG_UP);
                      handleLeaseOpen(record, LEASE_ACTION_STATUS.HANG_UP);
                    }}
                  >
                    挂起
                  </Button>
                )}
                {record.status === LEASE_ORDER_STATUS.HANG_UP && (
                  <Button
                    type="link"
                    onClick={() => {
                      setCurLeaseAction(LEASE_ACTION_STATUS.UN_HANG_UP);
                      handleLeaseOpen(record, LEASE_ACTION_STATUS.UN_HANG_UP);
                    }}
                  >
                    解除挂起
                  </Button>
                )}
                {firstProductCode === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE &&
                record?.status === 12 &&
                record?.remark ? (
                  <>
                    <Popover content={`驳回原因：${record?.remark}`}>
                      <a
                        style={{
                          margin: 10,
                        }}
                      >
                        驳回原因
                      </a>
                    </Popover>
                  </>
                ) : null}
              </>
            );
          }
        }

        return <>{renderOpt}</>;
      },
    },
  ];
  async function getSearchDataTotal() {
    const searchParams = filterProps(formRef.current?.getFieldsFormatValue?.());
    const data = await queryLoan({
      ...searchParams,
      // 1是进件 不传代表查出全部
      // type: paramsRequest.type ? paramsRequest.type : 1,
      productSecondCode: searchParams.productSecondCode
        ? searchParams.productSecondCode
        : SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY,
    });
    return data?.total;
  }

  console.log('formRef', formRef?.current?.getFieldsValue());
  return (
    <>
      <PageContainer>
        <ProTable<LoanListItem>
          actionRef={actionRef}
          formRef={formRef}
          form={{
            initialValues: {
              applyTIme: [
                dayjs().subtract(1, 'months').format('YYYY-MM-DD'),
                dayjs().format('YYYY-MM-DD'),
              ],
            },
          }}
          scroll={{
            x: 'max-content',
          }}
          rowKey={(record) => record?.lendingNo + record?.orderNo + record?.productName}
          request={(paramsRequest: LeanListParams) => {
            return queryLoan({
              ...paramsRequest,
              // 1是进件 不传代表查出全部
              // type: paramsRequest.type ? paramsRequest.type : 1,
              productSecondCode: paramsRequest.productSecondCode
                ? paramsRequest.productSecondCode
                : SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY,
            }).then((res) => {
              const { queryParam = {}, ...rest } = res;
              const { startApplyTime, endApplyTime, productSecondCode } = queryParam;

              if (productSecondCode === '0201') {
                return rest; // 融租不做处理， 根据前端的条件筛选
              }
              // 回显默认创建时间
              if (startApplyTime && endApplyTime) {
                formRef?.current?.setFieldsValue({
                  applyTime: [dayjs(startApplyTime), dayjs(endApplyTime)],
                });
              } else {
                formRef?.current?.setFieldsValue({
                  applyTime: undefined,
                });
              }
              return rest;
            });
          }}
          search={{
            labelWidth: 110,
            optionRender: ({ searchText, resetText }, { form }) => [
              <Button
                key="search"
                type="primary"
                onClick={() => {
                  const loanType = form?.getFieldValue('typeList');
                  handleSelectRow([]); // eslint-disable-next-line @typescript-eslint/no-unused-expressions
                  loanType?.includes('1') ||
                  loanType?.includes('5') ||
                  loanType?.includes('6') ||
                  loanType?.includes('9') ||
                  !loanType?.length ||
                  !loanType
                    ? showTableHide && setTableHide(false)
                    : !showTableHide && setTableHide(true);
                  form?.submit();
                }}
              >
                {searchText}
              </Button>,
              <Button
                key="rest"
                onClick={() => {
                  actionRef?.current?.reset?.();
                  setStatusOption(optionsMap(leaseStatusMap));
                  setLoanTypeOption(optionsMap(leaseLoanTypeMap));
                }}
              >
                {resetText}
              </Button>,
            ],
            defaultCollapsed: false,
          }}
          columns={columns}
          dateFormatter="string"
          rowSelection={{
            getCheckboxProps: (record) => {
              const { productCode, status } = record;
              if (
                productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE ||
                productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN
              ) {
                return {
                  disabled: status !== 10 && status !== 11 && status !== 7,
                };
              }

              return {
                disabled: status !== 1,
              };
            },
            selectedRowKeys: cleanSelectRow,
            onChange: (selectedRowKeys) => {
              handleSelectRow(selectedRowKeys as []);
            },
          }}
          tableAlertRender={({ selectedRows }) => {
            // const productCode = formRef?.current?.getFieldsValue().productCode;
            const status = parseInt(formRef?.current?.getFieldsValue().status, 10); // isSomeLeaseStatus是否全部为一审二审的一种

            const isSameLeaseStatus = selectedRows.every((item) => {
              return (
                [LEASE_ORDER_STATUS.FIRST_APPROVE, LEASE_ORDER_STATUS.SECOND_APPROVE].includes(
                  item.status,
                ) && item.status === selectedRows[0].status
              );
            }); // 判断选中项是否和筛选项一致

            const isSameFirstApproveStatus = selectedRows.every((item) => {
              return (
                [LEASE_ORDER_STATUS.FIRST_APPROVE].includes(item.status) &&
                item.status === selectedRows[0].status
              );
            });

            const isSameHanUpStatus = selectedRows.every((item) => {
              return (
                [LEASE_ORDER_STATUS.HANG_UP].includes(item.status) &&
                item.status === selectedRows[0].status
              );
            });

            const isSomeLoanStatus = selectedRows.every((item) => {
              return item.status === 1 && item.status === selectedRows[0].status;
            });
            return (
              <Space size={24}>
                <>
                  {status === 1 || isSomeLoanStatus ? (
                    <Button
                      type="primary"
                      onClick={() => {
                        handleOpen(selectedRows);
                      }}
                    >
                      批量放款
                    </Button>
                  ) : null}
                </>

                {/* 批量通过或者驳回在同一种状态显示，一审，二审  */}
                {status === 10 || status === 11 || isSameLeaseStatus ? (
                  <>
                    <Button
                      type="primary"
                      onClick={() => {
                        setCurLeaseAction(LEASE_ACTION_STATUS.PASS);
                        handleLeaseOpen(selectedRows, LEASE_ACTION_STATUS.PASS);
                      }}
                    >
                      批量通过
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        setCurLeaseAction(LEASE_ACTION_STATUS.REJECT);
                        handleLeaseOpen(selectedRows, LEASE_ACTION_STATUS.REJECT);
                      }}
                    >
                      批量驳回
                    </Button>
                  </>
                ) : null}
                {(status === 10 || isSameFirstApproveStatus) && (
                  <>
                    <Button
                      type="primary"
                      onClick={() => {
                        setCurLeaseAction(LEASE_ACTION_STATUS.HANG_UP);
                        handleLeaseOpen(selectedRows, LEASE_ACTION_STATUS.HANG_UP);
                      }}
                    >
                      批量挂起
                    </Button>
                  </>
                )}
                {(status === LEASE_ORDER_STATUS.HANG_UP || isSameHanUpStatus) && (
                  <>
                    <Button
                      type="primary"
                      onClick={() => {
                        setCurLeaseAction(LEASE_ACTION_STATUS.UN_HANG_UP);
                        handleLeaseOpen(selectedRows, LEASE_ACTION_STATUS.UN_HANG_UP);
                      }}
                    >
                      批量解除挂起
                    </Button>
                  </>
                )}
              </Space>
            );
          }}
          toolBarRender={() => {
            return [
              <AsyncExport
                key="export"
                getSearchDataTotal={getSearchDataTotal}
                getSearchParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  return removeBlankFromObject(
                    filterProps({
                      ...params,
                    }),
                  );
                }}
                trigger={<Button type="primary">导出</Button>}
                exportAsync={loanExport}
                taskCode={[ItaskCodeEnValueEnum.LENDING_MGR]}
              />,
              // <Button
              //   key="button"
              //   type="primary"
              //   loading={exportLoading}
              //   onClick={() => {
              //     const { applyTime, lendingTime, ...data } = formRef?.current?.getFieldsValue();
              //     let newForm = { ...data };
              //     console.log(applyTime);
              //     if (applyTime?.length) {
              //       const start = applyTime?.[0];
              //       const end = applyTime?.[1];
              //       // isValid 是否是一个有效的日期 dayjs的方法 由于用了替换插件, 原本应该是moment对象,现在是dayjs对象
              //       const startTime = start?.isValid?.() ? start?.format?.('YYYY-MM-DD') : start;
              //       const endTime = end?.isValid?.() ? end?.format?.('YYYY-MM-DD') : end;
              //       const startApplyTime = start ? `${startTime} 00:00:00` : undefined;
              //       const endApplyTime = end ? `${endTime} 23:59:59` : undefined;
              //       newForm = { ...data, startApplyTime, endApplyTime };
              //     }
              //     // 处理放款时间
              //     if (lendingTime?.length) {
              //       const startLendingTime = `${lendingTime[0].format('YYYY-MM-DD')} 00:00:00`;
              //       const endLendingTime = `${lendingTime[1].format('YYYY-MM-DD')} 23:59:59`;
              //       newForm = { ...data, startLendingTime, endLendingTime };
              //     }

              //     getExport(newForm);
              //   }}
              // >
              //   导出
              // </Button>,
            ];
          }}
        />
        <Modal
          title="放款"
          open={debtModalVisible}
          onOk={handleOk}
          centered
          onCancel={() => {
            handleModalVisible(false);
          }}
        >
          <p
            style={{
              fontSize: 16,
            }}
          >
            本次放款【
            <span
              style={{
                color: 'red',
              }}
            >
              {popParams.items}
            </span>
            】条,总金额【
            <span
              style={{
                color: 'red',
              }}
            >
              {Number(popParams.curAmount).toFixed(2)}
            </span>
            】元
          </p>
          <p
            style={{
              color: 'gray',
            }}
          >
            {popParams.tips}
          </p>
        </Modal>

        {/* 融租放款或者驳回 */}
        <ModalForm
          title={LEASE_ACTION_TEXT[curLeaseAction!]}
          width="500px"
          layout="horizontal"
          visible={leaseModalVisible}
          onVisibleChange={handleLeaseModalOpen}
          modalProps={{
            centered: true,
          }}
          onFinish={async (values) => {
            // return true;
            leaseHandleOK(values);
          }}
        >
          <WaitAuditingPassTextItem />
          {popParams?.extraInfo?.licenseType === LICENSE_TYPE.PERSONAL && (
            <div
              style={{
                marginTop: '10px',
                marginBottom: '10px',
              }}
            >
              注：赋强公证结果：
              <span
                style={{
                  fontSize: 16,
                  color:
                    popParams?.extraInfo?.notarizationStatus === NOTARIZATION_STATUS.CARD_DONE
                      ? 'green'
                      : 'red',
                }}
              >
                {NOTARIZATION_STATUS_MAP.get(popParams?.extraInfo?.notarizationStatus)}
              </span>
            </div>
          )}
          <p className={globalStyle.mb10}>
            <ExclamationCircleOutlined
              style={{
                fontSize: '20px',
                marginRight: 10,
                color: '#faad14',
              }}
            />
            {`是否确认${LEASE_ACTION_TEXT[curLeaseAction!]}？`}
          </p>

          {curLeaseAction === LEASE_ACTION_STATUS.REJECT && (
            <ProFormTextArea
              rules={[
                {
                  required: true,
                  message: '驳回原因为必填',
                },
                {
                  whitespace: true,
                },
              ]}
              fieldProps={{
                maxLength: 300,
              }}
              width="md"
              label="驳回原因"
              name="remark"
            />
          )}
        </ModalForm>
        <Drawer
          title="车架号详情"
          open={vinModalVisible}
          onClose={() => {
            setVinModalVisible(false);
            setVinList([]);
          }}
        >
          <List
            header={<strong>数量：{vinList?.length || 0}</strong>}
            dataSource={vinList}
            renderItem={(item) => <List.Item>{item}</List.Item>}
          />
        </Drawer>
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'businessMng/loan-list'} cacheKey="businessMng/loan-list">
      <LoanList />
    </KeepAlive>
  </>
);
