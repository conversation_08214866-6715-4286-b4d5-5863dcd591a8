html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

.display-none {
  display: none !important;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

// @media (max-width: @screen-xs) {
//   .ant-table {
//     width: 100%;
//     overflow-x: auto;
//     &-thead > tr,
//     &-tbody > tr {
//       > th,
//       > td {
//         white-space: pre;
//         > span {
//           display: block;
//         }
//       }
//     }
//   }
// }

// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.mt30 {
  margin-top: 30px !important;
}

.mt10 {
  margin-top: 10px;
}

.mt16 {
  margin-top: 16px !important;
}

.mt24 {
  margin-top: 24px;
}

.mt20 {
  margin-top: 20px !important;
}

.ml20 {
  margin-left: 20px;
}
.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.fontWBold {
  font-weight: bold;
}

.lineHeight40 {
  line-height: 40px;
}
.mb10 {
  margin-bottom: 10px;
}

.mb20 {
  margin-bottom: 20px !important;
}
.textCenter {
  text-align: center;
}
.textRight {
  text-align: right;
}

.pl20 {
  padding-left: 20px;
}
.pr5 {
  padding-right: 5px;
}
.pr8 {
  padding-right: 8px;
}
.w100 {
  width: 100%;
}
.w100_30 {
  width: calc(100% - 30px);
}
.w100_60 {
  width: calc(100% - 60px);
}
.w90 {
  width: 90%;
}

.stepItemDesc {
  :global {
    .ant-steps-item-description {
      width: 150px;
      text-align: left;
    }
  }
}

.childrenTr {
  :global {
    .ant-table-cell-with-append {
      .indent-level-1 {
        padding-left: 0 !important;
      }
    }
  }
}

.stepsInitial {
  :global {
    .stepsInitial {
      display: inherit;
    }
  }
}

.withHold {
  :global {
    .ant-form-item-label {
      width: 150px;
    }
  }
}

.iconCss {
  margin-right: 10px;
  color: #faad14;
  font-size: 20px;
}

.formModal {
  :global {
    .ant-form-item-label {
      width: 120px;
    }
  }
}

.formModalLabel130 {
  :global {
    .ant-form-item-label {
      width: 135px;
    }
  }
}
.formModalLabel140 {
  :global {
    .ant-form-item-label {
      width: 140px;
    }
  }
}
.formModalLabel150 {
  :global {
    .ant-form-item-label {
      width: 150px;
    }
  }
}
.fontS16 {
  font-size: 16px;
}
.fontS14 {
  font-size: 14px;
}

.pointer {
  cursor: pointer;
}

//抽屉在指定区域打开
.drawerLabel {
  position: relative;
  // overflow: hidden;
  // height: 200px;
  // height: calc(100% + 48px);
  // width: calc(100% + 48px);
  // padding: 48px;
  // overflow: hidden;
  // text-align: center;
  // background: #fafafa;
  // border: 1px solid #ebedf0;
  // border-radius: 2px;
}
.colorBlue {
  color: #1677ff;
}

.colorRed {
  color: red;
}
