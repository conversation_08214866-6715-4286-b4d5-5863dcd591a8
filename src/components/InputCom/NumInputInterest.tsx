import { Input, InputNumber } from 'antd';
import React, { useState } from 'react';

// type Currency = 'rmb' | 'dollar';

type RegionItem = 'CLOSE_RANGE' | 'OPEN_RANGE';
interface RegionValue {
  minOpenOrCloseRange?: RegionItem; // 最小值开闭区间
  minValue?: number; // 最小值
  maxOpenOrCloseRange?: RegionItem; // 最大值开闭区间
  maxValue?: number; // 最大值开闭区间
}
interface RegionInputProps {
  value?: RegionValue;
  onChange?: (value: RegionValue) => void;
  disabledForm?: boolean;
  minValueReplace?: string; // 字段替换名
  maxValueReplace?: string; // 字段替换名
  precision?: number;
  placeholder?: { start: string; end: string };
  addonAfter?: string; //  单位
  max?: number | undefined; // 最大值
}

const RegionInput: React.FC<RegionInputProps> = ({
  value = {},
  onChange,
  disabledForm,
  minValueReplace,
  maxValueReplace,
  precision,
  placeholder,
  addonAfter,
  max = undefined,
}) => {
  // console.log(value, '我是组件value');

  const [minValue, setMinValue] = useState<number>();
  const [maxValue, setMaxValue] = useState<number>();

  const triggerChange = (changedValue: {
    minValue?: number; // 最小值
    maxValue?: number;
  }) => {
    onChange?.({
      [minValueReplace || 'minValue']: minValue,
      [maxValueReplace || 'maxValue']: maxValue,
      ...value,
      ...changedValue,
    });
  };

  // 加载初始值
  const onMinValueChange = (newNumber: any) => {
    if (Number.isNaN(minValue)) {
      return;
    }
    if (!('minValue' in value && `${minValueReplace}` in value)) {
      setMinValue(newNumber);
    }
    triggerChange({ [minValueReplace || 'minValue']: newNumber });
  };

  const onMaxValueChange = (newNumber: any) => {
    if (Number.isNaN(maxValue)) {
      return;
    }
    if (!('maxValue' in value && `${maxValueReplace}` in value)) {
      setMaxValue(newNumber);
    }
    triggerChange({ [maxValueReplace || 'maxValue']: newNumber });
  };

  return (
    <div style={{ display: 'flex' }}>
      <Input.Group compact>
        <InputNumber
          style={{ width: '95%' }}
          placeholder={placeholder?.start}
          value={value?.[minValueReplace || 'minValue']}
          precision={precision}
          min={0}
          max={max}
          addonAfter={addonAfter}
          disabled={disabledForm}
          onChange={onMinValueChange}
        />
      </Input.Group>
      <span style={{ marginRight: 8, alignContent: 'center' }}>～</span>
      <Input.Group compact>
        <InputNumber
          style={{ width: '95%' }}
          placeholder={placeholder?.end}
          value={value?.[maxValueReplace || 'maxValue']}
          precision={precision}
          min={0}
          max={max}
          addonAfter={addonAfter}
          disabled={disabledForm}
          onChange={onMaxValueChange}
        />
      </Input.Group>
    </div>
  );
};

export default React.memo(RegionInput);
