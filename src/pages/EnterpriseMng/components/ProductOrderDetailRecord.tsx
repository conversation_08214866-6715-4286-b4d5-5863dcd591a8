/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 14:22:39
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-08 16:18:09
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/ProductOrderDetailRecord.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { downLoadExcel } from '@/utils/utils';
import { history, Link } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Button, Table } from 'antd';
import React, { useState } from 'react';
import { orderExport, queryOrder } from '../service';

const ProductOrderDetailRecord = (props: { secondClassification: string }) => {
  const userNo = history?.location?.query?.userNo;
  const [exportLoading, setExportLoading] = useState(false);
  const { tableProps } = useRequest(
    (params) => {
      return queryOrder({
        ...params,
        userNo: userNo as string,
        secondClassification: props?.secondClassification,
      });
    },
    {
      paginated: true,
      defaultPageSize: 10,

      formatResult: (response) => {
        return { list: response.data, total: response.total };
      },
    },
  );
  const mapStatus = {
    0: '待放款',
    1: '待还款',
    2: '提前结清',
    3: '结清',
    4: '逾期',
    5: '逾期结清',
    6: '坏账',
  };
  const columns = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '渠道订单号',
      dataIndex: 'orderDisplayId',
    },
    {
      title: '金额',
      dataIndex: 'orderAmount',
    },
    {
      title: '正常还款日',
      dataIndex: 'repayTime',
    },

    {
      title: '订单状态',
      dataIndex: 'status',
      render: (val) => {
        return mapStatus[val] || '-';
      },
    },
    {
      title: '进件时间',
      dataIndex: 'orderTime',
    },
    {
      title: '操作',
      type: 'option',
      render: (_, record) => (
        <>
          <Link to={`/businessMng/detail?orderNo=${record.orderNo}`}>查看详情</Link>
        </>
      ),
    },
  ];
  const queryOrderExport = (params: { userNo: string; secondClassification: string }) => {
    setExportLoading(true);
    orderExport(params)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          onClick={() =>
            queryOrderExport({
              userNo,
              secondClassification: props?.secondClassification,
            })
          }
          loading={exportLoading}
          type="primary"
        >
          导出
        </Button>
      </div>
      <Table columns={columns} {...tableProps} />
    </div>
  );
};

export default ProductOrderDetailRecord;
