/*
 * @Author: your name
 * @Date: 2021-03-20 15:03:17
 * @LastEditTime: 2021-04-28 13:45:01
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/order-detail.tsx
 */
import DetailCards from '@/components/DetailCards';
import HeaderTab from '@/components/HeaderTab/index';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import React from 'react';
import * as components from './components';
import { getLoanInfo } from './service';

const OrderDetail: React.FC<any> = () => {
  const { orderNo } = history.location.query;
  const { data } = useRequest(() => {
    return getLoanInfo(orderNo);
  });

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <components.StepProgress orderNo={orderNo} />
        <DetailCards.IncomingInfo orderNo={orderNo} />
        <components.LoanInfo dataInfo={data} />
        <components.RepayInfo orderNo={orderNo} />
        <components.Debt orderNo={orderNo} />
        <components.FeeRecord orderNo={orderNo} />
        <components.Contract orderNo={orderNo} />
      </PageContainer>
    </>
  );
};

export default OrderDetail;
