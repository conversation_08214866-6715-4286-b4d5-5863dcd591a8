import { ProTable } from '@ant-design/pro-components';
import React from 'react';
import { getTransferLog } from '../service';

const OperateRecordTable = (props: { recordNo: string }) => {
  const { recordNo } = props;
  if (!recordNo) return null;
  const columns = [
    {
      title: '操作ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '类型',
      dataIndex: 'operationDesc',
      key: 'operationDesc',
    },
    {
      title: '收款账户',
      dataIndex: 'operateContent',
      key: 'operateContent',

      render: (dom, data) => {
        if (data.extendInfo) {
          const parseData = JSON.parse(data.extendInfo);
          return parseData?.collectionAccountEncrypt;
        }
        return '-';
      },
    },
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
  ];
  return (
    <ProTable
      columns={columns}
      search={false}
      toolBarRender={false}
      request={(params) => getTransferLog({ recordNo, ...params })}
    />
  );
};

export default OperateRecordTable;
