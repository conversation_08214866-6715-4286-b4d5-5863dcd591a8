/*
 * @Author: alan771.tu <EMAIL>
 * @Date: 2025-04-15 10:38:32
 * @LastEditors: alan771.tu <EMAIL>
 * @LastEditTime: 2025-04-16 16:38:32
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/NewApplyReliefModal.tsx
 */
import RepaymentAttachUpload from '@/components/RepaymentAttachUpload/RepaymentAttachUpload';
import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { getBaseUrl } from '@/utils/utils';
import type { ProColumns } from '@ant-design/pro-components';
import { EditableProTable, ProTable } from '@ant-design/pro-components';
import { ModalForm, ProFormDependency, ProFormSelect, ProFormTextArea } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import { useDebounceFn } from 'ahooks';
import type { UploadFile } from 'antd';
import { Alert, Button, Form, message } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useCallback, useEffect, useState } from 'react';
import type { IbillInfo } from '../data';
import { calculateBill, getCollectReliefList, submitCollectRelief } from '../services';
import { CarOfflineRepayColumns, OrderOfflineRepayColumns } from './columns';
import './index.less';

export type AddReliefModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  // values: CallPayForm|{};
  onVisibleChange: any;
  overDueAmount: number;
  overdueCaseNo: string;
  productCode: string;
  productSecondaryCode: string;
  orderNoList: { orderNo: string; totalOverdueAmount: string }[];
  dimension?: number;
};

enum IdimensionEnCode {
  TERM_BILL = 1, // 车辆纬度
  ORDER_TERM_BILL = 2, // 订单纬度
  SUBJECT_MATTER_BILL = 3, //总账
}
export interface AmountDetailType {
  orderNo: string;
  amount: number;
}

const labelColSpan = 3;

const NewAddReliefModal: React.FC<AddReliefModalProps> = (props) => {
  const { overdueCaseNo, orderNoList, productCode, productSecondaryCode, dimension = 3 } = props;

  const { urgePersonList: userList } = useModel('userList');
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [form] = Form.useForm();
  const [modalLoading, setModalLoading] = useState(false);
  const [getCollectReliefListLoading, setGetCollectReliefListLoading] = useState(false);
  const [billInfoData, setBillInfoData] = useState<IbillInfo>(null);
  const [errorDesc, setErrorDesc] = useState('');

  //从后端获取数据
  const getRepayData = async () => {
    try {
      const err = form.getFieldsError(['remission', 'repayAmount']);
      const hasErrors = err.some((item) => item.errors.length > 0);
      //如果有校验error不请求后端接口
      if (hasErrors) {
        return;
      }
      const curFormData = form?.getFieldsValue();
      const { remission } = curFormData || {};
      setModalLoading(true);
      const res = await calculateBill({
        billInfoList: billInfoData?.billRspDTOList || [],
        remissionList: remission,
        repayAmount: 0, // 减免金额
      });
      setErrorDesc('');
      setBillInfoData(res);
    } catch (error: any) {
      //试算单独处理报错逻辑
      if (![0, 200].includes(error?.ret)) {
        setErrorDesc(error?.msg);
      }
    } finally {
      setModalLoading(false);
    }
  };

  const { run, cancel } = useDebounceFn(getRepayData, { wait: 500 });

  // 计算减免总金额
  function calculateRemissionAmount(remission: any) {
    const {
      remissionPrincipal,
      remissionInterest,
      remissionPenalty,
      // remissionAdvanceSettleLiquidatedDamages, // 违约金
      remissionDelayAmount,
    } = remission;
    const amount = new BigNumber(remissionPrincipal)
      .plus(remissionInterest)
      .plus(remissionPenalty)
      // .plus(remissionAdvanceSettleLiquidatedDamages)
      .plus(remissionDelayAmount)
      .toNumber();
    return amount;
  }

  // 计算减免总金额
  function getAmount() {
    const init = {
      expectRepayAmount: 0,
      totalAmountUnpaid: 0,
      totalPrincipalUnpaid: 0,
      totalInterestUnpaid: 0,
      totalOverduePenaltyUnpaid: 0,
      totalBreach: 0,
      totalLate: 0,
    };
    if (billInfoData?.billRspDTOList.length) {
      return billInfoData?.billRspDTOList?.reduce((pre: any, cur) => {
        const {
          expectRepayAmount,
          totalAmountUnpaid,
          totalPrincipalUnpaid,
          totalInterestUnpaid,
          totalOverduePenaltyUnpaid,
          totalBreach,
          totalLate,
        } = cur;
        const _expectRepayAmount = new BigNumber(pre.expectRepayAmount)
          .plus(expectRepayAmount)
          .toNumber();
        const _totalAmountUnpaid = new BigNumber(pre.totalAmountUnpaid)
          .plus(totalAmountUnpaid)
          .toNumber();
        const _totalPrincipalUnpaid = new BigNumber(pre.totalPrincipalUnpaid)
          .plus(totalPrincipalUnpaid)
          .toNumber();
        const _totalInterestUnpaid = new BigNumber(pre.totalInterestUnpaid)
          .plus(totalInterestUnpaid)
          .toNumber();
        const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
          .plus(totalOverduePenaltyUnpaid)
          .toNumber();
        const _totalBreach = new BigNumber(pre.totalBreach).plus(totalBreach).toNumber();
        const _totalLate = new BigNumber(pre.totalLate).plus(totalLate).toNumber();
        return {
          expectRepayAmount: isNaN(_expectRepayAmount) ? 0 : _expectRepayAmount,
          totalAmountUnpaid: isNaN(_totalAmountUnpaid) ? 0 : _totalAmountUnpaid,
          totalPrincipalUnpaid: isNaN(_totalPrincipalUnpaid) ? 0 : _totalPrincipalUnpaid,
          totalInterestUnpaid: isNaN(_totalInterestUnpaid) ? 0 : _totalInterestUnpaid,
          totalOverduePenaltyUnpaid: isNaN(_totalOverduePenaltyUnpaid)
            ? 0
            : _totalOverduePenaltyUnpaid,
          totalBreach: isNaN(_totalBreach) ? 0 : _totalBreach,
          totalLate: isNaN(_totalLate) ? 0 : _totalLate,
        };
      }, init);
    } else {
      return init;
    }
  }

  // 减免编辑项
  const editColumns: ProColumns<AmountDetailType>[] = [
    {
      title: '费项',
      dataIndex: 'option',
      editable: false,
      width: 100,
      render: () => {
        return '金额';
      },
    },
    {
      title: '减免本金(元)',
      dataIndex: 'remissionPrincipal',
      valueType: 'digit',
      fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalPrincipalUnpaid,
            message: `金额不可超过${getAmount().totalPrincipalUnpaid}`,
          },
        ],
      },
    },
    {
      title: '减免利息(元)',
      dataIndex: 'remissionInterest',
      valueType: 'digit',
      fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalInterestUnpaid,
            message: `金额不可超过${getAmount().totalInterestUnpaid}`,
          },
        ],
      },
    },
    {
      title: '减免罚息(元)',
      dataIndex: 'remissionPenalty',
      valueType: 'digit',
      fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalOverduePenaltyUnpaid,
            message: `金额不可超过${getAmount().totalOverduePenaltyUnpaid}`,
          },
        ],
      },
    },
    {
      title: '减免滞纳金(元)',
      dataIndex: 'remissionDelayAmount',
      valueType: 'digit',
      fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalLate,
            message: `金额不可超过${getAmount().totalLate}`,
          },
        ],
      },
    },
  ];

  // 获取减免账单列表
  const getCollectReliefListData = useCallback(
    async (orderNo: string) => {
      if (!orderNo) {
        return;
      }
      try {
        setGetCollectReliefListLoading(true);
        const res = await getCollectReliefList({
          caseNo: overdueCaseNo,
          orderNo,
        });
        setBillInfoData(res);
      } catch (error) {
      } finally {
        setGetCollectReliefListLoading(false);
      }
    },
    [overdueCaseNo],
  );

  useEffect(() => {
    //非循环单笔
    if (productCode?.substring(0, 2) !== PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN) {
      getCollectReliefListData(orderNoList[0]?.value);
    }
  }, [orderNoList, getCollectReliefListData, productCode]);

  return (
    <>
      <ModalForm
        title="申请减免"
        width={1000}
        form={form}
        className="apply_relief_modal"
        layout="horizontal"
        open={props.modalVisible}
        onOpenChange={props.onVisibleChange}
        loading={getCollectReliefListLoading || modalLoading}
        initialValues={{
          urgePerson: currentUser?.userId,
          remission: [
            {
              id: 1,
              remissionPrincipal: 0,
              remissionInterest: 0,
              remissionPenalty: 0,
              // remissionAdvanceSettleLiquidatedDamages: 0, // 违约金
              remissionDelayAmount: 0,
            },
          ],
        }}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          okButtonProps: { disabled: true },
          onCancel: () => {
            cancel(); //取消防抖请求 有bug,关闭弹窗，同时是失焦的会触发计算请求。有一次报错展示
            setErrorDesc('');
          },
        }}
        submitter={{
          render: (propsSubmitter, defaultDoms) => (
            <div className="buttonCss">
              <Button
                type="primary"
                key="submit"
                disabled={modalLoading || getCollectReliefListLoading}
                onClick={() => propsSubmitter.form?.submit?.()}
              >
                提交
              </Button>
              {defaultDoms[0]}
            </div>
          ),
        }}
        onFinish={async (values) => {
          console.log('values', values);
          try {
            //试算报错不允许提交
            if (errorDesc) {
              return;
            }

            const { remission, attach, urgePerson, remark, orderNo } = values;
            const remissionTotalAmount = calculateRemissionAmount(values.remission?.[0]);
            // const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
            //   .minus(remissionTotalAmount)
            //   .toNumber();
            if (remissionTotalAmount === 0) {
              message.error('减免金额不能为0');
              return;
            }
            setModalLoading(true);
            const params = {
              billInfoList: billInfoData?.billRspDTOList,
              remissionList: remission,
              attach,
              repayAmount: 0,
              urgePersonId: urgePerson,
              overdueCaseNo,
              orderNo: orderNo?.value ?? orderNoList[0]?.value,
              dimension,
              remark,
            };

            await submitCollectRelief(params);
            message.success('提交成功');
            props.onVisibleChange(false);
          } catch (error) {
          } finally {
            setModalLoading(false);
          }
        }}
      >
        {errorDesc && (
          <Alert message={errorDesc} type="error" showIcon style={{ marginBottom: 10 }} />
        )}
        <ProFormSelect
          rules={[{ required: true }]}
          labelCol={{ span: labelColSpan }}
          request={async () => userList}
          placeholder="请输入催收员"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            disabled: true,
            // filterOption: (input: string, option: { label: string }) =>
            //   option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
          }}
          width="md"
          name="urgePerson"
          label="催收员"
        />
        {/* 非循环的单笔单批-不需要选择订单号 */}
        {productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN && (
          <ProFormSelect
            rules={[{ required: true }]}
            labelCol={{ span: labelColSpan }}
            request={async () => orderNoList}
            disabled={getCollectReliefListLoading}
            onChange={async (value) => {
              getCollectReliefListData(value?.value);
            }}
            placeholder="请输入订单号"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
              labelInValue: true,
            }}
            width="md"
            name="orderNo"
            label="订单号"
          />
        )}
        <div className="relief_detail_container">
          <div className="flex_contianer">
            <ProFormDependency name={['remission']} labelCol={{ span: labelColSpan }}>
              {(values) => {
                const amount = calculateRemissionAmount(values.remission?.[0]);
                return (
                  <div className="relief_detail_title no_border">
                    <div className="detail_title">减免详情</div>
                    <span className="relief_offline_label">减免总金额：</span>
                    <span className="relief_offline_amount">{isNaN(amount) ? '-' : amount}</span>
                  </div>
                );
              }}
            </ProFormDependency>
            <EditableProTable
              className="remission_table"
              name="remission"
              rowKey="id"
              toolBarRender={false}
              pagination={false}
              columns={editColumns}
              recordCreatorProps={false}
              editable={{
                type: 'multiple',
                editableKeys: [1],
                actionRender: () => {
                  return [];
                },
              }}
            />
            <div className="account_info_title">请确认账单信息和还款金额无误后提交：</div>
            <div className="account_info_table">
              <ProTable
                search={false}
                options={false}
                pagination={false}
                columns={
                  dimension === IdimensionEnCode.ORDER_TERM_BILL
                    ? OrderOfflineRepayColumns
                    : CarOfflineRepayColumns.map((column) => {
                        // 小贷（圆易借）不展示车架号
                        if (
                          productSecondaryCode === SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY &&
                          (column.dataIndex === 'subjectMatterNo' ||
                            column.key === 'subjectMatterNo')
                        ) {
                          return {
                            ...column,
                            hidden: true,
                          };
                        }
                        return column;
                      })
                }
                dataSource={billInfoData?.billRspDTOList as any}
                scroll={{ x: 'max-content' }}
              />
            </div>
          </div>
        </div>
        <ProFormTextArea
          labelCol={{ span: labelColSpan }}
          placeholder="请输入备注"
          width="md"
          fieldProps={{ maxLength: 500 }}
          label="备注"
          name="remark"
        />
        <RepaymentAttachUpload
          labelCol={{ span: labelColSpan }}
          label="还款凭证"
          name="attach"
          accept=".png,.jpg,.jpeg"
          action={`${getBaseUrl()}/repayment/repay/uploadRepayPic`}
          data={{}}
          transform={(values: UploadFile[]) => {
            const attach = values
              ?.filter((item) => item.response)
              .map((file) => {
                const { response, name, uid } = file;
                const { data } = response as any;
                return {
                  url: data,
                  name,
                  uid,
                  filePath: null,
                };
              });
            return { attach };
          }}
        />
      </ModalForm>
    </>
  );
};

export default NewAddReliefModal;
