// 英文-en 数字-code 中文-Zh
import { OrderListItem } from '@/pages/BusinessCashMng/data';
import { OrderListPagination } from '@/pages/BusinessMng/data';
import type { ReactNode } from 'react';

export type AfterLoanListParams = {
  pageNumber?: number | undefined;
  pageSize?: number | undefined;
  accountName?: string;
  accountNumber?: string;
  classification?: string;
  orderNo?: string;
  productName?: string;
  repayEndTime?: string;
  repayStartTime?: string;
  status?: number;
};

export interface AfterLoanExportPages {
  pageNumber: number;
  pageSize: number;
}

export interface AfterLoanListItem {
  createTime: ReactNode | { children: ReactNode; props: any };
  repayNo: string;
  accountName: string;
  accountNumber: string;
  amountDue: number;
  classification: string;
  cost: number;
  orderNo: string;
  overdueDay: number;
  overduePenaltyInterest: number;
  principalInterest: number;
  principalRepayable: number;
  productName: string;
  repayTerm: number;
  repayTime: string;
  status: number;
  productCode: string;
  termDetail: string;
  advancedStatus: number;
  advanceAmount: string;
  advanceFirstTime: string;
  advanceSucceedTime: string;
}

export interface AfterLoanListPagination {
  total: number;
  size: number;
  current: number;
}

export interface AfterLoanListListData {
  list: OrderListItem[];
  pagination: Partial<OrderListPagination>;
}

export interface ProductNameList {
  code: string;
  desc: string;
}

export interface RepayManageItem {
  createTime: string;
  orderNo?: string;
  accountName?: string;
  productCode?: string;
  termDetail?: string;
  accountNumber?: string;
  classification?: string;
  secondaryClassification?: string;
  productName?: string;
  annualInterestRate?: string;
  lastClearDate?: string;
  repurchaseStatus?: number;
  buybackAmount?: string;
  buybackFirstTime?: string;
  buybackSucceedTime?: string;
}

export interface RepayRegistItem {
  paymentAmount: number;
  combineAmount: number;
  repayPlanNo?: string;
  termDetail?: string;
  orderNo?: string;
  productCode?: string;
  accountNumber?: string;
  accountName?: string;
  classification?: string;
  productFirstTypeName?: string;
  productSecondTypeName?: string;
  secondaryClassification?: string;
  productName?: string;
  actualRepaymentAmount?: string;
  principal?: string;
  interest?: string;
  penaltyInterest?: string;
  cost?: string;
  repayTypeStr?: string;
  repayTime?: string;
  repayType?: number;
  repayNode?: number;
  capitalAmount?: string;
}

export type IrepayExportAsyncParams = {
  accountName: string;
  accountNumber: string;
  channelIds: [];
  classification: string;
  clearEndTime: string;
  clearStartTime: string;
  current: number;
  endCreatedAt: string;
  licensePlateNo: string;
  orderNo: string;
  pageSize: number;
  productCode: string;
  secondaryClassification: string;
  startCreatedAt: string;
  status: number;
  storeIds: string[];
  vin: string;
};

// 二级分类 英文对数字的映射
export const secondaryClassificationEnCodeMap = {
  BUSINESS_ACCOUNTING_PERIOD: '0101',
  DARK_FACTORING: '0102',
  ACCOUNTS_RECEIVABLE: '0103',
  BUSINESS_ACCOUNTING_PERIOD_SHARED: '0104',
  DARK_FACTORING_SHARED: '0105',
  ACCOUNTS_RECEIVABLE_SHARED: '0106',
  FINANCE_LEASE: '0201',
  LOAN_INSTALLMENT_CREDIT: '0301',
  CAR_INSURANCE: '0303',
  ENTERPRISE_LOAN: '0304',
};

export type IsecondaryClassificationEn = keyof typeof secondaryClassificationEnCodeMap;

export const sellerFactoringClassification = {
  BUSINESS_ACCOUNTING_PERIOD: '明保',
  DARK_FACTORING: '暗保',
  BUSINESS_ACCOUNTING_PERIOD_SHARED: '共享明保',
  DARK_FACTORING_SHARED: '共享暗保',
  ACCOUNTS_RECEIVABLE: '应收账款',
  ACCOUNTS_RECEIVABLE_SHARED: '共享应收账款',
};

export const financeLeaseClassification = {
  FINANCE_LEASE: '小圆车融',
};

export const smallLoanClassification = {
  LOAN_INSTALLMENT_CREDIT: '圆易借',
  CAR_INSURANCE: '小圆车险分期',
  ENTERPRISE_LOAN: '圆商贷',
};

// 一级下面有哪些二级的分类
export const firstTosecondaryClassificationMap = {
  SELLER_FACTORING: sellerFactoringClassification,
  FINANCE_LEASE: financeLeaseClassification,
  SMALL_LOAN: smallLoanClassification,
};

export type CLASSIFICATION_TYPE = 'SELLER_FACTORING' | 'FINANCE_LEASE' | 'SMALL_LOAN';
