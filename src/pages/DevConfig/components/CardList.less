.card-list-wrapper {
  padding: 25px 30px;
  padding-right: 80px;
  background-color: #fff;
  .ant-timeline-item-head {
    // top: 25px;
  }
  .ant-timeline-item-tail {
    // top: 25px;
  }
  .ant-timeline-item-content {
    margin-left: 50px;
  }
  .ant-card-type-inner {
    box-shadow: 1px 1px 6px #ddd;
  }
  .van-task-priority {
    // color: #aaa;
    color: rgba(27, 144, 255, 0.6);
    font-weight: bold;
  }
  .gray {
    color: #bbb;
  }
  //
  .van-task-id {
    color: rgba(27, 144, 255, 1);
    font-weight: bold;
    font-size: 20px;
  }
  .description {
    margin-left: 12px;
    color: #aaa;
  }
  .gray-header {
    // display: inline-block;
    width: 320px;
    // margin-left: 120px;
    margin-top: 3px;
  }
  .header-group {
    .header-key {
      width: 150px;
      margin-left: 10px;
    }
    .header-separator {
      padding: 0 5px;
    }
    .header-value {
      width: 160px;
    }
  }
  .ant-card-type-inner .ant-card-head-title {
    padding: 6px 0;
  }
  .ant-card-body {
    padding: 10px 24px;
  }
}
.task-id {
  display: flex;
  .left {
    flex: 1;
  }
  .right {
    min-width: 50px;
    padding-top: 10px;
  }
}
.card-item-grayscale {
  color: #ccc;
  filter: grayscale(100%);
}
