import HeaderTab from '@/components/HeaderTab';
import { downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, DatePicker, Modal } from 'antd';
import React, { memo, useRef } from 'react';
import { columns } from './columns';
import AddModal from './components/AddModal';
import type { IpolicyItem } from './services';
import { exportPolicyList } from './services';
import { getPolicyList } from './services';
const { RangePicker } = DatePicker;
const PolicyList = () => {
  const actionRef = useRef<ActionType>();
  const timeRef = useRef<{ timeRange: string[] }>({ timeRange: [] });
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<IpolicyItem>
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          request={(params) => getPolicyList(params)}
          columns={columns}
          toolBarRender={() => {
            return [
              <AddModal reload={actionRef.current?.reload}>
                <Button key="button" type="primary">
                  新增
                </Button>
              </AddModal>,

              <Button
                key="export"
                type="primary"
                onClick={async () => {
                  Modal.confirm({
                    title: '商业险开始时间',
                    bodyStyle: { margin: 0, padding: 0 },
                    width: 500,
                    content: (
                      <RangePicker
                        style={{ width: 360 }}
                        showTime
                        onChange={(value: any, dateString) => {
                          timeRef.current.timeRange = dateString;
                        }}
                        placeholder={['开始时间', '结束时间']}
                      />
                    ),
                    async onOk() {
                      const [startTime, endTime] = timeRef.current.timeRange;
                      const res = await exportPolicyList({ startTime, endTime });
                      downLoadExcel(res);
                    },
                  });
                }}
              >
                导出
              </Button>,
            ];
          }}
        />
      </PageContainer>
    </>
  );
};
export default memo(PolicyList);
