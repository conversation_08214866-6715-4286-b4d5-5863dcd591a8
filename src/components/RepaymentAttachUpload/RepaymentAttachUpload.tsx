import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { ProFormUploadButton } from '@ant-design/pro-form';
import type { UploadFile } from 'antd';
import { message, Upload } from 'antd';
import React from 'react';

const RepaymentAttachUpload: React.FC<any> = (props) => {
  return (
    <ProFormUploadButton
      labelCol={{ span: 6 }}
      action={`${getBaseUrl()}/repayment/oss/common/uploadfile`}
      rules={[{ required: true }]}
      fieldProps={{
        headers: getAuthHeaders(),
        name: 'file',
        data: props?.data ?? { acl: 'PUBLIC_READ', destPath: 'EP_AUTH_INFO' }, // 后端商量默认格式
        multiple: true,
        beforeUpload: (file) => {
          const isLt10M = file.size / 1024 / 1024 < 10;
          if (!isLt10M) {
            message.error(`${file.name}超过了10M`);
            return false || Upload.LIST_IGNORE;
          }
          return true;
        },
      }}
      onChange={({ file }) => {
        if (
          file.status === 'done' &&
          file.response?.ret === 200 &&
          file?.response?.data?.netWorkPath
        ) {
          file.url = file?.response?.data?.netWorkPath;
          (file as any).filePath = file?.response?.data?.filePath;
        }
      }}
      transform={(values: UploadFile[]) => {
        const attach = values
          ?.filter((item) => item.url)
          .map((file) => {
            const { url, name, uid } = file;
            const { filePath } = file as any;
            return {
              url,
              name,
              uid,
              filePath,
            };
          });
        return { attach };
      }}
      max={10}
      label="还款凭证"
      name="attach"
      accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
      {...props}
    />
  );
};

export default RepaymentAttachUpload;
