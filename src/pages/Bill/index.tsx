/* eslint-disable react-hooks/exhaustive-deps */
import HeaderTab from '@/components/HeaderTab/index';
import { PRODUCT_CLASSIFICATION_CODE } from '@/enums';
import globalStyle from '@/global.less';
import { getProductNameEnum } from '@/services/enum';
import { downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, Link, useAccess } from '@umijs/max';
import { Button, DatePicker } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useEffect, useRef, useState } from 'react';
import { KeepAlive, useAliveController } from 'react-activation';
import OfflineRepayModal from './components/OfflineRepayModal';
import type { BillListItem, BillListParams } from './data';
import './index.less';
import { billExport, getBillList } from './service';

const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
  return (
    <DatePicker.RangePicker
      {...rest}
      className={globalStyle.w100}
      // disabledDate={(current: any) => {
      //   return current && current > dayjs().endOf('day');
      // }}
    />
  );
};

const BillList: React.FC<any> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [exportLoading, setExportLoading] = useState(false);
  const [repayModalVisilbe, setRepayModalVisible] = useState(false);
  const access = useAccess();
  const getExport = (form: BillListParams) => {
    setExportLoading(true);
    billExport(form)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  const query = history.location.query;

  useEffect(() => {
    // 重置无法清除 initialValue 所以这么设置一下
    formRef.current?.setFieldsValue({
      billNo: query?.orderNo,
      accountNumber: query?.userNo,
    });
    // request 的时候 有几率拿不到 表单值 所以主动请求
    const timerId = setTimeout(() => {
      // actionRef.current?.reload()
      formRef.current?.submit();
    }, 800);

    return () => {
      clearTimeout(timerId);
    };
  }, []);
  const columns: ProColumns<BillListItem>[] = [
    {
      title: '账单ID',
      dataIndex: 'billNo',
    },

    {
      title: '用户名称',
      dataIndex: 'accountName',
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      // valueEnum: productNameList,
      valueType: 'select',
      // 如果是企业财务，默认共享应收账款，应收账款
      // initialValue:
      initialValue: access.hasRole('enterpriseFinance') ? ['010301', '010601'] : [],
      request: () => getProductNameEnum(PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING),
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        // defaultValue:
        showArrow: true,
        // 如果是企业财务，默认共享应收账款，应收账款
        disabled: access.hasRole('enterpriseFinance') ? true : false,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '账单金额',
      dataIndex: 'totalAmount',
      search: false,
    },
    {
      title: '账单周期',
      dataIndex: 'billCycle',
      valueType: 'dateRange',
      render(text, row) {
        return row?.billCycle || '-';
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          billCycleStart: `${value[0].split(' ')[0]}`,
          billCycleEnd: `${value[1].split(' ')[0]}`,
        }),
      },
    },
    {
      title: '账单日',
      dataIndex: 'billingDate',
      valueType: 'date',
      search: false,
    },
    {
      title: '保理服务费率（%）',
      dataIndex: 'factoringServiceFee',
      key: 'factoringServiceFee',
      search: false,
    },
    {
      title: '保理服务费(元)',
      dataIndex: 'factoringServiceCharge',
      key: 'factoringServiceCharge',
      search: false,
    },
    {
      title: '账单状态',
      dataIndex: 'status',
      valueEnum: {
        1: '待出账',
        2: '待确认',
        3: '待债转',
        4: '待还款',
        5: '部分还款',
        6: '正常结清',
        7: '逾期结清',
        70: '逾期',
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDas',
      key: 'overdueDas',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <>
          <Link
            to={`/businessMng/bill-detail?billNo=${record.billNo}&accountNumber=${record.accountNumber}`}
          >
            查看详情
          </Link>
        </>
      ),
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<BillListItem>
          actionRef={actionRef}
          formRef={formRef}
          rowKey={(record) => record.billNo + record.accountNumber}
          scroll={{ x: 'max-content' }}
          // request={async ({ current, ...rest }) => {
          //   const { data } = await getBillList({ pageNumber: current, ...rest });
          //   return {
          //     data: data.records,
          //     success: true,
          //     // 不传会使用 data 的长度，如果是分页一定要传
          //     total: data.total,
          //   };
          // }}
          request={(params) => getBillList(params)}
          toolBarRender={() => {
            return [
              <Button
                key="offlineBtn"
                type="primary"
                onClick={() => {
                  setRepayModalVisible(true);
                }}
              >
                线下还款
              </Button>,
              <Button
                key="exportBtn"
                type="primary"
                loading={exportLoading}
                onClick={() => {
                  const { billCycle, ...data } = formRef?.current?.getFieldsValue();
                  let newForm = { ...data };
                  if (billCycle?.length) {
                    const billCycleStart = `${billCycle[0].format('YYYY-MM-DD')}`;
                    const billCycleEnd = `${billCycle[1].format('YYYY-MM-DD')}`;
                    newForm = { ...data, billCycleStart, billCycleEnd };
                  }
                  getExport(newForm);
                }}
              >
                导出
              </Button>,
            ];
          }}
          columns={columns}
        />
        {/* 线下还款弹窗 */}
        <OfflineRepayModal
          visible={repayModalVisilbe}
          onVisibleChange={(visible) => {
            setRepayModalVisible(visible);
          }}
          onOk={async () => {
            setRepayModalVisible(false);
            actionRef?.current?.reload();
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => {
  const { refresh } = useAliveController();

  // 从 另外一个页面跳 到该列表 由于订单号是变的，要根据query条件赋值到列表查询中，查询到最新数据,  是不希望缓存的
  useEffect(() => {
    const isCache = history?.location?.query?.isCache !== '0';
    if (!isCache) {
      refresh('businessMng/bill-list');
    }
  }, []);
  return (
    <>
      <HeaderTab />
      <KeepAlive name={'businessMng/bill-list'}>
        <BillList />
      </KeepAlive>
    </>
  );
};
