.warn-text-color {
  color: red;
}
.modal-wrapper {
  .step-wrapper {
    & > div {
      margin-bottom: 10px;
    }
    .step-title {
      font-size: 16px;
    }
  }
}
.row-divider-wrap {
  .row-divider {
    margin: 10px 0;
  }
}
.detail-label {
  display: inline-block;
  width: 110px;
  color: #666;
}
.gray-text {
  color: #666;
}
// .upload-area-inline{
//   .ant-upload-drag{
//     display: inline-block;
//     width: 50%;
//   }
//   .ant-upload-list{
//     display: inline-block;
//   }
// }
.primary-color {
  color: #1677ff;
}

.flex-wrap {
  display: flex;
  width: 100%;
  .img-list {
    width: 700px;
    overflow: hidden;
    .img-item {
      display: flex;
      width: 100%;
      overflow: hidden;
      color: #1677ff;
      .list-wrap {
        cursor: pointer;
        .list-icon {
          display: inline-block;
          width: 26px;
        }
        .list-ellipsis-text {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .upload-pannel-wrapper {
    flex: 1;
    .upload-pannel-title {
      margin-bottom: 10px;
      margin-left: 8px;
      color: #888;
      font-size: 14px;
    }
    .ant-upload-wrapper {
      display: flex;
      .ant-upload-drag {
        flex: 1;
        margin-left: 5px;
      }
      .ant-upload-list {
        // flex: 1;
        width: 400px;
        margin-left: 10px;
      }
    }
  }
}
.table-row-wrap {
  .ant-form-item {
    margin-bottom: 12px;
  }
}
.mt-14 {
  margin-top: 14px;
}
.red-text {
  color: red;
}
.ant-form-item-label {
  label {
    color: #666;
  }
}
.img-preview {
  height: 0;
  overflow: hidden;
}

//
.ant-input-affix-wrapper-readonly {
  padding: 0;
}

.upload-btn-submit {
  margin-top: 10px;
  margin-left: 10px;
}

.order-option-wrapper {
  .order {
    display: flex;
    .order-no {
      flex: 1;
    }
    .order-status {
      width: 80px;
      color: red;
      text-align: right;
    }
  }
  .car-info {
    color: #666;
  }
  .other-info {
    color: #666;
  }
}
