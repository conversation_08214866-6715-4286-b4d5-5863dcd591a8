import HeaderTab from '@/components/HeaderTab/index';
import NumInputInterest from '@/components/InputCom/NumInputInterest';
import globalStyle from '@/global.less';
import { downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import { history } from 'umi';
import type { CarAssetsListItem } from '../data';
import { carAssetsExport, getCarAssetsList } from '../service';
import './index.less';

const VehicleAssetManagementCom: React.FC = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [exportLoading, setExportLoading] = useState(false);

  const columns: ProColumns<CarAssetsListItem>[] = [
    {
      title: '车架号',
      dataIndex: 'vin',
      search: { transform: (value: string) => value?.trim() },
    },
    {
      title: '发动机号',
      dataIndex: 'engineCode',
      search: { transform: (value: string) => value?.trim() },
    },
    {
      title: '距离放款时间',
      dataIndex: 'distanceLendingDays',
      renderFormItem: (_: any, { type, defaultRender, ...rest }: any) => {
        return (
          <NumInputInterest
            minValueReplace="lendingTimeStartDays"
            maxValueReplace="lendingTimeEndDays"
            precision={0}
            addonAfter="天"
            max={9999}
            {...rest}
            className={globalStyle.w100}
          />
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        '': '全部',
        '0': '待完善',
        '1': '已完善',
      },
    },
    {
      title: '放款主体',
      dataIndex: 'funderCode',
      valueEnum: {
        YI_REN_XING: '广州易人行融资租赁有限公司',
        SHANG_HAI_YING_HANG: '上海银行',
      },
    },
    {
      title: '放款时间',
      dataIndex: 'lendingTime',
      search: false,
    },
    {
      title: '抵押人姓名',
      dataIndex: 'mortgagorName',
      search: false,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '车牌号',
      dataIndex: 'licenseCode',
    },
    {
      title: '最近编辑时间',
      dataIndex: 'updatedAt',
      search: false,
    },
    {
      title: '编辑人',
      dataIndex: 'updatedBy',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 160,
      render: (_, record) => (
        <>
          <Button
            type="link"
            onClick={() => {
              history.push(
                `/operation-manager/vehicle-basic-edit?orderNo=${record.orderNo}&type=edit`,
              );
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => {
              history.push(`/operation-manager/vehicle-basic-info?orderNo=${record.orderNo}`);
            }}
          >
            详情
          </Button>
        </>
      ),
    },
  ];
  console.log('ChannelAccount render!');

  const transformDistanceLendingDays = (params: any) => {
    const req: any = {
      ...params,
      lendingTimeStartDays: params?.distanceLendingDays?.lendingTimeStartDays,
      lendingTimeEndDays: params?.distanceLendingDays?.lendingTimeEndDays,
    };
    delete req?.distanceLendingDays;
    return req;
  };

  return (
    <>
      {/* <HeaderTab /> */}
      <PageContainer className={globalStyle.mt16}>
        <ProTable<CarAssetsListItem>
          actionRef={actionRef}
          formRef={formRef}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          request={(params) => {
            const req = transformDistanceLendingDays(params);
            return getCarAssetsList(req);
          }}
          columns={columns}
          search={{
            labelWidth: 100,
          }}
          dateFormatter="string"
          pagination={{
            showSizeChanger: true,
          }}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                loading={exportLoading}
                onClick={() => {
                  setExportLoading(true);
                  const data = formRef?.current?.getFieldsValue();
                  const newForm = transformDistanceLendingDays(data);
                  carAssetsExport(newForm)
                    .then((res) => {
                      downLoadExcel(res);
                      setExportLoading(false);
                      message.success('导出成功!');
                    })
                    .catch((e) => {
                      console.log(666, e);
                      setExportLoading(false);
                      message.error('导出失败!');
                    });
                }}
              >
                导出
              </Button>,
            ];
          }}
        />
      </PageContainer>
    </>
  );
};

// export default VehicleAssetManagementCom;
export default () => (
  <>
    <HeaderTab />
    <KeepAlive name="operation-manager/vehicle-asset-management">
      <VehicleAssetManagementCom />
    </KeepAlive>
  </>
);
