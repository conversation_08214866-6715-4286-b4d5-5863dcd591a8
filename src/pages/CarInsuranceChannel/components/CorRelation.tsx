/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-08-02 15:39:29
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2024-10-16 14:06:12
 * @FilePath: /lala-finance-biz-web/src/pages/CarInsuranceChannel/components/CorRelation.tsx
 * @Description: CorRelation，企业进件配置，和个人进件配置至少二选一填写
 */
import WalletActions from '@/components/WalletAccountInfo/WalletActions';
import {
  ClaimRechargeModal,
  ClaimRecordModal,
  TransactionDetailModal,
} from '@/pages/CarInsuranceCustomer/components';
import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ProFormSelect } from '@ant-design/pro-form';
import { useAccess, useModel } from '@umijs/max';
import { Checkbox, Divider, Form, Select, Space } from 'antd';
import type { MutableRefObject } from 'react';
import React, { memo, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

import '../index.less';
import type { IcompanyItem, TgetRelationList } from '../services';
import { getRelationList } from '../services';
import { getPersonalOrEnterpriseHasVal } from '../util';
import AddCompensationModal from './AddCompensationModal';
import CarInsuranceFeeModal from './CarInsuranceFeeModal';

type Props = {
  playground: 'edit' | 'add';
  corRelationRef: any;
  formRef: MutableRefObject<ProFormInstance | undefined>;
  parentChannelInfo?: Record<string, string>;
  currentChannelCode?: string;
  isSecondList: boolean;
  personalRelatedPayAccount?: string[];
  extMappingAccountBO?: {
    externalOwnerId: string | undefined; // 内部账号id
    accountId: string | undefined; // 帐号id
    externalAccountId: string | undefined; // 外部账号id
    bankNo: string | undefined; // 银行卡号
    bankName: string | undefined; // 银行卡名
    accountName: string | undefined; // 账户名
  }; // 专属账号信息
  channelName: string;
};

const dividerCommonStyle = { color: 'gray', fontSize: 14 };

const CorRelation: React.FC<Props> = ({
  playground = 'add',
  corRelationRef,
  formRef,
  currentChannelCode,
  parentChannelInfo,
  isSecondList,
  personalRelatedPayAccount,
  extMappingAccountBO,
  channelName,
}) => {
  // console.log(parentChannelInfo);
  const [relationOptions, setRelationOptions] = useState<TgetRelationList>({
    companyList: [] as IcompanyItem[],
  } as TgetRelationList);
  const [visibleFee, setVisibleFee] = useState(false);
  const { companyList = [], productList = [], enterpriseList = [] } = relationOptions;
  const [checkRules, setCheckRules] = useState({ personal: true, enterprise: true });
  const [, setUpdateSelect] = useState(0);
  const [selCompanyListKey, setSelCompanyListKey] = useState<string[] | undefined>(
    personalRelatedPayAccount || [],
  );
  const [hasConfigData, setHasConfigData] = useState<IcompanyItem[]>([]);
  // 代偿客户
  const [compensationCustomer, setCompensationCustomer] = useState<any>([]);
  // modal ref
  const claimRechargeRef = useRef<any>(null);
  const transactionRef = useRef<any>(null);
  const claimRecordRef = useRef<any>(null);
  const addCompensationRef = useRef<any>(null);

  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const isGrayUser = initialState?.currentUser?.isGrayUser;
  // console.log(hasConfigData, companyList, selCompanyListKey, personalRelatedPayAccount, '----需求');
  useEffect(() => {
    console.log('🚀 ~ useEffect ~ selCompanyListKey');
    if (selCompanyListKey?.length > 0) {
      const hasConfigDataT = companyList?.filter((item: IcompanyItem) =>
        selCompanyListKey?.includes(item?.id),
      );
      setHasConfigData(hasConfigDataT || []);
    }
  }, [companyList, selCompanyListKey]);

  useImperativeHandle(corRelationRef, () => ({
    //  必填规则校验，企业个人配置至少二选一
    onCheckRules: (values: any) => {
      // console.log(personal, enterprise);
      const { personal, enterprise } = getPersonalOrEnterpriseHasVal(values);
      if ((personal && enterprise) || (!personal && !enterprise))
        return setCheckRules({ personal: !personal, enterprise: !enterprise });
      return setCheckRules({ personal, enterprise });
    },
  }));

  const sortCompanyArr = (arr: IcompanyItem[], compareItem: string) => {
    arr.sort(function (a, b) {
      // 这里我们使用localeCompare进行比较，'zh-CN'是中文本地化
      return a?.[compareItem].localeCompare(b?.[compareItem], 'zh-CN');
    });
    return arr;
  };

  const init = useCallback(async () => {
    const data = await getRelationList(parentChannelInfo?.channelCode);
    setRelationOptions(data || []);
  }, [parentChannelInfo?.channelCode]);

  useEffect(() => {
    init();
  }, [init]);

  //获取确定后的值
  const handleOKFunc = useCallback(
    (val: string[]) => {
      setSelCompanyListKey(val);
      formRef.current?.setFieldValue('personalRelatedPayAccount', val);
      formRef?.current?.validateFields(['personalRelatedPayAccount']);
    },
    [formRef],
  );

  const actions = {
    showTransaction: () => {
      transactionRef.current.show();
    },
    showClaimRecharge: () => {
      claimRechargeRef.current.show();
    },
    showClaimRecord: () => {
      claimRecordRef.current.show();
    },
  };
  return (
    <>
      <div id="corRelation" style={{ marginTop: 35 }}>
        <Divider orientation="left" style={dividerCommonStyle}>
          企业进件配置
        </Divider>
        <Form.Item label="产品与企业" rules={[{ required: checkRules.enterprise }]}>
          <Form.List
            name="relatedProductAndEnterpriseFlat"
            rules={[
              {
                validator: async (_, values) => {
                  console.log(values, checkRules);
                  //不必填就不校验
                  if (!checkRules.enterprise) {
                    return Promise.resolve();
                  }
                  if (!values || values.length === 0) {
                    // console.log(values.length);
                    return Promise.reject(new Error('产品与企业整体必须有至少一条记录'));
                  }

                  for (let i = 0; i < values?.length; i++) {
                    const product = values[i]?.product;
                    const enterprise = values[i]?.enterprise;
                    // console.log(product, enterprise);
                    if (!product || product?.length === 0) {
                      console.log(product, enterprise, 'if');
                      return Promise.reject(new Error(`第${i + 1}条记录的产品不能为空`));
                    }
                    if (!enterprise || enterprise?.length === 0) {
                      console.log(product, enterprise, 'ifhhh');
                      return Promise.reject(new Error(`第${i + 1}条记录的企业不能为空`));
                    }
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                {fields.map((subField) => (
                  <div
                    key={subField.key}
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      marginBottom: 24,
                    }}
                  >
                    <Form.Item
                      noStyle
                      name={[subField.name, 'product']}
                      validateTrigger={['onChange', 'onBlur']}
                      // rules={[{ required: checkRules.enterprise }]}
                      // rules={[{ required: true, message: '请选择产品' }]}
                    >
                      <Select
                        mode="multiple"
                        options={productList?.map((item) => {
                          const { id, productName } = item;
                          //获取当前产品与企业字段选中产品的所有合集，并判断当前的id是否在其中。
                          const disabled = formRef?.current
                            ?.getFieldValue('relatedProductAndEnterpriseFlat')
                            .map((item1) => item1?.product)
                            .flat(Infinity)
                            .includes(id);
                          return {
                            value: id,
                            label: productName,
                            key: id,
                            disabled,
                          };
                        })}
                        style={{ width: 200 }}
                        filterOption={true}
                        showArrow={true}
                        allowClear
                        optionFilterProp="label"
                        placeholder="请输入关联产品"
                        onChange={() => {
                          corRelationRef.current?.onCheckRules(formRef.current?.getFieldsValue());
                          formRef?.current?.validateFields(['relatedProductAndEnterpriseFlat']);
                          // select禁用了选中的单个select没有删除按钮，
                          //用整个select框的删除，不会触发渲染，导致曾经选中但被清除的，无法继续备选。手动触发一次渲染
                          setUpdateSelect(Math.random);
                        }}
                        getPopupContainer={() => document.getElementById('corRelation')!}
                      />
                    </Form.Item>
                    <div className="split-line">—</div>
                    <Form.Item
                      noStyle
                      name={[subField.name, 'enterprise']}
                      validateTrigger={['onChange', 'onBlur']}
                      // rules={[{ required: checkRules.enterprise }]}
                      // validateTrigger={['onChange', 'onBlur']}
                      // rules={[{ required: true, message: '请选择企业' }]}
                    >
                      <Select
                        mode="multiple"
                        options={enterpriseList?.map((item) => {
                          const { id, certiName } = item;
                          return {
                            label: certiName,
                            value: id,
                          };
                        })}
                        onChange={() => {
                          formRef?.current?.validateFields(['relatedProductAndEnterpriseFlat']);
                          corRelationRef.current?.onCheckRules(formRef.current?.getFieldsValue());
                        }}
                        style={{ width: 250 }}
                        filterOption={true}
                        showArrow={true}
                        optionFilterProp="label"
                        getPopupContainer={() => document.getElementById('corRelation')!}
                        placeholder="请输入关联企业"
                      />
                    </Form.Item>
                    <Space>
                      <PlusOutlined className="dynamic-button add" onClick={() => add()} />
                      {fields.length > 1 ? (
                        <MinusOutlined
                          className="dynamic-button delete"
                          onClick={() => remove(subField.name)}
                        />
                      ) : null}
                    </Space>
                  </div>
                ))}
                {/* {JSON.stringify(errors)} */}
                <Form.ErrorList errors={errors} />
              </div>
            )}
          </Form.List>
        </Form.Item>

        {/* <ProFormSelect
          mode="multiple"
          rules={[{ required: checkRules.enterprise }]}
          options={productList?.map((item) => {
            const { id, productName } = item;
            return {
              value: id,
              label: productName,
            };
          })}
          name="relatedProduct"
          fieldProps={{
            filterOption: true,
            optionFilterProp: 'label',
            getPopupContainer: () => document.getElementById('corRelation')!,
          }}
          label="关联产品"
        /> */}
        {/* <ProFormSelect
          mode="multiple"
          rules={[{ required: checkRules.enterprise }]}
          options={enterpriseList?.map((item) => {
            const { id, certiName } = item;
            return {
              label: certiName,
              value: id,
            };
          })}
          fieldProps={{
            filterOption: true,
            optionFilterProp: 'label',
            getPopupContainer: () => document.getElementById('corRelation')!,
          }}
          name="relatedEnterprise"
          label="关联企业"
        /> */}
        {/* <ProFormSelectAll
          onSelectAllChange={(checked) => {
            if (checked) {
              formRef.current?.setFieldValue(
                'relatedPayAccount',
                companyList.map((item) => item.id),
              );
            } else {
              formRef.current?.setFieldValue('relatedPayAccount', []);
            }
            corRelationRef.current?.onCheckRules(formRef.current?.getFieldsValue());
          }}
          rules={[{ required: checkRules.enterprise }]}
          options={companyList?.map((item) => {
            const { id, companyName, paymentAccountNo = '' } = item;
            const paymentAccountNo1 = paymentAccountNo?.substring(paymentAccountNo?.length - 4);
            return {
              value: id,
              label: (
                <div>
                  {companyName}(<span style={{ color: 'red' }}>{paymentAccountNo1}</span>)
                </div>
              ),
              optionFilte: `${companyName}-${paymentAccountNo1}`,
            };
          })}
          name="relatedPayAccount"
          label="关联保费收款公司"
          fieldProps={{
            filterOption: true,
            optionFilterProp: 'optionFilte',
            getPopupContainer: () => document.getElementById('corRelation')!,
          }}
        /> */}
      </div>
      <div id="corPersonalRelation" style={{ marginTop: 35 }}>
        <Divider orientation="left" style={dividerCommonStyle}>
          个人进件配置
        </Divider>
        <ProFormSelect
          mode="multiple"
          rules={[{ required: checkRules.personal }]}
          options={productList?.map((item) => {
            const { id, productName } = item;
            return {
              value: id,
              label: productName,
            };
          })}
          name="personalRelatedProduct"
          fieldProps={{
            filterOption: true,
            optionFilterProp: 'label',
            getPopupContainer: () => document.getElementById('corPersonalRelation')!,
            onChange: () => {
              corRelationRef.current?.onCheckRules(formRef.current?.getFieldsValue());
            },
          }}
          label="关联产品"
        />

        {/* <ProFormSelectAll
          onSelectAllChange={(checked) => {
            if (checked) {
              formRef.current?.setFieldValue(
                'personalRelatedPayAccount',
                companyList.map((item) => item.id),
              );
            } else {
              formRef.current?.setFieldValue('personalRelatedPayAccount', []);
            }
            corRelationRef.current?.onCheckRules(formRef.current?.getFieldsValue());
          }}
          mode="multiple"
          rules={[{ required: checkRules.personal }]}
          options={companyList?.map((item) => {
            const { id, companyName, paymentAccountNo = '' } = item;
            const paymentAccountNo1 = paymentAccountNo?.substring(paymentAccountNo?.length - 4);
            return {
              value: id,
              label: (
                <div>
                  {companyName}(<span style={{ color: 'red' }}>{paymentAccountNo1}</span>)
                </div>
              ),
              optionFilte: `${companyName}-${paymentAccountNo1}`,
            };
          })}
          fieldProps={{
            filterOption: true,
            optionFilterProp: 'optionFilte',
            getPopupContainer: () => document.getElementById('corPersonalRelation')!,
          }}
          name="personalRelatedPayAccount"
          label="关联保费收款公司"
        /> */}
      </div>
      <div>
        <Divider orientation="left" style={dividerCommonStyle}>
          保费收款公司
        </Divider>
        <Form.Item
          label="保费收款公司"
          required
          name="personalRelatedPayAccount"
          rules={[
            {
              validator: async () => {
                if (!selCompanyListKey?.length) {
                  // console.log(values.length);
                  return Promise.reject(new Error('请选择保费收款公司'));
                } else {
                  return Promise.resolve();
                }
              },
            },
          ]}
        >
          <div className="fee-company">
            <span className="num">可用:{selCompanyListKey?.length || 0} </span>
            <span
              onClick={() => {
                setVisibleFee(true);
              }}
            >
              点击此处配置
            </span>
          </div>
          {/* <Form.ErrorList errors={selCompanyListKey?.length ? undefined : ['请选择保费收款公司']} /> */}
        </Form.Item>
        <div />
      </div>
      {playground === 'edit' && isGrayUser && (
        <div>
          <Divider orientation="left" style={dividerCommonStyle}>
            汇款余额代偿关系
          </Divider>
          <Form.Item label="代偿客户" name="compensationCustomer">
            <div className="compensation-customers">
              <span className="c_num_txt">
                审核通过：
                {compensationCustomer?.filter((item) => item.authStatus === 'bind')?.length ||
                  0}{' '}
              </span>
              <span className="c_num_txt c_end">
                审核中：
                {compensationCustomer?.filter((item) => item.authStatus === 'submit')?.length ||
                  0}{' '}
              </span>
              {access.hasAccess('createReimbursement_car_insurance_channel_list') ? (
                <span
                  className="c_end"
                  onClick={() => {
                    addCompensationRef.current?.show();
                  }}
                >
                  点击此处配置
                </span>
              ) : (
                <span className="c_end" /> // 占位元素
              )}
            </div>
          </Form.Item>
        </div>
      )}
      {/* 一级渠道进来，新建，需要勾选二级渠道的权限 */}
      {(isSecondList || !access.hasAccess('all_btn_car_insurance_channel_list')) && (
        <div>
          <Divider orientation="left" style={dividerCommonStyle}>
            权限设置
          </Divider>
          <Form.Item name="roleCodeList" required label="功能模块">
            <Checkbox.Group>
              {/* <Checkbox value="companyInsuranceChannelUser" style={{ lineHeight: '32px' }}>
                二级渠道-保费收款公司
              </Checkbox> */}
              <Checkbox value="orderInsuranceChannelUser" style={{ lineHeight: '32px' }}>
                二级渠道-订单管理
              </Checkbox>
              <Checkbox value="verifyInsuranceChannelUser" style={{ lineHeight: '32px' }}>
                二级渠道-企业管理
              </Checkbox>
            </Checkbox.Group>
          </Form.Item>
        </div>
      )}
      {/* 专属帐号信息 */}
      {playground === 'edit' && isGrayUser && (
        <div className="account-info">
          <Divider orientation="left" style={dividerCommonStyle}>
            专属充值帐号
          </Divider>
          <Form.Item name="accountName" label="账户名">
            {extMappingAccountBO?.accountName || '-'}
          </Form.Item>
          <Form.Item name="accountNo" label="帐号">
            {extMappingAccountBO?.bankNo || '-'}
          </Form.Item>
          <Form.Item name="bankName" label="银行名">
            {extMappingAccountBO?.bankName || '-'}
          </Form.Item>
          <Form.Item name="currentBalance" label="当前钱包余额">
            <WalletActions
              externalOwnerId={extMappingAccountBO?.externalOwnerId}
              actions={actions}
              secondProductCode="0303"
            />
          </Form.Item>
        </div>
      )}
      {/* 层级太深，判断visibleFee 防止重复渲染 */}
      {visibleFee && (
        <CarInsuranceFeeModal
          visible={visibleFee}
          setVisible={(val) => setVisibleFee(val)}
          handleOk={handleOKFunc}
          notConfigData={sortCompanyArr(companyList, 'companyName')}
          hasConfigData={sortCompanyArr(hasConfigData, 'companyName')}
        />
      )}
      {playground === 'edit' && isGrayUser && (
        <>
          {/* 充值 */}
          <ClaimRechargeModal
            targetRemitAccountName={channelName}
            accountName={extMappingAccountBO?.accountName as string}
            externalOwnerId={extMappingAccountBO?.externalOwnerId}
            claimRechargeRef={claimRechargeRef}
            productSecondCode="0303"
          />
          {/* 认领记录 */}
          <ClaimRecordModal
            externalOwnerId={extMappingAccountBO?.externalOwnerId}
            claimRecordRef={claimRecordRef}
          />
          {/* 流水明细 */}
          <TransactionDetailModal
            externalOwnerId={extMappingAccountBO?.externalOwnerId}
            transactionRef={transactionRef}
            secondProductCode="0303"
          />
          <AddCompensationModal
            addCompensationRef={addCompensationRef}
            channelCode={currentChannelCode}
            getDataSource={(values) => {
              setCompensationCustomer(values);
            }}
          />
        </>
      )}
    </>
  );
};
export default memo(CorRelation);
