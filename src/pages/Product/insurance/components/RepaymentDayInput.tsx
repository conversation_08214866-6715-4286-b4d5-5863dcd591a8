import { Input } from 'antd';
import React, { useRef, useState } from 'react';

type Props = {
  value?: string[];
  onChange?: (value: string[]) => void;
};
const RepaymentDayInput: React.FC<Props> = (props) => {
  const { value, onChange } = props;
  const [start, end] = !Array.isArray(value) ? [] : value;
  const [, setFLush] = useState({});
  const valueRef = useRef<{ value: string[] }>({ value: [] });
  console.log('valuevaluevalue', value);
  return (
    <div>
      <div style={{ marginBottom: 10, color: '#8c8c8c' }}>
        每月1-15日申请,次月
        <Input
          width={50}
          style={{ width: 100 }}
          value={start}
          onChange={(e) => {
            console.log('12121', e.target.value);
            valueRef.current.value[0] = e.target.value;
            onChange?.(valueRef.current.value);
            setFLush({});
          }}
        />{' '}
        日还款
      </div>
      <div style={{ color: '#8c8c8c' }}>
        每月16-31日申请,次月
        <Input
          style={{ width: 100 }}
          value={end}
          onChange={(e) => {
            valueRef.current.value[1] = e.target.value;
            onChange?.(valueRef.current.value);
            setFLush({});
          }}
        />{' '}
        日还款
      </div>
    </div>
  );
};
export default RepaymentDayInput;
