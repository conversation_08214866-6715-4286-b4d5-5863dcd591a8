// import { CloudSyncOutlined } from '@ant-design/icons';
import { Card, Input, Popover, Switch, Timeline } from 'antd';
import React from 'react';
import type { CardItem } from '../data';
import FeishuAt from '@hll/feishu-at';
import './CardList.less';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';

interface CardListProps {
  list: CardItem[];
  updateGrayHeaderInfoByIndex: (userList: Partial<CardItem>, index: number) => Promise<void>;
}

const CardList: React.FC<CardListProps> = (props: CardListProps) => {
  const { list, updateGrayHeaderInfoByIndex } = props;
  // const [formData, setFormData] = React.useState(list);
  const onKeyInputChange = (
    item: CardItem,
    index: number,
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    updateGrayHeaderInfoByIndex({ ...item, grayHeaderKey: e.target.value }, index);
  };
  const onValInputChange = (
    item: CardItem,
    index: number,
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    console.log('onValInputChange', item, index, e.target.value);
    updateGrayHeaderInfoByIndex({ ...item, grayHeaderValue: e.target.value }, index);
  };
  const onSwitchChange = (item: CardItem, index: number, valid: boolean) => {
    console.log('onSwitchChange', item, index, valid);
    updateGrayHeaderInfoByIndex({ ...item, valid: valid }, index);
  };

  return (
    <>
      <div className="card-list-wrapper">
        <Timeline mode="left">
          {list?.map((item, index) => (
            <Timeline.Item
              key={item.canaryId}
              dot={
                <span className={item.valid ? 'van-task-priority' : 'van-task-priority gray'}>
                  #{index + 1}
                </span>
              }
            >
              {/* <Badge.Ribbon text={`灰度人数：${item.userList.length}`}> */}
              <Card
                className={item.valid ? '' : 'card-item-grayscale'}
                title={
                  <div>
                    <div className="task-id">
                      <div className="left">
                        <span className="van-task-id">#{item.vanTaskId}</span>
                        <span className="description">{item.description}</span>
                      </div>
                      <div className="right">
                        <Popover
                          placement="topLeft"
                          content={`关闭时，前、后端灰度都会关闭, 灰度名单用户也不再使用其他sso灰度策略，将访问default版本`}
                          trigger="hover"
                        >
                          <Switch
                            checkedChildren={<CheckOutlined />}
                            unCheckedChildren={<CloseOutlined />}
                            checked={item?.valid}
                            onChange={() => onSwitchChange(item, index, !item?.valid)}
                          />
                        </Popover>
                      </div>
                    </div>
                    <div className="gray-header">
                      <Input.Group compact className="header-group">
                        <span className="header-title">API灰度请求头</span>
                        <Popover
                          placement="topLeft"
                          content="指定API请求头, 如: x-hll-gray-version"
                          trigger="hover"
                        >
                          <Input
                            value={item?.grayHeaderKey}
                            onChange={(e) => onKeyInputChange(item, index, e)}
                            size="small"
                            className="header-key"
                            placeholder="请输入key"
                          />
                        </Popover>
                        <span className="header-separator">: </span>
                        <Popover
                          placement="topLeft"
                          content="指定API请求头的值, 如: v2333"
                          trigger="hover"
                        >
                          <Input
                            value={item?.grayHeaderValue}
                            onChange={(e) => onValInputChange(item, index, e)}
                            size="small"
                            className="header-value"
                            placeholder="请输入value"
                          />
                        </Popover>
                      </Input.Group>
                    </div>
                  </div>
                }
                type="inner"
              >
                <div>
                  <span className="gray-title">灰度名单({item.userList.length}人)：</span>
                  {item.userList.map((userId) => (
                    <>
                      <FeishuAt key={userId} userId={userId} />
                    </>
                  ))}
                </div>
              </Card>
              {/* </Badge.Ribbon> */}
            </Timeline.Item>
          ))}
        </Timeline>
      </div>
    </>
  );
};

export default CardList;
