/*
 * @Author: your name
 * @Date: 2021-04-13 11:18:55
 * @LastEditTime: 2021-04-25 15:55:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/ModelLibrary/index.tsx
 */
import { PageContainer } from '@ant-design/pro-layout';
import React from 'react';

import { Tabs } from 'antd';

import HeaderTab from '@/components/HeaderTab';

import { CarPromotionTable } from './components';
// import { downLoadExcel } from '../../utils/utils';

const Promotion: React.FC<any> = () => {
  const { TabPane } = Tabs;
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Tabs defaultActiveKey="1" style={{ background: '#fff', padding: '0 10px' }}>
          <TabPane tab="新车" key="1">
            <CarPromotionTable type={1} />
          </TabPane>
          <TabPane tab="二手车" key="2">
            <CarPromotionTable type={2} />
          </TabPane>
        </Tabs>
      </PageContainer>
    </>
  );
};

export default Promotion;
