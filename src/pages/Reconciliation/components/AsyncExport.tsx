import { exportRecord } from '@/components/AsyncExport/services';
import type { IexportRecord, ItaskCode } from '@/components/AsyncExport/types';
import {
  canDownloadStatus,
  exportStatusValueZnMap,
  taskCodeValueZnMap,
} from '@/components/AsyncExport/types';
import type { ActionType } from '@ant-design/pro-components';
import { ModalForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Radio, Tabs, Tag } from 'antd';
import dayjs from 'dayjs';
import qs from 'qs';
import React, { memo, useEffect, useRef, useState } from 'react';

/**
 * 异步导出组件 -- 提交导出exportAsync, 在导出记录中下载
 * 全局已经封装了一个通用的 AsyncExport 但是调胀 有些特殊 所以拆出来 // 调帐多个参数 handleIsAllowOpen
 * 统一写成函数获取数据 不在外面维护状态
 */
type Props = {
  getSearchDataTotal: () => Promise<number>; // 获取筛选条件的数据总量的函数
  getSearchParams: () => any; // 获取筛选的参数

  getSelectedTotal?: () => number;
  getSelectedParams?: () => any; // 勾选的时候 需要提交的参数

  taskCode: ItaskCode[]; // 允许导出的范围
  exportAsync: (searchParams: any) => Promise<any>; // 提交导出的接口

  handleIsAllowOpen: () => Promise<boolean>; // 特殊的 是否允许开发弹窗 用于一些校验 // true 允许 false 不允许
};
const AsyncExport: React.FC<Props> = (props) => {
  const {
    getSearchDataTotal,
    getSelectedTotal,
    getSearchParams,
    taskCode,
    getSelectedParams,
    exportAsync,
    handleIsAllowOpen,
  } = props;
  const paramsRef = useRef<{ searchParams?: any; selectedParams?: any }>({});

  const [searchDataTotal, setSearchDataTotal] = useState(0);
  const [selectedDataTotal, setSelectedDataTotal] = useState(0);
  const [exportRange, setExportRange] = useState('search');
  const recordActionRef = useRef<ActionType>();
  const [activeKey, setActiveKey] = useState('range');

  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (selectedDataTotal > 0) {
      // 有勾选的数据
      setExportRange('selected');
    }
  }, [selectedDataTotal]);

  return (
    <div>
      <Button
        type="primary"
        onClick={async () => {
          const isAllowOpen = await handleIsAllowOpen();
          if (!isAllowOpen) {
            console.log('setOpen', isAllowOpen);
            setOpen(false);
          } else {
            setOpen(true);
          }
        }}
      >
        导出
      </Button>
      <ModalForm
        title="导出"
        width={1000}
        onOpenChange={(val) => {
          if (val) {
            // 获取搜索参数
            const searchParams = getSearchParams();

            // 获取选中参数
            const selectedParams = getSelectedParams?.();
            paramsRef.current.searchParams = searchParams;
            paramsRef.current.selectedParams = selectedParams;

            // 获取搜索数据总量
            getSearchDataTotal().then((num) => setSearchDataTotal(num));

            // 获取勾选数据总量
            const total = getSelectedTotal?.();
            setSelectedDataTotal(total || 0);
          }
        }}
        open={open}
        modalProps={{
          onCancel: () => setOpen(false),
          open,
        }}
        onFinish={async () => {
          if (activeKey === 'record') {
            setOpen(false);
            // 如果是导出记录 点击确定 则不做任何处理
            return true;
          }
          const { searchParams, selectedParams } = paramsRef.current;

          if (exportRange === 'selected') {
            if (!selectedDataTotal) {
              message.error('您还未选中数据');
              return false;
            } else {
              await exportAsync(selectedParams);
            }
          } else {
            if (searchDataTotal === 0) {
              message.error('数据总量为0');
              return false;
            }
            await exportAsync(searchParams);
          }
          message.success('导出中，稍后可在导出记录中下载导出文件');
          setActiveKey('record');
          setTimeout(() => {
            recordActionRef?.current?.reload();
          }, 2000);
          return false;
        }}
      >
        <Tabs
          onChange={(activeKey) => {
            setActiveKey(activeKey);
            if (activeKey === 'record') {
              recordActionRef.current?.reload();
            }
          }}
          activeKey={activeKey}
          items={[
            {
              label: '导出范围',
              key: 'range',
              children: (
                <Radio.Group
                  onChange={(e) => {
                    setExportRange(e.target.value);
                  }}
                  value={exportRange}
                >
                  <Radio disabled={selectedDataTotal <= 0} value={'selected'}>
                    导出已选数据,共计{selectedDataTotal}条记录
                  </Radio>
                  <Radio value={'search'}>
                    导出筛选条件下的全量数据,共计{searchDataTotal}条记录,实际导出条数以导出文件为准
                  </Radio>
                </Radio.Group>
              ),
            },
            {
              label: '导出记录',
              key: 'record',
              children: (
                <ProTable<IexportRecord>
                  search={false}
                  actionRef={recordActionRef}
                  pagination={{
                    defaultPageSize: 10,
                  }}
                  request={async (params) => {
                    const { current = 1, pageSize = 10 } = params;
                    return exportRecord({ current, pageSize, taskCode });
                  }}
                  columns={[
                    { dataIndex: 'totalCount', title: '数据量' },
                    {
                      dataIndex: 'taskCode',
                      title: '导出内容',
                      valueType: 'select',
                      valueEnum: taskCodeValueZnMap,
                    },
                    { dataIndex: 'executeDatetime', title: '开始时间' },
                    { dataIndex: 'finishDatetime', title: '结束时间' },
                    { dataIndex: 'exporterName', title: '操作人' },
                    {
                      dataIndex: 'status',
                      title: '状态',
                      valueType: 'select',
                      valueEnum: exportStatusValueZnMap,
                    },
                    {
                      dataIndex: 'status',
                      title: '操作',
                      render(_, record) {
                        try {
                          const { ossPath } = record;
                          let Expires = dayjs().valueOf() / 1000 + '10';
                          if (ossPath) {
                            const url = new URL(record.ossPath);
                            const query = qs.parse(url.search.slice(1));
                            Expires = query.Expires + '';
                          }
                          return (
                            <Button
                              download
                              href={record.ossPath}
                              type="link"
                              disabled={
                                !canDownloadStatus.includes(record.status) ||
                                dayjs().valueOf() / 1000 > Number(Expires)
                              }
                            >
                              下载
                            </Button>
                          );
                        } catch (error) {
                          return <Tag color="error">ossPath有误</Tag>;
                        }
                      },
                    },
                  ]}
                />
              ),
            },
          ]}
        />
      </ModalForm>
    </div>
  );
};

export default memo(AsyncExport);
