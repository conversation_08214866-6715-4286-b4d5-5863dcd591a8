/*
 * @Date: 2024-09-29 16:58:03
 * @Author: elisa.z<PERSON>
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-01-14 16:12:34
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/carInsurance/columns/CarTotalBillListColumns.tsx
 * @Description: 车辆总账 colums
 */
import { getChannelInfo } from '@/pages/CarInsurance/services';
import { LEVEL } from '@/pages/CarInsurance/type'; // @ts-ignore
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Button, Cascader, message, Modal } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import type { IcustomerName } from '../components/AccountNameFormItem';
import AccountNameFormItem from '../components/AccountNameFormItem';
import { insuranceExit } from '../services';
import type { IbillListItem } from '../types';
import { channelTypeMap, statusMap, userTypeEnCodeMap } from '../types';

/**
 * 车险-车辆总账
 */
// 1-31
const validDayRangeOptions = new Array(30).fill(1).map((_, index) => {
  return {
    label: index + 1,
    value: index + 1,
    // 子项比自己大1
    children: new Array(30 - index).fill(1).map((_, subIndex) => {
      return {
        label: index + subIndex + 2,
        value: index + subIndex + 2,
      };
    }),
  };
});
export function getCarTotalBillListColumn({
  access,
  channelCode,
  channelLevel,
  actionRef,
}: {
  access: any;
  channelCode: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  channelLevel: number;
}) {
  console.log(actionRef);

  const delConfirm = ({ expectCancelStatus, orderNo, subjectMatterNo }: IbillListItem) => {
    const content = expectCancelStatus
      ? '确认车辆不退保吗？确认后将在邮件附件中催收车辆账单金额。'
      : '确认车辆正在办理退保流程，不再发送该车辆账单给客户吗？';
    Modal.confirm({
      maskClosable: true,
      centered: true,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        insuranceExit({
          expectCancel: !expectCancelStatus,
          orderNo,
          subjectUniqueNo: subjectMatterNo,
        }).then(() => {
          console.log(actionRef);
          actionRef.current?.reload();
          message?.success('操作成功！');
          Modal.destroyAll();
        });
      },
      content,
    });
  };

  const CarTotalBillListColumn: ProColumns<IbillListItem>[] = [
    {
      title: '车辆账单ID',
      dataIndex: 'billNo',
      // formItemProps: {
      //   labelCol: { span: 8 },
      // },
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '车牌号',
      dataIndex: 'plateNo',
    },
    {
      title: '车架号',
      dataIndex: 'subjectMatterNo',
    },
    {
      title: '保司',
      dataIndex: 'insuranceCompany',
      search: false,
    },

    {
      title: '客户名称',
      dataIndex: 'customerName',
      hideInTable: true,
      renderFormItem: () => {
        return <AccountNameFormItem />;
      },
      search: {
        transform: (value: IcustomerName) => {
          const { userName, userNo, userType } = value;
          const result: any = {
            customerName: undefined,
          };
          if (userType === userTypeEnCodeMap.COMPANY) {
            result.userNo = userNo;
          } else {
            result.userName = userName;
          }
          return result;
        },
      },
    },

    {
      title: '客户名称',
      dataIndex: 'accountName',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelCode',
      hideInTable: true,
      debounceTime: 60000,
      fieldProps: {
        showSearch: true,
        // 如果是车险渠道 二级 用户只能查看自己渠道的 ,
        disabled: channelLevel === LEVEL.SECOND_CHANNEL,
        popupMatchSelectWidth: false,
      },
      //二级渠道只有自己，才有初始值
      initialValue: channelLevel === LEVEL.SECOND_CHANNEL ? channelCode : undefined,
      request: async () => {
        const data = await getChannelInfo({ channelCode, channelLevel });
        return data.map((item) => {
          return {
            value: item.channelCode,
            label: item.channelName,
            title: item.channelName,
          };
        });
      },
    },

    {
      title: '账单状态',
      dataIndex: 'statusName',
      search: false,
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDateNumber',
      search: false,
    },
    {
      title: '账单状态',
      dataIndex: 'statusList',
      hideInTable: true,
      valueType: 'select',
      valueEnum: statusMap,
      fieldProps: {
        mode: 'multiple',
      },
    },

    {
      title: '未还总额(元)',
      dataIndex: 'totalAmountUnpaid',
      search: false,
    },
    {
      title: '未还本金(元)',
      dataIndex: 'totalPrincipalUnpaid',
      search: false,
    },
    {
      title: '未还利息(元)',
      dataIndex: 'totalInterestUnpaid',
      search: false,
    },
    {
      title: '未还罚息(元)',
      dataIndex: 'totalOverduePenaltyUnpaid',
      search: false,
    },
    {
      title: '已还总额(元)',
      dataIndex: 'totalAmountPaid',
      search: false,
    },
    {
      title: '已还本金(元)',
      dataIndex: 'totalPrincipalPaid',
      search: false,
    },
    {
      title: '已还利息(元)',
      dataIndex: 'totalInterestPaid',
      search: false,
    },
    {
      title: '已还罚息(元)',
      dataIndex: 'totalOverduePenaltyPaid',
      search: false,
    },

    {
      title: '应还总额(元)',
      dataIndex: 'totalAmountDue',
      search: false,
    },
    {
      title: '应还本金(元)',
      dataIndex: 'totalPrincipalDue',
      search: false,
    },
    {
      title: '应还利息(元)',
      dataIndex: 'totalInterestDue',
      search: false,
    },

    {
      title: '应还罚息(元)',
      dataIndex: 'totalOverduePenaltyDue',
      search: false,
    },

    {
      title: '减免总额(元)',
      dataIndex: 'totalAmountExemption',
      search: false,
    },

    {
      title: '减免本金(元)',
      dataIndex: 'totalPrincipalExemption',
      search: false,
    },
    {
      title: '减免利息(元)',
      dataIndex: 'totalInterestExemption',
      search: false,
    },
    {
      title: '减免罚息(元)',
      dataIndex: 'totalOverduePenaltyExemption',
      search: false,
    },
    {
      title: '未还违约金(元)',
      dataIndex: 'totalBreach',
      search: false,
    },
    {
      title: '未还滞纳金(元)',
      dataIndex: 'totalLate',
      search: false,
    },

    {
      title: '已还违约金(元)',
      dataIndex: 'totalBreachPaid',
      search: false,
    },
    {
      title: '已还滞纳金(元)',
      dataIndex: 'totalLatePaid',
      search: false,
    },
    {
      title: '应还违约金(元)',
      dataIndex: 'totalBreachDue',
      search: false,
    },

    {
      title: '应还滞纳金(元)',
      dataIndex: 'totalLateDue',
      search: false,
    },

    {
      title: '减免违约金(元)',
      dataIndex: 'totalBreachExemption',
      search: false,
    },
    {
      title: '减免滞纳金(元)',
      dataIndex: 'totalLateExemption',
      search: false,
    },
    {
      title: '退保盈余(元)',
      dataIndex: 'cancellationProfitPaid',
      search: false,
    },
    {
      title: '应结清日期',
      dataIndex: 'dueDate',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(2, 'month'), dayjs().add(1, 'month')],
      search: {
        transform: (value: any) => {
          return {
            dueDateStart: `${value[0]} 00:00:00`,
            dueDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.dueDate || '-';
      },
    },
    {
      title: '更新日期',
      dataIndex: 'updatedAt',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            updateAtStart: `${value[0]} 00:00:00`,
            updateAtEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.updatedAt || '-';
      },
    },
    {
      title: '实际结清日期',
      dataIndex: 'clearTime',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            clearTimeStart: `${value[0]} 00:00:00`,
            clearTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.clearTime || '-';
      },
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            lendingTimeStart: `${value[0]} 00:00:00`,
            lendingTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.lendingTime || '-';
      },
    },

    {
      title: '渠道类型',
      dataIndex: 'channelTypeName',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      search: false,
    },

    {
      title: '客户类型',
      dataIndex: 'customerTypeName',
      search: false,
    },
    {
      title: '客户类型',
      dataIndex: 'userType',
      valueType: 'select',
      fieldProps: {
        options: [
          { value: 1, label: '企业' },
          { value: 2, label: '个人' },
        ],
      },
      hideInTable: true,
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      hideInTable: true,
      valueType: 'select',
      valueEnum: channelTypeMap,
    },
    {
      title: '每月还款日',
      dataIndex: '',
      renderFormItem: () => {
        return (
          <Cascader
            options={validDayRangeOptions}
            placeholder="请选择"
            displayRender={(label, selectedOptions) => {
              return <>{selectedOptions?.map((item) => item?.value)?.join('日 - ')}日</>;
            }}
          />
        );
      },
      search: {
        transform: (value: any) => {
          console.log('search value', value);
          return {
            repayDayStart: value[0],
            repayDayEnd: value[1],
          };
        },
      },
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record: IbillListItem) => {
        const { expectCancelStatus } = record;
        const text = expectCancelStatus ? '取消退保' : '预备退保';
        return (
          access.hasAccess('bill_cancel_car_insurance') && (
            <Button type="link" onClick={() => delConfirm(record)}>
              {text}
            </Button>
          )
        );
      },
    },
  ];
  return CarTotalBillListColumn;
}
