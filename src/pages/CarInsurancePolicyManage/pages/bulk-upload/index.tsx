/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-12-03 10:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/module/bulk-upload/index.tsx
 * @Description: index.tsx
 */

import HeaderTab from '@/components/HeaderTab';
import DraggerOcrUploadForm from '@/pages/CarInsurancePolicyManage/components/DraggerOCRUploadForm';
import OCRContainer from '@/pages/CarInsurancePolicyManage/components/OCRContainer/OCRContainer';
import {
  BulkUploadSteps,
  useCarInsuranceBulkUploadContext,
} from '@/pages/CarInsurancePolicyManage/context/CarInsuranceBulkUploadContext';
import eventBus, {
  InsuranceEventType,
} from '@/pages/CarInsurancePolicyManage/utils/recordInsuranceEvent';
import { ProForm } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { <PERSON><PERSON>, Card, Form, message, Spin } from 'antd';
import { debounce } from 'lodash';
import React, { useEffect, useRef } from 'react';
import { policyInfoTemplate } from '../../enum';
import { getCarInsurancePolicyOcrInfo } from '../../services';
import { transformFile2OcrStruct } from '../../utils';
const BulkUploadPage = () => {
  const ocrContainerRef = useRef<any>(null);
  const { loading, updateLoading, step, state, dispatch } = useCarInsuranceBulkUploadContext();
  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();

  // 下一步ocr识别
  const bulkUploadOcr = async (files: any) => {
    if (files.length === 0) {
      return;
    }

    // 如果所有文件都经过ocr识别了，则直接跳过ocr，直接下一步
    if (files.every((item: any) => !!item.ocrInfo)) {
      eventBus.emit(InsuranceEventType.NEXT_STEP);
      return;
    }
    // 更新loading状态
    updateLoading(true);

    // 筛选出未经过ocr识别的文件
    const filesNeedOcr = files.filter((item: any) => !item.ocrInfo);
    const requestList: Promise<any>[] = [];
    // 存储当前ocr的文件uid，作为匹配后续ocr结果的依据
    const uidList = filesNeedOcr.map((item: any) => item.uid);
    for (const file of filesNeedOcr) {
      requestList.push(
        getCarInsurancePolicyOcrInfo({
          shortFilePath: [file.response.data.filePath],
        }),
      );
    }
    // 需要保留所有调用结果，内部处理异常保单
    Promise.allSettled(requestList)
      .then((result) => {
        const formatFileResults = result.map((item: any, index: number) => {
          // 其他异常则直接返回error状态
          if (item.status === 'rejected' && item?.reason && !item?.reason?.ret) {
            return {
              ...(filesNeedOcr[index] || {}),
              uid: uidList[index],
              status: 'error',
            };
          }

          // ocr内部异常直接忽略，补充默认ocr信息，进到下一步给用户编辑提交
          if (
            item.status === 'rejected' ||
            item?.value?.success === false ||
            item?.reason?.ret === 0
          ) {
            return {
              ...(filesNeedOcr[index] || {}),
              uid: uidList[index],
              ocrInfo: transformFile2OcrStruct(policyInfoTemplate),
              status: 'done',
            };
          }
          return {
            ...(filesNeedOcr[index] || {}),
            uid: uidList[index],
            ocrInfo: transformFile2OcrStruct(item?.value?.data[0]),
            status: 'done',
          };
        });

        // 上面的结果只是针对文件上传成功的ocr列表，如果存在单个上传、或者后续上传等都会覆盖掉前面之前旧的，这里还需要针对性的匹配文件uid返回新的完整的列表
        const newFileList = state.policyInfoList.map((preFile: any) => {
          const NewFiles = formatFileResults.find((newFile: any) => newFile.uid === preFile.uid);
          return NewFiles || preFile;
        });

        // 将ocr识别结果添加到state中
        dispatch({
          type: 'add_policy',
          payload: {
            fileList: newFileList,
          },
        });
        // 如果存在ocr识别失败的文件，则提示请稍后检查重新上传
        if (formatFileResults.some((item: any) => item.status === 'error')) {
          messageApi.warning('部分保单ocr识别失败，请稍后检查重新上传识别');
          return;
        }
        // 全部通过则提交完后触发下一步
        eventBus.emit(InsuranceEventType.NEXT_STEP);
      })
      .finally(() => {
        updateLoading(false);
      });
  };

  // 下一步or上一步按钮点击事件
  const nextButtonClick = () => {
    switch (step) {
      case BulkUploadSteps.UPLOAD:
        // 点击下一步时校验是否存在文件
        form.validateFields().then((values) => {
          // 如果存在错误状态的文件，则提示请上传有效保单
          // 内部修改了文件状态，原有的表单字段无法正确获取，所以需要通过state来判断
          if (values?.carInsurancePolicyFiles?.some((item: any) => item.status === 'error')) {
            messageApi.error('部分保单存在异常，请重新检查后上传');
            return;
          }
          // 如果存在上传中的文件，则提示等待ocr识别完成
          if (values?.carInsurancePolicyFiles?.some((item: any) => item.status === 'uploading')) {
            messageApi.error('等待ocr识别完成');
            return;
          }
          bulkUploadOcr(state?.policyInfoList);
        });
        break;
      case BulkUploadSteps.OCR:
        eventBus.emit(InsuranceEventType.RETURN_LAST_STEP);
        break;
      default:
        break;
    }
  };
  // 上传失败提示-防抖
  const handleFileUploadError = debounce(() => {
    // 提示持续3秒
    message.error('上传失败', 3);
  }, 300);
  // 上传文件列表事件
  const uploadChange = async ({ fileList, file }: any) => {
    try {
      if (file?.status && file?.status === 'error') {
        handleFileUploadError();
      }
    } catch (err) {
      console.log(err);
    }
    // 将上传的文件列表添加到state中
    dispatch({
      type: 'add_policy',
      payload: {
        fileList: fileList,
      },
    });
  };

  // 重传跳转
  useEffect(() => {
    if (state.safePolicyInfoList.length === 0) {
      // 如果safePolicyInfoList为空，则返回上一步重新上传保单
      eventBus.emit(InsuranceEventType.RETURN_LAST_STEP);
    }
  }, [state.safePolicyInfoList]);

  return (
    <Spin spinning={loading}>
      <HeaderTab />
      <PageContainer
        title="车险保单-批量上传"
        fixedHeader={state.policyInfoList.length > 10}
        extra={[
          <Button
            key="close"
            className="bulk-custom-btn"
            htmlType="button"
            type="primary"
            onClick={() => {
              eventBus.emit(InsuranceEventType.CLOSE_UPLOAD);
            }}
          >
            关闭
          </Button>,
          <Button
            key="return"
            className="bulk-custom-btn"
            htmlType="button"
            type="primary"
            onClick={() => nextButtonClick()}
          >
            {step === BulkUploadSteps.UPLOAD ? '下一步' : '上一步'}
          </Button>,
          <Button
            key="commit"
            style={{ display: step === BulkUploadSteps.UPLOAD ? 'none' : 'block' }}
            type="primary"
            loading={loading}
            htmlType="submit"
            onClick={() => {
              ocrContainerRef.current?.hasPass(() => {
                eventBus.emit(InsuranceEventType.COMMIT_UPLOAD);
              });
            }}
          >
            提交审核
          </Button>,
        ]}
      >
        <Card>
          {/* 上传OCR */}
          <div
            className={`upload-container ${
              step === BulkUploadSteps.UPLOAD
                ? 'transition-container-show'
                : 'transition-container-hide'
            }`}
          >
            <ProForm
              form={form}
              submitter={{
                render: false,
              }}
            >
              <DraggerOcrUploadForm
                name="carInsurancePolicyFiles"
                isNeedLimit={true}
                limitCount={50}
                needOcr={true}
                needCustomRequest={false}
                max={50} // antd-pro ui截断，超过上限隐藏上传入口
                maxCount={50} // antd upload组件内部截断
                files={state.policyInfoList} // 上传的文件列表，受控展示
                onChange={uploadChange} // 上传文件列表改变事件
              />
            </ProForm>
            <div className="action-container">
              {state?.policyInfoList?.length > 0 && (
                <div className="upload-count">
                  已成功上传
                  {state.policyInfoList.filter((item: any) => item.status === 'done').length}
                  张保单， 上传中
                  {state.policyInfoList.filter((item: any) => item.status === 'uploading').length}
                  张保单， 识别异常
                  {state.policyInfoList.filter((item: any) => item.status === 'error').length}张保单
                </div>
              )}
            </div>
          </div>
          {/* OCR结果 */}
          <div
            className={`ocr-container ${
              step === BulkUploadSteps.OCR
                ? 'transition-container-show'
                : 'transition-container-hide'
            }`}
          >
            {state.policyInfoList.length > 0 ? (
              <OCRContainer ref={ocrContainerRef} />
            ) : (
              <div className="bulk-upload-empty">当前没有需要上传的保单</div>
            )}
          </div>
        </Card>
      </PageContainer>
      {contextHolder}
    </Spin>
  );
};

export default BulkUploadPage;
