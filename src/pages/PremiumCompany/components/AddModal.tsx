import { getChannelInfo } from '@/pages/CarInsurance/services';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import { Col, Form, message, Row } from 'antd';
import type { MutableRefObject } from 'react';
import React, { memo, useEffect, useRef, useState } from 'react';
import { useModel } from 'umi';
import province from '../province';
import type { IcarChannelItem, TlistItem } from '../services';
import { add, queryBank } from '../services';

type Props = {
  visible: boolean;
  setVisible: (val: boolean) => void;
  record: TlistItem;
  actionRef: MutableRefObject<ActionType | undefined>;
};

const AddModal: React.FC<Props> = (props) => {
  const { visible, setVisible, record, actionRef } = props;
  const ref = useRef<any>({ timeId: -1 });
  const [form] = Form.useForm();
  const [channelList, setChannelList] = useState<IcarChannelItem>([]);
  const { initialState = {} } = useModel<any>('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode, channelLevel } = currentUser;
  useEffect(() => {
    form.setFieldsValue(record);
  }, [form, record]);

  useEffect(() => {
    async function fetchData() {
      const data: IcarChannelItem = await getChannelInfo({ channelLevel, channelCode });
      setChannelList(data);
    }
    fetchData();
  }, []);
  return (
    <ModalForm<{
      companyName: string;
      paymentAccountNo: string;
      subBranchName: string;
      bankNo: string;
      bankName: string;
    }>
      title="保险收款公司"
      form={form}
      visible={visible}
      layout="horizontal"
      labelCol={{ span: 6 }}
      modalProps={{
        onCancel: () => {
          setVisible(false);
        },
        destroyOnClose: true,
      }}
      onFinish={async (values) => {
        const params: TlistItem = { ...values };
        if (record?.id) params.id = record.id;
        await add(params);
        actionRef.current?.reload();
        message.success('提交成功');
        setVisible(false);
        return true;
      }}
    >
      <ProFormText
        name="companyName"
        label="保费收款公司名称"
        rules={[
          { required: true },
          ({}) => ({
            validator(_, value) {
              if (/\s/g.test(value)) {
                return Promise.reject(new Error('不允许含有空格'));
              }
              return Promise.resolve();
            },
          }),
        ]}
      />
      <ProFormText
        name="paymentAccountNo"
        label="保费收款账号"
        rules={[
          { required: true },
          ({}) => ({
            validator(_, value) {
              if (/\s/g.test(value)) {
                return Promise.reject(new Error('不允许含有空格'));
              }
              return Promise.resolve();
            },
          }),
        ]}
      />
      <ProFormSelect
        fieldProps={{
          filterOption: false, // 远程搜索一定要加这个
          onSelect(value, options) {
            const { bankNo, bankName } = options;
            form.setFieldsValue({ bankNo, bankName });
          },
          onClear() {
            form.setFieldsValue({ bankNo: null });
          },
        }}
        request={async (values) => {
          const keyWords = values?.keyWords?.trim();
          if (!keyWords) return [];
          // 防抖 只执行最后一次
          const data = await new Promise((res, rej) => {
            if (ref.current.timeId) {
              clearInterval(ref.current.timeId);
            }
            ref.current.timeId = setTimeout(() => {
              queryBank(keyWords)
                .then((data1) => {
                  res(data1);
                })
                .catch((err) => {
                  rej(err);
                });
            }, 500);
          });
          const { data: list } = data as any;
          const options = list.map((item: any) => {
            const { subBranchName, bankNo, bankName } = item;
            return {
              label: subBranchName,
              value: subBranchName,
              bankNo,
              bankName,
            };
          });
          return options;
        }}
        rules={[{ required: true }]}
        showSearch
        name="subBranchName"
        label="保费收款账号开户行"
      />
      <ProFormText name="bankNo" label="银行号" rules={[{ required: true }]} disabled />
      <div style={{ display: 'none' }}>
        <ProFormText name="bankName" label="银行" rules={[{ required: true }]} disabled />
      </div>
      <ProFormSelect
        mode="multiple"
        rules={[{ required: true }]}
        options={channelList?.map((item) => {
          const { channelCode, channelName } = item;
          return {
            value: channelCode,
            label: channelName,
          };
        })}
        name="channelCodeList"
        fieldProps={{
          filterOption: true,
          optionFilterProp: 'label',
          // getPopupContainer: () => document.getElementById('corRelation')!,
        }}
        label="所属渠道"
      />
      <Row>
        <Col span={12} offset={3}>
          <ProFormText
            label="保司简称"
            name="insuranceCompanyShortName"
            fieldProps={{ maxLength: 10, showCount: true }}
          />
        </Col>
        <Col span={9}>
          <ProFormSelect
            showSearch
            label="地区"
            name="areaName"
            request={async () => {
              return province.map((v) => ({ label: v, value: v }));
              // const res = await getCityListEnum({ inLabel: false, level: 1 });
              // return (res || []).map((item) => ({
              //   label: item.label,
              //   value: item.label,
              // }));
            }}
          />
        </Col>
      </Row>
    </ModalForm>
  );
};
export default memo(AddModal);
