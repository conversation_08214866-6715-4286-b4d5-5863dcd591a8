import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { ICallAction } from '../Overdue/data';

// 获取外呼动作列表
export async function getCallerList(params: ICallAction) {
  return request(`/bizadmin/outbound/callAction/list`, {
    method: 'GET',
    headers,
    params: { ...params },
    ifTrimParams: true,
  });
}

// 获取所有的业务场景身份
export async function getCallerBusinessType() {
  return request(`/repayment/cms/overdue/management/callAction/business/type`).then((res) => {
    return res.data || [];
  });
}

// 获取所有的外呼结果枚举
export async function getCallerResultType() {
  return request(`/repayment/cms/overdue/management/callAction/call/result/type`).then((res) => {
    return res.data || [];
  });
}
