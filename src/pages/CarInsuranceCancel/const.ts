export interface ListItem {
  bizNo: string;
  orderNo: string;
  orderStatus: number;
  plateNo: string;
  vin: string;
  repaySchemaItemNo: string;
  cancellationReasonStatus: number;
  companyName: string;
  userName: string;
  channelCode: string;
  channelName: string;
  lendingTime: string;
  currentSettleTerm: number;
  loanTerm: number;
  expectedCancellationAmount: string;
  actualCancellationAmount: string;
  actualPaymentAmount: string;
  actualSettleAmount: string;
  expectedCancellationTime: Date;
  expectedPaymentTime: Date;
  actualPaymentTime: Date;
  fileInfoAttach: FileInfoAttach[];
  refundAllocation?: RefundTypeEnum;
  refundRejectionReason: string;
  yiRenXingBankInfo?: BankInfo;
  userBankInfo?: BankInfo;
  yirenxingRefundTime: string;
  userRefundTime: string;
  yirenxingRefundAmount: string;
  userRefundAmount: string;
  cashOrderPolicyExtendId: number;
  settle: boolean;
  createdAt?: string;
  transactionNo?: string;
}

export interface FileInfoAttach {
  fileName: string;
  filePath: string;
}

export interface BankInfo {
  userName: string;
  amount: string;
  accountName: string;
  bankCardNo: string;
  bankName: string;
  transactionNo: string;
  refundStatus: RefundStatusEnum;
  subBranchName: string;
  bankNumber: string;
}

// 退保原因枚举、map
export enum CancelReasonEnum {
  OVERDUE = '1',
  SETTLED = '2',
}
export const cancelReasonEnumMap = {
  [CancelReasonEnum.OVERDUE]: '逾期退保',
  [CancelReasonEnum.SETTLED]: '结清退保',
};

// 状态枚举、map
export enum CancelStatusEnum {
  // 待保司受理
  WAIT_PROCESS = 1,
  // 回款待确认
  WAIT_CONFIRM = 2,
  // 待分配退款
  WAIT_REFUND = 3,
  // 退款待审核
  REFUND_CONFIRM = 4,
  // 退款驳回
  REFUND_REJECT = 5,
  // 退款成功
  REFUND_SUCCESS = 6,
  // 退款失败
  REFUND_FAIL = 7,
  // 退款中
  REFUND_LOADING = 8,
  // 撤销
  CANCEL = 9,
}
export const cancelStatusEnumMap: { [key in CancelStatusEnum]: string } = {
  [CancelStatusEnum.WAIT_PROCESS]: '保司待受理',
  [CancelStatusEnum.WAIT_CONFIRM]: '回款待确认',
  [CancelStatusEnum.WAIT_REFUND]: '待分配退款',
  [CancelStatusEnum.REFUND_CONFIRM]: '退款待审核',
  [CancelStatusEnum.REFUND_REJECT]: '退款驳回',
  [CancelStatusEnum.REFUND_SUCCESS]: '退款成功',
  [CancelStatusEnum.REFUND_FAIL]: '退款失败',
  [CancelStatusEnum.REFUND_LOADING]: '退款中',
  [CancelStatusEnum.CANCEL]: '撤销',
};

// 退款分配
export enum RefundTypeEnum {
  // 结清金额退款至易人行，盈余退款至客户
  ALLOCATION_1 = 1,
  // 结清金额退款至客户，盈余退款至易人行
  ALLOCATION_2 = 2,
  // 全部退款至易人行
  ALLOCATION_3 = 3,
  // 全部退款至客户
  ALLOCATION_4 = 4,
  // 自定义
  ALLOCATION_5 = 5,
}
export const refundTypeEnumMap: { [key in RefundTypeEnum]: string } = {
  [RefundTypeEnum.ALLOCATION_1]: '结清金额退款至易人行，盈余退款至客户',
  [RefundTypeEnum.ALLOCATION_2]: '结清金额退款至客户，盈余退款至易人行',
  [RefundTypeEnum.ALLOCATION_3]: '全部退款至易人行',
  [RefundTypeEnum.ALLOCATION_4]: '全部退款至客户',
  [RefundTypeEnum.ALLOCATION_5]: '自定义',
};
export const refundTypeOptions: any[] = [
  {
    label: '结清金额退款至易人行，盈余退款至客户',
    value: RefundTypeEnum.ALLOCATION_1,
  },
  // {
  //   label: '结清金额退款至客户，盈余退款至易人行',
  //   value: RefundTypeEnum.ALLOCATION_2,
  // },
  {
    label: '全部退款至易人行',
    value: RefundTypeEnum.ALLOCATION_3,
  },
  {
    label: '全部退款至客户',
    value: RefundTypeEnum.ALLOCATION_4,
  },
  {
    label: '自定义',
    value: RefundTypeEnum.ALLOCATION_5,
  },
];

// 退款状态枚举
export enum RefundStatusEnum {
  // 退款成功
  REFUND_SUCCESS = 6,
  // 退款失败
  REFUND_FAIL = 7,
  // 退款中
  REFUND_LOADING = 8,
}
// 退款状态枚举map
export const refundStatusEnumMap: { [key in RefundStatusEnum]: string } = {
  [RefundStatusEnum.REFUND_SUCCESS]: '退款成功',
  [RefundStatusEnum.REFUND_FAIL]: '退款失败',
  [RefundStatusEnum.REFUND_LOADING]: '退款中',
};

// 车辆类别枚举
export enum CarTypeEnum {
  // 车架号
  VIN = 1,
  // 车牌号
  LPN = 2,
}
export const carTypeEnumOptions: any[] = [
  {
    label: '按车架号搜索',
    value: CarTypeEnum.VIN,
  },
  {
    label: '按车牌号搜索',
    value: CarTypeEnum.LPN,
  },
];

// 车辆校验错误类型枚举
export enum CarCheckErrorTypeEnum {
  // 没有车险分期订单
  ERROR_TYPE_1 = 1,
  // 该账号没有所属车辆渠道权限
  ERROR_TYPE_2 = 2,
  // // 车辆已有保单，并且未撤销
  // ERROR_TYPE_3 = 3,
  // // 车辆状态不是还款中/提前结清/逾期结清/逾期
  // ERROR_TYPE_4 = 4,
}
export const carCheckErrorTypeEnumMap: { [key in CarCheckErrorTypeEnum]: string } = {
  [CarCheckErrorTypeEnum.ERROR_TYPE_1]: '该车辆不存在可退保订单',
  [CarCheckErrorTypeEnum.ERROR_TYPE_2]: '你对该车辆没有处理权限',
  // [CarCheckErrorTypeEnum.ERROR_TYPE_3]: '该车辆已创建退保单且未撤销',
  // [CarCheckErrorTypeEnum.ERROR_TYPE_4]: '车辆状态不是还款中/提前结清/逾期结清/逾期',
};

// 退保详情
export interface DetailInfo {
  orderNo: string;
  orderStatus: CancelStatusEnum;
  plateNo: string;
  vin: string;
  repaySchemaItemNo: string;
  cancellationReasonStatus: CancelReasonEnum;
  companyName: string;
  userName: string;
  channelCode: string;
  channelName: string;
  lendingTime: string;
  currentSettleTerm: string;
  loanTerm: string;
  expectedCancellationAmount: string;
  actualCancellationAmount: string;
  actualPaymentAmount: string;
  actualSettleAmount: string;
  expectedCancellationTime: string;
  expectedPaymentTime: string;
  actualPaymentTime: string;
  fileInfoAttach: FileInfoAttach[];
  refundAllocation: RefundTypeEnum;
  refundRejectionReason: string;
  yiRenXingBankInfo: BankInfo;
  userBankInfo: BankInfo;
  settle: boolean;
  yirenxingRefundAmount: string;
  userRefundAmount: string;
  yirenxingRefundTime?: string;
  userRefundTime?: string;
  transactionNo?: string;
}

export interface FileInfoAttach {
  fileName: string;
  filePath: string;
  fileRelativePath?: string;
}

export enum CHANNEL_LEVEL {
  FIRST_CHANNEL = 1,
  SECOND_CHANNEL = 2,
}

// 识别类型枚举
export enum CarPolicyTypeEnum {
  VIN = 1,
  LPN = 2,
}
