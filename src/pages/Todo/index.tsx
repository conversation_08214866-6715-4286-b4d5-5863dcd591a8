/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-03-28 19:00:21
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-10-12 17:49:41
 * @FilePath: /code/lala-finance-biz-web/src/pages/Todo/index.tsx
 * @Description: Todo
 */
import HeaderTab from '@/components/HeaderTab';
import { ClockCircleOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { useAccess, useNavigate, useRequest } from '@umijs/max';
import { <PERSON>ert, Badge, Button, Card, Col, Row } from 'antd';
import React from 'react';
import type { State } from './data';
import { queryAuditStatistics } from './service';
import './todo.less';

const RENT_ROUTE = {
  submitInputCount: { text: '待提交进件信息', orderStatus: ['0', '1', '3'] }, //  包含已注册、授权签约成功、订单创建成功，所有待提交进件信息状态的订单
  preAuditPassCount: { text: '待提交风控审核', orderStatus: ['15'] }, //  包含列表预审通过状态的订单
  returnPatchCount: { text: '风控驳回待提交', orderStatus: ['22'] }, //  包含风控驳回的订单
  // deratingConfirmedCount: { text: '降额待确认', orderStatus: ['23'] }, //  包含降额待确认的订单
  stayBindCarCount: { text: '待绑车', orderStatus: ['40', '50'] }, //  包含审核通过、降额通过的订单
  stayBindBankCardCount: { text: '待绑定银行卡', orderStatus: ['51'] }, //  包含车辆绑定成功的订单
  staySignedCount: { text: '待签约', orderStatus: ['53'] }, //  包含银行卡绑定成功的订单
  pendingGpsOrderCount: {
    text: '待下单安装GPS设备',
    orderStatus: ['54', '60'],
    hasCreateGpsOrder: '0',
  }, // 订单完成合同签约+待放款+未下单安装GPS设备；
  stayApplyLendingCount: { text: '待请款', orderStatus: ['60'], loanStatus: '3' }, //  包含订单列表状态待放款且放款状态为待请款的订单
  stayLendingCount: { text: '待放款', orderStatus: ['60'], loanStatus: '10' }, //  包含放款管理列表放款状态为待一审的订单（只允许运营访问）
  staySubmitDeliveryCount: {
    text: '待提交交车资料',
    // orderStatus: ['54', '60', '61'], 不需要查询订单
    carDeliveryStatus: ['0', '1'], // 未提交&超期提交 交车资料状态
  }, //  包含订单状态等于签约成功、待放款、放款审批通过且未提交车交资料
  lendingRejectCount: { text: '请款驳回', orderStatus: ['60'], loanStatus: '12' }, // 包含订单状态待放款且放款状态为提交请款资料被驳回的订单
};

enum PRODUCE_CODE {
  rent = '0201',
}

const tablist = [
  {
    key: PRODUCE_CODE.rent,
    tab: '融资租赁',
  },
];

const ToDo: React.FC = () => {
  const access = useAccess();
  const navigate = useNavigate();

  const { data, loading } = useRequest(() => {
    return queryAuditStatistics({ productSecondCode: PRODUCE_CODE.rent });
  });

  const handleToPage = (state: State, key: string) => {
    const route = key === 'stayLendingCount' ? '/businessMng/loan-list' : '/businessMng/lease-list';
    const currentState = {
      ...state,
      startTime: data?.startTime,
      endTime: data?.endTime,
    };
    navigate(route, {
      state: currentState,
    });
    // history.push(route, currentState);
  };

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Card>
          <Row className="todo-title">
            <Col xxl={2} lg={2} md={3} sm={6}>
              <span className="title ant-typography-ellipsis-single-line">待办看板</span>
            </Col>
            <Col xxl={16} lg={16} md={15} sm={10}>
              <Button.Group className="btn-group">
                {tablist.map((item) => {
                  return (
                    <Button key={item.key} type="primary">
                      {item.tab}
                    </Button>
                  );
                })}
              </Button.Group>
            </Col>
          </Row>
          {data &&
            Object.keys(data).some((key: string) => {
              return data[key] > 0;
            }) && (
              <Alert
                message="你还有工作没做完 :)"
                showIcon
                banner
                style={{
                  margin: -12,
                  marginBottom: 24,
                }}
              />
            )}
          <Row gutter={[0, 32]}>
            {data &&
              Object.keys(RENT_ROUTE).map((key) => {
                // 待放款只有运营可以看
                if (!access.hasAccess('biz_table_pending_loan_todo') && key === 'stayLendingCount')
                  return <></>;
                return (
                  <Col span={6} key={key}>
                    <Badge
                      showZero
                      count={
                        loading ? (
                          <ClockCircleOutlined style={{ color: '#f5222d' }} />
                        ) : (
                          data?.[key] || ''
                        )
                      }
                    >
                      <a
                        className="link"
                        onClick={() => {
                          // handleToPage(RENT_ROUTE[key], key);
                          if (data?.[key] > 0) handleToPage(RENT_ROUTE[key], key);
                        }}
                      >
                        {RENT_ROUTE[key].text}
                      </a>
                    </Badge>
                  </Col>
                );
              })}
          </Row>
        </Card>
      </PageContainer>
    </>
  );
};

export default ToDo;
