/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2021-07-12 18:03:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
import { request } from '@umijs/max';
import type { OverdueConfigListParams } from './data';

// 获取用户管理列表数据
export async function getOverdueConfigList(params?: OverdueConfigListParams) {
  return request('/repayment/cms/overdue/management/overdueConfig', {
    method: 'GET',
    params: { ...params },
  });
}

export async function getWitholdConfig(productCode: string) {
  return request(`/loan/product/detail/${productCode}`);
}
