/*
 * @Author: your name
 * @Date: 2021-10-27 15:44:40
 * @LastEditTime: 2024-01-04 20:11:41
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/FeeConfigModal.tsx
 */
import {
  BASE_PROPORTION,
  BASE_PROPORTION_NO_BRACKETS,
  EXPENSE_ITEMS,
  EXPENSE_ITEMS_CONFIG,
  MAX_OPEN_CLOSE,
  MIN_OPEN_CLOSE,
} from '@/enums';
import globalStyle from '@/global.less';
import { UnlockOutlined } from '@ant-design/icons';
import ProForm, {
  ModalForm,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
} from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Button, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { updateFeeConfig } from '../service';
import EditTable from './EditTable';
import RegionInput from './RegionInput';

type FeeConfigModalProps = {
  onCancel?: () => void;
  onOk?: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  curRow?: any;
  // curOptionTypeFromProp?: string;
  setOptionType?: (curType: string) => void;
  // disableForm?: boolean;
  curOptionType?: string;
  initialWhetherByPriceTableData?: any;
  id?: string;
  classification?: string;
  repaymentTerm?: [];
  initialRepaymentTerm?: []; // 初始化的还款期限
  modifyLeaseFeeConfigData?: (dataItem: any) => void;
};

const FeeConfigModal: React.FC<FeeConfigModalProps> = ({
  modalVisible,
  onVisibleChange,
  setOptionType,
  curOptionType = 'SHOW',
  curRow,
  classification,
  repaymentTerm,
  initialRepaymentTerm = [],
  modifyLeaseFeeConfigData,
}) => {
  const [disableForm, setDisableForm] = useState(true);
  const [form] = Form.useForm();
  let CONFIG_ITEM = EXPENSE_ITEMS_CONFIG[curRow?.expenseItem];
  // console.log(CONFIG_ITEM, CONFIG_ITEM?.proportionNumberEnum !== undefined);
  // console.log(EXPENSE_ITEMS_CONFIG, curRow?.expenseItem, EXPENSE_ITEMS_CONFIG[curRow?.expenseItem]);
  useEffect(() => {
    // 融租的逾期滞纳金写死为0
    if (
      classification === 'FINANCE_LEASE' &&
      CONFIG_ITEM &&
      curRow?.expenseItem === 'OVERDUE_DELAY_AMOUNT'
    ) {
      CONFIG_ITEM['proportionNumberValue'] = 0;
    }
    const formObj = { ...curRow, ...CONFIG_ITEM };
    console.log('formObj', formObj);
    form.setFieldsValue(formObj); // 初始化默认值
  }, [CONFIG_ITEM, modalVisible]);
  // 校验区间格式
  const checkRegion = (_: any, value: any, fieldName: string) => {
    // console.log(value);
    if (
      (fieldName === 'regionAmount' && (value?.amountMin === null || value?.amountMax === null)) ||
      (fieldName === 'regionProportion' &&
        (value?.maxProportionNumberValue === null || value?.minProportionNumberValue == null))
    ) {
      return Promise.reject(new Error('请注意左右区间配置完整'));
    }

    if (
      (value?.minProportionNumberValue !== undefined &&
        value?.maxProportionNumberValue !== undefined &&
        value?.minProportionNumberValue >= value?.maxProportionNumberValue) ||
      (value?.amountMin !== undefined &&
        value?.amountMax !== undefined &&
        value?.amountMin >= value?.amountMax)
    ) {
      return Promise.reject(new Error('区间配置不正确'));
    }
    return Promise.resolve();
  };
  // console.log(
  //   initialWhetherByPriceTableData[curRow?.expenseItem],
  //   initialWhetherByPriceTableData,
  //   curRow?.expenseItem,
  // );

  const checkEditTable = (_, value: any, calculationEnum: string) => {
    let validateField = '';
    switch (curRow.expenseItem) {
      case 'BAIL':
        if (calculationEnum === 'AMOUNT_PROPORTION') {
          validateField = 'amount';
        } else {
          validateField = 'proportion';
        }
        break;
      case 'LEASE_INTEREST':
        validateField = 'proportion';
        break;
      default:
        break;
    }
    const hasRequiredError = value?.some((item) => {
      return (
        item[validateField] === null ||
        item[validateField] === undefined ||
        item[validateField] === ''
      );
    });
    // console.log(_, value, hasRequiredError, validateField);
    if (hasRequiredError) {
      return Promise.reject(new Error('分离定价表必填'));
    }
    return Promise.resolve();
  };

  return (
    <>
      <ModalForm
        title={`${curOptionType === 'SHOW' ? '查看' : '编辑'}${EXPENSE_ITEMS[curRow?.expenseItem]}`}
        className={globalStyle.formModalLabel130}
        layout="horizontal"
        form={form}
        modalProps={{
          centered: true,
          okText: '提交',
          afterClose: () => {
            setDisableForm(true);
            CONFIG_ITEM = {};
          },
          destroyOnClose: true,
        }}
        submitter={{
          render: (props, defaultDoms) => {
            if (curOptionType === 'SHOW') {
              return [
                defaultDoms[0],
                <Button
                  type="primary"
                  key="edit"
                  onClick={() => {
                    setDisableForm(false);
                    setOptionType?.('EDIT');
                    return false;
                  }}
                >
                  <span>编辑</span>
                  <UnlockOutlined />
                </Button>,
              ];
            }
            return [...defaultDoms];
          },
        }}
        initialValues={CONFIG_ITEM}
        visible={modalVisible}
        onVisibleChange={onVisibleChange}
        onFinish={async (values) => {
          console.log('values', values);
          console.log('classification', classification);
          // 修改LeaseFeeConfigTable数据，然后展示
          if (curRow?.expenseItem === 'LEASE_IN_ADVANCE_LIQUIDATED_DAMAGES') {
            // 如果是融租的 提前结清违约金需要请求接口
            const { id } = history.location.query;
            if (id) {
              updateFeeConfig({
                id,
                proportionalNumber: values?.proportionNumberValue,
              }).then(() => {
                message.success('更新成功');
              });
            }
          }
          modifyLeaseFeeConfigData?.({
            expenseItem: curRow?.expenseItem,
            expenseItemData: values,
          });
          onVisibleChange(false);
          return true;
        }}
      >
        <ProFormRadio.Group
          name="whetherByPriceTable"
          label="是否分离定价"
          rules={[{ required: true }]}
          disabled={disableForm || CONFIG_ITEM?.whetherByPriceTable !== undefined}
          options={[
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ]}
        />
        <ProFormDependency name={['whetherByPriceTable']}>
          {({ whetherByPriceTable }) => {
            return whetherByPriceTable ? (
              <ProFormSelect
                options={[
                  {
                    value: 'REPAYMENT_TERM',
                    label: '还款期限',
                  },
                  { value: 'RISK_LEVEL', label: '风控等级' },
                  {
                    value: 'CAR_TYPE',
                    label: '车辆类型',
                  },
                ]}
                width="md"
                rules={[{ required: true }]}
                fieldProps={{
                  mode: 'multiple',
                }}
                disabled={CONFIG_ITEM?.priceTableParams !== undefined}
                name="priceTableParams"
                placeholder="请选择分离定价组合参数"
                label="分离定价组合参数"
              />
            ) : (
              ''
            );
          }}
        </ProFormDependency>
        <ProFormRadio.Group
          name="calculationEnum"
          label="计算方式"
          rules={[{ required: true }]}
          disabled={disableForm || CONFIG_ITEM?.calculationEnum !== undefined}
          options={[
            {
              label: '按金额',
              value: 'AMOUNT_PROPORTION',
            },
            {
              label: '按比例',
              value: 'PROPORTIONALLY',
            },
          ]}
        />
        {/* 根据是金额或者比例联动 */}
        <ProFormDependency name={['calculationEnum']}>
          {({ calculationEnum }) => {
            // console.log(name2);
            if (!calculationEnum) {
              return '';
            }
            // 初始化区间比例数据
            if (calculationEnum !== curRow?.calculationEnum) {
              form.setFieldsValue({
                regionProportion: undefined,
              });
            }
            const amountOptions = [
              {
                label: '固定金额',
                value: 'FIXED_AMOUNT',
              },
              {
                label: '区间范围',
                value: 'RANGE_AMOUNT',
              },
            ];
            // 首付要增加比例区间金额范围
            if (curRow?.expenseItem === 'LOW_DOWN_PAYMENT') {
              amountOptions.push({
                label: '比例区间金额范围',
                value: 'PROPORTION_RANGE_AMOUNT',
              });
            }
            return calculationEnum === 'AMOUNT_PROPORTION' ? (
              <>
                <ProFormRadio.Group
                  name="amountTypeEnum"
                  label="金额类型"
                  rules={[{ required: true }]}
                  // initialValue="1"
                  disabled={disableForm || CONFIG_ITEM?.amountTypeEnum !== undefined}
                  options={amountOptions}
                />
                <ProFormDependency name={['amountTypeEnum', 'whetherByPriceTable']}>
                  {({ amountTypeEnum, whetherByPriceTable }) => {
                    // console.log(monType, monType === 1);
                    // 初始化区间比例数据
                    if (amountTypeEnum !== curRow?.amountTypeEnum) {
                      form.setFieldsValue({
                        regionProportion: undefined,
                      });
                    }

                    // 如果金额类型=比例区间金额范围，比例计算基数要改成'车辆成交价'，否则改回'车辆转让价'
                    if (curRow?.expenseItem === 'LOW_DOWN_PAYMENT') {
                      if (amountTypeEnum === 'PROPORTION_RANGE_AMOUNT') {
                        form.setFieldsValue({
                          baseEnum: 'CAR_DEAL_PRICE',
                        });
                      } else {
                        form.setFieldsValue({
                          baseEnum: 'CAR_TRANSFER_PRICE',
                        });
                      }
                    }

                    if (calculationEnum !== 'AMOUNT_PROPORTION' || !amountTypeEnum) {
                      return '';
                    }
                    // 分离定价为false才可以展示
                    return (
                      whetherByPriceTable === false && (
                        <>
                          {amountTypeEnum === 'PROPORTION_RANGE_AMOUNT' && (
                            <ProFormSelect
                              options={Object.keys(BASE_PROPORTION_NO_BRACKETS).map((key) => ({
                                value: key,
                                label: BASE_PROPORTION_NO_BRACKETS[key],
                              }))}
                              width="md"
                              rules={[{ required: true }]}
                              disabled={true}
                              name="baseEnum"
                              placeholder="请选择比例计算基数"
                              label="比例计算基数"
                            />
                          )}

                          {amountTypeEnum === 'FIXED_AMOUNT' && (
                            <ProFormDigit
                              rules={[{ required: true }]}
                              disabled={disableForm}
                              name="amount"
                              width="md"
                              fieldProps={{
                                precision: 2,
                                onPressEnter: (e) => {
                                  e.preventDefault();
                                },
                              }}
                              label="固定值（元）"
                            />
                          )}
                          {amountTypeEnum === 'RANGE_AMOUNT' && (
                            <ProForm.Item
                              name="regionAmount"
                              label="区间值（元）"
                              rules={[
                                {
                                  validator: (_, value) => {
                                    return checkRegion(_, value, 'regionAmount');
                                  },
                                },
                                { required: true },
                              ]}
                            >
                              <RegionInput
                                disabledForm={disableForm}
                                minValueReplace="amountMin"
                                maxValueReplace="amountMax"
                                precision={2}
                              />
                            </ProForm.Item>
                          )}
                          {/* 金额类型=比例区间金额 */}
                          {amountTypeEnum === 'PROPORTION_RANGE_AMOUNT' && (
                            <ProForm.Item
                              name="regionProportion"
                              label="区间比例"
                              rules={[
                                {
                                  validator: (_, value) => {
                                    return checkRegion(_, value, 'regionProportion');
                                  },
                                },
                                { required: true },
                              ]}
                            >
                              <RegionInput
                                disabledForm={disableForm}
                                minValueReplace="minProportionNumberValue"
                                maxValueReplace="maxProportionNumberValue"
                              />
                            </ProForm.Item>
                          )}
                          <ProFormDependency
                            name={['amountTypeEnum', 'baseEnum', 'regionProportion']}
                          >
                            {({ baseEnum, regionProportion }) => {
                              if (amountTypeEnum !== 'PROPORTION_RANGE_AMOUNT') {
                                return '';
                              }
                              // 组装比例范围
                              const regionProportionObj = regionProportion || {};
                              const regionProportionTemp = `${
                                MIN_OPEN_CLOSE[regionProportionObj?.minOpenOrCloseRange] || '['
                              }${regionProportionObj?.minProportionNumberValue || '区间比例1'},${
                                regionProportionObj?.maxProportionNumberValue || '区间比例2'
                              }${MAX_OPEN_CLOSE[regionProportionObj?.maxOpenOrCloseRange] || ']'}`;

                              // 组装预期公式
                              const finalStr = ` 预期公式:${EXPENSE_ITEMS[curRow?.expenseItem]}=
                              ${BASE_PROPORTION[baseEnum] || '比例计算基数'}*
                              ${regionProportionTemp}`;
                              return (
                                <Form.Item label=" " colon={false}>
                                  <p className={globalStyle.colorBlue}>{finalStr}</p>
                                </Form.Item>
                              );
                            }}
                          </ProFormDependency>
                        </>
                      )
                    );
                  }}
                </ProFormDependency>
              </>
            ) : (
              <>
                <ProFormRadio.Group
                  name="proportionNumberEnum"
                  label="比例类型"
                  rules={[{ required: true }]}
                  disabled={disableForm || CONFIG_ITEM?.proportionNumberEnum !== undefined}
                  options={[
                    {
                      label: '固定比例',
                      value: 'BY_FIXED_RATION',
                    },
                    {
                      label: '区间范围',
                      value: 'BY_RANGE_RATION',
                    },
                  ]}
                />
                <ProFormDependency name={['proportionNumberEnum', 'whetherByPriceTable']}>
                  {({ proportionNumberEnum, whetherByPriceTable }) => {
                    if (calculationEnum !== 'PROPORTIONALLY' || !proportionNumberEnum) {
                      return '';
                    }
                    // 初始化比例计算基数和区间比例数据
                    if (curRow?.expenseItem === 'LOW_DOWN_PAYMENT') {
                      form.setFieldsValue({
                        baseEnum: 'CAR_TRANSFER_PRICE',
                      });
                    }

                    return (
                      <>
                        <ProFormSelect
                          options={Object.keys(BASE_PROPORTION_NO_BRACKETS).map((key) => ({
                            value: key,
                            label: BASE_PROPORTION_NO_BRACKETS[key],
                          }))}
                          width="md"
                          rules={[{ required: true }]}
                          disabled={disableForm || CONFIG_ITEM?.baseEnum !== undefined}
                          name="baseEnum"
                          placeholder="请选择比例计算基数"
                          label="比例计算基数"
                        />
                        {/* 分离定价为否才展示 */}
                        {whetherByPriceTable === false ? (
                          <>
                            {proportionNumberEnum === 'BY_FIXED_RATION' ? (
                              <>
                                <ProFormRadio.Group
                                  name="relationFlage"
                                  label="固定比例类型"
                                  rules={[{ required: true }]}
                                  disabled={
                                    disableForm ||
                                    CONFIG_ITEM?.proportionNumberEnum !== undefined ||
                                    CONFIG_ITEM?.relationFlage !== undefined
                                  }
                                  options={[
                                    {
                                      label: '固定比例',
                                      value: false,
                                    },
                                    {
                                      label: '借款利率关联比例',
                                      value: true,
                                    },
                                  ]}
                                />
                                <ProFormDependency name={['relationFlage']}>
                                  {({ relationFlage }) => {
                                    // console.log(relationFlage);
                                    return relationFlage ? (
                                      <ProFormDigit
                                        label="上浮系数"
                                        width="md"
                                        disabled={disableForm}
                                        name="floatCoefficient"
                                        rules={[{ required: true }]}
                                        fieldProps={{
                                          onPressEnter: (e) => {
                                            e.preventDefault();
                                          },
                                        }}
                                      />
                                    ) : (
                                      <ProFormDigit
                                        label="固定比例"
                                        width="md"
                                        disabled={
                                          classification === 'FINANCE_LEASE' &&
                                          curRow?.expenseItem === 'OVERDUE_DELAY_AMOUNT'
                                            ? true
                                            : disableForm
                                        }
                                        name="proportionNumberValue"
                                        rules={[
                                          { required: true },
                                          // {
                                          //   validator: (rule, value) => {
                                          //     if (!Number(value)) {
                                          //       return Promise.reject(new Error('固定比例不能为0'));
                                          //     }
                                          //     return Promise.resolve();
                                          //   },
                                          // },
                                        ]}
                                        fieldProps={{
                                          formatter: (value?: number) => {
                                            return value
                                              ? `${parseFloat(Number(value).toFixed(4))}`
                                              : '';
                                          },

                                          onPressEnter: (e) => {
                                            e.preventDefault();
                                          },
                                        }}
                                      />
                                    );
                                  }}
                                </ProFormDependency>
                              </>
                            ) : (
                              <ProForm.Item
                                name="regionProportion"
                                label="区间比例"
                                rules={[
                                  {
                                    validator: (_, value) => {
                                      return checkRegion(_, value, 'regionProportion');
                                    },
                                  },
                                  { required: true },
                                ]}
                              >
                                <RegionInput
                                  disabledForm={disableForm}
                                  minValueReplace="minProportionNumberValue"
                                  maxValueReplace="maxProportionNumberValue"
                                />
                              </ProForm.Item>
                            )}
                            {/* 逾期和利息不展示预期公式 */}
                            {curRow?.expenseItem !== 'LEASE_INTEREST' &&
                              curRow?.expenseItem !== 'LEASE_OVERDUE_PENALTY_INTEREST' && (
                                <ProFormDependency
                                  name={[
                                    'baseEnum',
                                    'proportionNumberValue',
                                    'regionProportion',
                                    'relationFlage',
                                    'floatCoefficient',
                                  ]}
                                >
                                  {({
                                    baseEnum,
                                    proportionNumberValue,
                                    regionProportion,
                                    relationFlage,
                                    floatCoefficient,
                                  }) => {
                                    const regionProportionObj = regionProportion || {};
                                    const regionProportionTemp = `${
                                      MIN_OPEN_CLOSE[regionProportionObj?.minOpenOrCloseRange] ||
                                      '['
                                    }${
                                      regionProportionObj?.minProportionNumberValue || '区间比例1'
                                    },${
                                      regionProportionObj?.maxProportionNumberValue || '区间比例2'
                                    }${
                                      MAX_OPEN_CLOSE[regionProportion?.maxOpenOrCloseRange] || ']'
                                    }`;
                                    // console.log(regionProportionTemp);
                                    const proportionNumberValueTemp =
                                      proportionNumberValue !== null
                                        ? proportionNumberValue || 0
                                        : '固定比例';
                                    let finalStr = '';

                                    if (relationFlage) {
                                      //单独处理借款利率比例存在的情况
                                      finalStr = `预期公式：${EXPENSE_ITEMS[curRow?.expenseItem]}=${
                                        BASE_PROPORTION[baseEnum] || '比例计算基数'
                                      }*借款申请日利率*（1 + ${floatCoefficient})`;
                                    } else {
                                      finalStr = ` 预期公式:${EXPENSE_ITEMS[curRow?.expenseItem]}=
                                      ${BASE_PROPORTION[baseEnum] || '比例计算基数'}*
                                      ${
                                        proportionNumberEnum === 'BY_FIXED_RATION'
                                          ? proportionNumberValueTemp
                                          : regionProportionTemp
                                      }`;
                                    }
                                    if (proportionNumberEnum !== undefined) {
                                      return (
                                        <Form.Item label=" " colon={false}>
                                          <p className={globalStyle.colorBlue}>{finalStr}</p>
                                        </Form.Item>
                                      );
                                    }
                                  }}
                                </ProFormDependency>
                              )}
                          </>
                        ) : (
                          ''
                        )}
                      </>
                    );
                  }}
                </ProFormDependency>
              </>
            );
          }}
          {/* 根据金额类型联动区间值或者，固定值 */}
        </ProFormDependency>
        <ProFormDependency
          name={[
            'priceTableParams',
            'whetherByPriceTable',
            'calculationEnum',
            'proportionNumberEnum',
            'amountTypeEnum',
          ]}
        >
          {({
            priceTableParams,
            whetherByPriceTable,
            calculationEnum,
            proportionNumberEnum,
            amountTypeEnum,
          }) => {
            let amountOrProportion = '';
            if (calculationEnum !== undefined) {
              amountOrProportion =
                calculationEnum === 'AMOUNT_PROPORTION' ? amountTypeEnum : proportionNumberEnum; // 按金额或者按比例只一种
            }
            return whetherByPriceTable ? (
              <ProForm.Item
                name="priceTableList"
                label="分离定价表"
                rules={[
                  {
                    validator: (_, value) => {
                      return checkEditTable(_, value, calculationEnum);
                    },
                  },
                  { required: true },
                ]}
              >
                <EditTable
                  priceTableParams={[...(priceTableParams || []), amountOrProportion]}
                  initialDataSource={curRow?.priceTableList || []}
                  repaymentTerm={repaymentTerm || []}
                  initialRepaymentTerm={initialRepaymentTerm || []}
                  disabled={disableForm}
                  // modalVisible={modalVisible}
                />
              </ProForm.Item>
            ) : (
              ''
            );
          }}
        </ProFormDependency>

        {curRow?.expenseItem !== 'LEASE_INTEREST' &&
          curRow?.expenseItem !== 'LEASE_OVERDUE_PENALTY_INTEREST' && (
            <ProFormDependency
              name={[
                'baseEnum',
                'whetherByPriceTable',
                'proportionNumberEnum',
                'calculationEnum',
                'relationFlage',
              ]}
            >
              {({
                baseEnum, //比例计算基数
                whetherByPriceTable, //是否分离定价
                proportionNumberEnum, //比例类型
                calculationEnum, //计算方式
              }) => {
                const coefficient =
                  calculationEnum &&
                  (calculationEnum === 'AMOUNT_PROPORTION'
                    ? '每期应还'
                    : BASE_PROPORTION[baseEnum] || '比例计算基数');
                const isAmountOrProportion =
                  calculationEnum === 'AMOUNT_PROPORTION' ? '金额' : '比例';
                const coefficient2 =
                  proportionNumberEnum === 'BY_FIXED_RATION'
                    ? `固定${isAmountOrProportion}(n)`
                    : `区间${isAmountOrProportion}(n)`;

                return whetherByPriceTable !== undefined &&
                  whetherByPriceTable &&
                  calculationEnum ? (
                  <Form.Item
                    label=" "
                    colon={false}
                    //
                    className={globalStyle.mt16}
                    style={{ color: 'blue' }}
                  >
                    <p className={globalStyle?.colorBlue}>
                      预期公式:{EXPENSE_ITEMS[curRow?.expenseItem]}={coefficient}*{coefficient2}
                    </p>
                  </Form.Item>
                ) : (
                  ''
                );
              }}
            </ProFormDependency>
          )}
      </ModalForm>
    </>
  );
};

export default React.memo(FeeConfigModal);
