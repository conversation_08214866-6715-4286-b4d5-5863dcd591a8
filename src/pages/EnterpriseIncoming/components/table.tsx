import { SECONDARY_CLASSIFICATION_MAP } from '@/enums';
import globalStyle from '@/global.less';
import { disableFutureDate, downLoadExcel } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess } from '@umijs/max';
import { Button, DatePicker, Tooltip } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import type { UserInputRecordListItem, UserInputRecordListParams } from '../data';
import { getInputRecordList, incomeExport } from '../service';
import BackStageIncomeModal from './BackStageIncomeModal';

const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
  return (
    <DatePicker.RangePicker
      {...rest}
      className={globalStyle.w100}
      disabledDate={disableFutureDate}
    />
  );
};
const RecordTable: React.FC<{}> = () => {
  const currentDate = dayjs();
  const defaultStartDate = dayjs().subtract(1, 'month');
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const getExport = (form: UserInputRecordListParams) => {
    incomeExport(form).then((res) => {
      downLoadExcel(res);
    });
  };
  // 当前登陆用户信息
  const access = useAccess();
  const [visibleCreate, handleVisibleCreate] = useState<boolean>(false);
  const columns: ProColumns<UserInputRecordListItem>[] = [
    {
      title: '进件流水号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
      key: 'enterpriseName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'orgCode',
      key: 'orgCode',
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      key: 'classification',
      search: false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondClassification',
      key: 'secondClassification',
      valueType: 'select',

      // 如果是企业财务，默认共享应收账款，应收账款
      initialValue: access.hasRole('enterpriseFinance') ? ['0103', '0106'] : [],
      valueEnum: SECONDARY_CLASSIFICATION_MAP,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: !!access.hasRole('enterpriseFinance'),
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      // search: false,
    },

    {
      title: '申请额度',
      dataIndex: 'applyAmount',
      key: 'applyAmount',
      search: false,
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      valueType: 'dateRange',
      initialValue: [defaultStartDate, currentDate],
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.applyTime;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          startApplyTime: `${value[0].split(' ')[0]} 00:00:00`,
          endApplyTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '进件状态',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        0: { text: '用户创建成功' },
        11: { text: '风控中' },
        30: { text: '待初审' },
        31: { text: '待终审' },
        40: { text: '审核通过' },
        41: { text: '审核拒绝' },
        42: { text: '撤销' },
        42: { text: '撤销' },
        44: { text: '签约成功' },
        12: { text: '企业三要素认证成功' },
        14: { text: '授权人三要素认证成功' },
        16: { text: '银行卡信息校验成功' },
        '-2': { text: '注销' },
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '拒绝原因',
      dataIndex: 'rejectReason',
      key: 'rejectReason',
      search: false,
      width: 200,
      render: (rejectReason) => {
        // const rejectReason = '资格类资格类';
        return rejectReason?.length > 6 ? (
          <Tooltip placement="topLeft" title={rejectReason}>
            <div
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                width: 200,
                textOverflow: 'ellipsis',
              }}
            >
              {rejectReason}
            </div>
          </Tooltip>
        ) : (
          rejectReason
        );
      },
    },
    {
      title: '认证完成时间',
      dataIndex: 'verifyEndTime',
      key: 'verifyEndTime',
      search: false,
    },
    {
      title: '审核完成时间',
      dataIndex: 'creditEndTime',
      search: false,
    },
    {
      title: '授信额度',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
      search: false,
    },
    {
      title: '客户标签',
      dataIndex: 'userTag',
      key: 'userTag',
      valueEnum: {
        GKA: 'GKA',
      },
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (text, row) => (
        <>
          <Link to={`/userMng/enterpriseMng/detail?orderNo=${row.orderNo}`}>查看详情</Link>
        </>
      ),
    },
  ];

  return (
    <>
      <ProTable<UserInputRecordListItem>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        scroll={{ x: 'max-content' }}
        rowKey="orderNo"
        search={{ labelWidth: 120 }}
        toolBarRender={() => {
          return [
            <Button
              onClick={() => {
                handleVisibleCreate(true);
              }}
              type="primary"
            >
              创建进件
            </Button>,
            access.hasAccess('biz_download') && (
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  const { applyTime, ...data } = formRef?.current?.getFieldsValue();
                  let newForm = { ...data };
                  if (applyTime?.length) {
                    const startApplyTime = `${applyTime[0].format('YYYY-MM-DD')} 00:00:00`;
                    const endApplyTime = `${applyTime[1].format('YYYY-MM-DD')} 23:59:59`;
                    newForm = { ...data, startApplyTime, endApplyTime };
                  }
                  getExport(newForm);
                }}
              >
                导出
              </Button>
            ),
          ];
        }}
        request={(params) => {
          return getInputRecordList(params).then((res) => {
            const { queryParam = {}, ...rest } = res;
            const { startApplyTime, endApplyTime } = queryParam;
            if (startApplyTime && endApplyTime) {
              // 回显表单的申请时间
              formRef?.current?.setFieldsValue({
                applyTime: [dayjs(startApplyTime), dayjs(endApplyTime)],
              });
            } else {
              // 回显表单的申请时间
              formRef?.current?.setFieldsValue({
                applyTime: undefined,
              });
            }
            return rest;
          });
        }}
      />

      <BackStageIncomeModal
        modalVisible={visibleCreate}
        onOk={async () => {
          handleVisibleCreate(false);

          actionRef?.current?.reload();
        }}
        // isNewCar={}
        onCancel={() => {
          handleVisibleCreate(false);
        }}
        // isNewCar={props.deadLine}
        onVisibleChange={handleVisibleCreate}
      />
    </>
  );
};

export default RecordTable;
