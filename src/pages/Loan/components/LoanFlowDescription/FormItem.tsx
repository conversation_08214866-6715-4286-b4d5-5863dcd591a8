import ImagePreview from '@/components/ImagePreview';
import type { FormItemProps } from 'antd';
import { Col, Form } from 'antd';
import React from 'react';

interface FIInterface {
  readonly: boolean;
  formProps: FormItemProps;
  label: string;
  value: string | Record<string, any> | any[];
  name: string;
  isFile?: boolean;
  children: React.ReactNode;
}

const FormItem: React.FC<FIInterface> = (props) => {
  const { readonly, isFile, formProps, label, value, name, children } = props;

  return (
    <Col span={8} className="desc-item">
      {readonly ? (
        <div className="upload-file-show-list">
          <div className="data-index">{label}：</div>
          {isFile ? (
            <div className="upload-file-show-list">
              {(value || [])?.map((item: any) => (
                <div key={item.filePath}>
                  <ImagePreview url={item.netWorkPath}>
                    <div className="upload-file-form-item">
                      <span style={{ color: '#1677ff' }}>{item?.fileName}</span>
                    </div>
                  </ImagePreview>
                </div>
              ))}
              {(!value || value.length === 0) && '-'}
            </div>
          ) : (
            <div className="data">{value || '-'}</div>
          )}
        </div>
      ) : (
        <Form.Item
          label={label}
          style={{ width: '100%', color: '#a9a9a7' }}
          wrapperCol={{ span: 16 }}
          name={name}
          {...formProps}
        >
          {children}
        </Form.Item>
      )}
    </Col>
  );
};

export default React.memo(FormItem);
