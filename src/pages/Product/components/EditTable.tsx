import React, { useState, useEffect } from 'react';
import type { ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
// import ProCard from '@ant-design/pro-card';
import { InputNumber } from 'antd';
// import { ProFormField } from '@ant-design/pro-form';
import { CAR_TYPE, RANK } from '@/enums';
// import { usePrevious } from 'ahooks';
import { isEqual } from 'lodash';
import RegionInput from './RegionInput';

type DataSourceType = {
  id: React.Key;
  carType?: string;
  instalment?: string;
  auditResultEnum?: [];
  regionProportion?: {};
  interestRate?: number;
  initialRepaymentTerm?: number;
  // update_at?: string;
  children?: DataSourceType[];
};
interface EditTableProps {
  value?: any;
  onChange?: (value: any) => void;
  priceTableParams?: string | string[];
  initialDataSource?: [];
  repaymentTerm?: [];
  initialRepaymentTerm?: [];
  disabled?: boolean;
  // modalVisible?: Boolean;
}

enum TABLEPARAMS {
  '车辆类型' = 'CAR_TYPE',
  '风控等级' = 'RISK_LEVEL',
  '还款期限' = 'REPAYMENT_TERM',
  '固定比例（n）' = 'BY_FIXED_RATION',
  '区间比例（n）' = 'BY_RANGE_RATION',
  '区间值（n）' = 'RANGE_AMOUNT',
  '固定金额（n）' = 'FIXED_AMOUNT',
}

const EditTable: React.FC<EditTableProps> = ({
  // value,
  initialDataSource = [],
  onChange,
  priceTableParams, // 影响分离定价表table展示的参数
  repaymentTerm = [],
  disabled,
  // initialRepaymentTerm,
  // modalVisible,
}) => {
  // console.log(value);
  // 获取差值数组
  const getArrDifference = (arr1: any, arr2: any) => {
    return arr1.concat(arr2).filter((v, i, arr) => {
      return arr.indexOf(v) === arr.lastIndexOf(v);
    });
  };
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() =>
    initialDataSource?.map((item) => item.id),
  );
  const [dataSource, setDataSource] = useState<DataSourceType[]>(() => initialDataSource);
  // 调用formItem传的value
  const triggerChange = (changeValue) => {
    onChange?.(changeValue);
  };
  // createTableDataNew,新增编辑项
  const getNewTableData = (
    priceTableParamsC: string | string[] | undefined,
    repaymentTermC?: any[],
  ) => {
    const carTypeNum = priceTableParamsC?.includes('CAR_TYPE') ? CAR_TYPE.length : 1;
    const rankNum = priceTableParamsC?.includes('RISK_LEVEL') ? RANK.length : 1;
    const repayTermNum = priceTableParamsC?.includes('REPAYMENT_TERM')
      ? repaymentTermC?.length || 1
      : 1; // 还款期限
    //
    return Array.from({ length: repayTermNum * rankNum * carTypeNum }).map((_, index) => {
      // priceTableParams包含的字段就展示
      // 选了车辆类型，就有carType
      const carType = priceTableParamsC?.includes('CAR_TYPE')
        ? { carType: CAR_TYPE[Math.floor(index / (rankNum * repayTermNum))] }
        : {};
      // 还款期限
      const instalment = priceTableParamsC?.includes('REPAYMENT_TERM')
        ? { instalment: repaymentTermC?.[Math.floor(index / rankNum)] || '' }
        : {};
      // 风控等级
      const auditResultEnum = priceTableParamsC?.includes('RISK_LEVEL')
        ? { auditResultEnum: RANK[Math.floor(index % rankNum)] || '' }
        : {};
      return {
        amount: '', // 固定金额
        proportion: '', // 固定比例
        regionProportion: {}, // 区间比例
        regionAmount: {}, // 区间金额
        id: Date.now() + index,
        ...carType,
        ...instalment,
        ...auditResultEnum,
      };
    });
  };

  useEffect(() => {
    // defaultDate
    let newInitialTempData: any = [];
    // 初始化值不存在,设置默认表格，或者当是表格项包含了还款期限，且分期数不存在，则设置默认表格
    // console.log(
    //   priceTableParams?.includes('REPAYMENT_TERM') && !repaymentTerm?.length,
    //   repaymentTerm?.length,
    // );
    if (
      !initialDataSource?.length ||
      (priceTableParams?.includes('REPAYMENT_TERM') && !repaymentTerm?.length)
    ) {
      newInitialTempData = getNewTableData(
        priceTableParams,
        repaymentTerm || [], // 如果不存在，则用初始化的
      );
    } else {
      // 不是首次编辑,之前存过草稿了，新添加的还款期限和旧得对比
      const preTableRepayment = dataSource.map((item) => {
        return item?.instalment;
      });
      const repaymentTermPrev = [...new Set(preTableRepayment)];

      // const repaymentTermTemp = repaymentTerm?.length ? repaymentTerm : initialRepaymentTerm;
      // console.log(
      //   repaymentTermPrev,
      //   '----',
      //   repaymentTerm,
      //   initialRepaymentTerm,
      //   repaymentTermTemp,
      //   dataSource,
      // );
      // 处理二次编辑分期以及分期联动, 且table项包含了还款期限
      if (
        !isEqual(repaymentTerm, repaymentTermPrev || []) &&
        priceTableParams?.includes('REPAYMENT_TERM')
      ) {
        const leftDataArr: any[] = [];
        const rightDataArr: any[] = [];
        // 找出不同的数组
        getArrDifference(repaymentTerm, repaymentTermPrev)?.forEach((item) => {
          // 原来的包含了差异项，删除上一次数据中的
          if (repaymentTermPrev?.includes(item)) {
            leftDataArr.push(item);
          } else {
            // 新的分期数涵盖了差异项，新增一条数据
            rightDataArr.push(item);
          }
        });

        const oldReplaceData = dataSource?.filter((item) => {
          return !leftDataArr.includes(item.instalment);
        });

        const newDataTemp =
          (rightDataArr?.length && getNewTableData(priceTableParams, rightDataArr)) || [];
        // console.log(oldReplaceData, newDataTemp, leftDataArr, rightDataArr, '我打印下', dataSource);
        newInitialTempData = [...oldReplaceData, ...newDataTemp].map((item, index) => {
          return { ...item, id: Date.now() + index };
        });
      } else {
        // console.log(dataSource, 'dataSource');
        newInitialTempData = (dataSource || initialDataSource)?.map((item, index) => {
          return { ...item, id: Date.now() + index };
        });
      }
    }
    // console.log(newInitialTempData, 'dddd', initialDataSource);
    setDataSource(newInitialTempData || []);
    // 设置可编辑的项
    setEditableRowKeys(() => newInitialTempData.map((item: any) => item.id));
  }, [priceTableParams, repaymentTerm]);

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '车辆类型',
      dataIndex: 'carType',
      valueType: 'select',
      valueEnum: {
        NEW_CAR: '新车',
        OLD_CAR: '二手车',
      },
      editable: false,
      render: (value1, row, index) => {
        const repayLength = priceTableParams?.includes('REPAYMENT_TERM')
          ? repaymentTerm?.length
          : 1;
        const obj = {
          children: value1,
          props: {
            rowSpan:
              index % (RANK.length * (repayLength || 1)) ? 0 : RANK.length * (repayLength || 1), // 跨行展示:风控等级*还款期限的个数=跨行数,前提还款期限得存在
          },
        };
        return obj;
      },
    },
    {
      title: '还款期限',
      dataIndex: 'instalment',
      editable: false,
      render: (value1, row, index) => {
        const obj = {
          children: value1, // 跨行展示: 风控等级
          props: { rowSpan: index % RANK.length ? 0 : RANK.length },
        };
        return obj;
      },
    },
    {
      title: '风控等级',
      key: 'state',
      dataIndex: 'auditResultEnum',
      editable: false,
      valueType: 'select',
      valueEnum: {
        RANKA: 'A',
        RANKB: 'B',
        RANKC: 'C',
      },
    },
    {
      title: '固定比例（n）',
      dataIndex: 'proportion', // 利息
      formItemProps: {
        rules: [
          {
            required: true,
            // whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      renderFormItem: () => <InputNumber width="150px" min={0} disabled={disabled} />,
    },
    {
      title: '固定金额（n）',
      dataIndex: 'amount',
      formItemProps: {
        rules: [
          {
            required: true,
            // whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      renderFormItem: () => <InputNumber width="150px" min={0} precision={2} disabled={disabled} />,
    },
    {
      title: '区间值（n）',
      dataIndex: 'regionAmount',
      width: '60%',
      formItemProps: {
        rules: [
          {
            required: true,
            // whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      renderFormItem: () => (
        <RegionInput
          minValueReplace="amountMin"
          maxValueReplace="amountMax"
          disabledForm={disabled}
          precision={2}
        />
      ),
    },
    {
      title: '区间比例（n）',
      dataIndex: 'regionProportion',
      width: '60%',
      formItemProps: {
        rules: [
          {
            required: true,
            // whitespace: true,
            message: '此项是必填项',
          },
        ],
      },
      renderFormItem: () => (
        <RegionInput
          minValueReplace="minProportionNumberValue"
          maxValueReplace="maxProportionNumberValue"
          disabledForm={disabled}
          // precision={2}
        />
      ),
    },
  ];
  // 动态展示列表项
  const filterColumns = columns.filter((item: ProColumns) => {
    return priceTableParams?.includes(TABLEPARAMS[item?.title as string]);
  });

  return (
    <>
      <EditableProTable<DataSourceType>
        columns={filterColumns}
        rowKey="id"
        value={dataSource || []}
        onChange={setDataSource}
        recordCreatorProps={false}
        editable={{
          type: 'multiple',
          editableKeys,
          onValuesChange: (record, recordList) => {
            setDataSource(recordList);
            triggerChange(recordList);
          },
          onChange: setEditableRowKeys,
        }}
      />
    </>
  );
};
export default React.memo(EditTable);
