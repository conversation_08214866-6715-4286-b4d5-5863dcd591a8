/*
 * @Author: your name
 * @Date: 2021-04-15 18:24:14
 * @LastEditTime: 2024-10-17 17:16:58
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/RepayInfo.tsx
 */
import { Link, useAccess, useRequest } from '@umijs/max';
import { Button, Col, Row } from 'antd';
import React, { useState } from 'react';
// import ModalTable from '@/components/ModalTable';
import { RepayInfoCom } from '@/components/ReleaseCom';
import { ADVANCED_STATUS, REPAY_NODE_STATUS } from '@/enums';
import globalStyle from '@/global.less';
import { exportRepayDetail, getRepayInfo, getRepayRegist } from '@/services/global';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { downLoadExcel, getUuid, isChannelStoreUser, isExternalNetwork } from '@/utils/utils';
import styleLease from '../index.less';
import { getRecord } from '../service';

//复用cash
import { COST_TYPE, mapStatusZh } from '@/pages/BusinessCashMng/const';
import { getRemissionItem } from '@/pages/BusinessCashMng/service';
import type { ProColumns } from '@ant-design/pro-table';
import BigNumber from 'bignumber.js';
import type { RepayItem } from '../data';
import AddCashAndLeaseOfflineRepay from './AddCashAndLeaseOfflineRepay';

interface RepayInfoProps {
  orderNo: string;
  curId?: string;
  productCode?: string;
  accountNumber?: string;
  accountName?: string;
}

const commonAdvanceFields = {
  // 垫付信息
  advanceFirstTime: '首次发起垫付时间',
  advanceSucceedTime: '垫付完成时间',
  advanceAmount: '垫付金额',
};

const mapStatus = {
  '1': {
    // 待还款
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    remainingAmountDue: '剩余应还总额',
    remainingPrincipal: '剩余应还本金',
    remainingInterest: '剩余应还利息',
    ...commonAdvanceFields,
  },
  '2': {
    // 提前结清
    actualRepayTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    advanceLiquidatedDamages: '提前结清违约金',
    ...commonAdvanceFields,
  },
  '3': {
    // 结清
    // actualRepayTime: '实际结清时间',
    // returnedAmountDue: '已还总额',
    // returnedPrincipal: '已还本金',
    // returnedInterest: '已还利息',
    // preSettleCost: '提前结清违约金',
    // returnedCost: '已还费用',
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    ...commonAdvanceFields,
  },
  '4': {
    // 逾期
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    repayOverdueInterest: '已还逾期罚息',
    repayOverdueLatePaymentFee: '已还逾期滞纳金',
    remainingAmountDue: '剩余应还总额',
    remainingPrincipal: '剩余应还本金',
    remainingInterest: '剩余应还利息',
    remainingOverdueInterest: '剩余应还逾期罚息',
    remainingOverdueLatePaymentFee: '剩余应还逾期滞纳金',
    ...commonAdvanceFields,
  },
  '5': {
    // 逾期结清
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    repayOverdueInterest: '已还逾期罚息',
    repayOverdueLatePaymentFee: '已还逾期滞纳金',
    ...commonAdvanceFields,
  },
  '6': {
    // 坏账
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    repayOverdueInterest: '已还逾期罚息',
    repayOverdueLatePaymentFee: '已还逾期滞纳金',
    ...commonAdvanceFields,
  },
  '10': {
    //  正常还款
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    ...commonAdvanceFields,
  },
  '8': {
    //  单期代偿
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    ...commonAdvanceFields,
  },
  //多期代偿
  '9': {
    actualRepayTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    ...commonAdvanceFields,
  },
};
const expandLine = (record: RepayItem) => {
  return (
    <Row>
      {Object.keys(mapStatus[record?.status]).map((item) => {
        return (
          <Col span={6} offset={2} key={item}>
            <div className={globalStyle.lineHeight40}>
              <span>{mapStatus[record?.status][item]}:</span>
              <span className={globalStyle.ml20}>
                {/* 易人行垫付标识为是，展示垫付信息，否则展示‘-’ */}
                {Object.keys(commonAdvanceFields).includes(item) &&
                ![1, 2].includes(record.advancedStatus)
                  ? '-'
                  : record[item] ?? '-'}
              </span>
            </div>
          </Col>
        );
      })}
    </Row>
  );
};

const RepayInfo: React.FC<RepayInfoProps> = ({
  accountNumber,
  accountName,
  productCode,
  orderNo,
}) => {
  const access = useAccess();
  const { data } = useRequest(
    () => {
      return getRepayInfo(orderNo).then((res) => {
        return res;
      });
    },
    {
      onSuccess: () => {
        // if (props.curId) {
        //   window.location.hash = props.curId;
        // }
      },
    },
  );
  // const { data } = useRequest(
  //   () => {
  //     return getOfflineRepayInfo(orderNo).then(res=>{
  //       console.log('res?.data?.repayInfoList', res?.data?.repayInfoList);

  //       return {
  //         data: res?.data?.repayInfoList
  //       }
  //     });
  //   },
  //   {
  //     onSuccess: () => {
  //       // if (props.curId) {
  //       //   window.location.hash = props.curId;
  //       // }
  //     },
  //   },
  // );
  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const [dataRepayRegist, setDataRepayRegist] = useState<[]>([]);
  const [loadingExport, setExportLoading] = useState<boolean>(false);
  const [attachList, setAttachList] = useState<{ netWorkPath: string; name: string }[]>([]);
  const [remissionItem, setRemissionItem] = useState<
    { costType: string; remissionAmount: number }[]
  >([]);
  const getRepayList = (repayPlanNo: string) => {
    getRepayRegist(repayPlanNo).then((res) => {
      console.log('res', res);
      setDataRepayRegist(res.data);
    });
  };
  // console.log('dataRepayRegist', dataRepayRegist);
  //设置附件
  const getRecordFunc = (repayPlanNo: string) => {
    getRecord({ repayPlanNo }).then((res) => {
      // console.log(res?.data?.map((item) => item.attach)[0].flat(Infinity));
      setAttachList(res?.data?.map((item: { attach: any }) => item.attach)?.flat(Infinity));
    });
  };

  //减免项
  const getRemissionFunc = (orderNoArg: string, repayPlanNo: string) => {
    getRemissionItem(orderNoArg, repayPlanNo).then((res) => {
      console.log('res.data', res.data);
      setRemissionItem(res.data);
    });
  };

  const columns: ProColumns<RepayItem>[] = [
    {
      title: '期数',
      dataIndex: 'termDetail',
    },
    {
      title: '还款状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, row) => {
        return mapStatusZh[row.status];
      },
    },
    {
      title: '资方还款状态',
      dataIndex: 'channelStatus',
      key: 'channelStatus',
      render: (_, row) => {
        return mapStatusZh[row.channelStatus] || '-';
      },
      hidden: isChannelStoreUser(access),
    },
    {
      title: '易人行逾期垫付',
      dataIndex: 'advancedStatus',
      key: 'advancedStatus',
      hidden: isChannelStoreUser(access),
      render: (_, row) => {
        return ADVANCED_STATUS.get(row.advancedStatus) || '-';
      },
    },
    { title: '应还日期', dataIndex: 'repayTime', key: 'repayTime' },
    { title: '应还总额', dataIndex: 'shouldAmountDue', key: 'shouldAmountDue' },
    { title: '应还本金', dataIndex: 'shouldPrincipal', key: 'shouldPrincipal' },
    { title: '应还利息', dataIndex: 'shouldInterest', key: 'shouldInterest' },
    // {
    //   title: '提前结清违约金',
    //   dataIndex: 'advanceLiquidatedDamages',
    //   key: 'advanceLiquidatedDamages',
    // },
    // { title: '逾期天数', dataIndex: 'overdueDay', key: 'overdueDay' },
    { title: '逾期罚息', dataIndex: 'overdueInterest', key: 'overdueInterest' },
    // { title: '逾期滞纳金', dataIndex: 'overdueLatePaymentFee', key: 'overdueLatePaymentFee' },
    {
      title: '其他费用',
      dataIndex: 'otherCost',
      key: 'otherCost',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      key: 'repayPlanNo',
      render: (_: any, record: any, dataIndex: number) => (
        <>
          {/* 查看逾期详情需要有权限才能访问（门店渠道不可访问） */}
          {(record.status === 4 || record.status === 5) &&
            access.hasAccess('module_view_repayinfo_businessMng_leaseList') &&
            record?.overdueCaseNo && (
              <Link
                to={{
                  pathname: `/businessMng/postLoanMng/collection-detail`,
                  search: `?overdueCaseNo=${record?.overdueCaseNo}`,
                }}
                state={{
                  curList: [],
                  curItemIndex: dataIndex,
                }}
                style={{ display: 'block' }}
              >
                查看逾期详情
              </Link>
            )}
          <a
            onClick={() => {
              handleModalVisible(true);
              getRepayList(record?.repayPlanNo);
              getRecordFunc(record?.repayPlanNo);
              getRemissionFunc(orderNo, record?.repayPlanNo);
            }}
            style={{ display: 'block' }}
          >
            查看还款登记
          </a>
        </>
      ),
    },
  ];

  // const dataRepayRegist: any[] = [];
  const columnsRepayRegist = [
    {
      title: '还款流水号',
      dataIndex: 'recordingNo',
      key: 'recordingNo',
    },
    {
      title: '实际还款总金额',
      dataIndex: 'actualRepaymentAmount',
      key: 'actualRepaymentAmount',
    },
    {
      title: '实际还款本金',
      dataIndex: 'principal',
      key: 'principal',
    },
    {
      title: '实际还款利息',
      dataIndex: 'interest',
      key: 'interest',
    },
    {
      title: '实际还款罚息',
      dataIndex: 'penaltyInterest',
      key: 'penaltyInterest',
    },
    {
      title: '实际还款费用',
      dataIndex: 'cost',
      key: 'cost',
    },
    {
      title: '三方还款流水号',
      dataIndex: 'bankSerialNo',
      key: 'bankSerialNo',
    },
    {
      title: '还款渠道',
      dataIndex: 'payChannelName',
      key: 'payChannelName',
    },
    {
      title: '还款方式',
      dataIndex: 'repayType',
      key: 'repayType',
    },
    {
      title: '还款时间',
      dataIndex: 'repayTime',
      key: 'repayTime',
    },
    {
      title: '收款账号',
      dataIndex: 'merchantId',
      key: 'merchantId',
    },
    {
      title: '资方实收',
      dataIndex: 'capitalAmount',
      key: 'capitalAmount',
      render(_, row) {
        // 值为0时展示‘-’
        return +row.capitalAmount ? row.capitalAmount : '-';
      },
    },
    {
      title: '易人行逾期垫付',
      dataIndex: 'advancedStatus',
      key: 'advancedStatus',
      hidden: isChannelStoreUser(access),
      render: (_, row) => {
        return ADVANCED_STATUS.get(row.advancedStatus) || '-';
      },
    },
    {
      title: '还款节点',
      dataIndex: 'repayNode',
      key: 'repayNode',
      render: (_, row) => {
        return REPAY_NODE_STATUS[row?.repayNode || 0];
      },
    },
  ];
  const getExport = () => {
    setExportLoading(true);
    exportRepayDetail(orderNo)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  return (
    <RepayInfoCom
      carTitle="还款信息"
      expandable={expandLine}
      dataTable={data}
      rowKey={getUuid()}
      onCancel={() => {
        handleModalVisible(false);
      }}
      extra={
        <>
          {isExternalNetwork() && !isChannelStoreUser(access) ? null : (
            <>
              {data?.length ? (
                <>
                  {access.hasAccess('module_view_repayinfo_businessMng_leaseList') && (
                    <Button
                      onClick={() =>
                        optimizationModalWrapper(AddCashAndLeaseOfflineRepay)({
                          productCode,
                          channelCode: data[0].funderChannelCode,
                          repurchaseStatus: data[0].repurchaseStatus,
                          orderNo,
                          accountNumber,
                          accountName,
                        })
                      }
                      className={styleLease.buttonStyle}
                    >
                      线下还款
                    </Button>
                  )}
                  <Button loading={loadingExport} onClick={getExport} type="primary">
                    导出
                  </Button>
                </>
              ) : (
                ''
              )}
            </>
          )}
        </>
      }
      // request={() => getRepayRegist(repayPlanNo)}
      columns={columns}
      modalDataTable={dataRepayRegist}
      modalColumns={columnsRepayRegist}
      modalVisible={modalVisible}
      modalTitle="还款登记"
      accountNumber={accountNumber}
      accountName={accountName}
      remissionColumn={[
        {
          title: '减免费项',
          dataIndex: 'costType',
          render: (_: React.ReactNode, record: any) => {
            // console.log(item);
            return COST_TYPE[record?.costType] || '-';
          },
        },
        {
          title: '减免金额/元',
          dataIndex: 'remissionAmount',
          render: (_: React.ReactNode, record: any) => {
            console.log('record', record);
            return new BigNumber(record?.remissionAmount).div(100).toFixed(2) || '-';
          },
        },
        {
          title: '车架号',
          dataIndex: 'vin',
        },
        {
          title: '车牌号',
          dataIndex: 'plateNo',
        },
      ]}
      remissionDataSource={remissionItem}
      repayAttachList={attachList}
    />
  );
};

export default RepayInfo;
