import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';

interface IWalletAccountInfo {
  externalOwnerId: string;
}

export const getWalletAccountInfo = async (params: {
  externalOwnerId: string;
  secondProductCode: '0303' | '0201'; // 0303 车险， 0201 融租
}): Promise<IWalletAccountInfo> => {
  return request(`/bizadmin/insurance/policy/account/query/account/amount`, {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
};
