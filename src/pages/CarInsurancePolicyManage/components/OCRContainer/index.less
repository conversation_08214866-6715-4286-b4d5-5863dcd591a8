& {
  p {
    margin: 0;
  }

  .bulk-upload-content {
    display: flex;
    min-height: 800px;

    // div 实现table
    &-wrapper {
      display: flex;
      width: 100%;
    }

    &-body {
      max-height: 1200px;
      overflow-y: auto;
    }

    // 表头
    &-header {
      display: flex;
      width: 100%;
      height: 48px;
      padding-left: 12px;
      line-height: 48px;
      background-color: #fafaf8;
      border-bottom: 1px solid #f3f3f3;
      border-collapse: separate;

      &:last-child {
        padding-right: 12px;
      }
    }

    &-left {
      width: 35%;
      border-right: 1px solid #d5d5d5;
    }

    &-right {
      display: flex;
      flex-direction: column;
      width: 65%;
      height: 100%;
    }

    // 右侧ocr内容样式
    &-ocr {
      &-container {
        height: inherit;
        padding-left: 12px;
        overflow: hidden;
      }

      &-result {
        // height: calc(100% * 2 / 5);
        padding-top: 24px;
        // margin-bottom: 24px;
      }

      &-pdfquote {
        // 使用 calc 动态计算高度
        // 100% 为容器总高度
        // var(--result-height) 为 result 区域的实际高度
        // 70px 为上下 padding/margin 的总和
        height: calc(100% - var(--result-height) - 70px);
      }

      &-header {
        display: flex;
        justify-content: space-between;
        width: 100%;
        padding-right: 12px;
      }

      &-title {
        display: flex;
        justify-content: flex-start;
        font-weight: 600;
      }

      &-action {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-weight: normal;

        span {
          padding: 0 8px;
          color: #3176fd;
          cursor: pointer;

          &:last-child {
            color: #f5222d;

            &:hover {
              color: #f35a62;
            }
          }

          &:hover {
            color: #6e91d8;
          }
        }
      }
    }

    &-col {
      align-self: center;
      padding: 0 5px;
      overflow: hidden;
      font-weight: 600;
      text-align: left;

      &:nth-child(1) {
        width: calc(5 / 35 * 100%);
        text-align: center;
      }

      &:nth-child(2) {
        width: calc(12 / 35 * 100%);
        word-break: break-all;
      }

      &:nth-child(3) {
        width: calc(18 / 35 * 100%);
      }
    }

    &-col.text-item {
      font-weight: unset;

      &:nth-child(2) {
        color: #3779fd;
      }
    }

    &-row {
      position: relative;
      display: flex;
      width: 100%;
      min-height: 40px;
      padding: 4px 0;
      overflow: hidden;
      border-bottom: 1px solid #f3f3f3;
      cursor: pointer;

      &:hover:not(.selected) {
        background-color: #f0f0f0;
        transition: background-color 0.2s ease-in;
      }
    }
  }

  .action-status {
    &-success {
      color: #67c23a;
    }

    &-warning {
      color: #faad14;
    }

    &-error {
      color: #f5222d;
    }
  }

  .error-item {
    .ant-descriptions-item-label {
      color: #f5222d;
    }

    .ant-descriptions-item-content {
      color: #f5222d;
    }
  }

  .warning-item {
    .ant-descriptions-item-label {
      color: #faad14;
    }

    .ant-descriptions-item-content {
      color: #faad14;
    }
  }

  .selected {
    background-color: #e2ecfe;
    transition: background-color 0.2s ease-in;

    &::after {
      position: absolute;
      top: 0;
      right: 0;
      width: 5px;
      height: 100%;
      background-color: #035aff;
      transition: background-color 0.2s ease-in;
      content: '';
    }
  }
}

.ant-form-item .ant-form-item-label .ant-form-item-required {
  color: #bebebe;
}
