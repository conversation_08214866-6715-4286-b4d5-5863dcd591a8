import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { editPayBlackList } from '@/pages/PayBuyBack/service';
import type { EditPayBlackListParams } from '@/pages/PayBuyBack/types';
import { Button, message, Modal } from 'antd';
import React from 'react';

type AddPayBlackListButtonProps = {
  list: any[];
};

enum BLACK_LIST_TYPE {
  USER = '1',
  CHANNEL = '2',
}
const AddPayBlackListButton = (props: AddPayBlackListButtonProps) => {
  const { list } = props;

  const doAddBlackList = async (params: EditPayBlackListParams) => {
    await editPayBlackList(params);
    message.success('添加成功');
  };
  const handleAdd = () => {
    if (!list || !list.length) {
      message.error('请选择要加入黑名单的用户');
      return;
    }
    if (list.length > 5) {
      message.warning('单次添加不能超过5个');
      return;
    }
    const filterList = list.filter(
      (item: any) =>
        item.productCode?.substr(0, 4) === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
    );
    if (!filterList.length) {
      message.error('仅支持添加小圆车融黑名单，请重新选择');
      return;
    }
    const userIds = filterList.map((item: any) => {
      return { userId: item.accountNumber };
    });
    const params = {
      type: BLACK_LIST_TYPE.USER,
      productType: PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE,
      productCode: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
      item: userIds,
    };
    Modal.confirm({
      title: '是否将以下用户加入垫付黑名单?',
      content: (
        <div>
          {filterList.map((item: any) => {
            return (
              <div key={item.userId}>
                <span style={{ marginRight: 4 }}>{item.accountName}</span>
                <span style={{ color: '#666' }}>({item.accountNumber})</span>
              </div>
            );
          })}
        </div>
      ),
      onOk: () => {
        return doAddBlackList(params);
      },
    });
  };
  return (
    <Button type="primary" onClick={handleAdd}>
      加入垫付黑名单
    </Button>
  );
};

export default AddPayBlackListButton;
