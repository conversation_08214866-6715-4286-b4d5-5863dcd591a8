/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2021-07-15 11:46:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/PassModel.ts
 */
import {
  ModalForm,
  // ProFormUploadButton,
  ProFormDigit,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { history, useModel } from '@umijs/max';
import { message } from 'antd';
import React from 'react';
// import { getBaseUrl } from '@/utils/utils';
import type { ApplyReliefParams } from '../data';
import { applyRelief } from '../service';
import { UploadCom } from './index';

export type AddReliefModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  // values: CallPayForm|{};
  onVisibleChange: any;
  overDueAmount: number;
};

const AddReliefModal: React.FC<AddReliefModalProps> = (props) => {
  const { overdueId } = history.location.query;
  const { urgePersonList: userList } = useModel('userList');
  // const { data } = useRequest(() => {
  //   return getUserList();
  // });
  // const options =
  //   data?.map((item: { id: number; username: string }) => {
  //     return { value: item.id, label: item.username };
  //   }) || [];
  // const base_url = getBaseUrl();
  // const upload_url = `${base_url}/loan/audit/uploadCreditFile`;

  return (
    <>
      <ModalForm
        title="申请减免"
        width={600}
        layout="horizontal"
        visible={props.modalVisible}
        onVisibleChange={props.onVisibleChange}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
        }}
        onFinish={async (value) => {
          const fileList = [];
          const length = value?.fileList?.length || 0;
          for (let i = 0; i < length; i += 1) {
            const item = value.fileList[i];
            if (item.response) {
              const url = item.response.data;
              if (url) {
                fileList.push({ fileUrl: url, fileName: item.name });
              }
            }
          }
          try {
            await applyRelief({
              overdueId,
              ...value,
              fileList,
            } as ApplyReliefParams);
            message.success('添加成功');
            props.onOk();
            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <ProFormSelect
          rules={[{ required: true }]}
          labelCol={{ span: 5 }}
          request={async () => userList}
          placeholder="请输入催收员"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            // filterOption: (input: string, option: { label: string }) =>
            //   option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
          }}
          width="md"
          name="urgePerson"
          label="催收员"
        />
        <ProFormDigit
          name="amount"
          width="md"
          labelCol={{ span: 5 }}
          label="减免金额(元)"
          placeholder="请输入减免金额"
          fieldProps={{ min: 0, precision: 2 }}
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 有坑，同时出现很多个error,待优化
                if (/^\d+(\.\d+)?$/.test(val) && val && val > props.overDueAmount) {
                  // callBack('减免金额不能大于逾期金额');
                  return Promise.reject(new Error('减免金额不能大于逾期金额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  // callBack();
                  return Promise.reject(new Error('请输入数字'));
                }
                // callBack();
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormTextArea
          labelCol={{ span: 5 }}
          placeholder="请输入备注"
          width="md"
          fieldProps={{ maxLength: 500 }}
          label="备注"
          name="urgeRecentlyMsg"
        />
        <UploadCom span={5} />
      </ModalForm>
    </>
  );
};

export default AddReliefModal;
