import type { SECONDARY_CLASSIFICATION } from '@/enums';
import type { IgetCarRepaymentRes } from '@/pages/AfterLoanCarInsurance/types';
import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';
import axios from 'axios';
import type {
  IcarChannelItem,
  IcarInsuranceItem,
  Icity,
  IdetailData,
  IexportContractRes,
  IgetCarInsuranceListParams,
  ImonthlyCalcuParams,
  ImonthlyCalcuRes,
  IOrderActionParams,
} from '../type';
import { LEVEL } from '../type';
import { getHost } from '../utils';
const headers = {
  'hll-appid': 'bme-finance-bizadmin-svc',
};
/**
 * 超级管理员 渠道用户 运营用户 可以查看车险订单列表
 * 超级管理员 和 运营用户可以任意选择渠道信息, 产品信息 , 企业信息, 保费公司
 * 渠道用户 只能选择关联的
 */

// 操作订单 包括保存草稿 新增 编辑 提交审核 啊等
export async function saveCarInsurance(
  data: any,
  options?: { skipGlobalErrorTip?: boolean },
): Promise<string> {
  // const {userId,} = header
  const result = await request(`/bizadmin/insurance/policy/order/order/status/change/behavior`, {
    method: 'POST',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    ...options,
  });
  return result?.data;
}

interface IgetCarInsuranceListRes {
  data: IcarInsuranceItem[];
  current: number;
  pageSize: number;
  total: number;
}
/**
 * 获取车险订单列表
 */
export async function getCarInsuranceList(
  params: IgetCarInsuranceListParams,
): Promise<IgetCarInsuranceListRes> {
  return request(`/bizadmin/insurance/policy/order/queryOrderList`, {
    method: 'POST',
    headers,
    data: params,
    ifTrimParams: true,
  });
}

/**
 * 获取渠道列表信息
 * @returns {IcarChannelItem[]}
 */
export async function getChannelInfo({
  channelCode,
  channelLevel,
}: {
  channelLevel?: number;
  channelCode?: string;
  parentChannelCode?: string;
} = {}): Promise<IcarChannelItem[]> {
  let params = {};
  //channelLevel存在则为渠道用户,否则默认为运营  运营角色需要load出所有渠道数据（一级，二级）
  if (channelLevel && channelLevel === LEVEL.FIRST_CHANNEL) {
    params = {
      channelCode,
      levelList: [LEVEL.FIRST_CHANNEL, LEVEL.SECOND_CHANNEL],
    };
    //因为二级是禁用的，同时没有下级，该channelList返回[],默认选中的功能给下拉设置channelCode,会翻译失败，所以传父亲code查出下拉，让select选中
  } else if (channelLevel && channelLevel === LEVEL.SECOND_CHANNEL) {
    params = { channelCode, levelList: [LEVEL.SECOND_CHANNEL] };
  } else {
    params = { levelList: [LEVEL.FIRST_CHANNEL, LEVEL.SECOND_CHANNEL] };
  }
  const data = await request(`/bizadmin/channel/list`, {
    method: 'GET',
    params: {
      pageNumber: 1,
      pageSize: 10000,
      ...params,
    },
    ifTrimParams: true,
    headers,
  });
  return data?.data;
}

/**
 * 获取详情信息
 */
export async function getCarInsuranceDetail(orderNo: string, reuse?: true): Promise<IdetailData> {
  const data = await request(`/bizadmin/insurance/policy/order/queryOrderDetail`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    params: {
      orderNo,
      reuse,
    },
  });
  return data?.data;
}

// 下载上传车辆的excel模版
export async function downLoadCarInfoTemplate() {
  return request(`/bizadmin/insurance/policy/order/template`, {
    responseType: 'blob',
    getResponse: true,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

/**
 * 批量上传影像文件
 */
export async function uploadImg(formData: any) {
  return request(`/base/oss/common/uploadfiles`, {
    method: 'POST',
    headers: {
      // 不能写请求头
      // "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryyOLalq19H2JhSrFH"
    },
    requestType: 'form',
    data: formData,
  });
}

/**
 * 导出excel
 */
// export async function exportCarInsurance(params: IgetCarInsuranceListParams) {
//   return request(`/bizadmin/insurance/policy/order/list/excel`, {
//     method: 'GET',
//     params,
//     headers: {
//       'hll-appid': 'bme-finance-bizadmin-svc',
//     },
//     responseType: 'blob',
//     getResponse: true,
//   });
// }
export async function exportCarInsurance(params: IgetCarInsuranceListParams) {
  return request(`/bizadmin/insurance/policy/order/list/excel/async`, {
    method: 'GET',
    params,
    headers: bizadminHeader,
    ifTrimParams: true,
  });
}

/**
 * 月供测算
 */
export async function monthlyCalcu(params: ImonthlyCalcuParams): Promise<ImonthlyCalcuRes> {
  const data = await request(`/bizadmin/insurance/policy/order/preview/repay/plan`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data?.data;
}

/**
 * 还款计划
 */
export async function getCarRepayment(orderNo: string): Promise<IgetCarRepaymentRes> {
  const data = await request(`/repayment/cms/bill/repay/plan/${orderNo}`, {
    method: 'GET',
  });
  return data?.data || {};
}

/**
 * 更新车辆检查状态
 */
export async function updateCarInspected(params: {
  orderNo: string;
  vinList: string[];
  type: 1 | 2; // 1: 检查车辆信息 2: 检查车辆进件信息
}): Promise<IgetCarRepaymentRes> {
  return await request(`/bizadmin/insurance/policy/order/checkCar`, {
    method: 'POST',
    data: params,
    headers,
  });
}

/**
 * 导出合同
 */
export async function exportContract(
  params: IgetCarInsuranceListParams,
): Promise<IexportContractRes> {
  const data = await request(`/bizadmin/insurance/policy/order/contract/export`, {
    method: 'POST',
    data: params,
    headers,
    ifTrimParams: true,
  });
  return data?.data || {};
}

// 产品名称
// secondaryClassifcation二级分类 不传查全部
export async function getProductNameEnum(
  secondaryClassification?: keyof typeof SECONDARY_CLASSIFICATION,
) {
  // return request('/repayment/query/productName').then((res) => res.data);
  return request('/bizadmin/product/getProductEnum', {
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
    params: { secondaryClassification },
  }).then((res) => {
    const allItems =
      res?.data?.map((item: { productCode: string; productName: string }) => {
        return { value: item.productCode, label: item.productName };
      }) || [];
    return allItems;
  });
}

// 获取省份城市
export function getCityList(): Promise<Icity> {
  const host = getHost();
  return axios
    .get(`${host}/lbs-map/geo/region/district`, {
      params: {
        adcode: 100000, // 100000 代表中国
        subdistrict: 2, // 子集的级别
      },
    })
    .then((data) => {
      return data?.data?.data?.[0]?.children || [];
    });
}

// 放款回单
export async function getLoanReceipt(orderNo: string) {
  return request(`/bizadmin/insurance/policy/order/order/loanReceipt`, {
    // method: 'GET',
    params: { orderNo },
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    responseType: 'blob',
    getResponse: true,
  });
}

/**
 * ocr识别
 */
export async function vehicleOcrInfo(params: any) {
  return request(`/bizadmin/base/ocr/vehicleIdentifyOcrInfo`, {
    method: 'GET',
    params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

// 查询企业/个人是否有预期状态订单，返回最大逾期天数
export async function getOverdueCaseInfo(params: {
  idNo: string | number;
  type: 0 | 1; // 0 -> 企业，1 -> 个人
}) {
  return request(`/bizadmin/overdue/hasOverdueCase`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 轮询检查获取订单最新状态，用以提醒运营及渠道用户可不提交
export async function checkCarPolicyOrderStatus(params: { orderNo: string; status?: number }) {
  return request(`/bizadmin/insurance/policy/order/checkCarPolicyOrderStatus`, {
    method: 'GET',
    params,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 获取运营及运营管理列表
export async function getEmployeeList(params?: any) {
  return request(`/bizadmin/employee/queryEmployeeListByPage`, {
    method: 'POST',
    data: {
      ...params,
      // 只查运营
      current: 1,
      pageSize: 1000,
      employeeType: 9,
      roleCodes: ['financialOperation', 'financialOperationSupervisor'],
    },
    headers: {
      ...bizadminHeader,
    },
  });
}

// 重新分派订单
export async function reassignOrder(params: IOrderActionParams) {
  return request(`/bizadmin/insurance/policy/order/order/status/change/assign`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 领取订单
export async function receiveOrder(params: IOrderActionParams) {
  return request(`/bizadmin/insurance/policy/order/order/status/change/take`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 放弃领取订单
export async function abandonOrder(params: IOrderActionParams) {
  return request(`/bizadmin/insurance/policy/order/order/status/change/giveup`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 保存后补资料
interface IExtendData {
  orderNo: string;
  afterInformationList: {
    fileName?: string;
    shortOssUrl: string;
    netWorkPath: string;
  }[];
}
export async function saveExtend(data: IExtendData) {
  return request(`/bizadmin/insurance/policy/order/save/extend`, {
    method: 'POST',
    data,
    headers: {
      ...bizadminHeader,
    },
  });
}

export async function cancelClaim(data: { orderNo: string }) {
  return request(`/bizadmin/insurance/policy/order/claim/cancel`, {
    method: 'POST',
    data,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 操作批量OCR任务
export async function updateBulkOcrStatus(params: {
  businessNo: string;
  event: string; // 操作事件
}) {
  return request(`/bizadmin/vehicle/batch/ocr/task/operate`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 新增/更新批量OCR任务
export async function submitBulkUpload(params: {
  businessNo?: string;
  orderNo: string;
  groupFlag: 1 | 2; // 1: 团单 2: 非团单
  event: string; // 操作事件
  vehicleBatchOcrTaskExtendList: any[]; // 扩展字段，存放所有步骤的文件列表，文件以fileType区分
}) {
  return request(`/bizadmin/vehicle/batch/ocr/task/submit`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 查询批量OCR任务
export async function checkMatchProcess(params: { orderNo?: string; businessNo?: string }) {
  return request(`/bizadmin/vehicle/batch/ocr/task/query`, {
    method: 'POST',
    data: params,
    headers: {
      ...bizadminHeader,
    },
  });
}
