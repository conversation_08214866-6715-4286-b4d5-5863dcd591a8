/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2023-09-15 15:06:35
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseIncoming/index.tsx
 */
import HeaderTab from '@/components/HeaderTab/index';
import { PageContainer } from '@ant-design/pro-layout';
import React, { useEffect, useState } from 'react';
// import PageContainer from '@/components/PageContainer/PageContainer'
import { MenuUnfoldOutlined } from '@ant-design/icons';
import { Drawer, message, Switch } from 'antd';
import { KeepAlive } from 'react-activation';
import HeavyQuotaPromotionList from './components/HeavyQuotaPromoteList';
import RecordTable from './components/table';
import { getSwitch, onOrOff } from './service';

const EnterpriseMngList: React.FC = () => {
  const [visibleDrawer, setVisibleDrawer] = useState<boolean>(false);
  const [checked, setChecked] = useState<boolean>(true);
  const [openIncrease, setIncreaseChecked] = useState<boolean>(true);
  useEffect(() => {
    getSwitch()
      .then((res) => {
        if (res?.data === 'ON') {
          setIncreaseChecked(true);
        } else {
          setIncreaseChecked(false);
        }
      })
      .catch(() => {
        setIncreaseChecked(false);
      });
  });
  return (
    <>
      <PageContainer
        extra={
          <>
            <Switch
              defaultChecked
              checked={checked}
              onChange={(v) => {
                setChecked(v);
              }}
              checkedChildren={'切换为提额视图'}
              unCheckedChildren={'切换为进件管理'}
            />
            <MenuUnfoldOutlined
              style={{ color: '#1a87fe', fontSize: '20px' }}
              onClick={() => {
                setVisibleDrawer(!visibleDrawer);
              }}
            />
          </>
        }
      >
        {checked ? <RecordTable /> : <HeavyQuotaPromotionList />}
        <Drawer
          width={250}
          onClose={() => {
            setVisibleDrawer(false);
          }}
          // getContainer={false}
          closable={false}
          open={visibleDrawer}
          // bodyStyle={{ paddingBottom: 80 }}
        >
          <Switch
            checked={openIncrease}
            checkedChildren={'关闭大额提额入口'}
            unCheckedChildren={'打开大额提额入口'}
            onChange={(v) => {
              // setIncreaseChecked(v);
              // if(v)
              // console.log(v);
              onOrOff(v ? 1 : 2).then(() => {
                setIncreaseChecked(v);
                message.success(`${v ? '打开' : '禁用'}成功`);
              });
            }}
          />
        </Drawer>
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'userMng/enterpriseMng/list'}>
      <EnterpriseMngList />
    </KeepAlive>
  </>
);
