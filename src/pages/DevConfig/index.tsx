import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { getSSOAPICookie } from '@/utils/auth';
import { PageContainer } from '@ant-design/pro-layout';
import { setSSOIdentifier } from '@hll/feishu-at';
import { <PERSON><PERSON>, Button, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { KeepAlive } from 'react-activation';
import CardList from './components/CardList';
import type { CardItem } from './data';
import { batchUpdateBizSSOGrayInfo, fetchVanCanary, getBizSSOGrayInfo } from './service';

const DevConfig: React.FC = () => {
  // Van SSO 灰度信息
  const [dataFromVanCanary, setDataFromVanCanary] = useState<CardItem[]>([]);
  //
  const [list, setList] = useState<CardItem[]>([]);
  const [bikeDataMapByCanaryId, setBikeDataMapByCanaryId] = useState<any>({});
  // 从bike拿数据
  const getBikeInfo = async () => {
    return await getBizSSOGrayInfo().then((res) => {
      if (res && res?.data?.list) {
        try {
          const mapByCanaryId: any = {};
          res?.data?.list.forEach((item: CardItem) => {
            const { canaryId } = item;
            mapByCanaryId[canaryId] = item;
          });
          setBikeDataMapByCanaryId(mapByCanaryId);
          console.log('bikeDataMapByCanaryId', bikeDataMapByCanaryId);
        } catch (e) {
          console.error('bikeDataMapByCanaryId', e);
          setBikeDataMapByCanaryId({});
        }
      }
      return res;
    });
  };
  // 获取van canary信息，以van canary为基础渲染cardList， van sso灰度接口依赖sso cookie
  const [vanCanaryErrorMsg, setVanCanaryErrorMsg] = useState('');
  const getVanCanary = async () => {
    // const data: any = {
    //   default_task_id: 516722,
    //   canary: [
    //     {
    //       task_id: 516722,
    //       canary_id: 'z1pi5m6swwdz62gzajxizi6etufu9k',
    //       operator_chain: [
    //         {
    //           type: 'sso',
    //           left: 'sso',
    //           op: 'in',
    //           right: ['frank1.liu'],
    //         },
    //       ],
    //       description: '2024-01-01 xxx需求灰度',
    //     },
    //     {
    //       task_id: 516723,
    //       canary_id: 'z1pi5m6swwdz62gzajxizi6etufu9k1',
    //       operator_chain: [
    //         {
    //           type: 'sso',
    //           left: 'sso',
    //           op: 'in',
    //           right: ['frank1.liu'],
    //         },
    //       ],
    //       description: '',
    //     },
    //   ],
    // };
    setVanCanaryErrorMsg('');
    const { data }: any = await fetchVanCanary({
      ssoCookie: getSSOAPICookie(),
    }).catch(() => {
      setVanCanaryErrorMsg('获取van生产灰度策略失败，请在生产环境中使用此功能');
      return { data: {} };
    });
    setDataFromVanCanary(data.canary || []);
    // 同步最新数据到bike
    return data.canary || [];
  };
  // 前后端灰度信息不一致，提示信息
  const [diffMessage, setDiffMessage] = useState('');
  // 根据van canary信息，过滤出sso灰度信息，并与bike存储的后端api信息合并，渲染cardList
  useEffect(() => {
    setDiffMessage('');
    const bikeFormatData = dataFromVanCanary
      .filter(
        (item: any) =>
          item?.operator_chain?.length &&
          item?.operator_chain.every((v: any) => v.left === 'sso' && v.type === 'sso'),
      )
      .map((item: any, index: number) => {
        const { task_id, canary_id, operator_chain, description } = item;
        const { right } = operator_chain[0];
        const userList = right;
        // const userList = operator_chain.reduce((prev: string[], current: any) => {
        //   if (current.left ==='sso' && current.type ==='sso') {
        //     return [...prev, ...current.right];
        //   }
        //   return prev;
        // }, []);
        const currentBikedata: CardItem = bikeDataMapByCanaryId[canary_id] || {};
        // 判断前后端灰度名单是否一致
        if (
          !currentBikedata.userList ||
          !(
            userList.every((user: string) => currentBikedata.userList.includes(user)) &&
            currentBikedata.userList.every((user: string) => userList.includes(user))
          )
        ) {
          // 前端灰度策略名单与后端灰度名单不一致，需要更新后端灰度名单到bike
          setDiffMessage(`前后端灰度名单不一致，请点击“保存”按钮更新后端灰度名单`);
          console.log(`#${index + 1}前后端灰度名单不一致`, currentBikedata.userList, userList);
        }
        //
        return {
          // 前端灰度策略id（van canary_id）
          vanTaskId: task_id + '',
          userList,
          canaryId: canary_id,
          priority: index,
          description,
          // 对应bike 存储的后端api信息（名单、版本号、灰度请求头等信息）
          id: currentBikedata.id,
          grayHeaderKey: currentBikedata.grayHeaderKey,
          grayHeaderValue: currentBikedata.grayHeaderValue,

          valid: currentBikedata.valid === undefined ? true : currentBikedata.valid,
          createdAt: currentBikedata.createdAt,
          updatedAt: currentBikedata.updatedAt,
        };
      });
    console.log('bikeFormatData', bikeFormatData);
    setList(bikeFormatData);
  }, [dataFromVanCanary, bikeDataMapByCanaryId]);
  // 批量更新van canary、灰度配置信息到bike
  const [batchUpdateGrayInfoLoading, setBatchUpdateGrayInfoLoading] = useState(false);
  const asyncBatchUpdateGrayInfo = async (
    vanCanaryData?: CardItem[],
    needMessage: boolean = false,
  ) => {
    setBatchUpdateGrayInfoLoading(true);
    console.log('list', vanCanaryData || list);
    batchUpdateBizSSOGrayInfo(vanCanaryData || list)
      .then((res) => {
        console.log('asyncBatchUpdateGrayInfo', res);
        if (res.success === true && needMessage) {
          message.success('保存成功');
        }
        getBikeInfo().catch(() => {});
      })
      .catch((e) => {
        console.error('asyncBatchUpdateGrayInfo', e);
        message.error('灰度信息同步失败');
      })
      .finally(() => {
        setBatchUpdateGrayInfoLoading(false);
      });
  };
  //

  const updateGrayHeaderInfoByIndex = async (canaryItem: Partial<CardItem>, index: number) => {
    const { grayHeaderKey, grayHeaderValue, valid } = canaryItem;
    setList((prev) => {
      const newVal = [...prev];
      newVal[index].grayHeaderKey = grayHeaderKey;
      newVal[index].grayHeaderValue = grayHeaderValue;
      newVal[index].valid = valid;
      return newVal;
    });
    console.log('updateGrayHeaderInfoByIndex', list);
  };
  //

  // 初始化
  const [initLoading, setInitLoading] = useState(false);
  const init = async (refresh: boolean = false) => {
    setInitLoading(true);
    let res: boolean = true;
    await getBikeInfo().catch(() => {
      res = false;
    });
    await getVanCanary().catch(() => {
      res = false;
    });
    setInitLoading(false);
    if (refresh && res) {
      message.success('已刷新');
    }
  };
  //
  useEffect(() => {
    try {
      setSSOIdentifier(getSSOAPICookie());
    } catch (e) {
      console.error('setSSOIdentifier', e);
    }
    init();
  }, []);
  return (
    <>
      <PageContainer
        extra={
          vanCanaryErrorMsg === '' ? (
            <>
              <Button onClick={() => init(true)} loading={initLoading}>
                刷新
              </Button>
              <Button
                onClick={() => asyncBatchUpdateGrayInfo(undefined, true)}
                loading={batchUpdateGrayInfoLoading}
                type="primary"
              >
                保存
              </Button>
            </>
          ) : (
            <></>
          )
        }
      >
        {diffMessage && (
          <div className={globalStyle.mb20}>
            <Alert message="提示" description={diffMessage} type="warning" showIcon />
          </div>
        )}
        {vanCanaryErrorMsg && (
          <div className={globalStyle.mb20}>
            <Alert message="提示" description={vanCanaryErrorMsg} type="warning" showIcon />
          </div>
        )}
        {list.length === 0 && vanCanaryErrorMsg === '' && (
          <div className={globalStyle.mb20}>
            <Alert message="提示" description={'生产环境暂无sso灰度配置'} type="info" showIcon />
          </div>
        )}
        {vanCanaryErrorMsg === '' && list.length > 0 && (
          <CardList list={list} updateGrayHeaderInfoByIndex={updateGrayHeaderInfoByIndex} />
        )}
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'dev/sso-config'}>
      <DevConfig />
    </KeepAlive>
  </>
);
