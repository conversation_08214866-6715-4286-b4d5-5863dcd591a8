/*
 * @Author: your name
 * @Date: 2021-09-28 15:09:43
 * @LastEditTime: 2021-10-15 13:42:59
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /hll-finrisk-web/src/pages/RiskPreRun/index.tsx
 */
import ProTable from '@ant-design/pro-table';
// import { PlusOutlined } from '@ant-design/icons';
import HeaderTab from '@/components/HeaderTab';
import { disableFutureDate } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import { Button, DatePicker, Dropdown, Menu } from 'antd';
import React, { useRef, useState } from 'react';
import AddRiskPreRunModal from './components/AddRiskPreRunModal';
import type { RiskPreRunListItem } from './data';
import { queryRiskPreList } from './service';

const RiskPreRun: React.FC<any> = () => {
  const actionRef = useRef<ActionType>();
  const [visibleRiskPreAdd, setVisibleRiskPreAdd] = useState<boolean>(false);
  const [modalTitleType, setModalTitleType] = useState<number>(1);
  const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
    return (
      <DatePicker.RangePicker
        {...rest}
        // placeholder="请选择"
        style={{ width: '100%' }}
        disabledDate={disableFutureDate}
      />
    );
  };

  const columns: ProColumns<RiskPreRunListItem>[] = [
    {
      title: '任务编号',
      dataIndex: 'id',
      search: false,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      search: false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondName',
      search: false,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      search: false,
      valueEnum: {
        0: '保理进件风控预跑',
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      search: false,
      valueEnum: {
        1: { text: '成功', status: 'Success' },
        0: { text: '执行中', status: 'Processing' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      hideInTable: true,
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      width: 120,
      // renderFormItem,
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          startTime: `${value[0].split(' ')[0]} 00:00:00`,
          endTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '完成时间',
      dataIndex: 'finishTime',
      search: false,
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <>
          <Link to={`/operation-manager/risk-run/detail?flowNo=${record.flowNo}`}>查看详情</Link>
        </>
      ),
    },
  ];
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<RiskPreRunListItem>
          actionRef={actionRef}
          rowKey="flowNo"
          columns={columns}
          request={(params) => queryRiskPreList(params)}
          scroll={{ x: 1920 }}
          toolBarRender={() => [
            <Dropdown
              key="menu"
              overlay={
                <Menu>
                  <Menu.Item
                    key="0"
                    onClick={() => {
                      setVisibleRiskPreAdd(true);
                      setModalTitleType(0);
                    }}
                  >
                    单个
                  </Menu.Item>
                  <Menu.Item
                    key="1"
                    onClick={() => {
                      setVisibleRiskPreAdd(true);
                      setModalTitleType(1);
                    }}
                  >
                    批量
                  </Menu.Item>
                </Menu>
              }
            >
              <Button type="primary">创建预跑任务</Button>
            </Dropdown>,
          ]}
        />
        <AddRiskPreRunModal
          modelVisible={visibleRiskPreAdd}
          type={modalTitleType}
          onCancel={() => {
            setVisibleRiskPreAdd(false);
          }}
          onOk={async () => {
            setVisibleRiskPreAdd(false);
            actionRef?.current?.reload();
          }}
          onVisibleChange={setVisibleRiskPreAdd}
        />
      </PageContainer>
    </>
  );
};

export default React.memo(RiskPreRun);
