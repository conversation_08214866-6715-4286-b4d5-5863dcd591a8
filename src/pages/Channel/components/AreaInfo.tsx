/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-10-18 16:18:42
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2023-03-27 16:11:10
 * @FilePath: /lala-finance-biz-web/src/pages/Channel/components/LicensedInfo.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import globalStyle from '@/global.less';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { Button, Card, message, Modal, Table } from 'antd';
import React from 'react';
import AddAreaInfo from './AddAreaInfo';
// import { useRequest } from 'ahooks';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { modifyAreaStatus } from '../service';

const STATUS_LABEL = {
  1: '启用',
  0: '禁用',
};

const AreaInfo: React.FC<{ areaRspList: any[]; refresh: () => void }> = ({
  areaRspList,
  refresh,
}) => {
  const channelId = history?.location?.query?.id as string;
  // const { tableProps, refresh } = useRequest(
  //   () => {
  //     return getAreaList({ channelId });
  //   },
  //   {
  //     paginated: true,
  //     defaultPageSize: 10,
  //     formatResult: (response) => {
  //       return { list: response.data, total: response.total };
  //     },
  //   },
  // );
  const { cityList } = useModel('cityList');
  const columns = [
    {
      title: '门店名称',
      key: 'storeName',
      dataIndex: 'storeName',
    },
    {
      title: '销售城市',
      key: 'saleCity',
      dataIndex: 'saleCity',
    },
    {
      title: '门店地址',
      key: 'storeAddress',
      dataIndex: 'storeAddress',
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (status: number) => {
        return STATUS_LABEL[status] || '-';
      },
    },
    {
      title: '操作',
      key: 'option',
      // dataIndex: '',
      render: (_, row) => {
        // console.log(row);
        return (
          <>
            <a
              onClick={() => {
                optimizationModalWrapper(AddAreaInfo)({
                  title: '编辑',
                  curRow: row,
                  refresh,
                  channelId,
                  cityList,
                });
              }}
            >
              编辑
            </a>
            <a
              className={globalStyle.ml10}
              onClick={() => {
                Modal.confirm({
                  title: `你确定${row?.status === 0 ? '启用' : '禁用'}${row?.storeName}?`,
                  icon: <ExclamationCircleOutlined />,
                  // content: 'Some descriptions',
                  okText: '确认',
                  okType: 'danger',
                  cancelText: '取消',
                  onOk: async () => {
                    await modifyAreaStatus(row?.id, row?.status == 0 ? 1 : 0).then(() => {
                      message.success('修改成功');
                      refresh();
                    });
                  },
                });
              }}
            >
              {row?.status === 0 ? '启用' : '禁用'}
            </a>
          </>
        );
      },
    },
  ];

  return (
    <Card
      title="展业区域信息"
      extra={
        <Button
          type="primary"
          onClick={() => {
            optimizationModalWrapper(AddAreaInfo)({
              title: '新增',
              refresh,
              channelId,
              cityList,
            });
          }}
        >
          新增
        </Button>
      }
      className={globalStyle.mt20}
    >
      <Table columns={columns} rowKey="id" dataSource={areaRspList} />
    </Card>
  );
};

export default AreaInfo;
