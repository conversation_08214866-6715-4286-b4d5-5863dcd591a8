.root-image-preview {
  .ant-image-preview-mask {
    z-index: 1080;
  }
  .ant-image-preview-wrap {
    z-index: 1080;
  }
  .ant-image-preview-operations-wrapper {
    top: initial;
    bottom: 0;

    .ant-image-preview-operations {
      justify-content: center;

      .ant-image-preview-operations-progress {
        bottom: 60px;
      }
    }
  }
}

.image-preview-toolbar {
  padding: 6px 24px 0 24px;
  color: #fff;
  font-size: 20px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 100px;

  .anticon {
    padding: 12px;
    cursor: pointer;
  }
  &-counter {
    font-size: 16px;
    text-align: center;
  }
}

.image-perview-pdf {
  height: 800px;
  overflow-y: auto;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;

  &-page {
    canvas {
      transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
    }
  }
}
