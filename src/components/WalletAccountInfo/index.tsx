import { ProDescriptions } from '@ant-design/pro-components';
import React from 'react';
import './index.less';
import WalletActions from './WalletActions';

interface WalletAccountInfoProps {
  data: any;
  secondProductCode: '0303' | '0201';
  showTransaction: () => void;
  showClaimRecharge: () => void;
  showClaimRecord: () => void;
}
const WalletAccountInfo: React.FC<WalletAccountInfoProps> = ({
  data,
  secondProductCode,
  showTransaction,
  showClaimRecharge,
  showClaimRecord,
}) => {
  const actions = {
    showTransaction,
    showClaimRecharge,
    showClaimRecord,
  };
  return (
    <ProDescriptions column={2} dataSource={data}>
      <ProDescriptions.Item label="专属充值帐号账户名" dataIndex="accountName" />
      <ProDescriptions.Item label="专属充值帐号" dataIndex="epBankNo" />
      <ProDescriptions.Item label="专属充值帐号银行名" dataIndex="bankName" />
      <ProDescriptions.Item label="当前钱包余额">
        <WalletActions
          externalOwnerId={data?.externalOwnerId}
          actions={actions}
          secondProductCode={secondProductCode}
        />
      </ProDescriptions.Item>
      <ProDescriptions.Item label="代偿渠道">
        {data?.reimbursementEntityTerList || '暂无'}
      </ProDescriptions.Item>
    </ProDescriptions>
  );
};

export default React.memo(WalletAccountInfo);
