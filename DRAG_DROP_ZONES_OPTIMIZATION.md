# DragDropZones 组件数据结构优化说明

## 优化背景

根据您的反馈，原有的 `sourceFiles` 字段是一个错误的设计。拖拽时操作的全局 `formattedSourceFiles` 应该就足够了。

## 数据结构优化

### 1. 移除分类中的 sourceFiles 字段

**优化前：**
```typescript
const categoryDataMap: Record<string, {
  sourceFiles: DragItem[];  // ❌ 错误的字段
  targetZone1: DragItem[];
  targetZone2: DragItem[];
}> = {};
```

**优化后：**
```typescript
const categoryDataMap: Record<string, {
  targetZone1: DragItem[];  // ✅ 只保留目标区域
  targetZone2: DragItem[];
}> = {};
```

### 2. 优化 onDataChange 回调接口

**优化前：**
```typescript
interface DragDropZonesProps {
  onDataChange?: (data: {
    sourceFiles: DragItem[];      // ❌ 含义不明确
    targetZone1: DragItem[];
    targetZone2: DragItem[];
    formattedApiData?: Record<...>;
  }) => void;
}
```

**优化后：**
```typescript
interface DragDropZonesProps {
  onDataChange?: (data: {
    globalSourceFiles: DragItem[];     // ✅ 明确表示全局源文件
    currentCategoryData: {             // ✅ 明确表示当前分类数据
      targetZone1: DragItem[];
      targetZone2: DragItem[];
    };
    formattedApiData?: Record<...>;
  }) => void;
}
```

### 3. 简化拖拽逻辑

**核心改进：**
- 源文件只能拖拽到目标区域，不能拖拽回源区域
- 目标区域之间可以相互拖拽
- 全局源文件状态独立管理

**拖拽处理逻辑：**
```typescript
// 只允许拖拽到目标区域
if (draggedItem && (overId === 'targetZone1' || overId === 'targetZone2')) {
  const isFromGlobalSource = sourceLocation === 'source';
  
  if (isFromGlobalSource) {
    // 从全局源文件拖拽到目标区域
    setGlobalSourceFiles(prev => prev.filter(item => item.id !== activeId));
  }
  
  // 更新分类数据...
}
```

## 主要优化点

### 1. 数据结构清晰化
- **全局源文件**：`globalSourceFiles` - 来自 `notMatchFileDTOList`
- **分类数据**：只包含 `targetZone1` 和 `targetZone2`
- **API数据**：`formattedApiData` - 用于提交的格式化数据

### 2. 逻辑简化
- 移除了分类中的 `sourceFiles` 相关逻辑
- 简化了查找和拖拽处理
- 统一了数据流向

### 3. 团单重复ID处理
```typescript
const uniqueId = batchOcrData.groupFlag === 1 
  ? `${url}_${carIndex}_${index}_${getRandomUUID(8)}` // 团单时生成唯一ID
  : `${url}_${index}`; // 非团单时使用原逻辑
```

### 4. 分类计数修正
```typescript
// 移除了错误的 sourceFiles 计数
count: vehicleDocuments.length + insuranceMaterials.length
```

## 修改的主要函数

1. **formatCarInfoData** - 移除 sourceFiles 字段，返回 globalSourceFiles
2. **getCurrentCategoryData** - 简化返回结构
3. **findItemLocation** - 移除分类 sourceFiles 查找
4. **getActiveItem** - 移除分类 sourceFiles 查找
5. **handleDragEnd** - 简化拖拽逻辑，只处理到目标区域的拖拽
6. **handleDeleteItem** - 更新回调数据结构

## 向后兼容性

- 保持了原有的核心功能
- 只是优化了数据结构和逻辑
- 团单和非团单的处理逻辑保持不变
- API提交格式保持不变

## 测试要点

1. **notMatchFileDTOList 回显** - 确认全局源文件正确显示
2. **团单重复ID** - 确认每个车辆的文件都有唯一ID
3. **拖拽功能** - 确认从源文件到目标区域的拖拽正常
4. **数据回调** - 确认 onDataChange 回调数据结构正确
5. **分类切换** - 确认切换分类时数据显示正确

## 优化效果

✅ **数据结构更清晰** - 全局源文件和分类数据职责分明
✅ **逻辑更简单** - 移除了冗余的 sourceFiles 处理
✅ **性能更好** - 减少了不必要的数据操作
✅ **维护性更强** - 代码结构更清晰，易于理解和维护
