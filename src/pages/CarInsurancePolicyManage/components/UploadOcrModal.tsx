import { ModalForm } from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import DraggerOCRUploadForm from './DraggerOCRUploadForm';

interface UploadOcrModalProps {
  visibleRef: React.RefObject<any>;
  title?: string;
  onFinish: (values: any) => Promise<boolean>;
  draggerProps?: Record<string, any>;
  needOcr?: boolean;
  needCustomRequest?: boolean;
}

export interface UploadOcrModalRef {
  show: () => void;
  hide: () => void;
  getForm: () => any;
  saveRecord: (v: any) => void;
  getRecord: () => any;
  updateLoading: (v: boolean) => void;
}

const UploadOcrModal: React.FC<UploadOcrModalProps> = (props) => {
  const {
    visibleRef,
    title,
    needOcr = false,
    needCustomRequest = false,
    onFinish,
    draggerProps,
    ...rest
  } = props;
  const [modalVisit, setModalVisit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [record, setRecord] = useState<any>(null);
  const [form] = Form.useForm();

  useImperativeHandle(visibleRef, () => ({
    show: () => setModalVisit(true),
    hide: () => setModalVisit(false),
    getForm: () => form,
    saveRecord: (v: any) => setRecord(v),
    getRecord: () => record,
    updateLoading: (v: boolean) => setLoading(v),
  }));

  // 添加modal loading状态
  const catchOnUploadChange = async ({ file, fileList }: any) => {
    if (file.status === 'uploading') {
      setLoading(true);
    }
    if (file.status === 'error') {
      setLoading(false);
    }

    if (file.status === 'done') {
      setLoading(false);
    }

    if (file.status === 'removed') {
      setLoading(false);
    }
  };
  return (
    <ModalForm
      title={title || '上传保单'}
      open={modalVisit}
      loading={loading}
      onLoadingChange={setLoading}
      form={form}
      onOpenChange={setModalVisit}
      omitNil={false}
      modalProps={{
        destroyOnClose: true, // 关闭时销毁,避免表单缓存
        afterClose: () => {
          setRecord(null);
          setLoading(false);
        },
        maskClosable: false, // 禁止点击蒙层关闭
      }}
      submitTimeout={2000}
      onFinish={onFinish}
      {...rest}
    >
      <DraggerOCRUploadForm
        onChange={catchOnUploadChange}
        needOcr={needOcr}
        needCustomRequest={needCustomRequest}
        {...draggerProps}
      />
    </ModalForm>
  );
};

export default React.memo(UploadOcrModal);
