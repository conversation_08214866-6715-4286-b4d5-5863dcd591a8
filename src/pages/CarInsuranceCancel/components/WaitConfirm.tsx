import { useAccess } from '@umijs/max';
import { Button, Col, Form, Input, message, Modal, Popconfirm, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import { DetailInfo, ListItem } from '../const';
import '../index.less';
import { cancelCancellation, uploadBankInfo } from '../service';
import BasicInfo from './BasicInfo';
import UploadPannel from './UploadPannel';

interface ModalProps {
  closeModal: (refresh?: boolean) => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  detailInfo: DetailInfo;
}

const Page: React.FC<ModalProps> = ({ visible, closeModal, currentUser, record, detailInfo }) => {
  const { orderNo, bizNo } = record;
  const [submitLoading, setSubmitLoading] = useState(false);
  const access = useAccess();
  //
  const uploadRef = useRef<any>();
  const [uploadLoading, setUploadLoading] = useState(false);
  const handleUploadSubmit = async () => {
    setUploadLoading(true);
    const res: any = await uploadRef?.current?.submitUpload().catch(() => {});
    console.log(res);
    setUploadLoading(false);
  };

  const [formOfRepayConfirm] = Form.useForm();
  const handleRepayConfirmSubmit = async () => {
    try {
      await formOfRepayConfirm.validateFields();
    } catch {
      return Promise.reject();
    }
    //
    const formValues = formOfRepayConfirm.getFieldsValue();
    const params: any = {
      bizNo,
      createdBy: currentUser?.accountName,
      transactionNo: formValues?.transactionNo,
      actualPaymentAmount: formValues?.actualPaymentAmount,
      actualPaymentTime: dayjs(formValues?.actualPaymentTime).format('YYYY-MM-DD'),
    };
    setSubmitLoading(true);
    const res = (await uploadBankInfo(params).catch(() => {})) || {};
    setSubmitLoading(false);
    if (res?.success) {
      message.success('操作成功');
      closeModal(true);
    }
  };
  // 撤销二次确认
  const [cancelLoading, setCancelLoading] = useState(false);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const openConfirm = () => setConfirmVisible(true);
  const closeConfirm = () => setConfirmVisible(false);
  // 撤销
  const handleCancelAction = async () => {
    setCancelLoading(true);
    const params = {
      bizNo,
      createdBy: currentUser?.accountName,
    };
    const res: any = (await cancelCancellation(params).catch(() => {})) || {};
    setCancelLoading(false);
    console.log('res', res);
    if (res?.success) {
      message.success('撤销成功');
      closeModal(true);
    }
  };

  return (
    <>
      <Modal
        width={960}
        destroyOnClose
        title={`退保详情`}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Popconfirm
              title="确认撤销？"
              open={confirmVisible}
              onCancel={closeConfirm}
              onConfirm={handleCancelAction}
              okButtonProps={{ loading: cancelLoading }}
            >
              <Button loading={cancelLoading} onClick={openConfirm}>
                撤销
              </Button>
            </Popconfirm>
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            {/*  */}
            {access?.hasAccess('upload_file_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" onClick={handleUploadSubmit} loading={uploadLoading} ghost>
                保存附件
              </Button>
            )}
            {access.hasAccess('upload_bank_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" onClick={handleRepayConfirmSubmit} loading={submitLoading}>
                提交
              </Button>
            )}
          </>
        }
      >
        {/*  */}
        <BasicInfo detailInfo={detailInfo} orderStatus={detailInfo.orderStatus} />
        <UploadPannel
          ref={uploadRef}
          detailInfo={detailInfo}
          defaultFileList={detailInfo?.fileInfoAttach ? detailInfo?.fileInfoAttach : []}
        />
        {/*  */}
        <Form
          form={formOfRepayConfirm}
          labelCol={{ span: 6 }}
          labelAlign="left"
          wrapperCol={{ span: 18 }}
        >
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <p className="red-text">
                系统会每小时自动确认一次回款是否到账，自动更新退保状态，如果有其他异常情况，可使用手动确认到账
              </p>
            </Col>
            <Col span={12}>
              <Form.Item
                label="银行流水号"
                name="transactionNo"
                rules={[{ required: true, message: '请输入银行流水号' }]}
              >
                <Input
                  disabled={
                    !access.hasAccess('upload_bank_biz_businessMng_postLoanMng_cancellation')
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="实际到账金额"
                name="actualPaymentAmount"
                rules={[
                  { required: true, message: '请输入实际到账金额' },
                  {
                    // 大于0的数字，最多两位小数,不能填写除小数点和数字以外的字符
                    validator: (_, val) => {
                      if (isNaN(val) || val?.includes('e')) {
                        return Promise.reject('请输入数字');
                      }
                      if (val !== '' && val <= 0) {
                        return Promise.reject('请输入大于0的数字');
                      }
                      if (val.includes('.') && val.toString().split('.')[1].length > 2) {
                        return Promise.reject('最多两位小数');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  type="number"
                  disabled={
                    !access.hasAccess('upload_bank_biz_businessMng_postLoanMng_cancellation')
                  }
                />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
                  <Form.Item
                    label="实际到账时间"
                    name="actualPaymentTime"
                    rules={[{ required: true, message: '请选择实际到账时间' }]}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      disabledDate={(current: any) => {
                        return current && current < dayjs().endOf('day').subtract(1, 'days');
                      }}
                    />
                  </Form.Item>
                </Col> */}
          </Row>
        </Form>
      </Modal>
    </>
  );
};
export default Page;
