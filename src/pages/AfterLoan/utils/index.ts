import { isChannelStoreUser } from '@/utils/utils'; // @ts-ignore
import type { ProFormInstance } from '@ant-design/pro-components';
import type { MutableRefObject } from 'react';

// leaseStoreUser    融租门店用户 有一个固定的门店 access?.currentUser?.extSource?.storeId
// leaseChannelUser  融租渠道用户
// 有一些额外的请求请求参数的设置
export const getChannelStoreRequestParams = (
  access: any,
  keys?: { channel?: string; store?: string; channelType?: string }, //  是否适用重命名的key
  type?: { channel?: string; store?: string; channelType?: string }, //  格式,
) => {
  const params: any = {};
  if (isChannelStoreUser(access)) {
    // 渠道类型
    if (access.currentUser.channelType) {
      params[keys?.channelType || 'channelTypes'] = access.currentUser.channelType;
    }
    // 渠道
    if (access.currentUser?.channelCode)
      params[keys?.channel || 'channelIds'] =
        type?.channel === 'string'
          ? access.currentUser.channelCode
          : [access.currentUser.channelCode];
    // 门店
    if (access.currentUser?.extSource?.storeId)
      params[keys?.store || 'storeIds'] = [access.currentUser.extSource.storeId];
  }
  return params;
};

// leaseStoreUser    融租门店用户 有一个固定的门店 access?.currentUser?.extSource?.storeId
// leaseChannelUser  融租渠道用户
// 需要初始化表单值

export const setFormValuesForChannelStore = (
  access: any,
  formRef: MutableRefObject<ProFormInstance | undefined>,
  keys?: { channel?: string; store?: string; channelType?: string }, //  是否适用重命名的key
) => {
  if (isChannelStoreUser(access)) {
    if (access?.currentUser?.channelCode) {
      // 初始化表单值 渠道名称
      formRef?.current?.setFieldValue(keys?.channel || 'channelIds', [
        access.currentUser.channelCode,
      ]);
    }
    if (access.currentUser?.extSource?.storeId) {
      formRef.current?.setFieldValue(keys?.store || 'storeIds', [
        access?.currentUser?.extSource?.storeId?.toString(),
      ]);
    }
    if (access.currentUser?.channelType) {
      formRef.current?.setFieldValue(
        keys?.channelType || 'channelTypes',
        access.currentUser.channelType.toString(),
      );
    }
  }
};
