/*
 * @Author: your name
 * @Date: 2021-03-20 15:48:31
 * @LastEditTime: 2021-05-13 17:41:00
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalMng/components/IncomeTable.tsx
 */
import { Link, useRequest } from '@umijs/max';
import { Table } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { getRiskRecordList } from '../service';

interface PropsItem {
  userNo: string;
}

interface dataItem {
  applyAmount: number;
  applyTime: string;
  classification: string;
  creditAmount: number;
  enterpriseName: string;
  orderNo: string;
  secondClassification: string;
  status: number;
  userNo: string;
}

const statusMap = {
  10: '用户创建成功',
  11: '秒拒',
  20: '提交进件信息',
  30: '待初审',
  31: '待终审',
  32: '退回补件',
  40: '审核拒绝',
  41: '审核通过',
};

const columns = [
  {
    title: '进件流水号',
    dataIndex: 'orderNo',
    key: 'orderNo',
  },
  {
    title: '产品一级分类',
    dataIndex: 'classification',
    key: 'classification',
  },
  {
    title: '产品二级分类',
    dataIndex: 'secondClassification',
    key: 'secondClassification',
  },
  {
    title: '申请额度',
    dataIndex: 'applyAmount',
    key: 'applyAmount',
  },
  {
    title: '申请时间',
    key: 'applyTime',
    render: (_: any, record: dataItem) => {
      return dayjs(record.applyTime).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: '进件状态',
    key: 'status',
    render: (_: any, record: dataItem) => {
      return <div>{statusMap[record.status]}</div>;
    },
  },
  {
    title: '授信额度',
    dataIndex: 'creditAmount',
    key: 'creditAmount',
  },
  {
    title: '操作',
    key: 'action',
    render: (_: any, record: dataItem) => (
      <Link to={`/userMng/personalMng/detail?orderNo=${record.orderNo}`}>查看详情</Link>
    ),
  },
];

const IncomeTable: React.FC<PropsItem> = (props) => {
  // 获取企业进件记录列表
  const { data, loading } = useRequest(() => {
    return getRiskRecordList(props.userNo);
  });

  return (
    <Table
      columns={columns}
      loading={loading}
      dataSource={data}
      pagination={false}
      rowKey="orderNo"
    />
  );
};
export default IncomeTable;
