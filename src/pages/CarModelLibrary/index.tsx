/*
 * @Author: your name
 * @Date: 2021-04-13 11:18:55
 * @LastEditTime: 2022-02-21 11:04:27
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/ModelLibrary/index.tsx
 */
import { PageContainer } from '@ant-design/pro-layout';
import React from 'react';

import { Tabs } from 'antd';

import HeaderTab from '@/components/HeaderTab';
import { history } from '@umijs/max';
import { NewCarTable, SecondCarTable } from './components';
// import { downLoadExcel } from '../../utils/utils';
const ModelLibrary: React.FC<any> = () => {
  const { TabPane } = Tabs;
  const { carType, carNo } = history?.location?.query;
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Tabs defaultActiveKey={carType || '1'} style={{ background: '#fff', padding: '0 10px' }}>
          <TabPane tab="新车" key="1">
            <NewCarTable carTypeFromQuery={carType} carNo={carNo} />
          </TabPane>
          <TabPane tab="二手车" key="2">
            <SecondCarTable carTypeFromQuery={carType} carNo={carNo} />
          </TabPane>
        </Tabs>
      </PageContainer>
    </>
  );
};

export default ModelLibrary;
