/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-26 09:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/detail/index.tsx
 * @Description: index.tsx
 */

import React from 'react';
import { CarInsuranceDetailProvider } from '../context/CarInsuranceDetailContext';
import DetailPage from '../pages/detail';
import './index.less';
const DetailProviderContainer: React.FC = () => {
  return (
    <CarInsuranceDetailProvider>
      <DetailPage />
    </CarInsuranceDetailProvider>
  );
};

export default DetailProviderContainer;
