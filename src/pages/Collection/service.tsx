/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 14:33:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-14 10:23:14
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/service.tsx
 * @Description: Collection/service.tsx
 */

import { getBlob, getVanEvn, saveAs } from '@/utils/utils';
// import { message } from 'antd';
// import requestUmi from 'umi-request';
import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type {
  ApplyReliefParams,
  batchOverdueSMSItem,
  BillSubmitCallBackParams,
  CombineRepayBackParams,
  DispatchParams,
  HistorySMSListByOverdueCaseItem,
  IaddRefundCardRecord,
  IbatchOverdueSMSDataItem,
  IbillInfo,
  IbillInfoParams,
  ICallDetail,
  IgetInfoFromPhoneParams,
  IphoneInfoItem,
  IrefundCarRecord,
  OverdueCaseListParams,
  OverdueSMSItem,
  PhoneListByOverdueCaseWithHistoryItem,
  SettleAmountParams,
  SMSListByOverdueCaseItem,
  SubmitCallBackParams,
  WithHoldParams,
  WithHoldWalletParams,
} from './data';

// 获取催收案件详情的基础信息
export async function getBaseInfo(overdueCaseNo: string) {
  return request(`/bizadmin/overdue/overdueCase/baseInfo/${overdueCaseNo}`, {
    headers,
  })
    .then((data) => data?.data)
    .catch(() => Promise.resolve({}));
}

// 获取催收案件详情的催收信息
export async function getOverdueRecord(overdueCaseNo: string) {
  return request(`/bizadmin/overdue/overdueCase/overdueRecord/${overdueCaseNo}`, { headers })
    .then((data) => data?.data)
    .catch(() => Promise.resolve({}));
}

//获取催收案件详情
export async function getOverdueCaseDetail(overdueCaseNo: string) {
  const result = await Promise.all([getBaseInfo(overdueCaseNo), getOverdueRecord(overdueCaseNo)]);
  return result.reduce((pre, cur) => ({ ...pre, ...(cur || {}) }), {});
  // return request(`/repayment/cms/overdue/management/overdueCase/${overdueCaseNo}`);
}

// // 查询协议支付信息
export async function queryWithHoldInfo(overdueCaseNo: string) {
  return request('/repayment/cms/overdue/management/withhold', {
    method: 'GET',
    params: {
      overdueCaseNo,
    },
  });
}

// 计算协议支付结清金额
export async function calcSettleAmount(data: SettleAmountParams) {
  return request('/bizadmin/repayment/order/settle/pending', {
    method: 'POST',
    data,
    headers,
  });
}

// 提交协议支付信息
export async function postWithHold(data: WithHoldParams) {
  return request('/bizadmin/overdue/withhold', {
    method: 'POST',
    data,
    headers,
  });
}

// 提交协议支付信息-钱包余额
export async function postWithHoldWallet(data: WithHoldWalletParams) {
  return request(`/bizadmin/bill/overdueCase/wallet/submit`, {
    method: 'POST',
    data,
    headers,
  });
}

// 协议支付记录
export async function withHoldList(params: {
  overdueCaseNo?: string; // 催收单号
  current?: number; // 当前页
  pageSize?: number; // 页大小
}) {
  return request('/repayment/cms/overdue/management/withholdRecord', {
    method: 'GET',
    params: { ...params },
  });
}

// 查询历史还款记录
export async function repayRecording(params?: any) {
  return request('/repayment/cms/overdue/management/repay/recording', {
    method: 'GET',
    params: { ...params },
    skipErrorHandler: true,
    // errorHandler: (err) => {
    //   console.log(err.message);
    //   message.destroy();
    // },
  });
}

// 删除联系人
export async function deleteContact(contactId: string) {
  return request(`/repayment/cms/overdue/management/delete/contact?contactId=${contactId}`, {
    method: 'POST',
  });
}

// 添加联系人
export async function addContact(data: any) {
  return request('/repayment/cms/overdue/management/add/contact', {
    method: 'POST',
    data,
  });
}

// 获取外呼事件详情
export async function getCallerDetail(callRecord: string) {
  // return request(`/repayment/cms/overdue/management/callAction/detail/${callRecord}`,{
  //   headers
  // });
  return request(`/bizadmin/outbound/get/record`, {
    params: { callRecord },
    headers,
  });
}

// 获取外呼录音文件
export async function getCallerAudioRecord(callRecord: string) {
  // return request(`/repayment/cms/overdue/management/callAction/record/${callRecord}`);
  return request(`/bizadmin/outbound/get/recording`, {
    params: { callRecord },
    headers,
  });
}

// 获取行动标识枚举
export async function getActionList(businessType: string) {
  return request(`/repayment/cms/overdue/management/callAction/action/${businessType}`);
}

// 外呼动作插入
export function postCaller(data: ICallDetail) {
  return request(`/repayment/cms/overdue/management/callAction`, {
    method: 'POST',
    data,
  });
}

export const downLoad = (url: string, filename: string) => {
  // getOssPath(url).then((res) => {
  getBlob(url, (blob: Blob) => {
    saveAs(blob, filename);
  });
  // });
};

// 申请减免
export async function applyRelief(data: ApplyReliefParams) {
  return request('/repayment/cms/overdue/management/remission', {
    method: 'POST',
    data,
  });
}

// 提交催收回款
export async function submitCallBack(data: SubmitCallBackParams) {
  return request('/repayment/cms/overdue/management/payment', {
    method: 'POST',
    data,
  });
}

//  提交催收回款（新）
export async function submitBillCallBack(data: BillSubmitCallBackParams) {
  return request('/bizadmin/bill/overdueCase/payment/submit', {
    method: 'POST',
    data,
    headers,
  });
}

/**
 * 获取用于提交还款唯一键
 */
export function getRepayApplyId() {
  return request('/repayment/repay/getRepayApplyId', {
    method: 'GET',
  });
}

// 填写还款信息并提交回款信息（催收回款、线下回款）
export async function combineRepayBack(data: CombineRepayBackParams) {
  return request('/repayment/account/repay/initAndCommit', {
    method: 'POST',
    data,
    hideToast: true,
  });
}

//催收案件列表
export async function overdueCaseList(params: OverdueCaseListParams) {
  // return request('/repayment/cms/overdue/management/overdueCase/list', {
  //   method: 'get',
  //   params,
  // });
  return request('/bizadmin/overdue/overdueCase/list', {
    method: 'POST',
    data: params,
    headers,
    ifTrimParams: true,
  });
}

// /loan/lease/channel/cms/list4Page

//催收案件列表导出
export async function overdueCaseExport(params: OverdueCaseListParams) {
  return request('/repayment/cms/overdue/management/overdueCase/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

//案件今日数据统计
export async function overdueCaseCount(myList?: boolean) {
  return request('/repayment/cms/overdue/management/overdueCase/count', {
    params: { myList },
  });
}

// 派单
export async function dispatch(data: DispatchParams) {
  return request('/repayment/cms/overdue/management/batch/dispatch', {
    method: 'POST',
    data,
  });
}

//冻结
export async function freeze(data: { batchFreezeReq: { overdueCaseReqList: [] } }) {
  return request('/repayment/cms/overdue/management/batch/freeze', {
    method: 'POST',
    data,
  });
}

//解冻
export async function unFreeze(data: { batchFreezeReq: { overdueCaseReqList: [] } }) {
  return request('/repayment/cms/overdue/management/batch/unfreeze', {
    method: 'POST',
    data,
  });
}

//催收案件列表
export async function overdueSteps(overdueCaseNo: string) {
  return request('/repayment/cms/overdue/management/withdraw/statusLog', {
    method: 'get',
    params: { overdueCaseNo },
  });
}

//
export async function getContactList(userNo: string, productCode: string) {
  return request('/repayment/cms/overdue/management/overdueCase/enterpriseContact', {
    method: 'get',
    params: { userNo, productCode },
  });
}

// 获取催收还款银行列表
export async function getBankList(): Promise<
  { bankCode: string; bankName: string; imgUrl: string }[]
> {
  return request('/loan/cash/bankcard/bankList', {
    method: 'post',
  });
}

// 获取来电号码对应的各种订单信息
export async function getInfoFromPhone(
  params: IgetInfoFromPhoneParams,
): Promise<{ data: IphoneInfoItem[]; total: number }> {
  const data = await request('/bizadmin/overdue/get/order/OverdueInfo', {
    method: 'post',
    data: params,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data;
}

//获取催收短信模板
export async function getOverdueCaseSMSTemplate(params: {
  secondaryClassification: string;
  overdueMaxLevel: string;
}) {
  return request(`/bizadmin/overdue/getOverdueCaseSMSTemplate`, {
    method: 'get',
    params,
    headers,
  });
}

//手动发送短信
export async function sendOverdueSMS(data: OverdueSMSItem) {
  return request(`/bizadmin/overdue/sendOverdueSMS`, {
    method: 'post',
    data,
    headers,
  });
}

//获取批量发送客群数据
export async function getBatchOverdueSMSData(): Promise<IbatchOverdueSMSDataItem[]> {
  const data = await request(`/bizadmin/overdue/getBatchOverdueSMSData`, {
    method: 'get',
    headers,
    useCache: true,
    ttl: 60000,
  });
  return data?.data;
}

/**
 * @description 获取（历史）电话催收记录（分页）
 * @param params
 * @params overdueCaseNo 案件编号
 * @params productSecondCode 产品第二编号，类似 0301
 * @params userNo 用户编号
 * @params queryHistoryFlag 区分历史还是当前，默认不传查当前
 * @returns
 */
export async function getPhoneOverdueRecordWithHistory(
  params: PhoneListByOverdueCaseWithHistoryItem,
) {
  return request('/bizadmin/overdue/overdueCase/phone/overdueRecord', {
    method: 'POST',
    data: params,
    headers,
  });
}

//获取逾期案件下的短信催收记录
export async function getSMSListByOverdueCaseNo(params: SMSListByOverdueCaseItem) {
  return request(`/bizadmin/overdue/getSMSListByOverdueCaseNo`, {
    method: 'get',
    headers,
    params,
  });
}

/**
 * @description 获取历史短信催收记录（分页）
 * @param params
 * @params overdueCaseNo 案件编号
 * @params productSecondCode 产品第二编号
 * @params userNo 用户编号
 * @params current 当前页
 * @params pageSize 页大小
 * @returns
 */
export async function getHistorySMSOverdueRecord(params: HistorySMSListByOverdueCaseItem) {
  return request('/bizadmin/overdue/overdueCase/history/sms/overdueRecord', {
    method: 'POST',
    data: params,
    headers,
  });
}

//批量发送短信
export async function batchSendOverdueSMS(data: batchOverdueSMSItem) {
  return request(`/bizadmin/overdue/batchSendOverdueSMS`, {
    method: 'post',
    data,
    headers,
  });
}

//额度页面
export async function getOverdueDetail(overdueCaseNo: string) {
  return request(`/bizadmin/overdue/overdueCase/${overdueCaseNo}`, {
    method: 'get',
    headers,
  });
}

//小贷获取合同接口
export async function getSmallLoanContract(orderNo: string, secondCode: string) {
  return request(`/bizadmin/order/contract/getContractByOrderNo/${orderNo}/${secondCode}`, {
    method: 'get',
    headers,
  });
}

// 查询退车记录
export async function getRefundCarRecord(orderNo: string): Promise<{ data: IrefundCarRecord }> {
  return request(`/bizadmin/refund/process/query/lease/refundRecord/${orderNo}`, { headers });
}

// 新增退车记录
export async function addRefundCarRecord(params: IaddRefundCardRecord) {
  return request(`/bizadmin/refund/process/save/lease/refundRecord`, {
    method: 'POST',
    data: params,
    headers,
  });
}

// 司机钱包信息
export async function fetchDriverID(overdueCaseNo: string) {
  return request(`/bizadmin/overdue/overdueCase/getDriverId/${overdueCaseNo}`, {
    headers,
    skipGlobalErrorTip: true,
  });
}
export async function fetchDriverWalletInfo(info: {
  driverId?: string; // 司机id
  customerType?: string; // 当前页
  overdueCaseNo: string; // 单号
}) {
  return request('/bizadmin/overdue/overdueCase/driverWalletInfo/info', {
    method: 'POST',
    data: info,
    headers,
  });
}

export async function freezeList(params: {
  driverId?: string; // 司机id
  overdueCaseNo: string; // 单号
  customerType: string; // 类型
  current?: number; // 当前页
  pageSize?: number; // 页大小
}) {
  return request('/bizadmin/overdue/overdueCase/accountRecord/list', {
    method: 'GET',
    params: { ...params },
    headers,
  });
}

export async function depositList(params: {
  driverId?: string; // 司机id
  overdueCaseNo: string; // 单号
  customerType: string; // 类型
  current?: number; // 当前页
  pageSize?: number; // 页大小
}) {
  return request('/bizadmin/overdue/overdueCase/depositRecord/list', {
    method: 'GET',
    params: { ...params },
    headers,
  });
}

export async function getTotalTenDaysAmount(params: {
  driverId?: string; // 司机id
  overdueCaseNo: string; // 单号
  // customerType: string; // 类型
}) {
  return request('/bizadmin/overdue/overdueCase/getTotalTenDaysAmount', {
    method: 'GET',
    params: { ...params },
    headers,
  });
}

// 跳转到ap2平台
export const openAP2DriverPage = (driverId: string) => {
  if (driverId === undefined || driverId === null || driverId === '') {
    return;
  }
  const envMap: Record<string, string> = {
    prd: `https://ap2.huolala.work/index.php?_m=drivers&_a=driver_detail&owner_id=${driverId}`,
    pre: `https://ap2-pre.huolala.work/index.php?_m=drivers&_a=driver_detail&owner_id=${driverId}`,
    stg: `https://ap2-stg.huolala.work/index.php?_m=drivers&_a=driver_detail&owner_id=${driverId}`,
  };
  const currnetEnv = getVanEvn();
  const url = envMap[currnetEnv];
  if (url) {
    window.open(url);
  }
};

export async function fetchOverdueBillInfo(data: { orderNo: string; caseNo: string }) {
  return request(`/bizadmin/bill/overdueCase/overdue/billInfo`, {
    method: 'POST',
    data,
    headers,
  });
}
/**
 * 获取账单信息
 * @param params
 * @returns
 */
export async function billInfo(params: IbillInfoParams): Promise<IbillInfo> {
  const data = await request(`/bizadmin/bill/billInfo`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data;
}
