import HeaderTab from '@/components/HeaderTab';
import { EcarInsuranceChannelType } from '@/utils/bankend/enum';
import type { ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button } from 'antd';
import React, { Fragment, memo, useRef, useState } from 'react';
import { useAccess, useModel } from 'umi';
import AddModal from './components/AddModal';
import ChangeCorrelation from './components/ChangeCorrelation';
import ChangePassword from './components/ChangePassword';
import type { IgetListParams } from './services';
import { getList } from './services';
import type { IcarInsuranceChannelItem } from './type';

const CarInsuranceChannelList = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const [userAccount, setuserAccount] = useState<number>(0);
  const [currentChannelAccountNo, setCurrentChannelAccountNo] = useState<any>();
  const formRef = useRef<ProFormInstance>();
  const [relationVisible, setRelationVisible] = useState<boolean>(false);
  const [relationData, setRelationData] = useState({} as any);
  const { initialState } = useModel<'@@initialState'>('@@initialState');
  const { channelLevel, channelName, channelCode } = initialState?.currentUser || {};
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const [currentParentChannelInfo, setCurrentParentChannelInfo] = useState<Record<string, any>>({
    channelLevel,
    channelCode,
    channelName,
  });
  // const channelListDom = useMemo(() => {
  //   let dom = <></>;
  //   if (isSecondList || !access.hasAccess('all_btn_car_insurance_channel_list')) {
  //     dom = (
  //       <>
  //         <Button
  //           disabled={!access.hasAccess('all_btn_car_insurance_channel_list')}
  //           type="link"
  //           onClick={() => {
  //             formRef?.current?.resetFields();
  //             setIsSecondList(false);
  //             setParentChannelInfo({});
  //             formRef?.current?.submit();
  //           }}
  //         >
  //           所有渠道
  //         </Button>
  //         <span style={{ color: '#ccc' }}>{'>'}</span>
  //         <Button type="link">
  //           {parentChannelInfo?.channelName || currentUserChannelName}({total})
  //         </Button>
  //       </>
  //     );
  //   } else {
  //     dom = (
  //       <Button
  //         disabled={!access.hasAccess('all_btn_car_insurance_channel_list')}
  //         onClick={() => {
  //           formRef?.current?.resetFields();
  //           setIsSecondList(false);
  //           setParentChannelInfo({});
  //           formRef?.current?.submit();
  //         }}
  //         type="link"
  //       >
  //         所有渠道 {`(${total ?? 0})`}
  //       </Button>
  //     );
  //   }
  //   return dom;
  // }, [isSecondList, access, total, parentChannelInfo]);

  const columns: ProColumns<IcarInsuranceChannelItem>[] = [
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      valueType: 'select',
      valueEnum: EcarInsuranceChannelType,
      search: false,
    },
    {
      title: '渠道简称',
      dataIndex: 'channelShortName',
      search: false,
    },
    {
      title: '渠道全称',
      dataIndex: 'channelName',
    },
    {
      title: '上级渠道',
      dataIndex: 'parentChannelName',
      hidden: true,
    },
    {
      title: '上级渠道',
      dataIndex: 'parentChannel',
      search: false,
      render(_, record) {
        const { parentChannel } = record;
        if (parentChannel) {
          return (
            <>
              {parentChannel.channelName}
              <br />
              {parentChannel.orgCode}
            </>
          );
        } else {
          return '-';
        }
      },
    },
    {
      title: '渠道层级',
      dataIndex: 'channelLevel',
      search: false,
      render(value) {
        if (value === 2) {
          return '二级';
        } else if (value === 1) {
          return '一级';
        }
        return;
      },
    },
    {
      title: '社会统一信用代码',
      dataIndex: 'orgCode',
      search: false,
    },
    {
      title: '联系人',
      dataIndex: 'contactName',
      search: false,
    },
    {
      title: '联系人地址',
      dataIndex: 'contactAddress',
      search: false,
    },
    {
      title: '渠道账号',
      dataIndex: 'channelUserAccount',
      search: false,
      render: (_, record) => {
        const { channelUserAccount, channelAccountNo } = record;
        return (
          <>
            <>{channelUserAccount}</>
            <br />
            <a
              key="password"
              onClick={() => {
                setuserAccount(channelUserAccount);
                setCurrentChannelAccountNo(channelAccountNo);
                setVisible(true);
              }}
            >
              修改密码
            </a>
          </>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: '100',
      fixed: 'right',
      render: (_, record) => {
        const {
          companyList,
          enterpriseList,
          productList,
          personalCompanyList,
          personalProductList,
          relatedProductAndEnterpriseFlat = [{}],
          id,
          channelCode: targetChannelCode,
          channelName,
          parentChannel,
          personalRelatedPayAccount,
          channelLevel,
        } = record;

        // console.log(relatedProductAndEnterpriseFlat);
        return [
          // <>
          //   {/* TODO:需确认权限 */}
          //   {access.hasAccess('opt_subordinate_car_insurance_channel_list') && !isSecondList && (
          //     <a
          //       onClick={() => {
          //         formRef?.current?.resetFields();
          //         setIsSecondList(true);
          //         setParentChannelInfo({
          //           id,
          //           channelCode: targetChannelCode,
          //           channelName,
          //         });
          //         formRef.current?.submit();
          //       }}
          //     >
          //       下级
          //     </a>
          //   )}
          // </>,
          <a
            key="association"
            onClick={() => {
              setRelationData({
                relatedProduct: getCorRelatedId(productList),
                relatedEnterprise: getCorRelatedId(enterpriseList),
                relatedPayAccount: getCorRelatedId(companyList),
                personalRelatedProduct: getCorRelatedId(personalProductList),
                // personalRelatedPayAccount: getCorRelatedId(personalCompanyList),
                personalRelatedPayAccount,
                id,
                relatedProductAndEnterpriseFlat,
                channelCode: targetChannelCode,
                channelName,
                extMappingAccountBO: record?.extMappingAccountBO,
                withholdingChannels: record?.withholdingChannels,
              });
              let parentInfo = {};
              if (parentChannel) {
                parentInfo = {
                  channelLevel,
                  id: parentChannel?.id,
                  channelCode: parentChannel?.channelCode,
                  channelName: parentChannel?.channelName,
                };
              }
              setCurrentParentChannelInfo(parentInfo);
              console.log(relationData);
              setRelationVisible(true);
            }}
          >
            设置
          </a>,
          // <a
          //   key="delete"
          //   onClick={async () => {
          //     Modal.confirm({
          //       title: '删除渠道',
          //       content: `你确定要删除渠道-${record?.channelName}-吗`,
          //       async onOk() {
          //         await deleteChannel(record.id);
          //         message.success('删除成功');
          //         actionRef?.current?.reload();
          //       },
          //     });
          //   }}
          // >
          //   删除
          // </a>,
        ];
      },
    },
  ];

  function getCorRelatedId(arr: any[]): (number | string)[] {
    return arr?.map((item) => item?.id).filter((item) => item);
  }

  return (
    <div>
      <HeaderTab />
      <PageContainer>
        <ProTable<IcarInsuranceChannelItem, Omit<IgetListParams, 'pageNumber'>>
          columns={columns}
          cardBordered
          actionRef={actionRef}
          formRef={formRef}
          request={async (params) => {
            const { current = 1, ...otherParams } = params;
            const paramsT: IgetListParams = { pageNumber: current, ...otherParams };
            // 一级渠道展示下级
            if (channelLevel === 1) {
              paramsT.levelList = [2];
              paramsT.channelCode = channelCode;
            } else {
              // 运营、营运主管展示全部
              paramsT.levelList = [1, 2];
            }
            const { data, total }: any = await getList(paramsT);
            return {
              data,
              success: true,
              total,
            };
          }}
          scroll={{ x: 'max-content' }}
          // headerTitle={channelListDom}
          rowKey="id"
          toolBarRender={(action) => [
            <Fragment key="add">
              <AddModal action={action!}>
                <Button key="add" type="primary">
                  新增
                </Button>
              </AddModal>
            </Fragment>,
          ]}
        />
      </PageContainer>
      <ChangePassword
        visible={visible}
        setVisible={setVisible}
        userAccount={userAccount}
        action={actionRef?.current}
        pid={currentChannelAccountNo}
      />
      <ChangeCorrelation
        action={actionRef?.current}
        visible={relationVisible}
        isSecondList={currentParentChannelInfo.channelLevel === 2}
        setVisible={setRelationVisible}
        relationData={relationData}
        parentChannelInfo={currentParentChannelInfo}
      />
    </div>
  );
};

export default memo(CarInsuranceChannelList);
