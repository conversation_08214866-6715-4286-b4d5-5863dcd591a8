/*
 * @Author: your name
 * @Date: 2022-02-12 09:57:28
 * @LastEditTime: 2023-03-28 14:51:50
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/AddRepayRegist.tsx
 */

import { Button, Modal } from 'antd';
import React, { useRef } from 'react';
// import { ModalFormProps } from '@ant-design/pro-form';
import { getRecord } from '../service';
// import { getOssPath } from '@/services/global';
import { getUserListEnum } from '@/services/enum';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { getBlob, previewAS, saveAs } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { RegistList } from '../data';
import { AddOfflineRepay } from './index';

export type ShowRepayRegistListProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  repayPlanNo: string;
  title: string;
  shouldAmountDue?: number;
  refresh: () => void;
  repayStatus: number;
};

const ShowRepayRegistList: React.FC<ShowRepayRegistListProps> = ({
  visible,
  title,
  close,
  onOk,
  repayPlanNo,
  shouldAmountDue,
  refresh,
  repayStatus,
}) => {
  const previewPDF = (url: string, name: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      const exc = name.substring(name.lastIndexOf('.') + 1);
      if ('.jpg.jpeg.gif'.includes(exc)) {
        previewAS(blob, 'image/jpeg;chartset=UTF-8');
      } else if (exc === 'pdf') {
        previewAS(blob);
      } else {
        saveAs(blob, name);
      }
    });
    // });
  };
  const actionRef = useRef<ActionType>();

  const itemTableAttach = (_: React.ReactNode, record: RegistList) => {
    const attach: { netWorkPath: string; name: string }[] = record?.attach || [];
    return attach?.length
      ? attach.reduce((pre: React.ReactNode, cur: { netWorkPath: string; name: string }) => {
          return (
            <>
              {pre}
              <a
                target="_blank"
                onClick={() => {
                  previewPDF(cur?.netWorkPath, cur?.name);
                }}
              >
                {cur?.name}
              </a>
              <br />
            </>
          );
        }, <></>)
      : '-';
  };
  // const [visibleAdd, setVisibleAdd] = useState(false);

  const columns: ProColumns<RegistList>[] = [
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '申请人',
      dataIndex: 'applyName',
      key: 'applyName',
      valueType: 'select',
      request: getUserListEnum,
    },
    {
      title: '回款金额',
      dataIndex: 'remitAmount',
      key: 'remitAmount',
    },
    {
      title: '回款方式',
      dataIndex: 'remitType',
      key: 'remitType',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '附件',
      dataIndex: 'attach',
      key: 'attach',
      render: itemTableAttach,
    },
    {
      title: '审批结果',
      key: 'status',
      render: (_, record) => {
        const mapStatus = {
          0: '审核拒绝',
          1: '待审核',
          10: '审核通过',
        };
        return mapStatus[record?.status] || '-';
      },
    },
  ];
  return (
    <>
      <Modal
        width={800}
        destroyOnClose
        centered
        title={
          <>
            {/* 还款状态为待还款 */}
            {repayStatus === 1 ? (
              <Button
                type="primary"
                onClick={
                  () =>
                    optimizationModalWrapper(AddOfflineRepay)({
                      repayPlanNo,
                      shouldAmountDue,
                      onOk: () => {
                        actionRef?.current?.reload();
                        refresh();
                      },
                    })
                  // setVisibleAdd(true)
                }
              >
                新增线下还款
              </Button>
            ) : (
              ''
            )}
            {` 期数${title}`}
          </>
        }
        footer={null}
        open={visible}
        onOk={async () => {
          onOk();
          return true;
        }}
        onCancel={() => {
          close();
        }}
      >
        <ProTable
          columns={columns}
          actionRef={actionRef}
          // dataSource={dataList}
          scroll={{ x: 'max-content' }}
          request={(params) => {
            return getRecord({ ...params, repayPlanNo });
          }}
          search={false}
          toolBarRender={false}
        />
      </Modal>
    </>
  );
};

export default ShowRepayRegistList;
