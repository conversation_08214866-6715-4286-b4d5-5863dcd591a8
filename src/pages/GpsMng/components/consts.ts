export enum ORDER_TYPE {
  CREATE = '1', // 安装
  DISASSEMBLY = '2', // 拆机
  OVERHAUL = '3', // 检修
}

// gps订单状态
export enum GPS_ORDER_STATUS {
  CANCEL = -1, // 取消
  INIT = 0, // 初始
  NEW = 1, // 厂商新增
  WAIT_DISPATCH = 2, // 等待派单
  WAIT_INSTALL = 3, // 等待加装
  FINISH = 4, // 加装完成
}

export enum GPS_ACTION {
  CANCEL, // 取消安装
  APPLY_DISASSEMBLY, // 申请拆机
  APPLY_OVERHAUL, // 申请检修
  OFF_DISASSEMBLY, // 关闭拆机
  OFF_OVERHAUL, // 关闭检修
}
