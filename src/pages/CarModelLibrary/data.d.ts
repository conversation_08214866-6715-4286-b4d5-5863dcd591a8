/*
 * @Author: your name
 * @Date: 2021-04-13 11:20:11
 * @LastEditTime: 2023-04-13 17:08:47
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/ModelLibrary/data.d.ts
 */

export interface CarListItem {
  carCategory: number;
  carEmission: string;
  carModel: string;
  carModelCode: string;
  carType: number; // 车辆类型
  carUniqueCode: string;
  carYears: string;
  city: string;
  color: string;
  energyType: number;
  engineCode: string;
  environmentalType: string;
  firstRegisterDate: string;
  id: number | string;
  licenseCode: string;
  manufacturer: string;
  mileage: string;
  seatsNum: number;
  statusFlag: number;
  totalPrice?: number;
  otherMsg?: string;
  carNo: string;
  channelDetail: [];
  colorCityDetail: { color: string; cityCode: string; cityName: string }[];
}

export interface CarListParams {
  carModelCode?: string;
  carType?: number;
  carUniqueCode?: string;
  current?: number;
  pageSize?: number;
  carNo?: string;
}

export interface CarListPagination {
  total: number;
  pageSize: number;
  current: number;
}

export interface CarListData {
  list: CarListItem[];
  pagination: Partial<CarListPagination>;
}

export interface AddCarParams {
  carCategory?: number; // 车辆种类
  carEmission?: string; // 排放量
  carModel?: string;
  carModelCode?: string;
  carType?: number;
  carUniqueCode?: string;
  carYears?: string;
  colorCity?: Partial<ColorCity[]>;
  energyType?: number; // 能源类型
  engineCode?: string;
  environmentalType?: number; // 环保标准
  firstRegisterDate?: string; // 手次上牌日期
  licenseCode?: string; // 车牌号
  manufacturer?: string; // 厂商
  mileage?: string; // 里程数
  otherMsg?: string; // 其他
  seatsNum?: number; // 座位数
  totalPrice?: number; // 全包价
  carNo?: string;
}

interface ColorCity {
  color: string;
  cityCode: string;
  cityName: string;
}
