import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type {
  EditBuyBlackListParams,
  EditBuyParams,
  EditPayBlackListParams,
  EditRuleParams,
  QuertPayBlackListParams,
  QuertPayRuleParams,
  SwitchPayRuleConfigParams,
} from './types';

// 编辑垫付规则配置
export function editPayRule(params: EditRuleParams) {
  return request('/bizadmin/lease/guarantee/edit', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

export function getPayRuleList(params: QuertPayRuleParams) {
  return request('/bizadmin/lease/guarantee/queryList', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

//启用/禁用/删除垫付配置
export function changePayConfigStatus(params: SwitchPayRuleConfigParams) {
  return request('/bizadmin/lease/guarantee/updateStatus', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

// 编辑垫付黑名单配置
export function editPayBlackList(params: EditPayBlackListParams) {
  return request('/bizadmin/lease/guarantee/blacklist/edit', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

// 查询垫付黑名单配置列表
export function getPayBlackList(params: QuertPayBlackListParams) {
  return request('/bizadmin/lease/guarantee/blacklist/queryList', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

export function editBuyRule(params: EditBuyParams) {
  return request('/bizadmin/lease/buyBack/edit', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

export function editBuyBlackList(params: EditBuyBlackListParams) {
  return request('/bizadmin/lease/buyBack/blacklist/edit', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

export function getBuyRuleList(params: QuertPayRuleParams) {
  return request('/bizadmin/lease/buyBack/queryList', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

export function getBuyBlackList(params: QuertPayRuleParams) {
  return request('/bizadmin/lease/buyBack/blacklist/queryList', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}
