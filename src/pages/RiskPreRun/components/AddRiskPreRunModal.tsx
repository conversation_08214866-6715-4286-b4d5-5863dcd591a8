/**
 * @Date: 2021-09-29 14:59:31
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: Do not edit
 * @FilePath: Do not edit
 */
import React from 'react';
import ProForm, {
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormUploadDragger,
} from '@ant-design/pro-form';
import { message, Form } from 'antd';
import { downLoadExcel, getUuid } from '@/utils/utils';
import { useLockFn } from 'ahooks';
import { submitPrepareSingle, submitPrepareMultiple, downTemplateExample } from '../service';
import riskStyle from '../index.less';
import type { MultipleFormParam } from '../data.d';

export type AddRiskPreRunModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modelVisible?: boolean;
  type?: number;
  values?: {};
  onVisibleChange: any;
  title?: string;
};
enum PREPARE_STATUS {
  SINGLE, // 单个
  MULTIPLE, // 多个
}
const AddRiskPreRunModal: React.FC<AddRiskPreRunModalProps> = (props) => {
  const { modelVisible, onVisibleChange, type } = props;
  // let formData = new FormData();
  // formData.append('',JSON.)
  const [form] = Form.useForm();
  const downTemplate = (typeTask: string) => {
    downTemplateExample(typeTask || '0').then((res) => {
      downLoadExcel(res);
    });
  };
  return (
    <>
      <ModalForm
        title={`创建预跑任务-${type === PREPARE_STATUS.SINGLE ? '单个' : '批量'}`}
        className={riskStyle.formModalRisk}
        layout="horizontal"
        form={form}
        initialValues={{ productSecondCode: '0101', taskType: '0' }}
        visible={modelVisible}
        onVisibleChange={onVisibleChange}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            // handleShowMoreDetail(false);
            // setNewForm({});
          },
        }}
        onFinish={useLockFn(async (values: any) => {
          // console.log(values.dragger);

          if (type) {
            // eslint-disable-next-line @typescript-eslint/no-unused-expressions
            values.file &&
              values.file.forEach((item: any) => {
                // eslint-disable-next-line no-param-reassign
                values.file = item.originFileObj;
                return null;
              });
            const formData = new FormData();
            // console.log(dayjs().valueOf());
            const newValues = { ...values, timeCode: getUuid() };
            Object.keys(newValues).map((item) => formData.append(item, newValues[item]));
            // 多个
            await submitPrepareMultiple(formData as MultipleFormParam);
            message.success('添加成功');
            props.onOk();
          } else {
            // 单个
            await submitPrepareSingle({ ...values, timeCode: getUuid() });
            message.success('添加成功');
            props.onOk();
          }
        })}
      >
        <ProForm.Group>
          <ProFormText
            name="taskName"
            rules={[{ required: true }]}
            width="sm"
            fieldProps={{ maxLength: 50 }}
            label="任务名称"
            placeholder="请输入任务名称"
            // rules={[{ required: true }]}
          />
          <ProFormSelect
            rules={[{ required: true }]}
            options={[{ label: '明保', value: '0101' }]}
            placeholder="请选择产品二级分类"
            // initialValue="0101"
            name="productSecondCode"
            width="sm"
            label="产品二级分类"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: true }]}
            options={[{ label: '保理进件风控预跑', value: '0' }]}
            placeholder="请选择任务类型"
            // initialValue="0"
            name="taskType"
            width="sm"
            label="任务类型"
          />
          {!type && (
            <ProFormText
              name="enterpriseName"
              rules={[{ required: true }]}
              width="sm"
              fieldProps={{ maxLength: 50 }}
              label="企业名称"
              placeholder="请输入企业名称"
            />
          )}
        </ProForm.Group>
        {!type && (
          <ProForm.Group>
            <ProFormText
              name="enterpriseCode"
              width="sm"
              fieldProps={{ maxLength: 50 }}
              label="统一社会信用代码"
              placeholder="请输入统一社会信用代码"
              rules={[{ required: true }, { pattern: /^[0-9a-zA-Z]{18}$/, message: '格式不正确' }]}
            />
          </ProForm.Group>
        )}
        {type !== 0 && (
          <>
            <ProFormUploadDragger
              label="上传文件"
              // style={{ width: '100%' }}
              name="file"
              max={1}
              rules={[{ required: true }]}
              accept=".csv,.xls,.xlsx"
              fieldProps={{ listType: 'picture' }}
              title="点击或拖拽上传文件"
              description=""
            />
            <div className={riskStyle.textRight}>
              <a
                onClick={() => {
                  downTemplate(form?.getFieldValue('taskType'));
                }}
              >
                下载文件模板
              </a>
            </div>
          </>
        )}
      </ModalForm>
    </>
  );
};

export default React.memo(AddRiskPreRunModal);
