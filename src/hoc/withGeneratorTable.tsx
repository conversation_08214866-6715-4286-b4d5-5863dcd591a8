/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-11 14:20:17
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-12 14:25:26
 * @FilePath: lala-finance-biz-web/src/hoc/withGeneratorTable.tsx
 * @Description: 高阶组件，用以单模块复用多表格
 */
import type { ProColumns, ProTableProps } from '@ant-design/pro-table';
import React from 'react';
/**
 * 生成表格组件
 * @param Component 组件
 * @param rProps 组件的 外层透传props, 优先级比table props高
 * @param config table配置
 * @params props table组件的props
 * @returns
 */
export const withGeneratorTable = <RecordType extends Record<string, any>>(
  Component: React.FC<ProTableProps<RecordType, any>>,
  rProps: ProTableProps<RecordType, any>,
  config:
    | ProTableProps<RecordType, any>
    | {
        columns: ProColumns<any>[];
      },
) => {
  return (props: ProTableProps<RecordType, any>) => {
    return <Component {...props} {...rProps} {...config} />;
  };
};
