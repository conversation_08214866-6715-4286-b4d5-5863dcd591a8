/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-10-31 20:15:51
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/ProcessDetail/Overdue.tsx
 */
import { Col, Divider, Form, Input, Row, Select, Table, TimePicker } from 'antd';
// import {Link} from '@umijs/max'
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
// import {OverdueConfig} from '../../data';

const columns = [
  {
    title: '代偿项',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '代偿时间',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '代偿级别',
    dataIndex: 'address',
    key: 'address',
  },
  {
    title: '代偿机构',
    dataIndex: 'xxx',
    key: 'xxxx',
  },
  {
    title: '操作',
    render: () => {
      return <a>查看详情</a>;
    },
  },
];

type OverdueProps = {
  overDueData?: any;
  isLease?: boolean;
  name?: string;
};

const Overdue: React.FC<OverdueProps> = ({ overDueData, isLease, name }) => {
  // const { overDueData, isLease } = props;
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue({
      ...overDueData,
      overdueTime: overDueData?.overdueTime ? dayjs(overDueData?.overdueTime, 'HH:mm:ss') : null,
    });
  }, [overDueData]);
  const dataTable: any = [];
  return (
    <>
      <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 14 }} name={name}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="逾期时间" name="overdueTime">
              <TimePicker style={{ minWidth: '80%' }} disabled placeholder="24:00:00" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="坏账">
              <span>T+</span>
              <Form.Item name="badDebtDay" noStyle>
                <Input placeholder="90" style={{ width: '70%', marginLeft: '10px' }} disabled />
              </Form.Item>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="逾期罚息最大值" name="overduePenaltyInterest">
              {/* <Input placeholder="不超过本金的50%" style={{ width: '80%' }} disabled />
               */}
              <Select disabled style={{ width: '80%' }} placeholder="无">
                <Select.Option value="NO_MORE_THAN_PRINCIPAL_FIFTY_PERCENTAGE">
                  不超过本金的50%
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="催收单生成日期">
              <span>T+</span>
              <Form.Item name="overdueBillDay" noStyle>
                <Input placeholder="1" style={{ width: '70%', marginLeft: '10px' }} disabled />
              </Form.Item>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      {isLease && (
        <>
          <Divider type="vertical" style={{ borderLeft: '3px solid #1677ff', height: '1.5em' }} />
          <h3 style={{ display: 'inline-block' }}>逾期代偿配置</h3>
          <Table dataSource={dataTable} columns={columns} rowKey="address" />
        </>
      )}
    </>
  );
};

export default Overdue;
