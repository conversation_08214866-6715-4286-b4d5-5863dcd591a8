// 新单交车模块
import { DividerTit } from '@/components';
import ImagePreview from '@/components/ImagePreview';
import globalStyle from '@/global.less';
import {
  isChannelStoreUser,
  isExternalDesensitization,
  promiseGetBlob,
  saveAs,
  sliceArray,
} from '@/utils/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { Alert, Button, Col, Image, message, Row, Steps } from 'antd';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useAccess, useRequest } from 'umi';
import { allowShowDeliveryButtonStatus, CAR_DELIVERY_STATUS_MAP } from '../consts';
import type { BindCarInfo } from '../data';
import loanStyle from '../index.less';
import { newDeliveryAudit, queryDeliveryCarInfo } from '../service';
import AddDeliveryModal from './AddDeliveryModal';
import AddOtherDeliveryModal from './AddOtherDeliveryModal';
import ApprovedAddDeliveryModal from './ApprovedAddDeliveryModal';
type DeliveryProps = {
  data: BindCarInfo;
  orderNo: string;
  orderStatus: number;
  carDeliveryStatus: number; // 交车资料状态 0-未提交 1-超期未提交 2-待审核 3-审核通过 4-驳回
  refresh: () => void;
};

const { Step } = Steps;

const Delivery: React.FC<DeliveryProps> = (props) => {
  const { data, carDeliveryStatus, orderNo, refresh, orderStatus } = props;
  const mapOptType = {
    1: '提交信息',
    2: '驳回',
    3: '通过',
    20: '初审通过',
    21: '初审拒绝',
    40: '通过',
  };
  const [visibleAllHandleCar, handleVisibleAllHandleCar] = useState<boolean>(false); //  后台提交交车资料
  const [visibleHandleCar, handleVisibleHandleCar] = useState<boolean>(false); //  补充其他交车资料
  const [rejectModalVisible, setRejectModalVisible] = useState<boolean>(false);
  const [formEditHandlerCar, setFormEditHandlerCar] = useState<Record<string, any>>({});
  const approvedAddDeliveryModalRef = useRef<any>(null); //  补充交车资料
  const access = useAccess();

  const { data: deliveryCarInfo, run: runQueryDeliveryCarInfo } = useRequest(() => {
    return queryDeliveryCarInfo(orderNo);
  });
  // 交车图片数据
  const dataHandleResource = data?.newCarDeliveryInfo;

  const noCommitStatus = useMemo(() => {
    return [CAR_DELIVERY_STATUS_MAP.NOT_SUBMIT, CAR_DELIVERY_STATUS_MAP.OVER_SUBMIT];
  }, []);

  // 统一下交车资料状态，避免前后状态接口异常导致功能表现不一致
  const coverCarDeliveryStatus = carDeliveryStatus ?? deliveryCarInfo?.carDeliveryStatus; // 支持0

  // 是否展示错误状态 当交车资料状态为待审核且vin校验失败或不符合时
  const showError =
    coverCarDeliveryStatus === CAR_DELIVERY_STATUS_MAP.INITATED &&
    deliveryCarInfo?.vinOcrInfo &&
    (!deliveryCarInfo.vinOcrInfo?.validVin || !deliveryCarInfo.vinOcrInfo?.vinMatched);

  console.log('coverCarDeliveryStatus', coverCarDeliveryStatus);

  // 是否允许展示交车资料按钮，（未提交、超期未提交时，且交车资料为空，都允许提交资料）
  const getWhetherAllowShowDeliveryButton = useCallback(
    (latestDeliveryCarInfo: Record<string, any>) => {
      return (
        noCommitStatus.includes(coverCarDeliveryStatus) &&
        !(
          latestDeliveryCarInfo?.frameNumberPhoto ||
          latestDeliveryCarInfo?.groupPhotoOfPeopleAndVehicles
        ) &&
        allowShowDeliveryButtonStatus.includes(orderStatus)
      );
    },
    [coverCarDeliveryStatus, orderStatus, noCommitStatus],
  );

  // 审核交车
  const handlePassCarResource = () => {
    newDeliveryAudit({
      operation: true,
      orderNo,
    }).then(() => {
      refresh();
      runQueryDeliveryCarInfo();
      message.success('审核成功');
    });
  };

  // 下载
  const download = (url: string, filename: string) => {
    // downloadFileByElementA(url,filename)
    promiseGetBlob(url).then((blob) => {
      saveAs(blob, filename);
    });
  };
  console.log('新交车模块！', orderStatus);
  console.log(
    '交车状态！n p l',
    coverCarDeliveryStatus,
    carDeliveryStatus,
    deliveryCarInfo?.carDeliveryStatus,
  );
  // 已上传图片预览
  const DeliveryImagesPreview = React.useMemo(() => {
    console.log('DeliveryImagesPreview');
    let resource = dataHandleResource;
    // 上传了交车资料但是没提交（签约）的暂存回显场景
    if (
      ([CAR_DELIVERY_STATUS_MAP.NOT_SUBMIT, CAR_DELIVERY_STATUS_MAP.OVER_SUBMIT].includes(
        coverCarDeliveryStatus,
      ) ||
        !coverCarDeliveryStatus) &&
      (deliveryCarInfo?.frameNumberPhoto || deliveryCarInfo?.groupPhotoOfPeopleAndVehicles)
    ) {
      resource = deliveryCarInfo;
    }

    // 超期未提交
    if (coverCarDeliveryStatus === CAR_DELIVERY_STATUS_MAP.OVER_SUBMIT && !resource) {
      return (
        <p className={`${globalStyle.mt20} ${globalStyle.pl20}`} style={{ color: 'red' }}>
          超期未提交
        </p>
      );
    }

    if (!resource)
      return <p className={`${globalStyle.mt20} ${globalStyle.pl20}`}>暂未提交交车资料</p>;

    return (
      <>
        {coverCarDeliveryStatus === CAR_DELIVERY_STATUS_MAP.OVER_SUBMIT && (
          <p className={`${globalStyle.mt20} ${globalStyle.pl20}`} style={{ color: 'red' }}>
            超期未提交
          </p>
        )}
        <Row className={globalStyle.mt20} key="groupPhotoOfPeopleAndVehicles">
          <Col span={3}>
            <span className={`${globalStyle.fontWBold}  ${globalStyle.pl20}`}>人车合影照片</span>
          </Col>
          <Col span={21}>
            <Image
              width={200}
              height={200}
              key={resource?.groupPhotoOfPeopleAndVehicles}
              wrapperStyle={{ marginRight: 10, marginBottom: 20 }}
              src={
                isExternalDesensitization(access)
                  ? 'https://static.huolala.cn/image/9bd72ae3cdaf2dc24cb4ab4d0264c62945f87470.png'
                  : resource?.groupPhotoOfPeopleAndVehicles
              }
            />
          </Col>
        </Row>
        <Row className={globalStyle.mt20} key="frameNumberPhoto">
          <Col span={3}>
            <span className={`${globalStyle.fontWBold}  ${globalStyle.pl20}`}>车架号照片</span>
          </Col>
          <Col span={21}>
            <Image
              width={200}
              height={200}
              key={resource?.frameNumberPhoto}
              wrapperStyle={{ marginRight: 10, marginBottom: 20 }}
              src={
                isExternalDesensitization(access)
                  ? 'https://static.huolala.cn/image/9bd72ae3cdaf2dc24cb4ab4d0264c62945f87470.png'
                  : resource?.frameNumberPhoto
              }
            />
            {deliveryCarInfo?.vinOcrInfo && (
              <div className={loanStyle.ocrInfo}>
                <span>
                  车架号：
                  <span style={{ color: !deliveryCarInfo.vinOcrInfo?.vinMatched ? 'red' : '' }}>
                    {deliveryCarInfo.vinOcrInfo?.vinCode || '-'}
                  </span>
                </span>
                <span>
                  vin校验规则：
                  <span style={{ color: !deliveryCarInfo.vinOcrInfo?.validVin ? 'red' : 'green' }}>
                    {deliveryCarInfo?.vinOcrInfo.validVin ? '校验通过' : '校验失败'}
                  </span>
                </span>
              </div>
            )}
          </Col>
        </Row>
        <Row className={globalStyle.mt20} key="otherPhoto">
          <Col span={3}>
            <span className={`${globalStyle.fontWBold}  ${globalStyle.pl20}`}>其他：</span>
          </Col>
          <Col span={21}>
            {!isExternalDesensitization(access) &&
              resource?.otherDeliveryDataPhoto?.map((item: any, index: number) => {
                return (
                  <div
                    key={item.otherDeliveryDataPhoto + item.name}
                    className={loanStyle?.showAttatment}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      maxWidth: 500,
                    }}
                  >
                    <ImagePreview
                      url={item.otherDeliveryDataPhoto}
                      urlList={dataHandleResource?.otherDeliveryDataPhoto?.map(
                        (item) => item.otherDeliveryDataPhoto,
                      )}
                      fileName={
                        item.name ||
                        `${index + 1}.${item?.otherDeliveryDataPhoto?.substring(
                          item?.otherDeliveryDataPhoto.lastIndexOf('.') + 1,
                        )}`
                      }
                    >
                      <a
                        title={
                          ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'pdf'].includes(
                            item?.name?.substring(item?.name.lastIndexOf('.') + 1),
                          )
                            ? '预览'
                            : '下载'
                        }
                      >
                        {item?.name ||
                          `${index + 1}.${item?.otherDeliveryDataPhoto?.substring(
                            item?.otherDeliveryDataPhoto.lastIndexOf('.') + 1,
                          )}`}
                      </a>
                    </ImagePreview>

                    <div style={{ display: 'flex', gap: 20 }}>
                      <DownloadOutlined
                        className={loanStyle?.iconShow}
                        title="下载"
                        onClick={() =>
                          download(
                            item.otherDeliveryDataPhoto,
                            item.name ||
                              `${index + 1}.${item?.otherDeliveryDataPhoto?.substring(
                                item?.otherDeliveryDataPhoto.lastIndexOf('.') + 1,
                              )}`,
                          )
                        }
                        style={{ color: '#1677ff' }}
                      />
                    </div>
                  </div>
                );
              })}
          </Col>
        </Row>
        <Row className={globalStyle.mt20} key="remark">
          <Col span={3}>
            <span className={`${globalStyle.fontWBold}  ${globalStyle.pl20}`}>备注:</span>
          </Col>
          <Col span={21}>{resource.remark}</Col>
        </Row>
      </>
    );
  }, [access, dataHandleResource, coverCarDeliveryStatus, deliveryCarInfo]);

  return (
    <>
      {/* 已上传交车资料未完成交车单签约流程时展示提示 */}
      {noCommitStatus.includes(coverCarDeliveryStatus) &&
        (deliveryCarInfo?.frameNumberPhoto || deliveryCarInfo?.groupPhotoOfPeopleAndVehicles) && (
          <div>
            <Alert
              showIcon
              message={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      textAlign: 'left',
                      width: '90%',
                      flex: 1,
                      marginLeft: 10,
                      fontSize: 12,
                    }}
                  >
                    <span style={{ color: 'red' }}>
                      交车资料已上传，待客户完成签约单签署后进入交车资料审核
                    </span>
                  </div>
                </div>
              }
              type="warning"
            />
          </div>
        )}
      <DividerTit title="交车资料">
        {/* 订单状态：除了70、71，签约之后的订单且未操作交车，则后台展示交车资料按钮 */}
        {getWhetherAllowShowDeliveryButton(deliveryCarInfo) && (
          <Button
            className={globalStyle.ml10}
            onClick={async () => {
              // 补充打开modal前，先判断是否已上传交车资料
              const res = await runQueryDeliveryCarInfo();
              if (getWhetherAllowShowDeliveryButton(res)) {
                handleVisibleAllHandleCar(true);
              } else {
                message.error('交车资料已上传，请勿重复提交', 3);
                refresh();
                runQueryDeliveryCarInfo();
              }
            }}
          >
            交车资料
          </Button>
        )}
        {/* carDeliveryStatus = 2 待审核 */}
        {coverCarDeliveryStatus === CAR_DELIVERY_STATUS_MAP.INITATED && (
          <>
            {access.hasAccess('action_delivery_audit_businessMng_leaseList') && (
              <>
                <Button
                  className={globalStyle.ml10}
                  onClick={() => {
                    handlePassCarResource();
                  }}
                >
                  通过
                </Button>
                <Button
                  className={globalStyle.ml10}
                  onClick={() => {
                    setRejectModalVisible(true);
                  }}
                >
                  驳回
                </Button>
              </>
            )}
          </>
        )}
        {/* carDeliveryStatus = 2 / 4 提交资料，驳回（驳回时候运营不展示，渠道门店可以展示） */}
        {(CAR_DELIVERY_STATUS_MAP.INITATED === coverCarDeliveryStatus ||
          (CAR_DELIVERY_STATUS_MAP.REJECT === coverCarDeliveryStatus &&
            isChannelStoreUser(access))) && (
          <>
            <Button
              // type='primary'
              className={globalStyle.ml10}
              onClick={() => {
                setFormEditHandlerCar({
                  otherDeliveryDataPhotoList: data.newCarDeliveryInfo?.otherDeliveryDataPhoto,
                });
                handleVisibleHandleCar(true);
              }}
            >
              补充其他交车资料
            </Button>
          </>
        )}
        {/* carDeliveryStatus = 3，审核通过，可以提交补充交车资料，权限只分配给交车资料超管 */}
        {coverCarDeliveryStatus === CAR_DELIVERY_STATUS_MAP.ADOPT &&
          access.hasAccess('addCarDeliveryInfo_biz_businessMng_leaseList') && (
            <>
              <Button
                className={loanStyle.addDeliveryBtn}
                onClick={() => {
                  setFormEditHandlerCar({
                    otherDeliveryDataPhotoList: data.newCarDeliveryInfo?.otherDeliveryDataPhoto,
                    ...(deliveryCarInfo || {}),
                  });
                  approvedAddDeliveryModalRef.current?.show();
                }}
              >
                补充交车资料
              </Button>
            </>
          )}
      </DividerTit>
      {/* 交车资料进度条 */}
      {data?.newCarDeliveryInfo?.auditLogs.length && (
        <div className={`${globalStyle.mt20}  ${globalStyle.ml20}`}>
          {sliceArray(data?.newCarDeliveryInfo?.auditLogs, 6).map((sonArr: any[], index) => (
            <Steps
              progressDot
              current={sonArr.length}
              className={`${globalStyle.stepsInitial} ${globalStyle.mb20}`}
              key={`${Math.random()}-${index + 1}`}
            >
              {sonArr.map(
                (item: {
                  type: number;
                  operatorTime: string;
                  operatorBy: string;
                  rejectMsg: string;
                }) => {
                  return (
                    <Step
                      title={mapOptType[item.type]}
                      key={`${mapOptType[item.type]} ${item.operatorTime || '-'} ${
                        item.operatorBy || '-'
                      }`}
                      status={showError && item?.type === 1 ? 'error' : 'finish'} //  提交 时展示错误状态
                      description={
                        <div>
                          <div>
                            {item.operatorTime || '-'} {item.operatorBy || '-'}
                          </div>
                          {item.rejectMsg && (
                            <div style={{ color: 'red' }}>(原因：{item.rejectMsg})</div>
                          )}
                        </div>
                      }
                    />
                  );
                },
              )}
            </Steps>
          ))}
        </div>
      )}
      {/* 车辆图片信息 */}
      {/* <DeliveryImagesPreview /> */}
      {DeliveryImagesPreview}
      {/* 交车资料 */}
      <AddDeliveryModal
        modalVisible={visibleAllHandleCar}
        licenseCode={data?.licenseCode}
        orderNo={orderNo}
        onOk={async () => {
          handleVisibleAllHandleCar(false);
          refresh();
          runQueryDeliveryCarInfo();
        }}
        onCancel={() => {
          handleVisibleAllHandleCar(false);
        }}
        onVisibleChange={handleVisibleAllHandleCar}
      />
      {/* 补充其他交车资料 */}
      <AddOtherDeliveryModal
        modalVisible={visibleHandleCar}
        onOk={async () => {
          handleVisibleHandleCar(false);
          refresh();
          runQueryDeliveryCarInfo();
        }}
        formEdit={formEditHandlerCar}
        onCancel={() => {
          handleVisibleHandleCar(false);
        }}
        // isNewCar={props.deadLine}
        onVisibleChange={handleVisibleHandleCar}
      />
      {/* 补充交车资料 */}
      <ApprovedAddDeliveryModal
        modalRef={approvedAddDeliveryModalRef}
        onOk={async () => {
          approvedAddDeliveryModalRef.current?.hide();
          refresh();
          runQueryDeliveryCarInfo();
        }}
        formEdit={formEditHandlerCar}
      />
      {/* 驳回 */}
      <ModalForm
        title="驳回"
        width={600}
        layout="horizontal"
        visible={rejectModalVisible}
        onVisibleChange={setRejectModalVisible}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {},
        }}
        onFinish={async (values) => {
          try {
            const params = {
              operation: false,
              orderNo,
              rejectMsg: values.rejectMsg,
            };
            await newDeliveryAudit(params);
            refresh();
            runQueryDeliveryCarInfo();
            message.success('审核成功');

            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <ProFormText
          name="rejectMsg"
          labelCol={{ span: 4 }}
          width="md"
          fieldProps={{ maxLength: 20 }}
          label="驳回原因"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
      </ModalForm>
    </>
  );
};

export default Delivery;
