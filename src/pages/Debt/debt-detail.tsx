/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-04-28 15:31:16
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Debt/debt-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab/index';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import React from 'react';
import DebtInfo from './components/DebtInfo';
import DebtLoanInfo from './components/DebtLoanInfo';
import { getDebtInfo, getDebtLoanInfo } from './service';

const LoanDetail: React.FC<any> = () => {
  const { debtNo } = history.location.query;
  const { data } = useRequest(() => {
    return getDebtInfo(debtNo);
  });
  const { data: dataInfo } = useRequest(() => {
    return getDebtLoanInfo(debtNo);
  });
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <DebtInfo dataInfo={data} />
        <DebtLoanInfo dataInfo={dataInfo} />
      </PageContainer>
    </>
  );
};

export default LoanDetail;
