/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2025-06-05 15:58:29
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/service.ts
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { LatchPlanProps } from './components/CarInfo/LeaseScheme/types';
import type {
  AuditParams,
  BindCarParams,
  CarInfoParamsV2,
  CarOcrParams,
  CheckCarParams,
  DeliveryCarParams,
  ErrorDataParams,
  FinalRiskParams,
  NewDeliveryAuditParams,
  OffLineParams,
  OperateLogParams,
  OrderListParams,
  OtherDeliveryParams,
  PriceParams,
  ReCalcParams,
  RegistList,
  RentCarPlanParams,
  SaveDeliveryInfoParams,
  SubmitOtherDeliveryParams,
  submitRiskPatchParams,
  TempParams,
  ThreeVerifyData,
  UserSchemeParams,
} from './data';

// 订单列表
export async function queryOrder(params?: OrderListParams) {
  return request('/bizadmin/lease/order/list', {
    params,
    headers: bizAdminHeader,
    ifTrimParams: true,
    sensitiveSwitch: true,
  });
}

// 订单详情
export async function queryOrderDetail(orderNo: string) {
  return request(`/bizadmin/lease/order/detail/${orderNo}`, {
    headers: bizAdminHeader,
  });
}

// 订单扩展
// export async function queryOrderExtend(orderNo: string) {
//   return request(`/loan/user/order/extend/${orderNo}`);
// }
export async function queryOrderExtend(orderNo: string) {
  return request(`/bizadmin/lease/order/extend/${orderNo}`, {
    headers: bizAdminHeader,
  });
}

// 订单管理导出
export async function orderExport(params: OrderListParams) {
  return request(`/bizadmin/lease/order/export`, {
    params,
    headers: bizAdminHeader,
    ifTrimParams: true,
    sensitiveSwitch: true,
  });
}

// 审核交车资料
export async function audit(data: { auditType: number; operation: boolean; orderNo: string }) {
  return request(`/bizadmin/lease/car/audit`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 订单页绑车
export async function bindingCar(data: BindCarParams) {
  return request(`/bizadmin/lease/car/bindingCar`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 出厂日期校验
export async function carFabricateDateCheck(data: CheckCarParams) {
  return request(`/bizadmin/risk/car/carFabricateDateCheck`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 提交交车资料
export async function deliveryCarAdd(data: DeliveryCarParams) {
  return request(`/loan/user/order/deliveryCarAdd`, {
    method: 'POST',
    data,
  });
}

//补充其他交车资料
export async function addOtherDeliveryInfo(data: OtherDeliveryParams) {
  return request(`/bizadmin/lease/delivery/addDeliveryCarOtherInformation`, {
    method: 'POST',
    headers: bizAdminHeader,
    data,
  });
}

// 获取
export async function getOssPath(ossPath: string) {
  return request(`/repayment/getNetUrl`, {
    method: 'GET',
    params: { ossPath },
  });
}

export async function getOrderDudectDetail(orderNo: string, costType: number) {
  return request(`/loan/user/order/getOrderDudectDetail/${orderNo}/${costType}`);
}

export async function orderStatus(orderNo: string) {
  return request(`/bizadmin/lease/order/statusLogs/${orderNo}`, {
    headers: bizAdminHeader,
  });
}

export async function getAllChannelByChannelType(channelType: number) {
  return request(`/bizadmin/channel/lease/getAllChannelByChannelType`, {
    method: 'get',
    params: { channelType },
    headers: bizAdminHeader,
  });
}

export async function validateCarCodeEqual(
  newApplyCityId: string | number | undefined,
  newCarNo: string,
  orderNo: string,
) {
  return request(`/loan/user/order/checkCarNoBindCar`, {
    method: 'get',
    params: {
      newApplyCityId,
      newCarNo,
      orderNo,
    },
    skipErrorHandler: true,
  });
}

export async function getLicenseCityEnum() {
  return request(`/bizadmin/lease/licenseCity/getLicenseCityEnum`, {
    headers: bizAdminHeader,
  });
}

// 根据车型码获取车辆优惠信息
export async function getPromotionByCarNoAndApplyCityCode(params: {
  applyCityCode: string;
  carNo: string;
}) {
  return request(`/bizadmin/lease/car/getPromotionByCarNoAndApplyCityCode`, {
    method: 'get',
    params,
    headers: bizAdminHeader,
  });
}

export async function revokeOrder(orderNo: string, revokeReason: string) {
  return request(`/loan/user/order/revoke/${orderNo}`, {
    method: 'POST',
    data: { revokeReason },
    skipErrorHandler: true,
  });
}

/**
 * 查询更换银行卡日志
 * @param orderNo
 */
export async function getChangeBankRecord(orderNo: string) {
  return request(`/loan/user/order/get/card/log/${orderNo}`, {
    method: 'GET',
  });
}

/**
 * 新的交车审核
 * @param data
 */
export async function newDeliveryAudit(data: NewDeliveryAuditParams) {
  return request('/bizadmin/lease/car/auditDeliveryCar', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

/**
 * 获取还款记录
 * @param {string} repayPlanNo
 */
export async function getRecord(params: any) {
  return request(`/repayment/offlineRemit/getRecord`, {
    method: 'GET',
    params,
  });
}

/**
 * @Date: 2022-02-14 16:29:13
 * @Author: elisa.zhao
 * 提交线下还款申请
 * @param {RegistList} data
 */
export async function applyRepay(data: RegistList) {
  return request(`/repayment/offlineRemit/apply`, {
    method: 'POST',
    data,
  });
}

// 合格证ocr接口
export async function getCarOcrInfo(params: CarOcrParams) {
  return request('/loan/user/order/getCarInfo', {
    method: 'POST',
    data: params,
  });
}
// 降额待确认接受/拒绝
export async function manualAudit(params: AuditParams) {
  return request(`/bizadmin/lease/order/confirmReduceAmount/${params.orderNo}`, {
    method: 'POST',
    data: { confirmResult: params.confirmResult },
    headers: bizAdminHeader,
  });
}

//车辆解绑
export async function unbindCar(orderNo: string) {
  return request(`/bizadmin/lease/car/carUnbinding?orderNo=${orderNo}`, {
    method: 'POST',
    headers: bizAdminHeader,
  });
}

// 筛选可以发送交车催促短信的订单
export async function getSendMsgOrder(orderList: string[]) {
  return request('/bizadmin/lease/order/getSendMsgOrder', {
    method: 'GET',
    params: { orderList },
    headers: bizAdminHeader,
  });
}

// 导出筛选出的催交车订单列表
export async function exportSendMsgExcel(orderList: string[]) {
  return request('/bizadmin/lease/order/downloadSendMsgOrder', {
    responseType: 'blob',
    params: { orderList },
    getResponse: true,
    headers: bizAdminHeader,
  });
}

// 选中的订单发送短信
export async function sendMsgOrder(orderList: string[]) {
  return request('/bizadmin/lease/order/sendMsgOrder', {
    method: 'GET',
    params: { orderList },
    headers: bizAdminHeader,
  });
}

// 预审校验
export async function preApprovalCheck(orderList: string) {
  return request('/bizadmin/lease/order/preApproval/check', {
    method: 'POST',
    params: { orderList },
    headers: bizAdminHeader,
  });
}

// 预审提交
export async function preApprovalSubmit(orderList: string) {
  return request('/bizadmin/lease/order/preApproval/submit', {
    method: 'POST',
    params: { orderList },
    headers: bizAdminHeader,
  });
}

// 二期改造，还款信息查询接口
export async function getOfflineRepayInfo(orderNo: string) {
  return request(`/bizadmin/repayment/cms/bill/repay/info/${orderNo}`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
//二期改造，提交线下还款接口
export async function submitOfflineRepay(data: OffLineParams) {
  return request(`/bizadmin/repayment/offlineRemit/apply/v2`, {
    method: 'POST',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
// 二期改造，获取减免项信息
export async function getOfflineRepayRemission(orderNo: string) {
  return request(`/bizadmin/repayment/offlineRemit/getRemissionDetail/${orderNo}`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

//***** 进件接口  ******/

/**
 * 三要素提交认证
 * @param data
 */
export function threeVerify(data: ThreeVerifyData) {
  return request('/bizadmin/lease/certification/verify', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

/**
 * 根据用户信息拿出已选车方案 如果返回null 证明还未选车
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function getUserCarInfo(orderNo: string) {
  return request('/bizadmin/lease/car/getCallAllInfo', {
    params: { orderNo },
    headers: bizAdminHeader,
  });
}

/**
 * 获取渠道数据
 */
export function getChannel(params: { channelType?: string; orderNo: string }) {
  return request('/bizadmin/lease/car/channel', {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 根据汽车唯一ID查询汽车信息 V2
 * @param params
 */
export function getCarInfoByIdV2(params: CarInfoParamsV2) {
  return request('/bizadmin/lease/car/carInfoByCarIdV2', {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 查询新车 二手车级联关系
 * @param type 1.新车 2. 二手车
 */
export function getCarBaseList(
  type: number,
  params: { channelId: number | string; orderNo: string; licenseType: number },
) {
  const url = `/bizadmin/lease/car/carNode/${type}`;
  return request(url, {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 上牌公司数据
 * @param channelId
 */
export function getCompanyList(channelId: number | string, licenseType: number) {
  return request('/bizadmin/channel/lease/license/list', {
    params: { channelId, licenseType },
    headers: bizAdminHeader,
  });
}

/**
 * 申请门店数据
 * @param channelId
 */
export function getApplyStoreList(channelId: number | string) {
  return request('/bizadmin/channel/lease/area/list', {
    params: { channelId },
    headers: bizAdminHeader,
  });
}

/**
 * 根据车辆类型唯一id查询车辆优惠 转让价
 * @param params
 */
export function getPriceInfoByCarId(params: PriceParams) {
  return request('/bizadmin/lease/car/carPriceByCarId', {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 获取产品方案
 */
export function getProductList(params: LatchPlanProps) {
  return request('/bizadmin/lease/car/getLatchPlan', {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 拿取用户金融方案
 */
export function getUserScheme(params: UserSchemeParams) {
  return request('/bizadmin/lease/car/getFinancePlan', {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 获取定金下详细信息
 * @param orderNo
 */
export function getDepositDetail(orderNo: string) {
  return request('/bizadmin/lease/car/pay/deposit/detail', {
    params: { orderNo },
    headers: bizAdminHeader,
  });
}

/**
 * 查询待支付的费用
 * @param orderNo
 */
export function getPayFeeInfo(orderNo: string) {
  return request('/bizadmin/lease/car/pay/fee', {
    params: { orderNo },
    headers: bizAdminHeader,
  });
}

/**
 * 保存车辆租赁方案
 * @param params
 */
export function saveRentCarPlan(params: RentCarPlanParams) {
  return request('/bizadmin/lease/income/saveRentCarPlan', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

/**
 * 提交车辆租赁方案
 * @param params
 */
export function submitRentCarPlan(params: RentCarPlanParams) {
  return request('/bizadmin/lease/income/submitRentCarPlan', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

/**
 * 获取选过的金融方案
 * @param orderNo
 */
export function queryOrderFinancePlan(orderNo: string) {
  return request('/bizadmin/lease/car/queryOrderFinancePlan', {
    params: { orderNo },
    headers: bizAdminHeader,
  });
}

/**
 * 重新计算分期金额
 * @param params
 */
export function refreshCalculate(params: ReCalcParams) {
  return request('/bizadmin/lease/car/refreshFinancePlan', {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 获取暂存的风控字段
 */
export function getTempMetaData(orderNo: string) {
  return request('/bizadmin/lease/income/queryRiskField', {
    params: { orderNo },
    headers: bizAdminHeader,
  });
}

/**
 * 根据产品code获取进件配置,如果线上没有相应配置,则会获取到默认的配置
 * 默认的配置无法禁用,无法添加配置范围
 * 默认的配置是一条兜底的配置
 */
export function getEnterConfigByProductCode(productCode: string) {
  const url = `/bizadmin/risk/enter/config/getEnterConfigByProductCode`;
  const data = {
    productCode: '0401',
    productThirdCode: productCode || '',
  };
  return request(url, { method: 'POST', data, headers: bizAdminHeader });
}

/**
 * 获取身份识别信息
 */
export function getBasePersonalInfo(orderNo: string) {
  return request('/bizadmin/lease/income/user/info', {
    params: { orderNo },
    headers: bizAdminHeader,
  });
}

/**
 * 暂存进件字段
 * @param data
 */
export function temporaryStorage(data: TempParams) {
  return request('/bizadmin/lease/income/saveRiskControlInfo', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

/**
 * 最终提交进件个人信息字段
 * @param params
 */
export function submitFinalRiskField(params: FinalRiskParams[]) {
  return request('/bizadmin/lease/income/sendRiskField', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

/**
 * 补充/修改进件字段
 * @param params
 */
export function editFinalRiskField(params: FinalRiskParams[]) {
  return request('/bizadmin/lease/income/updateEnterFields', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
}

/**
 * 融租订单操作日志查询
 * @param params
 */
export function orderOperateLog(params: OperateLogParams) {
  return request('/bizadmin/lease/order/operateLogPage', {
    params,
    headers: bizAdminHeader,
  });
}

/**
 * 获取补件信息
 * @param params
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function getPatchData(orderNo: string) {
  return request('/bizadmin/risk/supplement/querySupplements', {
    method: 'POST',
    data: { orderNo },
    headers: {
      ...bizAdminHeader,
    },
  });
}

/**
 * 查询补件记录
 * @param orderNo
 */
export function getSupplementRecord(orderNo: string) {
  return request('/bizadmin/lease/income/getRiskSupplementField', {
    params: { orderNo },
    headers: {
      ...bizAdminHeader,
    },
  });
}

/**
 * 提交补件数据
 * @param data
 */
export function submitRiskPatch(data: submitRiskPatchParams) {
  return request('/bizadmin/lease/income/submitSupplement', {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

/**
 * 查询电联失败反馈信息
 * @param params
 */
export function getCallFailRecord(params: { riskOrderNo: string; status: number }) {
  return request('/bizadmin/risk/supplement/phoneContactFeedback', {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

/**
 * 查询交车资料
 */
export function queryDeliveryCarInfo(orderNo: string) {
  return request('/bizadmin/lease/delivery/queryDeliveryCarInfo', {
    params: { orderNo },
    headers: bizAdminHeader,
  });
}

/**
 * 保存/提交交车资料
 * @param data
 */
export function saveDeliveryCarInfo(data: SaveDeliveryInfoParams) {
  return request('/bizadmin/lease/delivery/saveDeliveryCarInfo', {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 放款信息
export async function getLoanInfo(orderNo: string, type?: string) {
  return request(`/quota/lending/getLendingInfoList/${orderNo}`, {
    method: 'GET',
    params: { type },
  });
}
// 分页查询差错数据
export function getErrorDataList(data: ErrorDataParams) {
  return request('/bizadmin/repayment/channel/errorData', {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 导出差错数据
export function asyncExportErrorData(data: ErrorDataParams) {
  return request('/bizadmin/repayment/channel/errorData/submit/export', {
    method: 'POST',
    data,
    responseType: 'blob',
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 计算提前结清违约金
export function getEarlyEndShouldMoney(orderNo: string) {
  return request('/bizadmin/repayment/early/settle/cal', {
    method: 'GET',
    params: { orderNo },
    headers: {
      ...bizAdminHeader,
    },
  });
}

//获取预审额度信息
export function preCreditAmountInfo(orderNo: string) {
  return request(`/bizadmin/lease/order/preCreditAmountInfo/${orderNo}`, {
    method: 'GET',
    // params: { orderNo },
    headers: {
      ...bizAdminHeader,
    },
  });
}

export function getNotraizationList(params: { productSecondTypeCode: string }) {
  return request(`/bizadmin/base/notarization/list`, {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

export function exportNotraizationList(params: { productSecondTypeCode: string }) {
  return request(`/bizadmin/base/notarization/export`, {
    method: 'POST',
    data: params,
    responseType: 'blob',
    headers: {
      ...bizAdminHeader,
    },
  });
}
// 重新推送资方渠道审核
export function repushChannelApprove(orderNo: string) {
  return request(`/bizadmin/lease/order/reFunderCredit/${orderNo}`, {
    method: 'POST',
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 检查是否展示重新推送按钮
export function checkIfShowRepushBtn(orderNo: string) {
  return request(`/bizadmin/lease/order/reFunderCreditCheck/${orderNo}`, {
    method: 'POST',
    headers: {
      ...bizAdminHeader,
    },
    skipGlobalErrorTip: true,
  });
}

// 车险车架号ocr
export function getCarInsuranceFrameNumberOcr(params: { orderNo: string; filePath: string }) {
  return request('/bizadmin/base/ocr/vehicleVinCodeOcr', {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 审核后提交补充交车资料
export function submitOtherDeliveryInfo(data: SubmitOtherDeliveryParams) {
  return request('/bizadmin/lease/delivery/updateDeliveryCarInfo', {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}
