/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2024-01-26 18:11:32
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/components/ShowInfo/index.tsx
 */
import globalStyle from '@/global.less';
import { Card, Col, Row, Typography } from 'antd';
import Tooltip from 'antd/es/tooltip';
import classNames from 'classnames';
import React from 'react';
import style from './index.less';

const styleShowInfo = {
  styleText: {
    overflow: 'hidden',
    maxWidth: 240,
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    display: 'inline-block',
    // lineHeight: 1,
    verticalAlign: 'bottom',
    cursor: 'pointer',
  },
  verticalLine: {
    verticalAlign: 'top',
  },
};
interface ShowInfoProps {
  data?: Record<string, any>;
  infoMap?: Record<string, any>;
  title?: React.ReactNode;
  itemMap?: Record<string, any>;
  selfDefine?: Record<string, React.ReactNode>;
  noCard?: boolean;
  rowSpan?: number;
  titleGap?: any;
  loading?: boolean;
  extra?: React.ReactNode;
  textClassName?: string;
  selfRowSpan?: Record<string, number>;
  selfDefineFunc?: Record<string, any>;
}

const ShowInfo: React.FC<ShowInfoProps> = (props) => {
  /**
   * @data 数据
   * @title card标题
   * @infoMap label-value对应关系
   * @itemMap 当value时数值，需要映射到额外的label时
   * @selfDefine 当有链接的时候单独处理
   * @loading: 加载中
   * @selfRowSpan 自定义占宽
   */
  const { Text } = Typography;
  const {
    data,
    infoMap,
    title,
    itemMap,
    selfDefine,
    loading,
    noCard,
    rowSpan,
    extra,
    selfRowSpan,
    textClassName,
    selfDefineFunc,
  } = props;
  const selfDefineFinial = { ...selfDefine, ...(selfDefineFunc || {}) };

  // console.log(selfDefineFinial, { ...(selfDefineFunc || {}) }, selfDefineFunc);
  const dom = () => (
    <Row className={globalStyle.pl20}>
      {/*
        infoMap 键值对 {status:"状态"}
       data[item] //数据值 {status:1}  有可能状态为-1数字，
       itemMap 要翻译的键值对  {status:{1:'失效'}}
      */}
      {Object.keys(infoMap || {}).map((item, index) => {
        const value =
          (data &&
            (itemMap && itemMap[item]
              ? itemMap[item][`${data[item]}`] //存在要翻译的枚举，直接先翻译 ,`${data[item]}`这里统一转为string的原因，status可能为-1数字，需要转成string,然后翻译
              : data[item] === 0 //接着判断item的值本来为0，要注意判断转为string，也要展示
              ? `${data[item]}` //展示值
              : data[item])) || //不存在展示为空
          '-';
        return (
          <Col span={selfRowSpan?.[item] || rowSpan || 8} key={item}>
            <div className={style.content}>
              <span style={styleShowInfo.verticalLine}>{infoMap?.[item]}:</span>
              {selfDefineFinial && selfDefineFinial[item] ? (
                <span className={style.selfText}>{selfDefineFinial[item]}</span>
              ) : (
                <>
                  <Tooltip title={value}>
                    <span className={classNames(style.textEllipsis, textClassName)}>{value}</span>
                  </Tooltip>
                  {value?.length > 10 && <Text copyable={{ text: value }} />}
                </>
              )}
            </div>
          </Col>
        );
      })}
    </Row>
  );
  return (
    <>
      {noCard ? (
        <>
          {dom()}
          {props.children}
        </>
      ) : (
        <Card title={title} className={globalStyle.mt30} loading={loading} extra={extra}>
          {dom()}
          {props.children}
        </Card>
      )}
    </>
  );
};

export default ShowInfo;
