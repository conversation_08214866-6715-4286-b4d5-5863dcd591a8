/*
 * @Date: 2024-03-07 14:20:14
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-08-20 16:42:59
 * @FilePath: /lala-finance-biz-web/src/pages/CarInsuranceChannel/components/CarInsuranceFeeModal.tsx
 * @Description:
 */

import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import { Button, Modal, Table, Transfer } from 'antd';
import { difference } from 'lodash';

export type TChannelInfoTransferProps = {
  notConfigData: string[];
  hasConfigData: string[];
  loading: boolean;
};

const CarInsuranceCompanyTransfer = forwardRef((props: TChannelInfoTransferProps, ref) => {
  const { notConfigData, hasConfigData, loading } = props;
  /**
   * 存储被勾选的 keys
   */
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  useEffect(() => {
    setTargetKeys(hasConfigData.map((item) => item.id));
    // console.log(targetKeys);
  }, [hasConfigData]);

  /**
   * 暴露 targetKeys 给父组件
   */
  useImperativeHandle(ref, () => ({
    targetKeys,
  }));
  const tableColumns = [
    {
      title: '',
      render: (item) => (
        <>
          {item.companyName}
          <br />
          {item.paymentAccountNo}
        </>
      ),
    },
  ];

  return (
    <Transfer
      rowKey={(row) => row?.id}
      targetKeys={targetKeys}
      dataSource={notConfigData}
      showSearch
      // showSelectAll={false}
      titles={['未选', '已选']}
      onChange={(nextTargetKeys: string[]) => setTargetKeys(nextTargetKeys)}
      filterOption={(
        inputValue: string,
        item: { paymentAccountNo: string; companyName: string },
      ) => {
        return (
          item?.paymentAccountNo?.indexOf(inputValue) > -1 ||
          item?.companyName?.indexOf(inputValue) > -1
        );
      }}
      listStyle={{ width: 400 }}
    >
      {({ filteredItems, onItemSelectAll, onItemSelect, selectedKeys: listSelectedKeys }) => {
        const rowSelection = {
          onSelectAll(selected: boolean, selectedRows: { id: string }[]) {
            const treeSelectedKeys = selectedRows.map(({ id }: { id: string }) => id);
            const diffKeys = selected
              ? difference(treeSelectedKeys, listSelectedKeys)
              : difference(listSelectedKeys, treeSelectedKeys);
            onItemSelectAll(diffKeys as string[], selected);
          },
          onSelect({ id }: { id: string }, selected: boolean) {
            onItemSelect(id, selected);
          },

          selectedRowKeys: listSelectedKeys,
        };
        return (
          <Table
            rowSelection={rowSelection}
            columns={tableColumns}
            loading={loading}
            showHeader={false}
            dataSource={filteredItems}
            size="small"
            onRow={({ id }) => ({
              onClick: () => {
                onItemSelect(id, !listSelectedKeys.includes(id));
              },
            })}
          />
        );
      }}
    </Transfer>
  );
});

type CarInsuranceCompanyTransferModalType = TChannelInfoTransferProps & {
  handleOk: (val) => void;
  visible: boolean;
  setVisible: (val: boolean) => void;
};

const CarInsuranceFeeModal = ({
  handleOk,
  notConfigData,
  hasConfigData,
  loading,
  visible,
  setVisible,
}: CarInsuranceCompanyTransferModalType) => {
  console.log(hasConfigData);
  const ref = useRef<any>();
  return (
    <Modal
      title="选择保费收款公司"
      open={visible}
      width={800}
      onCancel={() => {
        setVisible(false);
      }}
      footer={[
        <Button
          key="submit"
          type="primary"
          onClick={() => {
            setVisible(false);
            handleOk(ref?.current?.targetKeys);
          }}
        >
          确定
        </Button>,
      ]}
    >
      <CarInsuranceCompanyTransfer
        notConfigData={notConfigData}
        hasConfigData={hasConfigData}
        loading={loading}
        ref={ref}
      />
    </Modal>
  );
};
export default CarInsuranceFeeModal;
