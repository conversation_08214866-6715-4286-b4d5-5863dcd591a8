/*
 * @Date: 2023-07-17 11:12:26
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-07-24 15:09:37
 * @FilePath: /lala-finance-biz-web/src/pages/Reconciliation/components/ReconciliationRecord.tsx
 * @Description:
 */
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { ActionType, ProColumns, ProFormInstance, ProTable } from '@ant-design/pro-components';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import type { ReconciliationRecordList } from '../data';
import { getRecordList } from '../service';
import ConfirmReceiveModal from './ConfirmReceiveModal';

const ReconciliationRecord = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const columns: ProColumns<ReconciliationRecordList>[] = [
    {
      title: '调账流水号',
      key: 'reconciliationFlowNo',
      dataIndex: 'reconciliationFlowNo',
    },
    {
      title: '打款类型',
      key: 'reconciliationType',
      dataIndex: 'reconciliationType',
      valueEnum: {
        0: '默认值',
        1: '月结用户还款调账',
        2: '月结用户还款调账',
      },
      search: false,
    },
    {
      title: '付款主体姓名',
      key: 'remitAccountName',
      dataIndex: 'remitAccountName',
      search: false,
    },
    {
      title: '付款主体账号',
      key: 'remitAccountNo',
      dataIndex: 'remitAccountNo',
      search: false,
    },

    {
      title: '汇款金额（元）',
      key: 'remitAmount',
      dataIndex: 'remitAmount',
      search: false,
      render: (_, record) => {
        return Number(new BigNumber(record?.remitAmount as any).div(100));
      },
    },
    {
      title: '收款主体姓名',
      key: 'paymentAccountName',
      dataIndex: 'paymentAccountName',
      search: false,
    },
    {
      title: '收款主体账号',
      key: 'paymentAccountNo',
      dataIndex: 'paymentAccountNo',
      search: false,
    },

    {
      title: '发起时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateRange',
      initialValue: [
        dayjs().subtract(6, 'month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      fieldProps: {
        allowClear: false,
      },
      search: {
        transform: (value: any) => ({
          sendTimeStart: `${value[0]}`,
          sendTimeEnd: `${value[1]}`,
        }),
      },
      render: (_, record) => {
        return record?.createdAt;
      },
    },
    {
      title: '转账受理状态',
      dataIndex: 'sendStatus',
      key: 'sendStatus',
      valueEnum: {
        0: '待发送',
        1: '发送成功',
        2: '发送失败',
        3: '未知',
      },
    },
    {
      title: '转账结果状态',
      dataIndex: 'reconciliationStatus',
      key: 'reconciliationStatus',
      valueEnum: {
        0: '未知',
        1: '打款成功',
        2: '打款失败',
        3: '退票',
      },
    },
    {
      title: '转账结果原因',
      dataIndex: 'reconciliationMsg',
      key: 'reconciliationMsg',
      search: false,
    },
    {
      title: '到账状态',
      dataIndex: 'receiveStatus',
      key: 'receiveStatus',
      valueEnum: {
        0: '未到账',
        1: '已到账',
      },
    },
    {
      title: '到账银行流水号',
      dataIndex: 'receiveBankNo',
      key: 'receiveBankNo',
    },
    {
      title: '到账时间',
      dataIndex: 'receiveTime',
      key: 'receiveTime',
      // renderFormItem,
      valueType: 'dateRange',
      // initialValue: [
      //   dayjs().subtract(6, 'month').format('YYYY-MM-DD'),
      //   dayjs().format('YYYY-MM-DD'),
      // ],
      // 最近半年
      // initialValue: [
      //   dayjs().subtract(6, 'month').format('YYYY-MM-DD'),
      //   dayjs().format('YYYY-MM-DD'),
      // ],
      search: {
        transform: (value: any) => ({
          receiveTimeStart: `${value[0].split(' ')[0]}`,
          receiveTimeEnd: `${value[1].split(' ')[0]}`,
        }),
      },
      render: (_, record) => {
        return record?.receiveTime;
      },
    },
    {
      title: '操作时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => {
        const { remitAmount, reconciliationFlowNo, receiveStatus } = record;
        //已到账不展示
        return receiveStatus === 1 ? (
          ''
        ) : (
          <>
            <a
              onClick={() => {
                optimizationModalWrapper(ConfirmReceiveModal)({
                  remitAmount,
                  reconciliationFlowNo,
                  onOk: () => {
                    actionRef?.current?.reload();
                  },
                });
              }}
            >
              到账确认
            </a>
          </>
        );
      },
    },
  ];
  return (
    <div>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        rowKey="repayNo"
        search={{ labelWidth: 120, defaultCollapsed: false }}
        scroll={{ x: 'max-content' }}
        request={(params) =>
          //处理下有可能产品一级分类没赋值成功
          getRecordList(params)
        }
        dateFormatter="string"
        columns={columns}
      />
    </div>
  );
};

export default ReconciliationRecord;
