import { SECONDARY_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_MAP_ALL } from '@/enums';
import { ExclamationCircleOutlined } from '@ant-design/icons'; // @ts-ignore
import type { ActionType } from '@ant-design/pro-components'; // @ts-ignore
import { ModalForm } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Descriptions, Spin, Table } from 'antd';
import type { MutableRefObject } from 'react';
import React, { memo, useState } from 'react';
import { PAY_TYPE } from '../../const';
// import { OnlineRepayReviewModalColumns } from '../columns/OnlineRepayReviewModalColumns';
import { Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { applyOrderInfo } from '../services';
import type { IbillRspDTOItem } from '../types';
import { IapplyOrderInfo, IofflineRepayReviewItem, payStatusMap, repayStatusMap } from '../types';
import './index.less';

type Props = {
  record?: IofflineRepayReviewItem;
  onlineRepayReviewOpen: boolean;
  setOnlineRepayReviewOpen: (val: boolean) => void;
  setRemissionReviewOpen?: (val: boolean) => void;
  actionRef: MutableRefObject<ActionType | undefined>;
  setQrData: (val: any) => void;
  setQrVisible: (val: boolean) => void;
};

const ACTUALREPAYROL_EMAP = {
  //  实际还款方
  '1': '用户本人',
  '2': '第三方',
};
// 线上还款的详情弹窗（收银台支付，回款余额支付）
const OnlineRepayReviewModal: React.FC<Props> = (props) => {
  const {
    record,
    onlineRepayReviewOpen,
    setOnlineRepayReviewOpen,
    setRemissionReviewOpen,
    setQrData,
    setQrVisible,
  } = props;
  const [loading, setLoading] = useState(false);
  const [applyOrderInfoData, setApplyOrderInfoData] = useState<IapplyOrderInfo>();
  const access = useAccess();

  async function getApplyOrderInfo() {
    try {
      if (record?.businessNo) {
        setLoading(true);
        const data: any = await applyOrderInfo({ businessNo: record?.businessNo });
        try {
          if (data?.payRspInfo) {
            data.payRspInfo = JSON.parse(data?.payRspInfo);
          }
        } catch (e) {}
        console.log('data', data);
        setApplyOrderInfoData(data);
        setOnlineRepayReviewOpen(true);
      }
    } finally {
      setLoading(false);
    }
  }
  // 是否隐藏期数
  // 车险分期、账单维度为总账，隐藏
  const hideTermNumber =
    applyOrderInfoData?.secondProductCode === SECONDARY_CLASSIFICATION_CODE?.CAR_INSURANCE &&
    applyOrderInfoData?.billRspDTOList?.[0]?.dimension === 3;
  //
  const OnlineRepayReviewModalColumns: ColumnsType<IbillRspDTOItem> = [
    {
      title: '账单ID',
      dataIndex: 'billNo',
    },
    {
      title: '期数',
      dataIndex: 'termNumber',
      hidden: hideTermNumber,
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '车辆识别代码',
      dataIndex: 'subjectMatterNo',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record?.subjectMatterNoList?.map((subjectMatterNo) => {
                  return <div>{subjectMatterNo}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.subjectMatterNo}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '账单状态',
      dataIndex: 'statusName',
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
    },
    {
      title: '应还总额',
      dataIndex: 'totalAmountDue',
    },
    {
      title: '应还本金',
      dataIndex: 'totalPrincipalDue',
    },
    {
      title: '应还利息',
      dataIndex: 'totalInterestDue',
    },

    {
      title: '逾期罚息',
      dataIndex: 'totalOverduePenaltyDue',
    },
    {
      title: '还款金额（元）',
      dataIndex: 'expectRepayAmount',
      render: (_, record: any) => {
        return record?.expectRepayAmount || '-';
      },
    },
    // {
    //   title: '还款顺序',
    //   dataIndex: 'index',
    //   render: (_, record, index) => {
    //     return <div>{index + 1}</div>;
    //   },
    // },
    {
      title: '客户名称',
      dataIndex: 'accountName',
    },
    {
      title: '未还总额',
      dataIndex: 'totalAmountUnpaid',
    },
    {
      title: '未还本金',
      dataIndex: 'totalPrincipalUnpaid',
    },
    {
      title: '未还利息',
      dataIndex: 'totalInterestUnpaid',
    },
    {
      title: '未还罚息',
      dataIndex: 'totalOverduePenaltyUnpaid',
    },
    {
      title: '未还违约金',
      dataIndex: 'totalBreach',
    },
    {
      title: '未还滞纳金',
      dataIndex: 'totalLate',
    },
    {
      title: '应还违约金',
      dataIndex: 'totalBreachDue',
    },

    {
      title: '应还滞纳金',
      dataIndex: 'totalLateDue',
    },
    {
      title: '更新日期',
      dataIndex: 'updatedAt',
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDateNumber',
    },
  ];
  return (
    <>
      <ModalForm
        title={<div className="modal_tit">还款单详情</div>}
        open={onlineRepayReviewOpen}
        onOpenChange={(val) => {
          if (val) getApplyOrderInfo();
        }}
        width={1000}
        modalProps={{
          destroyOnClose: true,
          maskClosable: true,
          onCancel: () => {
            setOnlineRepayReviewOpen(false);
          },
        }}
        submitter={false}
      >
        <Spin spinning={loading}>
          <div className="block_area">
            <Descriptions bordered column={2} size={'small'}>
              <Descriptions.Item span={2} label={<div className="label_width">还款单号</div>}>
                {applyOrderInfoData?.businessNo || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">业务线</div>}>
                {SECONDARY_CLASSIFICATION_MAP_ALL[
                  applyOrderInfoData?.secondProductCode as string
                ] || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">还款单金额</div>}>
                {applyOrderInfoData?.repayAmount || '-'} 元
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">支付方式</div>}>
                {PAY_TYPE?.[applyOrderInfoData?.remitType as keyof typeof PAY_TYPE] || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">还款二维码</div>}>
                {applyOrderInfoData?.payRspInfo ? (
                  <a
                    onClick={() => {
                      setQrData({
                        h5RepayUrl: applyOrderInfoData?.payRspInfo?.h5RepayUrl,
                        businessNo: applyOrderInfoData?.businessNo,
                        accountName: applyOrderInfoData?.accountName,
                        isCarInsurance:
                          applyOrderInfoData?.secondProductCode ===
                          SECONDARY_CLASSIFICATION_CODE?.CAR_INSURANCE,
                      });
                      setQrVisible(true);
                    }}
                  >
                    查看
                  </a>
                ) : (
                  '-'
                )}
              </Descriptions.Item>
              {/* 车险不展示融租渠道 */}
              {applyOrderInfoData?.secondProductCode ===
                SECONDARY_CLASSIFICATION_CODE?.FINANCE_LEASE_SECOND && (
                <Descriptions.Item span={1} label={<div className="label_width">融租渠道</div>}>
                  {applyOrderInfoData?.channelName || '-'}
                </Descriptions.Item>
              )}
              <Descriptions.Item span={1} label={<div className="label_width">支付状态</div>}>
                {payStatusMap[applyOrderInfoData?.payStatus as string] || '-'}
              </Descriptions.Item>
              <Descriptions.Item
                span={1}
                label={<div className="label_width">支付返回结果时间</div>}
                style={{ whiteSpace: 'nowrap' }}
              >
                {applyOrderInfoData?.creditDate || '-'}
              </Descriptions.Item>
              <Descriptions.Item
                span={1}
                label={<div className="label_width">短信下发手机号</div>}
                style={{ whiteSpace: 'nowrap' }}
              >
                {applyOrderInfoData?.notifyPhone || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">还款状态</div>}>
                {repayStatusMap[applyOrderInfoData?.repayStatus as number] || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">提交人</div>}>
                {applyOrderInfoData?.applyName || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">创建时间</div>}>
                {applyOrderInfoData?.createdAt || '-'}
              </Descriptions.Item>
              {/* 车险不展示实际还款方 */}
              {applyOrderInfoData?.secondProductCode ===
                SECONDARY_CLASSIFICATION_CODE?.FINANCE_LEASE_SECOND && (
                <Descriptions.Item span={1} label={<div className="label_width">实际还款方</div>}>
                  {ACTUALREPAYROL_EMAP[applyOrderInfoData?.actualRepayRole as string] || '-'}
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
          <div className="tit">账单信息:</div>
          <div className="block_area bill_info_wrao">
            <Table
              title={() => (
                <>
                  <ExclamationCircleOutlined
                    style={{ fontSize: 16, color: 'orange', marginRight: 5 }}
                  />
                  以下为操作还款时，账单快照数据。
                </>
              )}
              pagination={false}
              columns={OnlineRepayReviewModalColumns}
              dataSource={applyOrderInfoData?.billRspDTOList}
              scroll={{ x: 'max-content' }}
            />
          </div>
          {access.hasAccess('bill_apply_remission_info_postLoanMng_callpay') && (
            <>
              <div className="tit">减免信息:</div>
              <div className="block_area">
                <div className="line">
                  <span className="" style={{ marginRight: 60, marginLeft: 18 }}>
                    减免单号: {/* 无需减免的不做跳转，无减免单信息 */}
                    {applyOrderInfoData?.remissionApprovalStatus === -1 ? (
                      '-'
                    ) : (
                      <a
                        onClick={() => {
                          setOnlineRepayReviewOpen(false);
                          setRemissionReviewOpen?.(true);
                        }}
                      >
                        {applyOrderInfoData?.businessNo || '-'}
                      </a>
                    )}
                  </span>
                  <span className="">
                    审核状态: {applyOrderInfoData?.remissionApprovalStatusName || '-'}
                  </span>
                </div>
                <div className="line no_border">
                  <span className="repay_label">减免总金额:</span>
                  <span className="repay_amount">{applyOrderInfoData?.exemptionAmount || '-'}</span>
                  元
                </div>

                <Table
                  pagination={false}
                  dataSource={applyOrderInfoData?.remissionList}
                  columns={[
                    {
                      title: '减免本金(元)',
                      dataIndex: 'remissionPrincipal',
                    },
                    {
                      title: '减免利息(元)',
                      dataIndex: 'remissionInterest',
                    },
                    {
                      title: '减免罚息(元)',
                      dataIndex: 'remissionPenalty',
                    },
                    {
                      title: '减免违约金(元)',
                      dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
                    },
                    {
                      title: '减免滞纳金(元)',
                      dataIndex: 'remissionDelayAmount',
                    },
                  ]}
                />
              </div>
            </>
          )}
        </Spin>
      </ModalForm>
    </>
  );
};
export default memo(OnlineRepayReviewModal);
