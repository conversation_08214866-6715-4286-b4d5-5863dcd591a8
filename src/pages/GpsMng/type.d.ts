export type GpsListItem = {
  gpsOrderNo: string;
  outerOrderNo: string;
  orderNo: string;
  orderType: number;
  supplierCode: string;
  orderStatus: number;
  vin: string;
  productId: string;
  productName: string;
  appointTime: string;
  siteCode: string;
  installationSite: string;
  contactName: string;
  contactPhone: string;
  orderChannel: string;
  operateChannel: string;
  operateUser: string;
  operateTime: string;
  extend: string;
  remark: string;
};

export type GpsOrderListParams = {
  current: number;
  pageSize: number;
  gpsOrderNo?: string;
  orderType?: number;
  vin?: string;
  orderChannel?: string;
  oderNo?: string;
  orderStatusList?: number[];
  operateTimeStart?: string;
  operateTimeEnd?: string;
  operateChannel?: string;
  operateUser?: string;
  supplierCode?: string;
};

export type GpsCreateOrderParams = {
  orderType: string; // 工单类型 1. 安装 2.拆机 3.检修
  productId: string; // 产品设备id
  contactName: string; // 安装联系人
  contactPhone: string; // 安装联系人电话
  installationSite: string; // 施工地点
  siteCode: string; // 施工地区行政编码
  carModel: string; // 车型
  vin: string; // 车架号
  remark: string; //备注
  appointTime: string; // 安装时间
};

export type CancelGpsOrderParams = {
  vin: string;
  orderNo: string;
  gpsOrderNo: string;
};
