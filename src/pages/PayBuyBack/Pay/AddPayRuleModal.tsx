// 新增垫付规则
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import {
  ModalForm,
  ProForm,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { editPayRule } from '../service';
import type { PayRuleListItem } from '../types';

type FormValues = {
  configId?: string;
  capital: string;
  productCode: string;
  productType: string;
  ruleCode: string;
  ruleDay: number;
};
type AddPayRuleModalProps = {
  open: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  data?: PayRuleListItem;
};

const AddPayRuleModal: React.FC<AddPayRuleModalProps> = (props) => {
  const [loading, setLoading] = useState(false);
  const [form] = ProForm.useForm();

  useEffect(() => {
    if (props.data) {
      form.setFieldsValue({ ...props.data });
    }
  }, [props.data]);

  const handleFinish = async (values: FormValues) => {
    const { productCode } = values;
    const productType = productCode.substring(0, 2);
    const params = {
      ...values,
      productType,
    };
    setLoading(true);
    try {
      await editPayRule(params);
      props.onSuccess();
      setLoading(false);
      return true;
    } catch {
      setLoading(false);
    }
  };

  return (
    <ModalForm<FormValues>
      title="垫付规则"
      open={props.open}
      layout="horizontal"
      form={form}
      modalProps={{
        destroyOnClose: true,
        onCancel: props.onCancel,
        confirmLoading: loading,
      }}
      labelCol={{ span: 4 }}
      onFinish={handleFinish}
    >
      <ProFormText name="configId" hidden />
      <ProFormSelect
        required
        width="md"
        options={[
          {
            value: 'SHANG_HAI_YING_HANG',
            label: '上海银行',
          },
        ]}
        name="capital"
        label="放款资方"
        rules={[{ required: true, message: '请选择放款资方' }]}
      />
      <ProFormSelect
        required
        width="md"
        options={[
          {
            value: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
            label: '融资租赁',
          },
        ]}
        name="productCode"
        label="合作业务线"
        rules={[{ required: true, message: '请选择合作业务线' }]}
      />
      <ProForm.Group>
        <ProFormSelect
          required
          width="sm"
          labelCol={{ span: 10 }}
          options={[
            {
              value: 'single_overdue',
              label: '单期连续逾期≥',
            },
          ]}
          name="ruleCode"
          label="垫付规则"
          rules={[{ required: true, message: '请选择垫付规则' }]}
        />
        <ProFormDigit
          width="xs"
          label="天数"
          labelCol={{ span: 6 }}
          name="ruleDay"
          min={1}
          fieldProps={{ precision: 0 }}
          rules={[{ required: true, message: '请输入天数' }]}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default AddPayRuleModal;
