import type { IenterpriseItem } from '@/pages/CarInsuranceChannel/services';
import { useModel } from '@umijs/max';
import type { FormInstance } from 'antd';
import { Form, Select, Space } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import type { IenterpriseInfo } from '../type';
import OverdueAlert from './OverdueAlert';

type Props = {
  enterpriseInfoForm: FormInstance;
};
const EnterpriseInfo: React.FC<Props> = (props) => {
  const { enterpriseInfoForm } = props;
  const { optionsData, isEditable, detailData } = useModel('CarInsurance.carInsurance');
  const enterpriseList: IenterpriseItem[] = optionsData?.enterpriseList || [];
  const [, setFlush] = useState({});
  const formItemStyle = { width: 400 };
  function onEnterpriseChange(_value: any, options: any) {
    const { certiNo, authorizerName, orgCode, epAuthNo } = options;
    enterpriseInfoForm.setFieldsValue({
      certiNo,
      authorizerName,
      orgCode,
      epAuthNo,
    });
    setFlush({});
  }

  useEffect(() => {
    const { enterpriseInfo, orderNo } = detailData;
    if (orderNo) enterpriseInfoForm.setFieldsValue(enterpriseInfo);
  }, [enterpriseInfoForm, detailData]);

  return (
    <>
      <h3>企业信息</h3>
      <OverdueAlert />
      <Form<IenterpriseInfo>
        layout={'horizontal'}
        form={enterpriseInfoForm}
        initialValues={{}}
        //   onValuesChange={onFormLayoutChange}
      >
        <Form.Item label="借款企业" required name="enterpriseName">
          <Select
            disabled={!isEditable}
            showSearch
            onChange={onEnterpriseChange}
            options={enterpriseList?.map((item) => {
              const { certiName, authorizerName, epAuthNo, certiNo } = item;
              return {
                label: certiName,
                value: certiName,
                authorizerName,
                epAuthNo,
                orgCode: certiNo,
              };
            })}
            style={formItemStyle}
          />
        </Form.Item>
        <Space size={60}>
          <Form.Item label="社会统一信用代码" name="orgCode">
            {enterpriseInfoForm.getFieldValue('orgCode') || '/'}
          </Form.Item>
          <Form.Item label="授权签约人" name="authorizerName">
            {enterpriseInfoForm.getFieldValue('authorizerName') || '/'}
          </Form.Item>
          <Form.Item label="企业认证编号" name="epAuthNo" style={{ display: 'none' }}>
            {enterpriseInfoForm.getFieldValue('epAuthNo') || '/'}
          </Form.Item>
        </Space>
      </Form>
    </>
  );
};
export default memo(EnterpriseInfo);
