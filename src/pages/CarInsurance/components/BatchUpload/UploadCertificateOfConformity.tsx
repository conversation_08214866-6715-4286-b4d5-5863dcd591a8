/**
 *  上传合格证
 */
import type { FormInstance } from 'antd';
import React, { memo } from 'react';
import { STEP_FILE_TYPE } from '../../type';
import CarOcrContent from './CarOcrContent';

interface UploadCertificateOfConformityIF {
  setLoading: (val: boolean) => void;
  onUploadStatusChange?: (hasUploading: boolean) => void;
  onErrorStatusChange?: (hasError: boolean) => void;
  form: FormInstance;
  otdName: string;
}

const UploadCertificateOfConformity: React.FC<UploadCertificateOfConformityIF> = (props) => {
  const { setLoading, onUploadStatusChange, onErrorStatusChange, form, otdName } = props;
  return (
    <CarOcrContent
      form={form}
      name="certificate"
      otdName={otdName}
      variant="credit"
      fileType={STEP_FILE_TYPE.CREDIT}
      setLoading={setLoading}
      onUploadStatusChange={onUploadStatusChange}
      onErrorStatusChange={onErrorStatusChange}
    />
  );
};

export default memo(UploadCertificateOfConformity);
