import { getChannelInfo } from '@/pages/CarInsurance/services';
import { LEVEL } from '@/pages/CarInsurance/type'; // @ts-ignore
import type { ProColumns } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import React from 'react';
import type { IcustomerName } from '../components/AccountNameFormItem';
import AccountNameFormItem from '../components/AccountNameFormItem';
import type { IbillListItem } from '../types';
import { channelTypeMap, statusMap, userTypeEnCodeMap } from '../types';

/**
 * 车险-车辆期账
 */

export function getCarPaymentTermListColumn({
  access,
  channelCode,
  channelLevel,
}: {
  access: any;
  channelCode: string;
  channelLevel?: number;
}) {
  const CarPaymentTermListColumn: ProColumns<IbillListItem>[] = [
    {
      title: '车辆分期账单ID',
      dataIndex: 'billNo',
    },

    {
      title: '期数',
      dataIndex: 'termList',
      // valueType: 'select',
      hideInTable: true,

      // fieldProps: {
      //   mode: 'multiple',
      //   options: Array(12)
      //     .fill(1)
      //     .map((_, index) => {
      //       return {
      //         value: index + 1,
      //         label: index + 1,
      //       };
      //     }),
      // },
    },
    {
      title: '期数',
      dataIndex: 'termNumber',
      search: false,
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '车牌号',
      dataIndex: 'plateNo',
    },
    {
      title: '车架号',
      dataIndex: 'subjectMatterNo',
    },
    {
      title: '保司',
      dataIndex: 'insuranceCompany',
      search: false,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      hideInTable: true,
      renderFormItem: () => {
        return <AccountNameFormItem />;
      },
      search: {
        transform: (value: IcustomerName) => {
          const { userName, userNo, userType } = value;
          const result: any = {
            customerName: undefined,
          };
          if (userType === userTypeEnCodeMap.COMPANY) {
            result.userNo = userNo;
          } else {
            result.userName = userName;
          }
          return result;
        },
      },
    },
    {
      title: '客户名称',
      dataIndex: 'accountName',
      search: false,
    },
    {
      title: '账单状态',
      dataIndex: 'statusName',
      search: false,
    },

    {
      title: '逾期天数',
      dataIndex: 'overdueDateNumber',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelCode',
      hideInTable: true,
      debounceTime: 60000,
      fieldProps: {
        showSearch: true,
        // 如果是车险渠道 二级 用户只能查看自己渠道的 ,
        disabled: channelLevel === LEVEL.SECOND_CHANNEL,
        popupMatchSelectWidth: false,
      },
      //二级渠道只有自己，才有初始值
      initialValue: channelLevel === LEVEL.SECOND_CHANNEL ? channelCode : undefined,
      request: async () => {
        const data = await getChannelInfo({ channelCode, channelLevel });
        return data.map((item) => {
          return {
            value: item.channelCode,
            label: item.channelName,
            title: item.channelName,
          };
        });
      },
    },

    {
      title: '账单状态',
      dataIndex: 'statusList',
      hideInTable: true,
      valueType: 'select',
      valueEnum: statusMap,
      fieldProps: {
        mode: 'multiple',
      },
    },

    {
      title: '未还总额',
      dataIndex: 'totalAmountUnpaid',
      search: false,
    },
    {
      title: '未还本金',
      dataIndex: 'totalPrincipalUnpaid',
      search: false,
    },
    {
      title: '未还利息',
      dataIndex: 'totalInterestUnpaid',
      search: false,
    },
    {
      title: '未还罚息',
      dataIndex: 'totalOverduePenaltyUnpaid',
      search: false,
    },
    {
      title: '已还总额',
      dataIndex: 'totalAmountPaid',
      search: false,
    },
    {
      title: '已还本金',
      dataIndex: 'totalPrincipalPaid',
      search: false,
    },
    {
      title: '已还利息',
      dataIndex: 'totalInterestPaid',
      search: false,
    },
    {
      title: '已还罚息',
      dataIndex: 'totalOverduePenaltyPaid',
      search: false,
    },

    {
      title: '应还总额',
      dataIndex: 'totalAmountDue',
      search: false,
    },
    {
      title: '应还本金',
      dataIndex: 'totalPrincipalDue',
      search: false,
    },
    {
      title: '应还利息',
      dataIndex: 'totalInterestDue',
      search: false,
    },

    {
      title: '应还罚息',
      dataIndex: 'totalOverduePenaltyDue',
      search: false,
    },

    {
      title: '减免总额',
      dataIndex: 'totalAmountExemption',
      search: false,
    },

    {
      title: '减免本金',
      dataIndex: 'totalPrincipalExemption',
      search: false,
    },
    {
      title: '减免利息',
      dataIndex: 'totalInterestExemption',
      search: false,
    },
    {
      title: '减免罚息',
      dataIndex: 'totalOverduePenaltyExemption',
      search: false,
    },
    {
      title: '未还违约金',
      dataIndex: 'totalBreach',
      search: false,
    },
    {
      title: '未还滞纳金',
      dataIndex: 'totalLate',
      search: false,
    },

    {
      title: '已还违约金',
      dataIndex: 'totalBreachPaid',
      search: false,
    },
    {
      title: '已还滞纳金',
      dataIndex: 'totalLatePaid',
      search: false,
    },
    {
      title: '应还违约金',
      dataIndex: 'totalBreachDue',
      search: false,
    },

    {
      title: '应还滞纳金',
      dataIndex: 'totalLateDue',
      search: false,
    },

    {
      title: '减免违约金',
      dataIndex: 'totalBreachExemption',
      search: false,
    },
    {
      title: '减免滞纳金',
      dataIndex: 'totalLateExemption',
      search: false,
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(2, 'month'), dayjs().add(1, 'month')],
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            dueDateStart: `${value[0]} 00:00:00`,
            dueDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.dueDate || '-';
      },
    },
    {
      title: '更新日期',
      dataIndex: 'updatedAt',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            updateAtStart: `${value[0]} 00:00:00`,
            updateAtEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.updatedAt || '-';
      },
    },
    {
      title: '实际结清日期',
      dataIndex: 'clearTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            clearTimeStart: `${value[0]} 00:00:00`,
            clearTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.clearTime || '-';
      },
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            lendingTimeStart: `${value[0]} 00:00:00`,
            lendingTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.lendingTime || '-';
      },
    },

    {
      title: '渠道类型',
      dataIndex: 'channelTypeName',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '客户类型',
      dataIndex: 'customerTypeName',
      search: false,
    },
    {
      title: '客户类型',
      dataIndex: 'userType',
      valueType: 'select',
      fieldProps: {
        options: [
          { value: 1, label: '企业' },
          { value: 2, label: '个人' },
        ],
      },
      hideInTable: true,
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      hideInTable: true,
      valueType: 'select',
      valueEnum: channelTypeMap,
    },

    // {
    //   title: '操作',
    //   dataIndex: 'option',
    //   valueType: 'option',
    //   fixed: 'right',
    //   width: 80,
    //   render: (_, record) => {
    //     const { orderNo } = record;
    //     const url = `/businessMng/postLoanMng/car-insurance/detail?orderNo=${orderNo}`;
    //     return <Link to={url}>查看详情</Link>;
    //   },
    // },
  ];
  return CarPaymentTermListColumn;
}
