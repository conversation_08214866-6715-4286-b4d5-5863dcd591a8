import { bizAdminHeader } from '@/utils/constant';
import { BellOutlined } from '@ant-design/icons';
import { history, request } from '@umijs/max';
import { Alert, Badge, Empty, Popover } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

type NoticeItem = {
  uuid: string;
  dataTime: string;
  content: string;
  firstTime: string;
  orderNo: string;
};

const LOOP_TIME = 3 * 60 * 1e3;
let loopTimeId: any = null;
const closeTimerId: Record<string, any> = {};

const CLOSE_TIME = 60 * 60 * 1e3;

async function getNoticeList() {
  return request('/bizadmin/message/getMsgPrompt', {
    method: 'POST',
    headers: bizAdminHeader,
  });
}

async function closeNotice(uuid: string) {
  return request('/bizadmin/message/closeMsgPrompt', {
    method: 'POST',
    data: {
      uuid,
    },
    headers: bizAdminHeader,
  });
}

const AuditNoticeList: React.FC = () => {
  const [noticeList, setNoticeList] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [showNotice, setShowNotice] = useState({});

  const handleClose = (item: NoticeItem) => {
    closeNotice(item.uuid);
    setNoticeList((prev) => {
      const newList = prev.filter((o: any) => o.uuid !== item.uuid);
      return newList;
    });
  };

  const clearLoopTime = () => {
    if (loopTimeId) {
      clearInterval(loopTimeId);
    }
  };

  const clearCloseTimeId = () => {
    if (Object.keys(closeTimerId).length) {
      Object.keys(closeTimerId).forEach((key: string) => {
        if (closeTimerId[key]) {
          clearTimeout(closeTimerId[key]);
          console.log('清除定时器', closeTimerId[key]);
        }
      });
    }
  };

  // 计时关闭
  const timeToClose = (data: NoticeItem[]) => {
    data.forEach((item) => {
      closeTimerId[item.uuid] = setTimeout(() => {
        const list = noticeList;
        const newList = list.filter((o: any) => o.uuid !== item.uuid);
        setNoticeList([...newList]);
        setShowNotice((pre) => {
          return {
            ...pre,
            [item.uuid]: false,
          };
        });
        if (!newList.length) {
          setModalVisible(false);
        }
        console.log('关闭消息', closeTimerId[item.uuid]);
      }, CLOSE_TIME);
    });
    console.log('定时器', closeTimerId);
  };

  const getList = () => {
    clearCloseTimeId();
    getNoticeList().then((res) => {
      console.log('轮询完成');
      const { data } = res;
      if (data && data.length) {
        setNoticeList(data);
        timeToClose(data);
        const obj = {};
        data.forEach((item: any) => {
          obj[item.uuid] = true;
        });
        setShowNotice(obj);
        setModalVisible(true);
      }
    });
  };

  useEffect(() => {
    // 执行一次
    getList();
    // 轮询
    loopTimeId = setInterval(() => {
      getList();
    }, LOOP_TIME);
    return () => {
      clearLoopTime();
      clearCloseTimeId();
    };
  }, []);

  const showList = (
    <div className="audit-notice-list">
      {noticeList.length ? (
        noticeList?.map((item: NoticeItem) => {
          return (
            showNotice[item.uuid] && (
              <Alert
                key={item.uuid}
                className="audit-notice-alert"
                message={
                  <div className="audit-notice-alert-content">
                    <div>{item.dataTime}</div>
                    {/* <div className="audit-notice-alert-content-text"> */}
                    <div>{item.content}</div>
                  </div>
                }
                action={
                  <a
                    onClick={() => {
                      handleClose(item);
                      history.push(`/businessMng/lease-detail?orderNo=${item.orderNo}`);
                    }}
                    type="link"
                  >
                    <span className="audit-notice-alert-content-text">立马处理</span>
                  </a>
                }
                showIcon={false}
                closable={true}
                onClose={() => {
                  handleClose(item);
                }}
              />
            )
          );
        })
      ) : (
        <Empty description="暂无消息" />
      )}
    </div>
  );
  return (
    <Popover
      style={{ width: 500 }}
      overlayClassName="audit-notice-popover"
      open={modalVisible}
      content={showList}
      trigger="click"
      placement="bottom"
      onOpenChange={(visible) => {
        setModalVisible(visible);
      }}
    >
      <Badge count={noticeList.length}>
        <BellOutlined style={{ color: '#fff', fontSize: 20, cursor: 'pointer' }} />
      </Badge>
    </Popover>
  );
};

export default AuditNoticeList;
