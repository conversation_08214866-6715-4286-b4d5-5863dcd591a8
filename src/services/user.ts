/*
 * @Author: your name
 * @Date: 2021-04-19 19:18:04
 * @LastEditTime: 2021-04-25 10:44:37
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/services/user.ts
 */
import { request } from '@umijs/max';

export async function query() {
  return request('/api/users');
}

export async function queryCurrent() {
  return request('/auth/operator/privilege/getUserInfo');
}
export async function querySSOCurrent() {
  return request(`/bizadmin/employee/queryEmployeeByPassportId`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

// 获取公钥
export async function getPublicKey() {
  return request(`/bizadmin/auth/queryPublicKey`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
