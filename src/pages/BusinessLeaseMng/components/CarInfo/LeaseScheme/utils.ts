import type { ProductItem } from './types';

export function formatPickerData(data: ProductItem[] = []) {
  if (!data) return [];
  return data.map((item) => {
    return {
      label: item.productName,
      value: item.productCode,
    };
  });
}

/**
 * 判断数字是否在区间内 a≤首付款≤b
 * @param value 判断的值
 * @param leftVal 左边界
 * @param leftSymbol 左边计算符号
 * @param rightVal 右边界
 * @param rightSymbol 右边计算符号
 */
export function isInRange(
  value: number,
  leftVal: number,
  leftSymbol: string,
  rightVal: number,
  rightSymbol: string,
) {
  // 左开右开
  if (leftSymbol === '<' && rightSymbol === '<') {
    return value > leftVal && value < rightVal;
  }
  // 左闭右闭
  if (leftSymbol === '≤' && rightSymbol === '≤') {
    return value >= leftVal && value <= rightVal;
  }
  // 左开右闭
  if (leftSymbol === '<' && rightSymbol === '≤') {
    return value > leftVal && value <= rightVal;
  }
  // 左闭右开
  if (leftSymbol === '≤' && rightSymbol === '<') {
    return value >= leftVal && value < rightVal;
  }
  return false;
}

// 万位向上取整
export function roundToTenThousand(num: number) {
  if (isNaN(num)) return 0; // 检查是否为数字
  return Math.ceil(num / 10000) * 10000;
}
