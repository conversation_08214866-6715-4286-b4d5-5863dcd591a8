import type { ProColumns } from '@ant-design/pro-table';
import { message, Modal, Tag } from 'antd';
import React from 'react';
import type { IpolicyItem } from './services';
import { deletePolicy } from './services';

function renderDate(text: any) {
  return typeof text === 'string' ? text.split(' ')[0] : '';
}

export const columns: ProColumns<IpolicyItem>[] = [
  {
    title: '贷款订单号',
    dataIndex: 'orderNo',
    search: false,
  },
  {
    title: '保单创建时间',
    dataIndex: 'createdAt',
    search: false,
  },
  {
    title: '车牌号/新车合格证号',
    dataIndex: 'plateNo',
    formItemProps: {
      label: '车牌号',
    },
  },
  {
    title: '车架号',
    dataIndex: 'vin',
  },
  {
    title: '发动机号',
    dataIndex: 'engineCode',
  },
  {
    title: '被保险人',
    dataIndex: 'assuredName',
    search: false,
  },
  {
    title: '投保人',
    dataIndex: 'policyHolder',
    search: false,
  },
  {
    title: '保险公司来源',
    dataIndex: 'insuranceCompanySource',
    search: false,
  },
  {
    title: '投保时间',
    dataIndex: 'insuranceTime',
    search: false,
    render: renderDate,
  },
  {
    title: '商业险保单号',
    dataIndex: 'commercialNo',
    search: false,
  },
  {
    title: '商业险开始日期',
    dataIndex: 'commercialInsuranceStartDate',
    search: false,
    render: renderDate,
  },
  {
    title: '商业险结束日期',
    dataIndex: 'commercialInsuranceEndDate',
    search: false,
    render: renderDate,
  },
  {
    title: '商业险保单期限',
    dataIndex: 'commercialInsurancePeriod',
    search: false,
  },
  {
    title: '交强险保单号',
    dataIndex: 'compulsoryNo',
    search: false,
  },
  {
    title: '保险公司名称',
    dataIndex: 'insuranceCompany',
    search: false,
  },
  {
    title: '商业险开始时间',
    dataIndex: 'commercialInsuranceStartDate',
    hideInTable: true,
    valueType: 'dateRange',
    colSize: 1.5,
    tooltip: '投保时间和商业险开始时间只能选一个',
    formItemProps: {
      labelCol: { span: 5 },
    },
    fieldProps: (form) => {
      return {
        onChange: () => {
          form.setFieldsValue({ insuranceTime: [] });
        },
      };
    },
    render(text) {
      return <div>{text}</div>;
    },
  },
  {
    title: '投保时间',
    dataIndex: 'insuranceTime',
    hideInTable: true,
    valueType: 'dateRange',
    colSize: 1.5,
    fieldProps: (form) => {
      return {
        onChange: () => {
          form.setFieldsValue({ commercialInsuranceStartDate: [] });
        },
      };
    },
    tooltip: '投保时间和商业险开始时间只能选一个',
    formItemProps: {
      // labelCol: { span: 5 },
    },
    render(text) {
      return <div>{text}</div>;
    },
  },
  {
    title: '操作',
    dataIndex: 'Options',
    fixed: 'right',
    width: 100,
    search: false,
    render(_, record, _1, action) {
      const { commercialNo } = record;
      return [
        <a
          key={'delete'}
          onClick={async () => {
            Modal.confirm({
              title: '删除',
              content: <div>您确认要删除-{<Tag color="error">{commercialNo}</Tag>}保单吗</div>,
              async onOk() {
                await deletePolicy(record?.id);
                message.success('删除成功');
                action.reload();
              },
            });
          }}
        >
          删除
        </a>,
      ];
    },
  },
];
