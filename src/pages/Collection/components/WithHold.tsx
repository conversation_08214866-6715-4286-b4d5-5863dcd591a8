/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2025-05-16 18:41:30
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/WithHold.tsx
 */
import { LEASE_LOAN_CHANNEL, PRODUCT_CLASSIFICATION_CODE, REPURCHASE_STATUS } from '@/enums';
import globalStyle from '@/global.less';
import { offlineRepayReviewList } from '@/pages/CallPay/carInsurance/services';
import { getUuid } from '@/utils/utils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { <PERSON><PERSON>, <PERSON>, message, Popover, Row, Tabs } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import type { WithHoldListItem } from '../data';
import { STATUS_CASE } from '../data.d';
import { repayRecording, withHoldList } from '../service';
import { AddWithHoldModal, AddWithHoldWalletModal } from './index';
import './index.less';

const WithHold: React.FC<any> = (props) => {
  const { overDueAmount, data, overdueCaseNo, dataDetail, driverWalletInfo, customerType } = props;

  const [dataWithHoldInfo, setDataWithHoldInfo] = useState<{
    userName: string;
    bankNo: string;
    phone: string;
    orderNo: string;
    repayOverdueCostBO: any[];
  }>();

  const [selectedValue, setSelectedValue] = useState();
  const [listOptions, setListOptions] = useState([]);
  const [btnDisabled, setBtnDisabled] = useState(true);

  const { TabPane } = Tabs;

  // 资方渠道是上海银行，且没有被易人行回购
  const isShangHaiYinHang =
    data?.funderChannelCode === LEASE_LOAN_CHANNEL.SHANG_HAI_YING_HANG &&
    ![
      REPURCHASE_STATUS.BUY_CHANNEL,
      REPURCHASE_STATUS.BUY_CHANNEL_ING,
      REPURCHASE_STATUS.BUY_SELF,
      REPURCHASE_STATUS.BUY_SELF_ING,
    ].includes(data?.repurchaseStatus);

  const columns: ProColumns<WithHoldListItem>[] = [
    {
      title: '协议支付时间',
      dataIndex: 'withholdTime',
      key: 'withholdTime',
    },
    {
      title: '协议支付订单',
      dataIndex: 'billNo',
      key: 'billNo',
    },
    {
      title: '协议支付金额',
      dataIndex: 'withholdMoney',
      render: (_, row) => {
        return (row.withholdMoney / 100).toFixed(2) || '-';
      },
    },
    {
      title: '协议支付结果',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        1: '待受理',
        2: '已受理',
        3: '支付成功',
        4: '支付失败',
      },
    },
    {
      title: '协议支付信息',
      dataIndex: 'reason',
      key: 'reason',
    },
    {
      title: '申请人',
      dataIndex: 'withholdOperator',
    },
  ];

  const walletColumns: ProColumns<WithHoldListItem>[] = [
    {
      title: '协议支付时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '协议支付订单',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    {
      title: '协议支付金额',
      dataIndex: 'repayAmount',
      key: 'repayAmount',
    },
    {
      title: '协议支付结果',
      dataIndex: 'payStatusName',
      key: 'payStatusName',
      // valueEnum: {
      //   1: '待受理',
      //   2: '已受理',
      //   3: '协议支付成功',
      //   4: '协议支付失败',
      // },
    },
    {
      title: '协议支付信息',
      dataIndex: 'reason',
      key: 'reason',
    },
    {
      title: '申请人',
      dataIndex: 'applyName',
      key: 'applyName',
    },
  ];

  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const [modalWalletVisible, handleModalWalletVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const actionRefHistory = useRef<ActionType>();
  //催收案件单号变化，重新刷新table
  useEffect(() => {
    actionRef?.current?.reload();
    actionRefHistory?.current?.reload();
    // setActiveKey(activeKey);
  }, [overdueCaseNo]);

  useEffect(() => {
    if (dataDetail && data) {
      const list = dataDetail.map((item) => {
        return {
          label: (
            <span>
              {item.orderNo}
              <span style={{ color: 'red', marginLeft: 4 }}>逾期{item.totalOverdueAmount}元</span>
            </span>
          ),
          value: item.orderNo,
        };
      });
      setListOptions(list);
      if (list[0]?.value) {
        setSelectedValue(list[0]?.value);
        const { totalOverdueAmount, bankCode, phoneNumber } = dataDetail[0];
        const btnStatus =
          [Number(STATUS_CASE.REVOKE), Number(STATUS_CASE.SETTLE)].indexOf(data?.status) > -1 ||
          Number(totalOverdueAmount) === 0;
        setBtnDisabled(btnStatus);
        // 设置协议支付数据
        setDataWithHoldInfo({
          userName: data.accountName,
          bankNo: bankCode,
          phone: phoneNumber,
          orderNo: list[0]?.value,
          ...dataDetail[0],
        });
      }
    }
  }, [dataDetail, data]);

  const onSelectChange = (value) => {
    setSelectedValue(value);
    if (!value) {
      setBtnDisabled(true);
    }
    const selectedOption = dataDetail.find((item) => item.orderNo === value);
    if (selectedOption) {
      const { totalOverdueAmount, bankCode, phoneNumber } = selectedOption;
      const btnStatus =
        [Number(STATUS_CASE.REVOKE), Number(STATUS_CASE.SETTLE)].indexOf(data?.status) > -1 ||
        Number(totalOverdueAmount) === 0;
      setBtnDisabled(btnStatus);
      // 设置协议支付数据
      setDataWithHoldInfo({
        userName: data.accountName,
        bankNo: bankCode,
        phone: phoneNumber,
        orderNo: value,
        ...selectedOption,
      });
    }
  };

  //人工协议支付
  function manualWithHoldElement() {
    return (
      <>
        <ProTable<WithHoldListItem>
          columns={columns}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey={getUuid()}
          search={false}
          pagination={{ pageSize: 10 }}
          options={false}
          toolBarRender={false}
          request={(params) => withHoldList({ ...params, overdueCaseNo })}
        />
      </>
    );
  }

  //钱包协议支付
  function walletWithHoldElement() {
    return (
      <>
        <ProTable<any>
          columns={walletColumns}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey={getUuid()}
          search={false}
          pagination={{ pageSize: 10 }}
          options={false}
          toolBarRender={false}
          request={(params) =>
            offlineRepayReviewList({
              ...params,
              orderNo: dataDetail?.[0]?.orderNo || '',
              repayMode: '9',
            })
          }
        />
      </>
    );
  }

  //历史记录
  const columnsHistory = [
    {
      title: '还款流水号',
      dataIndex: 'recordingNo',
    },
    {
      title: '实际还款总金额',
      dataIndex: 'actualRepaymentAmount',
    },
    {
      title: '实际还款本金',
      dataIndex: 'principal',
    },
    {
      title: '实际还款利息',
      dataIndex: 'interest',
    },
    {
      title: '实际还款罚息',
      dataIndex: 'penaltyInterest',
    },
    {
      title: '实际还款费用',
      dataIndex: 'cost',
    },
    {
      title: '还款方式',
      dataIndex: 'repayType',
    },
    {
      title: '还款时间',
      dataIndex: 'repayTime',
    },
  ];
  function contentHistoryElement() {
    return (
      <ProTable
        columns={columnsHistory}
        actionRef={actionRefHistory}
        scroll={{ x: 'max-content' }}
        rowKey="recordingNo"
        search={false}
        options={false}
        toolBarRender={false}
        request={(params) => {
          return repayRecording({
            ...params,
            overdueCaseNo,
          })
            .then((res) => res)
            .catch((err) => {
              console.log(err?.message);
              message.destroy();
            });
        }}
      />
    );
  }

  return (
    <>
      {dataDetail?.[0]?.productCode?.substring(0, 2) !==
      PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING ? (
        <>
          <Row gutter={[8, 8]} className={classNames(globalStyle.pl20, globalStyle.mt20)}>
            <Col span={10}>
              <ProFormSelect
                label="人工协议支付记录："
                width="md"
                options={listOptions}
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
                fieldProps={{
                  showSearch: true,
                  value: selectedValue,
                  onChange: onSelectChange,
                  filterOption: (input, option: any) =>
                    (option?.value ?? '').toLowerCase().includes(input.toLowerCase()),
                  popupMatchSelectWidth: false,
                }}
              />
            </Col>
            <Col>
              <Button
                type="primary"
                onClick={() => {
                  if (dataWithHoldInfo) {
                    handleModalVisible(true);
                  }
                }}
                //结清和撤销
                disabled={btnDisabled}
                className={globalStyle.mb10}
              >
                协议支付-银行卡
              </Button>
              {isShangHaiYinHang && (
                <Popover content="租金贷模式下，按照银行可还款时间限制可操作时间。上海银行7~17:30点。">
                  <QuestionCircleOutlined
                    style={{ fontSize: 16, marginLeft: 6, color: 'rgba(0, 0, 0, 0.45)' }}
                  />
                </Popover>
              )}
              {/* 火辣辣司机才展示钱包还款按钮 */}
              {driverWalletInfo?.driverId && (
                <Button
                  type="primary"
                  danger
                  style={{ marginLeft: 20 }}
                  onClick={() => {
                    handleModalWalletVisible(true);
                  }}
                  //结清和撤销
                  disabled={btnDisabled}
                  className={globalStyle.mb10}
                >
                  协议支付-钱包还款
                </Button>
              )}
            </Col>
          </Row>

          {/* <div style={{ margin: 10 }}>
            人工协议支付记录：
            {dataDetail?.map(
              ({
                orderNo,
                phoneNumber,
                bankCode,
                totalOverdueAmount,
                ...item
              }: {
                orderNo: string;
                phoneNumber: string;
                bankCode: string;
                totalOverdueAmount: string;
              }) => {
                return (
                  <>
                    <span className="mr-10 ml-10">{orderNo}</span>
                    <Button
                      type="primary"
                      onClick={() => {
                        setDataWithHoldInfo({
                          userName: data.accountName,
                          bankNo: bankCode,
                          phone: phoneNumber,
                          orderNo,
                          ...item,
                        });
                        handleModalVisible(true);
                      }}
                      //结清和撤销
                      disabled={
                        [Number(STATUS_CASE.REVOKE), Number(STATUS_CASE.SETTLE)].indexOf(
                          data?.status,
                        ) > -1 || Number(totalOverdueAmount) === 0
                      }
                      className={globalStyle.mb10}
                    >
                      发起协议支付
                    </Button>
                  </>
                );
              },
            )}
          </div> */}
          <div>
            <Tabs style={{ background: '#fff', padding: '0 10px' }}>
              <TabPane tab="人工协议支付记录" key="3">
                {manualWithHoldElement()}
              </TabPane>
              {driverWalletInfo?.driverId && (
                <TabPane tab="钱包协议支付记录" key="5">
                  {walletWithHoldElement()}
                </TabPane>
              )}
              <TabPane tab="历史还款记录" key="4">
                {contentHistoryElement()}
              </TabPane>
            </Tabs>
          </div>
        </>
      ) : (
        ''
      )}
      {/* {!hideCard && (
        <Card title="协议支付记录" className={globalStyle.mt20}>

        </Card>
      )}
      {hideCard && <>{contentElement()}</>} */}
      <AddWithHoldModal
        onOk={async () => {
          handleModalVisible(false);
          // refresh();
          actionRef?.current?.reload();
        }}
        onCancel={() => {
          handleModalVisible(false);
        }}
        overDueAmount={overDueAmount}
        onVisibleChange={handleModalVisible}
        modalVisible={modalVisible}
        overdueCaseNo={overdueCaseNo}
        dataWithHoldInfo={dataWithHoldInfo}
        channelCode={data?.funderChannelCode}
        repurchaseStatus={data?.repurchaseStatus}
      />
      <AddWithHoldWalletModal
        onOk={async () => {
          handleModalWalletVisible(false);
          actionRef?.current?.reload();
        }}
        onCancel={() => {
          handleModalWalletVisible(false);
        }}
        overDueAmount={overDueAmount}
        onVisibleChange={handleModalWalletVisible}
        modalVisible={modalWalletVisible}
        overdueCaseNo={overdueCaseNo}
        dataWithHoldInfo={dataWithHoldInfo}
        channelCode={data?.funderChannelCode}
        repurchaseStatus={data?.repurchaseStatus}
        driverWalletInfo={driverWalletInfo}
        customerType={customerType}
      />
    </>
  );
};

export default WithHold;
