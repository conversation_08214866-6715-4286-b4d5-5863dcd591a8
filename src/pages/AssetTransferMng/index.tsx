// 资产保理管理-转让资产管理
import HeaderTab from '@/components/HeaderTab';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { useNavigate } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';
import { getTransferList } from './service';
import type { AssetTransferListItem } from './type';
const AssetTransferList = () => {
  const nav = useNavigate();
  const columns: ProColumns<AssetTransferListItem>[] = [
    {
      title: '转让流水号',
      dataIndex: 'recordNo',
      key: 'recordNo',
      search: false,
    },
    {
      title: '出让方',
      dataIndex: 'transferorName',
      key: 'transferorName',
      search: false,
    },
    {
      title: '受让方',
      dataIndex: 'transfereeName',
      key: 'transfereeName',
      search: false,
    },
    {
      title: '转让时间',
      dataIndex: 'transferClaimTime',
      key: 'transferClaimTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            startTransferClaimTime: `${value[0]} 00:00:00`,
            endTransferClaimTime: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.transferClaimTime || '-';
      },
    },
    {
      title: '资产金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      search: false,
    },
    {
      title: '转让比例',
      dataIndex: 'transferRatio',
      key: 'transferRatio',
      search: false,
    },
    {
      title: '融资金额',
      dataIndex: 'financingAmount',
      key: 'financingAmount',
      search: false,
    },
    {
      title: '融资期限',
      dataIndex: 'financingTerm',
      key: 'financingTerm',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      search: false,
    },
    {
      title: '操作',
      key: 'operate',
      search: false,
      render: (dom, record) => {
        return (
          <Button
            type="link"
            onClick={() => {
              nav(`/businessMng/assetFactorMng/transfer-detail?recordNo=${record.recordNo}`);
            }}
          >
            查看详情
          </Button>
        );
      },
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<AssetTransferListItem>
          columns={columns}
          scroll={{ x: 'max-content' }}
          request={(params) => getTransferList(params)}
        />
      </PageContainer>
    </>
  );
};

export default AssetTransferList;
