name: Deploy CI

on:
  push:
    branches:
      - master

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@master
      - run: npm install
      - run: npm run lint
      # - run: yarn run tsc
      - name: Build and Deploy
        uses: JamesIves/github-pages-deploy-action@master
        env:
          CI: true
          GA_KEY: UA-72788897-6
          PROGRESS: none
          GIT_CONFIG_NAME: qixian.cs
          GIT_CONFIG_EMAIL: <EMAIL>
          NODE_OPTIONS: --max_old_space_size=4096
          ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: site
          GITHUB_TOKEN: ${{ secrets.ACTION_TOKEN }}
          BRANCH: gh-pages
          FOLDER: 'dist/'
          BUILD_SCRIPT: yarn && npm uninstall husky && yarn add umi-plugin-setting-drawer umi-plugin-antd-theme umi-plugin-pro && yarn run pro fetch-blocks && npm run site && git checkout . && git clean -df
