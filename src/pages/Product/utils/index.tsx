/**
 * 判断一个参数是否是数字
 * @param num
 * @returns
 */
function isNumber(num: any) {
  return typeof num === 'number' && !isNaN(num);
}
/**
 * 判断一个参数是否是整形数字 或者 整形字符串数字
 * @param num
 * @returns
 */
export const isIntNumber = (num: any) => {
  return /^\d+$/gi.test(num);
};
/**
 * 得到一个固定范围的整型数组
 * [start,end]
 */
export const getIntArray = (end: number = 100, start: number = 1): number[] => {
  if (!isNumber(end) || !isNumber(start) || start > end) return [];
  const arr = Array.from({ length: end - start + 1 }, (_, i) => start + i);
  // console.log('arr11111', arr);
  return arr;
};
