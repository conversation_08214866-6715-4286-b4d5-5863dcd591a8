/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2023-12-12 15:43:03
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseIncoming/service.ts
 */
import { bizAdminHeader, headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type {
  CreateFactoryData,
  PromoteListParams,
  UnRegistrationParams,
  UserInputRecordListParams,
} from './data';

// 获取进件记录列表
export async function getInputRecordList(params?: UserInputRecordListParams) {
  return request('/loan/riskOrder/list', {
    method: 'GET',
    params: { ...params },
    ifTrimParams: true,
  });
}

// 获取进件详情---基础信息
export async function getInputDetailBsae(orderNo: string) {
  return request(`/loan/riskOrder/baseInfo/${orderNo}`, {
    method: 'GET',
  });
}

// 获取进件详情---风控记录
export async function getInputDetailRecord(orderNo: string) {
  return request(`/loan/riskOrder/getRiskOrderCreditData/${orderNo}`, {
    method: 'GET',
  });
}

// 还款计划
export async function getRepayPlan(orderNo: string) {
  return request(`/repayment/cms/bill/repay/plan/${orderNo}`);
}

// 进件导出 incomeExport
export async function incomeExport(params: UserInputRecordListParams) {
  return request(`/loan/riskOrder/exportExcel4RiskOrder`, {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

//创建进件单
export async function createFactory(data: CreateFactoryData) {
  return request(`/loan/cms/applyOrder/create`, {
    method: 'POST',
    data,
  });
}

// 注销
export async function unRegistration(data: UnRegistrationParams) {
  return request(`/loan/enterprise/cms/unRegistration`, {
    method: 'POST',
    data,
  });
}

//获取还款日
export async function getRepayDay(productCode: string, type: string) {
  return request(`/loan/product/find/${productCode}/${type}`);
}

//查询提额视图列表
export async function getPromoteList(data: PromoteListParams) {
  return request(`/bizadmin/increase/order/queryOrderList`, {
    data,
    headers,
    method: 'POST',
    ifTrimParams: true,
  });
}

//提额入口开关
export async function onOrOff(status: number) {
  return request(`/bizadmin/increase/order/onOrOff`, {
    data: { status },
    headers,
    method: 'POST',
  });
}

//获取提额入口开关
export async function getSwitch() {
  return request(`/bizadmin/increase/order/getSwitch`, {
    headers,
    method: 'GET',
  });
}

//获取母子企业
export async function getMotherAndSon(params: { productSecondCode: string; userNo: string }) {
  const data = await request(`/bizadmin/factoring/enterpriseQuotaMsg`, {
    headers,
    data: params,
    method: 'POST',
  });
  return data?.data || {};
}

// 注销暗保
export async function logoutDark(params: { productSecondCode: string; userNo: string }) {
  return request(`/bizadmin/factoring/logout`, {
    headers,
    data: params,
    method: 'POST',
  });
}

export async function getProcessStatus(orderNo: string) {
  return request(`/bizadmin/factoring/enterprise/v2/queryIncomeProcessStatus?orderNo=${orderNo}`, {
    headers,
    method: 'GET',
  });
}

// 提额列表导出
export async function exporHeavyQuota(data: PromoteListParams) {
  return request(`/bizadmin/increase/order/orderList/export`, {
    method: 'POST',
    responseType: 'blob',
    data,
    getResponse: true,
    headers: {
      ...bizAdminHeader,
    },
    ifTrimParams: true,
  });
}

// 撤销订单
export async function revokeOrder(applyNo: string, remark: string) {
  return request(`/bizadmin/increase/revoke`, {
    method: 'POST',
    data: { applyNo, remark },
    skipErrorHandler: true,
    headers: {
      ...bizAdminHeader,
    },
  });
}
