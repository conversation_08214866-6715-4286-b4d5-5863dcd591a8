/*
 * @Author: your name
 * @Date: 2021-04-13 11:20:03
 * @LastEditTime: 2025-04-18 17:11:12
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/ModelLibrary/sevice.ts
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from 'umi';
import type { AddCarParams, CarListParams } from './data';

export async function queryCar(params?: CarListParams) {
  return request('/bizadmin/lease/car/list', {
    params,
    headers: bizAdminHeader,
    ifTrimParams: true,
  });
}

export async function updateStatus(params?: {
  id: number | string | undefined;
  status: number | undefined;
}) {
  return request('/bizadmin/lease/car/updateStatus', {
    params,
    method: 'PUT',
    headers: bizAdminHeader,
  });
}

// 校验唯一性
export async function existCarIdCode(carIdCode: string, carType: number) {
  return request(`/bizadmin/lease/car/existCarIdCode/${carIdCode}/${carType}`, {
    headers: bizAdminHeader,
  });
}

// 添加新车
export async function addCar(data: AddCarParams) {
  return request(`/bizadmin/lease/car/add`, {
    data,
    method: 'POST',
    headers: bizAdminHeader,
  });
}

// 渠道枚举
export async function getAllChannelEnum() {
  return request(`/bizadmin/channel/lease/getAllChannelEnum`, {
    headers: bizAdminHeader,
  });
}

// 车型库修改
export async function modifyCarModelLibrary(data: AddCarParams) {
  return request(`/bizadmin/lease/car/carInfo/modify`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 车型库信息删除
export async function delCarLibrary(id?: string | number) {
  return request(`/bizadmin/lease/car/carInfo/delete`, {
    params: { id },
    headers: bizAdminHeader,
  });
}

// 车型库导出-异步
export async function carExportAsync(params: any) {
  return request(`/bizadmin/lease/car/export`, {
    headers: bizAdminHeader,
    data: params,
    method: 'POST',
    ifTrimParams: true,
  });
}
