/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2023-12-19 13:56:39
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/data.d.ts
 */

// 获取企业用户管理列表的请求数据类型
export interface EnterpriseUserListParams {
  endApplyTime?: string; // 创建结束时间
  enterpriseName?: string; // 企业名称
  idNo?: string; // 统一社会信用代码
  startApplyTime?: string; // 创建开始时间
  status?: number; // 状态
  userNo?: number; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 页大小
  productSecondTypeCode?: string | string[];
}

//产品参数
export interface EnterpriseProductParams {
  current?: number;
  endCreditEndTime?: string;
  enterpriseName?: string;
  idNo?: string;
  pageSize?: number;
  productSecondTypeCode?: string;
  startCreditEndTime?: string;
  status?: number;
  userNo?: string;
  productSecondTypeCodes?: string | string[];
}

// 获取企业用户管理列表的响应数据类型
export interface EnterpriseUserListItem {
  createdAt: string; // 创建时间
  creditAmount: number; // 授信总金额
  currentAmount: number; // 剩余可用额度
  enterpriseName: string; // 企业名称
  freezeAmount: number; // 冻结额度
  productSecondTypeName: string; // 产品二级分类
  idNo: string; // 统一社会信用代码
  status: number; // 状态
  usedAmount: number; // 已使用额度
  userNo: string; // 用户编号
  productSecondTypeCode?: string; // 二级分类
  productSecondTypeCodes?: string | string[];
}

//产品维度的列表
export interface EnterpriseProductListItem {
  billRepayDate?: string; //还款日
  createdAt?: string; //创建时间
  creditAmount?: string; //授信总金额
  creditEndTime?: string; //授信时间
  currentAmount?: string; //剩余可用额度
  enterpriseName?: string; //企业名称
  factoringServiceFee?: string; //保理服务费率
  freezeAmount?: string; //冻结额度
  idNo?: string; //统一社会信用代码
  overdueAmount?: string; //逾期金额
  productSecondTypeName?: string; //产品二级分类
  riskLevel?: string; //风控评级
  status?: number; //状态
  usedAmount?: number; //已使用额度
  userNo?: string; //用户编号
  userTag?: string; //客户标签
  subAccountNo?: string; //此账号
}

// 获取企业用户详情---风控数据---额度详情的请求参数数据类型
export interface QuotaDetailParams {
  subAccountNo: string; // 子账户编号
  userNo: string; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 每页的数据量
}

// 获取企业用户详情---风控数据---额度详情的响应参数数据类型
export interface QuotaDetailItem {
  createTime: string; // 创建时间
  creditAmount: number; // 总额度
  id: string; // id
  idNo: string; // 身份证号
  name: string; // 企业名
  operator: string; // 操作人
  operatorAmount: number; // 调整额度
  operatorType: number; // 操作类型
  riskProductName: string; // 风控产品名
  riskProductTypeName: string; // 风控产品类型名
  status: number; // 状态
}

export interface ProductDataListItem {
  createTime: string;
  creditAmount: number;
  currAmount: number;
  freezeAmount: number;
  overdueAmount: number;
  productFirstTypeName: string;
  productSecondTypeName: string;
  riskLevel: string;
  subAccountNo: string;
  usedAmount: number;
  status: number;
  userName?: string;
  userNo?: string;
  productCode?: string;
  authorizedName?: string;
  productSecondTypeCode?: string;
  lastConfirmDate: number;
  billRepayDate?: number;
}

export interface ModifyStatusParams {
  operationType?: number;
  subAccountNo?: string;
  token?: string;
  userNo?: string;
}

export interface PaybackListItem {
  accountName: string;
  accountNumber: string;
  billNo: string;
  contactPerson: string;
  contactPhone: string;
  createdAt: string;
  daysOverdue: number;
  overdueAmount: number;
  overdueId: string;
  overdueInterest: number;
  overduePenaltyInterest: number;
  productName: string;
  returnedAmount: number;
  urgePerson: string;
  urgeRecentlyMsg: string;
  urgeRecentlyTime: string;
  urgeState: number;
}

export interface AuthorItem {
  authorizedName: string;
  idType: string;
  authorizedIdCard: string;
  authorizedPhone: string;
  email: string;
}
export interface ItemType {
  filePath: string;
  name: string;
}
export interface UpdateAuthor {
  authorizedIdCard?: string;
  authorizedName?: string;
  authorizedPhone?: string;
  email?: string;
  enclosure?: ItemType[];
  enterpriseId?: string;
  idType?: number;
  productSecondTypeCode?: string;
}

export interface AuthInfoLog {
  current?: number;
  enterpriseId?: string;
  pageSize?: number;
  productSecondTypeCode?: string;
  bizType?: string;
}

export interface LogListItem {
  afterValue?: [];
  beforeValue?: [];
  createdAt?: string;
  modifyItem?: string;
  operatorBy?: string;
  changeType?: string;
}

export interface ModifyData {
  operationToken?: string;
  productSecondTypeCode?: string;
  repayDate?: number;
  userNo?: string;
  updateAt?: string;
  oldRepayDate?: string | number;
}
export interface StatusModifyLog {
  userNo: string;
  subAccountNo: string;
  current: number;
  pageSize: number;
}

export interface ProductQuotaBankWaterListItem {
  createdAt?: string;
  extendInfo?: string;
  operationAmount?: string;
  operationType?: number;
  subAfterAmount?: string;
  userNo?: string;
}

export interface AccoutFlowParams {
  subAccountNo?: string;
  createdAtEnd?: string;
  createdAtStart?: string;
  current?: string;
  operationType?: number;
  pageSize?: string;
}

export interface BillListParams {
  pageNumber?: number;
  pageSize?: number;
  accountName?: string;
  accountNumber?: string;
  billCycleTime?: string;
  billNo?: string;
  productName?: string;
  status?: number;
  secondaryClassification?: string;
  accountNumber?: string;
  userNo?: string;
}

export interface BillListItem {
  accountName: string;
  accountNumber: string;
  billCycle: string;
  billNo: string;
  billingDate: string;
  orderNo: string;
  productName: string;
  status: number;
  totalAmount: number;
}
export interface OrderListItem {
  orderAmount: string;
  orderNo: string;
  orderTime: number;
  productName: string;
  status: string;
  userName: number;
  userNo: string;
}

export interface OrderListParams {
  channel?: string;
  classification?: string;
  current?: number;
  endApplyTime?: string;
  orderNo?: string;
  pageSize?: number;
  productName?: string;
  secondClassification?: string;
  startApplyTime?: string;
  status?: number;
  userName?: string;
  userNo?: string;
  secondaryClassification?: string;
}

export type RiskLevelChangeRecordParams = {
  changeType: string;
  userNo: string;
  productSecondTypeCode: string;
};
