/*
 * @Date: 2024-08-13 18:39:20
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2025-03-03 18:25:15
 * @FilePath: /lala-finance-biz-web/src/pages/CarInsuranceChannel/components/ChangeCorrelation.tsx
 * @Description:
 */
import type { ProFormInstance } from '@ant-design/pro-components';
import { ModalForm } from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import { Button, Form, message, Modal } from 'antd';
import React, { memo, useEffect, useRef } from 'react';
import { changeCorrelation, deleteChannel, getRoleList } from '../services';
import { getPersonalOrEnterpriseHasVal } from '../util';
import CorRelation from './CorRelation';

type TrelationData = {
  id: number;
  channelCode: string;
  relatedProduct: number[]; // 关联的产品列表
  relatedEnterprise: number[]; // 关联的企业列表
  relatedPayAccount: number[]; // 关联的收款公司列表
  channelName: string; //渠道名称
  relatedProductAndEnterpriseFlat?: Record<string, any>[];
  personalRelatedPayAccount: string[]; // 关联的收款公司列表（个人）
  extMappingAccountBO?: {
    accountId: string; // 账号id
    externalOwnerId: string | undefined; // 内部账号id
    externalAccountId: string; // 外部账号id
    bankNo: string; // 银行卡号
    bankName: string; // 银行卡名
    accountName: string; // 账户名
  }; // 专属账号信息
  withholdingChannels: string; // 代偿渠道
};
type Props = {
  relationData: TrelationData;
  visible: boolean;
  setVisible: (val: boolean) => void;
  action: ActionType | undefined;
  isSecondList: boolean;
  parentChannelInfo?: Record<string, string>;
};
const ChangeCorrelation: React.FC<Props> = (props) => {
  const { relationData, visible, setVisible, action, isSecondList, parentChannelInfo } = props;
  const [form] = Form.useForm();
  const formRef = useRef<ProFormInstance>();
  const corRelationRef = useRef<any>();
  useEffect(() => {
    const fetchData = async () => {
      getRoleList(relationData?.channelCode).then((res) => {
        form.setFieldsValue({
          roleCodeList: res?.data || [],
          // roleCodeList: ['companyInsuranceChannelUser', 'orderInsuranceChannelUser'],
        });
      });
    };
    //如果是二级渠道
    if (isSecondList) {
      fetchData();
    }
    const { relatedProductAndEnterpriseFlat, ...rest } = relationData;

    // console.log(relationData);
    form.setFieldsValue({
      ...rest,
      relatedProductAndEnterpriseFlat: relatedProductAndEnterpriseFlat || [{}],
    });
    corRelationRef.current?.onCheckRules(relationData);
    try {
      console.log('corRelationRef.current?.onCheckRules', corRelationRef.current?.onCheckRules);
      const timerId = setTimeout(() => {
        corRelationRef.current?.onCheckRules(relationData);
        clearTimeout(timerId);
      }, 800);
    } catch (error) {
      console.log(error);
    }
  }, [relationData, form, isSecondList]);

  return (
    <ModalForm<TrelationData>
      form={form}
      formRef={formRef}
      visible={visible}
      title="修改关联"
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 14 }}
      layout="horizontal"
      modalProps={{
        onCancel: () => {
          setVisible(false);
        },
        destroyOnClose: true,
      }}
      submitter={{
        render: (_, defaultDoms) => {
          return [
            <Button
              key="delete"
              type="primary"
              danger
              onClick={() => {
                Modal.confirm({
                  title: '删除渠道',
                  content: `你确定要删除渠道-${relationData?.channelName}-吗`,
                  async onOk() {
                    await deleteChannel(relationData.id);
                    message.success('删除成功');

                    action?.reload();
                    setVisible(false);
                  },
                });
              }}
            >
              删除
            </Button>,
            defaultDoms[1],
          ];
        },
      }}
      onFinish={async (values) => {
        // console.log(
        //   values,
        //   '---',
        //   values?.relatedProductAndEnterpriseFlat?.filter(
        //     (item) => item !== null && item !== undefined,
        //   ) || [],
        // );
        // return;
        const { id, channelCode } = relationData;
        const personalRelatedPayAccount = formRef?.current?.getFieldValue(
          'personalRelatedPayAccount',
        );
        const { personal, enterprise } = getPersonalOrEnterpriseHasVal(values);
        let personalRelatedPayAccountT, relatedPayAccountT;
        //个人配置公用保司选择，后端字段依旧沿用两个。需要根据产品与企业的是否选中判断一下
        if (personal) {
          personalRelatedPayAccountT = personalRelatedPayAccount;
        }
        if (enterprise) {
          relatedPayAccountT = personalRelatedPayAccount;
        }
        await changeCorrelation({
          ...values,
          relatedProductAndEnterpriseFlat:
            values?.relatedProductAndEnterpriseFlat?.filter(
              (item) => item !== null && item !== undefined,
            ) || [],
          id,
          channelCode,
          personalRelatedPayAccount: personalRelatedPayAccountT,
          relatedPayAccount: relatedPayAccountT,
        });
        message.success('提交成功');
        setVisible(false);
        action?.reload();
        return true;
      }}
      onValuesChange={(val, values) => {
        //  校验规则
        corRelationRef.current?.onCheckRules(values);
      }}
    >
      <CorRelation
        playground="edit"
        corRelationRef={corRelationRef}
        formRef={formRef}
        isSecondList={isSecondList}
        parentChannelInfo={parentChannelInfo}
        currentChannelCode={relationData?.channelCode}
        extMappingAccountBO={relationData?.extMappingAccountBO}
        channelName={relationData?.channelName}
        withholdingChannels={relationData?.withholdingChannels}
        personalRelatedPayAccount={
          relationData?.personalRelatedPayAccount || relationData?.relatedPayAccount
        } // 个人配置公用保司选择，后端字段依旧沿用两个。哪个字段有值都需要赋值
      />
    </ModalForm>
  );
};
export default memo(ChangeCorrelation);
