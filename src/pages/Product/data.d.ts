/*
 * @Author: your name
 * @Date: 2020-11-23 17:22:42
 * @LastEditTime: 2025-04-18 11:17:12
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/data.d.ts
 */
export interface ProductListParams {
  pageNumber?: number;
  pageSize?: number;
  status?: number;
  startInterest?: number;
  endInterest?: number;
  // annualInterestRate: string;
  // classification: string;
  // loadFromName: string;
  // productCode: string;
  // productName: string;
  // repaymentModeName: string;
  // secondaryClassification: string;
  // status: boolean;
  // userType: string;
}

export interface ProductListItem {
  annualInterestRate: string;
  classification: string;
  loadFromName: string;
  productCode: string;
  productName: string;
  repaymentModeName: string;
  secondaryClassification: string;
  status: number;
  userType: string;
  id: number;
  interest: number;
  guaranteeType: string;
  guaranteePeriods: number;
  periodsList: number[];
  createdAt?: string;
  registerLicenceType: string;
}
export interface ProductListPagination {
  total: number;
  size: number;
  current: number;
}
export interface ProductListListData {
  list: ProductListItem[];
  pagination: Partial<ProductListPagination>;
}

export interface OverdueConfig {}
