/*
 * @Date: 2024-01-27 15:27:54
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-01-27 15:54:55
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/CashProductConfig/ProCashProcessConfig.tsx
 * @Description:
 */
import React, { useState, useEffect } from 'react';
import { Card, Tabs } from 'antd';
import globalStyle from '@/global.less';
import IncomingCash from './IncomingCash';
import LoanCash from './LoanCash';
import OverdueCash from './OverdueCash';
import SettleEarlyCash from './SettleEarlyCash';
import PayBackCash from './PayBackCash';
import ClaimCash from './ClaimCash';
import BillCash from './BillCash';

type ProLeaseProcessConfigProps = {
  isAddProduct?: boolean;
  data?: any;
  handleProcessTabsChanges?: (
    tabItemKey?: string,
    changedFormValues?: any,
    allFormValues?: any,
  ) => void;
  finalErrorTab?: string;
  drawerStatus?: boolean;
};
const ProLeaseProcessConfig: React.FC<ProLeaseProcessConfigProps> = ({
  data,
  isAddProduct,
  // handleProcessTabsChanges,
  finalErrorTab,
  drawerStatus = true,
}) => {
  const [activeKey, setActiveTabKey] = useState<string>('leaseIncoming');
  useEffect(() => {
    setActiveTabKey(finalErrorTab || 'leaseIncoming');
  }, [finalErrorTab]);
  return (
    <Card title="产品流程配置" className={globalStyle.mt30}>
      <Tabs
        activeKey={activeKey}
        onTabClick={(key) => {
          setActiveTabKey(key);
        }}
      >
        <Tabs.TabPane tab="进件" key="leaseIncoming" forceRender>
          <IncomingCash
            name="leaseIncoming"
            drawerStatus={drawerStatus}
            activateDTO={data?.activateDTO}
            isAddProduct={isAddProduct}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="放款" key="loan" forceRender>
          <LoanCash name="loan" loanData={data?.loanDTO} drawerStatus={drawerStatus} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="账单" key="bill" forceRender>
          <BillCash name="bill" billData={data?.billDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="债权转让" key="claim" forceRender>
          <ClaimCash name="claim" claimData={data?.debentureTransferDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="还款" key="payback" forceRender>
          <PayBackCash
            payBackData={data?.repaymentDTO}
            name="payback"
            isAddProduct={isAddProduct}
            repayDateCalculateEnum={data?.billDTO?.repayDateCalculateEnum}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="提前结清" key="settleEarly" forceRender>
          <SettleEarlyCash settleEarlyData={data?.inAdvanceRepaymentDTO} name="settleEarly" />
        </Tabs.TabPane>
        <Tabs.TabPane tab="逾期" key="overdue" forceRender>
          <OverdueCash overDueData={data?.overdueDTO} isLease name="overdue" />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};

export default React.memo(ProLeaseProcessConfig);
