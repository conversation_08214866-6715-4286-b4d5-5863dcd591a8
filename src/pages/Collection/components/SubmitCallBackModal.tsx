/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-29 10:45:55
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-11-19 18:07:51
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/SubmitCallBackModal.tsx
 * @Description: SubmitCallBackModal
 */
import {
  BOHAI_BANK_NO,
  LEASE_LOAN_CHANNEL,
  PRODUCT_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_MAP_ALL,
} from '@/enums';
import { getProductNameEnum } from '@/services/enum';
import { accountRule, companyNameRule } from '@/services/validate';
import { disableFutureDate } from '@/utils/utils';
import type { ProColumns } from '@ant-design/pro-components';
import { EditableProTable, ProTable } from '@ant-design/pro-components';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import { useDebounceFn, useRequest } from 'ahooks';
import { Button, Form, message } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import type { AmountDetailType, BillSubmitCallBackParams, IbillInfo } from '../data';
import { combineRepayBack, getRepayApplyId, submitBillCallBack } from '../service';
import { calculateBill, getCollectBillList } from '../services';
import { IdimensionEnCode } from '../types';
import { CarOfflineRepayColumns, OrderOfflineRepayColumns } from './columns';
import { UploadCom } from './index';

export type SubmitCallBackModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  deadLine: string;
  overDueAmount: number;
  overdueCaseNo: string;
  userNo: string;
  isFactoring: boolean; //是否是商业保理的催收单
  productCode: string;
  productSecondaryCode: string;
  funderChannelCode?: string; // 资方渠道
  accountName: string;
  billType: number; // 账单类型 1——自有账单；2——共享账单
  repayOverdueCostBO: [];
  orderNoList: { orderNo: string; totalOverdueAmount: string; value: string }[];
  dimension?: number;
};

const receiveAccountInfo = {
  receiveBank: '招商银行股份有限公司广州黄埔大道支行',
  receiveBankAccount: '***************',
  receiveBankName: '广州易人行商业保理有限公司',
};

const PAYBACK_TYPE = {
  1: '线下还款',
  2: '普通对公还款',
  3: '扫码还款',
};

const RECIEVE_BANK_CHANNEL = {
  2: '***************', //  对公打款
  3: '**********', //  二维码
};

const SubmitCallBackModal: React.FC<SubmitCallBackModalProps> = (props) => {
  const { urgePersonList: userList } = useModel('userList');
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [getCollectBillListLoading, setCollectBillListLoading] = useState(false);
  const [errorDesc, setErrorDesc] = useState('');
  const [billInfoData, setBillInfoData] = useState<IbillInfo>({} as any);
  const { overdueCaseNo, productCode, productSecondaryCode, orderNoList, dimension = 1 } = props;

  const { data: productEnum } = useRequest(getProductNameEnum);

  const billListOptions = useMemo(() => {
    const list: object[] = [];
    (props.repayOverdueCostBO || []).forEach((item: any) => {
      if (item.startTime) {
        list.push({
          label: item.billNo,
          value: item.startTime,
        });
      }
    });
    return list;
  }, [props.repayOverdueCostBO]);

  // 小贷和融租的提交
  const cashSubmit = async (value) => {
    const fileList: object[] = [];
    const length = value?.fileList?.length || 0;
    for (let i = 0; i < length; i += 1) {
      const item = value.fileList[i];
      const { response, name, uid } = item || {};
      if (response?.data) {
        fileList.push({
          url: response.data,
          name,
          uid,
          filePath: null,
        });
      }
    }
    const {
      remission,
      urgePerson,
      urgeRecentlyMsg,
      orderNo,
      amount,
      paymentTime,
      thirdFlowId,
      repayChannel,
      compensation,
      repayBankNo,
    } = value;
    try {
      await submitBillCallBack({
        billInfoList: billInfoData?.billRspDTOList,
        remissionList: [{ ...remission[1] }],
        repayAmount: amount,
        attach: fileList,
        bankAmountDate: paymentTime,
        overdueCaseNo,
        orderNo: orderNo?.value,
        urgePersonId: urgePerson,
        dimension,
        repaySerialNo: thirdFlowId,
        remitType: repayChannel,
        compensation,
        remark: urgeRecentlyMsg,
        payeeBankNo: repayBankNo,
      } as BillSubmitCallBackParams);
      message.success('添加成功');
      props.onOk();

      // eslint-disable-next-line no-empty
    } catch (error) {}
    return true;
  };

  // 保理提交
  const factoryingSubmit = async (value) => {
    const {
      accountBank,
      subAccountBank,
      bankName,
      bankNumber,
      billType,
      billDate,
      amount,
      paymentTime,
      payBackType,
      urgePerson,
      urgeRecentlyMsg,
    } = value;
    const curBill: any =
      props?.repayOverdueCostBO?.find((item: any) => item?.startTime === billDate) || {};
    // 获取还款唯一键
    const applyIdRes = await getRepayApplyId();
    // 保理走保理的逻辑，融租和小贷只传催收还款的数据
    const params = {
      initiateBillRepaymentReq: {
        accountNumber: props.userNo,
        productCode: props.productCode,
        applyId: applyIdRes?.data,
        accountBank: accountBank + subAccountBank,
        bankName,
        bankNumber,
        billType,
        billDate,
        repayChannel: 1, //还款方式： 1,普通对公汇款;2,溢缴款冲抵;3,专属账号还款;4,溢缴款提取;5,人工后台还款;6,主动还款;7,后台人工对公还款
        repayEntrance: 1, //还款入口： 1 催收案件管理，提交催收回款 2、账单管理，线下还款
        repayMoney: amount,
        repayTime: paymentTime,
        payBillAccountNumbers: [{ accountNumber: props.userNo, billNOs: [curBill.billNo] }],
        certificateUrl: undefined,
      },
      paymentRecordReq: {
        amount,
        fileList: [] as object[],
        overdueCaseNo,
        payBackType,
        paymentTime,
        urgePerson,
        urgeRecentlyMsg,
      },
    };

    const fileList: { fileName: string; fileUrl: string; netWorkPath: string }[] = [];
    const length = value?.fileList?.length || 0;
    for (let i = 0; i < length; i += 1) {
      const item = value.fileList[i];
      if (item.response) {
        const url = item.response.data;
        if (url) {
          fileList.push({
            fileName: item.name,
            fileUrl: url,
            netWorkPath: url,
          });
          if (props.isFactoring) {
            params.initiateBillRepaymentReq.certificateUrl = url; // 付款凭证
          }
        }
      }
    }
    // 写入附件
    params.paymentRecordReq.fileList = fileList;
    try {
      await combineRepayBack(params as any);
      message.success('添加成功');
      props.onOk();
    } catch (error) {
      return false;
    }
    return true;
  };

  // 获取催收回款账单列表
  const getCollectBillListData = useCallback(
    async (orderNo: string) => {
      if (!orderNo) {
        return;
      }
      try {
        setCollectBillListLoading(true);
        const res = await getCollectBillList({
          caseNo: overdueCaseNo,
          orderNo,
        });
        setBillInfoData(res);
        const { totalPrincipalUnpaid, totalInterestUnpaid, totalOverduePenaltyUnpaid, totalLate } =
          getAmount(res) || {};
        form?.setFieldValue('remission', [
          {
            id: 1,
            remissionPrincipal: totalPrincipalUnpaid,
            remissionInterest: totalInterestUnpaid,
            remissionPenalty: totalOverduePenaltyUnpaid,
            remissionDelayAmount: totalLate,
          },
          {
            id: 2,
            remissionPrincipal: 0,
            remissionInterest: 0,
            remissionPenalty: 0,
            remissionDelayAmount: 0,
          },
        ]);
      } catch (error) {
      } finally {
        setCollectBillListLoading(false);
      }
    },
    [overdueCaseNo],
  );

  //从后端获取数据
  const getRepayData = async () => {
    try {
      const err = form?.getFieldsError(['remission', 'amount']);
      const hasErrors = err.some((item) => item.errors.length > 0);
      //如果有校验error不请求后端接口
      if (hasErrors) return;
      const curFormData = form?.getFieldsValue();
      const { remission, amount } = curFormData || {};
      setLoading(true);
      const remissionAmount = calculateRemissionAmount(remission?.[1]);
      const totalAmount = new BigNumber(amount || 0).plus(remissionAmount).toNumber();
      form?.setFieldValue('remissionAmount', remissionAmount);
      form?.setFieldValue('totalAmount', totalAmount);
      const res = await calculateBill({
        billInfoList: billInfoData?.billRspDTOList || [],
        remissionList: [{ ...remission[1] }],
        repayAmount: amount || 0, // 回款金额
      });
      setErrorDesc('');
      setBillInfoData(res);
    } catch (error: any) {
      //试算单独处理报错逻辑
      if (![0, 200].includes(error?.ret)) {
        setErrorDesc(error?.msg);
      }
    } finally {
      setLoading(false);
    }
  };

  const { run, cancel } = useDebounceFn(getRepayData, { wait: 500 });

  // 计算各项减免金额上限，用于校验
  function getAmount(res?: IbillInfo) {
    const init = {
      expectRepayAmount: 0,
      totalAmountUnpaid: 0,
      totalPrincipalUnpaid: 0,
      totalInterestUnpaid: 0,
      totalOverduePenaltyUnpaid: 0,
      totalBreach: 0,
      totalLate: 0,
    };
    const originData = res || billInfoData;
    if (originData?.billRspDTOList?.length) {
      return originData?.billRspDTOList?.reduce((pre: any, cur) => {
        const {
          expectRepayAmount,
          totalAmountUnpaid,
          totalPrincipalUnpaid,
          totalInterestUnpaid,
          totalOverduePenaltyUnpaid,
          totalBreach,
          totalLate,
        } = cur;
        const _expectRepayAmount = new BigNumber(pre.expectRepayAmount)
          .plus(expectRepayAmount)
          .toNumber();
        const _totalAmountUnpaid = new BigNumber(pre.totalAmountUnpaid)
          .plus(totalAmountUnpaid)
          .toNumber();
        const _totalPrincipalUnpaid = new BigNumber(pre.totalPrincipalUnpaid)
          .plus(totalPrincipalUnpaid)
          .toNumber();
        const _totalInterestUnpaid = new BigNumber(pre.totalInterestUnpaid)
          .plus(totalInterestUnpaid)
          .toNumber();
        const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
          .plus(totalOverduePenaltyUnpaid)
          .toNumber();
        const _totalBreach = new BigNumber(pre.totalBreach).plus(totalBreach).toNumber();
        const _totalLate = new BigNumber(pre.totalLate).plus(totalLate).toNumber();
        return {
          expectRepayAmount: isNaN(_expectRepayAmount) ? 0 : _expectRepayAmount,
          totalAmountUnpaid: isNaN(_totalAmountUnpaid) ? 0 : _totalAmountUnpaid,
          totalPrincipalUnpaid: isNaN(_totalPrincipalUnpaid) ? 0 : _totalPrincipalUnpaid,
          totalInterestUnpaid: isNaN(_totalInterestUnpaid) ? 0 : _totalInterestUnpaid,
          totalOverduePenaltyUnpaid: isNaN(_totalOverduePenaltyUnpaid)
            ? 0
            : _totalOverduePenaltyUnpaid,
          totalBreach: isNaN(_totalBreach) ? 0 : _totalBreach,
          totalLate: isNaN(_totalLate) ? 0 : _totalLate,
        };
      }, init);
    } else {
      return init;
    }
  }

  // 计算减免总金额
  function calculateRemissionAmount(remission: any) {
    const {
      remissionPrincipal,
      remissionInterest,
      remissionPenalty,
      remissionDelayAmount,
    } = remission;
    const amount = new BigNumber(remissionPrincipal)
      .plus(remissionInterest)
      .plus(remissionPenalty)
      .plus(remissionDelayAmount)
      .toNumber();
    return amount;
  }

  // 减免编辑项
  const editColumns: ProColumns<AmountDetailType>[] = [
    {
      title: '费项',
      dataIndex: 'option',
      editable: false,
      width: 120,
      align: 'center',
      render: (_, __, index) => {
        return index === 0 ? '应还逾期金额' : '减免金额';
      },
    },
    {
      title: '本金(元)',
      dataIndex: 'remissionPrincipal',
      valueType: 'digit',
      align: 'center',
      editable: (_, __, index) => index !== 0,
      fieldProps: { style: { width: '85%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalPrincipalUnpaid,
            message: `金额不可超过${getAmount().totalPrincipalUnpaid}`,
          },
        ],
      },
    },
    {
      title: '利息(元)',
      dataIndex: 'remissionInterest',
      valueType: 'digit',
      align: 'center',
      editable: (_, __, index) => index !== 0,
      fieldProps: { style: { width: '85%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalInterestUnpaid,
            message: `金额不可超过${getAmount().totalInterestUnpaid}`,
          },
        ],
      },
    },
    {
      title: '罚息(元)',
      dataIndex: 'remissionPenalty',
      valueType: 'digit',
      align: 'center',
      editable: (_, __, index) => index !== 0,
      fieldProps: { style: { width: '85%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalOverduePenaltyUnpaid,
            message: `金额不可超过${getAmount().totalOverduePenaltyUnpaid}`,
          },
        ],
      },
    },
    {
      title: '滞纳金(元)',
      dataIndex: 'remissionDelayAmount',
      valueType: 'digit',
      align: 'center',
      editable: (_, __, index) => index !== 0,
      fieldProps: { style: { width: '85%' }, onBlur: run, precision: 2 },
      formItemProps: {
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            type: 'number',
            max: getAmount().totalLate,
            message: `金额不可超过${getAmount().totalLate}`,
          },
        ],
      },
    },
  ];

  useEffect(() => {
    //非循环单笔
    if (productCode?.substring(0, 2) !== PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN) {
      getCollectBillListData(orderNoList?.[0]?.value);
    }
  }, [orderNoList, getCollectBillListData, productCode]);

  return (
    <>
      <ModalForm
        title="提交催收回款"
        width={800}
        layout="horizontal"
        grid={true}
        rowProps={{ gutter: 0 }}
        labelCol={{ span: 8 }}
        form={form}
        open={props.modalVisible}
        onOpenChange={props.onVisibleChange}
        loading={getCollectBillListLoading || loading}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          onCancel: () => {
            cancel(); //取消防抖请求 有bug,关闭弹窗，同时是失焦的会触发计算请求。有一次报错展示
            setErrorDesc('');
          },
        }}
        initialValues={{
          productCode,
          productSecondaryCode,
          billType: '1',
          bankName: props.accountName,
          payBackType: props.isFactoring ? '2' : undefined, // 保理默认还款方式普通对公还款
          ...receiveAccountInfo,
          urgePerson:
            currentUser?.accountName ?? currentUser?.extSource?.storeName ?? currentUser?.userId,
          remission: [
            {
              id: 1,
              remissionPrincipal: 0,
              remissionInterest: 0,
              remissionPenalty: 0,
              remissionDelayAmount: 0,
            },
            {
              id: 2,
              remissionPrincipal: 0,
              remissionInterest: 0,
              remissionPenalty: 0,
              remissionDelayAmount: 0,
            },
          ],
        }}
        onFinish={async (value: any) => {
          try {
            if (errorDesc) {
              message.error(errorDesc);
              return;
            }
            if (props.isFactoring) factoryingSubmit(value);
            else {
              const { remission, amount } = form?.getFieldsValue() || {};
              const remissionAmount = calculateRemissionAmount(remission?.[1]);
              const totalAmount = new BigNumber(amount || 0).plus(remissionAmount).toNumber();
              if (totalAmount === 0) {
                message.error('总金额不能为0');
                return;
              }
              setLoading(true);
              await cashSubmit(value);
              message.success('提交成功');
              props.onVisibleChange(false);
            }
          } catch (error) {
          } finally {
            setLoading(false);
          }
        }}
        submitter={{
          render: (propsSubmitter, defaultDoms) => (
            <div>
              <Button
                type="primary"
                key="submit"
                disabled={getCollectBillListLoading || loading}
                style={{ marginRight: 10 }}
                onClick={() => propsSubmitter.form?.submit?.()}
              >
                提交
              </Button>
              {defaultDoms[0]}
            </div>
          ),
        }}
      >
        <h4 style={{ width: '100%' }}>基础信息</h4>
        <ProFormSelect
          label="产品一级分类"
          name="productSecondaryCode"
          valueEnum={SECONDARY_CLASSIFICATION_MAP_ALL}
          colProps={{ span: 12 }}
          readonly
        />
        {
          // 车险分期有多个订单 多个产品 所以干脆不展示
          productCode.substring(0, 4) === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE ? null : (
            <ProFormSelect
              label="产品类型"
              name="productCode"
              options={productEnum}
              colProps={{ span: 12 }}
              readonly
              width="sm"
            />
          )
        }

        <ProFormSelect
          rules={[{ required: true }]}
          options={userList}
          placeholder="请输入催收员"
          name="urgePerson"
          label="催收员"
          width="sm"
          colProps={{ span: 12 }}
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            disabled: true,
          }}
        />
        {productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN && (
          <ProFormSelect
            rules={[{ required: true }]}
            request={async () => orderNoList}
            placeholder="请输入订单号"
            disabled={getCollectBillListLoading}
            onChange={async (value: any) => {
              getCollectBillListData(value?.value);
            }}
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
              labelInValue: true,
            }}
            colProps={{ span: 12 }}
            width="md"
            name="orderNo"
            label="订单号"
          />
        )}
        {props.isFactoring && (
          <ProFormSelect
            name="billType"
            label="账单类型"
            placeholder="请输入回款方式"
            rules={[{ required: true }]}
            colProps={{ span: 12 }}
            width="sm"
            valueEnum={{
              1: '自有账单',
              2: '共享账单',
            }}
            disabled
          />
        )}
        {props.isFactoring && (
          <ProFormSelect
            name="billDate"
            label="指定账单还款"
            width="sm"
            colProps={{ span: 12 }}
            rules={[{ required: true }]}
            options={billListOptions}
          />
        )}
        <h4 style={{ width: '100%' }}>回款信息</h4>
        <ProFormDigit
          name="totalAmount"
          label="总金额"
          fieldProps={{ min: 0, precision: 2 }}
          colProps={{ span: 24 }}
          labelCol={{ span: 4 }}
          width="sm"
          readonly
        />
        <ProFormDigit
          name="amount"
          label="回款金额"
          placeholder="请输入回款金额"
          colProps={{ span: 24 }}
          labelCol={{ span: 4 }}
          width="sm"
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 区分产品，小贷有多个循环额度，按订单维度区分，回款金额也会按每个订单的限度
                // 有坑，同时出现很多个error,待优化
                // 融租
                if (
                  productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE &&
                  /^\d+(\.\d+)?$/.test(val) &&
                  val &&
                  val > props.overDueAmount
                ) {
                  return Promise.reject(new Error('催收回款金额不能大于逾期金额'));
                }
                // 小贷区分订单
                if (
                  productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN &&
                  /^\d+(\.\d+)?$/.test(val) &&
                  val &&
                  val > Number(form?.getFieldValue('orderNo')?.totalOverdueAmount)
                ) {
                  return Promise.reject(new Error('催收回款金额不能大于逾期金额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  return Promise.reject(new Error('请输入数字'));
                }
                return Promise.resolve();
              },
            },
          ]}
          fieldProps={{
            min: 0,
            precision: 2,
            onBlur: () => run(),
          }}
        />
        {[
          PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE,
          PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN,
        ].includes(productCode?.substring(0, 2) as any) && (
          <>
            <ProFormDigit
              name="remissionAmount"
              label="减免金额"
              colProps={{ span: 24 }}
              labelCol={{ span: 4 }}
              fieldProps={{ required: true, min: 0, precision: 2 }}
              width="sm"
              readonly
            />
            <EditableProTable
              name="remission"
              rowKey="id"
              toolBarRender={false}
              pagination={false}
              columns={editColumns}
              recordCreatorProps={false}
              scroll={{ x: '100%' }}
              editable={{
                type: 'multiple',
                editableKeys: [1],
                actionRender: () => {
                  return [];
                },
              }}
            />
            <h4 style={{ width: '100%', color: 'red', marginTop: 10 }}>
              请确认账单信息和还款金额无误后提交：
            </h4>
            <ProTable
              search={false}
              options={false}
              pagination={false}
              style={{ width: '100%' }}
              columns={
                dimension === IdimensionEnCode.ORDER_TERM_BILL
                  ? OrderOfflineRepayColumns
                  : CarOfflineRepayColumns.map((column) => {
                      // 小贷（圆易借）不展示车架号
                      if (
                        productSecondaryCode === SECONDARY_CLASSIFICATION_CODE.PRIVATE_MONEY &&
                        (column.dataIndex === 'subjectMatterNo' || column.key === 'subjectMatterNo')
                      ) {
                        return {
                          ...column,
                          hidden: true,
                        };
                      }
                      return column;
                    })
              }
              dataSource={billInfoData?.billRspDTOList as any}
              scroll={{ x: 'max-content' }}
            />
          </>
        )}
        {/* 保理展示收款信息 */}
        {props.isFactoring && (
          <>
            <h4 style={{ width: '100%', marginTop: 20 }}>收款信息</h4>
            <ProFormText
              name="receiveBank"
              label="收款银行"
              rules={[{ required: true }]}
              colProps={{ span: 12 }}
              width="md"
              readonly
            />
            <ProFormText
              name="receiveBankAccount"
              label="收款账户"
              rules={[{ required: true }]}
              colProps={{ span: 12 }}
              width="md"
              readonly
            />
            <ProFormText
              name="receiveBankName"
              label="开户名称"
              rules={[{ required: true }]}
              colProps={{ span: 12 }}
              width="md"
              readonly
            />
          </>
        )}
        <br />
        <h4 style={{ width: '100%', marginTop: 20 }}>付款信息</h4>
        {props.isFactoring && (
          <>
            <ProFormText
              name="bankNumber"
              label="付款账号"
              placeholder="请输入付款账号"
              rules={[{ required: true }, accountRule]}
              colProps={{ span: 12 }}
              width="sm"
            />
            <ProFormText
              name="bankName"
              label="付款户名"
              placeholder="请输入付款户名"
              rules={[{ required: true }, companyNameRule]}
              colProps={{ span: 12 }}
              width="sm"
            />
            <ProFormText
              name="accountBank"
              fieldProps={{ maxLength: 30 }}
              label="付款银行"
              rules={[{ required: true }]}
              colProps={{ span: 12 }}
              width="sm"
            />
            <ProFormText
              name="subAccountBank"
              fieldProps={{ maxLength: 30 }}
              label="付款银行支行"
              rules={[{ required: true }]}
              colProps={{ span: 12 }}
              width="sm"
            />
          </>
        )}
        {/* 上传付款凭证 */}
        <UploadCom
          afterUpload={() => {
            setLoading(false);
          }}
          uploading={() => {
            setLoading(true);
          }}
          span={4}
        />
        {
          // 融租和小贷的回款方式合并到了支付方式，不再展示
          ![
            PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE,
            PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN,
          ].includes(productCode?.substring(0, 2) as any) && (
            <>
              <ProFormSelect
                name="payBackType"
                label="回款方式"
                placeholder="请选择回款方式"
                rules={[{ required: true }]}
                colProps={{ span: 12 }}
                width="sm"
                valueEnum={PAYBACK_TYPE}
                readonly={props.isFactoring}
              />
            </>
          )
        }
        <ProFormDatePicker
          name="paymentTime"
          label="回款日期"
          rules={[{ required: true }]}
          colProps={{ span: 12 }}
          placeholder="请输入回款日期"
          width="sm"
          fieldProps={{ disabledDate: disableFutureDate }}
        />
        {
          // 只有融租才有是否代偿
          productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE && (
            <ProFormRadio.Group
              name="compensation"
              label="是否代偿"
              rules={[{ required: true }]}
              colProps={{ span: 12 }}
              options={[
                {
                  label: '是',
                  value: 1,
                },
                {
                  label: '否',
                  value: 0,
                },
              ]}
            />
          )
        }
        {
          // 融租和小贷业务线新增银行卡号，三方还款流水号，支付方式字段
          [
            PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE,
            PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN,
          ].includes(productCode?.substring(0, 2) as any) && (
            <>
              <ProFormSelect
                name="repayChannel"
                label="支付方式"
                placeholder="请选择支付方式"
                rules={[{ required: true }]}
                colProps={{ span: 12 }}
                width="sm"
                options={[
                  {
                    label: '普通对公打款',
                    value: '2',
                  },
                  {
                    label: '线下二维码打款',
                    value: '3',
                  },
                ]}
                fieldProps={{
                  onChange: (val: string) => {
                    if (productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN) {
                      // 小贷写死银行卡号
                      form.setFieldsValue({ repayBankNo: '***************' });
                    } else {
                      form.setFieldsValue({ repayBankNo: RECIEVE_BANK_CHANNEL[val] });
                    }
                  },
                }}
              />
              <ProFormText
                name="thirdFlowId"
                width="sm"
                label="三方还款流水号"
                placeholder="请输入三方还款流水号"
                colProps={{ span: 12 }}
                rules={[
                  {
                    required: true,
                    max: 64,
                    pattern: /^[A-Za-z0-9]+$/,
                    message: '请输入三方还款流水号（支持大小写字母和数字）',
                  },
                ]}
              />
              <ProFormDependency name={['repayChannel']}>
                {({ repayChannel }) => {
                  // 资方渠道是渤海银行时，需要选择收款账号
                  if (
                    props.funderChannelCode === LEASE_LOAN_CHANNEL.BO_HAI_YIN_HANG &&
                    repayChannel === '2'
                  ) {
                    return (
                      <ProFormSelect
                        rules={[{ required: true }]}
                        name="repayBankNo"
                        width="sm"
                        label="银行卡号"
                        colProps={{ span: 12 }}
                        options={[
                          {
                            label: RECIEVE_BANK_CHANNEL[2],
                            value: RECIEVE_BANK_CHANNEL[2],
                          },
                          {
                            label: BOHAI_BANK_NO,
                            value: BOHAI_BANK_NO,
                          },
                        ]}
                      />
                    );
                  }

                  return (
                    <ProFormText
                      name="repayBankNo"
                      width="sm"
                      label="银行卡号"
                      placeholder="请输入银行卡号"
                      disabled={true}
                      colProps={{ span: 12 }}
                      rules={[{ required: true }]}
                      // 小贷不展示tooltip
                      {...(productCode?.substring(0, 2) !==
                        PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN && {
                        tooltip: {
                          title:
                            '因财务对账所需，需录入我们的收款账户号。（微信二维码：**********、对公打款：***************）',
                          overlayStyle: { minWidth: 300 },
                        },
                      })}
                    />
                  );
                }}
              </ProFormDependency>
            </>
          )
        }
        <ProFormTextArea
          label="备注"
          name="urgeRecentlyMsg"
          width="xl"
          colProps={{ span: 24 }}
          labelCol={{ span: 4 }}
          fieldProps={{ maxLength: 500 }}
        />
      </ModalForm>
    </>
  );
};

export default React.memo(SubmitCallBackModal);
