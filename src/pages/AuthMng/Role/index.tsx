import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Col, Drawer, message, Row, Space, Tree, Typography } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import { useAccess } from 'umi';
import { statusMap } from '../const';
import '../index.less';
import {
  bindRolePrivilege,
  // queryFeatureList,
  queryFeatureTreeList,
  queryRoleList,
  queryRolePrivilegeList,
} from '../service';
import PostModal from './PostModal';
import RolesBindSyncModal from './RolesBindSyncModal';
import RolesSyncModal from './RolesSyncModal';

const { Paragraph } = Typography;

interface ListItem {
  id: string;
  roleCode: string;
  roleName: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}
const Page: React.FC<any> = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  // 是否有编辑绑定关系，保存按钮的权限
  const bindsEditAble = access?.hasAccess('save_bind_role_manage');
  // // 展开所有层级
  // const [isExpanded, setIsExpanded] = useState(false);
  // 当前抽屉编辑的数据
  const [currentDrawerRecord, setCurrentDrawerRecord] = useState<ListItem>();
  //
  // 获取所有权限
  // const [allPrivilegeList, setAllPrivilegeList] = useState<any[]>([]);
  // const getAllPrivilegeInfo = async () => {
  //   const list = await queryFeatureList({ current: 1, pageSize: 1000 }).catch(() => ({}));
  //   setAllPrivilegeList(list?.data);
  //   return list?.data || [];
  // };
  // 获取树形权限列表，和角色绑定的权限
  //
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const onExpand = (expandedKeysValue: string[]) => {
    console.log('onExpand', expandedKeysValue);
    setExpandedKeys(expandedKeysValue);
  };
  //
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const onCheck = (checkedKeysValue: any) => {
    console.log('onCheck', checkedKeysValue);
    // 没有编辑权限，不允许操作
    if (!bindsEditAble) {
      return;
    }
    setCheckedKeys(checkedKeysValue?.checked);
  };
  // // 全选
  // const selectAll = () => {
  //   const checkedKeysList = allPrivilegeList.map((item: any) => item.privilegeCode);
  //   setCheckedKeys(checkedKeysList);
  // };
  // // 全不选
  // const selectNone = () => {
  //   setCheckedKeys([]);
  // };
  const [privilegeTreeList, setPrivilegeTreeList] = useState<any>([]);
  // 获取角色，用于同步绑定关系
  const [currentRoleBinds, setCurrentRoleBinds] = useState<any[]>([]);
  //
  const getPrivilegeTreeList = async (roleCode: string) => {
    const treeData = await queryFeatureTreeList().catch(() => []);
    console.log('treeData', treeData);
    setPrivilegeTreeList(treeData?.data);
    const checkedKeysInfo = await queryRolePrivilegeList({ roleCodes: [roleCode] });
    console.log('checkedKeysInfo', checkedKeysInfo);
    // 用于同步的接口数据
    setCurrentRoleBinds(checkedKeysInfo?.data || []);
    //
    const checkedKeysList = checkedKeysInfo?.data?.map((item: any) => item.privilegeCode);
    setExpandedKeys(checkedKeysList);
    setCheckedKeys(checkedKeysList);
  };
  // 关闭抽屉
  const [open, setOpen] = useState(false);
  const handleClose = () => {
    setOpen(false);
    setCheckedKeys([]);
    setExpandedKeys([]);
    setCurrentDrawerRecord(undefined);
  };
  const handleOpen = async (record: ListItem) => {
    console.log(record);
    setCurrentDrawerRecord(record);
    // await getAllPrivilegeInfo().catch(() => {});
    await getPrivilegeTreeList(record.roleCode).catch(() => {});
    setOpen(true);
  };
  const [sumbitLoading, setSumbitLoading] = useState(false);
  // 确定操作
  const saveAction = async () => {
    setSumbitLoading(true);
    const { roleCode } = (currentDrawerRecord as ListItem) || {};
    const privilegeCodes: any = checkedKeys;
    console.log('privilegeCodes', privilegeCodes);
    const res: any =
      (await bindRolePrivilege({
        roleCode,
        privilegeCodeList: privilegeCodes,
      }).catch(() => {})) || {};
    if (res?.success === true) {
      message.success('绑定成功');
      actionRef.current?.reload();
      handleClose();
    }
    setSumbitLoading(false);
  };
  // 弹窗
  const [isEdit, setIsEdit] = useState(false);
  const [visible, setVisible] = useState(false);
  const [listItem, setListItem] = useState<ListItem>();
  const closeModal = () => {
    setVisible(false);
  };
  const openModal = (isEdit: boolean, item?: ListItem) => {
    setIsEdit(isEdit);
    setListItem({} as ListItem);
    if (isEdit) {
      setListItem(item);
    }
    //
    setVisible(true);
  };
  //
  // 同步批量操作
  //

  // 角色列表key
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  // 角色record
  const [selectedRows, setSelectedRows] = useState<ListItem[]>([]);
  // 角色同步弹窗
  const [rolesSyncVisible, setRolesSyncVisible] = useState(false);
  const openRolesSyncModal = () => {
    if (!selectedRows || selectedRows?.length === 0) {
      message.error('请选择需要同步的角色');
      return;
    }
    setRolesSyncVisible(true);
  };
  const closeRolesSyncModal = () => {
    setRolesSyncVisible(false);
  };

  // 同步关系弹窗
  const [RolesBindSyncVisible, setRolesBindSyncVisible] = useState(false);
  const openRolesBindSyncModal = () => {
    setRolesBindSyncVisible(true);
  };
  const closeRolesBindSyncModal = () => {
    setRolesBindSyncVisible(false);
  };
  // 表格列
  const columns: ProColumns<ListItem>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      search: false,
      width: 100,
    },
    {
      title: '角色名称',
      dataIndex: 'roleName',
      search: {
        transform: (value: string) => {
          return {
            roleNameLike: value?.trim(),
          };
        },
      },
    },
    {
      title: '角色编码',
      dataIndex: 'roleCode',
      search: {
        transform: (value: string) => {
          return {
            roleCodeLike: value?.trim(),
          };
        },
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: statusMap,
      search: false,
      width: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 200,
      hideInTable: !(access.hasAccess('edit_role_manage') || access.hasAccess('bind_role_manage')),
      render: (_, record) => (
        <>
          <Row>
            {access.hasAccess('edit_role_manage') && (
              <Col span={8}>
                <Button type="link" onClick={() => openModal(true, record)}>
                  编辑
                </Button>
              </Col>
            )}
            {access.hasAccess('bind_role_manage') && (
              <Col span={12}>
                <Button type="link" onClick={() => handleOpen(record)}>
                  功能
                </Button>
              </Col>
            )}
          </Row>
        </>
      ),
    },
  ];

  return (
    <>
      <PageContainer className={globalStyle.mt16}>
        <ProTable<ListItem>
          actionRef={actionRef}
          formRef={formRef}
          rowSelection={{
            selectedRowKeys: selectedRowKeys,
            onChange: (_selectedRowKeys, _selectedRows) => {
              setSelectedRowKeys(_selectedRowKeys);
              setSelectedRows(_selectedRows);
            },
          }}
          // tableAlertOptionRender={false}
          // tableAlertRender={false}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          request={(params: any) => {
            console.log(params);
            try {
              // 切页、刷新重置多选框为空
              setSelectedRowKeys([]);
              setSelectedRows([]);
            } catch (e) {
              console.log(e);
            }
            return queryRoleList(params);
          }}
          search={{
            defaultCollapsed: false,
            labelWidth: 90,
          }}
          columns={columns}
          toolBarRender={() => {
            return [
              access.hasAccess('sync_role_manage') && (
                <Button
                  key="button"
                  // disabled={selectedRowKeys?.length === 0}
                  type="primary"
                  danger
                  onClick={openRolesSyncModal}
                >
                  同步
                </Button>
              ),
              access.hasAccess('create_role_manage') && (
                <Button key="button" type="primary" onClick={() => openModal(false)}>
                  新建
                </Button>
              ),
            ];
          }}
        />
        <PostModal
          isEdit={isEdit}
          visible={visible}
          listItem={listItem}
          closeModal={closeModal}
          refreshTable={() => actionRef.current?.reload()}
        />
      </PageContainer>
      {/* 抽屉 */}
      <Drawer
        title={`功能`}
        placement="right"
        size="large"
        onClose={handleClose}
        open={open}
        extra={
          <Space>
            <Button onClick={handleClose}>取消</Button>
            {bindsEditAble && (
              <Button type="primary" loading={sumbitLoading} onClick={saveAction}>
                保存
              </Button>
            )}
            {access.hasAccess('sync_bind_role_manage') && (
              <Button key="button" type="primary" danger onClick={openRolesBindSyncModal}>
                同步
              </Button>
            )}
          </Space>
        }
      >
        <div className="tree-wrapper">
          {/* <div className="tree-header">
            <Space>
              <Button type="primary" onClick={selectAll}>
                全选
              </Button>
              <Button onClick={selectNone}>重置</Button>
            </Space>
          </div> */}
          <div className="tree-header">
            <span className="tree-title">当前角色：</span>
            {currentDrawerRecord?.roleName}({currentDrawerRecord?.roleCode})
          </div>
          <div className={`tree-body ${bindsEditAble ? '' : 'disabled-checkbox'}`}>
            <Tree
              checkable
              selectable={false}
              checkStrictly={true}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              onCheck={onCheck}
              checkedKeys={checkedKeys}
              treeData={privilegeTreeList}
              fieldNames={{ title: 'privilegeName', key: 'privilegeCode' }}
              // titleRender={(node: any) => `${node.privilegeName}(${node.privilegeCode})`}
              titleRender={(node: any) => {
                return (
                  <Paragraph copyable={{ text: node?.privilegeCode }} style={{ marginBottom: 0 }}>
                    {`${node.privilegeName}(${node.privilegeCode})`}
                  </Paragraph>
                );
              }}
            />
          </div>
        </div>
      </Drawer>
      {/* 角色同步弹窗 */}
      {rolesSyncVisible && (
        <RolesSyncModal
          visible={rolesSyncVisible}
          selectedRows={selectedRows}
          closeModal={closeRolesSyncModal}
        />
      )}
      {/* 关系同步弹窗 */}
      {RolesBindSyncVisible && (
        <RolesBindSyncModal
          visible={RolesBindSyncVisible}
          roleCode={currentDrawerRecord?.roleCode}
          info={currentRoleBinds}
          closeModal={closeRolesBindSyncModal}
        />
      )}
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'/authMng/role'}>
      <Page />
    </KeepAlive>
  </>
);
