// 融租 - 期账，线下还款
// @ts-ignore
import type { ProColumns } from '@ant-design/pro-components';
import type { IbillListItem } from '../types';

export const OnlineRepayColumns: ProColumns<IbillListItem>[] = [
  {
    title: '还款金额(元)',
    dataIndex: 'expectRepayAmount',
    key: 'expectRepayAmount',
  },
  {
    title: '账单ID',
    dataIndex: 'billNo',
    key: 'billNo',
  },
  {
    title: '期数',
    dataIndex: 'termNumber',
    key: 'termNumber',
  },
  {
    title: '订单号',
    dataIndex: 'orderNo',
    key: 'orderNo',
  },
  {
    title: '账单状态',
    dataIndex: 'statusName',
    key: 'statusName',
  },
  {
    title: '车架号',
    dataIndex: 'subjectMatterNo',
    key: 'subjectMatterNo',
  },
  {
    title: '用户姓名',
    dataIndex: 'accountName',
    key: 'accountName',
  },
  {
    title: '未还总额(元)',
    dataIndex: 'totalAmountUnpaid',
    key: 'totalAmountUnpaid',
  },
  {
    title: '未还本金(元)',
    dataIndex: 'totalPrincipalUnpaid',
    key: 'totalPrincipalUnpaid',
  },
  {
    title: '未还利息(元)',
    dataIndex: 'totalInterestUnpaid',
    key: 'totalInterestUnpaid',
  },
  {
    title: '未还罚息(元)',
    dataIndex: 'totalOverduePenaltyUnpaid',
    key: 'totalOverduePenaltyUnpaid',
  },
];
