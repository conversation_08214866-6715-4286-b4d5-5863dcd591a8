/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 14:19:50
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-26 15:31:36
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/ProductRelativeSubEnterprise.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { downLoadExcel } from '@/utils/utils';
import { history } from '@umijs/max';
import { Button, Table } from 'antd';
import React, { useEffect, useState } from 'react';
import { relatedSubExport } from '../service';

interface ProductRelativeSubEnterpriseProps {
  refresh: () => void;
  tableProps: any;
}

const ProductRelativeSubEnterprise: React.FC<ProductRelativeSubEnterpriseProps> = ({
  refresh,
  tableProps,
}) => {
  const subAccountNo = history?.location?.query?.subAccountNo as string;
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    refresh();
  }, [subAccountNo]);

  const columns = [
    {
      title: '子企业用户ID',
      key: 'userNo',
      dataIndex: 'userNo',
    },
    {
      title: '企业名称',
      key: 'enterpriseName',
      dataIndex: 'enterpriseName',
    },
    {
      title: '总额度',
      key: 'creditAmount',
      dataIndex: 'creditAmount',
    },
    {
      title: '剩余可用额度（元）',
      key: 'currentAmount',
      dataIndex: 'currentAmount',
    },
    {
      title: '授信时间',
      key: 'creditEndTime',
      dataIndex: 'creditEndTime',
    },
    {
      title: '操作',
      type: 'option',
      render: (row: any) => {
        return (
          <a
            onClick={() => {
              history.push(
                `/userMng/enterpriseMng/product-detail?subAccountNo=${row.subAccountNo}&userNo=${row?.userNo}`,
              );
            }}
          >
            查看详情
          </a>
        );
      },
    },
  ];

  const relatedSubExportFunc = (params: { subAccountNo: string }) => {
    setExportLoading(true);
    relatedSubExport(params)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          loading={exportLoading}
          onClick={() => relatedSubExportFunc({ subAccountNo })}
          type="primary"
        >
          导出
        </Button>
      </div>
      <Table columns={columns} {...tableProps} />
    </div>
  );
};

export default ProductRelativeSubEnterprise;
