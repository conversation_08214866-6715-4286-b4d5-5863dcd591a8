/*
 * @Date: 2024-10-12 10:03:46
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-10-14 15:04:42
 * @FilePath: /lala-finance-biz-web/src/pages/CallPay/carInsurance/columns/EarlySettleReviewListColumns.tsx
 * @Description:
 */

export const EarlySettleModalColumns = [
  {
    title: '车辆账单ID',
    dataIndex: 'billNo',
  },
  {
    title: '订单号',
    dataIndex: 'orderNo',
  },
  {
    title: '车架号',
    dataIndex: 'subjectMatterNo',
  },
  {
    title: '客户名称',
    dataIndex: 'accountName',
  },

  {
    title: '未还总额(元)',
    dataIndex: 'totalAmountUnpaid',
  },
  {
    title: '未还本金(元)',
    dataIndex: 'totalPrincipalUnpaid',
  },
  {
    title: '未还利息(元)',
    dataIndex: 'totalInterestUnpaid',
  },
  {
    title: '未还罚息(元)',
    dataIndex: 'totalOverduePenaltyUnpaid',
  },
  {
    title: '未还违约金(元)',
    dataIndex: 'totalBreach',
  },
  {
    title: '未还滞纳金(元)',
    dataIndex: 'totalLate',
  },

  {
    title: '应还款总额(元)',
    dataIndex: 'totalAmountDue',
  },
  {
    title: '应还本金(元)',
    dataIndex: 'totalPrincipalDue',
  },
  {
    title: '应还利息(元)',
    dataIndex: 'totalInterestDue',
  },

  {
    title: '应还罚息(元)',
    dataIndex: 'totalOverduePenaltyDue',
  },

  {
    title: '应还违约金(元)',
    dataIndex: 'totalBreachDue',
  },

  {
    title: '应还滞纳金(元)',
    dataIndex: 'totalLateDue',
  },
  {
    title: '更新日期',
    dataIndex: 'updateAt',
  },
  {
    title: '放款日期',
    dataIndex: 'lendingTime',
  },
  {
    title: '应还日期',
    dataIndex: 'dueDate',
  },

  {
    title: '账单状态',
    dataIndex: 'statusName',
  },
  {
    title: '逾期天数',
    dataIndex: 'overdueDateNumber',
  },
];
