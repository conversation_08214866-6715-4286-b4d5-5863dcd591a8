import { carStatusMap } from '@/utils/bankend/enum';
import { ModalForm } from '@ant-design/pro-form';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Descriptions, Tooltip } from 'antd';
import React, { memo } from 'react';
import type { IrepaymentInfoItem } from '../types';

type Props = {
  allCarRepayInfo: any;
  specialCarRepayInfo: any;
};
const RepayAmountDetail: React.FC<Props> = (props) => {
  const { allCarRepayInfo = [], specialCarRepayInfo = [] } = props;
  // 所有车辆的明细
  const allCarInfoColumns: ProColumns<IrepaymentInfoItem>[] = [
    { dataIndex: 'term', title: '当前期数' },
    { dataIndex: 'status', title: '状态', valueEnum: carStatusMap, valueType: 'select' },
    { dataIndex: 'repayTime', title: '应还日期' },
    { dataIndex: 'shouldAmountDue', title: '应还总额' },
    { dataIndex: 'shouldPrincipal', title: '应还本金' },
    { dataIndex: 'shouldInterest', title: '应还利息' },
    { dataIndex: 'shouldCost', title: '费用' },
    { dataIndex: 'returnedAmountDue', title: '已还总额' },
    { dataIndex: 'returnedPrincipal', title: '已还本金' },
    { dataIndex: 'returnedInterest', title: '已还利息' },
    { dataIndex: 'returnedCost', title: '已还费用' },
    { dataIndex: 'remainingAmountDue', title: '剩余应还总额' },
    { dataIndex: 'remainingPrincipal', title: '剩余应还本金' },
    { dataIndex: 'remainingInterest', title: '剩余应还利息' },
    { dataIndex: 'remainingCost', title: '剩余应还费用' },

    { dataIndex: 'overdueInterest', title: '逾期罚息' },
    { dataIndex: 'overdueLatePaymentFee', title: '逾期滞纳金' },
    { dataIndex: 'advanceLiquidatedDamages', title: '提前结清违约金' },

    { dataIndex: 'actualRepayTime', title: '实际还款时间' },
    { dataIndex: 'actualSettleTime', title: '实际结清时间' },
    { dataIndex: 'preSettleCost', title: '提前结清手续费' },

    { dataIndex: 'returnedOverdueInterest', title: '已还逾期罚息' },
    { dataIndex: 'remainingOverdueInterest', title: '剩余应还逾期罚息' },
    { dataIndex: 'returnedOverdueLatePaymentFee', title: '已还逾期滞纳金' },
    { dataIndex: 'remainingOverdueLatePaymentFee', title: '剩余应还逾期滞纳金' },
    { dataIndex: 'otherCost', title: '其它费用' },
    { dataIndex: 'policyCancelFee', title: '已还退保盈余' },
  ];

  // 特殊处理的明细
  const specialCarInfoColumns = [
    { dataIndex: 'licensePlateNo', title: '车牌号' },
    { dataIndex: 'repaySchemaItemNo', title: '还款项编号' },
    { dataIndex: 'repayAmount', title: '还款金额' },
  ];
  // const specialCarRepayInfoNo = specialCarRepayInfo.map((item: any) => item.repaySchemaItemNo);
  // function getAmount() {
  //   const otherCars = allCarRepayInfo.filter(
  //     (item: any) => !specialCarRepayInfoNo?.includes(item.schemaItemNo),
  //   );
  //   const otherCarsAmount = otherCars.reduce((pre: any, cur: any) => {
  //     return pre + cur.repayAmount;
  //   }, 0);
  //   const specialCarRepayInfoAmount = specialCarRepayInfo.reduce(
  //     (pre: any, cur: any) => pre + cur.repayAmount,
  //     0,
  //   );
  //   return otherCarsAmount + specialCarRepayInfoAmount;
  // }
  return (
    <ModalForm trigger={<a>查看金额计算明细</a>} width={1800} modalProps={{ zIndex: 1001 }}>
      {/* <h4>总还款金额 - {getAmount()}</h4> */}

      <ProTable
        bordered
        headerTitle="特殊处理的车辆"
        dataSource={specialCarRepayInfo}
        columns={specialCarInfoColumns}
        pagination={false}
        search={false}
        size="small"
        toolbar={{
          multipleLine: true,
          search: false,
          settings: [],
        }}
      />

      {allCarRepayInfo.map((item: any) => {
        const {
          carPolicyTermRspBOS,
          licensePlateNo,
          schemaItemNo,
          // repayAmount,
          // currentDate,
          preSettleAmount,
        } = item;
        const filterJsx = (
          <Descriptions column={2}>
            <Descriptions.Item label="还款编号">{schemaItemNo}</Descriptions.Item>
            {/* <Descriptions.Item
              label={
                <div>
                  当前车辆的还款金额
                  <Tooltip
                    title={
                      <div>
                        <div>1. 只需要算至距离时间,再往后延一期</div>
                        <div>2. 应还本金+应还利息+逾期罚息+逾期滞纳金</div>
                      </div>
                    }
                  >
                    ?
                  </Tooltip>
                </div>
              }
            >
              {repayAmount}
            </Descriptions.Item> */}
            <Descriptions.Item label={<Tooltip title="preSettleAmount">提前结清的金额</Tooltip>}>
              {preSettleAmount}
            </Descriptions.Item>
          </Descriptions>
        );
        return (
          <ProTable
            bordered
            headerTitle={licensePlateNo}
            dataSource={carPolicyTermRspBOS}
            columns={allCarInfoColumns}
            pagination={false}
            search={false}
            // toolBarRender={false}
            size="small"
            toolbar={{
              multipleLine: true,
              search: false,
              settings: [],
              filter: filterJsx,
            }}
          />
        );
      })}
    </ModalForm>
  );
};

export default memo(RepayAmountDetail);
