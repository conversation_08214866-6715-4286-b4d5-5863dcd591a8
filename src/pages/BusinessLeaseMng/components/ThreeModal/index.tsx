/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-unused-vars */
// 三要素进件校验弹窗
import { QrViewer } from '@/components/QrViewer';
import { pattern, rules } from '@/utils/validate';
import { CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { Button, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { OrderDetailItem } from '../../data';
import { queryOrderDetail, threeVerify } from '../../service';
import './index.less';

type ThreeModalProps = {
  orderNo?: string;
  visible: boolean;
  orderStatus?: number; //订单状态
  onClose: () => void;
  onNextStep: (userNo: string) => void;
  onReflash: () => void;
};

enum VERIFY_STATUS {
  ING_60 = 10, //认证中 没有超过60
  ING_OUT_60 = 11, // 认证中 未出结果 超过60s
  FAIL = 20, //实名认证不通过
  EXCEED_TIMES = 25, // 实名认证超过次数
  REJECT = 30, //秒拒
  PASS = 40, //实名认证通过
}

const ERROR_MSG: Record<string, string> = {
  11: '三要素验证一致',
  12: '三要素信息均不一致',
  13: '手机号姓名验证不一致',
  14: '手机号身份证号验证不一致',
  15: '手机号证件类型不匹配',
  16: '手机号与其他要素不一致',
  '-3': '接口异常，请联系销售专员',
  '-4': '网络异常，请稍后重试',
};

enum ORDER_STATUS {
  WAIT_SIGN = 1, // 认证成功，等待签约
  SIGN_DONE = 3, // 认证成功，也完成了签约
}

enum VERIFY_RESULT {
  SUCCESS,
  FAIL,
}

const TitleRender = (props: {
  status: number;
  verifyStatus: number;
  handleQRVisible: (status: boolean) => void;
}) => {
  const { status, verifyStatus, handleQRVisible } = props;
  const statusText = {
    [ORDER_STATUS.WAIT_SIGN]: '(校验状态：要素认证成功!待签约)',
    [ORDER_STATUS.SIGN_DONE]: '(校验状态：要素认证成功!合同签约成功)',
  };
  const verifyStatusText = {
    [VERIFY_RESULT.FAIL]: '(校验状态：认证失败!三要素认证失败)',
  };
  return (
    <>
      <div className="three-modal-title">
        进件准入校验
        <span className="three-modal-status-text">
          {statusText[status] || verifyStatusText[verifyStatus]}
        </span>
      </div>
      {status === ORDER_STATUS.WAIT_SIGN && (
        <Button
          type="link"
          className="three-modal-btn"
          onClick={() => {
            handleQRVisible(true);
          }}
        >
          查看合同签约码
        </Button>
      )}
    </>
  );
};

const ThreeModal = (props: ThreeModalProps) => {
  const { onReflash, onNextStep } = props;

  const preInfo = useRef({
    idNo: '',
    name: '',
    phone: '',
  });

  const [curMessage, setCurMessage] = useState('');
  const formRef = useRef<any>();
  const [loading, setLoading] = useState(false);
  const [orderStatus, setOrderStatus] = useState();
  const [detailData, setDetailData] = useState<OrderDetailItem>({});
  const [qrVisible, setQrVisible] = useState(false);
  const [verifyStatus, setVerifyStatus] = useState();

  const initUserInfo = async (orderNo) => {
    if (!orderNo) return;
    const res = await queryOrderDetail(orderNo);
    const { data }: { data: OrderDetailItem } = res;
    const { orderStatus } = data;
    setOrderStatus(orderStatus);
    // 已经完成认证，需要查询订单详情接口获取三要素信息显示出来
    if (data && [ORDER_STATUS.SIGN_DONE, ORDER_STATUS.WAIT_SIGN].includes(orderStatus)) {
      const { idNo, userName, phone } = data;
      formRef?.current?.setFieldsValue({
        idNo,
        phone,
        name: userName,
      });
      setDetailData(data);
    }
  };

  useEffect(() => {
    if (props.orderNo) {
      initUserInfo(props.orderNo);
    }
  }, [props.orderNo]);

  const onBack = () => {
    props.onClose();
    formRef?.current?.resetFields();
    setOrderStatus(0);
    setVerifyStatus(-1);
    setCurMessage('');
    preInfo.current = {
      idNo: '',
      name: '',
      phone: '',
    };
  };

  const onSubmit: any = (values: any) => {
    const { idNo, name, phone } = values;
    if (
      idNo === preInfo.current.idNo &&
      name === preInfo.current.name &&
      phone === preInfo.current.phone
    ) {
      message.warning('请修改后再提交');
      return;
    }
    const params = {
      idNo,
      name,
      phone,
    };
    preInfo.current = params;
    setLoading(true);
    threeVerify(params)
      .then((res) => {
        const { status, realNameFirmFailStatus, orderNo } = res?.data;
        const errorMsg = ERROR_MSG[realNameFirmFailStatus] || '';
        let resMessage = '认证成功！';
        let verifyStatus = VERIFY_RESULT.FAIL;
        switch (status) {
          case VERIFY_STATUS.FAIL: // 实名认证不通过可以重试
            resMessage = errorMsg || '信息不匹配';
            break;
          case VERIFY_STATUS.EXCEED_TIMES: // 实名认证超过次数
            resMessage = '信息不匹配';
            break;
          case VERIFY_STATUS.REJECT: // 秒拒
            resMessage = '认证失败！';
            break;
          case VERIFY_STATUS.PASS:
            verifyStatus = VERIFY_RESULT.SUCCESS;
            break;
        }
        setCurMessage(resMessage);
        setVerifyStatus(verifyStatus);
        setLoading(false);
        // 更新数据
        if (verifyStatus === VERIFY_RESULT.SUCCESS && orderNo) {
          initUserInfo(orderNo);
        }

        onReflash();
      })
      .catch((res) => {
        if (res?.msg) {
          setCurMessage(res.msg);
        }
        setVerifyStatus(VERIFY_RESULT.FAIL);
        setLoading(false);
      });
  };

  return (
    <>
      <ModalForm
        title={
          <TitleRender
            status={orderStatus}
            verifyStatus={verifyStatus}
            handleQRVisible={setQrVisible}
          />
        }
        layout="horizontal"
        initialValues={{}}
        visible={props.visible}
        formRef={formRef}
        modalProps={{
          centered: true,
          onCancel: onBack,
          maskClosable: false,
        }}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 8 }}
        submitter={{
          render: (formProps) => {
            return [
              // 没有完成签约
              orderStatus !== ORDER_STATUS.SIGN_DONE && (
                <Button key="back" onClick={onBack}>
                  返回
                </Button>
              ),
              // 没有提交三要素
              orderStatus !== ORDER_STATUS.SIGN_DONE && orderStatus !== ORDER_STATUS.WAIT_SIGN && (
                <Button
                  type="primary"
                  key="submit"
                  loading={loading}
                  onClick={() => formProps.form?.submit?.()}
                >
                  提交
                </Button>
              ),

              // 已经完成签约
              orderStatus === ORDER_STATUS.SIGN_DONE && (
                <Button
                  type="primary"
                  htmlType="submit"
                  key="submit"
                  onClick={() => onNextStep(detailData?.userNo || '')}
                >
                  下一步
                </Button>
              ),
            ];
          },
        }}
        onFinish={onSubmit}
      >
        {curMessage && (
          <div className="three-modal-error">
            {verifyStatus === VERIFY_RESULT.SUCCESS && (
              <CheckCircleOutlined style={{ color: 'rgb(50, 203, 50)' }} />
            )}
            {verifyStatus === VERIFY_RESULT.FAIL && (
              <ExclamationCircleOutlined style={{ color: '#f23041' }} />
            )}
            <span className="three-modal-error-msg">{curMessage}</span>
          </div>
        )}
        <ProFormText
          label="真实姓名"
          name="name"
          rules={[
            {
              required: true,
            },
            { pattern: pattern.ch, message: '不能录入非汉字且不少于2个汉字' },
          ]}
          placeholder="请输入姓名"
          allowClear
          fieldProps={{
            maxLength: 15,
          }}
          validateTrigger="onBlur"
          disabled={orderStatus === ORDER_STATUS.WAIT_SIGN}
        />
        <ProFormText
          label="身份证号"
          name="idNo"
          placeholder="请输入身份证号"
          rules={[{ required: true }, { pattern: pattern.idCard, message: '格式错误' }]}
          fieldProps={{
            maxLength: 18,
          }}
          allowClear
          validateTrigger="onBlur"
          disabled={orderStatus === ORDER_STATUS.WAIT_SIGN}
        />
        <ProFormText
          label="手机号"
          name="phone"
          placeholder="请输入手机号"
          rules={[{ required: true }, { validator: rules.phone }]}
          fieldProps={{ maxLength: 11 }}
          allowClear
          validateTrigger="onBlur"
          disabled={orderStatus === ORDER_STATUS.WAIT_SIGN}
        />
      </ModalForm>
      <QrViewer
        data={detailData}
        qrVisible={qrVisible}
        handleQRVisible={setQrVisible}
        title="签约二维码"
      />
    </>
  );
};

export default ThreeModal;
