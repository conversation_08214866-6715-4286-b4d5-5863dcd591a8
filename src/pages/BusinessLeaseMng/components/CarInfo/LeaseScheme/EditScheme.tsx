/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ProCard, ProFormDigit, ProFormMoney } from '@ant-design/pro-components';
import { ProFormSelect } from '@ant-design/pro-form';
import { Form, message, Spin } from 'antd';
import BigNumber from 'bignumber.js';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import type { ReCalcParams } from '../../../data';
import {
  getProductList,
  getUserScheme,
  queryOrderFinancePlan,
  refreshCalculate,
} from '../../../service';
import './index.less';
import { formatPickerData, isInRange, roundToTenThousand } from './utils';

import { INCOME_STATUS, ORDER_STATUS } from '@/pages/BusinessLeaseMng/consts';
import type { LatchPlanProps, PickerColumnItem } from './types';

function formatRatio(ratio: number) {
  return Number((ratio * 100).toFixed(0));
}

// 边界符号
const SYMBOL: Record<string, string> = {
  OPEN_RANGE: '<',
  CLOSE_RANGE: '≤',
};

type LeaseSchemeProps = {
  orderNo: string;
  userNo: string;
  orderStatus: number;
  latchPlanParams: LatchPlanProps;
  readOnly: boolean;
  incomeStatus: number;
  preCreditRefresh: boolean;
};

const LeaseScheme = forwardRef((props: LeaseSchemeProps, ref) => {
  const {
    orderNo,
    userNo,
    readOnly,
    orderStatus,
    incomeStatus,
    latchPlanParams,
    preCreditRefresh,
  } = props;

  const [form] = Form.useForm();

  const inited = useRef<boolean>();
  const [loading, setLoading] = useState(false);
  const [dealPriceConfig, setDealPriceConfig] = useState<any>({}); // 成交价校验配置
  const [productList, setProductList] = useState<PickerColumnItem[]>([]);
  const [dataInfo, setDataInfo] = useState<any>();

  const [productDisabled, setProductDisabled] = useState(false); // 产品方案是否可编辑
  const [showDownPayment, setShowDownPayment] = useState(false); // 是否展示首付
  const [downPaymentRangeText, setDownPaymentRangeText] = useState(''); // 首付范围文字提示
  const [showExtraTips, setShowExtraTips] = useState(true); // 是否展示首付提示文案，默认展示
  const [isPaymentEdit, setIsPaymentEdit] = useState(true); // 首付金额默认可以编辑
  const [showDownPaymentRatio, setShowDownPaymentRatio] = useState(false); // 是否展示首付比例
  const [isRatioEdit, setIsRatioEdit] = useState(true); // 默认首付比例可以编辑
  const [downPaymentRatioRangeText, setDownPaymentRatioRangeText] = useState('');

  const [periodsList, setPeriodsList] = useState<PickerColumnItem[]>([]); // 期数

  useEffect(() => {
    if (preCreditRefresh) inited.current = false;
  }, [preCreditRefresh]);
  // 重新选车，租赁方案输入出首付和成交价之外禁用
  const [isDisabled, setIsDisabled] = useState(false);

  const initStatus = (data: any) => {
    const {
      dealPrice,
      numberOfLeasePeriodList,
      downPaymentConfig,
      numberOfLeasePeriods,
      downPaymentProportion, //首付比例
      downPaymentPrice,
    } = data;
    // 处理一下期数
    const pickerData = numberOfLeasePeriodList?.map((val: number) => {
      return {
        label: `${val}期`,
        value: val,
      };
    });
    // 先还原所有状态
    setPeriodsList(pickerData || []);
    setIsPaymentEdit(false);
    // 还原首付比例
    setIsRatioEdit(false);

    let showDownPayment = false;
    let showDownPaymentRatio = false;
    let downPaymentRatio = 0;
    // 处理一下首付配置
    if (downPaymentConfig) {
      const {
        amountMin,
        maxProportionNumberValue,
        minProportionNumberValue,
        minOpenOrCloseRange,
        maxOpenOrCloseRange,
        amountMax,
        calculationEnum, //计算方式
        amountTypeEnum, //金额类型
      } = downPaymentConfig;

      if (calculationEnum === 'PROPORTIONALLY') {
        // 按比例
        const downPaymentRatioRangeText = `${formatRatio(minProportionNumberValue)}%
        ${SYMBOL[minOpenOrCloseRange]}首付比例
        ${SYMBOL[maxOpenOrCloseRange]}
        ${formatRatio(maxProportionNumberValue)}%`;
        // 设置范围提示语
        setDownPaymentRatioRangeText(downPaymentRatioRangeText);

        downPaymentRatio = formatRatio(downPaymentProportion);
        showDownPaymentRatio = true;

        // 最大比例和最小比例相同时，固定比例值
        if (maxProportionNumberValue === minProportionNumberValue) {
          setIsRatioEdit(false);
          downPaymentRatio = formatRatio(maxProportionNumberValue);
        } else {
          setIsRatioEdit(true);
        }
      } else if (calculationEnum === 'AMOUNT_PROPORTION') {
        // 按金额
        let downPaymentRangeText = `${amountMin / 100}
        ${SYMBOL[minOpenOrCloseRange]}首付款
        ${SYMBOL[maxOpenOrCloseRange]}
        ${amountMax / 100}`;

        // 固定金额时不可以编辑首付
        if (amountTypeEnum === 'FIXED_AMOUNT') {
          setIsPaymentEdit(false);
        } else if (amountTypeEnum === 'RANGE_AMOUNT') {
          // 区间范围
          setIsPaymentEdit(true);
        } else if (amountTypeEnum === 'PROPORTION_RANGE_AMOUNT') {
          // 根据成交价和比例区间，计算首付款范围
          if (dealPrice) {
            const leftAmount = new BigNumber(dealPrice).times(minProportionNumberValue).toFixed(0);
            const rightAmount = new BigNumber(dealPrice).times(maxProportionNumberValue).toFixed(0);
            downPaymentRangeText = `${leftAmount}${SYMBOL[minOpenOrCloseRange]}首付款${SYMBOL[maxOpenOrCloseRange]}${rightAmount}`;
          }
          // 比例区间金额范围
          setIsPaymentEdit(true);
        }

        showDownPayment = true;
        setDownPaymentRangeText(downPaymentRangeText);
      }
    }

    setShowDownPayment(showDownPayment);
    setShowDownPaymentRatio(showDownPaymentRatio);

    // 设置表单
    form.setFieldsValue({
      ...data,
      downPaymentRatio,
    });
    // 获取费用小计
    reCaclRentMoney(showDownPayment, showDownPaymentRatio);
  };

  // 获取产品配置方案
  const initProductPlan = (obj: { productCode: string; dealPrice: string }) => {
    // 读取金融方案
    const { productCode } = obj;
    const { transferPrice, carType } = latchPlanParams;
    const dealPrice = obj.dealPrice || form.getFieldValue('dealPrice');

    if (productCode && dealPrice && transferPrice && carType) {
      const params = {
        orderNo,
        carType,
        transferPrice,
        productCode,
        dealPrice,
      };
      getUserScheme(params).then((res: any) => {
        const { data } = res;
        setDataInfo({ ...data });
        initStatus(data);
      });
    }
  };

  const onCarInfoChange = async () => {
    const { orderNo, carNo, channelId, cityName } = latchPlanParams;
    if (orderNo && carNo && channelId && cityName) {
      const params = {
        orderNo,
        carNo,
        channelId,
        cityName,
      };
      setLoading(true);
      const productRes = await getProductList(params);
      const productData = productRes.data;
      const pickerData = formatPickerData(productData || []);
      setProductList(pickerData);
      // 车辆变化时，把成交价和产品code清除，重新选
      form.setFieldsValue({
        dealPrice: undefined,
        productCode: undefined,
      });

      // 读取用户保存的草稿，如果有数据需要反显
      // 反显用户选过的金融方案
      let planRes: any = {};
      let planData: any = {};
      try {
        planRes = await queryOrderFinancePlan(orderNo);
        planData = planRes.data || {};
      } catch {}

      const {
        carPriceProportion,
        carPriceSwitch,
        productCode,
        dealPrice,
        financePlanFlowCode,
      } = planData;
      //
      let isMatchCurrentProductCode: boolean = false;
      // 特殊情况：重新选车后，要重新确认金融方案，部分字段不可编辑
      if (financePlanFlowCode && financePlanFlowCode === 13) {
        setIsDisabled(true);
        // 特殊情况：可选产品方案和已选产品方案不匹配
        try {
          console.log('pickerData', pickerData);
          isMatchCurrentProductCode = pickerData?.some(
            (item: PickerColumnItem) => item?.value === productCode,
          );
          if (productCode && !isMatchCurrentProductCode) {
            message.warning('产品方案下无该车型，请重新选择当前产品方案下车型信息');
          }
        } catch (e) {
          console.log(e);
        }
      }
      // debugger;
      // console.log(preCreditRefreshT, '--preCreditRefreshT');
      // 保存车辆成交价校验配置
      setDealPriceConfig({ carPriceProportion, carPriceSwitch });
      if (productCode && dealPrice && (!inited.current || isMatchCurrentProductCode)) {
        inited.current = true;
        // setPreCreditRefreshT(false);
        setDataInfo({ ...planData });
        initStatus(planData);
      }
    } else {
      // 没有确定好汽车时，要清空租赁计划数据
      form.resetFields();
      setProductList([]);
    }
    setLoading(false);
  };

  const getEditValidStatus = async (orderNo) => {
    const res: any = (await queryOrderFinancePlan(orderNo).catch(() => {})) || {};
    if (res?.data && res?.data?.financePlanFlowCode === 13) {
      setIsDisabled(true);
    }
  };
  useEffect(() => {
    // 订单号变化时，初始化标记还原
    inited.current = false;

    // 重新确认金融方案时，产品code不可以修改
    setProductDisabled(incomeStatus === INCOME_STATUS.RECONFIRM_LEASE_PLAN);
  }, [orderNo]);

  useEffect(() => {
    onCarInfoChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [latchPlanParams]);
  useEffect(() => {
    if (latchPlanParams?.orderNo) {
      getEditValidStatus(latchPlanParams?.orderNo).catch(() => {});
    }
  }, [latchPlanParams, latchPlanParams?.orderNo]);

  // 产品变更时
  const handleProductChange = (value: string) => {
    const { dealPrice } = form.getFieldsValue();
    if (value) {
      if (dealPrice) {
        // 重新获取产品配置
        initProductPlan({ productCode: value, dealPrice });
      } else {
        setDataInfo(undefined);
      }
    }
  };

  // 车辆成交价变更时要重新计算首付款范围
  const onDealPriceChange = () => {
    const { dealPrice, productCode } = form.getFieldsValue();

    if (dealPrice) {
      //如果没有产品配置，需要重新获取
      if (!dataInfo) {
        initProductPlan({ productCode, dealPrice });
        return;
      }
      const { downPaymentConfig } = dataInfo;
      // 重新确认首付款范围
      if (downPaymentConfig) {
        const {
          amountTypeEnum,
          minOpenOrCloseRange,
          maxOpenOrCloseRange,
          minProportionNumberValue,
          maxProportionNumberValue,
        } = downPaymentConfig;
        // 比例区间金额范围，根据输入的车辆成交价进行计算
        if (amountTypeEnum === 'PROPORTION_RANGE_AMOUNT') {
          const leftNum = new BigNumber(dealPrice).times(minProportionNumberValue).toFixed(0);
          const rightNum = new BigNumber(dealPrice).times(maxProportionNumberValue).toFixed(0);
          const text = `${leftNum}${SYMBOL[minOpenOrCloseRange]}首付款${SYMBOL[maxOpenOrCloseRange]}${rightNum}`;
          setDownPaymentRangeText(text);
          // 检查当前首付是否在范围内
          form.validateFields(['downPaymentPrice']);
          // 检查融资额是否正确
          checkFinanceAmount();
        }
      }
      // 重新计算
      reCaclRentMoney(showDownPayment, showDownPaymentRatio);
    }
  };

  const checkCarDealPrice = () => {
    const carDealPrice = form.getFieldValue('dealPrice');
    if (carDealPrice === 0) {
      const msg = `车辆成交价金额不能为0,请重新输入`;
      return Promise.reject(new Error(msg));
    }
    // 读取校验配置
    const { carPriceProportion, carPriceSwitch } = dealPriceConfig;
    if (!carPriceSwitch || !carPriceProportion) {
      return Promise.resolve();
    }

    // 成交价不大于转让价/配置系数，万位向上取整
    const { transferPrice } = latchPlanParams;
    if (!transferPrice) {
      return Promise.reject();
    }
    const limitPrice = new BigNumber(transferPrice || '').div(carPriceProportion);
    const roundLimitPrice = roundToTenThousand(limitPrice.toNumber());
    const price = new BigNumber(carDealPrice);
    if (price.toNumber() > roundLimitPrice) {
      const msg = `车辆成交价金额不可大于${roundLimitPrice},请重新输入`;
      return Promise.reject(new Error(msg));
    }
    return Promise.resolve();
  };

  const isRatioInRange = () => {
    setShowExtraTips(true);
    const ratio = form.getFieldValue('downPaymentRatio');
    const { downPaymentConfig } = dataInfo || {};
    if (!downPaymentConfig) {
      return Promise.resolve();
    }
    const {
      calculationEnum,
      maxProportionNumberValue,
      minProportionNumberValue,
      minOpenOrCloseRange,
      maxOpenOrCloseRange,
    } = downPaymentConfig;
    //按照金额计算时直接返回true
    if (calculationEnum === 'AMOUNT_PROPORTION') {
      return Promise.resolve();
    }
    // 最大和最小一样时，是固定比例，返回ture
    if (maxProportionNumberValue === minProportionNumberValue) {
      return Promise.resolve();
    }
    const ret = isInRange(
      ratio,
      formatRatio(minProportionNumberValue),
      SYMBOL[minOpenOrCloseRange],
      formatRatio(maxProportionNumberValue),
      SYMBOL[maxOpenOrCloseRange],
    );
    if (ret) {
      return Promise.resolve();
    }
    setShowExtraTips(false);
    return Promise.reject(new Error(`您已超过范围区间，可选范围${downPaymentRatioRangeText}`));
  };

  //检查融资额是否正确：融资额不可超过车辆转让价
  const checkFinanceAmount = () => {
    const { downPaymentPrice, dealPrice } = form.getFieldsValue();
    const { transferPrice } = latchPlanParams;
    if (!transferPrice || !dealPrice) {
      return true;
    }
    // 融资额=车辆成交价-首付款
    const transNumber = new BigNumber(transferPrice);
    const financeAmout = new BigNumber(dealPrice).minus(new BigNumber(downPaymentPrice));
    const minDownpayment = new BigNumber(dealPrice).minus(transNumber);
    // 如果融资额大于车辆转让价
    if (financeAmout.isGreaterThan(transNumber)) {
      const msg = `融资额不可超过${transferPrice}, 最低首付款≥${minDownpayment
        .toNumber()
        .toFixed(2)}元`;
      message.warning(msg);
      return false;
    }
    return true;
  };

  const isPaymentInRange = () => {
    setShowExtraTips(true);
    let errorMsg = '';
    const downPaymentPrice = form.getFieldValue('downPaymentPrice');
    const carDealPrice = form.getFieldValue('dealPrice');
    const { downPaymentConfig } = dataInfo || {};
    if (!downPaymentConfig) {
      return Promise.resolve();
    }
    const {
      calculationEnum,
      amountTypeEnum,
      amountMax,
      amountMin,
      minOpenOrCloseRange,
      maxOpenOrCloseRange,
      minProportionNumberValue,
      maxProportionNumberValue,
    } = downPaymentConfig;
    // 按照比例时直接返回true
    if (calculationEnum === 'PROPORTIONALLY') {
      return Promise.resolve();
    }
    // 固定金额时直接返回true
    if (amountTypeEnum === 'FIXED_AMOUNT') {
      return Promise.resolve();
    }
    // 比例区间金额范围，根据输入的车辆成交价进行计算
    if (amountTypeEnum === 'PROPORTION_RANGE_AMOUNT') {
      const leftNum = new BigNumber(carDealPrice).times(minProportionNumberValue).toFixed(0);
      const rightNum = new BigNumber(carDealPrice).times(maxProportionNumberValue).toFixed(0);
      const ret = isInRange(
        downPaymentPrice,
        Number(leftNum),
        SYMBOL[minOpenOrCloseRange],
        Number(rightNum),
        SYMBOL[maxOpenOrCloseRange],
      );
      if (ret) {
        return Promise.resolve();
      } else {
        errorMsg = `首付款范围超限,可选首付款范围${downPaymentRangeText},请重新输入`;
        setShowExtraTips(false);
        return Promise.reject(new Error(errorMsg));
      }
    }
    // 剩下的是按金额,区间比例
    const ret = isInRange(
      downPaymentPrice,
      amountMin / 100,
      SYMBOL[minOpenOrCloseRange],
      amountMax / 100,
      SYMBOL[maxOpenOrCloseRange],
    );
    if (ret) {
      return Promise.resolve();
    }
    errorMsg = `首付款范围超限,可选首付款范围${downPaymentRangeText},请重新输入`;
    setShowExtraTips(false);
    return Promise.reject(new Error(errorMsg));
  };

  // 重新计算每月租金
  const reCaclRentMoney = (showDownPayment: boolean, showDownPaymentRatio: boolean) => {
    const formValues = form.getFieldsValue();
    const {
      numberOfLeasePeriods,
      productCode,
      dealPrice,
      downPaymentRatio,
      downPaymentPrice,
    } = formValues;
    const { transferPrice, carType } = latchPlanParams;
    // 没有选择期数
    if (!numberOfLeasePeriods || !transferPrice) {
      return;
    }
    const params: ReCalcParams = {
      userNo,
      numberOfLeasePeriods,
      productCode,
      dealPrice,
      transferPrice,
      carTypeCode: carType,
    };
    if (showDownPayment) {
      if (downPaymentPrice === undefined) {
        return;
      }
      params.downPaymentPrice = Number(downPaymentPrice);
    } else if (showDownPaymentRatio) {
      if (downPaymentRatio === undefined) {
        return;
      }
      params.downPaymentProportion = new BigNumber(downPaymentRatio).div(100).toNumber();
    }

    refreshCalculate(params).then((res: any) => {
      const { data } = res;
      if (data) {
        // 更新数据
        setDataInfo({ ...data });
        const {
          downPaymentPrice,
          rentPrice,
          handlingFee, // 手续费
          guaranteePrice,
          totalCost,
          transferPrice,
          financeAmount, // 融资额
        } = data;
        form.setFieldsValue({
          downPaymentPrice,
          rentPrice,
          handlingFee,
          guaranteePrice,
          totalCost,
          transferPrice,
          financeAmount,
        });
        // 写完数据后再校验一下
        form.validateFields();
      }
    });
  };

  // 首付款金额输入失去焦点
  const handleNumberBlur = () => {
    // 重新计算
    reCaclRentMoney(showDownPayment, showDownPaymentRatio);
    // 检查融资额是否正确
    checkFinanceAmount();
  };

  const handleRatioChange = () => {
    reCaclRentMoney(showDownPayment, showDownPaymentRatio);
  };

  const handlePeriodsChange = () => {
    reCaclRentMoney(showDownPayment, showDownPaymentRatio);
  };

  const tempSave = () => {
    const formValues = form.getFieldsValue();
    const { downPaymentRatio } = formValues;
    const params = {
      ...formValues,
    };
    // 首付比例
    if (downPaymentRatio) {
      params.downPaymentProportion = downPaymentRatio / 100;
    }
    return params;
  };

  const handleSubmit = async () => {
    try {
      if (isDisabled) {
        const formValues = form.getFieldsValue();
        const { productCode } = formValues;
        console.log('productCode', productCode);
        console.log('productList', productList);
        if (!productCode || !productList?.some((item) => item?.value === productCode)) {
          message.warning('产品方案下无该车型，请重新选择当前产品方案下车型信息');
          return Promise.reject();
        }
      }
    } catch {}
    //
    try {
      await form.validateFields();
    } catch {
      return Promise.reject();
    }

    // 检查一下融资额是否正确
    if (!checkFinanceAmount()) {
      return Promise.reject();
    }

    const formValues = form.getFieldsValue();
    const { downPaymentRatio = 0 } = formValues;
    const params = {
      ...formValues,
      downPaymentProportion: downPaymentRatio / 100, // 首付比例
    };
    console.log('金融方案最终参数', params);
    return Promise.resolve(params);
  };

  const resetForm = () => {
    form.resetFields();
    setProductList([]);
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    // clear: () => {
    //   resetForm();
    // },
    save: () => {
      return tempSave();
    },
    submit: () => {
      return handleSubmit();
    },
  }));

  return (
    <Spin spinning={loading}>
      <ProCard title="租赁计划" className="lease-scheme" bordered headerBordered collapsible>
        <Form form={form} style={{ width: 800 }} wrapperCol={{ span: 12 }} labelCol={{ span: 3 }}>
          <ProFormDigit
            label="车辆成交价"
            name="dealPrice"
            min={0}
            readonly={readOnly}
            addonBefore="¥"
            placeholder="请输入车辆成交价"
            rules={[
              {
                required: true,
              },
              {
                validator: checkCarDealPrice,
              },
            ]}
            fieldProps={{
              precision: 2,
              onBlur: onDealPriceChange,
            }}
          />

          {
            <Form.Item shouldUpdate noStyle>
              {({ getFieldsValue }) => {
                const { dealPrice, productCode } = getFieldsValue();
                return (
                  <>
                    <ProFormSelect
                      label="产品方案"
                      name="productCode"
                      options={productList}
                      readonly={readOnly}
                      disabled={productDisabled || isDisabled}
                      rules={[{ required: true }]}
                      fieldProps={{
                        onChange: handleProductChange,
                      }}
                    />
                    {productCode && dealPrice !== undefined && (
                      <>
                        <h4>付款计划</h4>
                        {showDownPaymentRatio && (
                          <ProFormDigit
                            label="首付比例"
                            name="downPaymentRatio"
                            min={0}
                            max={100}
                            readonly={readOnly || !isRatioEdit}
                            placeholder="请输入"
                            rules={[
                              {
                                required: true,
                              },
                              {
                                validator: isRatioInRange,
                              },
                            ]}
                            fieldProps={{
                              onBlur: handleRatioChange,
                            }}
                            addonAfter="%"
                            extra={
                              isRatioEdit &&
                              showExtraTips && (
                                <span className="lease-scheme-red-text">
                                  可选首付比例的范围 {downPaymentRatioRangeText}
                                </span>
                              )
                            }
                          />
                        )}
                        <ProFormDigit
                          label="首付款(元)"
                          name="downPaymentPrice"
                          readonly={readOnly || !showDownPayment}
                          disabled={!isPaymentEdit}
                          placeholder="请输入"
                          rules={[
                            {
                              required: showDownPayment,
                            },
                            {
                              validator: isPaymentInRange,
                            },
                          ]}
                          fieldProps={{
                            onBlur: handleNumberBlur,
                            precision: 2,
                          }}
                          min={0}
                          addonBefore="¥"
                          extra={
                            isPaymentEdit &&
                            showExtraTips && (
                              <span className="lease-scheme-red-text">
                                可选首付款的范围
                                {downPaymentRangeText}
                              </span>
                            )
                          }
                        />

                        <ProFormSelect
                          label="选择期数"
                          name="numberOfLeasePeriods"
                          options={periodsList}
                          rules={[{ required: true }]}
                          readonly={readOnly}
                          disabled={isDisabled}
                          placeholder="请选择"
                          fieldProps={{
                            onChange: handlePeriodsChange,
                          }}
                          extra={
                            (orderStatus === ORDER_STATUS.PRE_AUDIT_PASS ||
                              incomeStatus === INCOME_STATUS.RECONFIRM_LEASE_PLAN) && (
                              <span className="lease-scheme-red-text">
                                可根据首付款变动，重新选择您所需的期数
                              </span>
                            )
                          }
                        />
                        <ProFormMoney
                          label="每月租金"
                          name="rentPrice"
                          readonly
                          placeholder="请输入"
                        />
                        <ProFormMoney label="融资额" name="financeAmount" readonly />
                        {dataInfo?.prePayCost && (
                          <>
                            <h4>首次费用小计</h4>
                            {dataInfo?.prePayCostItem?.indexOf('LOW_DOWN_PAYMENT') > -1 && (
                              <ProFormMoney label="首付款" name="downPaymentPrice" readonly />
                            )}
                            {dataInfo?.prePayCostItem?.indexOf('BAIL') > -1 && (
                              <ProFormMoney label="保证金" name="guaranteePrice" readonly />
                            )}
                            {dataInfo?.prePayCostItem?.indexOf('COMMISSION') > -1 && (
                              <ProFormMoney label="手续费" name="handlingFee" readonly />
                            )}
                            <ProFormMoney label="共计" name="totalCost" readonly />
                          </>
                        )}
                      </>
                    )}
                  </>
                );
              }}
            </Form.Item>
          }
        </Form>
      </ProCard>
    </Spin>
  );
});

export default LeaseScheme;
