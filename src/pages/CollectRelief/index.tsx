import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { getUserListEnum } from '@/services/enum';
import { disableFutureDate, downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link, useAccess } from '@umijs/max';
import { Button, Tabs } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import { PassModal, RefuseModal, StatusModal } from '../CallPay/components';
import type { CollectReliefItem, CollectReliefParams } from './data';
import {
  collectExport,
  getCollectReliefList,
  getOffLineCollectReliefList,
  offLineCollectExport,
} from './service';

const getExport = (
  form: CollectReliefParams,
  exportDown: (params?: CollectReliefParams | undefined) => Promise<RequestResponse<any>>,
) => {
  exportDown(form).then((res) => {
    downLoadExcel(res);
  });
};

const CollectRelief: React.FC<any> = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [passModalVisible, handlePassModalVisible] = useState<boolean>(false);
  const [refuseModalVisible, handleRefuseModalVisible] = useState<boolean>(false);
  const [statusModalVisible, handleStatusModalVisible] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState([]);
  const [currentRow, setCurrentRow] = useState<CollectReliefItem>();
  const [activeTabKey, setActiveTabKey] = useState('1');
  const access = useAccess();
  const statusMap = {
    0: '待审核',
    1: '通过',
    2: '驳回',
  };
  const statusMapOffLine = {
    1: '待审核',
    2: '通过',
    3: '驳回',
  };

  const { TabPane } = Tabs;

  const runTable = (params) => {
    // console.log(sorter);
    return activeTabKey === '1'
      ? getCollectReliefList(params).then((res) => {
          setDataSource(res.data);
          return res;
        })
      : getOffLineCollectReliefList(params).then((res) => {
          setDataSource(res.data);
          return res;
        });
  };

  const overdueCaseNoList = dataSource?.map((item) => {
    return item.overdueCaseNo;
  });

  const columns: ProColumns<CollectReliefItem>[] = [
    {
      title: '催收减免单号',
      dataIndex: 'remissionId',
    },
    {
      title: '催收案件编号',
      dataIndex: 'overdueCaseNo',
      render: (_, row, dataIndex) => {
        return row?.overdueCaseNo ? (
          <Link
            to={{
              pathname: `/businessMng/postLoanMng/collection-detail`,
              search: `?overdueCaseNo=${row?.overdueCaseNo}`,
            }}
            state={{
              curList: overdueCaseNoList || [],
              curItemIndex: dataIndex,
            }}
          >
            {row.overdueCaseNo}
          </Link>
        ) : (
          // <a
          //   onClick={() => {
          //     if (!row.productCode) {
          //       message.warning('产品Code不能为空哦！');
          //       return;
          //     }
          //     history.push(
          //       createDetailPath({ productCode: row.productCode, overdueId: row.overdueId }),
          //     );
          //   }}
          // >
          //   {row.overdueId}
          // </a>
          '-'
        );
      },
    },
    {
      title: '催收员',
      dataIndex: 'urgePerson',
      valueType: 'select',
      request: getUserListEnum,
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '关联单号',
      dataIndex: 'orderNo',
      search: false,
      render: (_, row) => {
        let dom = <></>;
        //循环额度的不跳转
        if (row?.orderNo?.includes(',')) {
          dom = <>{row?.orderNo}</>;
        } else {
          const strMap = {
            '03': 'cash-',
            '02': 'lease-',
            '01': '',
          };

          const isCarInsurance = row?.productCode?.substring(0, 4);
          const carInsuranceUrl = '/businessMng/car-insurance/detail';
          dom = (
            <>
              {row?.orderNo && row?.orderNo !== '-' ? (
                <Link
                  to={{
                    pathname: isCarInsurance
                      ? carInsuranceUrl
                      : `/businessMng/${strMap[row?.productCode?.substring(0, 2)]}detail`,
                    search: `?orderNo=${row?.orderNo}`,
                  }}
                >
                  {row.orderNo}
                </Link>
              ) : (
                '-'
              )}
            </>
          );
        }
        return dom;
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '借款人姓名',
      dataIndex: 'accountName',
      search: false,
    },
    {
      title: '订单金额（元）',
      dataIndex: 'applyAmount',
      search: false,
    },
    {
      title: '待还款总金额（元）',
      dataIndex: 'residueAmount',
      search: false,
    },
    {
      title: '逾期总金额（元）',
      dataIndex: 'overdueAmount',
      search: false,
    },

    {
      title: '申请减免金额',
      dataIndex: 'amount',
      key: 'amount',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: {
        0: { text: '待审核' },
        1: {
          text: '通过',
        },
        2: {
          text: '驳回',
        },
      },
      render: (_, row) => (
        <>
          <a
            onClick={() => {
              handleStatusModalVisible(true);
              // console.log(row)
              setCurrentRow(row);
            }}
          >
            {statusMap[row.status]}
          </a>
        </>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      // renderFormItem,
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          createdAtStart: `${value[0].split(' ')[0]} 00:00:00`,
          createdAtEnd: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
      render(dom, row) {
        return row?.createdAt || '-';
      },
    },
    {
      title: '操作',
      key: 'option',
      width: 180,
      fixed: 'right',
      valueType: 'option',
      // 权限判断展示操作列
      hideInTable: !(
        access && access.hasAccess('biz_businessMng_postLoanMng_collect_audit_button')
      ),
      render: (_, row: CollectReliefItem) => (
        <>
          {row.status === 0 ? (
            <>
              <a
                className={globalStyle.mr10}
                onClick={() => {
                  handlePassModalVisible(true);
                  setCurrentRow(row);
                }}
              >
                通过
              </a>
              <a
                type="link"
                className={globalStyle.mr10}
                onClick={() => {
                  handleRefuseModalVisible(true);
                  setCurrentRow(row);
                }}
              >
                驳回
              </a>
            </>
          ) : (
            '-'
          )}
          {/* <a>查看详情</a> */}
        </>
      ),
    },
  ];

  const columnsOffline: ProColumns<CollectReliefItem>[] = [
    {
      title: '减免单号',
      dataIndex: 'remissionNo',
    },
    {
      title: '操作人员',
      dataIndex: 'applyUserName',
      key: 'applyUserName',
      search: false,
    },
    {
      title: '关联单号',
      dataIndex: 'orderNo',
      // search: false,
      render: (_, row) => {
        let dom = <></>;
        //循环额度的不跳转
        if (row?.orderNo?.includes(',')) {
          dom = <>{row?.orderNo}</>;
        } else {
          const strMap = {
            '03': 'cash-',
            '02': 'lease-',
            '01': '',
          };
          const isCarInsurance = row?.productCode?.substring(0, 4);
          const carInsuranceUrl = '/businessMng/car-insurance/detail';
          dom = (
            <>
              {' '}
              {row?.orderNo && row?.orderNo !== '-' ? (
                <Link
                  to={{
                    pathname: isCarInsurance
                      ? carInsuranceUrl
                      : `/businessMng/${strMap[row?.productCode?.substring(0, 2)]}detail`,
                    search: `?orderNo=${row?.orderNo}`,
                  }}
                >
                  {row.orderNo}
                </Link>
              ) : (
                '-'
              )}
            </>
          );
        }
        return dom;
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '借款人姓名',
      dataIndex: 'accountName',
      search: false,
    },
    {
      title: '订单金额（元）',
      dataIndex: 'orderAmount',
      search: false,
    },
    {
      title: '待还款总金额（元）',
      dataIndex: 'repayAmount',
      search: false,
    },

    {
      title: '申请减免金额',
      dataIndex: 'remissionAmount',
      key: 'remissionAmount',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      valueEnum: {
        1: { text: '待审核' },
        2: {
          text: '通过',
        },
        3: {
          text: '驳回',
        },
      },
      render: (_, row) => (
        <>
          <a
            onClick={() => {
              handleStatusModalVisible(true);
              // console.log(row)
              setCurrentRow(row);
            }}
          >
            {statusMapOffLine[row.status]}
          </a>
        </>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.createdAt || '-';
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          createStartTime: `${value[0].split(' ')[0]} 00:00:00`,
          createEndTime: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
  ];
  function handleTabChange(key) {
    setActiveTabKey(key);
    actionRef?.current?.reload();
  }
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<CollectReliefItem>
          columns={activeTabKey === '1' ? columns : columnsOffline}
          actionRef={actionRef}
          formRef={formRef}
          scroll={{ x: 'max-content' }}
          rowKey="remissionId"
          search={{
            labelWidth: 100,
          }}
          headerTitle={
            <>
              <Tabs onChange={handleTabChange} defaultActiveKey="1" activeKey={activeTabKey}>
                <TabPane key="1" tab="减免审核" />
                <TabPane key="2" tab="线下减免审核" />
              </Tabs>
            </>
          }
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  const { createdAt, ...data } = formRef?.current?.getFieldsValue();
                  let newForm = { ...data };
                  if (createdAt?.length) {
                    const createStartTime = `${createdAt[0].format('YYYY-MM-DD')} 00:00:00`;
                    const createEndTime = `${createdAt[1].format('YYYY-MM-DD')} 23:59:59`;
                    newForm = { ...data, createStartTime, createEndTime };
                  }
                  const exportDown = activeTabKey === '1' ? collectExport : offLineCollectExport;
                  getExport(newForm, exportDown);
                }}
              >
                导出
              </Button>,
            ];
          }}
          // dataSource={dataSource}
          request={(params) => runTable(params)}
          // request={(params) => getCollectReliefList(params)}
        />

        {/* 催收通过弹窗 */}
        <PassModal
          onOk={async () => {
            handlePassModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          title="催收减免"
          onCancel={() => {
            handlePassModalVisible(false);
            setCurrentRow(undefined);
          }}
          passModalVisible={passModalVisible}
          values={currentRow?.remissionId || ''}
        />

        {/* 催收驳回 */}
        <RefuseModal
          onOk={async () => {
            handleRefuseModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          title="催收减免"
          onCancel={() => {
            handleRefuseModalVisible(false);
            setCurrentRow(undefined);
          }}
          onVisibleChange={handleRefuseModalVisible}
          refuseModalVisible={refuseModalVisible}
          values={currentRow?.remissionId || ''}
        />
        {/* 通过弹窗 */}
        <StatusModal
          onOk={async () => {
            handleStatusModalVisible(false);
            setCurrentRow(undefined);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          onCancel={() => {
            handleStatusModalVisible(false);
            setCurrentRow(undefined);
          }}
          statusModalVisible={statusModalVisible}
          values={currentRow || {}}
        />
      </PageContainer>
    </>
  );
};

export default CollectRelief;
