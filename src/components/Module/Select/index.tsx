import { Select } from 'antd';
import type { SelectProps, SelectValue } from 'antd/lib/select';
import React from 'react';
type Props = {
  // dataFormat default 默认情况下 select 多选是数组 单选是字符串或者number
  // array 为了支持 单选也是数组 [value]
  dataFormat?: 'array' | 'default'; //

  // form.item 一定会传 value 和 onchange 自定义表单控件
  value?: SelectValue;
  onChange?: (value: SelectValue | SelectValue[]) => void;
} & SelectProps<any>;
const HllSelect: React.FC<Props> = (props) => {
  const { onChange, dataFormat = 'default', value: value1, mode } = props;

  function onSelectChange(value: SelectValue) {
    if (dataFormat === 'array' && ['string', 'number'].includes(typeof value) && !mode) {
      onChange?.([value]);
    } else {
      onChange?.(value);
    }
  }

  const selectProps: SelectProps<any> = {
    ...props,
    onChange: onSelectChange,
    value: dataFormat === 'array' ? value1?.[0] : value1,
  };

  return React.createElement(Select, selectProps);
};
export default HllSelect;
