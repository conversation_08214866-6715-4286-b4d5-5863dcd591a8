/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2024-12-12 14:34:06
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/service.ts
 */
import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';
import type {
  AddApplyDataParams,
  ApplymoneyOffline,
  LeanListParams,
  LeaseLendingParams,
  LendingParams,
  UOcrFileType,
} from './data';
import type { IgetCarRepaymentRes, IocrItem } from './types';

export async function queryLoan(params?: LeanListParams) {
  return request('/bizadmin/quota/lending/lendingList4Page', {
    params,
    headers: bizadminHeader,
    ifTrimParams: true,
  });
}
export async function doLending(params?: LendingParams) {
  return request('/quota/lending/doLending', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 进件信息
export async function getLendingOrderInfo(orderNo: string) {
  return request(`/quota/lending/getLendingOrderInfo/${orderNo}`);
}

// 放款信息
export async function getLendingDataInfo(lendingNo: string) {
  return request(`/quota/lending/getLendingDataInfo/${lendingNo}`);
}

export function getLeaseLoanInfo(lendingNo: string) {
  return request(`/quota/order/getLendingInfoByLendingNo/${lendingNo}`);
}

export async function loanExport(params: LeanListParams) {
  return request('/bizadmin/quota/lending/excelAsync', {
    headers: bizadminHeader,
    params,
    ifTrimParams: true,
  });
}

export async function addApplyData(data: AddApplyDataParams) {
  return request('/bizadmin/quota/applymoney/lease/addApplyData', {
    method: 'POST',
    data,
    headers: bizadminHeader,
  });
}

// 小贷确认请款
export async function addCashApplyData(lendingNo: string) {
  return request('/quota/lending/cash/commitApplyPay', {
    method: 'PUT',
    params: { lendingNo },
  });
}

export async function leaseDetail(lendingNo: string, orderNo: string) {
  return request(`/bizadmin/quota/applymoney/lease/query/${lendingNo}/${orderNo}`, {
    headers: bizadminHeader,
  });
}

export async function leaseAudit(data: LeaseLendingParams) {
  return request('/bizadmin/quota/lending/lease/audit', {
    method: 'POST',
    data,
    headers: bizadminHeader,
  });
}

// 导出融租放款单附件
export async function exportLeaseInfo(lendingNo: string, orderNo: string) {
  return request(`/bizadmin/quota/applymoney/lease/downloadZip/${lendingNo}/${orderNo}`, {
    method: 'POST',
    responseType: 'blob',
    getResponse: true,
    headers: bizadminHeader,
  });
}

// 审批日志状态
export async function getAuditLogs(lendingNo: string) {
  return request(`/quota/lending/lease/getAuditLogs/${lendingNo}`);
}

// 线下放款提交
export async function applymoneyOfflineSubmit(data: ApplymoneyOffline, productCode: string) {
  return request(`/bizadmin/quota/applymoney/cash/offline/submit`, {
    method: 'POST',
    data,
    headers: {
      productCode,
      ...bizadminHeader,
    },
  });
}

// 线下放款提交
export async function changeCard(lendingNo: string, productCode: string) {
  return request(`/quota/lending/touch/change/card/${lendingNo}`, {
    method: 'POST',
    data: {
      lendingNo,
    },
    headers: {
      productCode,
    },
    skipErrorHandler: true,
  });
}

/**
 * 获取车险的放款的详情信息 - 车险新增接口
 */
export async function getCarLendingInfo(lendingNo: string) {
  const data = await request(`/quota/order/getLendingInfoByLendingNo/${lendingNo}`, {
    method: 'GET',
  });
  return data?.data || {};
}

/**
 * 获取车险分期的进件详情 - 车险新增接口
 */
export async function getCarIncommingInfo(orderNo: string) {
  const data = await request(`/bizadmin/cash/order/detail/${orderNo}`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data?.data || {};
}

/**
 * 还款计划 - 车险新增接口
 */
export async function getCarRepayment(orderNo: string): Promise<IgetCarRepaymentRes> {
  const data = await request(`/repayment/cms/bill/repay/plan/${orderNo}`, {
    method: 'GET',
  });
  return data?.data || {};
}

interface IgetOcrInfoParams {
  filePath: string; // 相对路径
  lendingNo: string; // 放款单号
  fileType: UOcrFileType;
}

/**
 * ocr识别
 */
export async function vehicleIdentifyOcrInfo(
  params: IgetOcrInfoParams,
): Promise<{ returnCode: number; ocrValueList: IocrItem[] }> {
  const data = await request(`/bizadmin/base/ocr/vehicleIdentifyOcrInfo`, {
    method: 'GET',
    params,
    headers: bizadminHeader,
  });
  return data?.data || {};
}

// 获取gps设备运行状态
export function getGpsDeviceStatus(vin: string) {
  return request('/bizadmin/lease/gpsOrder/queryGpsPositionsInfoByVin', {
    params: { vin },
    headers: bizadminHeader,
  });
}

/**
 * 导出车险分期放款单
 */
export async function exportCarLendingInfo(lendingNo: string, orderNo: string) {
  return request(`/bizadmin/insurance/policy/order/lending/export`, {
    method: 'GET',
    headers: bizadminHeader,
    responseType: 'blob',
    getResponse: true,
    params: {
      lendingNo,
      orderNo,
    },
  });
}

/**
 * 查询车险分期放款明细
 */
export async function getCarLendingDetail(lendingNo: string, orderNo: string) {
  return request(`/bizadmin/insurance/policy/order/lendingDetail`, {
    method: 'GET',
    headers: bizadminHeader,
    params: {
      lendingNo,
      orderNo,
    },
  });
}

/**
 * 提交车险分期放款单
 */
export async function submitCarLendingInfo(data: {
  orderNo: string; // 订单号
  lendingNo: string; // 放款编号
  tranceFlowNo: string; // 银行交易流水号
  lendingTime: string; // 实际放款时间
  extend: any[]; // 凭证
}) {
  return request(`/bizadmin/quota/lending/submitOfflineLendingInfo`, {
    method: 'POST',
    data,
    headers: bizadminHeader,
  });
}

/**
 * 线下放款驳回
 */

export async function rejectCarLendingInfo(data: {
  orderNo: string; // 订单号
  lendingNo: string; // 放款编号
}) {
  return request(`/bizadmin/quota/lending/rejectOfflineLendingInfo`, {
    method: 'POST',
    data,
    headers: bizadminHeader,
  });
}

/**
 * 线下放款通过
 */
export async function passCarLendingInfo(data: {
  orderNo: string; // 订单号
  lendingNo: string; // 放款编号
}) {
  return request(`/bizadmin/quota/lending/approveOfflineLendingInfo`, {
    method: 'POST',
    data,
    headers: bizadminHeader,
  });
}
