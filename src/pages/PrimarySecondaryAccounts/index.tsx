/*
 * @Date: 2023-08-29 17:48:48
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-11-14 14:38:22
 * @FilePath: /lala-finance-biz-web/src/pages/PrimarySecondaryAccounts/index.tsx
 * @Description:
 */
import HeaderTabs from '@/components/HeaderTab';
import { SECOND_PRODUCT_SOME } from '@/enums';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, message, Modal } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useRef } from 'react';
import AddPrimarySecondaryAccountsModal from './components/AddPrimarySecondaryAccountsModal';
import type { PrimarySecondaryAccountsItems } from './data';
import {
  freezePrimarySecondaryAccounts,
  getPrimarySecondaryAccountsList,
  unfreezePrimarySecondaryAccounts,
} from './service';

const HeavyQuotaPrimarySecondaryAccounts: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const columns: ProColumns<PrimarySecondaryAccountsItems>[] = [
    {
      title: '配置场景',
      dataIndex: 'configScene',
      key: 'configScene',
      render: () => {
        return '主次账号';
      },
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      key: 'productCode',
      valueType: 'select',
      valueEnum: SECOND_PRODUCT_SOME,
    },

    {
      title: '社会统一信用代码',
      dataIndex: 'orgCode',
      key: 'orgCode',
    },

    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateRange',
      // initialValue: [dayjs().subtract(6, 'month'), dayjs()],
      search: {
        transform: (value: any) => {
          if (typeof value[0] !== 'string') {
            return {
              startCreateTime: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
              endCreateTime: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
            };
          }
          return {
            startCreateTime: `${value[0].split(' ')[0]} 00:00:00`,
            endCreateTime: `${value[1].split(' ')[0]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record?.createdAt;
      },
    },
    {
      title: '配置状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        2: { text: '禁用', status: 'error' },
        1: { text: '开启', status: 'success' },
      },
      key: 'status',
    },
    {
      title: '创建人',
      dataIndex: 'operationName',
      key: 'operationName',
    },
    {
      title: '操作',
      key: 'option',
      width: 200,
      fixed: 'right',
      valueType: 'option',
      render: (_, row) => {
        let optText = '';
        let func: (id: string) => Promise<any>;
        if (row?.status === 1) {
          optText = '禁用';
          func = freezePrimarySecondaryAccounts;
        } else {
          optText = '开启';
          func = unfreezePrimarySecondaryAccounts;
        }
        return (
          <>
            <a
              onClick={() => {
                optimizationModalWrapper(AddPrimarySecondaryAccountsModal)({
                  currentRow: { ...row, productType: row?.productCode?.substring(0, 2) || '' },
                  showMode: true,
                });
              }}
            >
              查看
            </a>
            <a
              style={{ marginLeft: 5 }}
              onClick={async () => {
                Modal.confirm({
                  title: `你确定${optText}?`,
                  icon: <ExclamationCircleOutlined />,
                  // content: 'Some descriptions',
                  okText: '确认',
                  okType: 'danger',
                  cancelText: '取消',
                  centered: true,
                  onOk: async () => {
                    await func(row.configId).then(() => {
                      message.success(`${optText}成功`);
                      actionRef?.current?.reload();
                    });
                  },
                });
              }}
            >
              {optText}
            </a>
          </>
        );
      },
    },
  ];

  return (
    <>
      <HeaderTabs />
      <PageContainer>
        <ProTable<PrimarySecondaryAccountsItems>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="configId"
          search={{ labelWidth: 120 }}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  optimizationModalWrapper(AddPrimarySecondaryAccountsModal)({
                    onOk: () => {
                      actionRef?.current?.reload();
                    },
                  });
                }}
              >
                添加
              </Button>,
            ];
          }}
          request={(params) => getPrimarySecondaryAccountsList(params)}
        />
      </PageContainer>
    </>
  );
};

export default HeavyQuotaPrimarySecondaryAccounts;
