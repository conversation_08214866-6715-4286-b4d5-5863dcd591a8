/*
 * @Author: your name
 * @Date: 2021-04-15 18:24:14
 * @LastEditTime: 2024-10-17 17:17:03
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/Repayinfo.tsx
 */
import { Link, useRequest } from '@umijs/max';
import { Button, Col, Row } from 'antd';
import React, { useState } from 'react';
// import ModalTable from '@/components/ModalTable';
import { RepayInfoCom } from '@/components/ReleaseCom';
import globalStyle from '@/global.less';
import { exportRepayDetail, getRepayInfo, getRepayRegist } from '@/services/global';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { downLoadExcel, getUuid, isExternalNetwork } from '@/utils/utils';
import styleCash from '../index.less';

import type { ProColumns } from '@ant-design/pro-table';
import BigNumber from 'bignumber.js';
import { COST_TYPE, mapStatusZh } from '../const';
import type { RepayItem } from '../data';
import { getRecord, getRemissionItem } from '../service';
import AddCashAndLeaseOfflineRepay from './AddCashAndLeaseOfflineRepay';
// import type { FeeItem } from '../data';

interface RepayInfoProps {
  orderNo: string;
  curId?: string;
  productCode?: string;
  accountName?: string;
  accountNumber?: string;
}

const mapStatus = {
  '0': {
    actualRepayTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    returnedCost: '已还资方其他费用',
  },
  '1': {
    // 待还款
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    remainingAmountDue: '剩余应还总额',
    remainingPrincipal: '剩余应还本金',
    remainingInterest: '剩余应还利息',
    remainingCost: '剩余应还资方其他费用',
  },
  '2': {
    // 提前结清
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    advanceLiquidatedDamages: '提前结清违约金',
    returnedCost: '已还资方其他费用',
  },
  '3': {
    // 结清
    // actualRepayTime: '实际结清时间',
    // returnedAmountDue: '已还总额',
    // returnedPrincipal: '已还本金',
    // returnedInterest: '已还利息',
    // preSettleCost: '提前结清违约金',
    // returnedCost: '已还费用',
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    returnedCost: '已还资方其他费用',
  },
  '4': {
    // 逾期
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    repayOverdueInterest: '已还逾期罚息',
    // repayOverdueLatePaymentFee: '已还逾期滞纳金',
    returnedCost: '已还资方其他费用',
    remainingAmountDue: '剩余应还总额',
    remainingPrincipal: '剩余应还本金',
    remainingInterest: '剩余应还利息',
    remainingOverdueInterest: '剩余应还逾期罚息',
    // remainingOverdueLatePaymentFee: '剩余应还逾期滞纳金',
    remainingCost: '剩余应还资方其他费用',
  },
  '5': {
    // 逾期结清
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    repayOverdueInterest: '已还逾期罚息',
    // repayOverdueLatePaymentFee: '已还逾期滞纳金',
    returnedCost: '已还资方其他费用',
  },
  '6': {
    // 坏账
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    repayOverdueInterest: '已还逾期罚息',
    // repayOverdueLatePaymentFee: '已还逾期滞纳金',
    returnedCost: '已还资方其他费用',
  },
  '10': {
    //  正常还款
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    returnedCost: '已还资方其他费用',
  },
  '8': {
    //  单期代偿
    actualSettleTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedInterest: '已还利息',
    returnedCost: '已还资方其他费用',
  },
  //多期代偿
  '9': {
    actualRepayTime: '实际结清日期',
    returnedAmountDue: '已还总额',
    returnedPrincipal: '已还本金',
    returnedCost: '已还资方其他费用',
  },
};
const expandLine = (record: any) => {
  return mapStatus[record?.status] ? (
    <Row>
      {Object.keys(mapStatus[record?.status]).map((item) => {
        // console.log(item);
        return (
          <Col span={6} offset={2} key={item}>
            <div className={globalStyle.lineHeight40}>
              <span>{mapStatus[record?.status]?.[item]}:</span>
              <span className={globalStyle.ml20}>{record?.[item]}</span>
            </div>
          </Col>
        );
      })}
    </Row>
  ) : (
    '-'
  );
};

const RepayInfo: React.FC<RepayInfoProps> = ({
  productCode,
  orderNo,
  accountName,
  accountNumber,
}) => {
  const { data } = useRequest(
    () => {
      return getRepayInfo(orderNo);
    },
    {
      onSuccess: () => {},
    },
  );
  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const [dataRepayRegist, setDataRepayRegist] = useState<[]>([]);
  const [loadingExport, setExportLoading] = useState<boolean>(false);
  const [attachList, setAttachList] = useState<{ netWorkPath: string; name: string }[]>([]);
  const [remissionItem, setRemissionItem] = useState<
    { costType: string; remissionAmount: number }[]
  >([]);
  const getRepayList = (repayPlanNo: string) => {
    getRepayRegist(repayPlanNo).then((res) => {
      setDataRepayRegist(res.data);
    });
  };
  // console.log('dataRepayRegist', dataRepayRegist);
  //设置附件
  const getRecordFunc = (repayPlanNo: string) => {
    getRecord({ repayPlanNo }).then((res) => {
      setAttachList(res?.data?.map((item: Record<string, any>) => item.attach)?.flat(Infinity));
    });
  };

  //减免项
  const getRemissionFunc = (orderNoArg: string, repayPlanNo: string) => {
    getRemissionItem(orderNoArg, repayPlanNo).then((res) => {
      // 原来数组 车险之后是对象
      setRemissionItem(Array.isArray(res.data) ? res.data : [res.data]);
    });
  };

  const columns: ProColumns<RepayItem>[] = [
    {
      title: '期数',
      dataIndex: 'termDetail',
    },
    {
      title: '还款状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, row) => {
        return mapStatusZh[row.status] || '-';
      },
    },
    { title: '应还日期', dataIndex: 'repayTime', key: 'repayTime' },
    { title: '应还总额', dataIndex: 'shouldAmountDue', key: 'shouldAmountDue' },
    { title: '应还本金', dataIndex: 'shouldPrincipal', key: 'shouldPrincipal' },
    { title: '应还利息', dataIndex: 'shouldInterest', key: 'shouldInterest' },
    // {
    //   title: '提前结清违约金',
    //   dataIndex: 'advanceLiquidatedDamages',
    //   key: 'advanceLiquidatedDamages',
    // },
    // { title: '逾期天数', dataIndex: 'overdueDay', key: 'overdueDay' },
    { title: '逾期罚息', dataIndex: 'overdueInterest', key: 'overdueInterest' },
    // { title: '逾期滞纳金', dataIndex: 'overdueLatePaymentFee', key: 'overdueLatePaymentFee' },
    {
      title: '其他费用',
      dataIndex: 'otherCost',
      key: 'otherCost',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      key: 'repayPlanNo',
      render: (_: any, record: any, dataIndex: number) => (
        <>
          {(record.status === 4 || record.status === 5) && (
            // <Button
            //   type="link"
            //   onClick={() => {
            //     if (!record.overdueId) {
            //       message.warning('催收单号有误！');
            //       return;
            //     }
            //     history.push(
            //       createDetailPath({
            //         productCode: history?.location?.query?.productCode,
            //         overdueId: record.overdueId,
            //       }),
            //     );
            //   }}
            // >
            //   查看逾期详情
            // </Button>
            <Link
              to={{
                pathname: `/businessMng/postLoanMng/collection-detail`,
                search: `?overdueCaseNo=${record?.overdueCaseNo}`,
              }}
              state={{
                curList: [],
                curItemIndex: dataIndex,
              }}
              style={{ display: 'block' }}
            >
              查看逾期详情
            </Link>
          )}
          <a
            // type="link"
            onClick={() => {
              // setPlanNo(record.repayPlanNo);
              handleModalVisible(true);
              getRepayList(record?.repayPlanNo);
              getRecordFunc(record?.repayPlanNo);
              getRemissionFunc(orderNo, record?.repayPlanNo);
            }}
            style={{ display: 'block' }}
          >
            查看还款登记
          </a>
          {/* <a
            // type="link"
            onClick={() => {
              const { repayPlanNo, termDetail, remainingAmountDue } = record;
              optimizationModalWrapper(ShowRepayRegistList)({
                title: termDetail,
                repayPlanNo,
                shouldAmountDue: remainingAmountDue,
                refresh: () => refresh(),
                repayStatus: record?.status,
              });
            }}
            style={{ display: 'block' }}
          >
            线下还款
          </a> */}
        </>
      ),
    },
  ];
  // const dataRepayRegist: any[] = [];
  const columnsRepayRegist = [
    {
      title: '还款流水号',
      dataIndex: 'recordingNo',
      key: 'recordingNo',
    },
    {
      title: '实际还款总金额',
      dataIndex: 'actualRepaymentAmount',
      key: 'actualRepaymentAmount',
    },
    {
      title: '实际还款本金',
      dataIndex: 'principal',
      key: 'principal',
    },
    {
      title: '实际还款利息',
      dataIndex: 'interest',
      key: 'interest',
    },
    {
      title: '实际还款罚息',
      dataIndex: 'penaltyInterest',
      key: 'penaltyInterest',
    },
    {
      title: '实际还款费用',
      dataIndex: 'cost',
      key: 'cost',
    },
    {
      title: '三方还款流水号',
      dataIndex: 'bankSerialNo',
      key: 'bankSerialNo',
    },
    {
      title: '还款渠道',
      dataIndex: 'payChannelName',
      key: 'payChannelName',
    },
    {
      title: '还款方式',
      dataIndex: 'repayType',
      key: 'repayType',
    },
    {
      title: '还款时间',
      dataIndex: 'repayTime',
      key: 'repayTime',
    },
  ];
  const getExport = () => {
    setExportLoading(true);
    exportRepayDetail(orderNo)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  return (
    <>
      <RepayInfoCom
        carTitle="还款信息"
        expandable={expandLine}
        dataTable={data}
        rowKey={getUuid()}
        onCancel={() => {
          handleModalVisible(false);
        }}
        extra={
          isExternalNetwork() ? null : (
            <>
              {data?.length ? (
                <>
                  <Button
                    onClick={() =>
                      optimizationModalWrapper(AddCashAndLeaseOfflineRepay)({
                        productCode,
                        orderNo,
                        accountName,
                        accountNumber,
                      })
                    }
                    className={styleCash.buttonStyle}
                  >
                    线下还款
                  </Button>
                  <Button loading={loadingExport} onClick={getExport} type="primary">
                    导出
                  </Button>
                </>
              ) : (
                ''
              )}
            </>
          )
        }
        // request={() => getRepayRegist(repayPlanNo)}
        columns={columns}
        modalDataTable={dataRepayRegist}
        modalColumns={columnsRepayRegist}
        modalVisible={modalVisible}
        modalTitle="还款登记"
        remissionColumn={[
          {
            title: '减免费项',
            dataIndex: 'costType',
            render: (_: React.ReactNode, record: any) => {
              // console.log(item);
              return COST_TYPE[record?.costType] || '-';
            },
          },
          {
            title: '减免金额/元',
            dataIndex: 'remissionAmount',
            render: (_: React.ReactNode, record: any) => {
              // console.log(item);
              return new BigNumber(record?.remissionAmount).div(100).toFixed(2) || '-';
            },
          },
        ]}
        remissionDataSource={remissionItem}
        repayAttachList={attachList}
      />
    </>
  );
};

export default RepayInfo;
