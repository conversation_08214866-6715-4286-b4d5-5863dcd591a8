import React from 'react';
import type { Options, ItemProps } from './types';
import { ProFormText, ProFormSelect } from '@ant-design/pro-components';

function formatOptions(data: Options[] = []) {
  if (!data) return [];
  return data.map((item) => {
    return {
      label: item.optionValue,
      value: item.optionCode,
    };
  });
}

const DisplayItem: React.FC<ItemProps> = (props) => {
  const { label, name, renderType, rules, disabled, readonly, defaultValue, options } = props;

  let node = null;

  // 需要特殊组件渲染
  switch (renderType) {
    case 'INPUT':
      node = (
        <ProFormText
          label={label}
          name={name}
          rules={rules}
          readonly={readonly}
          disabled={disabled}
          fieldProps={{
            value: defaultValue,
          }}
          placeholder="请输入"
        />
      );
      break;
    case 'SELECT':
      node = (
        <ProFormSelect
          label={label}
          name={name}
          disabled={disabled}
          readonly={readonly}
          options={formatOptions(options || [])}
          rules={rules}
          fieldProps={{
            value: defaultValue,
          }}
          placeholder="请选择"
        />
      );
  }

  return node;
};

export default DisplayItem;
