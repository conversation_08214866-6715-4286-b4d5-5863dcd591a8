/*
 * @Author: your name
 * @Date: 2021-04-13 13:56:13
 * @LastEditTime: 2024-12-09 10:44:25
 * @LastEditors: alan771.tu
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Promotion/components/CarPromotionTable.tsx
 */
import ConfirmModal from '@/components/ConfirmModal';
import { validateCarCodeUnique } from '@/services/validate';
import { ModalForm, ProFormDigit, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Button, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import type { CarListPromotionItem } from '../data';
import { addPromotion, deletePromotion, queryPromotion } from '../service';

const CarPromotionTable: React.FC<any> = (props: { type: number }) => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const confirmModalRef = useRef<any>();
  const addFormRef = useRef<FormInstance>();
  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const [addVisible, handleAddVisible] = useState<boolean>(false);
  const [curCity, handleCurCity] = useState<object>({});

  const [curRow, setCurrentRow] = useState<CarListPromotionItem>();
  const carType = props.type;
  const { cityList } = useModel('cityList');
  const handleConfirm = () => {
    return deletePromotion(curRow?.id).then(() => {
      return true;
    });
  };
  const columns: ProColumns<CarListPromotionItem>[] = [
    {
      title: carType === 1 ? '车型码' : '车辆识别代码',
      dataIndex: 'carIdCode',
    },
    {
      title: '申请城市',
      dataIndex: 'cityName',
    },
    {
      title: '优惠金额（元）',
      dataIndex: 'discount',
      search: false,
    },

    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <>
          <a
            onClick={() => {
              handleOptVisible(true);
              // confirm()
              setCurrentRow(record);
            }}
          >
            删除
          </a>
          {/* <a style={{marginLeft:5}}>查看详情</a> */}
        </>
      ),
    },
  ];

  // 抽离出原弹窗提交时的逻辑，方便阅读，其余数据逻辑不动
  const handleAdd = async () => {
    const values = addFormRef?.current?.getFieldsValue();
    const success: boolean = await addPromotion({
      ...values,
      carType,
      ...curCity,
      discount: values.discount * 100, // 单位：分，但前端做转换不太合理，后续应调整后端换算
    });

    if (success) {
      message.success('添加成功');

      // 关闭弹窗
      handleAddVisible(false);
      // 刷新列表
      actionRef?.current?.reload();
      return true;
    }

    // 关闭弹窗
    handleAddVisible(false);
    return false;
  };

  return (
    <>
      <ProTable<CarListPromotionItem>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        request={(params) => queryPromotion({ carType, ...params })}
        search={{
          labelWidth: 120,
        }}
        columns={columns}
        toolBarRender={() => {
          return [
            <Button
              key="button"
              type="primary"
              onClick={() => {
                handleAddVisible(true);
              }}
            >
              添加
            </Button>,
          ];
        }}
      />
      <ModalForm
        title="提示"
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
        }}
        open={optVisible}
        onOpenChange={handleOptVisible}
        onFinish={async () => {
          const success: boolean = await handleConfirm();
          if (success) {
            handleOptVisible(false);
            actionRef?.current?.reload();
          }
          return true;
        }}
      >
        <div>是否确定删除该促销方案?</div>
      </ModalForm>
      <ModalForm
        formRef={addFormRef}
        title={`添加促销方案-${carType === 1 ? '新车' : '二手车'}`}
        width="500px"
        layout="horizontal"
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            handleCurCity({});
            confirmModalRef?.current?.setConfirmData({});
          },
        }}
        open={addVisible}
        onOpenChange={handleAddVisible}
        onFinish={async (values) => {
          const data = {
            车型码: values?.carIdCode,
            申请城市: curCity?.applyCityName,
            优惠金额: values?.discount?.toFixed(2),
          };

          confirmModalRef?.current?.setConfirmData(data);
          // 二次确认添加
          confirmModalRef?.current?.setOpen(true);
          return false;
        }}
      >
        <ProFormText
          label={`${carType === 1 ? '车型码' : '车辆识别代码'}`}
          labelCol={{ span: 6 }}
          rules={[
            { required: true },
            {
              validator: async (_, value) => {
                if (value) {
                  return validateCarCodeUnique(value, carType, 2);
                }
                return true;
              },
            },
          ]}
          name="carIdCode"
          fieldProps={{ maxLength: 20 }}
          width="sm"
          placeholder={`请输入${carType === 1 ? '车型码' : '车辆识别代码'}`}
        />
        <ProFormSelect
          labelCol={{ span: 6 }}
          placeholder="请选择申请城市"
          rules={[{ required: true }]}
          request={async () => cityList}
          name="city"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            labelInValue: true,
            onChange: (option: { value: string; label: any }) => {
              const curCityTemp = {
                applyCityCode: option.value,
                applyCityName: option.label,
              };
              handleCurCity(curCityTemp);
            },
          }}
          width="sm"
          label="申请城市"
        />
        <ProFormDigit
          name="discount"
          width="sm"
          labelCol={{ span: 6 }}
          label="优惠金额(元)"
          placeholder="请输入优惠金额"
          rules={[
            { required: true },
            {
              pattern: /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/,
              message: '优惠金额不能为0',
            },
          ]}
          fieldProps={{
            min: 0,
            precision: 2,
          }}
        />
      </ModalForm>
      <ConfirmModal
        ref={confirmModalRef}
        onOk={() => {
          handleAdd();
          // 关闭二次确认弹窗，无论成功与否都需要关闭弹窗
          confirmModalRef?.current?.setOpen(false);
        }}
      />
    </>
  );
};

export default CarPromotionTable;
