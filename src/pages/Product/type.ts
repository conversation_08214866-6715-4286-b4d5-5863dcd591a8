import type {
  BASE_PROPORTION_NO_BRACKETS,
  CLASSIFICATION,
  EXPENSE_ITEMS,
  LOAD_FROM_ENUM,
  SECONDARY_CLASSIFICATION,
} from '@/enums';

export type Tstatus = 'draftDetail' | 'formalDetail' | 'add';

export type Tclassification = keyof typeof CLASSIFICATION;
export type TsecondaryClassification = keyof typeof SECONDARY_CLASSIFICATION;
export interface IbaseInfo {
  classification: Tclassification; // 产品一级分类
  secondaryClassification: TsecondaryClassification; // 产品二级分类
  productName: string; // 产品名称
}
// 前置费用
export enum EexpenseItemsEnum {
  INCOMING_USER = '进件用户',
  CHANNEL_MERCHANT = '渠道商户',
  PREMIUM_COMPANY = '保费收款公司',
  GUANG_ZHOU_HUO_MAN_MAN = '广州货满满咨询有限公司',
}
export type TexpenseItems = keyof typeof EexpenseItemsEnum;

export enum EloadModeEnum {
  ON_LINE = '线上',
  OFF_LINE = '线下',
}
/**
 *   ON_LINE = '线上', OFF_LINE = '线下',
 */
export type TloadModeEnum = keyof typeof EloadModeEnum;
export type TloadFromEnum = keyof typeof LOAD_FROM_ENUM;

export enum EloadToEnum {
  INCOMING_USER = '进件用户',
  CHANNEL_MERCHANT = '渠道商户',
  PREMIUM_COMPANY = '保费收款公司',
  GUANG_ZHOU_HUO_MAN_MAN = '广州货满满咨询有限公司',
}

export type TloadToEnum = keyof typeof EloadToEnum;

export interface IrepaymentDTO {
  // 还款
  repaymentModeEnum: 'ALL_AT_ONCE' | 'PRINCIPAL_EQUALS_INTEREST'; // 还款方式
  repaymentTerms: string[]; // 还款期限
  defaultRepaymentDate: string; // 默认还款日
  repaymentCycleStartTimeEnum: string; // 还款周期起始时间
  partialRepayment: boolean; // 是否允许部分还款
  withholdingTime: string[];
}
export interface IdefaultConfig {
  id: number; // 产品id，更新时需要携带
  productCode: string; // 产品code，更新时需要携带
  classification: Tclassification; // 产品一级分类
  secondaryClassification: TsecondaryClassification; // 产品二级分类
  productName: string; // 产品名称
  productDesc: string; // 产品介绍
  status: number; // 产品状态，-1:草稿 0:禁用 1：启用
  activateDTO: {
    // 进件
    activateFormula: string; // 进件金额公式
    expenseItems: TexpenseItems[]; // 前置费用
  };
  loanDTO: {
    // 放款
    loadFromEnum: TloadFromEnum; // 放款主体
    loadToEnum: TloadToEnum; // 收款主体
    loadPeriod: string; // 放款周期
    loadModeEnum: TloadModeEnum; // 放款方式

    insuranceLoadFromEnum: TloadFromEnum; // 放款主体
    insuranceLoadToEnum: TloadToEnum; // 收款主体
    insuranceLoadPeriod: string; // 放款周期
    insuranceLoadModeEnum: TloadModeEnum; // 放款方式
  };
  repaymentDTO: IrepaymentDTO;
  costConfigDTO: IfeeConfigItem[];
}

export type IdetailData = IdefaultConfig;

export type TexpenseItem = keyof typeof EXPENSE_ITEMS;
export enum EreceivingNode {
  AUDIT_BEFORE = '审核前',
  SIGN_AFTER = '签署合同后',
  REPAYMENT = '还款时',
  INADVANCE_REPAYMENT = '提前结清时',
}
export type TreceivingNodeEnum = keyof typeof EreceivingNode;
export enum EfundersEnum {
  INCOMING_USER = '进件用户',
  CHANNEL_MERCHANT = '渠道商户',
}
export type TfundersEnum = keyof typeof EfundersEnum;

export enum EreceiverEnum {
  INCOMING_USER = '进件用户',
  CHANNEL_MERCHANT = '渠道商户',
  LA_LA_TIAN_JIN_TAXI = '啦啦（天津）汽车科技有限公司',
  GUANG_ZHOU_YI_REN = '广州易人行商业保理有限公司',
  GUANG_ZHOU_YI_REN_LEASE = '广州易人行融资租赁有限公司',
  GUANG_ZHOU_YI_REN_LOAN = '广州易人行小额贷款有限公司',
  GUANG_ZHOU_HUO_MAN_MAN = '广州货满满汽车咨询有限公司',
  INSURANCE_SUBJECT = '投保主体',
}
export type TreceiverEnum = keyof typeof EreceiverEnum;

export enum EcollectionMethodEnum {
  ACCUMULATED_BY_DAY = '按天累积',
  TWENTY_EIGHT_DAY = '28天',
  REPAYMENT_PERIOD = '还款期限',
  STROKE = '单笔',
  SINGLE = '按笔',
  DAY = '按天',
}
export type TcollectionMethodEnum = keyof typeof EcollectionMethodEnum;

export enum EcalculationEnum {
  PROPORTIONALLY = '按比例',
  AMOUNT_PROPORTION = '按金额',
  UNPAID_INTEREST = '按未还利息',
}
export type TcalculationEnum = keyof typeof EcalculationEnum;
export type TbaseEnum = keyof typeof BASE_PROPORTION_NO_BRACKETS;
export enum EamountTypeEnum {
  FIXED_AMOUNT = '固定金额',
  RANGE_AMOUNT = '区间范围',
}
export type TamountTypeEnum = keyof typeof EamountTypeEnum;

export enum EproportionNumberEnum {
  BY_FIXED_RATION = '固定比例',
  BY_RANGE_RATION = '比例区间',
}
export type TproportionNumberEnum = keyof typeof EproportionNumberEnum;
export interface IfeeConfigItem {
  expenseItem: TexpenseItem; // 费用项
  receivingNodeEnum?: TreceivingNodeEnum; // 收取节点
  fundersEnum?: TfundersEnum; // 出资方
  receiverEnum?: TreceiverEnum; // 收取方
  collectionMethodEnum?: TcollectionMethodEnum; // 计收方式
  calculationEnum?: TcalculationEnum; // 计算方式
  baseEnum: TbaseEnum; // 基数
  amountTypeEnum?: TamountTypeEnum;
  proportionNumberEnum?: TproportionNumberEnum;
  term?: string; // 提前结清违约金-按未还利息-期数
  regionAmount?: {
    // 金额范围
    minOpenOrCloseRange: null; // 最小值开闭区间
    maxOpenOrCloseRange: null; // 最大值开闭区间
    amountMin: null; // 金额范围下限
    amountMax: null; // 金额范围上限
  };
  // 比例范围
  regionProportion?: {
    minOpenOrCloseRange: null; // 最小值开闭区间
    maxOpenOrCloseRange: null; // 最大值开闭区间
    minProportionNumberValue: null; // 最小比例值
    maxProportionNumberValue: null; // 最大比例值
  };
}
