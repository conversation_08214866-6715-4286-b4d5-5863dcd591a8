.showAttatment {
  padding: 2px;
}

.showAttatment:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.iconShow {
  // display: none;
  opacity: 0;
}

.showAttatment:hover .iconShow {
  opacity: 1;
}

//   }
// }

.buttonStyle {
  :global {
    margin-right: 10px;
    color: white;
    background-color: rgb(129, 201, 22);
    border-color: rgb(129, 201, 22);
  }
}

.buttonStyle:hover {
  color: white;
  background-color: rgb(129, 201, 22);
  border-color: rgb(129, 201, 22);
}

.buttonStyle:focus {
  color: white;
  background-color: rgb(129, 201, 22);
  border-color: rgb(129, 201, 22);
}

.confirmTitle {
  color: #f5f5f5;
}

.confirmBtnWrapper {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
}

.green-radio {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: #87d068;
  border-radius: 12px;
}

.formItemRemission {
  :global {
    .ant-col.ant-form-item-control {
      width: 221px;
    }
  }
}

.offLineDefine {
  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }

    .ant-form-item-label {
      width: 150px;
    }
  }
}

.deliveryTab {
  position: relative;
}

.addDeliveryBtn {
  position: absolute;
  top: 25px;
  right: 10px;
}

.ocrInfo {
  display: flex;
  flex-direction: column;
  gap: 10px;
  color: #acacac;
}
