/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2022-09-14 11:34:55
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import globalStyle from '@/global.less';
import { genSimpleName } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Button, Card } from 'antd';
import React, { useRef, useState } from 'react';
import type { CallPayMoneyListItem } from '../data';
import { downLoad } from '../service';
import SubmitCallBackModal from './SubmitCallBackModal';

const CallPayMoneyList: React.FC<any> = (props) => {
  const { callPayMoneyList, overDueAmount, hideCard } = props;
  const { mapUrgePersonList: mapUserList } = useModel('userList');
  const columns: ProColumns<CallPayMoneyListItem>[] = [
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 200,
    },
    {
      title: '催收员',
      dataIndex: 'urgePerson',
      render: (_, row) => {
        return mapUserList[row?.urgePerson] || '-';
      },
    },
    {
      title: '回款金额',
      dataIndex: 'amount',
    },
    {
      title: '回款方式',
      dataIndex: 'payBackType',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'urgeRecentlyMsg',
      ellipsis: true,
    },
    {
      title: '附件',
      dataIndex: 'fileList',
      render: (_, record) => (
        <>
          {record?.fileList?.length
            ? record?.fileList?.map((item: { netWorkPath: string; fileName: string }) => {
                return (
                  <div key={item.netWorkPath} title={item.fileName}>
                    {item.fileName ? (
                      <a
                        onClick={() => downLoad(item.netWorkPath, item.fileName)}
                        title={item.fileName}
                      >
                        {genSimpleName(item.fileName)}
                      </a>
                    ) : (
                      '-'
                    )}
                  </div>
                );
              })
            : '-'}
        </>
      ),
    },
    {
      title: '审批结果',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        0: { text: '待审核', status: 'default' },
        1: { text: '通过', status: 'success' },
        2: {
          text: '驳回',
          status: 'error',
        },
      },
    },
  ];
  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  function contentElement() {
    return (
      <>
        {/* <Table ></Table> */}
        <Button
          type="primary"
          onClick={() => {
            handleModalVisible(true);
          }}
          disabled={!props.isSettle || props.isSettle === 40 || props.isSettle === 50}
          className={globalStyle.mb10}
        >
          提交催收回款
        </Button>
        <ProTable<CallPayMoneyListItem>
          columns={columns}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey={(row) => {
            return row.createdAt + row.amount;
          }}
          search={false}
          options={false}
          toolBarRender={false}
          dataSource={callPayMoneyList}
          pagination={false}
        />
      </>
    );
  }

  return (
    <>
      {!hideCard && (
        <Card title="催收回款记录" className={globalStyle.mt20}>
          {contentElement()}
        </Card>
      )}
      {hideCard && <>{contentElement()}</>}
      <SubmitCallBackModal
        onOk={async () => {
          handleModalVisible(false);
          props.refresh();
        }}
        onCancel={() => {
          handleModalVisible(false);
        }}
        onVisibleChange={handleModalVisible}
        modalVisible={modalVisible}
        deadLine={props.deadLine}
        overDueAmount={overDueAmount}
      />
    </>
  );
};

export default CallPayMoneyList;
