/*
 * @Author: your name
 * @Date: 2020-11-24 11:10:13
 * @LastEditTime: 2021-05-07 14:23:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/components/ LoanInfo.ts
 */
// import { useRequest } from '@umijs/max';
import globalStyle from '@/global.less';
import { useRequest } from '@umijs/max';
import { Card, Steps } from 'antd';
import React from 'react';
import { getOrderStatus } from '../service';

interface StepProgressProps {
  orderNo: string;
}
const mapStepStatus = {
  0: '进件',
  1: '待还款',
  3: '结清',
  4: '逾期',
  5: '逾期结清',
  6: '坏账',
  10: '已放款',
  20: '撤单处理中',
  21: '提前结清',
};
const StepProgress: React.FC<StepProgressProps> = (props) => {
  const { data } = useRequest(() => {
    return getOrderStatus(props.orderNo);
  });

  // console.log(data);
  const { Step } = Steps;
  return (
    <Card className={globalStyle.mt30} title="订单进度">
      {/* <Row> */}
      <Steps current={data && data.length}>
        {data &&
          data.map((item: { status: number; statusTime: string }) => {
            return (
              <Step
                title={mapStepStatus[item.status]}
                description={item.statusTime}
                key={item.status}
              />
            );
          })}
      </Steps>
      {/* </Row> */}
    </Card>
  );
};

export default StepProgress;
