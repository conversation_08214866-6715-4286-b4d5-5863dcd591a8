import { DividerTit } from '@/components';
import { useRequest } from 'ahooks';
import { Table } from 'antd';
import React from 'react';
import { getModifyRequest } from '../service';

export const OptList = ({ configId }) => {
  const { data: dataTable } = useRequest(() => {
    return getModifyRequest(configId);
  });

  return (
    <div>
      <DividerTit title="操作记录">
        <Table
          style={{ marginTop: 10 }}
          pagination={{ pageSize: 5 }}
          columns={[
            { title: '操作时间', key: 'createdAt', dataIndex: 'createdAt' },
            { title: '操作人员', key: 'operationName', dataIndex: 'operationName' },
            {
              title: '操作详情',
              key: 'operationType',
              dataIndex: 'operationType',
            },
          ]}
          dataSource={dataTable?.data}
        />
      </DividerTit>
    </div>
  );
};

export default OptList;
