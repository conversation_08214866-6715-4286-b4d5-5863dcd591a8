export type modalProps = {
  editformRef: any;
  modalData: any;
  setModalData: (obj: any) => void;
  onCallback: () => void;
};

export interface StandardPrizePageProps {
  prizeUseStatus?: string;
  prizeType?: string;
  pageSize?: number;
  current?: number;
}

export interface StandardPrizeProps {
  id?: number;
  prizePrice?: number;
  prizeType?: string;
  prizeTotalNum?: number;
  prizeValidDays?: number;
  grantLimitValue?: number;
}

// 奖品类型枚举
export enum AwardTypeEnum {
  QUOTA_BAG = '101', //  定额红包
  RATIO_BAG = '102', //  比例红包
  INTEREST_FREE = '103', //  免息券
}

// 奖品类型
export const awardTypes = {
  [AwardTypeEnum.QUOTA_BAG]: '定额红包',
  [AwardTypeEnum.RATIO_BAG]: '比例红包',
  [AwardTypeEnum.INTEREST_FREE]: '免息券',
};

// 状态枚举
export const statusEnum = {
  '0': { text: '未使用' },
  '1': { text: '已使用' },
};

// 奖品单位映射
export const awardUnitMap = {
  [AwardTypeEnum.QUOTA_BAG]: {
    unit: '元',
    minNum: 1,
    maxNum: 10000,
    precision: 0,
    placeholderValue: '在此填写红包金额',
    placeholderLimit: '',
    msgValue: '请输入1-10000之间的整数',
    limitTpl: '用信 > [N]',
  }, // 定额红包
  [AwardTypeEnum.RATIO_BAG]: {
    unit: '%',
    minNum: 1,
    maxNum: 10,
    precision: 1,
    placeholderValue: '在此填写返现比例',
    placeholderLimit: '在此填写最高返现金额',
    msgValue: '请输入1-10之间的数',
    limitTpl: '最高[N]元',
  }, //  比例红包
  [AwardTypeEnum.INTEREST_FREE]: {
    unit: '天',
    minNum: 1,
    maxNum: 100,
    precision: 0,
    placeholderValue: '在此填写免息天数',
    placeholderLimit: '在此填写最高折算金额',
    msgValue: '请输入1-100之间的整数',
    limitTpl: '最高[N]元',
  }, //  免息券
};
