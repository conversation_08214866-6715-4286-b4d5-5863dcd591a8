export type EarlyEndPayItem = {
  orderNo: string;
  prepayNo: string;
  status: number;
  userName: string;
  createStartTime: string;
};

export type EarlyEndListParams = {
  createEndTime: string;
  createStartTime: string;
  current: number;
  orderNo: string;
  pageSize: number;
  prepayNo: string;
  status: number;
  userName: string;
};

export type AuditParams = {
  id: number;
  operatorBy?: string;
  otherMsg?: string;
  prepayType?: number;
  rejectReason?: string;
  status: number;
};

export type ExportParams = {
  createEndTime: string;
  createStartTime: string;
  current: number;
  orderNo?: '';
  pageSize: number;
  prepayNo?: '';
  status?: 0;
  userName?: '';
};
