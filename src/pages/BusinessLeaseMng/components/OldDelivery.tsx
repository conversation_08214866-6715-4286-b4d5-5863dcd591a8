//老单交车模块
import { DividerTit } from '@/components';
import globalStyle from '@/global.less';
import { useAccess } from '@umijs/max';
import { Button, Col, Image, message, Row, Steps } from 'antd';
import React, { useState } from 'react';
import type { BindCarInfo } from '../data';
import { audit } from '../service';
import HandleCarResourceModal from './HandleCarResourceModal';

type DeliveryProps = {
  data: BindCarInfo;
  orderNo: string;
  orderStatus: number;
  isNewCarDelivery: number;
  refresh: () => void;
};

const { Step } = Steps;

const OldDelivery: React.FC<DeliveryProps> = (props) => {
  const { data, orderStatus, orderNo, refresh, isNewCarDelivery } = props;
  const access = useAccess();
  const dataHandleResource = data?.carDeliveryInfo;

  const mapOptType = {
    1: '提交信息',
    20: '初审通过',
    21: '初审拒绝',
    40: '通过',
    41: '驳回',
  };
  const mapResource = {
    carDeliveryReceipt: '车辆交接单',
    groupPhoto: '人车合影',
    remark: '其他',
  };
  const itemType = {
    carDeliveryReceipt: 'IMAGE',
    groupPhoto: 'IMAGE',
    remark: 'TEXT',
  };

  const [visibleHandleCar, handleVisibleHandleCar] = useState<boolean>(false);
  const [bindRemark, setBindMark] = useState<string>('');
  const [formEditHandlerCar, setFormEditHandlerCar] = useState<object>({});

  // 审核交车或者绑车
  const handlePassCarResource = (auditType: number, operation: boolean) => {
    audit({
      auditType,
      orderNo,
      operation,
    }).then(() => {
      refresh();
      message.success('审核成功');
    });
  };
  console.log('旧交车模块！', orderStatus);
  return (
    <>
      <DividerTit title="交车资料">
        {/* 放款成功才可以展示交车按钮 */}
        {isNewCarDelivery && access.hasAccess('action_delivery_audit_businessMng_leaseList') && (
          <>
            {(orderStatus === 61 ||
              data?.carDeliveryInfo?.auditStatus === 0 ||
              data?.carDeliveryInfo?.auditStatus === 40) && (
              <Button
                // type='primary'
                className={globalStyle.ml10}
                onClick={() => {
                  const carDeliveryInfo = data?.carDeliveryInfo;
                  if (carDeliveryInfo?.auditStatus === 40) {
                    setFormEditHandlerCar({
                      carDeliveryReceipt: carDeliveryInfo?.carDeliveryReceipt,
                      groupPhoto: carDeliveryInfo?.groupPhoto,
                    });
                    setBindMark(carDeliveryInfo?.remark);
                  }
                  handleVisibleHandleCar(true);
                }}
              >
                {data?.carDeliveryInfo?.auditStatus === 40 ? '编辑' : '添加'}
                交车资料
              </Button>
            )}
            {data?.carDeliveryInfo?.auditStatus === 10 && (
              <Button
                className={globalStyle.ml10}
                onClick={() => {
                  handlePassCarResource(2, true);
                }}
              >
                通过
              </Button>
            )}
            {data?.carDeliveryInfo?.auditStatus === 10 && (
              <Button
                className={globalStyle.ml10}
                onClick={() => {
                  handlePassCarResource(2, false);
                }}
              >
                驳回
              </Button>
            )}
          </>
        )}
      </DividerTit>
      {/* 交车资料进度条 */}
      {data?.carDeliveryInfo?.auditLogs.length && (
        <Row className={`${globalStyle.mt20}  ${globalStyle.ml20}`}>
          <Col span={8}>
            <Steps
              progressDot
              current={data?.carDeliveryInfo?.auditLogs.length}
              className={globalStyle.stepItemDesc}
            >
              {data?.carDeliveryInfo?.auditLogs.map(
                (item: { type: number; operatorTime: string; operatorBy: string }) => {
                  return (
                    <Step
                      title={mapOptType[item.type]}
                      key={`${mapOptType[item.type]} ${item.operatorTime || '-'} ${
                        item.operatorBy || '-'
                      }`}
                      description={`${item.operatorTime || '-'} ${item.operatorBy || '-'}`}
                    />
                  );
                },
              )}
            </Steps>
          </Col>
        </Row>
      )}
      {/* 车辆图片信息 */}
      {dataHandleResource ? (
        Object.keys(mapResource).map((item) => {
          let itemRender = '';
          // 根据type类型渲染不同类型参数
          if (itemType[item] === 'IMAGE') {
            itemRender = dataHandleResource[item].map((typeItem: { url: string }) => {
              return (
                <Image
                  width={200}
                  height={200}
                  key={typeItem?.url}
                  wrapperStyle={{ marginRight: 10, marginBottom: 20 }}
                  fallback="data:image/png;base64,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"
                  src={typeItem?.url}
                />
              );
            });
          } else if (itemType[item] === 'TEXT') {
            itemRender = dataHandleResource[item];
          }
          return (
            <Row className={globalStyle.mt20} key={item}>
              <Col span={3}>
                <span className={`${globalStyle.fontWBold}  ${globalStyle.pl20}`}>
                  {mapResource[item]}:
                </span>
              </Col>
              <Col span={21}>{itemRender}</Col>
            </Row>
          );
        })
      ) : (
        <p className={`${globalStyle.mt20} ${globalStyle.pl20}`}>暂未提交交车资料</p>
      )}

      {/* 交车资料 */}
      <HandleCarResourceModal
        modalVisible={visibleHandleCar}
        onOk={async () => {
          handleVisibleHandleCar(false);
          props.refresh();
        }}
        formEdit={formEditHandlerCar}
        otherRemark={bindRemark}
        onCancel={() => {
          handleVisibleHandleCar(false);
        }}
        // isNewCar={props.deadLine}
        onVisibleChange={handleVisibleHandleCar}
      />
    </>
  );
};

export default OldDelivery;
