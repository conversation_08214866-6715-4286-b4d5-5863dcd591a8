import CityPicker from '@/components/CityPicker';
import ConfirmModal from '@/components/ConfirmModal';
import HeaderTab from '@/components/HeaderTab/index';
import { CHANNEL_TYPES_LABEL_MAP, CHANNEL_TYPES_MAP, LICENSE_TYPES_OPTIONS } from '@/enums';
import globalStyle from '@/global.less';
import { getCarInsuranceCustomerAccountInfo } from '@/pages/CarInsuranceCustomer/services';
import { getQueryStringObject } from '@/utils/get-query-string';
import { UnlockOutlined } from '@ant-design/icons';
import ProForm, { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useModel, useRequest } from '@umijs/max';
import { Badge, Button, Card, Form, message, Switch, Table } from 'antd';
import { debounce } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { transformLicenseTypesToString } from '..';
import type { AddParams } from '../data';
import {
  addChannel,
  getBankCardList,
  getCurConfigInfo,
  getLeaseChannelAccessList,
  modifyChannel,
} from '../service';
import AccessInfo from './AccessInfo';
import AreaInfo from './AreaInfo';
import CompensationRelationship from './CompensationRelationship'; // 代偿关系
import GpsConfig from './GpsConfig';
import styles from './index.less';
import LicensedInfo from './LicensedInfo';
import RemittanceBalanceFlow from './RemittanceBalanceFlow'; // 汇款余额流水

export type TBankEnumItem = {
  value: string;
  label: string;
  payBankNumber: string; // 联行号
  bankName: string; // 总行名称
};

const optionMap = {
  add: '添加渠道',
  show: '查看渠道',
  edit: '编辑渠道',
};

type TConfirmData = Record<string, string>;

const ConfigChannel: React.FC = () => {
  const [formInstance] = Form.useForm();
  const confirmModalRef = useRef<any>();
  /**
   * 保存当前的产品上牌类型
   */
  const [currentLicenseType, setCurrentLicenseType] = useState<string>('');
  /**
   * 保存 银行下拉框的信息
   */
  const [bankInfoList, setBankInfoList] = useState<TBankEnumItem[]>([]);
  /**
   * 页面初始化 loading 状态
   */
  const [loading, setLoading] = useState<boolean>(false);
  /**
   * 当前的 操作类型
   */
  const [opType, setOpType] = useState<string>('add');
  /**
   * 表单是否可以进行填写
   */
  const [disableForm, setDisableForm] = useState<boolean>(false);

  // 已勾选的权限列表
  const [selectedRoleCodeList, setSelectedRoleCodeList] = useState<string[]>([]);

  /**
   *
   * 渠道钱包信息
   */
  const [accountInfo, setAccountInfo] = useState(null);
  /**
   * 获取 id && type
   */
  const { id, type } = getQueryStringObject();
  const { initialState } = useModel('@@initialState');
  const isGrayUser = initialState?.currentUser?.isGrayUser;
  console.log(isGrayUser, 'isGrayUser');

  const { data, run } = useRequest(
    (idArg) => {
      setLoading(true);
      return getCurConfigInfo(idArg)
        .then(async (res) => {
          const data = res.data || {};
          const { dealerProvince, dealerCity } = data;
          const provinceArr: string[] = [];
          if (dealerProvince && dealerProvince.includes('_')) provinceArr.push(dealerProvince);
          if (dealerCity && dealerProvince !== dealerCity && dealerCity.includes('_'))
            provinceArr.push(dealerCity);
          console.log(provinceArr);
          const values = {
            ...data,
            dealerProvince: provinceArr,
          };
          setCurrentLicenseType(data?.licenseTypeList?.[0]?.toString() || '');
          formInstance.setFieldsValue(values);

          // 获取渠道信息
          await getCarInsuranceCustomerAccountInfo({ channelCode: data?.id }).then(
            (accountData) => {
              setAccountInfo(accountData?.data);
            },
          );
          return res;
        })
        .finally(() => {
          setLoading(false);
        });
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    // 保存当前的操作类型
    setOpType(type);
    // 查看不可以编辑
    if (type === 'show') {
      setDisableForm(true);
    }
    /**
     * 获取 当前配置的信息
     */
    //   if (id) {
    //     setLoading(true);
    //     getCurConfigInfo(id)
    //       .then((res) => {
    //         formInstance.setFieldsValue(res.data);
    //       })
    //       .finally(() => {
    //         setLoading(false);
    //       });
    //   }
    if (id) {
      run(id);
    }
  }, []);

  /**
   * 提交 配置信息
   */
  const handleFinish = async () => {
    const values: AddParams = formInstance.getFieldsValue();
    const { dealerProvince, licenseTypeList } = values;
    const params: any = {
      ...values,
      dealerProvince: dealerProvince?.[0] || '',
      dealerCity: dealerProvince?.[1] || dealerProvince?.[0] || '',
      licenseTypeList: typeof licenseTypeList === 'string' ? [licenseTypeList] : licenseTypeList,
    };
    switch (opType) {
      case 'add':
        // console.log(values);
        try {
          // 新增接口增加权限参数
          const leaseChannelPrivilegeReqList = selectedRoleCodeList?.map((item) => {
            return {
              roleCode: item,
              status: 1,
            };
          });
          params.leaseChannelPrivilegeReqList = leaseChannelPrivilegeReqList;
        } catch (e) {
          console.log(e);
        }
        await addChannel(params).then((res) => {
          setOpType('show');
          console.log(res);
          setDisableForm(true);
          message.success('提交成功！');
          history.push(`/operation-manager/channel/channel-detail?id=${res.data}&type=show`);
          run(res.data);
        });

        break;
      case 'edit':
        await modifyChannel({ ...params, id });
        setOpType('show');
        setDisableForm(true);
        message.success('编辑成功！');
        break;
      case 'show':
        setOpType('edit');
        setDisableForm(false);
        break;
      default:
        break;
    }
  };
  ///channel/channel-detail?id=1420570955600347159&type=show

  /**
   * 提交前先二次确认
   */
  const handleConfirm = async () => {
    const values: AddParams = formInstance.getFieldsValue();
    const formatConfirmData: TConfirmData = {
      渠道名称: values.channelName,
      渠道类型: CHANNEL_TYPES_MAP[values.channelType],
      上牌类型: transformLicenseTypesToString(
        (typeof values.licenseTypeList === 'string'
          ? [values.licenseTypeList]
          : values.licenseTypeList) || [],
      ),
      收款主体: values.receiveMaster,
      收款账号: values.receiverAccount,
      联行号: values.payBankNumber,
      收款银行: values.receiverBank,
    };
    // 设置确认数据
    confirmModalRef?.current?.setConfirmData(formatConfirmData);
    // 打开确认弹窗
    confirmModalRef?.current?.show();
    // 拦截提交
    return false;
  };

  /**
   * 编辑  配置信息
   */
  const handleEdit = () => {
    setOpType('edit');
    setDisableForm(false);
  };

  const handleSearchBank = debounce(async (newValue: string) => {
    if (!newValue) return;

    const res = await getBankCardList({ subBranchName: newValue });
    const temp: TBankEnumItem[] = res.data.map(
      (item: { bankCode: string; bankName: string; bankNo: string; subBranchName: string }) => {
        return {
          value: item.subBranchName,
          label: item.subBranchName,
          payBankNumber: item.bankNo,
          bankName: item.bankName,
        };
      },
    );
    setBankInfoList(() => [...temp]);
  }, 200);

  //

  const [roleListData, setRoleListData] = React.useState([]);
  const [roleListLoading, setRoleListLoading] = React.useState(false);
  // 权限列表全部可选的code列表
  const [allCheckedRoleCodeList, setAllCheckedRoleCodeList] = React.useState([]);
  //
  const getRoleList = async () => {
    setRoleListLoading(true);
    const res = await getLeaseChannelAccessList().catch(() => {});
    console.log('getLeaseChannelAccessList', res);
    if (res && res?.data) {
      setRoleListData(res?.data);
      // 默认全选
      const allRoleCodeList = res?.data?.map((item) => item?.roleCode) || [];
      setSelectedRoleCodeList(allRoleCodeList);
      // 存下所有可选code
      setAllCheckedRoleCodeList(allRoleCodeList);
    }
    setRoleListLoading(false);
  };
  useEffect(() => {
    if (opType === 'add') {
      getRoleList()
        .catch(() => {})
        .finally(() => {
          setRoleListLoading(false);
        });
    }
  }, [opType]);
  const addAccessColumns = [
    {
      title: '角色权限',
      key: 'roleName',
      dataIndex: 'roleName',
      render: (value) => {
        return value || '--';
      },
    },
    {
      title: '一级菜单',
      key: 'level1PrivilegeName',
      dataIndex: 'level1PrivilegeName',
      render: (value) => {
        return value || '--';
      },
    },
    {
      title: '二级菜单',
      key: 'level2PrivilegeName',
      dataIndex: 'level2PrivilegeName',
      render: (value) => {
        return value || '--';
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (_, row) => {
        const checked = selectedRoleCodeList?.includes(row?.roleCode);
        if (checked) {
          return <Badge status="success" text="启用" />;
        }
        return <Badge status="error" text="禁用" />;
      },
    },
    {
      title: '操作',
      key: 'option',
      // dataIndex: '',
      render: (_, row) => {
        // console.log(row);
        return (
          <>
            <Switch
              checkedChildren="禁用"
              unCheckedChildren="启用"
              checked={selectedRoleCodeList.includes(row?.roleCode)}
              onChange={() => {
                const checked = selectedRoleCodeList?.includes(row?.roleCode);
                if (checked) {
                  setSelectedRoleCodeList(
                    selectedRoleCodeList?.filter((item) => item !== row?.roleCode),
                  );
                } else {
                  setSelectedRoleCodeList([...selectedRoleCodeList, row?.roleCode]);
                }
              }}
            />
          </>
        );
      },
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer title={optionMap[opType]}>
        <div className={globalStyle?.drawerLabel}>
          <ProForm
            layout="horizontal"
            labelAlign="right"
            form={formInstance}
            className={styles.configForm}
            onReset={() => {
              if (currentLicenseType) {
                formInstance.setFieldsValue({ licenseTypeList: [currentLicenseType] });
              }
              // 重置权限列表
              try {
                // 重置权限列表为默认全选
                setSelectedRoleCodeList(allCheckedRoleCodeList);
              } catch (e) {
                console.log(e);
              }
            }}
            onFinish={handleConfirm}
            submitter={{
              resetButtonProps: {
                style: {
                  marginRight: '20px',
                  marginTop: '20px',
                },
                disabled: opType === 'show',
              },
              submitButtonProps: {
                style: {
                  marginTop: '20px',
                  display: opType === 'show' ? 'none' : 'block',
                },
              },
              render: (_, dom) => (
                <div className={styles.btnBox}>
                  {dom}
                  <Button
                    type="primary"
                    style={{
                      marginTop: '20px',
                      display: opType === 'show' ? 'block' : 'none',
                    }}
                    onClick={handleEdit}
                  >
                    编辑
                    <UnlockOutlined />
                  </Button>
                </div>
              ),
            }}
          >
            <Card title="基本信息" bordered loading={loading} className={globalStyle.mt20}>
              <ProForm.Group>
                <ProFormText
                  name="channelName"
                  rules={[{ required: true }]}
                  fieldProps={{ maxLength: 100 }}
                  disabled={disableForm}
                  width="sm"
                  label="渠道名称"
                  placeholder="请输入渠道名称"
                />
                <ProFormSelect
                  rules={[{ required: true }]}
                  options={CHANNEL_TYPES_LABEL_MAP}
                  disabled={disableForm}
                  placeholder="请选择渠道类型"
                  fieldProps={{
                    showSearch: true,
                    optionFilterProp: 'label',
                  }}
                  width="sm"
                  name="channelType"
                  label="渠道类型"
                />
              </ProForm.Group>
              <ProForm.Group>
                <ProFormSelect
                  rules={[{ required: true }]}
                  options={[
                    {
                      label: '一级',
                      value: 1,
                    },
                    {
                      label: '二级',
                      value: 2,
                    },
                  ]}
                  disabled={disableForm}
                  placeholder="请选择经销商类型"
                  fieldProps={{
                    showSearch: true,
                    optionFilterProp: 'label',
                  }}
                  width="sm"
                  name="dealerType"
                  label="经销商类型"
                />
                <Form.Item
                  label="经销商所属省市"
                  name="dealerProvince"
                  rules={[{ required: true }]}
                >
                  <CityPicker
                    style={{ width: 216 }}
                    placeholder="请选择经销商所属省市"
                    level={2}
                    disabled={disableForm}
                  />
                </Form.Item>
              </ProForm.Group>
              <ProForm.Group>
                <ProFormSelect
                  rules={[{ required: true }]}
                  options={LICENSE_TYPES_OPTIONS}
                  disabled={disableForm || opType === 'edit'}
                  placeholder="请选择上牌类型"
                  fieldProps={{
                    showSearch: true,
                    optionFilterProp: 'label',
                    // defaultValue: LICENSE_TYPES.AFFILIATE,
                  }}
                  // mode="multiple"
                  width="sm"
                  name="licenseTypeList"
                  label="上牌类型"
                  convertValue={(value) => {
                    return Array.isArray(value) ? value[0]?.toString() : value;
                  }}
                  // transform={(value) => { return typeof value === 'string' ? [value] : value }}
                />
              </ProForm.Group>
            </Card>
            <Card title="收款配置" bordered className={globalStyle?.mt20} loading={loading}>
              <ProForm.Group>
                <ProFormText
                  name="receiveMaster"
                  rules={[{ required: true }]}
                  fieldProps={{ maxLength: 100 }}
                  disabled={disableForm}
                  width="sm"
                  label="收款主体"
                  placeholder="请选择收款主体"
                />
                <ProFormText
                  name="receiverAccount"
                  rules={[{ required: true }]}
                  fieldProps={{ maxLength: 50 }}
                  disabled={disableForm}
                  width="sm"
                  label="收款账号"
                  placeholder="请选择收款账号"
                />
                <ProFormSelect
                  hidden
                  name="receiverBankHeadOffice"
                  rules={[{ required: true }]}
                  disabled={disableForm}
                  width="sm"
                  label="收款银行总行"
                  placeholder="请选择收款银行总行"
                />
              </ProForm.Group>
              <ProForm.Group>
                {/* <ProFormDependency name={['receiverBankHeadOffice', ['receiverBankHeadOffice']]}>
                修改: 剌世勇 2023-06-27 多次重复无限更新导致bug
                  {({ receiverBankHeadOffice }) => {
                    bankInfoList.forEach((item) => {
                      if (item.value === receiverBankHeadOffice) {
                        formInstance.setFieldsValue({ payBankNumber: item.payBankNumber });
                      }
                    });
                    return (
                      <ProFormText
                        disabled
                        name="payBankNumber"
                        rules={[{ required: true }]}
                        width="sm"
                        label="联行号"
                        placeholder="请输入联行号"
                      />
                    );
                  }}
                </ProFormDependency> */}
                <ProFormText
                  disabled
                  name="payBankNumber"
                  rules={[{ required: true }]}
                  width="sm"
                  label="联行号"
                  placeholder="请输入联行号"
                />
                <ProFormSelect
                  name="receiverBank"
                  rules={[{ required: true }]}
                  disabled={disableForm}
                  width="sm"
                  label="收款银行"
                  placeholder="请输入收款银行"
                  options={bankInfoList}
                  fieldProps={{
                    showSearch: true,
                    filterOption: false,
                    onSearch: handleSearchBank,
                    onChange: (value) => {
                      bankInfoList.forEach((item) => {
                        if (item.value === value) {
                          formInstance.setFieldsValue({
                            payBankNumber: item.payBankNumber,
                            receiverBankHeadOffice: item.bankName,
                          });
                        }
                      });
                    },
                  }}
                />
              </ProForm.Group>
            </Card>
            {opType === 'add' && (
              <>
                <Card title="功能权限设置" extra={<></>} className={globalStyle.mt20}>
                  <Table
                    columns={addAccessColumns}
                    rowKey="roleCode"
                    dataSource={roleListData}
                    loading={roleListLoading}
                    pagination={false}
                  />
                </Card>
              </>
            )}
          </ProForm>
        </div>
        {/* 上牌方信息 */}
        <LicensedInfo
          refresh={() => run(id)}
          licenseRspList={data?.licenseRspList}
          currentLicenseType={currentLicenseType}
        />
        {!loading && opType !== 'add' && isGrayUser && (
          <>
            {/* 汇款余额流水 */}
            <RemittanceBalanceFlow
              accountInfo={accountInfo}
              channelName={data?.channelName}
              accountName={data?.channelName}
            />
            {/* 代偿关系 */}
            <CompensationRelationship channelCode={data?.id} />
          </>
        )}
        {/* 展业区域信息 */}
        <AreaInfo refresh={() => run(id)} areaRspList={data?.areaRspList} />
        {/* 功能权限设置 */}
        {opType !== 'add' && <AccessInfo />}
        {/* gps供应商配置 */}
        <GpsConfig channelId={id} checkedValue={data?.gpsSupplier} />
      </PageContainer>
      <ConfirmModal
        ref={confirmModalRef}
        onOk={async () => {
          await handleFinish();
          confirmModalRef?.current?.hide();
        }}
      />
    </>
  );
};

export default ConfigChannel;
