/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-10-09 17:53:13
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-10-09 17:54:17
 * @FilePath: /code/lala-finance-biz-web/src/pages/Authentication/components/AddAuthSignatory.tsx
 * @Description: AddAuthSignatory
 */
import { ProFormText } from '@ant-design/pro-form';
import { Form } from 'antd';
import React, { memo } from 'react';
import styles from '../styles/index.less';
import UploadImage from './UploadImage';

const AddAuthSignatory: React.FC = () => {
  return (
    <div style={{ display: 'flex' }}>
      <div style={{ width: 500 }} className={styles.license}>
        <div>
          <span style={{ color: 'red' }}>*</span> 授权签约人身份证 - 人像面
          <a>需原件拍照,四角齐全,图片需小于5M,格式为.pdf,.png,.jpg,.jpeg,.webp</a>
        </div>
        <Form.Item name="frontFilePath" rules={[{ required: true }]}>
          <UploadImage />
        </Form.Item>
      </div>
      <div style={{ width: 520 }}>
        <div style={{ marginTop: 20 }} className={styles.national}>
          <Form.Item
            rules={[{ required: true }]}
            name={'unFrontFilePath'}
            required
            label={
              <div style={{ fontSize: 12 }}>
                授权签约人身份证 - 国徽面
                <br />
                <a>需原件拍照,四角齐全,图片需小于5M,格式为.pdf,.png,.jpg,.jpeg,.webp</a>
              </div>
            }
          >
            <UploadImage />
          </Form.Item>
        </div>
        <ProFormText
          name={'authorizerName'}
          label="授权签约人姓名"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
        <ProFormText
          fieldProps={{ inputMode: 'numeric' }}
          name={'authorizerCertiNo'}
          label="授权签约人身份证号"
          placeholder="请输入"
          transform={(value: string) => {
            return { authorizerCertiNo: value?.trim() };
          }}
          rules={[{ required: true }]}
        />
        <ProFormText
          name={'authorizerTel'}
          label="授权签约人联系电话"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
        <ProFormText
          name={'authorizerEmail'}
          label="授权签约人联系邮箱"
          placeholder="请输入"
          rules={[{ required: true }]}
        />
      </div>
    </div>
  );
};
export default memo(AddAuthSignatory);
