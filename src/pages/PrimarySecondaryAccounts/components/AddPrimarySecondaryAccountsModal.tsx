/*
 * @Date: 2023-08-29 17:26:49
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-10-09 18:17:18
 * @FilePath: /lala-finance-biz-web/src/pages/PrimarySecondaryAccounts/components/AddPrimarySecondaryAccountsModal.tsx
 * @Description:
 */
import React from 'react';
import ProForm, { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Button, message } from 'antd';
import { addPrimarySecondaryAccounts } from '../service';
import { CLASSIFICATION_CODE_LABEL, SCENE, SECOND_PRODUCT_SOME } from '@/enums';
import { DividerTit } from '@/components';
import OptList from '@/pages/IncreaseDetail/components/OptList';
import globalStyle from '@/global.less';

export type AddConfigModalProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  showMode?: boolean;
  currentRow?: Record<string, string>;
};

const AddPrimarySecondaryAccountsModal: React.FC<AddConfigModalProps> = ({
  visible,
  close,
  onOk,
  showMode = false,
  currentRow,
}) => {
  // console.log(currentRow);
  return (
    <>
      <ModalForm
        title="新建配置"
        layout="horizontal"
        visible={visible}
        initialValues={currentRow}
        className={globalStyle.formModal}
        modalProps={{
          centered: true,
          onCancel: close,
          okText: '提交',
        }}
        submitter={{
          render: (_, defaultDoms) => {
            let doms = defaultDoms;
            if (showMode) {
              doms = [
                <Button
                  key="update"
                  onClick={() => {
                    close();
                  }}
                >
                  关闭
                </Button>,
              ];
            }
            return doms;
          },
        }}
        onFinish={async (values) => {
          // console.log(values);
          // const mapUploadFile = convertUploadFileList(allFileList, ['attach']);
          addPrimarySecondaryAccounts({
            ...values,
            productCode: values?.productCode.map((item: { value: string }) => item.value).join(','),
          }).then(() => {
            message.success('添加成功');
            close();
            onOk();
          });
          return true;
        }}
      >
        <DividerTit title="产品信息" style={{ marginTop: 0 }} />
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: true }]}
            disabled
            initialValue={'01'}
            // request={getUserListEnum}
            options={Object.keys(CLASSIFICATION_CODE_LABEL).map((key) => ({
              value: key,
              label: CLASSIFICATION_CODE_LABEL[key],
            }))}
            width="sm"
            name="productType"
            label="产品一级分类"
          />
          <ProFormSelect
            rules={[{ required: true }]}
            disabled
            initialValue={'share_enterprise'}
            options={Object.keys(SCENE).map((key) => ({
              value: key,
              label: SCENE[key],
            }))}
            width="sm"
            name="configScene"
            label="配置场景"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: true }]}
            options={Object.keys(SECOND_PRODUCT_SOME).map((key) => ({
              value: key,
              label: SECOND_PRODUCT_SOME[key],
            }))}
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
              labelInValue: true,
              mode: 'multiple',
            }}
            width="sm"
            disabled={showMode}
            name="productCode"
            label="产品名称"
          />
        </ProForm.Group>
        <DividerTit title="配置规则" />
        <ProForm.Group>
          <ProFormText
            rules={[{ required: true }]}
            width="sm"
            disabled={showMode}
            name="orgCode"
            label="统一信用代码"
          />
        </ProForm.Group>
        {showMode && <OptList configId={currentRow?.configId} />}
      </ModalForm>
    </>
  );
};

export default AddPrimarySecondaryAccountsModal;
