/*
 * @Author: your name
 * @Date: 2021-09-28 16:09:08
 * @LastEditTime: 2021-10-18 10:16:49
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /hll-finrisk-web/src/pages/RiskPreRun/detail.tsx
 */
import ProTable from '@ant-design/pro-table';
// import { PlusOutlined } from '@ant-design/icons';
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { Button, Card, Empty } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { DataBasicInfo, RiskPreRunListItem } from './data';
import riskStyle from './index.less';
import { exportRiskResult, queryPrepareDetail } from './service';
// import {queryReplenish} from './service';

const RiskPreRunDetail: React.FC<any> = () => {
  const { flowNo } = history.location.query;
  const actionRef = useRef<ActionType>();
  const [dataOther, setDataOther] = useState<DataBasicInfo>({});
  const columns: ProColumns<RiskPreRunListItem>[] = [
    {
      title: '序号',
      dataIndex: 'taskNo',
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'orgCode',
    },
    {
      title: '结果',
      dataIndex: 'result',
      valueEnum: {
        40: { text: '通过', status: 'Success' },
        41: { text: '拒绝', status: 'Error' },
      },
    },
    {
      title: '等级',
      dataIndex: 'level',
    },

    {
      title: '评分',
      dataIndex: 'score',
    },
    {
      title: '额度',
      render: (_, { minAmount, maxAmount }) => {
        return minAmount !== null && maxAmount !== null ? `${minAmount}~${maxAmount}` : '-';
      },
    },
  ];
  const downRiskResult = () => {
    exportRiskResult(flowNo).then((res) => {
      downLoadExcel(res);
    });
  };

  useEffect(() => {
    queryPrepareDetail({ flowNo }).then((res) => {
      // console.log(res, res?.data[0]);
      setDataOther(res);
    });
  }, []);
  return (
    <>
      <HeaderTab />
      <PageContainer
        breadcrumbRender={() => (
          <div className="ant-breadcrumb">
            运营管理 / 风控预跑 /<span className="ant-breadcrumb-link"> {dataOther?.taskName}</span>
          </div>
        )}
      >
        <Card>
          <div className={riskStyle?.detailTit}>
            <div>
              {dataOther?.flag === 1 ? (
                <div>
                  批量预跑文件：
                  <a download target="_blank" href={dataOther?.filePath} rel="noopener noreferrer">
                    {dataOther?.filePathName}
                  </a>
                </div>
              ) : (
                <div>
                  <div>
                    企业名称:
                    {dataOther?.data?.length && dataOther?.data[0]?.enterpriseName}
                  </div>
                  <div>
                    统一社会信用代码:
                    {dataOther?.data?.length && dataOther?.data[0]?.orgCode}
                  </div>
                </div>
              )}
            </div>
            {dataOther?.status === 1 ? (
              <Button
                type="primary"
                onClick={() => {
                  // todo 导出
                  downRiskResult();
                }}
              >
                导出
              </Button>
            ) : (
              ''
            )}
          </div>
          {/* flag 单个0，批量1  status:0未执行完，1执行完了 */}
          {dataOther?.status === 0 ? (
            <Empty description="任务执行中，请稍等..." />
          ) : (
            <ProTable<RiskPreRunListItem>
              actionRef={actionRef}
              className={globalStyle?.mt20}
              rowKey="flowNo"
              columns={columns}
              request={(params) => queryPrepareDetail({ flowNo, ...params })}
              search={false}
              // scroll={{ x: 1920 }}
              toolBarRender={false}
            />
          )}
        </Card>
      </PageContainer>
    </>
  );
};

export default React.memo(RiskPreRunDetail);
