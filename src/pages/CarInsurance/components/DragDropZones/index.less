.drag-drop-zones-new {
  padding: 20px;
  background: #f5f5f5;

  .top-section {
    margin-bottom: 20px;

    .source-area {
      .ant-card {
        background: #fff;
        border: 2px solid #1890ff;
        transition: all 0.3s ease;
      }

      &.source-over {
        .ant-card {
          background-color: #f6ffed;
          border-color: #52c41a;
          box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
        }
      }

      .area-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          color: #1890ff;
          font-weight: 500;
        }
      }

      .source-files-container {
        max-height: 300px;
        overflow-x: hidden;
        overflow-y: auto;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }

      .source-files-grid {
        position: relative;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 12px;
        min-height: 120px;
        padding: 16px 0;
      }

      .empty-source {
        display: flex;
        grid-column: 1 / -1;
        align-items: center;
        justify-content: center;
        height: 100px;
        color: #999;
        font-size: 14px;
        background: #fafafa;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
        }
      }
    }
  }

  .bottom-section {
    .ant-card-body {
      max-height: 550px;
      overflow-y: auto;
    }

    .sidebar-list {
      .ant-card {
        height: 100%;
        border: 1px solid #d9d9d9;
      }

      .list-item {
        margin-bottom: 4px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #f0f8ff;
        }

        &.selected {
          background: #e6f7ff;
          border: 1px solid #1890ff;
        }
      }
    }

    .target-zones {
      .target-zones-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        padding: 12px 16px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 6px;

        span {
          font-weight: 500;
          font-size: 16px;
        }
      }
    }
  }

  .target-zone {
    .ant-card {
      height: 480px;
      border: 2px dashed #d9d9d9;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
      }
    }

    &.target-over {
      .ant-card {
        background-color: #f6ffed;
        border-color: #52c41a;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
      }
    }

    .zone-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        font-weight: 500;
      }
    }

    .target-drop-area {
      height: 400px;
      padding: 8px 0;
      overflow-y: auto;
    }

    .empty-target {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 80px 20px;
      color: #999;
      font-size: 14px;
      text-align: center;
      background-color: #fafafa;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
    }

    .target-files-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  .draggable-file-item {
    position: relative;
    // cursor: grab;
    transition: all 0.2s ease;

    // &:active {
    //   cursor: grabbing;
    // }

    &.dragging {
      z-index: 999;
      opacity: 0.5;
    }

    &.source-item {
      .file-item-content {
        padding: 12px;
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          transform: translateY(-2px);
        }
      }
    }

    &.target-item {
      .file-item-content {
        padding: 8px 12px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          border-color: #4faddf;
          box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);

          .file-actions {
            opacity: 1;

            .preview-btn,
            .delete-btn {
              opacity: 1;
            }
          }
        }
      }
    }

    .file-item-content {
      display: flex;
      gap: 12px;
      align-items: center;

      .drag-handle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        color: #999;
        border-radius: 4px;
        cursor: grab;
        transition: all 0.2s ease;

        &:hover {
          color: #666;
          background-color: #f0f0f0;
        }

        &:active {
          background-color: #e6e6e6;
          cursor: grabbing;
        }
      }

      .file-icon {
        display: flex;
        align-items: center;
        color: #1890ff;
        font-size: 18px;

        .preview-btn {
          padding: 2px;
          color: #1890ff;
          border: none;
          border-radius: 2px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            color: #40a9ff;
            background-color: #f0f0f0;
          }
        }
      }

      .file-info {
        flex: 1;
        min-width: 0;

        .file-name {
          margin-bottom: 2px;
          overflow: hidden;
          color: #1890ff;
          font-size: 14px;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: break-word;
          cursor: pointer;

          // &:hover {
          //   text-decoration: underline;
          // }
        }

        .file-size {
          color: #999;
          font-size: 12px;
        }
      }

      .file-actions {
        display: flex;
        gap: 4px;
        align-items: center;

        .delete-btn {
          padding: 2px;
          color: #ff4d4f;
          border: none;
          border-radius: 2px;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            color: #ff7875;
            background-color: #f0f0f0;
          }
        }
      }

      &:hover {
        .file-actions {
          opacity: 1;

          .preview-btn,
          .delete-btn {
            opacity: 1;
          }
        }
      }
    }
  }

  .drag-overlay-new {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    // transform: rotate(5deg);

    .draggable-file-item {
      .file-item-content {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
      }
    }
  }
}

// 响应式样式
@media (max-width: 1200px) {
  .drag-drop-zones-new {
    .top-section {
      .source-files-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 10px;
      }
    }

    .bottom-section {
      .target-zone {
        .ant-card {
          height: 400px;
        }

        .target-drop-area {
          height: 300px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .drag-drop-zones-new {
    padding: 16px;

    .top-section {
      .source-files-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 8px;
      }
    }

    .bottom-section {
      .ant-row {
        flex-direction: column;
      }

      .sidebar-list {
        margin-bottom: 16px;
      }

      .target-zones {
        .ant-row {
          flex-direction: column;
        }

        .target-zone {
          margin-bottom: 16px;

          .ant-card {
            height: 350px;
          }

          .target-drop-area {
            height: 250px;
          }
        }
      }
    }

    .draggable-file-item {
      &.source-item {
        .file-item-content {
          gap: 8px;
          padding: 8px;
        }
      }

      .file-item-content {
        .file-info {
          .file-name {
            font-size: 12px;
          }

          .file-size {
            font-size: 11px;
          }
        }
      }
    }
  }
}
