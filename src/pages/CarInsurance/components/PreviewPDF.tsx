import { DrawerForm } from '@ant-design/pro-components';
import type { ReactElement } from 'react';
import React from 'react';
import style from './PreviewPdf.less';

type PreviewPDFProps = {
  children: ReactElement;
  url: string;
};
const PreviewPDF: React.FC<PreviewPDFProps> = (props) => {
  return (
    <>
      <DrawerForm
        trigger={props.children}
        drawerProps={{
          className: style.previewPdf,
          placement: 'left',
          closeIcon: null,
          footer: null,
          footerStyle: { display: 'none' },
          headerStyle: { display: 'none' },
          // mask: false,
          rootStyle: { position: 'fixed' },
          bodyStyle: {
            padding: 0,
            margin: 12,
          },
        }}
        width={1024}
      >
        <iframe
          src={props.url}
          width={1000}
          //   height={600}
          style={{ height: '99%', width: '100%' }}
        />
      </DrawerForm>
    </>
  );
};

export default PreviewPDF;
