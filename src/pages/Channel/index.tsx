import { <PERSON>Container } from '@ant-design/pro-layout';
import React, { useEffect, useRef, useState } from 'react';
// import { Link } from '@umijs/max';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Checkbox, Form, Image, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
// import { downLoadExcel } from '@/utils/utils';
// import { getRepayTypeEnum } from '@/services/enum';
import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { getBlob, saveAs } from '@/utils/utils';
import { DownloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import type { ChannelListItem } from './data';
// import styles from './channel.less';
import { CHANNEL_TYPES_MAP, LICENSE_TYPES, LICENSE_TYPES_MAP } from '@/enums';
import LicenseTabsCom from '../Product/components/LicenseTabsCom';
import { channelDel, getChannel, gpsChannelConfig, queryChannel, updateStates } from './service';

const optionMap = {
  DELETE: { value: 2, label: '删除' },
  FORBID: { value: 0, label: '禁用' }, // 禁用
  START: { value: 1, label: '启用' }, // 启用
  EDIT: { label: '编辑' },
  ADD: { label: '添加' },
  SHOW: { label: '查看' },
};

export const transformLicenseTypesToString = (licenseTypeList: number[]) => {
  if (!Array.isArray(licenseTypeList)) return licenseTypeList;
  const licenseTypeStr = licenseTypeList?.map((item) => {
    return LICENSE_TYPES_MAP[item?.toString()];
  });
  return licenseTypeStr.toString();
};

const gpsOptions = [{ label: '中瑞平台', value: 'ZhongRui' }];

const ChannelList: React.FC<any> = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();

  const [curRow, setCurRow] = useState<ChannelListItem | undefined | null>(null);
  const [curOptionType, setOptionType] = useState<string | null>(null);
  // 查看按钮
  const [disableForm, setDisableForm] = useState<boolean>(false);
  // 操作栏的
  const [optVisible, handleOptVisible] = useState<boolean>();
  // 新增弹窗
  // const [addVisible, handleAddVisible] = useState<boolean>(false);
  // 二维码弹窗
  const [qrVisible, handleQRVisible] = useState<boolean>(false);

  const [selectedChannelId, setSelectedChannelId] = useState<string[]>([]);
  const [showGpsModal, setShowGpsModal] = useState(false);

  const licenseTabsRef = useRef<any>(null);
  const [licenseType, setLicenseType] = useState<string>(LICENSE_TYPES.AFFILIATE);

  const handleConfirm = () => {
    const optionRequest =
      curOptionType === 'DELETE'
        ? channelDel(curRow?.id)
        : updateStates(curRow?.id as string, curRow?.status ? 0 : 1);
    return optionRequest.then(() => {
      return true;
    });
  };
  // const handleAddorEdit = (values) => {
  //   const requestFunc =
  //     curOptionType === 'ADD'
  //       ? addChannel({
  //           ...values,
  //         })
  //       : modifyChannel({
  //           id: curRow?.id,
  //           ...values,
  //         });
  //   return requestFunc.then(() => {
  //     return true;
  //   });
  // };
  const download = (url: string, filename: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
    // });
  };
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      ...curRow,
    });
  }, [curRow, disableForm]);

  const columns: ProColumns<ChannelListItem>[] = [
    {
      title: '渠道编码',
      dataIndex: 'id',
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
      request: async () => {
        const data = await getChannel();
        return data.map((item) => {
          const { channelName } = item;
          return {
            value: channelName,
            label: channelName,
          };
        });
      },
      render(text, record) {
        return record.channelName;
      },
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      // search: false,
      valueEnum: {
        ...CHANNEL_TYPES_MAP,
        '': '全部',
      },
    },
    {
      title: '经销商类型',
      dataIndex: 'dealerType',
      search: false,
      valueEnum: {
        1: '一级',
        2: '二级',
      },
    },
    {
      title: '经销商所属省份',
      dataIndex: 'dealerProvince',
      search: false,
    },
    {
      title: '经销商所属城市',
      dataIndex: 'dealerCity',
      search: false,
    },
    {
      title: '上牌类型',
      dataIndex: 'licenseType',
      valueType: 'select',
      valueEnum: LICENSE_TYPES_MAP,
      hideInSearch: true,
      render: (_, record) => {
        return transformLicenseTypesToString(record?.licenseTypeList || []) || '-';
      },
    },
    {
      title: '收款主体',
      dataIndex: 'receiveMaster',
      search: false,
    },
    {
      title: '收款账号',
      dataIndex: 'receiverAccount',
      search: false,
    },
    {
      title: '收款银行',
      dataIndex: 'receiverBank',
      search: false,
    },
    {
      title: '联行号',
      dataIndex: 'payBankNumber',
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '禁用', status: 'error' },
        1: { text: '启用', status: 'success' },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 160,
      render: (_, record) => (
        <>
          <a
            className={globalStyle.mr10}
            onClick={() => {
              // handOpenDel(true)
              setCurRow(record);
              setOptionType('DELETE');
              handleOptVisible(true);
              // showDeleteConfirm(record?.id);
            }}
          >
            删除
          </a>
          <a
            className={globalStyle.mr10}
            onClick={() => {
              // handOpenDel(true)
              setCurRow(record);
              setOptionType(record?.status ? 'FORBID' : 'START');
              handleOptVisible(true);
              // showDeleteConfirm(record?.id);
            }}
          >
            {record?.status === 0 ? '启用' : '禁用'}
          </a>
          <br />
          <a
            className={globalStyle.mr10}
            // onClick={() => {
            //   // handOpenDel(true)
            //   setCurRow(record);
            //   setOptionType('SHOW');
            //   handleAddVisible(true);
            //   setDisableForm(true);
            //   // showDeleteConfirm(record?.id);
            // }}
            onClick={() =>
              history.push(`/operation-manager/channel/channel-detail?id=${record.id}&type=show`)
            }
          >
            查看详情
          </a>
          <a
            onClick={() => {
              setCurRow(record);
              // showDeleteConfirm(record?.id);
              handleQRVisible(true);
            }}
          >
            获取二维码
          </a>
        </>
      ),
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<ChannelListItem>
          actionRef={actionRef}
          formRef={formRef}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          request={(params) => queryChannel(params)}
          columns={columns}
          dateFormatter="string"
          rowSelection={{
            onChange: (selectedRowKeys: any[]) => {
              setSelectedChannelId(selectedRowKeys as []);
              // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
            },
          }}
          params={{ licenseType }}
          headerTitle={
            <LicenseTabsCom
              defaultVisible={true}
              tabsRef={licenseTabsRef}
              onChange={(val) => {
                setLicenseType(val as string);
                formRef?.current?.submit();
              }}
            />
          }
          toolBarRender={() => {
            return [
              <Button
                key="gpsBtn"
                type="primary"
                onClick={() => {
                  if (!selectedChannelId.length) {
                    message.warning('请先选择需要批量修改的渠道');
                    return;
                  }
                  setShowGpsModal(true);
                }}
              >
                批量修改GPS供应商
              </Button>,
              <Button
                key="button"
                type="primary"
                // onClick={() => {
                //   setOptionType('ADD');
                //   handleAddVisible(true);
                // }}
                onClick={() => history.push(`/operation-manager/channel/channel-detail?type=add`)}
              >
                添加渠道
              </Button>,
            ];
          }}
        />
      </PageContainer>
      <ModalForm
        title="提示"
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
        }}
        visible={optVisible}
        onVisibleChange={handleOptVisible}
        onFinish={async () => {
          const success: boolean = await handleConfirm();
          if (success) {
            message.success(`${optionMap[curOptionType as string].label}成功`);
            actionRef?.current?.reload();
            handleOptVisible(false);
          }
          return true;
        }}
      >
        <div>
          <ExclamationCircleOutlined className={globalStyle.iconCss} />
          {!['FORBID', 'START'].includes(curOptionType as any) && (
            <>是否确认{optionMap[curOptionType as string]?.label}该渠道?</>
          )}
          {/*  */}
          {curOptionType === 'FORBID' ? (
            <span>
              是否禁用该渠道下全部功能？
              <br />
              若禁用部分功能，请前往
              {
                <Button
                  type="link"
                  style={{ padding: '4px' }}
                  onClick={() => {
                    history.push(
                      `/operation-manager/channel/channel-detail?id=${curRow?.id}&type=show`,
                    );
                  }}
                >
                  【查看详情】
                </Button>
              }
              进行设置
            </span>
          ) : (
            ''
          )}
          {curOptionType === 'START' ? (
            <span>
              是否启用该渠道下全部功能？
              <br />
              若启用部分功能，请前往
              {
                <Button
                  type="link"
                  style={{ padding: '4px' }}
                  onClick={() => {
                    history.push(
                      `/operation-manager/channel/channel-detail?id=${curRow?.id}&type=show`,
                    );
                  }}
                >
                  【查看详情】
                </Button>
              }
              进行设置
            </span>
          ) : (
            ''
          )}
        </div>
      </ModalForm>
      {/* 新增编辑弹窗 */}
      {/* <ModalForm
        title={`${optionMap[curOptionType]?.label}渠道`}
        className={styles.formModal}
        layout="horizontal"
        form={form}
        modalProps={{
          centered: true,
          okText:
            curOptionType === 'SHOW' ? (
              <>
                <span>编辑</span>
                <UnlockOutlined />
              </>
            ) : (
              '提交'
            ),
          okButtonProps: { disabled: disableForm },
          afterClose: () => {
            setDisableForm(false);
            setCurRow(undefined);
          },
          destroyOnClose: true,
        }}
        visible={addVisible}
        onVisibleChange={handleAddVisible}
        onFinish={async (values) => {
          if (curOptionType === 'SHOW') {
            setDisableForm(false);
            setOptionType('EDIT');
            return false;
          }
          // const success: boolean = ;
          const success: boolean = await handleAddorEdit(values);
          if (success) {
            handleAddVisible(false);
            message.success(`${optionMap[curOptionType]?.label}成功`);
            // 刷新列表
            actionRef?.current?.reload();
          }
          // resetForm();
          return true;
        }}
      >
        <ProForm.Group>
          <ProFormText
            name="channelName"
            disabled={disableForm}
            rules={[{ required: true }]}
            fieldProps={{ maxLength: 100 }}
            width="sm"
            label="渠道名称"
            placeholder="请输入渠道名称"
          />
          <ProFormSelect
            rules={[{ required: true }]}
            disabled={disableForm}
            options={[
              { value: 20, label: '啦啦拍档' },
              { value: 10, label: '货拉拉' },
              { value: 0, label: '其他' },
              { value: '', label: '全部' },
            ]}
            placeholder="请选择渠道类型"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
            }}
            width="sm"
            name="channelType"
            label="渠道类型"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            name="receiveMaster"
            disabled={disableForm}
            rules={[{ required: true }]}
            fieldProps={{ maxLength: 100 }}
            width="sm"
            label="收款主体"
            placeholder="请选择收款主体"
          />
          <ProFormText
            name="receiverAccount"
            disabled={disableForm}
            rules={[{ required: true }]}
            fieldProps={{ maxLength: 50 }}
            width="sm"
            label="收款账号"
            placeholder="请选择收款账号"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            disabled={disableForm}
            name="receiverBankHeadOffice"
            rules={[{ required: true }]}
            width="sm"
            label="收款银行总行"
            placeholder="请选择收款银行总行"
            request={() => {
              return getBankCardList().then((res: { data: { receiverBankHeadOffice: string; payBankNumber: string; }[]; }) => {
                const temp = res.data.map(
                  (item: { receiverBankHeadOffice: string; payBankNumber: string }) => {
                    return {
                      value: item.receiverBankHeadOffice,
                      label: item.receiverBankHeadOffice,
                      payBankNumber: item.payBankNumber,
                    };
                  },
                );
                return Promise.resolve(temp);
              });
            }}
          />
          <ProFormText
            disabled
            name="payBankNumber"
            rules={[{ required: true }]}
            width="sm"
            label="联行号"
            placeholder="请输入联行号"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormText
            disabled={disableForm}
            name="receiverBank"
            rules={[{ required: true }]}
            width="sm"
            fieldProps={{ maxLength: 100 }}
            label="收款银行"
            placeholder="请输入收款银行"
          />
        </ProForm.Group>
      </ModalForm> */}
      <ModalForm
        title="获取二维码"
        width="400px"
        modalProps={{
          centered: true,
          okText: (
            <>
              <span>下载</span>
              <DownloadOutlined />
            </>
          ),
          okButtonProps: { disabled: disableForm },
          afterClose: () => {
            setDisableForm(false);
            setCurRow(undefined);
          },
          destroyOnClose: true,
        }}
        visible={qrVisible}
        onVisibleChange={handleQRVisible}
        onFinish={async () => {
          const success: any = await download(
            curRow?.qrCodeAddress as string,
            `${curRow?.channelName}进件二维码`,
          );
          if (success) {
            message.success(`下载成功成功`);
            actionRef?.current?.reload();
            handleQRVisible(false);
          }
          return true;
        }}
      >
        <div className={globalStyle?.textCenter}>
          <Image
            width={300}
            preview={false}
            crossOrigin="anonymous"
            height={300}
            src={curRow?.qrCodeAddress}
            placeholder={<Image preview={false} src={curRow?.qrCodeAddress} width={300} />}
          />
        </div>
      </ModalForm>
      <ModalForm
        title="批量修改GPS供应商"
        width="400px"
        visible={showGpsModal}
        layout="horizontal"
        onVisibleChange={setShowGpsModal}
        onFinish={async (values) => {
          const { gpsSupplier } = values;
          const params: any = {
            channelIds: selectedChannelId,
          };
          if (gpsSupplier) {
            params.gpsSupplier = gpsSupplier[0];
          }
          await gpsChannelConfig(params);
          setShowGpsModal(false);
          message.success('GPS供应商配置成功');
        }}
      >
        <Form.Item label="支持GPS供应商" name="gpsSupplier">
          <Checkbox.Group options={gpsOptions} />
        </Form.Item>
      </ModalForm>
    </>
  );
};

export default ChannelList;
