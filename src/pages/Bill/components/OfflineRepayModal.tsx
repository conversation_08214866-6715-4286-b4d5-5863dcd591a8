import { getProductNameEnum } from '@/services/enum';
import { accountRule, companyNameRule } from '@/services/validate';
import { disableFutureDate } from '@/utils/utils';
import type { ProFormInstance } from '@ant-design/pro-components';
import ProForm, {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import { message, Select } from 'antd';
import { debounce } from 'lodash';
import React, { useRef, useState } from 'react';
import {
  combineRepayBack,
  getBillList,
  getBillListDetail,
  getBillUserNameList,
  // getBankList,
  getRepayApplyId,
} from '../service';
import UploadCom from './UploadCom';

export type OfflineRepayModalProps = {
  onOk: () => Promise<void>;
  visible: boolean;
  onVisibleChange: (visible: boolean) => void;
  overDueAmount: number;
};

const receiveAccountInfo = {
  receiveBank: '招商银行股份有限公司广州黄埔大道支行',
  receiveBankAccount: '***************',
  receiveBankName: '广州易人行商业保理有限公司',
};

const PAYBACK_TYPE = {
  1: '线下还款',
  2: '普通对公还款',
  3: '扫码还款',
};

const OfflineRepayModal: React.FC<OfflineRepayModalProps> = (props) => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  // const { overdueCaseNo } = history.location.query;
  const productCode = '010101'; // 默认明保-一次本息：010101
  const formRef = useRef<ProFormInstance>();
  const [accountNameOptions, setAccountNameOptions] = useState([]);
  const [billDateOptions, setBillDateOptions] = useState([]);
  const [billList, setBillList] = useState({});

  const { data: productEnum } = useRequest(getProductNameEnum);
  // const { data: bankList } = useRequest(async () => {
  //   const res = await getBankList();

  //   return res?.data?.map((item) => {
  //     return {
  //       value: item.bankCode,
  //       label: item.bankName,
  //     };
  //   });
  // });

  const searchBillDateList = async (accountName) => {
    if (accountName) {
      const params = {
        accountName,
        status: 4, // 待还款：4
        pageSize: 999,
        current: 1,
      };
      const res = await getBillList(params);
      if (res?.data) {
        const options = (res.data || []).map((item) => {
          return {
            label: item.billNo,
            value: item.billNo,
          };
        });
        setBillList(res.data);
        setBillDateOptions(options);
        formRef?.current?.setFieldsValue({
          bankName: accountName,
        });
      }
    }
  };

  const onSubmit = async (value) => {
    const {
      accountBank,
      subAccountBank, // 付款银行支行
      bankName,
      bankNumber,
      billType,
      remitAmount,
      remitDate,
      remitType,
      remark,
      billNo,
    } = value;
    console.log('表单值', value);
    // 获取还款唯一键
    const applyIdRes = await getRepayApplyId();
    const applyId = applyIdRes?.data;
    //获取订单号
    const curBill = billList.find((item) => item.billNo === billNo) || {};
    if (curBill.productCode !== '010101') {
      message.warning('当前仅支持明保-一次本息还款');
      return false;
    }
    const billParams = {
      accountNumber: curBill.accountNumber,
      billNo: curBill.billNo,
      pageSize: 999,
      current: 1,
    };
    const billDetailRes = await getBillListDetail(billParams);
    console.log('订单详情', billDetailRes);
    const orderNo = billDetailRes?.data[0]?.orderNo;
    if (!orderNo) {
      message.error('没有查到订单号');
      return false;
    }
    const params = {
      initiateBillRepaymentReq: {
        productCode,
        accountNumber: curBill.accountNumber,
        accountBank: accountBank + subAccountBank,
        applyId,
        bankName,
        bankNumber,
        billType,
        billDate: curBill.billCycle.split(' - ')[0],
        repayChannel: 1, //还款方式： 1,普通对公汇款;2,溢缴款冲抵;3,专属账号还款;4,溢缴款提取;5,人工后台还款;6,主动还款;8,后台人工对公还款,9, 线下还款
        repayEntrance: 2, //还款入口： 1 催收案件管理，提交催收回款 2、账单管理，线下还款
        repayMoney: remitAmount,
        repayTime: remitDate,
        payBillAccountNumbers: [
          { accountNumber: curBill.accountNumber, billNOs: [curBill.billNo] },
        ],
      },
      offlineRemitApplyReq: {
        applyName: currentUser.userId,
        remitAmount,
        remitDate,
        orderNO: orderNo,
        attach: [],
        remitType,
        remark,
      },
    };
    const fileList = [];
    const length = value?.fileList?.length || 0;
    for (let i = 0; i < length; i += 1) {
      const item = value.fileList[i];
      if (item.response) {
        const url = item.response.data;
        if (url) {
          fileList.push({ filePath: url, name: item.name });
          params.initiateBillRepaymentReq.certificateUrl = url; // 付款凭证
        }
      }
    }
    params.offlineRemitApplyReq.attach = fileList;
    console.log('回款参数', params);
    try {
      await combineRepayBack(params);
      message.success('添加成功');
      props.onOk();
    } catch (error) {
      return false;
    }
    return true;
  };

  return (
    <>
      <ModalForm
        className="offline-repay-modal"
        title="线下还款"
        width={600}
        layout="horizontal"
        labelCol={{ span: 5 }}
        visible={props.visible}
        onVisibleChange={props.onVisibleChange}
        formRef={formRef}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
        }}
        initialValues={{
          productCode,
          productSecondaryCode: '商业保理',
          billType: '1',
          remitType: '2', // 默认普通对公还款
          ...receiveAccountInfo,
        }}
        onFinish={onSubmit}
      >
        <h4>基础信息</h4>
        <ProFormText label="产品一级分类" name="productSecondaryCode" readonly />
        <ProFormSelect
          label="产品名称"
          name="productCode"
          options={productEnum}
          readonly
          width="sm"
        />
        <ProForm.Item name="accountName" label="用户名称" rules={[{ required: true }]}>
          <Select
            className="account-name-select"
            showSearch
            placeholder="请选择"
            style={{ width: 216 }}
            onChange={searchBillDateList}
            onSearch={debounce(async (value) => {
              if (value) {
                const res = await getBillUserNameList({ accountName: value });
                console.log(res);
                if (res?.data) {
                  const options = res.data.map((name) => {
                    return { label: name, value: name };
                  });
                  setAccountNameOptions(options);
                }
              }
            }, 1e3)}
            options={accountNameOptions}
            getPopupContainer={() => {
              return document.getElementsByClassName('account-name-select')[0] as HTMLElement;
            }}
          />
        </ProForm.Item>

        <ProFormDigit
          name="remitAmount"
          label="回款金额"
          fieldProps={{ min: 0, precision: 2 }}
          placeholder="请输入回款金额"
          width="sm"
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 有坑，同时出现很多个error,待优化
                if (Number(val) <= 0) {
                  // callBack('回款金额不能大于逾期金额');
                  return Promise.reject(new Error('线下汇款金额不能为0'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  // callBack();
                  return Promise.reject(new Error('请输入数字'));
                }

                // callBack();
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormDatePicker
          name="remitDate"
          label="回款日期"
          rules={[{ required: true }]}
          placeholder="请输入回款日期"
          width="sm"
          fieldProps={{
            className: 'offline-repay-date',
            getPopupContainer: () => {
              return document.getElementsByClassName('offline-repay-date')[0] as HTMLElement;
            },
            disabledDate: disableFutureDate,
          }}
        />
        <ProFormSelect
          name="remitType"
          label="回款方式"
          placeholder="请选择回款方式"
          rules={[{ required: true }]}
          width="sm"
          valueEnum={PAYBACK_TYPE}
          fieldProps={{
            className: 'offline-remit-type',
            getPopupContainer: () => {
              return document.getElementsByClassName('offline-remit-type')[0] as HTMLElement;
            },
          }}
          disabled
        />
        <ProFormSelect
          name="billType"
          label="账单类型"
          rules={[{ required: true }]}
          width="sm"
          valueEnum={{
            1: '自有账单',
            2: '共享账单',
          }}
          disabled
        />
        <ProFormSelect
          name="billNo"
          label="指定账单还款"
          width="sm"
          rules={[{ required: true }]}
          options={billDateOptions}
          fieldProps={{
            className: 'offline-billno',
            getPopupContainer: () => {
              return document.getElementsByClassName('offline-billno')[0] as HTMLElement;
            },
          }}
        />
        {/* 上传付款凭证 */}
        <UploadCom />
        {/* 保理展示付款信息 */}

        <>
          <h4>付款信息</h4>
          <ProFormText
            name="bankNumber"
            label="付款账号"
            placeholder="请输入付款账号"
            rules={[{ required: true }, accountRule]}
            width="sm"
          />
          <ProFormText
            name="bankName"
            label="付款户名"
            placeholder="请输入付款户名"
            rules={[{ required: true }, companyNameRule]}
            width="sm"
          />
          {/* <ProFormSelect
            name="accountBank"
            label="付款银行"
            placeholder="请选择付款银行"
            rules={[{ required: true }]}
            width="sm"
            options={bankList}
            fieldProps={{
              labelInValue: true,
              className: 'offline-bank',
              getPopupContainer: () => {
                return document.getElementsByClassName('offline-bank')[0] as HTMLElement;
              },
            }}
          /> */}
          <ProFormText
            name="accountBank"
            fieldProps={{ maxLength: 30 }}
            label="付款银行"
            rules={[{ required: true }]}
            width="sm"
          />
          <ProFormText
            name="subAccountBank"
            fieldProps={{ maxLength: 30 }}
            label="付款银行支行"
            rules={[{ required: true }]}
            width="sm"
          />
          <h4>收款信息</h4>
          <ProFormText
            name="receiveBank"
            fieldProps={{ maxLength: 30 }}
            label="收款银行"
            rules={[{ required: true }]}
            width="md"
            readonly
          />
          <ProFormText
            name="receiveBankAccount"
            label="收款账户"
            rules={[{ required: true }]}
            width="md"
            readonly
          />
          <ProFormText
            name="receiveBankName"
            label="开户名称"
            rules={[{ required: true }]}
            width="md"
            readonly
          />
        </>

        <h4>其他说明</h4>
        <ProFormTextArea fieldProps={{ maxLength: 500 }} label="备注" name="remark" width="sm" />
      </ModalForm>
    </>
  );
};

export default OfflineRepayModal;
