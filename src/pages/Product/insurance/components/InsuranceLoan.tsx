import { LOAD_FROM_ENUM } from '@/enums';
import { useModel } from '@umijs/max';
import { Col, Form, Input, Row, Select } from 'antd';
import React, { useEffect } from 'react';

const InsuranceLoan: React.FC<any> = () => {
  const [form] = Form.useForm();
  const { detailData } = useModel('Product.insuranceProduct');
  useEffect(() => {
    const {
      loadFromEnum,
      loadToEnum,
      loadPeriod,
      loadModeEnum,
      insuranceLoadFromEnum,
      insuranceLoadToEnum,
      insuranceLoadPeriod,
      insuranceLoadModeEnum,
    } = detailData?.loanDTO || {};
    form.setFieldsValue({
      loadFromEnum,
      loadToEnum,
      loadPeriod,
      loadModeEnum,
      insuranceLoadFromEnum,
      insuranceLoadToEnum,
      insuranceLoadPeriod,
      insuranceLoadModeEnum,
    });
  }, [form, detailData]);

  return (
    <>
      <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 10 }} name={'insuranceLoan'}>
        <Row gutter={40}>
          <Col>
            <h4>放款1</h4>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="loadFromEnum" label="放款主体" rules={[{ required: true }]}>
              <Select disabled>
                {Object.keys(LOAD_FROM_ENUM).map((key) => (
                  <Select.Option key={key} value={key}>
                    {LOAD_FROM_ENUM[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="loadToEnum" label="收款主体" rules={[{ required: true }]}>
              <Select disabled>
                <Select.Option value="INCOMING_USER">进件用户</Select.Option>
                <Select.Option value="CHANNEL_MERCHANT">渠道商户</Select.Option>
                <Select.Option value="PREMIUM_COMPANY">保费收款公司</Select.Option>
                <Select.Option value="GUANG_ZHOU_HUO_MAN_MAN">广州货满满咨询有限公司</Select.Option>
                <Select.Option value="INSURANCE_SUBJECT">投保主体</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col style={{ marginRight: 30 }} />
          <Col span={10} offset={1}>
            <Form.Item name="loadPeriod" label="放款周期" rules={[{ required: true }]}>
              <Input placeholder="" disabled />
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="loadModeEnum" label="放款方式" rules={[{ required: true }]}>
              <Select disabled>
                <Select.Option value="ON_LINE">线上</Select.Option>
                <Select.Option value="OFF_LINE">线下</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={40}>
          <Col>
            <h4>放款2</h4>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="insuranceLoadFromEnum" label="放款主体" rules={[{ required: true }]}>
              <Select disabled>
                {Object.keys(LOAD_FROM_ENUM).map((key) => (
                  <Select.Option key={key} value={key}>
                    {LOAD_FROM_ENUM[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="insuranceLoadToEnum" label="收款主体" rules={[{ required: true }]}>
              <Select disabled>
                <Select.Option value="INCOMING_USER">进件用户</Select.Option>
                <Select.Option value="CHANNEL_MERCHANT">渠道商户</Select.Option>
                <Select.Option value="PREMIUM_COMPANY">保费收款公司</Select.Option>
                <Select.Option value="GUANG_ZHOU_HUO_MAN_MAN">广州货满满咨询有限公司</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col style={{ marginRight: 30 }} />
          <Col span={10} offset={1}>
            <Form.Item name="insuranceLoadPeriod" label="放款周期" rules={[{ required: true }]}>
              <Input placeholder="" disabled />
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="insuranceLoadModeEnum" label="放款方式" rules={[{ required: true }]}>
              <Select disabled>
                <Select.Option value="ON_LINE">线上</Select.Option>
                <Select.Option value="OFF_LINE">线下</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default InsuranceLoan;
