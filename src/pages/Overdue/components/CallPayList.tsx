/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2021-11-17 10:44:21
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import globalStyle from '@/global.less';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel, useRequest } from '@umijs/max';
import { Button, Card } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { ICallDetail } from '../data';
import { getCallerDetail } from '../service';
import { AddCallPayModal, CallDetailModal } from './index';

const CallPayList: React.FC<any> = (props) => {
  // props
  const { callPayList, businessType, hideCard, productCode } = props;

  // enum
  // enum BusinessEnum {
  //   明保 = '010101',
  //   融资租赁 = '020101',
  //   小易速贷 = '030101',
  // }

  // state
  const {
    dialAction,
    dialTime,
    calledNumber,
    isDialOuter,
    callId,
    setDialOuter,
    dialOff,
  } = useModel('caller');
  const { initialState } = useModel('@@initialState');
  const [callRecordAddModalVisible, handleModalVisible] = useState<boolean>(false);
  const [callDetailModalVisible, handleCallDetailModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const callPayModalSource = {
    productCode,
    productName: businessType,
    callRecord: callId,
    isDialOuter,
    dialTime,
    dialNumber: calledNumber,
    urgePersonName: initialState?.currentUser?.accountName || '',
    urgePersonId: initialState?.currentUser?.userId || '',
  };
  const { data: callDetailModalSource, run } = useRequest(
    (callRecord) => {
      return getCallerDetail(callRecord);
    },
    {
      manual: true,
    },
  );
  const columns: ProColumns<ICallDetail>[] = [
    {
      title: '催收员',
      dataIndex: 'urgePersonName',
    },
    {
      title: '行动标识',
      dataIndex: 'actionSignName',
    },
    {
      title: '行动结果',
      dataIndex: 'actionResultsName',
    },
    {
      title: '拨打号码',
      dataIndex: 'dialNumber',
    },
    {
      title: '拨打时间',
      dataIndex: 'dialTime',
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="detail"
          onClick={async () => {
            const { callRecord } = record;
            await run(callRecord);
            handleCallDetailModalVisible(true);
          }}
        >
          查看
        </a>,
      ],
    },
  ];

  // watch
  useEffect(() => {
    // 40: '催收结清', 50: '逾期撤销' 状态下，不弹窗
    if ([40, 50].indexOf(props.isSettle) > -1) return;
    // 挂断之后，自动弹出【添加催收记录】框
    if (dialAction === 'hangupBySys' || dialAction === 'hangup') {
      handleModalVisible(true);
    }
  }, [dialAction]);
  function contentElement() {
    return (
      <>
        <Button
          type="primary"
          onClick={() => {
            setDialOuter(false);
            handleModalVisible(true);
          }}
          disabled={!props.isSettle || props.isSettle === 40 || props.isSettle === 50}
          className={globalStyle.mb10}
        >
          添加催收记录
        </Button>
        <ProTable<ICallDetail>
          rowKey="callRecord"
          columns={columns}
          actionRef={actionRef}
          search={false}
          options={false}
          toolBarRender={false}
          dataSource={callPayList}
          pagination={false}
        />
      </>
    );
  }
  return (
    <>
      {!hideCard && (
        <Card title="催收记录" className={globalStyle.mt20}>
          {contentElement()}
        </Card>
      )}
      {hideCard && <>{contentElement()}</>}
      <AddCallPayModal
        onOk={async () => {
          dialOff();
          props.refresh();
          handleModalVisible(false);
        }}
        onVisibleChange={(value: boolean) => {
          if (!value) dialOff();
          handleModalVisible(value);
        }}
        modalVisible={callRecordAddModalVisible}
        source={callPayModalSource}
      />
      <CallDetailModal
        visible={callDetailModalVisible}
        source={callDetailModalSource}
        onCancel={() => handleCallDetailModalVisible(false)}
      />
    </>
  );
};

export default CallPayList;
