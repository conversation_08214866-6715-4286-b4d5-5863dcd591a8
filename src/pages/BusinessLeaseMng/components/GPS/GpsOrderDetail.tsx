import DividerTit from '@/components/DividerTit';
import ShowInfo from '@/components/ShowInfo';
import globalStyle from '@/global.less';
import DisassemblyModal from '@/pages/GpsMng/components/DisassemblyModal';
import OverhaulModal from '@/pages/GpsMng/components/OverhaulModal';
import { cancelGpsOrder, getGpsOrderDetial } from '@/pages/GpsMng/service';
import { useAccess } from '@umijs/max';
import {
  Button,
  Col,
  Image,
  message,
  Modal,
  Row,
  Space,
  Spin,
  Steps,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import { GPS_ACTION, GPS_ORDER_STATUS, SBC_STATUS_MAP } from './consts';

import './index.less';

import type { Action, StepItem } from './type';

const { Paragraph } = Typography;

type OrderItem = {
  gpsOrderNo: string;
};

type GpsOrderDetailProps = {
  orderNo: string;
  dataList: OrderItem[];
  refresh: () => void;
};

const baseInfoMap = {
  productName: '产品设备',
  contactName: '安装联系人',
  contactPhone: '安装联系电话',
  siteData: '施工地点',
  installationSite: '施工地点地址',
  appointTime: '安装时间',
};

const remarkInfoMap = {
  remark: '备注',
};

// 设备信息
const deviceInfoMap = {
  allNormal: '是否所有设备都正常',
  anyNormal: '是否存在正常定位的设备',
  allAbnormal: '是否所有设备否无法正常定位',
  anyAbnormal: '是否存在不能正常地位的设备',
};

const positionInfoMap = {
  equipment: '设备型号',
  imei: 'IMEI',
  sim: 'sim卡号',
  sbcStatus: '运行状态',
  equipLocation: '安装位置',
};

const positionInfoItemMap = {
  sbcStatus: SBC_STATUS_MAP,
};

const positionRowSpanMap = {
  equipment: 5,
  imei: 5,
  sim: 5,
  sbcStatus: 4,
  equipLocation: 5,
};

const feedbackType = {
  1: '改约',
  2: '等通知',
  3: '请求关闭',
  7: '智能改约',
};

type DetailContentProps = {
  orderNo: string;
  gpsOrderNo: string;
  orderStatus: number;
  refresh: () => void;
};

let loopId = null;

// 详情内容
const DetailContent = (props: DetailContentProps) => {
  const { orderNo, gpsOrderNo } = props;
  const access = useAccess();

  const [detailInfo, setDetailInfo] = useState();
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [actions, setActions] = useState<Action[]>([]);
  const [openDisassemblyModal, setOpenDisassemblyModal] = useState(false);
  const [openOverhaulModal, setOpenOverhaulModal] = useState(false);

  const deviceInfoTransMap = {
    allNormal: detailInfo?.zrOrderEqPositions?.allNormal ? '是' : '否',
    anyNormal: detailInfo?.zrOrderEqPositions?.anyNormal ? '是' : '否',
    allAbnormal: detailInfo?.zrOrderEqPositions?.allAbnormal ? '是' : '否',
    anyAbnormal: detailInfo?.zrOrderEqPositions?.anyAbnormal ? '是' : '否',
  };

  const initActions = (data) => {
    const btnList = [];
    const { orderStatus } = data.gpsOrderInfo || {};
    // 状态不等于加装完成和取消，可取消安装,渠道账号可见
    if (![GPS_ORDER_STATUS.FINISH, GPS_ORDER_STATUS.CANCEL].includes(orderStatus)) {
      btnList.push({
        key: GPS_ACTION.CANCEL,
        btnText: '取消安装',
      });
    }
    // 业务运营可见
    if (!access?.currentUser?.channelCode && orderStatus === GPS_ORDER_STATUS.FINISH) {
      btnList.push({
        key: GPS_ACTION.APPLY_DISASSEMBLY,
        btnText: '申请拆机',
      });
    }
    // 渠道账号可见
    if (
      orderStatus === GPS_ORDER_STATUS.FINISH &&
      (!data?.repairGpsOrderNos || data?.repairGpsOrderNos?.length === 0)
    ) {
      btnList.push({
        key: GPS_ACTION.APPLY_OVERHAUL,
        btnText: '申请检修',
      });
    }
    setActions(btnList);
  };

  const loopRefresh = (start: number, isRefresh?: boolean) => {
    setLoading(true);
    loopId = setTimeout(() => {
      getGpsOrderDetial(gpsOrderNo)
        .then((res) => {
          setDetailInfo(res?.data);
          clearTimeout(loopId);
          loopId = null;
          setLoading(false);
          if (isRefresh) {
            props.refresh();
          }
        })
        .catch((res) => {
          if (res?.ret === 30020) {
            // 下单失败直接重新下单,不用再轮询了
            message.error(res?.msg);
            clearTimeout(loopId);
            loopId = null;
            setLoading(false);
            props.refresh();
          } else {
            const now = Date.now();
            if (now - start > 10e3) {
              clearTimeout(loopId);
              loopId = null;
              setLoading(false);
              if (res?.msg) {
                message.error(res.msg);
              }
            } else {
              // 有问题就继续查询
              loopRefresh(start, isRefresh);
            }
          }
        });
    }, 1e3);
  };

  const initGpsDetail = async (isRefresh?: boolean) => {
    try {
      clearTimeout(loopId);
      loopId = null;
      setLoading(true);
      const res = await getGpsOrderDetial(gpsOrderNo);
      setDetailInfo(res?.data);
      setLoading(false);
      if (isRefresh) {
        props.refresh();
      }
    } catch {
      const start = Date.now();
      loopRefresh(start, isRefresh);
    }
  };

  useEffect(() => {
    initGpsDetail();
    return () => {
      clearTimeout(loopId);
      loopId = null;
    };
  }, [gpsOrderNo]);

  useMemo(() => {
    const list = detailInfo?.zrOrderSteps?.map((item: StepItem) => {
      return {
        title: item.stepName,
        subTitle: item.stepTime,
        description: item.stepPerson && (
          <div style={{ width: 120 }}>
            <Paragraph copyable={{ text: item.stepPerson }} style={{ marginBottom: 0 }}>
              {item.stepPerson}
            </Paragraph>
            {item.stepPhone && (
              <Paragraph copyable={{ text: item.stepPhone }} style={{ marginBottom: 0 }}>
                {item.stepPhone}
              </Paragraph>
            )}
          </div>
        ),
      };
    });
    setItems(list);
    // 初始化操作按钮
    if (detailInfo) {
      initActions(detailInfo);
    }
  }, [detailInfo]);

  const refresh = () => {
    initGpsDetail(true);
  };

  // 取消安装
  const handleCancel = () => {
    Modal.confirm({
      content: '是否确认取消安装？',
      onOk: async () => {
        const params = {
          vin: detailInfo?.gpsOrderInfo?.vin,
          orderNo,
          gpsOrderNo,
        };
        setLoading(true);
        try {
          await cancelGpsOrder(params);
        } finally {
          setLoading(false);
        }
        // 刷新数据
        refresh();
        message.success('取消成功！');
        return true;
      },
    });
  };

  const doAction = (actionKey: string) => {
    switch (actionKey) {
      case GPS_ACTION.CANCEL:
        handleCancel();
        break;
      case GPS_ACTION.APPLY_DISASSEMBLY: // 拆机
        setOpenDisassemblyModal(true);
        break;
      case GPS_ACTION.APPLY_OVERHAUL: // 检修
        setOpenOverhaulModal(true);
        break;
    }
  };

  return (
    <Spin spinning={loading}>
      <Steps
        items={items}
        labelPlacement="vertical"
        current={items?.length}
        style={{ justifyContent: 'center' }}
      />
      {/* 基础信息 */}
      <DividerTit title="基础信息">
        <div style={{ paddingRight: 78 }}>
          <ShowInfo noCard infoMap={baseInfoMap} data={detailInfo?.gpsOrderInfo} />
        </div>
      </DividerTit>
      {/* 反馈记录 */}
      <DividerTit title="反馈记录">
        <div className={globalStyle.pl20} style={{ border: '1px solid #eee', margin: 8 }}>
          {detailInfo?.zrOrderBaseInfo?.appointments?.map((item) => {
            return (
              <Row className={globalStyle.lineHeight40}>
                <Col span={1}>
                  <Tag bordered={false} color="processing" style={{ fontSize: 14 }}>
                    现场
                  </Tag>
                </Col>
                <Col span={5}>
                  <span style={{ marginRight: 4 }}>{item.orderTime}</span>
                  <span>{item.operator}</span>
                </Col>
                <Col span={16}>
                  <Tooltip title={item.remark}>
                    <span className="gps-order-detail-text">
                      反馈: {feedbackType[item.ifAppointed]}
                      {item.remark && <span>,{item.remark}</span>}
                    </span>
                  </Tooltip>
                </Col>
              </Row>
            );
          })}
        </div>
      </DividerTit>
      {/* 车辆信息 */}
      <DividerTit title="车辆信息">
        <Row className={globalStyle.pl20}>
          <Col span={8}>
            <span className={globalStyle.lineHeight40}>车型：</span>
            <span className={globalStyle.ml20}>
              {JSON.parse(detailInfo?.gpsOrderInfo?.extend || '{}')?.carModel}
            </span>
          </Col>
          <Col span={8}>
            <span className={globalStyle.lineHeight40}>车架号：</span>
            <span className={globalStyle.ml20}>{detailInfo?.gpsOrderInfo?.vin}</span>
          </Col>
        </Row>
      </DividerTit>
      {/* 备注信息 */}
      <DividerTit title="备注信息">
        <ShowInfo noCard infoMap={remarkInfoMap} data={detailInfo?.gpsOrderInfo} />
      </DividerTit>
      {/* 设备信息 */}
      {detailInfo?.gpsOrderInfo?.supplierCode === 'ZhongRui' && (
        <DividerTit title="设备信息">
          {detailInfo?.zrOrderEqPositions ? (
            <>
              <ShowInfo
                noCard
                rowSpan={6}
                infoMap={deviceInfoMap}
                data={detailInfo?.zrOrderEqPositions}
                selfDefine={deviceInfoTransMap}
              />
              <div style={{ border: '1px solid #eee', margin: 8 }}>
                {detailInfo?.zrOrderEqPositions?.positions?.map((item) => {
                  return (
                    <Row key={item.equipment} align="middle">
                      <Col span={20}>
                        <ShowInfo
                          noCard
                          selfRowSpan={positionRowSpanMap}
                          infoMap={positionInfoMap}
                          itemMap={positionInfoItemMap}
                          data={item}
                        />
                      </Col>
                      <Col span={4}>
                        <Button
                          type="primary"
                          size="middle"
                          onClick={() => {
                            if (item.locationUrl) {
                              window.open(item.locationUrl, '_blank');
                            }
                          }}
                        >
                          定位监控
                        </Button>
                      </Col>
                    </Row>
                  );
                })}
              </div>
            </>
          ) : (
            <div className={classNames(globalStyle.pl20, globalStyle.mt10)}>暂无数据</div>
          )}
        </DividerTit>
      )}
      {/* 图片信息 */}
      {detailInfo?.gpsOrderInfo?.supplierCode === 'ZhongRui' && (
        <DividerTit title="图片信息">
          <div className={classNames(globalStyle.pl20, globalStyle.mt10)}>
            <Space size="middle" wrap>
              {detailInfo?.zrOrderImgs
                ? detailInfo?.zrOrderImgs?.map((item) => {
                    return (
                      <div key={item.id} style={{ textAlign: 'center', width: 150 }}>
                        <Image src={item.url} width={150} height={150} />
                        <Tooltip title={item.filename}>
                          <div className="gps-order-detail-text">{item.filename}</div>
                        </Tooltip>
                        <div>{item.remark}</div>
                      </div>
                    );
                  })
                : '暂无数据'}
            </Space>
          </div>
        </DividerTit>
      )}
      {/* 操作按钮 */}
      {actions.length > 0 && (
        <Space direction="vertical" className="gps-order-detail-action">
          {actions.map((action) => {
            return (
              <Button
                key={action.key}
                type="primary"
                onClick={() => {
                  doAction(action.key);
                }}
              >
                {action.btnText}
              </Button>
            );
          })}
        </Space>
      )}
      {/* 申请拆机弹窗 */}
      <DisassemblyModal
        open={openDisassemblyModal}
        gpsOrderNo={detailInfo?.gpsOrderInfo?.gpsOrderNo}
        orderNo={orderNo}
        onCancel={() => {
          setOpenDisassemblyModal(false);
        }}
        onSuccess={() => {
          setOpenDisassemblyModal(false);
          refresh();
        }}
      />
      {/* 申请检修弹窗 */}
      <OverhaulModal
        open={openOverhaulModal}
        orderNo={orderNo}
        gpsOrderNo={detailInfo?.gpsOrderInfo?.gpsOrderNo}
        onCancel={() => {
          setOpenOverhaulModal(false);
        }}
        onSuccess={() => {
          setOpenOverhaulModal(false);
          refresh();
        }}
      />
    </Spin>
  );
};

const GpsOrderDetail = (props: GpsOrderDetailProps) => {
  const { dataList, orderNo } = props;
  return dataList?.length > 1 ? (
    <Tabs type="card">
      {dataList?.map((item) => {
        return (
          <Tabs.TabPane key={item.gpsOrderNo} tab={`安装工单${item.gpsOrderNo}`}>
            <DetailContent orderNo={orderNo} gpsOrderNo={item.gpsOrderNo} refresh={props.refresh} />
          </Tabs.TabPane>
        );
      })}
    </Tabs>
  ) : (
    <DetailContent orderNo={orderNo} gpsOrderNo={dataList[0].gpsOrderNo} refresh={props.refresh} />
  );
};

export default GpsOrderDetail;
