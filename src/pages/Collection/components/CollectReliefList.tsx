/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-29 10:38:15
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-03-21 14:46:12
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/CollectReliefList.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Tabs } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
// import { getUserListEnum } from '@/services/enum';
import { LEASE_LOAN_CHANNEL, PRODUCT_CLASSIFICATION_CODE, REPURCHASE_STATUS } from '@/enums';
import globalStyle from '@/global.less';
import { offlineRepayReviewList } from '@/pages/CallPay/carInsurance/services';
import { genSimpleName } from '@/utils/utils';
import { useModel } from '@umijs/max';
import type { CollectReliefListItem, NewCollectReliefListItem } from '../data';
import { downLoad } from '../service';
import { NewAddReliefModal } from './index';
// import { getCollectReliefList } from '../service';

const { TabPane } = Tabs;
const CollectReliefList: React.FC<any> = (props) => {
  const {
    baseInfo,
    collectReliefList,
    overDueAmount,
    hideCard,
    overdueCaseNo,
    productCode,
    productSecondaryCode,
    orderNoList,
  } = props;
  const { mapUrgePersonList: mapUserList } = useModel('userList');

  const [reduceDisabled, setReduceDisabled] = useState(false);
  const [hideTable, setHideTable] = useState(true);

  useEffect(() => {
    if (!props.isSettle || props.isSettle === 40 || props.isSettle === 50) {
      setReduceDisabled(true);
    }
    // 如果是上海银行，且未回购，申请减免置灰不可用
    if (baseInfo) {
      const { funderChannelCode, repurchaseStatus } = baseInfo || {};
      if (
        funderChannelCode === LEASE_LOAN_CHANNEL.SHANG_HAI_YING_HANG &&
        ![
          REPURCHASE_STATUS.BUY_SELF,
          REPURCHASE_STATUS.BUY_SELF_ING,
          REPURCHASE_STATUS.BUY_CHANNEL,
          REPURCHASE_STATUS.BUY_CHANNEL_ING,
        ].includes(repurchaseStatus)
      ) {
        setReduceDisabled(true);
      }
    }
  }, [props.isSettle, baseInfo]);

  const columns: ProColumns<NewCollectReliefListItem>[] = [
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    {
      title: '催收员',
      dataIndex: 'urgePerson',
      // valueType: 'select',
      render: (_, row) => {
        return mapUserList[row?.urgePerson] || '-';
      },
      // request: getUserListEnum,applyName
      hideInTable: hideTable,
    },
    {
      title: '催收员',
      dataIndex: 'applyName',
      hideInTable: !hideTable,
    },
    {
      title: '减免金额',
      dataIndex: 'amount',
      hideInTable: hideTable,
    },
    {
      title: '减免金额',
      dataIndex: 'exemptionAmount',
      hideInTable: !hideTable,
    },
    {
      title: '备注',
      dataIndex: 'urgeRecentlyMsg',
      ellipsis: true,
      hideInTable: hideTable,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      hideInTable: !hideTable, // 新减免记录列
    },
    {
      title: '附件',
      dataIndex: 'fileList',
      render: (_, record) => {
        if (hideTable) {
          // 新减免记录附件
          return (
            <>
              {record?.attachList?.length
                ? record?.attachList?.map((item: { url: string; name: string }) => {
                    return (
                      <div key={item.url} title={item.name}>
                        {item.name ? (
                          <a onClick={() => downLoad(item.url, item.name)}>
                            {genSimpleName(item.name)}
                          </a>
                        ) : (
                          '-'
                        )}
                      </div>
                    );
                  })
                : '-'}
            </>
          );
        }
        return (
          <>
            {record?.fileList?.length
              ? record?.fileList?.map((item: { netWorkPath: string; fileName: string }) => {
                  return (
                    <div key={item.netWorkPath} title={item.fileName}>
                      {item.fileName ? (
                        <a onClick={() => downLoad(item.netWorkPath, item.fileName)}>
                          {genSimpleName(item.fileName)}
                        </a>
                      ) : (
                        '-'
                      )}
                    </div>
                  );
                })
              : '-'}
          </>
        );
      },
    },
    {
      title: '审批结果',
      dataIndex: 'status',
      key: 'status',
      valueEnum: !hideTable
        ? {
            0: { text: '待审核', status: 'default' },
            1: { text: '通过', status: 'success' },
            2: {
              text: '驳回',
              status: 'error',
            },
          }
        : {
            0: { text: '待审批', status: 'default' },
            1: { text: '驳回', status: 'error' },
            2: { text: '审批通过', status: 'success' },
          },
    },
  ];
  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  // 拉取回款审核-还款单下的催收减免数据
  async function getCollectReliefList(params: any) {
    const formatParams = {
      ...params,
      overdueCaseNo,
      accountName: baseInfo?.accountName,
      repayMode: '8', // 催收减免
      secondProductCode: productSecondaryCode,
    };
    const data = await offlineRepayReviewList(formatParams);
    return data;
  }

  function contentElement() {
    return (
      <div style={{ margin: 20 }}>
        {/* 由于催收回款上线，催收减免隐藏入口，暂不作功能下线 */}
        {false && (
          <div>
            <Button
              type="primary"
              disabled={
                reduceDisabled ||
                productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING
              } // 保理没有减免
              onClick={async () => {
                handleModalVisible(true);
              }}
              className={globalStyle.mb10}
            >
              申请减免
            </Button>
          </div>
        )}
        <Tabs
          defaultActiveKey="2"
          onChange={(key) => {
            if (key === '2') {
              setHideTable(true);
            } else {
              setHideTable(false);
            }
          }}
        >
          <TabPane tab="减免记录（新）" key="2">
            <ProTable<NewCollectReliefListItem>
              columns={columns}
              actionRef={actionRef}
              scroll={{ x: 'max-content' }}
              rowKey={(row) => {
                return row.createdAt + row.amount;
              }}
              search={false}
              options={false}
              toolBarRender={false}
              request={(params) => {
                return getCollectReliefList(params).then((res) => {
                  return {
                    data: (res?.data as unknown) as NewCollectReliefListItem[],
                    total: res?.total,
                    success: true,
                  };
                });
              }}
            />
          </TabPane>
          <TabPane tab="减免记录（旧）" key="1">
            <ProTable<CollectReliefListItem>
              columns={columns}
              actionRef={actionRef}
              scroll={{ x: 'max-content' }}
              rowKey={(row) => {
                return row.createdAt + row.amount;
              }}
              search={false}
              options={false}
              toolBarRender={false}
              dataSource={collectReliefList}
              pagination={false}
              // request={(params) => getCollectReliefList(params)}
            />
          </TabPane>
        </Tabs>
      </div>
    );
  }

  return (
    <>
      {!hideCard && <>{contentElement()}</>}
      {hideCard && <>{contentElement()}</>}
      {/* 申请减免弹窗，使用逻辑展示虽然会触发dom重绘，但是可以在逻辑否时不触发组件内部生命周期 */}
      {modalVisible && (
        <NewAddReliefModal
          onOk={async () => {
            handleModalVisible(false);
            props.refresh();
          }}
          overdueCaseNo={overdueCaseNo}
          onCancel={() => {
            handleModalVisible(false);
          }}
          productCode={productCode}
          productSecondaryCode={productSecondaryCode}
          orderNoList={orderNoList}
          overDueAmount={overDueAmount}
          onVisibleChange={handleModalVisible}
          modalVisible={modalVisible}
        />
      )}
    </>
  );
};

export default CollectReliefList;
