/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-09-15 17:13:08
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-09-18 20:13:59
 * @FilePath: /code/lala-finance-biz-web/src/pages/SmsRecordMsg/service.ts
 * @Description: SmsRecordMsg/service
 */
import { headers } from '@/utils/constant';
import { request } from 'umi';
import type { OverdueSMSRecordPageItem } from './data';

// 获取逾期案件下的短信催收记录
export async function getOverdueSMSRecordPage(data: OverdueSMSRecordPageItem) {
  return request(`/bizadmin/overdue/getOverdueSMSRecordPage`, {
    method: 'post',
    headers,
    data,
    ifTrimParams: true,
  });
}

// 催收短信记录导出
export async function exportSMSRecordByUrgeId(urgeId: string) {
  return request(`/bizadmin/overdue/exportSMSRecordByUrgeId/${urgeId}`, {
    responseType: 'blob',
    headers,
    getResponse: true,
  });
}
