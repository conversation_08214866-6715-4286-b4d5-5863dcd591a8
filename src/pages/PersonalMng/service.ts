/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2024-03-08 15:15:22
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalMng/service.ts
 */
import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';
import type { EnterpriseUserListParams, ModifyStatusParams, QuotaDetailParams } from './data';

// 获取用户管理列表数据
export async function getUserList(params?: EnterpriseUserListParams) {
  return request('/quota/user/person/list', {
    method: 'GET',
    params: { ...params },
    ifTrimParams: true,
  });
}

// 获取企业用户详情---基础信息
export async function getBaseInfo(userNo: string) {
  return request(`/bizadmin/quota/getBaseInfo/${userNo}`, {
    method: 'GET',
    headers: bizadminHeader,
  });
}

// 获取企业用户详情---风控数据
export async function getRiskManagementData(userNo: string) {
  return request(`/quota/user/person/getRiskData/${userNo}`, {
    method: 'GET',
  });
}

// 获取用户详情---进件记录
export async function getRiskRecordList(userNo?: string) {
  return request(`/loan/into/user/getIntoRecord/${userNo}`, {
    method: 'GET',
  });
}

// 获取企业用户详情---额度详情
export async function getQuotaDetail(params?: QuotaDetailParams) {
  return request('/quota/account/getOperatorLogs', {
    method: 'GET',
    params: { ...params },
  });
}

// 个人用户导出
export async function userExport(params?: EnterpriseUserListParams) {
  return request('/quota/user/person/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

// 还款计划
export async function getRepayPlan(orderNo: string) {
  return request(`/repayment/cms/bill/repay/plan/${orderNo}`);
}

// 产品数据
export async function getProductData(userNo: string) {
  return request(`/quota/user/person/getProductData/${userNo}`);
}

// 冻结或者激活
export async function modifyAccountStatus(data: ModifyStatusParams) {
  return request(`/quota/user/enterprise/modifyAccountStatus`, {
    method: 'POST',
    data,
  });
}

// 催收详情
export async function getPayListByAccount(params: {
  accountNumber: string;
  pageSize: number;
  current: number;
}) {
  return request(`/bizadmin/overdue/findOverdue`, {
    method: 'post',
    data: {
      pageSize: params.pageSize,
      current: params.current,
      accountNumber: params.accountNumber,
    },
    headers: bizadminHeader,
  });
}

//获取用户实时额度
export async function getQuota(userNo: string, productSecondCode: string) {
  return request(`/bizadmin/quota/getQuotaInfo/${userNo}/${productSecondCode}`, {
    method: 'get',
    headers: bizadminHeader,
  });
}

//冻结解冻
export async function freezeOrUnfreezeQuota(data: {
  freezeAction: boolean;
  userNo: string;
  productSecondCode: string;
}) {
  return request(`/bizadmin/quota/freezeOrUnfreezeCreditAccount`, {
    method: 'post',
    data,
    headers: bizadminHeader,
  });
}

//根据ID获取进件单列表
export async function getInComeBaseInfo(userNo: string) {
  return request(`/bizadmin/active/order/getByUserNo/${userNo}`, {
    method: 'get',
    headers: bizadminHeader,
  });
}

//额度流水
export async function getQuotaFlow(data: {
  activateOrderNo: string;
  pageSize: number;
  current: number;
}) {
  return request(`/bizadmin/quota/getQuotaFlow`, {
    method: 'post',
    data,
    headers: bizadminHeader,
  });
}
