import React, { useEffect } from 'react';
import { Form, Row, Input, Col, Select, Radio } from 'antd';
import { LOAD_FROM_ENUM } from '@/enums';

type LoanProps = {
  loanData?: any;
  name?: string;
  drawerStatus?: boolean;
};
const Loaning: React.FC<LoanProps> = ({ loanData, name, drawerStatus }) => {
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue(loanData);
  }, [loanData, form]);
  return (
    <>
      <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 10 }} name={name}>
        <Row gutter={40}>
          <Col span={10} offset={1}>
            <Form.Item name="loadFromEnum" label="放款主体" rules={[{ required: !drawerStatus }]}>
              <Select disabled>
                {Object.keys(LOAD_FROM_ENUM).map((key) => (
                  <Select.Option key={key} value={key}>
                    {LOAD_FROM_ENUM[key]}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="loadToEnum" label="收款主体" rules={[{ required: !drawerStatus }]}>
              <Select disabled>
                <Select.Option value="INCOMING_USER">进件用户</Select.Option>
                <Select.Option value="CHANNEL_MERCHANT">渠道商户</Select.Option>
                <Select.Option value="PREMIUM_COMPANY">保费收款公司</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item
              name="haveCashFlow"
              label="是否有资金流"
              rules={[{ required: !drawerStatus }]}
            >
              <Radio.Group value={1} disabled>
                <Radio value>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="loadPeriod" label="放款周期" rules={[{ required: !drawerStatus }]}>
              <Input placeholder="" disabled />
            </Form.Item>
          </Col>
          <Col span={10} offset={1}>
            <Form.Item name="loadModeEnum" label="放款方式" rules={[{ required: !drawerStatus }]}>
              <Select disabled>
                <Select.Option value="ON_LINE">线上</Select.Option>
                <Select.Option value="OFF_LINE">线下</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default Loaning;
