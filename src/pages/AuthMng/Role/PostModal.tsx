import React, { useEffect, useState } from 'react';
import '../index.less';
import { Modal, Form, Input, message } from 'antd';
import { createRole, updateRole } from '../service';

type PostModalProps = {
  visible: boolean;
  closeModal: () => void;
  isEdit: boolean;
  listItem?: any;
  refreshTable: () => void;
};
export default (props: PostModalProps) => {
  const { visible, isEdit, listItem, closeModal, refreshTable } = props;
  //
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const handleCancel = () => {
    closeModal();
  };
  const submitEditForm = async (values: any) => {
    console.log('Received values of Edit form: ', values);
    setConfirmLoading(true);
    updateRole(values)
      .then((res) => {
        console.log(' updateRole res: ', res);
        setConfirmLoading(false);
        message.success('编辑成功');
        refreshTable();
        closeModal();
      })
      .catch(() => {
        setConfirmLoading(false);
      });
  };
  const submitCreateForm = async (values: any) => {
    console.log('Received values of Create form: ', values);
    setConfirmLoading(true);
    createRole(values)
      .then((res) => {
        console.log('createRole res', res);
        message.success('新建成功');
        setConfirmLoading(false);
        refreshTable();
        closeModal();
      })
      .catch(() => {
        setConfirmLoading(false);
      });
  };
  const handleSave = () => {
    form
      .validateFields()
      .then((values) => {
        // form.resetFields();
        // onCreate(values);
        submitEditForm(values);
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  };
  const handleCreate = () => {
    form
      .validateFields()
      .then((values) => {
        // form.resetFields();
        // onCreate(values);
        submitCreateForm(values);
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  };
  useEffect(() => {
    console.log('listItem', listItem);
    form.resetFields();
    if (isEdit && listItem) {
      form.setFieldsValue(listItem);
    }
  }, [listItem, visible, isEdit]);

  return (
    <>
      <Modal
        title={isEdit ? '编辑角色' : '新建角色'}
        open={visible}
        okText={isEdit ? '保存' : '确定'}
        onOk={isEdit ? handleSave : handleCreate}
        confirmLoading={confirmLoading}
        onCancel={handleCancel}
      >
        <Form
          form={form}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 16 }}
          layout="horizontal"
          autoComplete="off"
        >
          <Form.Item
            label="角色名称"
            name="roleName"
            rules={[
              { required: true, message: '必填项' },
              { max: 128, message: '最多128个字符' },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="角色编码"
            name="roleCode"
            rules={[
              { required: true, message: '必填项' },
              { max: 128, message: '最多128个字符' },
            ]}
          >
            <Input disabled={isEdit} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
