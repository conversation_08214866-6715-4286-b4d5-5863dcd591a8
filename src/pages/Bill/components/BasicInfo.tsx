/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2021-04-28 11:23:29
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import React from 'react';
import { Card, Row, Col } from 'antd';
import globalStyle from '@/global.less';

const BasicInfo: React.FC<any> = (props) => {
  const { basicInfo } = props;
  const basicMap = {
    accountName: '用户名称',
    accountNumber: '用户ID',
    productName: '产品名称',
  };
  return (
    <Card title="基础信息" className={globalStyle.mt30}>
      <Row>
        {Object.keys(basicMap).map((item) => {
          const value = (basicInfo && basicInfo[item]) || '';
          return (
            <Col span={8} key={item}>
              <div className={globalStyle.lineHeight40}>
                <span>{basicMap[item]}:</span>
                <span className={globalStyle.ml20}>{value}</span>
              </div>
            </Col>
          );
        })}
      </Row>
    </Card>
  );
};

export default BasicInfo;
