/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 11:16:24
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-12-25 17:12:05
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/product-detail.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import HeaderTabs from '@/components/HeaderTab';
import { MenuUnfoldOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { useRequest, useSearchParams } from '@umijs/max';
import { Button, Drawer, Tabs, Tag } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import * as ProductComponent from './components';
import AuthorizedChangeModal from './components/AuthorizedChangeModal';
import ChangeUpdateRepayDate from './components/ChangeUpdateRepayDate';
import Contract from './components/Contract';
import EnterpriseChangeModal from './components/EnterpriseChangeModal';
import RiskLevelChangeModal from './components/RiskLevelChangeModal';
import UpdataStatus from './components/UpdataStatus';
import { getProductBaseInfo, getRelated, getSlaveAccount } from './service';

import comDetailTitle from './index.less';

const ProductDetail = () => {
  const [searchParams] = useSearchParams();
  const subAccountNo = searchParams.get('subAccountNo');
  const userNo = searchParams.get('userNo');

  const [visibleChangeAuthroized, setVisibleChangeAuthorized] = useState<boolean>(false);
  const [visibleUpdateCompany, setVisibleUpdateCompany] = useState(false);
  const [updateRepayDayVisible, handleUpdateRepayDayVisible] = useState<boolean>(false);
  const [statusModalVisible, handleModalVisible] = useState<boolean>(false);
  const [riskLevelChangeVisible, setRiskLevelChangeVisible] = useState(false);
  const { data, run } = useRequest(() => {
    return getProductBaseInfo(subAccountNo as string);
  });

  const [visibleDrawer, setVisibleDrawer] = useState<boolean>(false);
  const mapStatus = {
    0: { color: 'red', label: '冻结' },
    1: { color: 'green', label: '激活' },
    '-1': { color: 'grey', label: '注销' },
  };

  // 次账号
  const { tableProps, refresh: refreshSlave } = useRequest(
    (params) => {
      return getSlaveAccount({ ...params, subAccountNo });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      formatResult: (response) => {
        return { list: response.data, total: response.total };
      },
    },
  );

  // 关联子企业
  const { tableProps: tableRelatedProps, refresh } = useRequest(
    (params) => {
      return getRelated({ ...params, subAccountNo });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      formatResult: (response) => {
        return { list: response.data, total: response.total };
      },
    },
  );
  useEffect(() => {
    run();
    refresh();
    refreshSlave();
  }, [subAccountNo, userNo]);
  const tabs = {
    基本信息: <ProductComponent.ProductBaseInfo data={data} />,
    关联子企业: (
      <ProductComponent.ProductRelativeSubEnterprise
        tableProps={tableRelatedProps}
        refresh={refresh}
      />
    ),
    额度流水: <ProductComponent.ProductQuotaBankWaterBill />,
    账单明细: (
      <ProductComponent.ProductBillRecord
        secondaryClassification={data?.productSecondTypeCode}
        // accountNo={data?.accountNo}
      />
    ),
    订单明细: (
      <ProductComponent.ProductOrderDetailRecord
        secondClassification={data?.productSecondTypeCode}
      />
    ),
    // 催收单: <ProductComponent.ProductOverdueDetailRecord />,
    溢缴款: <ProductComponent.ProductOverpayment />,
    次账号: (
      <ProductComponent.SecondAccount
        productCode={data?.productCode}
        tableProps={tableProps}
        refreshSlave={refreshSlave}
      />
    ),
    合同: <Contract productSecondTypeCode={data?.productSecondTypeCode} userNo={data?.userNo} />,
  };

  // useEffect(() => {
  //   console.log(!tableProps?.dataSource?.length);
  //   if (!tableProps?.dataSource?.length) delete tabs['次账号'];

  //   //关联子企业展示不展示
  //   if (!tableRelatedProps?.dataSource?.length) delete tabs['关联子企业'];
  //   // console.log(tabs);
  //   // setTabFinal(tabs);
  // }, [tableProps, tableRelatedProps]);
  const tabFinal = useMemo(() => {
    // 次账号展示不展示
    if (!tableProps?.dataSource?.length) delete tabs['次账号'];

    // 关联子企业展示不展示
    if (!tableRelatedProps?.dataSource?.length) delete tabs['关联子企业'];
    return tabs;
  }, [tableProps, tableRelatedProps]);

  const { TabPane } = Tabs;
  // console.log(tabs['基本信息']);
  return (
    <>
      <HeaderTabs />
      <PageContainer
        title={
          data ? (
            <>
              {`${data?.enterpriseName}/${userNo}/${data?.productSecondTypeName}`}
              <Tag color={mapStatus[data?.status]?.color}>{mapStatus[data?.status].label}</Tag>
            </>
          ) : null
        }
        extra={
          <MenuUnfoldOutlined
            style={{ color: '#1a87fe', fontSize: '20px' }}
            onClick={() => {
              setVisibleDrawer(!visibleDrawer);
            }}
          />
        }
      >
        <Tabs defaultActiveKey="0" style={{ background: '#fff', padding: 20 }}>
          {Object.keys(tabFinal).map((tabName, index) => {
            return (
              // todo判断是不是需要展示
              <TabPane tab={tabName} key={index}>
                {tabFinal[tabName]}
              </TabPane>
            );
          })}
        </Tabs>
        <Drawer
          width={250}
          onClose={() => {
            setVisibleDrawer(false);
          }}
          getContainer={false}
          closable={false}
          open={visibleDrawer}
          bodyStyle={{ paddingBottom: 80 }}
        >
          <p className={comDetailTitle?.drawerTit}>功能面板</p>
          <p className={comDetailTitle?.drawerItem}>
            <Button
              type="primary"
              size="large"
              onClick={() => {
                handleModalVisible(true);
                // setVisibleChangeAuthorized(true);
                setVisibleDrawer(false);
              }}
            >
              状态变更
            </Button>
          </p>
          <p className={comDetailTitle?.drawerItem}>
            <Button
              type="primary"
              size="large"
              onClick={() => {
                setVisibleUpdateCompany(true);
                setVisibleDrawer(false);
              }}
            >
              变更企业信息
            </Button>
          </p>
          <p className={comDetailTitle?.drawerItem}>
            <Button
              type="primary"
              size="large"
              onClick={() => {
                setVisibleChangeAuthorized(true);
                setVisibleDrawer(false);
              }}
            >
              授权人信息变更
            </Button>
          </p>
          <p className={comDetailTitle?.drawerItem}>
            <Button
              type="primary"
              size="large"
              onClick={() => {
                // setVisibleUpdateCompany(true);
                handleUpdateRepayDayVisible(true);
                setVisibleDrawer(false);
              }}
            >
              还款日（账期天数）变更
            </Button>
          </p>
          <p className={comDetailTitle?.drawerItem}>
            <Button
              type="primary"
              size="large"
              onClick={() => {
                // setVisibleUpdateCompany(true);
                setRiskLevelChangeVisible(true);
                setVisibleDrawer(false);
              }}
            >
              风控评级变更记录
            </Button>
          </p>
        </Drawer>
        <AuthorizedChangeModal
          title="变更授权人信息"
          visible={visibleChangeAuthroized}
          onCancel={() => {
            setVisibleChangeAuthorized(false);
          }}
          onVisibleChange={setVisibleChangeAuthorized}
          refresh={() => {
            run();
            // refreshBase();
            // refreshRisk();
            // childRef?.current?.reload();
          }}
        />
        {/* 变更企业信息 */}
        <EnterpriseChangeModal
          title="变更企业信息"
          visible={visibleUpdateCompany}
          onCancel={() => {
            setVisibleUpdateCompany(false);
          }}
          productSecondTypeCode={data?.productSecondTypeCode}
          onVisibleChange={setVisibleUpdateCompany}
        />
        {/* 变更还款日 */}
        <ChangeUpdateRepayDate
          updateRepayDayVisible={updateRepayDayVisible}
          handleUpdateRepayDayVisible={handleUpdateRepayDayVisible}
          currentData={{
            productSecondTypeCode: data?.productSecondTypeCode,
            lastConfirmDate: data?.lastConfirmDate,
            userName: data?.enterpriseName,
            repayDate: data?.billRepayDate,
            userNo,
          }}
          refresh={() => {
            run();
          }}
          // repayDateInitial={data?.billRepayDate}
        />
        {/* 变更状态 */}
        <UpdataStatus
          handleModalVisible={handleModalVisible}
          statusModalVisible={statusModalVisible}
          currentData={{
            status: data?.status,
            userNo,
            subAccountNo,
            productSecondTypeName: data?.productSecondTypeName,
            productCode: data?.productCode,
          }}
          refresh={() => {
            run();
          }}
        />
        {/* 风控评级变更记录 */}
        <RiskLevelChangeModal
          visible={riskLevelChangeVisible}
          data={{
            userNo,
            productSecondTypeCode: data?.productSecondTypeCode,
          }}
          onClose={() => {
            setRiskLevelChangeVisible(false);
          }}
        />
      </PageContainer>
    </>
  );
};

export default ProductDetail;
