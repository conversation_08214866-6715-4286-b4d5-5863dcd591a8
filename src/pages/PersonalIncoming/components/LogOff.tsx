import LoadingButton from '@/components/LoadingButton';
import { ModalForm, ProFormGroup, ProFormInstance, ProFormText } from '@ant-design/pro-components';
import ProTable from '@ant-design/pro-table';
import { Button, Form, message, Modal, Tabs, TabsProps } from 'antd';
import React, { memo, useRef } from 'react';
import { useModel } from 'umi';
import { isLogoff, logoff, logoffRecord } from '../service';

const LogOff = () => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { userName } = currentUser || {};

  const option = (
    <>
      <ProFormGroup>
        <ProFormText
          label="用户ID"
          name="userNo"
          transform={(value) => {
            return {
              userNo: value?.trim(),
            };
          }}
          rules={[{ required: true, message: '用户ID是必填的' }]}
          width="lg"
        />
        <Form.Item>
          <LoadingButton
            onClick={async () => {
              try {
                const userNo = formRef.current?.getFieldValue?.('userNo');
                const data = await isLogoff(userNo?.trim());
                message.success('查询成功');
                formRef.current?.setFieldValue('phone', data?.data);
                formRef.current?.validateFields();
              } catch (error) {
                formRef.current?.setFieldValue('phone', undefined);
              }
            }}
          >
            查询
          </LoadingButton>
        </Form.Item>
      </ProFormGroup>
      <ProFormText
        label="手机号"
        name="phone"
        disabled
        rules={[{ required: true, message: '手机号是必填的' }]}
      />
      <div style={{ textAlign: 'center' }}>提示: 注销后,该用户进件单将置为失效</div>
    </>
  );

  const records = (
    <>
      <ProTable
        search={false}
        size="small"
        request={async (params) => {
          const { current = 1, pageSize = 10 } = params;
          const data = await logoffRecord({ page: current, size: pageSize });
          return data;
        }}
        columns={[
          { dataIndex: 'userNo', title: '用户ID' },
          { dataIndex: 'phone', title: '手机号' },
          { dataIndex: 'logoffTime', title: '注销时间' },
          {
            dataIndex: 'status',
            title: '注销状态',
            render() {
              return '注销成功';
            },
          },
          { dataIndex: 'operator', title: '操作人' },
        ]}
      />
    </>
  );

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '注销操作',
      children: option,
    },
    {
      key: '2',
      label: '注销记录',
      children: records,
    },
  ];

  return (
    <ModalForm
      modalProps={{
        destroyOnClose: true,
      }}
      onFinish={async (values) => {
        const { phone, userNo } = values;
        Modal.confirm({
          title: '确认注销',
          content: '您确认要注销吗?',
          onOk: async () => {
            try {
              await logoff({
                phone,
                userNo,
                operator: userName!,
              });
              message.success('注销成功');
              formRef.current?.resetFields();
            } catch (error) {
              message.error('注销失败');
            }
          },
        });
      }}
      formRef={formRef}
      layout="horizontal"
      trigger={<Button>注销</Button>}
    >
      <Tabs defaultActiveKey="1" items={items} />
    </ModalForm>
  );
};

export default memo(LogOff);
