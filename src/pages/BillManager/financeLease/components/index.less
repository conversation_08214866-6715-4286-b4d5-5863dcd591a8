.modal_tit{
    font-size: 24px;
}
.label_width{
  width: 70px;
  min-width: 70px;
}
.block_area{
    padding-left: 20px;
    padding-top: 10px;
  &.pt_0{
    padding-top: 0;
  }
  &.pl_0{
    padding-left: 0;
  }
}
.label_css{
  font-size: 15px !important;
}
.tit{
    font-size: 18px;
    margin-top: 20px;
    font-weight: bold;
    &.mr5{
        margin-right: 5px;
    }
}
.line{
    line-height: 40px;
    &.mt10{
      margin-top: 12px;
    }
   .submit_offline_label{
    text-align: right;
    display: inline-block;
    width: 110px;
    font-size: 15px;
    margin-right: 6px;
   }
   .submit_offline_amount{
    color: red;
    font-size: 18px;
    margin-left: 10px ;
    margin-right: 10px;
    }
    &.mt10{
        margin-top: 20px;
    }
    &::after{
        content: "";
        display: block;
        height: 0.5px; /* 设置分割线的高度为0.5px */
        background: #eee; /* 分割线的颜色 */
        margin: 10px auto; /* 上下外边距，以及自动左右外边距来居中 */
    }
    &.no_border{
      margin-bottom: 10px;
    }
    &.no_border::after {
      content: none;
    }
}
.remission_sel{
  display: flex;
  align-items: baseline;
  .ant-form-item{
    margin-bottom: 0;
  }
}


.settle_tit{
  font-size: 18px;
  font-weight: bold;
}







