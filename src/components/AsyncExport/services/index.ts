import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';
import type { IexportRecord, ItaskCode } from '../types';

// 查询导出记录
export async function exportRecord(params: {
  current: number;
  pageSize: number;
  taskCode: ItaskCode[];
}): Promise<{ data: IexportRecord[]; current: number; pageSize: number; total: number }> {
  return request(`/bizadmin/excel/query`, {
    headers: bizadminHeader,
    method: 'POST',
    data: params,
  });
}
