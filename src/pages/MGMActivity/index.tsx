import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { FormInstance } from 'antd/lib/form';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import React, { useRef } from 'react';
import { KeepAlive } from 'react-activation';
import { queryMGMOrder } from './service';
// // 解决日期范围选择器默认值报错
dayjs.extend(weekday);
dayjs.extend(localeData);

const MGMList: React.FC<any> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const columns: ProColumns[] = [
    {
      title: '用户ID',
      dataIndex: 'inviterUserNo',
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '状态',
      dataIndex: 'statusDesc',
      search: false,
    },
    {
      title: '授信时间',
      dataIndex: 'loanTime',
      search: false,
    },
    {
      title: '累计邀请注册人数',
      dataIndex: 'totalValidInvitationNum',
      search: false,
    },
    {
      title: '累计邀请用信人数',
      dataIndex: 'totalValidInvitationLoanNum',
      search: false,
    },
    {
      title: '累计偿还首期款人数',
      dataIndex: 'totalValidInvitationRepayNum',
      search: false,
    },
    {
      title: '累计邀请收益',
      dataIndex: 'totalPrizePrice',
      search: false,
      render: (_, record) => {
        return `¥${Number(new BigNumber((record?.totalPrizePrice as any) || 0).div(100))}`;
      },
    },
    {
      title: '累计发放金额',
      dataIndex: 'totalGrantPrizePrice',
      render: (_, record) => {
        return `¥${Number(new BigNumber((record?.totalGrantPrizePrice as any) || 0).div(100))}`;
      },
      search: false,
    },
  ];

  return (
    <>
      <PageContainer>
        <ProTable
          actionRef={actionRef}
          formRef={formRef}
          pagination={{ pageSizeOptions: ['10'], defaultPageSize: 10 }}
          rowKey="bizNo"
          scroll={{ x: 'max-content' }}
          request={(params) => {
            return queryMGMOrder(params);
          }}
          search={{
            labelWidth: 100,
            defaultCollapsed: false,
          }}
          columns={columns}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'MGMActivity/list'}>
      <MGMList />
    </KeepAlive>
  </>
);
