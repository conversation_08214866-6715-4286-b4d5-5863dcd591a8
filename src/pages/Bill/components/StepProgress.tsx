/*
 * @Author: your name
 * @Date: 2020-11-24 11:10:13
 * @LastEditTime: 2021-04-28 11:26:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/components/ LoanInfo.ts
 */
// import { useRequest } from '@umijs/max';
import globalStyle from '@/global.less';
import { Card, Steps } from 'antd';
import React from 'react';

interface StepProgressProps {
  stepStatus: StatusItem[];
  index: number;
}
interface StatusItem {
  bol: boolean;
  desc: string;
  localDate: string;
}

const StepProgress: React.FC<StepProgressProps> = (props) => {
  const { stepStatus, index } = props;
  const { Step } = Steps;
  return (
    <Card className={globalStyle.mt30} title="账单进度">
      <Steps current={index + 1}>
        {stepStatus &&
          stepStatus.map((item: { bol: boolean; desc: string; localDate: string }) => {
            return <Step title={item.desc} description={item.localDate} key={item.localDate} />;
          })}
      </Steps>
    </Card>
  );
};

export default StepProgress;
