/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-10-18 19:47:50
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-13 22:56:53
 * @FilePath: /lala-finance-biz-web/src/pages/Channel/components/AddLicensedInfo.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { LICENSE_TYPES, LICENSE_TYPES_OPTIONS } from '@/enums';
import { getLicenseCityEnum } from '@/services/enum';
import { ProFormDependency } from '@ant-design/pro-components';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { message } from 'antd';
import React from 'react';
import type { AddLicense } from '../data';
import { addLicense, modifyLicenseInfo } from '../service';
export type AddLicensedInfoProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean | undefined;
  values: string;
  onVisibleChange: any;
  title: string;
  refresh: () => void;
  channelId?: string;
  curRow?: Record<string, any>;
  currentLicenseType?: string;
  // keyTitle: string; //2为线下回款审批
  // offlineRemitOrderNo?: string;
};
const AddLicensedInfo: React.FC<AddLicensedInfoProps> = ({
  visible,
  onVisibleChange,
  // onOk,
  close,
  title,
  refresh,
  channelId,
  curRow,
  currentLicenseType,
}) => {
  return (
    <div>
      <ModalForm
        title={`${title}上牌方`}
        width="500px"
        layout="horizontal"
        open={visible}
        initialValues={{
          ...curRow,
          // licenseCity: curRow
          //   ? { label: curRow?.licenseCity, value: curRow?.licenseCityCode }
          //   : null,
          licenseCityList: curRow?.licenseCityList?.map((item: any) => {
            return {
              label: item?.licenseCity,
              title: item?.licenseCity,
              key: item?.licenseCityCode,
              value: item?.licenseCityCode,
            };
          }),
          licenseType: curRow?.licenseType || currentLicenseType,
        }}
        onOpenChange={onVisibleChange}
        modalProps={{
          centered: true,
          onCancel: close,
        }}
        onFinish={async (formValues) => {
          console.log(formValues);
          const commonObj = {
            ...formValues,
            channelId,
          };
          const addPost = {
            ...commonObj,
          } as AddLicense;

          const editPost = {
            id: curRow?.id,
            ...commonObj,
          };
          const finalPostInfo = title === '新增' ? addPost : editPost;
          const finalPost = title === '新增' ? addLicense : modifyLicenseInfo;
          // console.log(finalPostInfo)
          // return;
          await finalPost(finalPostInfo).then(() => {
            message.success(`${title}成功`);
            // onOk()
            refresh();
            close();
          });

          return true;
        }}
      >
        <ProFormSelect
          name="licenseType"
          options={LICENSE_TYPES_OPTIONS}
          rules={[{ required: true }]}
          width="sm"
          labelCol={{ span: 7 }}
          label="上牌类型"
          placeholder="请选择上牌类型"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
          }}
          convertValue={(value) => value?.toString()}
          disabled={title === '编辑' || !!currentLicenseType}
        />
        <ProFormDependency name={['licenseType']}>
          {({ licenseType }) => {
            if (licenseType == LICENSE_TYPES.CUSTOMER) {
              //  个户只需要多选上牌城市信息，挂靠则单选
              return (
                <ProFormSelect
                  rules={[{ required: true }]}
                  request={getLicenseCityEnum}
                  placeholder="请选择上牌城市"
                  name="licenseCityList"
                  width="sm"
                  labelCol={{ span: 7 }}
                  label="上牌城市"
                  debounceTime={60000}
                  fieldProps={{
                    mode: 'multiple',
                    showSearch: true,
                    optionFilterProp: 'label',
                    labelInValue: true,
                  }}
                  transform={(value, namePath) => {
                    console.log('transform1', value);
                    return {
                      [namePath]: value?.map((item) => {
                        return {
                          licenseCity: item?.title || item?.label,
                          licenseCityCode: item?.value,
                        };
                      }),
                    };
                  }}
                />
              );
            }
            return (
              <>
                <ProFormText
                  name="licenseCompany"
                  rules={[{ required: true }]}
                  width="sm"
                  labelCol={{ span: 7 }}
                  fieldProps={{ maxLength: 20 }}
                  label="上牌公司"
                  placeholder="请输入上牌公司"
                />
                <ProFormSelect
                  rules={[{ required: true }]}
                  request={getLicenseCityEnum}
                  placeholder="请选择上牌城市"
                  name="licenseCityList"
                  width="sm"
                  labelCol={{ span: 7 }}
                  label="上牌城市"
                  debounceTime={60000}
                  fieldProps={{
                    showSearch: true,
                    optionFilterProp: 'label',
                    labelInValue: true,
                  }}
                  transform={(value, namePath) => {
                    console.log('transform2', value);
                    return {
                      [namePath]: [{ licenseCity: value?.label, licenseCityCode: value?.value }],
                    };
                  }}
                />
                <ProFormText
                  name="orgCode"
                  rules={[{ required: true }, { pattern: /\w{18}/, message: '需为18位' }]}
                  labelCol={{ span: 7 }}
                  width="sm"
                  fieldProps={{ maxLength: 18 }}
                  label="统一社会信用代码"
                  placeholder="请输入统一社会信用代码"
                />
                <ProFormText
                  name="phone"
                  rules={[{ required: true }, { pattern: /^[0-9-()]+$/, message: '格式有误' }]}
                  labelCol={{ span: 7 }}
                  width="sm"
                  fieldProps={{ maxLength: 20 }}
                  label="联系电话"
                  placeholder="请输入联系电话"
                />
              </>
            );
          }}
        </ProFormDependency>
      </ModalForm>
    </div>
  );
};

export default AddLicensedInfo;
