import { useModel } from '@umijs/max';
import { Alert, theme } from 'antd';
import React, { memo } from 'react';

const { useToken } = theme;

const OverdueAlert = memo(() => {
  const { token } = useToken();

  const { overdueLongestDays } = useModel('CarInsurance.carInsurance');
  return overdueLongestDays !== undefined ? (
    <Alert
      message={`注意：客户当前存在逾期，最大逾期天数：${overdueLongestDays}天`}
      type="error"
      style={{ marginBottom: 24, color: token.colorError }}
    />
  ) : null;
});

export default OverdueAlert;
