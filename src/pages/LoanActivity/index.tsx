import HeaderTab from '@/components/HeaderTab';
import { getUuid } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { FormInstance } from 'antd/lib/form';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import React, { useRef } from 'react';
import { KeepAlive } from 'react-activation';
import { queryOrder } from './service';
// // 解决日期范围选择器默认值报错
dayjs.extend(weekday);
dayjs.extend(localeData);

const OrderList: React.FC<any> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const columns: ProColumns[] = [
    {
      title: '订单号',
      dataIndex: 'bizNo',
    },
    {
      title: '客户ID',
      dataIndex: 'userNo',
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      valueEnum: {
        '小贷- 小拉用信抽奖返现活动': { text: '小贷- 小拉用信抽奖返现活动' },
        '小贷- 用信抽奖返现活动': { text: '小贷- 用信抽奖返现活动' },
        '小贷- 用信抽奖返现活动（二期）': { text: '小贷- 用信抽奖返现活动（二期）' },
        '小贷- 用信抽奖返现活动三期': { text: '小贷- 用信抽奖返现活动（三期）' },
        '小贷- MGM裂变活动（一期）': { text: '小贷- MGM裂变活动（一期）' },
        '小贷-新客贷前试借活动一期': { text: '小贷-新客贷前试借活动一期' },
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        popupMatchSelectWidth: false,
      },
    },
    {
      title: '奖励类型',
      dataIndex: 'activityType',
      valueEnum: {
        // 1: { text: '随机红包' },
        2: { text: '固定额度红包' },
        // 3: { text: '折扣优惠券' },
        4: { text: '邀请返现活动' },
        6: { text: '试借活动' },
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        // defaultValue:
        showArrow: true,
      },
    },
    {
      title: '奖励金额',
      // 单位：分，展示为元
      dataIndex: 'prizePrice',
      search: false,
      render: (_, record) => {
        return `¥${Number(new BigNumber(record?.prizePrice as any).div(100))}`;
      },
    },
    {
      title: '奖励发放状态',
      dataIndex: 'status',
      valueEnum: {
        0: { text: '不发送' },
        1: { text: '待发送' },
        2: { text: '发送中' },
        3: { text: '发放成功' },
        4: { text: '发放失败' },
        5: { text: '已作废' },
        6: { text: '退票' },
      },
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        // defaultValue:
        showArrow: true,
      },
    },
  ];

  return (
    <>
      <PageContainer>
        <ProTable
          actionRef={actionRef}
          formRef={formRef}
          rowKey={getUuid()}
          scroll={{ x: 'max-content' }}
          request={(params) => {
            return queryOrder(params);
          }}
          search={{
            labelWidth: 100,
            defaultCollapsed: false,
          }}
          columns={columns}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'loanActivity/list'}>
      <OrderList />
    </KeepAlive>
  </>
);
