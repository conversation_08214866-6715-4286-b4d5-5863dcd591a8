import { Button, Space, Typography } from 'antd';
import React, { memo } from 'react';

const { Text, Title } = Typography;

interface MatchCompleteStatusProps {
  // 匹配结果数据
  totalCount: number; // 总数量
  matchedCount: number; // 已匹配数量
  businessNo?: string; // 业务编号

  // 操作回调
  onGiveUp?: () => void; // 放弃操作
  onConfirm?: () => void; // 确认操作
}

const MatchCompleteStatus: React.FC<MatchCompleteStatusProps> = ({
  totalCount,
  matchedCount,
  businessNo,
  onGiveUp,
  onConfirm,
}) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '60px 20px',
        backgroundColor: '#fafafa',
        borderRadius: '8px',
        minHeight: '300px',
      }}
    >
      <div style={{ width: '100%', maxWidth: '400px', textAlign: 'center' }}>
        <Title level={4} style={{ marginBottom: '24px', color: '#52c41a' }}>
          数据匹配完成
        </Title>

        <div style={{ marginBottom: '24px' }}>
          <Text style={{ fontSize: '16px', color: '#333' }}>
            匹配结果：{matchedCount} / {totalCount} 条数据
          </Text>
        </div>

        {businessNo && (
          <Text
            type="secondary"
            style={{ fontSize: '12px', marginBottom: '24px', display: 'block' }}
          >
            业务编号：{businessNo}
          </Text>
        )}

        <Space>
          <Button onClick={onGiveUp} disabled={!onGiveUp}>
            放弃本次操作
          </Button>
          <Button type="primary" onClick={onConfirm} disabled={!onConfirm}>
            确认
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default memo(MatchCompleteStatus);
