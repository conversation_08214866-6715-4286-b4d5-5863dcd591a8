/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2021-02-03 14:48:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
import { request } from '@umijs/max';

import type { OrderListParams } from './data';

export async function queryOrder(params?: OrderListParams) {
  return request('/repayment/cms/order/list4Page', {
    params,
    ifTrimParams: true,
  });
}

// 进件信息
export async function getIncomingInfo(orderNo: string) {
  return request(`/quota/order/getOrderInfo/${orderNo}`);
}

// 还款计划
export async function getRepayPlan(orderNo: string) {
  return request(`/repayment/cms/bill/repay/plan/${orderNo}`);
}

// 还款信息
export async function getRepayInfo(orderNo: string) {
  return request(`/repayment/cms/bill/repay/info/${orderNo}`);
}

// 放款信息
export async function getLoanInfo(orderNo: string) {
  return request(`/quota/order/getLendingInfo/${orderNo}`);
}

// 订单状态
export async function getOrderStatus(orderNo: string) {
  return request(`/quota/order/getOrderStatus/${orderNo}`);
}

// 根据还款计划查详情
export async function getRepayRegist(planNo: string) {
  return request(`/repayment/cms/bill/record/${planNo}`);
}

// 订单管理导出
export async function orderExport(params: OrderListParams) {
  return request(`/repayment/cms/order/exportExcel`, {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

// 产品名称
export async function productList() {
  return request(`/repayment/query/productName`);
}

// 获取合同
export async function contractList(orderNo: string) {
  return request(`/repayment/cms/bill/order/debtFile/${orderNo}`);
}

//
export async function getOssPath(ossPath: string) {
  return request(`/repayment/getNetUrl`, {
    method: 'GET',
    params: { ossPath },
  });
}

// 根据订单id债转转让
export async function getDebtByOrderId(orderId: string) {
  return request(`/repayment/cms/order/debt/${orderId}`);
}

// 费用记录
export async function getFeePlan(orderId: string) {
  return request(`/repayment/cms/order/cost/${orderId}`);
}
