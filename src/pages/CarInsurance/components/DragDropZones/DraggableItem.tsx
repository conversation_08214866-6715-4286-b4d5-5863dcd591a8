import { DeleteOutlined, DragOutlined, EyeOutlined } from '@ant-design/icons';
import { useDraggable } from '@dnd-kit/core';
import { Button, Tooltip } from 'antd';
import React from 'react';
import { DragItem } from '.';

interface DraggableItemProps {
  item: DragItem;
  onDelete?: (itemId: string) => void;
  isDragging?: boolean;
  isSource?: boolean;
}
// 可拖拽的文件项目组件
const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  onDelete,
  isDragging = false,
  isSource = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: isCurrentDragging,
  } = useDraggable({
    id: item.id,
  });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        opacity: isCurrentDragging ? 0.5 : 1,
      }
    : undefined;

  // 处理预览
  const handlePreview = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('预览按钮点击:', item);

    if (item.url) {
      // 使用全局的预览方法，通过事件或回调传递给父组件
      const event = new CustomEvent('drog-item-preview-file', {
        detail: {
          url: item.url,
          fileName: item.name,
        },
      });
      console.log('发送预览事件:', event.detail);
      window.dispatchEvent(event);
    } else {
      console.warn('文件没有URL，无法预览:', item);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`draggable-file-item ${isCurrentDragging ? 'dragging' : ''} ${
        isSource ? 'source-item' : 'target-item'
      }`}
      {...attributes}
      {...listeners}
    >
      <div className="file-item-content">
        {/* <div className="drag-handle" {...attributes} {...listeners}> */}
        <div className="drag-handle">
          <DragOutlined />
        </div>
        <div className="file-icon">
          <Tooltip title="预览">
            <Button
              type="text"
              size="small"
              // icon={item.type === 'image' ? <FileImageOutlined /> : <FolderOutlined />}
              icon={<EyeOutlined />}
              onClick={handlePreview}
              className="preview-btn"
            />
          </Tooltip>
        </div>
        <div className="file-info">
          <div className="file-name">
            <Tooltip title={item.name}>{item.name}</Tooltip>
          </div>
          {/* {item.size && <div className="file-size">{item.size}</div>} */}
        </div>
        <div className="file-actions">
          {onDelete && (
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(item.id);
                }}
                className="delete-btn"
              />
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(DraggableItem);
