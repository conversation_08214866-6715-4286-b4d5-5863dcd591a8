/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2023-03-17 17:50:03
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/CallPay/components/PassModel.ts
 */
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { message, Modal } from 'antd';
import React, { useState } from 'react';
import { callPayReview, collectReliefReview, compensateAudit, offlineRemit } from '../service';

// import { ModalFormProps } from '@ant-design/pro-form';
export type PassModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  passModalVisible: boolean;
  values: string;
  title: string;
  keyTitle: string; //2为线下回款审批
  offlineRemitOrderNo?: string;
  compensateNo?: string;
  repayKey?: string;
};

const PassModal: React.FC<PassModalProps> = ({
  keyTitle,
  title,
  repayKey,
  passModalVisible,
  values,
  compensateNo,
  offlineRemitOrderNo,
  onOk,
  onCancel,
}) => {
  const [loading, setLoading] = useState(false);
  // console.log(repayKey);
  return (
    <>
      <Modal
        width={400}
        // bodyStyle={{ padding: '32px 40px 48px' }}
        destroyOnClose
        centered
        title={`${title}审核`}
        open={passModalVisible}
        okButtonProps={{ loading: loading }}
        onOk={async () => {
          setLoading(true);
          let fun;
          if (keyTitle === '2') {
            if (repayKey === '44') {
              fun = offlineRemit({ auditResult: 1, offlineRemitOrderNo });
            } else {
              fun = compensateAudit({ auditResult: 1, compensateNo });
            }
          } else {
            if (title === '催收减免') {
              fun = collectReliefReview({ paymentId: values, status: 1 });
            } else {
              fun = callPayReview({ paymentId: values, status: 1 });
            }
          }
          fun
            .then(() => {
              setLoading(false);
              message.success('审批成功');
              onOk();
            })
            .finally(() => {
              setLoading(false);
            });
          return true;
        }}
        onCancel={() => {
          onCancel();
        }}
      >
        <ExclamationCircleOutlined
          style={{ fontSize: '20px', marginRight: 10, color: '#faad14' }}
        />
        是否确认通过该笔{title}？
      </Modal>
    </>
  );
};

export default PassModal;
