/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-28 18:04:09
 * @LastEditors: alan771.tu <EMAIL>
 * @LastEditTime: 2024-11-11 15:33:43
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/CollectInfo.tsx
 * @Description: 催收信息
 */
import { DividerTit } from '@/components';
import React from 'react';
import { CallPayList, CallPayMoneyList, CollectReliefList } from './index';
interface CollectInfoProps {
  data: any;
  btnRef?: any;
  refresh: (type?: string) => void;
  overdueCaseNo: string;
  orderNoList: { orderNo: string; totalOverdueAmount: string }[];
  dataDetail: any;
}
const CollectInfo: React.FC<CollectInfoProps> = ({
  data,
  refresh,
  overdueCaseNo,
  btnRef,
  orderNoList,
  dataDetail,
}) => {
  return (
    <div>
      <DividerTit title="催收记录">
        <CallPayList
          // 字段留为兜底使用，新数据改为分页接口 date: 2024-11-13 alan771.tu
          callPayList={data?.repaySmsUrgeCollectionBO} // /bizadmin/overdue/overdueCase/overdueRecord/
          isSettle={data?.status}
          userNo={data?.userNo}
          overdueCaseNo={overdueCaseNo}
          businessType={dataDetail?.productName ?? ''}
          productSecondCode={data?.productSecondaryCode ?? ''}
          productCode={dataDetail?.productCode ?? ''}
          refresh={refresh}
          btnRef={btnRef}
        />
      </DividerTit>

      {
        // 车险的企业用户 没有减免记录
        data.userType !== 5 && (
          <DividerTit title="减免记录">
            <CollectReliefList
              collectReliefList={data?.repayRemissionRecordBO}
              isSettle={data?.status}
              productCode={dataDetail?.productCode}
              productSecondaryCode={data?.productSecondaryCode}
              orderNoList={orderNoList}
              overDueAmount={Number(dataDetail?.totalOverdueAmount)}
              overdueCaseNo={overdueCaseNo}
              baseInfo={data}
              refresh={refresh}
            />
          </DividerTit>
        )
      }

      {data.userType !== 5 && (
        <DividerTit title="回款记录">
          <CallPayMoneyList
            callPayMoneyList={data?.repayPaymentRecordBO}
            overdueCaseNo={overdueCaseNo}
            isSettle={data?.status}
            overDueAmount={Number(dataDetail?.totalOverdueAmount)}
            productClassicCode={data?.productClassicCode}
            productCode={dataDetail?.productCode}
            funderChannelCode={data?.funderChannelCode}
            orderNoList={orderNoList}
            productSecondaryCode={data?.productSecondaryCode}
            repayOverdueCostBO={dataDetail?.repayOverdueCostBO}
            userNo={data?.userNo}
            billType={data?.billType}
            accountName={data?.accountName}
            refresh={refresh}
          />
        </DividerTit>
      )}
    </div>
  );
};

export default React.memo(CollectInfo);
