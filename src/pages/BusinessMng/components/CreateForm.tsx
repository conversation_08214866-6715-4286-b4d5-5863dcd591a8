/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-01-23 17:59:42
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/components/CreateForm.tsx
 */
import { Modal } from 'antd';
import React from 'react';

interface CreateFormProps {
  title: string;
  modalVisible: boolean;
  onCancel: () => void;
}

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { title, modalVisible, onCancel } = props;

  return (
    <Modal
      destroyOnClose
      title={title}
      open={modalVisible}
      onCancel={() => onCancel()}
      width="auto"
      centered
      footer={null}
    >
      {props.children}
    </Modal>
  );
};
export default CreateForm;
