/*
 * @Author: your name
 * @Date: 2021-01-13 15:01:51
 * @LastEditTime: 2021-11-17 17:48:17
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/CollectRelief/components/index.ts
 */

import DispatchModal from './DispatchModal';
import BasicInfo from './BasicInfo';
import CollectReliefList from './CollectReliefList';
import CallPayList from './CallPayList';
import SubmitCallBackModal from './SubmitCallBackModal';
import AddCallPayModal from './AddCallPayModal';
import ApplyReliefModal from './ApplyReliefModal';
import CallPayMoneyList from './CallPayMoneyList';
import UploadCom from './UploadCom';
import WithHold from './WithHold';
import AddWithHoldModal from './AddWithHoldModal';
import OverdueDetail from './OverdueDetail';
import CallDetailModal from './CallDetailModal';
import RepaymentRecord from './RepaymentRecord';

export {
  DispatchModal,
  BasicInfo,
  CollectReliefList,
  CallPayList,
  SubmitCallBackModal,
  AddCallPayModal,
  ApplyReliefModal,
  CallPayMoneyList,
  UploadCom,
  WithHold,
  AddWithHoldModal,
  OverdueDetail,
  CallDetailModal,
  RepaymentRecord,
};
