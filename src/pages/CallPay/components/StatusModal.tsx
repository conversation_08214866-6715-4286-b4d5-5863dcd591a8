/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2022-02-15 13:46:21
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/CollectRelief/components/PassModel.ts
 */
import { useModel } from '@umijs/max';
import { Modal, Steps } from 'antd';
import React from 'react';

export type StatusModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  statusModalVisible: boolean;
  values: any;
};

const StatusModal: React.FC<StatusModalProps> = (props) => {
  const { Step } = Steps;
  const { values } = props;
  const { mapUserList } = useModel('userList');
  const statusMap = {
    0: '待审核',
    1: '通过',
    2: '驳回',
  };
  return (
    <>
      <Modal
        destroyOnClose
        centered
        title="审核状态"
        open={props.statusModalVisible}
        // footer={submitter}
        onOk={async () => {
          props.onOk();
        }}
        onCancel={() => {
          props.onCancel();
        }}
        footer={null}
      >
        {/* urgePerson是催收回款tab创建人字段
         operationLog是线下回款tab日志列表 */}
        {values?.operationLog?.length ? (
          <>
            <Steps
              direction="vertical"
              size="small"
              current={values?.operationLog?.length}
              progressDot
            >
              {values?.operationLog.map(
                (item: {
                  operationDesc: string;
                  creatTime: string;
                  operatorName: string;
                  errorMsg: string;
                }) => {
                  return (
                    <Step
                      title={item?.operationDesc}
                      description={
                        <>
                          <div>
                            {item?.creatTime || '-'}
                            {mapUserList[item?.operatorName] || item?.operatorName || '-'}
                          </div>
                          <div>{item?.errorMsg}</div>
                        </>
                      }
                    />
                  );
                },
              )}
            </Steps>
          </>
        ) : (
          <Steps direction="vertical" size="small" current={values?.reviewTime ? 2 : 1} progressDot>
            {values.reviewTime ? (
              <Step
                title={statusMap[values.status]}
                description={
                  <>
                    <div>
                      {values.reviewTime || '-'}
                      {mapUserList[values?.reviewPerson] || values?.reviewPerson || '-'}
                    </div>
                    <div>{values.reason}</div>
                  </>
                }
              />
            ) : null}
            <Step
              title="提交"
              description={`${values.createdAt || '-'}
           ${mapUserList[values?.urgePerson] || values?.reviewPerson || '-'}`}
            />
          </Steps>
        )}
      </Modal>
    </>
  );
};

export default StatusModal;
