// 申请拆机弹窗
import CityPicker from '@/components/CityPicker';
import { pattern } from '@/utils/validate';
import {
  ModalForm,
  ProForm,
  ProFormCheckbox,
  ProFormDateTimePicker,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Col, Form, message } from 'antd';
import { useState } from 'react';
import { createGpsOrder, getGpsDefaultData } from '../service';
import { ORDER_TYPE } from './consts';

type Props = {
  open: boolean;
  orderNo?: string;
  gpsOrderNo?: string;
  onCancel: () => void;
  onSuccess: () => void;
};

type OrderForm = {
  orderReason: string; // 拆机原因
  contactName: string; // 安装联系人
  contactPhone: string; // 安装联系人电话
  installationSite: string; // 施工地点
  siteCode: string; // 施工地区行政编码
  remark: string; //备注
  appointTime: string; // 安装时间
};

const reasonList = {
  取消工单: '取消工单',
  贷款结清: '贷款结清',
  重复安装: '重复安装',
  移机: '移机',
};

const DisassemblyModal = (props: Props) => {
  const { orderNo, gpsOrderNo } = props;
  const [form] = Form.useForm();
  const [btnLoading, setBtnLoading] = useState(false);

  const { data: defaultData } = useRequest(() => {
    return getGpsDefaultData(orderNo);
  });

  const formatCity = (cityValues: string[]) => {
    let cityStr = '';
    cityValues.forEach((item, index) => {
      cityStr += item.split('_')[0];
      if (index !== cityValues.length - 1) {
        cityStr += '-';
      }
    });
    return cityStr;
  };

  const onSubmit = async (values: OrderForm) => {
    const { siteCode, supplierCode } = values;
    const newSiteCode = siteCode[siteCode.length - 1]?.split('_')[1];
    const params = {
      ...values,
      orderType: ORDER_TYPE.DISASSEMBLY,
      orderNo,
      siteCode: newSiteCode,
      siteData: formatCity(siteCode),
      baseGpsOrderNo: gpsOrderNo,
    };
    if (supplierCode) {
      params.supplierCode = supplierCode[0];
    }
    setBtnLoading(true);
    try {
      await createGpsOrder(params);
      message.success('申请拆机成功！');
      setBtnLoading(false);
      props.onSuccess();
    } catch {
      setBtnLoading(false);
    }
  };

  return (
    <ModalForm<OrderForm>
      title="申请拆机"
      form={form}
      layout="horizontal"
      open={props.open}
      grid
      rowProps={{
        gutter: [16, 0],
      }}
      modalProps={{
        width: 1100,
        destroyOnClose: true,
        onCancel: props.onCancel,
        okButtonProps: {
          loading: btnLoading,
        },
      }}
      initialValues={{
        vin: defaultData?.vin,
      }}
      onFinish={onSubmit}
    >
      <ProForm.Group>
        <ProFormText
          colProps={{ span: 8 }}
          labelCol={{ span: 9 }}
          name="vin"
          label="车辆信息(车架号)"
          rules={[{ required: true }]}
          disabled={!!defaultData?.vin}
          fieldProps={{
            autoComplete: 'on',
          }}
        />
      </ProForm.Group>
      <ProFormSelect
        required
        colProps={{ span: 8 }}
        labelCol={{ span: 9 }}
        name="orderReason"
        label="拆机原因"
        valueEnum={reasonList}
        rules={[{ required: true }]}
      />
      <ProForm.Group>
        <Col span={8}>
          <Form.Item
            required
            label="施工地点"
            name="siteCode"
            labelCol={{ span: 9 }}
            rules={[{ required: true }]}
          >
            <CityPicker level={2} />
          </Form.Item>
        </Col>
        <ProFormText
          required
          colProps={{ span: 9 }}
          labelCol={{ span: 8 }}
          name="installationSite"
          label="施工地点地址"
          fieldProps={{
            maxLength: 200,
            autoComplete: 'on',
          }}
          rules={[{ required: true }]}
        />
        <ProFormDateTimePicker
          required
          name="appointTime"
          label="预约时间"
          colProps={{ span: 7 }}
          labelCol={{ span: 9 }}
          width="md"
          rules={[{ required: true }]}
        />
        <ProFormText
          required
          colProps={{ span: 8 }}
          labelCol={{ span: 9 }}
          name="contactName"
          label="联系人"
          fieldProps={{
            maxLength: 50,
            autoComplete: 'on',
          }}
          rules={[{ required: true }]}
        />
        <ProFormText
          required
          colProps={{ span: 9 }}
          labelCol={{ span: 8 }}
          name="contactPhone"
          label="联系人电话"
          fieldProps={{
            maxLength: 50,
            autoComplete: 'on',
          }}
          rules={[{ required: true }, { pattern: pattern.phone, message: '格式错误' }]}
        />
      </ProForm.Group>
      {!orderNo && (
        <ProForm.Group title="GPS供应商">
          <ProFormCheckbox.Group
            name="supplierCode"
            colProps={{ span: 7 }}
            labelCol={{ span: 8 }}
            label="GPS供应商"
            options={[{ label: '中瑞平台', value: 'ZhongRui' }]}
          />
        </ProForm.Group>
      )}
      <ProForm.Group title="备注信息">
        <ProFormTextArea
          label="备注"
          name="remark"
          width="lg"
          labelCol={{ span: 2 }}
          fieldProps={{ rows: 4, maxLength: 500 }}
        />
      </ProForm.Group>
    </ModalForm>
  );
};

export default DisassemblyModal;
