/*
 * @Author: your name
 * @Date: 2021-06-10 15:24:20
 * @LastEditTime: 2021-06-17 15:28:03
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/quota-detail.tsx
 */
import React from 'react';
import ProTable from '@ant-design/pro-table';
import type { QuotaDetailItem } from '../data';
import { getQuotaDetail } from '../service';

interface PropsItem {
  userNo: string;
  subAccountNo: string;
}

const QuotaDetail: React.FC<PropsItem> = (props) => {
  const columns = [
    {
      title: '总额度',
      width: 150,
      dataIndex: 'creditAmount',
      key: 'creditAmount',
    },
    {
      title: '调整额度',
      width: 150,
      dataIndex: 'operatorAmount',
      key: 'operatorAmount',
    },
    {
      title: '操作类型',
      width: 150,
      dataIndex: 'operatorType',
      key: 'operatorType',
      valueEnum: {
        0: { text: '冻结' },
        1: { text: '解冻' },
        2: { text: '提额' },
        3: { text: '降额' },
        4: { text: '开户' },
        40: { text: '风控逾期冻结' },
        41: { text: '风控逾期解冻' },
        50: { text: '注销' },
        70: { text: '提额命黑冻结' },
        71: { text: '提额命黑解冻' },
        72: { text: '提额未命黑冻结' },
      },
    },
    // {
    //   title: '冻结额度',
    //   width: 150,
    //   dataIndex: 'creditAmount',
    //   key: 'operatorAmount',
    // },
    {
      title: '创建时间',
      width: 200,
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '更新时间',
      width: 200,
      dataIndex: 'createTime',
      key: 'createTime',
    },
  ];

  return (
    <ProTable<QuotaDetailItem>
      columns={columns}
      rowKey="createTime"
      search={false}
      scroll={{ y: 300 }}
      toolBarRender={false}
      request={(params) =>
        getQuotaDetail({ ...params, userNo: props.userNo, subAccountNo: props.subAccountNo })
      }
    />
  );
};

export default QuotaDetail;
