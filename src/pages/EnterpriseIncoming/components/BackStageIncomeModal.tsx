/*
 * @Author: your name
 * @Date: 2021-11-22 14:54:40
 * @LastEditTime: 2022-01-25 18:07:55
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseIncoming/components/BackStageIncomeModal.tsx
 */
import React from 'react';
import ProForm, {
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormDigit,
  ProFormDependency,
  ProFormRadio,
} from '@ant-design/pro-form';
import { CLASSIFICATION, SECONDARY_CLASSIFICATION_INCOME } from '@/enums';
import globalStyle from '@/global.less';
import {
  patterNoChineseValidate,
  emailValidate,
  telPhoneValidate,
  letterOrNumber,
  notNumber,
  orgCodeValidate,
} from '@/services/validate';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { createFactory, getRepayDay } from '../service';
import { message, Tooltip } from 'antd';

interface BackStageIncomeModalProps {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible?: boolean;
  onVisibleChange?: any;
}

const BackStageIncomeModal: React.FC<BackStageIncomeModalProps> = ({
  modalVisible,
  onVisibleChange,
  onOk,
}) => {
  const initialForm = {
    authorIdType: 200,
    classification: 'SELLER_FACTORING',
    enableEpAuth: true,
  };
  return (
    <ModalForm
      title="创建进件-单个"
      // width={600}
      className={globalStyle.formModalLabel130}
      // form={form}
      initialValues={initialForm}
      layout="horizontal"
      visible={modalVisible}
      onVisibleChange={onVisibleChange}
      modalProps={{
        centered: true,
        okText: '提交',
        maskClosable: false,
        destroyOnClose: true,
        afterClose: () => {
          // handleShowMoreDetail(false);
          // setNewForm({});
          // setDisableForm(false);
          // setCurrentRow(undefined);
        },
      }}
      onFinish={async (values) => {
        const success: boolean = await createFactory(values);
        if (success) {
          message.success('添加成功');
        }
        onOk();
        // return true;
      }}
    >
      <ProForm.Group>
        <ProFormSelect
          disabled
          options={Object.keys(CLASSIFICATION).map((key) => ({
            value: key,
            label: CLASSIFICATION[key],
          }))}
          placeholder="请选择产品一级分类"
          name="classification"
          width="sm"
          label="产品一级分类"
        />
        <ProFormSelect
          name="productCode"
          options={Object.keys(SECONDARY_CLASSIFICATION_INCOME).map((key) => ({
            value: key,
            label: SECONDARY_CLASSIFICATION_INCOME[key],
          }))}
          width="sm"
          label="产品二级分类"
          rules={[{ required: true }]}
          placeholder="请选择产品二级分类"
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormText
          name="epName"
          width="sm"
          fieldProps={{ maxLength: 100 }}
          label="企业名称"
          placeholder="请输入企业名称"
          rules={[{ required: true }, notNumber]}
        />
        <ProFormText
          name="orgCode"
          width="sm"
          fieldProps={{ maxLength: 100 }}
          label="统一社会信用代码"
          placeholder="请输入统一社会信用代码"
          rules={[{ required: true }, orgCodeValidate]}
        />
      </ProForm.Group>
      <ProForm.Group>
        <ProFormDigit
          width="sm"
          label="申请额度（元）"
          placeholder="请输入申请额度（元）"
          name="applyAmount"
          rules={[{ required: true }]}
        />
        <ProFormText
          name="channelId"
          width="sm"
          fieldProps={{ maxLength: 15 }}
          label="渠道用户ID"
          placeholder="请输入渠道用户ID"
          rules={[{ required: true }, patterNoChineseValidate]}
        />
      </ProForm.Group>

      <ProForm.Group titleStyle={{ gap: 0 }}>
        <ProFormDependency name={['productCode']}>
          {({ productCode }) => {
            return (
              <ProFormText
                name="billRepayDate"
                width="sm"
                fieldProps={{
                  addonBefore: 'D+',
                  suffix: (
                    <>
                      <Tooltip placement="topLeft" title="D指账单周期最后一天">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </>
                  ),
                }}
                label="还款日"
                placeholder="请输入还款日"
                rules={[
                  {
                    required: true,
                  },
                  { pattern: /^[0-9]*[1-9][0-9]*$/, message: '格式不正确' },
                  {
                    validator: async (_, value) => {
                      if (!productCode) {
                        return Promise.reject(new Error('请选择二级产品分类'));
                      }
                      const res = await getRepayDay(`${productCode}01`, 'BILL');
                      if (!res) {
                        return Promise.reject(new Error('还款日获取失败'));
                      }
                      // const res = { data: { billDTO: { latestBillConfirmationDate: 2 } } };
                      if (value && value <= res?.data?.latestBillConfirmationDate) {
                        return Promise.reject(new Error('还款日需大于最晚确认账单日'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              />
            );
          }}
        </ProFormDependency>
        <ProFormRadio.Group
          name="enableEpAuth"
          label="是否需要企业认证"
          rules={[{ required: true }]}
          options={[
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ]}
        />
      </ProForm.Group>

      <ProFormDependency name={['enableEpAuth']}>
        {({ enableEpAuth }) =>
          enableEpAuth === false ? (
            <>
              <ProForm.Group>
                <ProFormText
                  name="authorizedName"
                  width="sm"
                  label="授权人姓名"
                  fieldProps={{ maxLength: 50 }}
                  placeholder="请输入授权人姓名"
                  rules={[{ required: true }, notNumber]}
                />
                <ProFormSelect
                  disabled
                  label="授权人证件类型"
                  width="sm"
                  name="authorIdType"
                  placeholder="请选择授权人证件类型"
                  options={[{ label: '身份证', value: 200 }]}
                />
              </ProForm.Group>
              <ProForm.Group>
                <ProFormText
                  label="授权人证件号"
                  width="sm"
                  fieldProps={{ maxLength: 50 }}
                  placeholder="请输入授权人证件号"
                  name="authorizedIdNo"
                  rules={[letterOrNumber]}
                />
                <ProFormText
                  label="授权人手机号"
                  name="authorizedPhone"
                  width="sm"
                  placeholder="请输入授权人手机号"
                  rules={[{ required: true }, telPhoneValidate]}
                />
              </ProForm.Group>
              <ProForm.Group>
                <ProFormText
                  label="授权人邮箱"
                  width="sm"
                  name="authorEmail"
                  placeholder="请输入授权人邮箱"
                  rules={[emailValidate]}
                />
              </ProForm.Group>
            </>
          ) : null
        }
      </ProFormDependency>
    </ModalForm>
  );
};

export default BackStageIncomeModal;
