import HeaderTab from '@/components/HeaderTab';
import { filterProps } from '@/utils/tools';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable from '@ant-design/pro-table';
import type { FormInstance } from 'antd';
import React, { memo, useRef } from 'react';
import columns from './columns/repaymentList';
import ExportExcel from './components/ExportExcel';
import { exportExcel, getList } from './services';
import type { IlistItem } from './types';

const List = () => {
  const dataRef = useRef({ current: 1, pageSize: 20 });
  const formRef = useRef<FormInstance>();

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<IlistItem>
          rowKey="repayNo"
          formRef={formRef}
          search={{ labelWidth: 100, defaultCollapsed: false }}
          scroll={{ x: 'max-content' }}
          request={async (params) => {
            params.secondaryClassification = 'CAR_INSURANCE';
            params.classification = 'SMALL_LOAN';
            return getList(filterProps(params));
          }}
          pagination={{
            onChange(page, pageSize) {
              dataRef.current.current = page;
              dataRef.current.pageSize = pageSize || 20;
            },
          }}
          toolBarRender={() => {
            return [
              <ExportExcel
                getParams={() => {
                  const values = formRef?.current?.getFieldsValue();
                  const params = {
                    ...dataRef.current, // 分页数据
                    ...values, // form查询数据
                    secondaryClassification: 'CAR_INSURANCE',
                    classification: 'SMALL_LOAN',
                  };
                  return params;
                }}
                exportExcel={exportExcel}
              />,
            ];
          }}
          columns={columns}
        />
      </PageContainer>
    </>
  );
};
export default memo(List);
