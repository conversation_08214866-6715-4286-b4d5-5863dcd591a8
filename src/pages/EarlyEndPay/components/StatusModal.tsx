import { Modal, Steps } from 'antd';
import React from 'react';

export type StatusModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  statusModalVisible: boolean;
  data: [];
};

const StatusModal: React.FC<StatusModalProps> = (props) => {
  const { Step } = Steps;
  const { data } = props;

  return (
    <>
      <Modal
        destroyOnClose
        centered
        title="审核状态"
        open={props.statusModalVisible}
        onOk={async () => {
          props.onOk();
        }}
        onCancel={() => {
          props.onCancel();
        }}
        footer={null}
      >
        <Steps direction="vertical" size="small" progressDot current={data?.length}>
          {data?.map((item) => {
            return (
              <Step
                title={item.remark}
                description={
                  <>
                    <div>{item.rejectReason}</div>
                    <div>{`${item.time || '-'} ${item.operatorBy || '-'}`}</div>
                  </>
                }
              />
            );
          })}
        </Steps>
      </Modal>
    </>
  );
};

export default StatusModal;
