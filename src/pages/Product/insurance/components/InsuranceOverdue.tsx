import { useModel } from '@umijs/max';
import { Col, Form, Input, Row, Select, TimePicker } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';

const Overdue: React.FC<any> = () => {
  const [form] = Form.useForm();
  const { detailData } = useModel('Product.insuranceProduct');
  useEffect(() => {
    const { overdueTime, badDebtDay, overduePenaltyInterest, overdueBillDay } =
      detailData?.overdueDTO || {};
    form.setFieldsValue({
      overdueTime: overdueTime ? dayjs(overdueTime, 'HH:mm:ss') : null,
      badDebtDay,
      overduePenaltyInterest,
      overdueBillDay,
    });
  }, [detailData, form]);
  return (
    <>
      <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 14 }} name={'insuranceOverdue'}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="逾期时间" name="overdueTime">
              <TimePicker style={{ minWidth: '80%' }} placeholder="24:00:00" disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="坏账">
              <span>T+</span>
              <Form.Item name="badDebtDay" noStyle>
                <Input placeholder="90" style={{ width: '70%', marginLeft: '10px' }} disabled />
              </Form.Item>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="逾期罚息最大值" name="overduePenaltyInterest">
              <Select disabled style={{ width: '80%' }} placeholder="无">
                <Select.Option value="NO_MORE_THAN_PRINCIPAL_FIFTY_PERCENTAGE">
                  不超过本金的50%
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="催收单生成日期">
              <span>T+</span>
              <Form.Item name="overdueBillDay" noStyle>
                <Input placeholder="1" style={{ width: '70%', marginLeft: '10px' }} disabled />
              </Form.Item>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default Overdue;
