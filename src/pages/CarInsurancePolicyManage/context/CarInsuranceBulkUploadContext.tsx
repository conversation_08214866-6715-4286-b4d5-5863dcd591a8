/*
 * @Author: alan771.tu
 * @Date: 2024-11-27 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2025-02-13 10:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/context/CarInsuranceDetailContext.tsx
 * @Description: 车险保单批量上传的context，用于存储交换上下文内容
 */

import { history } from '@umijs/max';
import { message } from 'antd';
import { cloneDeep, pick } from 'lodash';
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
  useState,
} from 'react';
import type { CarInsurancePolicyBaseInfoParams } from '../data';
import { PolicyPagePaths, PostPolicyBaseParams } from '../enum';
import { commitCarInsurancePolicy, recordCarInsurancePolicyAutoApprove } from '../services';
import {
  carInsurancePolicyBaseInfoKeys,
  processDuplicateOcrInfo,
  transformFile2OcrStruct,
} from '../utils';
import eventBus, { InsuranceEventType } from '../utils/recordInsuranceEvent';

export enum BulkUploadSteps {
  UPLOAD = 'UPLOAD',
  OCR = 'OCR',
}

export const CarInsuranceBulkUploadContext = createContext<{
  loading: boolean;
  step: BulkUploadSteps;
  state: any;
  dispatch: (action: any) => void;
  updateLoading: (loading: boolean) => void;
  updateSteps: (step: BulkUploadSteps) => void;
  request: (params?: any) => void;
}>({
  loading: false,
  state: {},
  step: BulkUploadSteps.UPLOAD,
  dispatch: () => {},
  updateLoading: () => {},
  updateSteps: () => {},
  request: () => {},
});

export const useCarInsuranceBulkUploadContext = () => {
  return useContext(CarInsuranceBulkUploadContext);
};

// 批量上传的reducer，涉及policyList的增删改查
function reducer(state: any, action: any) {
  // 深拷贝policyInfoList和safePolicyInfoList，避免直接修改state
  const deepClonePolicyInfoList = cloneDeep(state.policyInfoList);
  const deepCloneSafePolicyInfoList = cloneDeep(state.safePolicyInfoList);

  switch (action.type) {
    case 'add_policy':
      const newPolicyInfoList = cloneDeep(action.payload.fileList);
      const formatDupilicateList = processDuplicateOcrInfo(newPolicyInfoList);
      return { ...state, policyInfoList: formatDupilicateList };
    case 'delete_policy':
      const deletePolicyEndNewInfoList = processDuplicateOcrInfo(
        deepClonePolicyInfoList.filter((policy: any) => policy.uid !== action.payload.uid),
      );
      const deletePolicyEndSafeInfoList = processDuplicateOcrInfo(
        deepCloneSafePolicyInfoList.filter((policy: any) => policy.uid !== action.payload.uid),
      );
      return {
        ...state,
        policyInfoList: deletePolicyEndNewInfoList,
        safePolicyInfoList: deletePolicyEndSafeInfoList,
      };
    case 'sync_update_policy':
      const syncUpdatePolicyEndNewInfoList = processDuplicateOcrInfo(
        deepClonePolicyInfoList.map((policy: any) =>
          policy.uid === action.payload.uid ? action.payload.file : policy,
        ),
      );
      const syncUpdatePolicyEndSafeInfoList = processDuplicateOcrInfo(
        deepCloneSafePolicyInfoList.map((policy: any) =>
          policy.uid === action.payload.uid ? action.payload.file : policy,
        ),
      );
      return {
        ...state,
        policyInfoList: syncUpdatePolicyEndNewInfoList,
        safePolicyInfoList: syncUpdatePolicyEndSafeInfoList,
      };
    case 'sync_update_policy_list':
      return {
        ...state,
        policyInfoList: action.payload.policyInfoList,
        safePolicyInfoList: action.payload.policyInfoList,
      };
    case 'safe_aync_policy':
      return { ...state, safePolicyInfoList: state.policyInfoList };
    case 'clear_policy':
      return { ...state, policyInfoList: [], safePolicyInfoList: [] };
    default:
      throw new Error('Invalid action type');
  }
}

export const CarInsuranceBulkUploadProvider = (props: { children: React.ReactNode }) => {
  const { children } = props;
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<BulkUploadSteps>(BulkUploadSteps.UPLOAD);

  const [state, dispatch] = useReducer(reducer, {
    policyInfoList: [],
    safePolicyInfoList: [],
  });

  // 批量提交ocr结果
  const request = useCallback(async () => {
    setLoading(true);
    const requestQueue = state.safePolicyInfoList.map((policy: any) => {
      const pickKeys = carInsurancePolicyBaseInfoKeys<CarInsurancePolicyBaseInfoParams>(
        PostPolicyBaseParams,
      );

      // 解析extendInfo, 严格格式化为后端需要的格式
      const extendInfo = JSON.parse(policy?.ocrInfo?.extendInfo || '{}');
      const strictExtendInfo = {
        ...extendInfo,
        policyFileList: [
          {
            fileName: policy?.name || '',
            ossPath: policy?.response?.data?.netWorkPath || '',
            shortPath: policy?.response?.data?.filePath || '',
          },
        ],
      };
      // 挑选必要字段
      const formatParams = pick(
        Object.assign({}, policy?.ocrInfo, {
          extendInfo: JSON.stringify(strictExtendInfo),
          requestOrderId: policy?.uid,
        }),
        pickKeys,
      );

      // 如果保单校验后满足所有字段都通过，则会经过自动通过的接口提交
      if (policy?.ocrInfo?.passInit) {
        return recordCarInsurancePolicyAutoApprove(formatParams);
      }
      return commitCarInsurancePolicy(formatParams);
    });

    Promise.allSettled(requestQueue)
      .then((results) => {
        // 判断是否所有保单都成功
        const isSuccess = results.every((item) => item.status === 'fulfilled');
        // 如果所有都成功，则回到列表
        if (isSuccess) {
          message.success('操作成功');
          // 返回到保单管理表格
          history.push(PolicyPagePaths.MANAGEMENT);
          return;
        }

        // 处理返回结果，根据requestOrderId匹配对应的保单数据
        const formatToFaildPolicyList = results
          .map((item, index) => {
            const originalPolicy = state.safePolicyInfoList[index];

            if (item.status === 'fulfilled') {
              return {
                ...originalPolicy,
                ocrInfo: {
                  ...(originalPolicy?.ocrInfo || {}),
                  ...item?.value?.data,
                },
              };
            } else {
              return {
                ...originalPolicy,
                ...item,
                ocrInfo: {
                  ...(originalPolicy?.ocrInfo || {}),
                  // 如果是后端异常或网关异常，则将错误信息转换为ocr结构体，如果其他异常则返回默认结构
                  ...(item?.reason?.ret === 500 || item?.reason?.data
                    ? transformFile2OcrStruct(item?.reason?.data)
                    : {
                        ...originalPolicy?.ocrInfo,
                        checkStatus: 'error',
                        checkFlag: false,
                        passInit: false,
                        exceptionMsgList: Array.from(
                          new Set([
                            '请针对异常保单重新检测校验或重新提交审核',
                            ...(originalPolicy?.ocrInfo?.exceptionMsgList || []),
                          ]),
                        ),
                      }),
                },
              };
            }
          })
          .filter((item) => item.status === 'rejected');
        // 将失败保单的保留，成功的从policyInfoList中删除
        dispatch({
          type: 'sync_update_policy_list',
          payload: { policyInfoList: formatToFaildPolicyList },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, [state]);

  const goToOCRCallback = useCallback(() => {
    // 点击下一步时，将当前的policyInfoList同步到safePolicyInfoList
    dispatch({ type: 'safe_aync_policy' });
    setStep(BulkUploadSteps.OCR);
  }, [dispatch]);

  useEffect(() => {
    eventBus.on(InsuranceEventType.CLOSE_UPLOAD, () => {
      history.push(PolicyPagePaths.MANAGEMENT);
    });
    eventBus.on(InsuranceEventType.RETURN_LAST_STEP, () => {
      setStep(BulkUploadSteps.UPLOAD);
    });
    return () => {
      eventBus.off(InsuranceEventType.CLOSE_UPLOAD);
      eventBus.off(InsuranceEventType.RETURN_LAST_STEP);
      eventBus.off(InsuranceEventType.COMMIT_UPLOAD);
    };
  }, []);

  // 额外依赖的事件单独绑定
  useEffect(() => {
    eventBus.on(InsuranceEventType.NEXT_STEP, () => {
      goToOCRCallback();
    });
    return () => {
      eventBus.off(InsuranceEventType.NEXT_STEP);
    };
  }, [goToOCRCallback]);

  useEffect(() => {
    // 提交上传内容
    eventBus.on(InsuranceEventType.COMMIT_UPLOAD, () => {
      request();
    });

    return () => {
      eventBus.off(InsuranceEventType.COMMIT_UPLOAD);
    };
  }, [request]);

  return (
    <CarInsuranceBulkUploadContext.Provider
      value={{
        loading,
        step,
        state,
        dispatch,
        updateSteps: setStep,
        updateLoading: setLoading,
        request,
      }}
    >
      {children}
    </CarInsuranceBulkUploadContext.Provider>
  );
};
