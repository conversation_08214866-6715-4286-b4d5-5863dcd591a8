import { useAccess } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Layout, Menu, Popconfirm, Tooltip } from 'antd';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { getItem } from '../utils/generateSubMenu';
import eventBus, { InsuranceEventType } from '../utils/recordInsuranceEvent';
import './index.less';
import PdfView from './PdfView';

const { Sider, Content } = Layout;
type MenuItem = Required<MenuProps>['items'][number];
interface ReviewPdfContentProps {
  policyData: Record<string, any>;
  isFirstAndSecondChannel: boolean;
}
const ReviewPdfContent: React.FC<ReviewPdfContentProps> = (props) => {
  const { policyData, isFirstAndSecondChannel } = props;
  //
  const access = useAccess();

  const [selectedPdf, setSelectedPdf] = useState('');
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // 获取菜单项额外内容
  const getMenuItemExtra = useCallback(
    (fileList: any[], key: string, eventType: InsuranceEventType, fileIndex: number) => {
      // 批单文件，是否有删除权限
      if (key === 'endorsementFileList') {
        if (!access?.hasAccess('biz_businessMng_policyMng_policy_detial_delete_policy')) {
          return null;
        }
      }
      // 其他文件，是否有删除权限
      if (key === 'otherFileList') {
        if (!access?.hasAccess('biz_businessMng_policyMng_policy_detial_delete_other')) {
          return null;
        }
      }
      return (
        <Popconfirm
          title="确定要删除吗？"
          onConfirm={() => {
            // 删除文件
            const newList = fileList?.filter((item: any, index: number) => index !== fileIndex);
            eventBus.emit(eventType, {
              [key]: newList,
            });
          }}
        >
          <div className="review-pdf-content-menu-item-extra">
            <div role="button">删除</div>
          </div>
        </Popconfirm>
      );
    },
    [],
  );

  const memoizedFiles: MenuItem[] = useMemo(() => {
    if (!policyData) {
      return [];
    }

    return [
      {
        label: '保单文件',
        key: 'policy',
        type: 'group',
        hidden: policyData?.policyFileList?.length === 0,
        children: [
          ...policyData?.policyFileList?.map((item: any) => {
            return getItem(
              <Tooltip title={item?.fileName}>
                <span>{item?.fileName}</span>
              </Tooltip>,
              `${item?.ossPath}`,
            );
          }),
        ],
      },
      {
        label: '批单文件',
        key: 'endorsement',
        type: 'group',
        hidden: policyData?.endorsementFileList?.length === 0,
        children: [
          ...policyData?.endorsementFileList?.map((item: any, index: number) => {
            // 一级/二级渠道不能删除批单
            return getItem(
              <Tooltip title={item?.fileName}>
                <span>{item?.fileName}</span>
              </Tooltip>,
              `${item?.ossPath}`,
              undefined,
              !isFirstAndSecondChannel
                ? getMenuItemExtra(
                    policyData?.endorsementFileList,
                    'endorsementFileList',
                    InsuranceEventType.DELETE_ENDORSEMENT,
                    index,
                  )
                : undefined,
            );
          }),
        ],
      },
      {
        label: '其他资料',
        key: 'other-material',
        type: 'group',
        hidden: policyData?.otherFileList?.length === 0,
        children: [
          ...policyData?.otherFileList?.map((item: any, index: number) => {
            // 一级/二级渠道不能删除其他资料
            return getItem(
              <Tooltip title={item?.fileName}>
                <span>{item?.fileName}</span>
              </Tooltip>,
              `${item?.ossPath}`,
              undefined,
              !isFirstAndSecondChannel
                ? getMenuItemExtra(
                    policyData?.otherFileList,
                    'otherFileList',
                    InsuranceEventType.DELETE_OTHER_MATERIAL,
                    index,
                  )
                : undefined,
            );
          }),
        ],
      },
    ];
  }, [policyData, isFirstAndSecondChannel, getMenuItemExtra]);

  // 初始化默认选中
  useEffect(() => {
    // 保单 -> 批单 -> 其他资料
    const allFiles = [
      ...(policyData?.policyFileList || []),
      ...(policyData?.endorsementFileList || []),
      ...(policyData?.otherFileList || []),
    ];
    if (allFiles?.length > 0) {
      setSelectedKeys([allFiles[0]?.ossPath]);
      setSelectedPdf(allFiles[0]?.ossPath);
    }
  }, [policyData]);

  return (
    <Layout>
      <Sider theme="light" title="文件列表">
        <Menu
          mode="inline"
          forceSubMenuRender
          defaultOpenKeys={['policy', 'endorsement']}
          items={memoizedFiles}
          selectedKeys={selectedKeys}
          onSelect={(values) => {
            const sourcePdf = values.selectedKeys[0];
            setSelectedPdf(sourcePdf);
            setSelectedKeys(values.selectedKeys);
          }}
        />
      </Sider>
      <Content style={{ width: '800px' }}>
        <PdfView url={selectedPdf} />
      </Content>
    </Layout>
  );
};

export default memo(ReviewPdfContent);
