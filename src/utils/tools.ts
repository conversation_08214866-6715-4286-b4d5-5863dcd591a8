/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-03-06 14:14:36
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-07-03 18:18:51
 * @FilePath: /code/lala-finance-biz-web/src/utils/tools.ts
 * @Description: tools
 */
/**
 * 用于接口传参,排除掉一些不需要传给后端的对象属性
 * 比如 "" undefined null 不需要传给后端
 * _prop 排除掉自定义属性
 * 深克隆
 */
export function filterProps<T>(value: T, prop: string[] = []): T {
  function _filter_prop(_value: any, _prop: string[] = []): any {
    if (_value instanceof Function) return _value;
    else if (_value instanceof Array) {
      const new_value = [];
      for (let i = 0; i < _value.length; ++i) new_value[i] = _filter_prop(_value[i]);
      return new_value;
    } else if (_value instanceof Object) {
      const new_value = {};
      for (const i in _value) {
        const res = _filter_prop(_value[i], _prop);
        if (!['', undefined, null].includes(res as any)) {
          if (!_prop.includes(i)) new_value[i] = res;
        }
      }
      return new_value;
    } else return _value;
  }
  return _filter_prop(value, prop);
}

/**
 * 将一个数转位百分比字符串
 * 2.05 * 100 会有精度问题
 * 理论输出 205 采用循环处理2.05, 小数点向后移两位以及字符串拼接的方式
 */
export function toPercentage(num: number): number {
  const num1 = Number(num);
  if (!isNaN(num1)) {
    // 说明他是正常的数字
    let newStrNum = '';
    let index = 0;
    if (Number.isInteger(num1)) {
      // 如果是整形直接乘以%
      // 1 1.0 1.
      return num1 * 100;
    } else {
      // 肯定是小数
      const arrNum = (num1 + '').split('.');
      const endStrNum = arrNum[1].padEnd(2, '0');
      const strNum = arrNum[0] + '.' + endStrNum;
      // 1.1 1.23 1.234
      for (let i = 0; i < strNum.length; i++) {
        let item = strNum[i];
        if (item == '.') {
          index = i;
        } else {
          if (i - index === 2 && index !== 0) {
            if (i < strNum.length - 1) {
              // 最后一个字符不需要拼接item
              item = item + '.';
            }
          }
          newStrNum = newStrNum + item;
        }
      }
    }
    //parseFloat 是为了去除 0050 000.5 去除前面的0
    return parseFloat(newStrNum);
  } else {
    return 0;
  }
}

/**
 *
 * @param num 需要多少个字符 不穿返回全部 ， 超出返回全部
 * @returns
 */
export function getRandomUUID(num?: number) {
  // any 会报ts错误 高版本ts 会支持
  return (crypto as any).randomUUID().replaceAll('-', '').slice(0, num);
}

// label value 转换
export const optionsMap = (mapStatus: any) => {
  return Object.keys(mapStatus).map((item) => {
    return {
      value: item,
      label: mapStatus[item],
    };
  });
};

// valueEnum 转换
export const valueEnumMap = (mapStatus: any) => {
  const obj = Object.assign({}, mapStatus);
  if (!obj) return {};
  Object.keys(mapStatus).forEach((key) => {
    obj[key] = { text: obj[key] };
  });
  return obj;
};

/**
 *
 * @param imgbase64 data:image/jpg,asdasdasdasdasd...
 * @param type blob 的 mime 类型
 * @returns blob 将图片的base转换成 blob对象
 */
export function base64ToBlob(imgbase64: string, type: string = 'image/png'): Blob {
  const base64 = imgbase64.split(',');
  const byteCharacters = atob(base64[1]); //要把 data:image/png;base64, 截掉
  const byteArrays = [];
  for (let i = 0; i < byteCharacters.length; i++) {
    byteArrays.push(byteCharacters.charCodeAt(i));
  }
  // 创建图片的Blob对象
  const blob = new Blob([new Uint8Array(byteArrays)], {
    type,
  });
  return blob;
}

/**
 * 去除一个对象中属性值的前后空格 只遍历一层
 */

export function removeBlankFromObject(obj: any) {
  if (typeof obj === 'object') {
    const newObj: any = {};
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        newObj[key] = obj[key]?.trim?.();
      } else {
        newObj[key] = obj[key];
      }
    }
    return newObj;
  } else {
    return obj;
  }
}

/**
 * 深度遍历一个object,去除字符串value的首尾空格
 * @param obj 参数对象
 */
export function deepTrimParams<T>(value: T): T {
  function innerDeepTrim(innerValue: any): any {
    if (innerValue instanceof Function) {
      return innerValue;
    }
    if (typeof innerValue === 'string') {
      return innerValue.trim();
    }
    if (innerValue instanceof Array) {
      const newValue: any = [];
      for (let i = 0; i < innerValue.length; ++i) {
        newValue[i] = innerDeepTrim(innerValue[i]);
      }
      return newValue;
    }
    if (innerValue instanceof Object) {
      const newValue = {};
      for (const i in innerValue) {
        const res = innerDeepTrim(innerValue[i]);
        if (typeof res === 'string') {
          newValue[i] = res.trim();
        } else {
          newValue[i] = res;
        }
      }
      return newValue;
    } else {
      return innerValue;
    }
  }
  return innerDeepTrim(value);
}

/**
 * 转换获取antd table的order
 *
 * 入参的orderList数组顺序是table的search头显示的顺序（从左到右，是为了让人目视方便），
 * 实际上antd中的order是数值越大则search组件越靠前，所以在后面需要重新reverse一下，
 *
 * 为什么要通过orderList管理order？
 * 1. 如果通过column的order来管理，则需要每次添加新的筛选条件时，都要修改column的顺序，不灵活
 * 2. 通过list来管理，则只需要修改list顺序即可，灵活方便
 *
 * 注意：
 * 1. 如果key不存在column中，则order会失效
 * 2. 如果key存在column中，但是key不一致，则order也会失效
 * @param key 需要排序的key
 * @param orderList 排序的list，内部有reverse，使用时可以按照自己design的顺序编写orderList即可
 * @returns
 */
export function getSortOrder(key: string, orderList: string[]) {
  // 简单去重-浅克隆-反转-查找
  return [...new Set([...orderList])].reverse().indexOf(key);
}
