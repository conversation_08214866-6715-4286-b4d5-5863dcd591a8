/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-26 09:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/detail/index.tsx
 * @Description: index.tsx
 */

import { PageContainer } from '@ant-design/pro-components';
import { history, useAccess, useModel } from '@umijs/max';
import {
  Button,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  message,
  Popconfirm,
  Row,
  Select,
  Spin,
  Table,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import HeaderTab from '@/components/HeaderTab';
import type { ReasonFormModalRef } from '@/pages/CarInsurancePolicyManage/components/ReasonFormModal';
import ReviewPdfContent from '@/pages/CarInsurancePolicyManage/components/ReviewPdfContent';
import type { UploadOcrModalRef } from '@/pages/CarInsurancePolicyManage/components/UploadOcrModal';
import UploadOcrModal from '@/pages/CarInsurancePolicyManage/components/UploadOcrModal';
import { useCarInsuranceDetailContext } from '@/pages/CarInsurancePolicyManage/context/CarInsuranceDetailContext';
import {
  InsuranceSubjectMap,
  PolicyPagePaths,
  PolicyStatusMap,
} from '@/pages/CarInsurancePolicyManage/enum';
import { isFirstAndSecondChannel } from '@/pages/CarInsurancePolicyManage/utils';
import eventBus, {
  InsuranceEventType,
} from '@/pages/CarInsurancePolicyManage/utils/recordInsuranceEvent';
import { isCarInsuranceStoreUser } from '@/utils/utils';
import { UploadDestPathEnum } from '../../components/DraggerOCRUploadForm';
import {
  getCarInsurancePolicyList,
  recordCarInsurancePolicyException,
  recordCarInsurancePolicyManualApprove,
  recordCarInsurancePolicyReject,
  updatePolicy,
} from '../../services';
import './index.less';

const commonItemClassName = 'no-colon';
// 驳回状态
const rejectStatus = [50];
// 保单异常状态
const anormalStatus = [60];
// 拥有保单的状态
const hasPolicyStatus = [20, 30, 40, ...anormalStatus];
// 可以查看保单内容的状态
const canSeePolicyContent = [10, ...hasPolicyStatus, ...rejectStatus];

const DetailPage: React.FC = () => {
  const { loading, request, carInsurancePolicyDetail } = useCarInsuranceDetailContext();
  const carInsuranceActionRef = useRef(null);
  const policyActionRef = useRef(null);
  //
  const access = useAccess();
  //
  // 是否有编辑详情的权限、且为待复核、复核通过、自动通过、保单异常状态
  const hasEditPermission =
    access?.hasAccess('update_biz_businessMng_policyMng_policy_detial') &&
    [20, 30, 40, 60].includes(carInsurancePolicyDetail?.approveStatus);
  // 上传弹窗
  const uploadOrderModalRef = useRef<UploadOcrModalRef>(null);
  const uploadOtherMaterialModalRef = useRef<UploadOcrModalRef>(null);
  const reasonFormModalRef = useRef<ReasonFormModalRef>(null);

  const { initialState } = useModel('@@initialState');
  const { currentUser = {} } = initialState || {};
  const { channelLevel } = currentUser as any;

  const hasFile =
    carInsurancePolicyDetail?.policyFileList?.length > 0 ||
    carInsurancePolicyDetail?.endorsementFileList?.length > 0 ||
    carInsurancePolicyDetail?.otherFileList?.length > 0;

  // 复核意见
  const [reviewOpinion, setReviewOpinion] = useState('');
  // 需要附带

  // 上传其他资料
  const uploadOtherMaterial = async (values: any) => {
    const { carInsuranceOtherFiles } = values;

    if (
      Array.isArray(carInsuranceOtherFiles) &&
      carInsuranceOtherFiles.some((file) => file.status === 'uploading' || file.status === 'error')
    ) {
      message.error('文件存在异常，请检查后重新提交');
      return false;
    }

    const params = {
      otherFileList: [
        ...(carInsurancePolicyDetail?.otherFileList || []),
        ...carInsuranceOtherFiles.map((file) => ({
          ossPath: file.response.data.netWorkPath,
          shortPath: file.response.data.filePath,
          fileName: file.name,
        })),
      ],
    };
    // 触发重新上传其他资料事件
    eventBus.emit(InsuranceEventType.UPLOAD_OTHER_MATERIAL, params);
    // 隐藏弹窗
    uploadOtherMaterialModalRef.current?.hide();
    return false;
  };

  // 上传批单
  const uploadEndorsement = async (values: any) => {
    const { carInsuranceEndorsementFiles } = values;

    if (
      Array.isArray(carInsuranceEndorsementFiles) &&
      carInsuranceEndorsementFiles.some(
        (file) => file.status === 'uploading' || file.status === 'error',
      )
    ) {
      message.error('文件存在异常，请检查后重新提交');
      return false;
    }

    const params = {
      endorsementFileList: [
        ...(carInsurancePolicyDetail?.endorsementFileList || []),
        ...carInsuranceEndorsementFiles.map((file) => ({
          ossPath: file.response.data.netWorkPath,
          shortPath: file.response.data.filePath,
          fileName: file.name,
        })),
      ],
    };
    // 触发重新上传批单事件
    eventBus.emit(InsuranceEventType.RE_UPLOAD_ENDORSEMENT, params);
    // 隐藏弹窗
    uploadOrderModalRef.current?.hide();
    return false;
  };

  // 驳回重新上传弹窗
  // const rejectReloadHandler = () => {
  //   reasonFormModalRef.current?.setFormInfo({
  //     title: '驳回原因',
  //     trigger: <Button type="primary">确认</Button>,
  //     name: 'rejectReason',
  //     placeholder: '驳回原因',
  //     okText: '确认驳回',
  //     rules: [{ required: true, message: '请输入驳回原因' }],
  //   });
  //   reasonFormModalRef.current?.show();
  // };

  // 标记为保单异常弹窗
  // const recordPolicyToAnormal = () => {
  //   reasonFormModalRef.current?.setFormInfo({
  //     title: '异常原因',
  //     trigger: <Button type="primary">确认</Button>,
  //     name: 'anormalReason',
  //     placeholder: '异常原因',
  //     okText: '确认标记为异常',
  //     rules: [{ required: true, message: '请输入异常原因' }],
  //   });
  //   reasonFormModalRef.current?.show();
  // };
  // 标记异常、驳回重新上传、复核通过action之后，自动抽取下一笔跳转
  const handleReviewActionFinish = async () => {
    // 获取待复核状态列表取第一笔
    const res: any =
      (await getCarInsurancePolicyList({
        current: 1,
        pageSize: 20,
        // 待复核状态
        approveStatus: '20',
        // 最近一年
        lendingTimeStart: dayjs().subtract(1, 'year').format('YYYY-MM-DD 00:00:00'),
        lendingTimeEnd: dayjs().format('YYYY-MM-DD 23:59:59'),
      }).catch(() => {})) || [];
    console.log('handleReviewActionFinish', res);
    if (res?.data) {
      if (res?.data?.length && res?.data?.length > 0) {
        // 跳转到待复核状态列表第一笔
        const nextId = res?.data[0]?.id;
        window.location.href = `/businessMng/car-insurance-policy/detail?id=${nextId}`;
      } else {
        // 跳转到保单列表
        history.replace(`/businessMng/car-insurance-policy/manage`);
      }
    } else {
      // 获取失败刷新当前单详情
      message.error('获取待复核状态列表失败，请稍后再试');
      request();
    }
  };
  // 标记异常、驳回重新上传、复核通过action
  const [reviewActionLoading, setReviewActionLoading] = useState(false);
  const reviewAction = async (type: 'reject' | 'anormal' | 'approve') => {
    if (carInsurancePolicyDetail?.approveStatus !== 20) {
      return;
    }
    setReviewActionLoading(true);
    try {
      if (type === 'approve') {
        await recordCarInsurancePolicyManualApprove({
          id: carInsurancePolicyDetail?.id,
          reason: reviewOpinion,
        });
      } else if (type === 'reject') {
        if (!reviewOpinion) {
          message.error('请输入复核备注');
          return false;
        }
        await recordCarInsurancePolicyReject({
          id: carInsurancePolicyDetail?.id,
          reason: reviewOpinion,
        });
      } else if (type === 'anormal') {
        if (!reviewOpinion) {
          message.error('请输入复核备注');
          return false;
        }
        await recordCarInsurancePolicyException({
          id: carInsurancePolicyDetail?.id,
          reason: reviewOpinion,
        });
      }
      message.success('操作成功');
      setReviewActionLoading(false);
      handleReviewActionFinish();
      // 请求成功后，重置表单
    } catch (error) {
      message.error('操作失败，请稍后再试');
      setReviewActionLoading(false);
    } finally {
      setReviewActionLoading(false);
    }
    setReviewActionLoading(false);
  };

  // 驳回/异常原因弹窗确认
  // const reasonFormModalFinish = async (values: any) => {
  //   try {
  //     if (values?.rejectReason) {
  //       eventBus.emit(InsuranceEventType.REJECT, values.rejectReason); // 触发驳回事件
  //     }
  //     if (values?.anormalReason) {
  //       eventBus.emit(InsuranceEventType.MARK_ABNORMAL, values.anormalReason); // 触发异常事件
  //     }
  //     // 请求成功后，重置表单
  //     reasonFormModalRef.current?.getForm()?.resetFields();
  //     return true;
  //   } catch (error) {
  //     message.error('操作失败，请稍后再试');
  //     return false;
  //   } finally {
  //     reasonFormModalRef.current?.updateLoading(false);
  //   }
  // };

  // 编辑详情
  // 编辑表单
  const [editForm] = Form.useForm();
  // 表单初始化
  useEffect(() => {
    if (hasEditPermission) {
      // 回显可编辑字段
      console.log('carInsurancePolicyDetail', carInsurancePolicyDetail);
      //
      const commercialInsuranceStartDate = carInsurancePolicyDetail?.commercialInsuranceStartDate
        ? dayjs(carInsurancePolicyDetail?.commercialInsuranceStartDate)
        : null;
      const commercialInsuranceEndDate = carInsurancePolicyDetail?.commercialInsuranceEndDate
        ? dayjs(carInsurancePolicyDetail?.commercialInsuranceEndDate)
        : null;
      //
      editForm?.setFieldsValue({
        plateNo: carInsurancePolicyDetail?.plateNo,
        policyNo: carInsurancePolicyDetail?.policyNo,
        policyCommercialInsurance: carInsurancePolicyDetail?.policyCommercialInsurance,
        commercialInsuranceStartDate,
        commercialInsuranceEndDate,
        factoryPlateModel: carInsurancePolicyDetail?.factoryPlateModel,
        policyHolder: carInsurancePolicyDetail?.policyHolder,
      });
    }
  }, [hasEditPermission, carInsurancePolicyDetail]);
  // 编辑表单提交
  const [editSubmitLoading, setEditSubmitLoading] = useState(false);
  const handleEditSubmit = async () => {
    try {
      await editForm?.validateFields();
    } catch {
      message.error('存在部分数据不符合规范，无法保存修改');
      return Promise.reject();
    }
    //
    const formValues = editForm?.getFieldsValue();
    // 组装参数
    const data: any = {
      id: carInsurancePolicyDetail?.id,
      plateNo: formValues?.plateNo,
      policyNo: formValues?.policyNo,
      policyHolder: formValues?.policyHolder,
      commercialInsuranceStartDate: formValues?.commercialInsuranceStartDate?.format(
        'YYYY-MM-DD HH:mm:ss',
      ),
      commercialInsuranceEndDate: formValues?.commercialInsuranceEndDate?.format(
        'YYYY-MM-DD HH:mm:ss',
      ),
      factoryPlateModel: formValues?.factoryPlateModel,
      policyCommercialInsurance: formValues?.policyCommercialInsurance,
    };
    //
    setEditSubmitLoading(true);
    const res = (await updatePolicy(data).catch(() => {})) || {};
    setEditSubmitLoading(false);
    if (res?.ret === 0) {
      message.success('操作成功');
      request();
    }
  };

  // 根据不同的状态显示不同按钮组合
  function getStatusButtons(baseState: string) {
    const uploadOtherMaterialButton = access?.hasAccess(
      'upload_other_biz_businessMng_policyMng_policy_detial',
    ) ? (
      <Button
        key="approve1"
        type="primary"
        onClick={() => {
          uploadOtherMaterialModalRef.current?.show();
        }}
      >
        上传其他资料
      </Button>
    ) : null;
    const formEditButton = hasEditPermission ? (
      <Button
        key="edit"
        type="primary"
        loading={editSubmitLoading}
        onClick={() => {
          handleEditSubmit().catch(() => {
            setEditSubmitLoading(false);
          });
        }}
      >
        保存数据修改
      </Button>
    ) : null;
    switch (String(baseState)) {
      // 暂无保单、复核驳回状态 显示
      case '10':
      case '50':
        return [uploadOtherMaterialButton];
      // 保单待复核状态
      case '20':
        return [
          uploadOtherMaterialButton,
          // 非一级渠道，显示驳回重新上传、标记为保单异常、复核通过按钮
          !isFirstAndSecondChannel(channelLevel) && (
            <>
              {access?.hasAccess('biz_businessMng_policyMng_policy_detial_reject') && (
                <Button
                  key="reject"
                  type="primary"
                  danger
                  loading={reviewActionLoading}
                  onClick={() => {
                    reviewAction('reject');
                  }}
                >
                  驳回重新上传
                </Button>
              )}
              {access?.hasAccess('biz_businessMng_policyMng_policy_detial_exception') && (
                <Button
                  key="exception"
                  type="primary"
                  style={{ background: '#fa8c16' }}
                  loading={reviewActionLoading}
                  onClick={() => {
                    reviewAction('anormal');
                  }}
                >
                  标记为保单异常
                </Button>
              )}
              {access?.hasAccess('biz_businessMng_policyMng_policy_detial_approve') && (
                <Popconfirm
                  title="确定要复核通过吗？"
                  okText="确定"
                  cancelText="取消"
                  placement="bottomLeft"
                  okButtonProps={{ loading: reviewActionLoading }}
                  onConfirm={() => {
                    // eventBus.emit(InsuranceEventType.APPROVE);
                    reviewAction('approve');
                  }}
                >
                  <Button key="approve" type="primary" loading={reviewActionLoading}>
                    复核通过
                  </Button>
                </Popconfirm>
              )}
            </>
          ),
          formEditButton,
        ];
      // 保单异常状态、复核通过、自动通过状态显示
      case '30':
      case '40':
      case '60':
        return [
          uploadOtherMaterialButton,
          access?.hasAccess('upload_policy_biz_businessMng_policyMng_policy_detial') && (
            <Button
              key="approve"
              type="primary"
              onClick={() => {
                uploadOrderModalRef.current?.show();
              }}
            >
              上传批单
            </Button>
          ),
          formEditButton,
        ];
      default:
        return [];
    }
  }

  // 操作日志相关
  const operationLogColumns = [
    {
      title: '操作',
      dataIndex: 'eventDesc',
      key: 'eventDesc',
    },
    {
      title: '操作账号',
      dataIndex: 'operatorName',
      key: 'operatorName',
    },
    {
      title: '操作时间',
      dataIndex: 'time',
      key: 'time',
    },
  ];
  // 是否展示【弱校验】信息
  // 状态为待审核且非渠道用户
  const showCheckInfo =
    carInsurancePolicyDetail?.approveStatus === 20 && !isCarInsuranceStoreUser(access);
  // 监听表单值
  const plateNoValue = Form.useWatch('plateNo', editForm);
  const orderNoValue = Form.useWatch('orderNo', editForm);
  const policyNoValue = Form.useWatch('policyNo', editForm);
  const policyCommercialInsuranceValue = Form.useWatch('policyCommercialInsurance', editForm);
  const commercialInsuranceStartDateValue = Form.useWatch('commercialInsuranceStartDate', editForm);
  const commercialInsuranceEndDateValue = Form.useWatch('commercialInsuranceEndDate', editForm);
  const factoryPlateModelValue = Form.useWatch('factoryPlateModel', editForm);
  const policyHolderValue = Form.useWatch('policyHolder', editForm);
  // 弱校验信息
  const checkInfo = useMemo(() => {
    return carInsurancePolicyDetail?.policyRuleInfoMap || {};
  }, [carInsurancePolicyDetail]);
  // 处理弱校验信息
  const getCheckInfoByName = (keyName: string) => {
    if (!showCheckInfo || !keyName) {
      return null;
    }
    //
    if (checkInfo[keyName] === undefined || checkInfo[keyName] === null) {
      return null;
    }
    if (checkInfo[keyName]?.ruleInfo === undefined || checkInfo[keyName]?.ruleInfo === null) {
      return null;
    }
    if (checkInfo[keyName]?.deleteFlag === 1) {
      return null;
    }
    // 弱校验字段，有被修改过的字段，不展示弱校验信息
    const referenceValue = carInsurancePolicyDetail[keyName];
    const editFormValue = editForm?.getFieldsValue() || {};
    let currentValue = {
      plateNo: plateNoValue,
      orderNo: orderNoValue,
      policyNo: policyNoValue,
      policyCommercialInsurance: policyCommercialInsuranceValue,
      commercialInsuranceStartDate: commercialInsuranceStartDateValue,
      commercialInsuranceEndDate: commercialInsuranceEndDateValue,
      factoryPlateModel: factoryPlateModelValue,
      policyHolder: policyHolderValue,
    }[keyName];
    if (['commercialInsuranceStartDate', 'commercialInsuranceEndDate'].includes(keyName)) {
      currentValue = currentValue?.format('YYYY-MM-DD HH:mm:ss');
    }
    // 被修改过的字段，不展示弱校验信息
    if (currentValue !== referenceValue && keyName !== 'orderNo') {
      return null;
    }
    return (
      <>
        <div className="validate-tip">{checkInfo[keyName]?.ruleInfo}</div>
      </>
    );
  };

  // 订阅事件
  useEffect(() => {
    // 驳回
    eventBus.on(InsuranceEventType.REJECT, (reason) => {
      request('reject', { rejectMsg: reason });
    });
    // 标记为保单异常
    eventBus.on(InsuranceEventType.MARK_ABNORMAL, (reason) => {
      request('markAbnormal', { abnormalMsg: reason });
    });
    // 复核通过
    eventBus.on(InsuranceEventType.APPROVE, () => {
      request('approve');
    });
    // 重新上传其他资料
    eventBus.on(InsuranceEventType.UPLOAD_OTHER_MATERIAL, (extendInfo) => {
      request('uploadOtherMaterial', { extendInfo });
    });
    // 重新上传批单
    eventBus.on(InsuranceEventType.RE_UPLOAD_ENDORSEMENT, (extendInfo) => {
      request('reUploadEndorsement', { extendInfo });
    });
    // 删除批单
    eventBus.on(InsuranceEventType.DELETE_ENDORSEMENT, (extendInfo) => {
      request('deleteEndorsement', { extendInfo });
    });
    // 删除其他资料
    eventBus.on(InsuranceEventType.DELETE_OTHER_MATERIAL, (extendInfo) => {
      request('deleteOtherMaterial', { extendInfo });
    });
    // 回到列表页
    eventBus.on(InsuranceEventType.GO_TO_MANAGEMENT, () => {
      history.push(PolicyPagePaths.MANAGEMENT);
    });

    return () => {
      eventBus.off(InsuranceEventType.REJECT);
      eventBus.off(InsuranceEventType.MARK_ABNORMAL);
      eventBus.off(InsuranceEventType.APPROVE);
      eventBus.off(InsuranceEventType.RE_UPLOAD_ENDORSEMENT);
      eventBus.off(InsuranceEventType.UPLOAD_OTHER_MATERIAL);
      eventBus.off(InsuranceEventType.DELETE_ENDORSEMENT);
      eventBus.off(InsuranceEventType.GO_TO_MANAGEMENT);
    };
  }, [request]);

  useEffect(() => {
    // 滚动到顶部
    window.scrollTo({ top: 0 });
  }, []);

  return (
    <Spin spinning={loading}>
      <>
        <HeaderTab />
        <PageContainer
          fixedHeader
          title="保单详情"
          tags={
            carInsurancePolicyDetail?.approveStatus ? (
              <div className="ant-page-header-heading-title">
                （
                {
                  PolicyStatusMap[
                    carInsurancePolicyDetail?.approveStatus as keyof typeof PolicyStatusMap
                  ]
                }
                ）
              </div>
            ) : undefined
          }
          extra={[
            <Button
              key="close"
              type="primary"
              danger
              onClick={() => {
                eventBus.emit(InsuranceEventType.GO_TO_MANAGEMENT);
              }}
            >
              关闭
            </Button>,
            ...getStatusButtons(carInsurancePolicyDetail?.approveStatus),
          ]}
        >
          <Card>
            {/* 待审核：审核意见-输入 */}
            {carInsurancePolicyDetail?.approveStatus === 20 &&
              access?.hasAccess('remark_biz_businessMng_policyMng_policy_detial') && (
                <>
                  <Input.TextArea
                    placeholder="请在此填写复核备注"
                    value={reviewOpinion}
                    onChange={(e) => setReviewOpinion(e.target.value)}
                    rows={3}
                  />
                  <Divider />
                </>
              )}
            {/* 复核通过、复核驳回、保单异常：审核意见-查看 */}
            {[30, 50, 60].includes(carInsurancePolicyDetail?.approveStatus) && (
              <>
                <div>
                  <p>审核意见：{carInsurancePolicyDetail?.reason || '-'}</p>
                </div>
                <Divider />
              </>
            )}
            {/*  */}
            <Form form={editForm} layout="vertical">
              <Row gutter={6}>
                {/* 暂无保单时显示的详情信息 */}
                {/* 车牌号/新车合格证号-查看 */}
                {!hasEditPermission && (
                  <Col span={6} className="desc-item">
                    <div className="data-index">车牌号/新车合格证号:</div>
                    <div className="data">{carInsurancePolicyDetail?.plateNo || '-'}</div>
                    {/* 弱校验 */}
                    {getCheckInfoByName('plateNo')}
                  </Col>
                )}
                {/*  */}
                {/* 车牌号/新车合格证号-编辑 */}
                {hasEditPermission && (
                  <Col span={6} className="desc-item">
                    <Form.Item
                      label="车牌号/新车合格证号"
                      style={{ width: '100%' }}
                      wrapperCol={{ span: 20 }}
                      name="plateNo"
                      extra={getCheckInfoByName('plateNo')}
                      rules={[
                        { required: true, message: '必填' },
                        { max: 20, message: '限20个字符以内' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                )}
                <Col span={6} className="desc-item">
                  <div className="data-index">车架号:</div>
                  <div className="data">{carInsurancePolicyDetail?.vin || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">订单号:</div>
                  <div className="data">{carInsurancePolicyDetail?.orderNo || '-'}</div>
                  {/* 弱校验 */}
                  {getCheckInfoByName('orderNo')}
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">放款时间:</div>
                  <div className="data">{carInsurancePolicyDetail?.lendingTime || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">渠道名称:</div>
                  <div className="data">{carInsurancePolicyDetail?.channelName || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">客户名称:</div>
                  <div className="data">{carInsurancePolicyDetail?.userName || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">客户身份证/信用代码:</div>
                  <div className="data">{carInsurancePolicyDetail?.idNo || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">订单商业险金额:</div>
                  <div className="data">
                    {carInsurancePolicyDetail?.orderCommercialInsurance || '-'}
                  </div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">投保主体:</div>
                  <div className="data">
                    {InsuranceSubjectMap[carInsurancePolicyDetail?.insuranceSubject] || '-'}
                  </div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">保司名称:</div>
                  <div className="data">{carInsurancePolicyDetail?.insuranceCompany || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">保单剩余价值:</div>
                  <div className="data">
                    {carInsurancePolicyDetail?.remainingValueOfPolicy || '-'}
                  </div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">保单上传逾期天数:</div>
                  <div className="data">
                    {carInsurancePolicyDetail?.uploadOverdueDays === null ||
                    carInsurancePolicyDetail?.uploadOverdueDays === undefined
                      ? '-'
                      : carInsurancePolicyDetail?.uploadOverdueDays}
                  </div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">联系电话:</div>
                  <div className="data">{carInsurancePolicyDetail?.borrowerPhone || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">行驶证车主:</div>
                  <div className="data">{carInsurancePolicyDetail?.licenseOwner || '-'}</div>
                </Col>
                <Col span={6} className="desc-item">
                  <div className="data-index">被保险人:</div>
                  <div className="data">{carInsurancePolicyDetail?.assuredName || '-'}</div>
                </Col>
              </Row>
              {/* 驳回原因 */}
              {/* {rejectStatus.includes(carInsurancePolicyDetail?.approveStatus) && (
                // <ProDescriptions
                //   column={4}
                //   style={{
                //     marginTop: '20px',
                //   }}
                //   layout="vertical"
                // >
                //   <ProDescriptions.Item label="驳回原因" className={commonItemClassName}>
                //     <div style={{ color: 'red' }}>{carInsurancePolicyDetail?.rejectMsg}</div>
                //   </ProDescriptions.Item>
                // </ProDescriptions>
                <Row gutter={6}>
                  <Col span={6} className="desc-item">
                    <div className="data-index">驳回原因:</div>
                    <div className="data red-text">{carInsurancePolicyDetail?.rejectMsg}</div>
                  </Col>
                </Row>
              )} */}

              {/* 保单详情信息-查看 */}
              {hasPolicyStatus.includes(carInsurancePolicyDetail?.approveStatus) &&
                !hasEditPermission && (
                  <>
                    <Divider />
                    {/* <ProDescriptions
                    actionRef={policyActionRef}
                    column={4}
                    layout="vertical"
                    dataSource={carInsurancePolicyDetail}
                  >
                    <ProDescriptions.Item
                      label="保单号"
                      className={commonItemClassName}
                      dataIndex={['policyNo']}
                    />
                    <ProDescriptions.Item
                      label="保单商业险金额"
                      className={commonItemClassName}
                      dataIndex={['policyCommercialInsurance']}
                    />
                    <ProDescriptions.Item
                      label="商业险实际开始时间"
                      className={commonItemClassName}
                      dataIndex={['commercialInsuranceStartDate']}
                      valueType="date"
                      render={(_, record) => {
                        return record?.commercialInsuranceStartDate
                          ? dayjs(record?.commercialInsuranceStartDate).format('YYYY-MM-DD HH:mm:ss')
                          : '';
                      }}
                    />
                    <ProDescriptions.Item
                      label="商业险实际结束时间"
                      dataIndex={['commercialInsuranceEndDate']}
                      valueType="date"
                      className={commonItemClassName}
                      render={(_, record) => {
                        return record?.commercialInsuranceEndDate
                          ? dayjs(record?.commercialInsuranceEndDate).format('YYYY-MM-DD HH:mm:ss')
                          : '';
                      }}
                    />

                    <ProDescriptions.Item
                      label="厂牌型号"
                      className={commonItemClassName}
                      dataIndex={['factoryPlateModel']}
                    />
                    <ProDescriptions.Item
                      label="投保人"
                      className={commonItemClassName}
                      dataIndex={['policyHolder']}
                    />

                    {anormalStatus.includes(carInsurancePolicyDetail?.approveStatus) && (
                      <ProDescriptions.Item label="异常原因" className={commonItemClassName}>
                        <div style={{ color: 'red' }}>{carInsurancePolicyDetail?.abnormalMsg}</div>
                      </ProDescriptions.Item>
                    )}
                  </ProDescriptions> */}
                    {/* 改成Row */}
                    <Row gutter={6}>
                      <Col span={6} className="desc-item">
                        <div className="data-index">保单号:</div>
                        <div className="data">{carInsurancePolicyDetail?.policyNo || '-'}</div>
                      </Col>
                      <Col span={6} className="desc-item">
                        <div className="data-index">保单商业险金额:</div>
                        <div className="data">
                          {carInsurancePolicyDetail?.policyCommercialInsurance || '-'}
                        </div>
                        {/* 弱校验 */}
                        {getCheckInfoByName('policyCommercialInsurance')}
                      </Col>
                      <Col span={6} className="desc-item">
                        <div className="data-index">商业险实际开始时间:</div>
                        <div className="data">
                          {carInsurancePolicyDetail?.commercialInsuranceStartDate || '-'}
                        </div>
                        {/* 弱校验 */}
                        {getCheckInfoByName('commercialInsuranceStartDate')}
                      </Col>
                      <Col span={6} className="desc-item">
                        <div className="data-index">商业险实际结束时间:</div>
                        <div className="data">
                          {carInsurancePolicyDetail?.commercialInsuranceEndDate || '-'}
                        </div>
                        {/* 弱校验 */}
                        {getCheckInfoByName('commercialInsuranceEndDate')}
                      </Col>
                      <Col span={6} className="desc-item">
                        <div className="data-index">厂牌型号:</div>
                        <div className="data">
                          {carInsurancePolicyDetail?.factoryPlateModel || '-'}
                        </div>
                      </Col>
                      <Col span={6} className="desc-item">
                        <div className="data-index">投保人:</div>
                        <div className="data">{carInsurancePolicyDetail?.policyHolder || '-'}</div>
                      </Col>
                      {/* {anormalStatus.includes(carInsurancePolicyDetail?.approveStatus) && (
                        <Col span={6} className="desc-item">
                          <div className="data-index">异常原因:</div>
                          <div className="data red-text">
                            {carInsurancePolicyDetail?.abnormalMsg || '-'}
                          </div>
                        </Col>
                      )} */}
                    </Row>
                  </>
                )}
              {/* 保单详情信息-编辑 */}
              {hasPolicyStatus.includes(carInsurancePolicyDetail?.approveStatus) &&
                hasEditPermission && (
                  <>
                    <Divider />
                    <Row gutter={6}>
                      {/* 保单号 */}
                      <Col span={6}>
                        <Form.Item
                          label="保单号"
                          wrapperCol={{ span: 20 }}
                          name="policyNo"
                          // 50个字符以内
                          rules={[
                            { required: true, message: '必填' },
                            { max: 50, message: '限50个字符以内' },
                          ]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                      {/* 保单商业险金额 */}
                      <Col span={6}>
                        <Form.Item
                          label="保单商业险金额"
                          wrapperCol={{ span: 20 }}
                          name="policyCommercialInsurance"
                          // 弱校验
                          extra={getCheckInfoByName('policyCommercialInsurance')}
                          rules={[
                            { required: true, message: '必填' },
                            {
                              //最多两位小数,不能填写除小数点和数字以外的字符
                              validator: (_, val) => {
                                if (isNaN(val) || val?.includes('e')) {
                                  return Promise.reject('请输入数字');
                                }
                                if (val !== '' && val > 100000) {
                                  return Promise.reject(
                                    '需要为100000以内，支持到小数点后两位的数字',
                                  );
                                }
                                if (val.includes('.') && val.toString().split('.')[1].length > 2) {
                                  return Promise.reject('最多两位小数');
                                }
                                return Promise.resolve();
                              },
                            },
                          ]}
                        >
                          <Input type="number" />
                        </Form.Item>
                      </Col>
                      {/* 商业险实际开始时间 */}
                      <Col span={6}>
                        <Form.Item
                          label="商业险实际开始时间"
                          // 弱校验
                          extra={getCheckInfoByName('commercialInsuranceStartDate')}
                          wrapperCol={{ span: 20 }}
                          name="commercialInsuranceStartDate"
                          rules={[{ required: true, message: '必填' }]}
                        >
                          <DatePicker style={{ width: '100%' }} showTime />
                        </Form.Item>
                      </Col>
                      {/* 商业险实际结束时间 */}
                      <Col span={6}>
                        <Form.Item
                          label="商业险实际结束时间"
                          // 弱校验
                          extra={getCheckInfoByName('commercialInsuranceEndDate')}
                          wrapperCol={{ span: 20 }}
                          name="commercialInsuranceEndDate"
                          dependencies={['commercialInsuranceStartDate']}
                          // 大于商业险实际开始时间
                          rules={[
                            { required: true, message: '必填' },
                            ({ getFieldValue }) => ({
                              validator(_, value) {
                                if (
                                  value &&
                                  getFieldValue('commercialInsuranceStartDate') &&
                                  value <= getFieldValue('commercialInsuranceStartDate')
                                ) {
                                  return Promise.reject(new Error('需大于商业险实际开始时间'));
                                }
                                return Promise.resolve();
                              },
                            }),
                          ]}
                        >
                          <DatePicker style={{ width: '100%' }} showTime />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={6}>
                      {/* 厂牌型号 */}
                      <Col span={6}>
                        <Form.Item
                          label="厂牌型号"
                          wrapperCol={{ span: 20 }}
                          name="factoryPlateModel"
                          rules={[
                            // { required: true, message: '必填' },
                            { max: 50, message: '限50个字符以内' },
                          ]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                      {/* 投保人 */}
                      <Col span={6}>
                        <Form.Item
                          label="投保人"
                          wrapperCol={{ span: 20 }}
                          name="policyHolder"
                          rules={[{ required: true, message: '必填' }]}
                        >
                          <Select
                            options={[
                              {
                                label: '易立信区块链科技(广州)有限公司',
                                value: '易立信区块链科技(广州)有限公司',
                              },
                              {
                                label: '广州货满满汽车咨询有限公司',
                                value: '广州货满满汽车咨询有限公司',
                              },
                              { label: '其他', value: '其他' },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                      {/* {anormalStatus.includes(carInsurancePolicyDetail?.approveStatus) && (
                        <Col span={6} className="desc-item">
                          <div className="data-index">异常原因:</div>
                          <div className="data red-text">
                            {carInsurancePolicyDetail?.abnormalMsg || '-'}
                          </div>
                        </Col>
                      )} */}
                    </Row>
                  </>
                )}
              {/* 拥有其他材料或者上传过保单时展示保单/批单/其他材料内容 */}
              {canSeePolicyContent.includes(carInsurancePolicyDetail?.approveStatus) && hasFile && (
                <>
                  <Divider />
                  <ReviewPdfContent
                    isFirstAndSecondChannel={isFirstAndSecondChannel(channelLevel)}
                    policyData={carInsurancePolicyDetail}
                  />
                </>
              )}
              {/* 操作记录 */}
              <Table
                style={{ marginTop: '20px' }}
                title={() => <span className="log-title">操作记录</span>}
                columns={operationLogColumns}
                dataSource={carInsurancePolicyDetail?.policyStatusLogList || []}
                //
                pagination={false}
              />
            </Form>
          </Card>
        </PageContainer>
        {/* 上传批单弹窗 */}
        <UploadOcrModal
          visibleRef={uploadOrderModalRef}
          onFinish={uploadEndorsement}
          title="上传批单"
          draggerProps={{
            isNeedLimit: true,
            destPath: UploadDestPathEnum.POLICY_EXTEND_FILE,
            name: 'carInsuranceEndorsementFiles',
            title: '请将批单拖拽到此处',
            description: '或点击上传',
            buttonText: '上传批单',
            rules: [{ required: true, message: '请上传批单!' }],
          }}
        />
        {/* 上传其他资料弹窗 */}
        <UploadOcrModal
          visibleRef={uploadOtherMaterialModalRef}
          onFinish={uploadOtherMaterial}
          title="上传其他资料"
          draggerProps={{
            isNeedLimit: true,
            destPath: UploadDestPathEnum.POLICY_EXTEND_FILE,
            name: 'carInsuranceOtherFiles',
            title: '请将其他资料拖拽到此处',
            description: '或点击上传',
            buttonText: '上传其他资料',
            rules: [{ required: true, message: '请上传其他资料!' }],
          }}
        />
        {/* <ReasonFormModal ref={reasonFormModalRef} onFinish={reasonFormModalFinish} /> */}
      </>
    </Spin>
  );
};

export default DetailPage;
