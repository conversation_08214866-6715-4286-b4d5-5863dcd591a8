import { message, Space, Tag } from 'antd';
// import { QuestionCircleOutlined } from '@ant-design/icons';
import { Access, history, useAccess, useModel } from '@umijs/max';
import React, { useEffect } from 'react';
// import Caller, { CallerHandleMsgArgs } from '@/components/caller';
import { useIsMountedRef } from '@/hooks/useIsMountedRef';
import { getPid, getSSOVanTaskID, getToken } from '@/utils/auth';
import type { CallerHandleMsgArgs } from '@hll/bme-caller-react';
import Caller from '@hll/bme-caller-react';
import Avatar from './AvatarDropdown';
// import HeaderSearch from '../HeaderSearch';
import { getVanEvn, isExternalNetwork, isStandbyLogin } from '@/utils/utils';
import { addListener, launch } from 'devtools-detector';
import DevVersionMng from '../DevVersionMng';
import AuditNoticeList from './AuditNoticeList';
import styles from './index.less';
export type SiderTheme = 'light' | 'dark';

const ENVTagColor = {
  dev: 'orange',
  test: 'green',
  pre: '#87d068',
};

const GlobalHeaderRight: React.FC<{}> = () => {
  const { initialState } = useModel('@@initialState');
  const {
    outId,
    helpStr,
    outerDialNumber,
    setDialAction,
    setDialCallId,
    dialOff,
    dialStart,
  } = useModel('caller');
  const isMountedRef = useIsMountedRef();
  const access = useAccess(); // access 实例的成员: hasRole, hasRoute

  useEffect(() => {
    // 外网域名 https://risk-fin-v.lalafin.net
    if (localStorage.getItem('debugger') === '1' || ['stg', 'pre'].includes(getVanEvn())) {
      return;
    }

    if (isExternalNetwork()) {
      // 如果是外网域名 不允许打开控制台
      addListener((isOpen: any) => {
        if (isOpen) {
          let time = 5;
          message.warning(
            <div>
              您不能打开控制台, 页面即将关闭(<span id="time">5</span>)...
            </div>,
          );
          const aaa = setInterval(() => {
            time--;
            const dom = document.getElementById('time');
            if (dom) {
              dom!.innerText = time + '';
            }
            if (time === 0) {
              clearInterval(aaa);
              window.close();
            }
          }, 1000);
        }
      });
      launch();
    }
  }, []);

  if (!initialState || !initialState.settings) {
    return null;
  }

  const { navTheme, layout } = initialState.settings;
  let className = styles.right;

  if ((navTheme === 'realDark' && layout === 'top') || layout === 'mix') {
    className = `${styles.right}  ${styles.dark}`;
  }
  const { pathname } = history.location;
  const whiteList = [
    '/businessMng/postLoanMng/overdue-lease-detail',
    '/businessMng/postLoanMng/overdue-detail',
    '/businessMng/postLoanMng/collection-detail',
  ];
  const callerToken = isStandbyLogin()
    ? getToken()
    : initialState?.currentUser?.userId || initialState?.currentUser?.pid || getPid();
  const callerProps = {
    needRebindSupplierExtnumber: true,
    identifier: callerToken,
    outId: !whiteList.includes(pathname) ? '' : outId,
    serviceId: '346', // 175 | 346
    sceneId: 'LalaFinance_Collection', // xinge-panel | Lalafinance_SysRollCall
    cityId: '1002,1001',
    helpStr: !whiteList.includes(pathname)
      ? JSON.stringify({
          scene: 'BUSINESS_TOP_CALL',
          productCode: 'LalaFinance_Collection',
          productName: '金融催收外呼',
        })
      : helpStr,
    tokenType: 'lalafinance',
    mobile: outerDialNumber,
    handleMsg: (msg: CallerHandleMsgArgs) => {
      console.log('拨打电话的回调信息', msg);
      if (isMountedRef.current) {
        if (msg.type === 'error') {
          if (msg.msg) message.error(msg.msg);
          dialOff();
        } else {
          // 在非 逾期详情 页面直接 return
          if (!whiteList.includes(pathname)) return;
          // 设置 外呼号码 是否外部拨号
          if (msg.action === 'beginDial') {
            dialStart(msg?.others);
          }
          // 设置 callId
          if (msg.action === 'dial') {
            setDialCallId(msg?.data?.callId);
          }
          // 设置 拨打动作类型
          setDialAction(msg.action);
        }
      }
    },
  };

  return (
    <Space className={className}>
      {/* <HeaderSearch
        className={`${styles.action} ${styles.search}`}
        placeholder="站内搜索"
        defaultValue="umi ui"
        options={[
          { label: <a href="https://umijs.org/zh/guide/umi-ui.html">umi ui</a>, value: 'umi ui' },
          {
            label: <a href="next.ant.design">Ant Design</a>,
            value: 'Ant Design',
          },
          {
            label: <a href="https://protable.ant.design/">Pro Table</a>,
            value: 'Pro Table',
          },
          {
            label: <a href="https://prolayout.ant.design/">Pro Layout</a>,
            value: 'Pro Layout',
          },
        ]}
        onSearch={value => {
          console.log('input', value);
        }}
      /> */}
      {/* <Tooltip title="使用文档">
        <span
          className={styles.action}
          onClick={() => {
            window.location.href = 'https://pro.ant.design/docs/getting-started';
          }}
        >
          <QuestionCircleOutlined />
        </span>
      </Tooltip> */}
      <Access accessible={access.hasAccess('biz_caller')}>
        <Caller {...callerProps} />
      </Access>
      {/* 审核通知列表 */}
      <AuditNoticeList />
      {/* 预发环境、本地环境多版本工具 */}
      {(getVanEvn() === 'pre' || window?.location?.hostname === 'localhost') && <DevVersionMng />}
      <Avatar />
      {/* {REACT_APP_ENV && (
        <span>
          <Tag color={ENVTagColor[REACT_APP_ENV]}>{REACT_APP_ENV}</Tag>
        </span>
      )} */}
      {['prd', 'prod'].includes(getVanEvn()) &&
        getSSOVanTaskID() &&
        getSSOVanTaskID() !== 'undefined' && <Tag color="blue">SSO: #{getSSOVanTaskID()}</Tag>}
      {/* <SelectLang className={styles.action} /> */}
    </Space>
  );
};
export default GlobalHeaderRight;
