/**
 * 订单纬度 - 线下还款
 */
import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import LoadingButton from '@/components/LoadingButton';
import RepaymentAttachUpload from '@/components/RepaymentAttachUpload/RepaymentAttachUpload';
import { disableFutureDate } from '@/utils/utils';
import type { ActionType } from '@ant-design/pro-components';
import {
  EditableProTable,
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Alert, Col, Descriptions, Form, message, Row } from 'antd';
import BigNumber from 'bignumber.js';
// import { useEffect } from 'react';
import { useAccess } from '@umijs/max';
import { useDebounceFn } from 'ahooks';
import type { PropsWithChildren } from 'react';
import React, { memo, useRef, useState } from 'react';
import { CarOfflineRepayColumns } from '../columns/CarOfflineRepayColumns';
import { OrderOfflineRepayColumns } from '../columns/OrderOfflineRepayColumns';
import { billInfo, calculateBill, submitBillRepay } from '../services';
import type { IbillInfo, IbillListItem, Idimension } from '../types';
import { IdimensionEnCode } from '../types';
import './index.less';

type Props = {
  selectedRows: IbillListItem[];
  dimension: Idimension;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};
const OfflineRepay = (props: PropsWithChildren<Props>) => {
  const { selectedRows, dimension, actionRef } = props;
  const [billInfoData, setBillInfoData] = useState<IbillInfo>();
  const [open, setOpen] = useState(false);
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const access = useAccess();
  const [form] = Form.useForm();
  const [errorDesc, setErrorDesc] = useState('');

  // 计算减免总金额
  function calculateRemissionAmount(remission: any) {
    const {
      remissionPrincipal,
      remissionInterest,
      remissionPenalty,
      remissionAdvanceSettleLiquidatedDamages,
      remissionDelayAmount,
    } = remission;
    const amount = new BigNumber(remissionPrincipal)
      .plus(remissionInterest)
      .plus(remissionPenalty)
      .plus(remissionAdvanceSettleLiquidatedDamages)
      .plus(remissionDelayAmount)
      .toNumber();
    return amount;
  }

  function getAmount() {
    const init = {
      expectRepayAmount: 0,
      totalAmountUnpaid: 0,
      totalPrincipalUnpaid: 0,
      totalInterestUnpaid: 0,
      totalOverduePenaltyUnpaid: 0,
      totalBreach: 0,
      totalLate: 0,
    };
    if (billInfoData?.billRspDTOList.length) {
      return billInfoData?.billRspDTOList?.reduce((pre: any, cur) => {
        const {
          expectRepayAmount,
          totalAmountUnpaid,
          totalPrincipalUnpaid,
          totalInterestUnpaid,
          totalOverduePenaltyUnpaid,
          totalBreach,
          totalLate,
        } = cur;
        const _expectRepayAmount = new BigNumber(pre.expectRepayAmount)
          .plus(expectRepayAmount)
          .toNumber();
        const _totalAmountUnpaid = new BigNumber(pre.totalAmountUnpaid)
          .plus(totalAmountUnpaid)
          .toNumber();
        const _totalPrincipalUnpaid = new BigNumber(pre.totalPrincipalUnpaid)
          .plus(totalPrincipalUnpaid)
          .toNumber();
        const _totalInterestUnpaid = new BigNumber(pre.totalInterestUnpaid)
          .plus(totalInterestUnpaid)
          .toNumber();
        const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
          .plus(totalOverduePenaltyUnpaid)
          .toNumber();
        const _totalBreach = new BigNumber(pre.totalBreach).plus(totalBreach).toNumber();
        const _totalLate = new BigNumber(pre.totalLate).plus(totalLate).toNumber();
        return {
          expectRepayAmount: isNaN(_expectRepayAmount) ? 0 : _expectRepayAmount,
          totalAmountUnpaid: isNaN(_totalAmountUnpaid) ? 0 : _totalAmountUnpaid,
          totalPrincipalUnpaid: isNaN(_totalPrincipalUnpaid) ? 0 : _totalPrincipalUnpaid,
          totalInterestUnpaid: isNaN(_totalInterestUnpaid) ? 0 : _totalInterestUnpaid,
          totalOverduePenaltyUnpaid: isNaN(_totalOverduePenaltyUnpaid)
            ? 0
            : _totalOverduePenaltyUnpaid,
          totalBreach: isNaN(_totalBreach) ? 0 : _totalBreach,
          totalLate: isNaN(_totalLate) ? 0 : _totalLate,
        };
      }, init);
    } else {
      return init;
    }
  }
  console.log(getAmount(), '000000');

  // console.log('asdasd', getAmount());

  //从后端获取数据
  const getRepayData = async () => {
    try {
      // form?.validateFields();
      const err = form.getFieldsError(['remission', 'repayAmount']);
      console.log(err);
      const hasErrors = err.some((item) => item.errors.length > 0);
      console.log(hasErrors);
      //如果有校验error不请求后端接口
      if (hasErrors) {
        return;
      }
      const curFormData = form?.getFieldsValue();
      const { repayAmount, remission } = curFormData || {};
      fullScreenLoadingRef.current?.open();
      const res = await calculateBill({
        billInfoList: billInfoData?.billRspDTOList,
        remissionList: remission,
        repayAmount,
      });
      setErrorDesc('');
      setBillInfoData(res);
    } catch (error: any) {
      //试算单独处理报错逻辑
      // console.log([0, 200].includes(error?.ret));
      if (![0, 200].includes(error?.ret)) {
        setErrorDesc(error?.msg);
      }
      // console.log(error?.msg);
      // console.log(error);
    } finally {
      fullScreenLoadingRef.current?.close();
    }
  };
  const { run, cancel } = useDebounceFn(getRepayData, { wait: 500 });

  return (
    <>
      <LoadingButton
        type="primary"
        onClick={async () => {
          try {
            if (!selectedRows?.length) {
              message.error('还未勾选任何数据');
              return;
            }
            fullScreenLoadingRef.current?.open();
            const billNoList = selectedRows.map((item) => item.billNo);
            const data = await billInfo({
              billNoList,
              dimension,
              secondaryClassification: '0303',
            });
            setBillInfoData(data);
            setOpen(true);
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        线下还款
      </LoadingButton>
      <ModalForm
        open={open}
        form={form}
        layout="horizontal"
        labelCol={{ span: 3 }}
        title={<div className="modal_tit">线下还款</div>}
        modalProps={{
          onCancel: () => {
            cancel(); //取消防抖请求 有bug,关闭弹窗，同时是失焦的会触发计算请求。有一次报错展示
            setOpen(false);
            setErrorDesc('');
          },
          destroyOnClose: true,
        }}
        initialValues={{
          remission: [
            {
              id: 1,
              remissionPrincipal: 0,
              remissionInterest: 0,
              remissionPenalty: 0,
              remissionAdvanceSettleLiquidatedDamages: 0,
              remissionDelayAmount: 0,
            },
          ],
        }}
        width={1000}
        onFinish={async (values) => {
          try {
            console.log('values', values);
            //试算报错不允许提交
            if (errorDesc) {
              return;
            }
            // 是否有权限提交
            if (!access.hasAccess('bill_submit_repay_info_postLoanMng_afterLoanList')) {
              message.error('没有权限提交');
              return;
            }

            const { remission, attach, isRemission, ...rest } = values;
            // const billNoList = billInfoData?.billRspDTOList.map((item) => item.billNo) || [];
            // const {
            //   remissionPrincipal,
            //   remissionInterest,
            //   remissionPenalty,
            //   remissionAdvanceSettleLiquidatedDamages,
            //   remissionDelayAmount,
            // } = remission?.[0];
            const remissionTotalAmount = calculateRemissionAmount(values.remission?.[0]);
            const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              .minus(remissionTotalAmount)
              .toNumber();
            fullScreenLoadingRef.current?.open();
            // console.log({
            //   // remissionPrincipal,
            //   // remissionInterest,
            //   // remissionPenalty,
            //   // remissionAdvanceSettleLiquidatedDamages,
            //   // remissionDelayAmount,
            //   remissionList: remission,
            //   billInfoList: billInfoData?.billRspDTOList,
            //   attach,
            //   // billNoList,
            //   repayAmount: repayTotalAmount,
            //   dimension,
            //   repayDate: repayDate && repayDate + ' 00:00:00',
            //   ...rest,
            // });
            await submitBillRepay({
              // remissionPrincipal,
              // remissionInterest,
              // remissionPenalty,
              // remissionAdvanceSettleLiquidatedDamages,
              // remissionDelayAmount,
              remissionList: remission,
              billInfoList: billInfoData?.billRspDTOList,
              attach,
              // billNoList,
              repayAmount: repayTotalAmount,
              dimension,
              ...rest,
            });
            message.success('提交成功');
            setOpen(false);
            actionRef.current?.reload();
          } catch (error) {
          } finally {
            fullScreenLoadingRef.current?.close();
          }
        }}
      >
        <div className="block_area">
          <Descriptions bordered column={1} size={'small'}>
            <Descriptions.Item label={<div className="label_width">渠道名称</div>}>
              {billInfoData?.channelName}
            </Descriptions.Item>
            <Descriptions.Item label={<div className="label_width">客户名称</div>}>
              {billInfoData?.accountNameList.join(',')}
            </Descriptions.Item>
          </Descriptions>
        </div>
        <div className="tit">还款和减免金额:</div>
        <div className="block_area">
          {errorDesc && (
            <Alert message={errorDesc} type="error" showIcon style={{ marginBottom: 10 }} />
          )}
          <ProFormDependency name={['repayAmount', 'remission']}>
            {(values) => {
              const remissionTotalAmount = calculateRemissionAmount(values.remission?.[0]);
              // const repayTotalAmount = new BigNumber(billInfoData?.repayAmount || '')
              //   .plus(remissionTotalAmount)
              //   .toNumber();
              const repayTotalAmount = new BigNumber(values.repayAmount)
                .plus(remissionTotalAmount)
                .toNumber();
              return (
                <div className="line">
                  <span className="submit_offline_label">总金额:</span>
                  <span className="submit_offline_amount">
                    {isNaN(repayTotalAmount) ? '-' : repayTotalAmount}
                  </span>
                  元
                </div>
              );
            }}
          </ProFormDependency>
          <div className="line mt10">
            <ProFormDigit
              label={<div style={{ fontSize: 18 }}>还款金额</div>}
              name="repayAmount"
              initialValue={getAmount().expectRepayAmount}
              validateTrigger={['onBlur', 'onChange']}
              width="sm"
              rules={[
                { required: true, message: '请输入还款金额' },
                {
                  type: 'number',
                  max: getAmount().totalAmountUnpaid,
                  message: `金额不能大于${getAmount().totalAmountUnpaid}`,
                },
              ]}
              fieldProps={{
                onBlur: run,
                suffix: '元',
                precision: 2,
              }}
            />
          </div>
          {/* </Col>
        </Row> */}
          <ProFormDependency name={['remission']}>
            {(values) => {
              const amount = calculateRemissionAmount(values.remission?.[0]);
              return (
                <div className="line no_border ">
                  <span className="submit_offline_label">减免总金额:</span>
                  <span className="submit_offline_amount">{isNaN(amount) ? '-' : amount}</span>元
                </div>
              );
            }}
          </ProFormDependency>
          <EditableProTable
            name="remission"
            rowKey="id"
            toolBarRender={false}
            pagination={false}
            columns={[
              {
                title: '减免本金(元)',
                dataIndex: 'remissionPrincipal',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalPrincipalUnpaid,
                      message: `金额不可超过${getAmount().totalPrincipalUnpaid}`,
                    },
                  ],
                },
              },
              {
                title: '减免利息(元)',
                dataIndex: 'remissionInterest',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalInterestUnpaid,
                      message: `金额不可超过${getAmount().totalInterestUnpaid}`,
                    },
                  ],
                },
              },
              {
                title: '减免罚息(元)',
                dataIndex: 'remissionPenalty',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalOverduePenaltyUnpaid,
                      message: `金额不可超过${getAmount().totalOverduePenaltyUnpaid}`,
                    },
                  ],
                },
              },
              {
                title: '减免违约金(元)',
                dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalBreach,
                      message: `金额不可超过${getAmount().totalBreach}`,
                    },
                  ],
                },
              },
              {
                title: '减免滞纳金(元)',
                dataIndex: 'remissionDelayAmount',
                valueType: 'digit',
                fieldProps: { style: { width: '100%' }, onBlur: run, precision: 2 },
                formItemProps: {
                  rules: [
                    { required: true, message: '请输入大于等于0的数字' },
                    {
                      type: 'number',
                      max: getAmount().totalLate,
                      message: `金额不可超过${getAmount().totalLate}`,
                    },
                  ],
                },
              },
            ]}
            recordCreatorProps={false}
            editable={{
              type: 'multiple',
              editableKeys: [1],
              // onChange: setEditableRowKeys,
              actionRender: () => {
                return [];
              },
            }}
          />
        </div>
        <div className="tit">请确认账单信息和还款金额无误后提交:</div>
        <div className="block_area pl_0">
          <ProTable
            search={false}
            options={false}
            pagination={false}
            columns={
              dimension === IdimensionEnCode.ORDER_TERM_BILL
                ? OrderOfflineRepayColumns
                : CarOfflineRepayColumns
            }
            dataSource={billInfoData?.billRspDTOList as any}
            scroll={{ x: 'max-content' }}
          />
        </div>
        <div className="tit">付款信息</div>
        <div className="block_area ">
          <Row>
            <Col span={12}>
              <RepaymentAttachUpload />
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <ProFormSelect
                labelCol={{ span: 6 }}
                name="remitType"
                label="支付方式"
                initialValue={2}
                width="sm"
                rules={[{ required: true }]}
                options={[
                  { label: '普通对公打款', value: 2 },
                  { label: '线下二维码还款', value: 3 },
                ]}
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                labelCol={{ span: 6 }}
                name="bankAmount"
                label="打款金额"
                fieldProps={{ min: 0, precision: 2 }}
                placeholder="请输入打款金额"
                width="sm"
                rules={[
                  { required: true },
                  // {
                  //   validator: (_, val) => {
                  //     // 有坑，同时出现很多个error,待优化
                  //     if (Number(val) <= 0) {
                  //       // callBack('回款金额不能大于逾期金额');
                  //       return Promise.reject(new Error('线下汇款金额不能为0'));
                  //     }
                  //     if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  //       // callBack();
                  //       return Promise.reject(new Error('请输入数字'));
                  //     }

                  //     // callBack();
                  //     return Promise.resolve();
                  //   },
                  // },
                ]}
              />
            </Col>
            <Col span={12}>
              <ProFormDatePicker
                name="bankAmountDate"
                labelCol={{ span: 6 }}
                label="打款日期"
                fieldProps={{ disabledDate: disableFutureDate }}
                rules={[{ required: true }]}
                placeholder="请输入回款日期"
                width="sm"
                // fieldProps={{
                //   className: 'offline-repay-date',
                //   getPopupContainer: () => {
                //     return document.getElementsByClassName('offline-repay-date')[0];
                //   },
                // }}
              />
            </Col>
            <Col span={12}>
              <ProFormText name="bankNo" labelCol={{ span: 6 }} label="付款账号" width="sm" />
            </Col>
            <Col span={12}>
              <ProFormText name="bankAccount" labelCol={{ span: 6 }} label="付款户名" width="sm" />{' '}
            </Col>
            <Col span={12}>
              <ProFormText name="bankName" labelCol={{ span: 6 }} label="付款银行" width="sm" />
            </Col>
            <Col span={12}>
              <ProFormText
                name="subBranchBank"
                labelCol={{ span: 6 }}
                label="付款银行支行"
                width="sm"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="repaySerialNo"
                labelCol={{ span: 6 }}
                label="银行到账流水号"
                width="sm"
              />
            </Col>
          </Row>
        </div>
      </ModalForm>
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};

export default memo(OfflineRepay);
