import { ProFormInstance } from '@ant-design/pro-components';
import type { FormInstance } from 'antd';
import type { Rule } from 'antd/es/form';

export type Step = {
  title: string;
  ename: string;
};

export type MetaDataItem = {
  fieldCode: string;
  attribute: {
    allowNull: boolean;
    componentType: string;
    fieldDesc: string;
    fieldOptions: [
      {
        optionCode: string;
        optionValue: string;
      },
    ];
    fieldType: string;
    value?: any;
  };
};

export type SubmitDataItem = {
  finished: boolean;
  list: any[];
};
export type SubmitData = Record<string, SubmitDataItem>;

export type Options = {
  optionCode: string;
  optionValue: string;
};

export type ErrorItem = {
  name: string;
  message: string;
};

export type ItemProps = {
  label: string;
  renderType: string;
  fieldType: string;
  name: string;
  defaultValue?: any;
  options?: Options[] | null;
  required?: boolean;
  rules?: Rule;
  disabled?: boolean;
  onPickerChange?: (v: any) => void;
  onLongDateSelect?: (date: Date) => void;
  onUploadChange?: (fileUrl: string) => void;
  data: any[];
  form?: ProFormInstance;
};

export type FieldItem = {
  fieldCode: string;
  attribute: {
    allowNull: boolean;
    componentType: string;
    fieldDesc: string;
    fieldOptions: { optionCode: string; optionValue: string }[];
    fieldType: string;
    moduleId: string;
    value?: string;
  };
};

export type TempDataObj = Record<string, SubmitDataItem> & {
  form: Record<string, any>;
  isLastOrderData?: boolean;
};

export type FormPageProps = {
  readOnly: boolean;
  form: FormInstance<any>;
  data: any[];
  baseInfo: Record<string, string>;
  step: number;
  viewMode?: boolean;
  editMode: boolean; // 是否是修改模式
  onPreStep: (step: number, values: Record<string, any>) => void;
  onNextStep: (step: number, values: Record<string, any>) => void;
  onAutoSave: (step: number, values: Record<string, any>) => void;
  onValidate: (errNum: number) => void;
};
