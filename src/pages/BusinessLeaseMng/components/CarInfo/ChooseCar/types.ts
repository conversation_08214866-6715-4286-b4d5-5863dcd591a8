/*
 * @Date: 2025-02-08 10:53:27
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2025-02-11 18:37:03
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/CarInfo/ChooseCar/types.ts
 * @Description:
 */
import type { FormInstance } from 'antd';
export type PickerData = {
  label: string;
  value: string | number;
};

export type ChildOptionsType = PickerData & { parentId: string };

export interface Option {
  code: string;
  desc: string;
}

export interface ItemPropsType {
  type: string;
  readOnly?: boolean;
  isView?: boolean;
  disableView?: boolean;
  name: string;
  value?: string[] | number[];
  label?: string;
  options?: PickerData[];
  rules?: Record<string, any>[];
  disabled?: boolean;
  extra?: string;
  onInputChange?: (v: string) => void;
  onInputBlur: (v: string) => void;
  onPickerChange?: (v: any) => void;
}

export interface ChildProps {
  child: ItemPropsType;
}

export interface CarNode {
  carNo: null | string;
  carCode: null | string;
  code: string;
  desc: string;
  nodeList: [];
}

export interface FormPageProps {
  className?: string;
  orderNo: string;
  form: FormInstance<any>;
  readOnly?: boolean;
  viewMode: boolean;
  carList: CarNode[];
  formData: any;
  carType: number;
  channelId: string | null;
  formTree: string[];
  preCarCode?: string;
  preCarType?: number;
  orderStatus?: number;
  incomeStatus?: number;
  onCarCodeConfirm: ({ carCode }: { carCode: string }) => void;
  onSubmit: (values: any) => void;
}

export interface PriceInfo {
  preferentialPrice: number; //优惠金额
  totalPrice: number; //全包价
  transferPrice: number; //车俩转让价
}

export interface Channel {
  channelId: string;
  channelName: string;
  channelType: number;
  status: number;
  licenseTypeList: number[];
}

//上牌城市
export interface CarCity {
  code: string;
  desc: string;
  status: number;
}
