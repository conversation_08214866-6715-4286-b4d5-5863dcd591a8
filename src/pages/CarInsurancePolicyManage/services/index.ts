import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { CarInsurancePolicyItem, CarInsurancePolicyOcrParams } from '../data';

export async function getCarInsurancePolicyOcrInfo(
  params: CarInsurancePolicyOcrParams,
  customRequest?: any,
) {
  return request('/bizadmin/policy/ocr', {
    method: 'POST',
    data: params,
    headers: {
      ...bizAdminHeader,
    },
    skipGlobalErrorTip: true,
    ...customRequest,
  });
}

// 获取保单列表
export async function getCarInsurancePolicyList(params: any) {
  return request('/bizadmin/policy/query/list', {
    method: 'POST',
    data: params,
    headers: {
      ...bizAdminHeader,
    },
    ifTrimParams: true,
  });
}

// 获取保单详情
export async function getCarInsurancePolicyDetail(params: { id: string }) {
  return request(`/bizadmin/policy/query/detail/${params.id}`, {
    method: 'GET',
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 保单列表内容导出
export async function carInsurancePolicyListExportAsync(params: any) {
  return request(`/bizadmin/policy/policy/export`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
    ifTrimParams: true,
  });
}

// 保单压缩包导出
export async function carInsurancePolicyZipExport(params: any) {
  return request(`/bizadmin/policy/policy/zip/export`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
    ifTrimParams: true,
  });
}

// 编辑保单提交校验
export async function editUpdateCarInsurancePolicy(params: CarInsurancePolicyItem) {
  return request(`/bizadmin/policy/checkPolicy`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
  });
}

// 更新保单相关文件（批单、资料）
export async function updateCarInsurancePolicyFile(params: any) {
  return request(`/bizadmin/policy/savePolicyExtend`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
  });
}

// 提交保单
export async function commitCarInsurancePolicy(params: any) {
  return request(`/bizadmin/policy/submitPolicy`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
  });
}

// 提交自动审核
export async function recordCarInsurancePolicyAutoApprove(params: any) {
  return request(`/bizadmin/policy/autoApprovePlicy`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
  });
}

// 手动审核通过
export async function recordCarInsurancePolicyManualApprove(params: any) {
  return request(`/bizadmin/policy/approvePolicy`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
  });
}

// 手动审核驳回
export async function recordCarInsurancePolicyReject(params: any) {
  return request(`/bizadmin/policy/rejectPolicy`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
  });
}

// 手动审核保单异常
export async function recordCarInsurancePolicyException(params: any) {
  return request(`/bizadmin/policy/policyException`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
  });
}

// 编辑详情
export async function updatePolicy(data: any) {
  return request('/bizadmin/policy/updatePolicy', {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
    ifTrimParams: true,
  });
}
