import { memo } from 'react';
import { Upload } from 'antd';
import React from 'react';
import { getBaseUploadAction } from '../utils';
import { getAuthHeaders } from '@/utils/auth';
import type { IuploadCarInfoItem } from '../type';
import ReactDOM from 'react-dom';
const { Dragger } = Upload;

ReactDOM.unstable_batchedUpdates(() => {});

type Props = {
  type: 'vehicleLicenseUrlList' | 'insuranceSlipUrlList';
  ossType: 'vehicleLicenseOssUrlList' | 'insuranceSlipOssUrlList';
  currentIndex: number;
  setCarInfoList: (val: (car: IuploadCarInfoItem[]) => IuploadCarInfoItem[]) => void;
};
const CarUploadDragger: React.FC<Props> = (props) => {
  const { setCarInfoList, type, currentIndex, ossType } = props;

  console.log('currentIndex', currentIndex);

  return (
    <Dragger
      listType="picture"
      action={getBaseUploadAction('/base/oss/common/uploadfile')}
      style={{ width: 360, height: 150 }}
      data={{
        attachment: false,
        acl: 'PUBLIC_READ',
        destPath: 'insurance/policy/vehicleLicense/',
      }}
      multiple={true}
      headers={{ ...getAuthHeaders() }}
      onChange={({ file: newFile, fileList }) => {
        if (newFile.status === 'done') {
          const successUrlList: string[] = [];
          const successOssUrlList: string[] = [];
          fileList.forEach((file) => {
            const { response } = file;
            const { code, data } = response || {};
            const { netWorkPath, filePath } = data || {};
            if (code === 200) {
              // 上传成功
              file.thumbUrl = netWorkPath;
              file.url = netWorkPath; // 有了这个就可以 新标签页打开

              successUrlList.push(netWorkPath);
              successOssUrlList.push(filePath);
            }
          });

          setCarInfoList((carList) => {
            return carList.map((item, index) => {
              return {
                ...item,
                [type]: currentIndex === index ? successUrlList : item[type],
                [ossType]: currentIndex === index ? successOssUrlList : item[ossType],
              };
            });
          });
        }
      }}
    >
      <p style={{ padding: '20px 0px' }}>点击或拖拽上传</p>
    </Dragger>
  );
};
export default memo(CarUploadDragger);
