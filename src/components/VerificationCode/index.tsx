import { LoadingOutlined } from '@ant-design/icons';
import { Button, Input, message, Modal } from 'antd';
import { useEffect, useState } from 'react';
import './index.less';
import { fetchCaptchaImageInfo, verifyCaptcha } from './service';
//

interface VerificationCodeProps {
  visible: boolean;
  handleVisible: (status: boolean) => void;
  onVerifySucceed: () => void;
  onVerifyFailed?: () => void;
}
export default ({ visible, handleVisible, onVerifySucceed }: VerificationCodeProps) => {
  //
  const [isVerifying, setIsVerifying] = useState(false);
  //
  const [codeValue, setCodeValue] = useState('');
  const [codeId, setCodeId] = useState();
  //
  //
  const [codeImgBase64, setCodeImgBase64] = useState('');
  const [imgLoading, setImgLoading] = useState(false);
  //
  const refreshCodeImg = async () => {
    if (isVerifying) {
      return;
    }
    setCodeValue('');
    setImgLoading(true);
    const res = (await fetchCaptchaImageInfo().catch(() => {})) || {};
    setImgLoading(false);
    if (res?.ret === 0 && res?.data?.uuid && res?.data?.img) {
      setCodeId(res?.data?.uuid);
      setCodeImgBase64(res?.data?.img);
    }
  };
  //
  const handleSubmit = async () => {
    if (!codeValue || codeValue?.length === 0) {
      message.error('请输入验证码');
      return;
    }
    // 校验验证码
    setIsVerifying(true);
    const res =
      (await verifyCaptcha({
        uuid: codeId,
        code: codeValue,
      }).catch((data: any) => {
        console.log('verifyCaptcha error', data);
        if (data?.ret && [3034, 3031].includes(data?.ret)) {
          refreshCodeImg();
        }
      })) || {};
    setIsVerifying(false);
    console.log('verifyCaptcha', res);
    if (res?.ret === 0) {
      message.success('验证码校验成功');
      handleVisible(false);
      onVerifySucceed();
    }
  };

  //
  useEffect(() => {
    if (visible) {
      refreshCodeImg().catch(() => {});
    }
  }, [visible]);
  //
  return (
    <>
      <Modal
        open={visible}
        destroyOnClose
        onCancel={() => handleVisible(false)}
        okButtonProps={{
          style: {
            display: 'none',
          },
        }}
        width={280}
        cancelButtonProps={{
          style: {
            display: 'none',
          },
        }}
      >
        <>
          <div className="code-wrap">
            {/* 输入提示 */}
            <p className="code-tips">请先按图形输入正确字符</p>
            {/* 验证码图片 */}
            <div className="img-wrapper">
              {imgLoading ? (
                <div className="code-loading-icon">
                  <LoadingOutlined className="loading-icon" />
                </div>
              ) : (
                <img
                  src={codeImgBase64}
                  alt="点击刷新"
                  onClick={refreshCodeImg}
                  className="code-img"
                />
              )}
            </div>
            {/* 刷新提示 */}
            <p className="code-refresh-tips">看不清？点击图片刷新</p>
            {/* 输入框 */}
            <div className="code-input-wrap">
              <Input
                autoFocus
                value={codeValue}
                onChange={(e) => setCodeValue(e.target.value)}
                maxLength={6}
                placeholder="请输入图中字符"
                className="code-input"
                onPressEnter={() => handleSubmit()}
              />
              <Button
                type="primary"
                loading={isVerifying}
                className="code-submit-btn"
                onClick={() => handleSubmit()}
              >
                提交
              </Button>
            </div>
          </div>
        </>
      </Modal>
    </>
  );
};
