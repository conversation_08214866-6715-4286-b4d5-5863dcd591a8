/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2025-01-10 13:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/utils/recordInsuranceEvent.ts
 * @Description: recordInsuranceEvent.ts
 */

type EventCallback = (...args: any[]) => void;

class EventEmitter {
  private event: Record<string, EventCallback | null> = {};
  private timerRecord: Record<string, NodeJS.Timeout> = {};

  // 订阅事件
  on(eventName: string, callback: EventCallback) {
    this.event[eventName] = callback;
  }

  // 取消订阅
  off(eventName: string) {
    if (!this.event[eventName]) return;
    this.event[eventName] = null;
  }

  // 触发事件
  emit(eventName: string, ...args: any[]) {
    if (!this.event[eventName]) return;
    if (this.timerRecord[eventName]) {
      clearTimeout(this.timerRecord[eventName]);
    }
    this.event[eventName]?.(...args);
  }

  // 只订阅一次
  once(eventName: string, callback: EventCallback) {
    const wrapper = (...args: any[]) => {
      callback(...args);
      this.off(eventName);
    };
    this.on(eventName, wrapper);
  }
  // 周期更新自动更新恢复订阅
  onceRange(eventName: string, callback: EventCallback, interval: number) {
    const wrapper = (...args: any[]) => {
      callback(...args);
      this.off(eventName);
      // 恢复订阅
      const timer = setTimeout(() => {
        this.on(eventName, wrapper);
      }, interval);
      this.timerRecord[eventName] = timer;
    };
    this.on(eventName, wrapper);
  }
}

// 创建事件实例
const eventBus = new EventEmitter();

// 保单操作事件类型
export enum InsuranceEventType {
  REJECT = 'REJECT', // 驳回重新上传
  MARK_ABNORMAL = 'MARK_ABNORMAL', // 标记为保单异常
  APPROVE = 'APPROVE', // 复核通过
  RE_UPLOAD_POLICY = 'RE_UPLOAD_POLICY', // 重新上传保单
  RE_UPLOAD_ENDORSEMENT = 'RE_UPLOAD_ENDORSEMENT', // 重新上传批单
  DELETE_ENDORSEMENT = 'DELETE_ENDORSEMENT', // 删除批单
  DELETE_OTHER_MATERIAL = 'DELETE_OTHER_MATERIAL', // 删除其他资料
  UPLOAD_OTHER_MATERIAL = 'UPLOAD_OTHER_MATERIAL', // 上传其他资料

  // 批量上传事件
  CLOSE_UPLOAD = 'CLOSE_UPLOAD', // 关闭上传
  RETURN_LAST_STEP = 'RETURN_LAST_STEP', // 返回上一步
  NEXT_STEP = 'NEXT_STEP', // 下一步
  COMMIT_UPLOAD = 'COMMIT_UPLOAD', // 提交上传
  // 跳转
  GO_TO_MANAGEMENT = 'GO_TO_MANAGEMENT', // 跳转车险保单管理
  GO_TO_DETAIL = 'GO_TO_DETAIL', // 跳转车险保单详情
  GO_TO_BULK_UPLOAD = 'GO_TO_BULK_UPLOAD', // 跳转车险保单-批量上传
}

export default eventBus;
