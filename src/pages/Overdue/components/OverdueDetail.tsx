/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2021-11-17 15:05:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import React, { useRef } from 'react';
import { Card } from 'antd';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import globalStyle from '@/global.less';
import type { OverdueListItem } from '../data';

const OverdueDetail: React.FC<any> = (props) => {
  const { dataList } = props;
  const columns: ProColumns<OverdueListItem>[] = [
    {
      title: '类型',
      dataIndex: 'type',
      valueEnum: {
        1: '应还',
        2: '已还',
        3: '剩余',
      },
    },
    {
      title: '总额',
      dataIndex: 'totalRepayAmount',
      key: 'totalRepayAmount',
      render: (_, row) => {
        return (row?.totalRepayAmount / 100).toFixed(2) || '-';
      },
    },

    {
      title: '本金',
      dataIndex: 'totalPrincipal',
      key: 'totalPrincipal',
      render: (_, row) => {
        return (row?.totalPrincipal / 100).toFixed(2) || '-';
      },
    },
    {
      title: '利息',
      dataIndex: 'totalInterest',
      key: 'totalInterest',
      render: (_, row) => {
        return (row?.totalInterest / 100).toFixed(2) || '-';
      },
    },
    {
      title: '逾期罚息',
      dataIndex: 'totalDelayInterest',
      key: 'totalDelayInterest',
      render: (_, row) => {
        return (row?.totalDelayInterest / 100).toFixed(2) || '-';
      },
    },
    {
      title: '逾期滞纳金',
      dataIndex: 'totalPenalty',
      key: 'totalPenalty',
      render: (_, row) => {
        return row?.totalPenalty ? (row?.totalPenalty / 100).toFixed(2) : '-';
      },
    },
  ];
  const actionRef = useRef<ActionType>();
  return (
    <>
      <Card title="逾期明细" className={globalStyle.mt20}>
        <ProTable<OverdueListItem>
          columns={columns}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="repayNo"
          search={false}
          options={false}
          toolBarRender={false}
          dataSource={dataList}
          pagination={false}
        />
      </Card>
    </>
  );
};

export default OverdueDetail;
