import { ShowInfo } from '@/components';
import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import { getBlob, saveAs } from '@/utils/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { Descriptions, message } from 'antd';
import React, { memo, useRef } from 'react';
import style from '../index.less';
import type { FileItem, IleaseDetail } from '../types';

type Props = {
  data: IleaseDetail;
  dataLending: any;
  orderDetail: any;
  baseInfo: {
    userName: string;
    loanTerm: string;
  };
};

const infoMap = {
  userName: '客户名称',
  loanTerm: '期数',
  status: '放款状态',
  amount: '放款金额',
  vin: '车架号',
  receiptMaster: '收款主体',
};
const itemMap = {
  status: {
    1: '待放款',
    0: '放款失败',
    2: '放款成功',
    3: '待请款',
    7: '挂起',
    10: '待一审',
    11: '待二审',
    12: '驳回',
    13: '待换卡',
    '-1': '失效',
  },
};

enum LICENSE_TYPE {
  COMPANY = 1, // 挂靠
  PERSONAL = 2, // 个户
}

const LoanResourceUploadInfo: React.FC<Props> = (props) => {
  const { data, dataLending, baseInfo, orderDetail } = props;
  const { otherMsg } = data || {};
  const imagePreviewRef = useRef<ImagePreviewInstance>(null);

  const footerInfo = (
    <div className={style.previewInfo}>
      <ShowInfo
        noCard
        textClassName={style.previewText}
        infoMap={infoMap}
        data={{ ...dataLending, ...baseInfo }}
        itemMap={itemMap}
      />
    </div>
  );

  const download = (url: string, filename: string) => {
    message.loading('下载文件中...');
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
      message.destroy();
    });
  };

  function renderJsx(fileList: FileItem[]) {
    if (!fileList?.length) {
      return '-';
    }
    return (
      <div style={{ marginTop: 20 }}>
        {fileList.map((file, index) => {
          const { name, url } = file;
          const urlList = fileList.map((file) => file.url);
          return (
            <div key={url} style={{ display: 'flex', gap: 20, marginBottom: 10 }}>
              <a
                // 支持预览的就预览，不支持的就下载
                title={
                  ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'pdf'].includes(
                    name.substring(name.lastIndexOf('.') + 1)?.toLowerCase(),
                  )
                    ? '预览'
                    : '下载'
                }
                onClick={() => {
                  imagePreviewRef.current?.previewFile({ url, urlList }, { footer: footerInfo });
                }}
                style={{
                  flex: 1,
                }}
              >
                {name}
              </a>

              <DownloadOutlined
                onClick={() => {
                  download(url, name);
                }}
              />
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div style={{ background: 'white' }}>
      <Descriptions column={2} bordered>
        <Descriptions.Item span={2} label="备注">
          {otherMsg || '-'}
        </Descriptions.Item>
        <Descriptions.Item label={'购车发票'}>
          {renderJsx(data?.carPurchaseInvoice)}
        </Descriptions.Item>
        <Descriptions.Item label={'购置税票'}>
          {renderJsx(data?.purchaseTaxReceipt)}
        </Descriptions.Item>
        <Descriptions.Item label={'行驶证'}> {renderJsx(data?.drivingLicense)}</Descriptions.Item>
        <Descriptions.Item label={'车辆登记证'}>
          {renderJsx(data?.carRegistration)}
        </Descriptions.Item>
        <Descriptions.Item label={'交强险保单'}>
          {renderJsx(data?.trafficInsurance)}
        </Descriptions.Item>
        <Descriptions.Item label={'商业险保单'}>
          {renderJsx(data?.businessInsurance)}
        </Descriptions.Item>
        <Descriptions.Item label={'GPS系统截图'}>
          {renderJsx(data?.gpsSysScreenshot)}
        </Descriptions.Item>
        <Descriptions.Item label={'担保函'}> {renderJsx(data?.guaranteeLetter)}</Descriptions.Item>
        {orderDetail?.licenseType === LICENSE_TYPE.PERSONAL && (
          <Descriptions.Item label={'赋强公证'}>{renderJsx(data?.notarization)}</Descriptions.Item>
        )}
        {orderDetail?.licenseType === LICENSE_TYPE.COMPANY && (
          <Descriptions.Item label={'挂靠协议'}>
            {renderJsx(data?.franchiseAgreement)}
          </Descriptions.Item>
        )}
        <Descriptions.Item label={'购车合同'}>
          {renderJsx(data?.carPurchaseContract)}
        </Descriptions.Item>

        <Descriptions.Item label={'其他附件'}> {renderJsx(data?.otherEnclosure)}</Descriptions.Item>
      </Descriptions>

      <ImagePreview ref={imagePreviewRef} />
    </div>
  );
};

export default memo(LoanResourceUploadInfo);
