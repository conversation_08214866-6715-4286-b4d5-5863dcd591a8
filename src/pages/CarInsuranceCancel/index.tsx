import React, { useEffect, useRef, useState } from 'react';
// import { history, Link } from 'umi';
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import ProTable from '@ant-design/pro-table';
import { Button } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { KeepAlive } from 'react-activation';
import { useAccess, useModel } from 'umi';
import AddModal from './components/AddModal';
import DetailModal from './components/DetailModal';
import { cancelReasonEnumMap, cancelStatusEnumMap, CHANNEL_LEVEL, ListItem } from './const';
import './index.less';
import { exportCancellationList, getChannelInfo, queryCancellationList } from './service';

const Page: React.FC<any> = () => {
  // 渠道用户信息
  const { initialState = {} } = useModel('@@initialState') as any;
  const { currentUser = {} } = initialState;
  //
  const actionRef = useRef();
  const access = useAccess();
  const formRef = useRef<ProFormInstance>();
  const [exportLoading, setExportLoading] = useState(false);
  console.log('exportLoading', exportLoading);

  // 新增弹窗展示、关闭
  const [addModalVisible, setAddModalVisible] = useState(false);
  const closeAddModal = () => {
    setAddModalVisible(false);
  };
  const openAddModal = () => {
    setAddModalVisible(true);
  };
  // 详情弹窗展示、关闭
  const [currentRecord, setCurrentRecord] = useState<ListItem>({} as ListItem);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const closeDetailModal = () => {
    setCurrentRecord({} as ListItem);
    setDetailModalVisible(false);
  };
  const openDetailModal = (item: ListItem) => {
    setCurrentRecord(item);
    setDetailModalVisible(true);
  };
  //

  //
  useEffect(() => {}, []);
  //
  const [modalInitLoading, setModalInitLoading] = useState(false);
  //
  const columns: ProColumns<ListItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      hideInSearch: true,
    },
    {
      title: '车牌号',
      dataIndex: 'plateNo',
      hideInTable: true,
    },
    {
      title: '车架号',
      dataIndex: 'vin',
      hideInTable: true,
    },
    {
      title: '车辆',
      dataIndex: '',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return (
          <>
            {record?.vin && <div>{record?.vin}</div>}
            {record?.plateNo && <div>{record?.plateNo}</div>}
          </>
        );
      },
    },
    {
      title: '客户名称',
      dataIndex: 'userName',
      render: (_, record: ListItem) => {
        return <>{record?.userName || '-'}</>;
      },
    },
    {
      title: '所属渠道',
      dataIndex: 'channelName',
      search: false,
      render: (_, record: ListItem) => {
        return <>{record?.channelName || '-'}</>;
      },
    },
    {
      title: '所属渠道',
      dataIndex: 'channelCode',
      width: 120,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        // 如果是车险渠道 二级 用户只能查看自己渠道的 ,
        disabled: currentUser?.channelLevel === CHANNEL_LEVEL.SECOND_CHANNEL,
        filterOption: (input: string, option: { label: string }) =>
          option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0,
      },
      search: currentUser?.channelLevel === CHANNEL_LEVEL.SECOND_CHANNEL ? false : undefined, // undefined 相当于 true  二级渠道不展示
      // 渠道用户只有一个渠道 不需要搜索
      request: async () => {
        const data = await getChannelInfo({
          channelLevel: currentUser?.channelLevel,
          channelCode: currentUser?.channelCode,
        });
        return data.map((item) => {
          const { channelName, channelCode } = item;
          return {
            label: channelName,
            value: channelCode,
            key: channelCode,
          };
        });
      },
    },
    {
      title: '退保原因',
      dataIndex: 'cancellationReasonStatus',
      valueEnum: cancelReasonEnumMap,
    },
    {
      title: '放款日',
      dataIndex: 'lendingTime',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.lendingTime ? dayjs(record?.lendingTime).format('YYYY-MM-DD') : '-'}</>;
      },
    },
    // {
    //   title: '结清发起期数',
    //   dataIndex: '',
    //   hideInSearch: true,
    //   render: (_, record: ListItem) => {
    //     return (
    //       //   2. 如果该车辆没有提前结清 / 退保成功，则此处展示为/
    //       <>
    //         {record?.settle
    //           ? `${record?.currentSettleTerm || '-'}/${record?.loanTerm || '-'}`
    //           : '-'}
    //       </>
    //     );
    //   },
    // },
    {
      title: '是否结清',
      dataIndex: 'settle',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.settle === true ? '是' : '否'}</>;
      },
    },
    {
      title: '退保金额',
      dataIndex: '',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        if (record?.actualPaymentAmount) {
          return `¥${record?.actualPaymentAmount}`;
        }
        if (record?.actualCancellationAmount) {
          return `¥${record?.actualCancellationAmount}`;
        }
        if (record?.expectedCancellationAmount) {
          return (
            <>
              {`¥${record?.expectedCancellationAmount}`}
              <span className="primary-color">(预估)</span>
            </>
          );
        }
        return '-';
      },
    },
    // {
    //   title: '结清金额',
    //   dataIndex: 'actualSettleAmount',
    //   hideInSearch: true,
    // },
    {
      title: '付款方',
      dataIndex: 'companyName',
      render: (_, record: ListItem) => {
        return <>{record?.companyName || '-'}</>;
      },
    },
    {
      title: '保司退保到账时间',
      dataIndex: 'actualPaymentTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            actualPaymentTimeStart: `${value[0]} 00:00:00`,
            actualPaymentTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render: (_, record: ListItem) => {
        return <>{record?.actualPaymentTime || '-'}</>;
      },
    },
    {
      title: '保司退保流水号',
      dataIndex: 'transactionNo',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.transactionNo || '-'}</>;
      },
    },
    {
      title: '易人行回款',
      dataIndex: 'yirenxingRefundAmount',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.yirenxingRefundAmount ? `¥${record?.yirenxingRefundAmount}` : '-'}</>;
      },
    },
    {
      title: '易人行回款成功时间',
      dataIndex: 'yirenxingRefundTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            yirenxingRefundTimeStart: `${value[0]} 00:00:00`,
            yirenxingRefundTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render: (_, record: ListItem) => {
        if (!record?.yirenxingRefundAmount || BigNumber(record?.yirenxingRefundAmount).eq(0)) {
          return '-';
        }
        return <>{record?.yirenxingRefundTime || '-'}</>;
      },
    },
    {
      title: '易人行回款流水号',
      dataIndex: '',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.yiRenXingBankInfo?.transactionNo || '-'}</>;
      },
    },
    {
      title: '客户回款',
      dataIndex: 'userRefundAmount',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.userRefundAmount ? `¥${record?.userRefundAmount}` : '-'}</>;
      },
    },
    {
      title: '客户回款成功时间',
      dataIndex: 'userRefundTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            userRefundTimeStart: `${value[0]} 00:00:00`,
            userRefundTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render: (_, record: ListItem) => {
        if (!record?.userRefundAmount || BigNumber(record?.userRefundAmount).eq(0)) {
          return '-';
        }
        return <>{record?.userRefundTime || '-'}</>;
      },
    },
    {
      title: '客户回款流水号',
      dataIndex: '',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.userBankInfo?.transactionNo || '-'}</>;
      },
    },
    {
      title: '状态',
      dataIndex: 'orderStatus',
      // fixed: 'right',
      valueEnum: cancelStatusEnumMap,
      width: 100,
      fieldProps: {
        mode: 'multiple',
      },
      search: {
        transform: (value: any) => {
          return {
            orderStatusList: value,
          };
        },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      hideInSearch: true,
      render: (_, record: ListItem) => {
        return <>{record?.createdAt || '-'}</>;
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => {
        return (
          <>
            <Button type="link" disabled={modalInitLoading} onClick={() => openDetailModal(record)}>
              查看详情
            </Button>
          </>
        );
      },
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer className={globalStyle.mt16}>
        <ProTable<ListItem>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="bizNo"
          scroll={{ x: 'max-content' }}
          request={(params: any) => {
            console.log('params', params);
            const validParams = removeBlankFromObject(filterProps(params));
            console.log('validParams', validParams);
            return queryCancellationList({
              userChannelCode: currentUser?.channelCode || undefined,
              ...validParams,
            }).catch(() => {});
          }}
          search={{
            labelWidth: 140,
            defaultCollapsed: false,
          }}
          columns={columns}
          toolBarRender={() => {
            return [
              // access.hasAccess('') && (
              <AsyncExport
                key="export"
                getSearchDataTotal={async () => {
                  const values = formRef.current?.getFieldsFormatValue?.();
                  const params: any = removeBlankFromObject(
                    filterProps({
                      userChannelCode: currentUser?.channelCode || undefined,
                      ...values,
                      current: 1,
                      pageSize: 10,
                    }),
                  );
                  console.log('params', params);
                  const res: any = await queryCancellationList(params).catch(() => {});
                  console.log('res', res);
                  return res?.total;
                }}
                getSearchParams={() => {
                  const values = formRef.current?.getFieldsFormatValue?.();
                  const { current = 1, pageSize = 20 } = values;
                  const params: any = {
                    ...values,
                    current,
                    pageSize,
                    userChannelCode: currentUser?.channelCode || undefined,
                  };
                  return removeBlankFromObject(filterProps(params));
                }}
                exportAsync={exportCancellationList}
                taskCode={[ItaskCodeEnValueEnum.INSURANCE_CANCEL_EXPORT]}
              />,
              // ),
              <Button key="add" onClick={openAddModal} type="primary">
                新增
              </Button>,
            ];
          }}
        />
        {/* 新增弹窗 */}
        {addModalVisible && (
          <AddModal
            currentUser={currentUser}
            visible={addModalVisible}
            refreshTable={() => actionRef.current?.reload()}
            closeModal={closeAddModal}
          />
        )}
        {/* 详情弹窗 */}
        {detailModalVisible && (
          <DetailModal
            setModalInitLoading={setModalInitLoading}
            record={currentRecord}
            currentUser={currentUser}
            visible={detailModalVisible}
            refreshTable={() => actionRef.current?.reload()}
            closeModal={closeDetailModal}
          />
        )}
      </PageContainer>
    </>
  );
};

export default () => {
  return (
    <>
      <HeaderTab />
      <KeepAlive name={'businessMng/postLoanMng/car-insurance-cancel'}>
        <Page />
      </KeepAlive>
    </>
  );
};
