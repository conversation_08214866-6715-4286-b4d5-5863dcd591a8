import { getAuthHeaders } from '@/utils/auth';
import customUpload from '@/utils/customRequest';
import { getBaseUrl } from '@/utils/utils';
import { ProFormUploadDragger } from '@ant-design/pro-components';
import type { UploadFile } from 'antd';
import { Button, message, Upload } from 'antd';
import type { UploadRequestOption } from 'rc-upload/lib/interface';
import React, { useEffect, useRef } from 'react';
import { getCarInsurancePolicyOcrInfo } from '../services';
import { transformFile2OcrStruct } from '../utils';
import eventBus from '../utils/recordInsuranceEvent';

interface DraggerUploadFormProps {
  files?: UploadFile<any>[]; //如果需要使用受控，则需要传入files
  onChange?: any; //如果需要使用受控，则需要传入onChange
  title?: string;
  extra?: string;
  description?: string;
  buttonText?: string;
  isNeedLimit?: boolean; // 是否需要限制文件数量，是则限制一个
  limitCount?: number; // 限制文件数量
  needOcr?: boolean; // 是否需要ocr识别
  needCustomRequest?: boolean; // 是否需要ocr识别
  limitSize?: number; // 限制文件大小,默认4M
  onRemove?: (file: UploadFile<any>) => void; // 删除文件回调
  [key: string]: any;
}

export enum UploadDestPathEnum {
  POLICY_OCR_FILE = 'insurance/policy/policy/', // 车险分期保单ocr地址
  POLICY_EXTEND_FILE = 'insurance/policy/extent/', // 车险分期保单扩展文件地址
}

/**
 * 拖拽上传组件
 * @param files 文件列表 如果需要受控则需要传入
 * @param onChange 文件变化回调 如果需要受控则需要传入
 * @param title 标题
 * @param extra 额外信息
 * @param description 描述
 * @param buttonText 按钮文本
 * @param isNeedLimit 是否需要限制文件数量
 * @param limitCount 限制文件数量
 * @param needOcr 是否需要ocr识别
 * @param destPath 文件存储路径
 * @param actionUrl 上传地址
 * @param needCustomRequest 是否需要自定义请求
 * @param limitSize 限制文件大小,默认4M
 * @param disabled 是否禁用
 * @param maxCount 最大上传数量
 * @param data 上传的文件信息格式（存储、权限等
 * @param onRemove 删除文件回调
 * @returns
 */

const DraggerOcrUploadForm = (props: DraggerUploadFormProps) => {
  const {
    disabled = false,
    data = {},
    isNeedLimit = false,
    needOcr = false,
    needCustomRequest = false,
    limitCount = 0,
    limitSize = 4,
    files,
    actionUrl,
    destPath,
    onChange,
    title,
    extra,
    description,
    buttonText,
    maxCount,
    onRemove,
    ...rest
  } = props;
  const base_url = getBaseUrl();
  const upload_url = `${base_url}/${actionUrl || 'base/oss/common/uploadfile'}`;
  const fileListRef = useRef<UploadFile<any>[]>([]);
  const requestData = {
    acl: 'PUBLIC_READ', // 文件权限
    destPath: destPath || UploadDestPathEnum.POLICY_OCR_FILE, // 文件存储路径
    attachment: false, // 是否是附件, 是附件时则会生成一个可下载文件url，不可预览
    ...(data || {}),
  };

  // 自定义请求
  function customRequest(options: UploadRequestOption) {
    // 每个file request生成一个Abort controller
    const abort = new AbortController();
    return customUpload(
      options,
      async (file, response) => {
        let res;
        if (response && response.code === 200) {
          // 触发别的接口校验文件上传成功返回的部份字段结果
          const ocrResponse = await getCarInsurancePolicyOcrInfo(
            {
              shortFilePath: [response?.data?.filePath],
            },
            {
              signal: abort.signal,
              onUploadProgress(progressEvent) {
                if (options?.onProgress) {
                  if (progressEvent.total > 0) {
                    progressEvent.percent = (progressEvent.loaded / progressEvent.total) * 100;
                  }
                  options?.onProgress?.(progressEvent);
                }
              },
            },
          ).catch((err) => {
            // 需要把此err捕获，并更新到uplaod组件内部
            return err;
          });
          res = ocrResponse;
          if (ocrResponse?.success && !!ocrResponse?.data) {
            (file as any).ocrInfo = transformFile2OcrStruct(ocrResponse?.data[0]);
          } else {
            // 忽略cancel错误, 非正常响应code无意义
            if (ocrResponse?.code !== 'ERR_CANCELED') {
              message.error(
                `code: ${ocrResponse?.ret || ocrResponse?.code || ''} msg: ${
                  ocrResponse?.msg || ocrResponse?.message || 'OCR识别失败'
                }`,
              );
            }
          }
        }
        return {
          res,
        };
      },
      () => {
        abort.abort();
      },
    );
  }

  useEffect(() => {
    // 周期更新订阅
    eventBus.onceRange(
      'once-tip',
      (tip: string) => {
        message.error(tip);
      },
      100,
    );

    return () => {
      eventBus.off('once-tip');
    };
  }, []);

  return (
    <ProFormUploadDragger
      disabled={disabled}
      fieldProps={{
        ref: fileListRef,
        headers: {
          ...getAuthHeaders(),
        },
        data: requestData,
        // 自定义请求
        customRequest: needCustomRequest ? customRequest : undefined,
        listType: 'picture', // 上传完成后回显布局
        multiple: true, // 是否支持多选
        name: 'file', // 后端接收的文件名
        fileList: files || undefined,
        maxCount: maxCount || undefined,
        beforeUpload: (file, fileList) => {
          if (needOcr && file.type !== 'application/pdf') {
            eventBus.emit('once-tip', '请上传pdf文件');
            return Upload.LIST_IGNORE;
          }
          if (isNeedLimit && file.size > limitSize * 1024 * 1024) {
            eventBus.emit('once-tip', `上传文件大小不能超过${limitSize}M，请检查后重新上传`);
            return Upload.LIST_IGNORE;
          }
          const fileListTotalCount = (fileList?.length || 0) + (files?.length || 0);
          if (
            fileList?.length > props.max ||
            (files && props?.limitCount && fileListTotalCount > props.limitCount)
          ) {
            eventBus.emit('once-tip', `只能上传${props.max}个文件，请检查后重新上传`);
            return Upload.LIST_IGNORE;
          }
          return true;
        },
        onRemove: (file) => {
          onRemove?.(file);
        },
      }}
      action={upload_url}
      rules={[
        { required: true, message: '请上传保单!' },
        {
          validator: (rule, val, callBack) => {
            val?.forEach((item: any) => {
              const suffix = item.name.substring(item.name.lastIndexOf('.') + 1);
              if (needOcr && suffix !== 'pdf') {
                callBack(`【此文件类型${suffix}，不在允许范围内】`);
              }
            });

            if (isNeedLimit && limitCount && val.length > limitCount) {
              callBack(`最多只能上传${limitCount}个文件`);
            }
            callBack();
          },
        },
      ]}
      icon={null}
      name="carInsurancePolicyFiles"
      accept={needOcr ? '.pdf' : ''}
      title={title || '请将保单拖拽到此处'}
      extra={
        extra ||
        (needOcr
          ? `注：仅支持pdf文件，单文件大小不能超过${limitSize}M，每天最多识别500次，请注意使用。`
          : `注：单文件大小不能超过${limitSize}M。`)
      }
      description={description || '或点击上传'}
      onChange={onChange}
      {...rest}
    >
      <Button type="primary" itemType="button" disabled={disabled}>
        {buttonText || '上传保单'}
      </Button>
    </ProFormUploadDragger>
  );
};

export default React.memo(DraggerOcrUploadForm);
