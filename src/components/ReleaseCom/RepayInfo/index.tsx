/*
 * @Author: your name
 * @Date: 2021-04-13 10:42:59
 * @LastEditTime: 2024-01-31 10:55:22
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/components/ReleaseCom/RepayInfo.tsx
 */
// import { useRequest } from '@umijs/max';
import ModalTable from '@/components/ModalTable';
import { Card, Table } from 'antd';
import React from 'react';

const styles = {
  mt30: { marginTop: 30 },
};
interface RepayInfoProps {
  carTitle?: string;
  expandable?: (record: any) => void;
  onCancel: () => void;
  dataTable: any[];
  columns: any[];
  modalDataTable: any[];
  modalColumns: any[];
  modalVisible: boolean;
  modalTitle: string;
  rowKey?: string;
  extra?: React.ReactNode;
  remissionColumn: Record<string, any>[];
  remissionDataSource: { costType: string; remissionAmount: number }[];
  repayAttachList: { netWorkPath: string; name: string }[];
  accountNumber?: string;
  accountName?: string;
}

const RepayInfo: React.FC<RepayInfoProps> = (props) => {
  // const { termDetail } = history.location.query;
  const {
    carTitle,
    expandable,
    onCancel,
    dataTable,
    columns,
    modalDataTable,
    modalColumns,
    modalVisible,
    modalTitle,
    rowKey,
    extra,
    remissionColumn,
    remissionDataSource,
    repayAttachList,
  } = props;
  return (
    <Card title={carTitle} style={styles.mt30} extra={extra}>
      <Table
        columns={columns}
        rowKey={rowKey || 'repayPlanNo'}
        scroll={{ x: 'max-content' }}
        // defaultExpandedRowKeys={[termDetail]}
        expandable={{
          expandedRowRender: (record) => {
            return expandable && expandable(record);
          },
        }}
        dataSource={dataTable}
        pagination={false}
        onRow={(record) => {
          return {
            id: record.termDetail ? record.termDetail.split('/')[0] : 1,
          };
        }}
      />
      <ModalTable
        onCancel={onCancel}
        modalVisible={modalVisible}
        title={modalTitle}
        dataSource={modalDataTable}
        columns={modalColumns}
        remissionColumn={remissionColumn}
        remissionDataSource={remissionDataSource}
        repayAttachList={repayAttachList}
      />
    </Card>
  );
};

export default RepayInfo;
