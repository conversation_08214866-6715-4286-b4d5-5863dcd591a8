/*
 * @Author: your name
 * @Date: 2021-11-05 18:10:28
 * @LastEditTime: 2021-11-18 11:07:34
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/ProProcessConfig.tsx
 */
import { Card, Tabs } from 'antd';
import React from 'react';
// import {useRequest} from '@umijs/max';
import globalStyle from '@/global.less';
import Bill from './ProcessDetail/Bill';
import Claim from './ProcessDetail/Claim';
import Incoming from './ProcessDetail/Incoming';
import Loan from './ProcessDetail/Loan';
import Overdue from './ProcessDetail/Overdue';
import PayBack from './ProcessDetail/PayBack';
import SettleEarly from './ProcessDetail/SettleEarly';

const ProProcessConfig: React.FC<any> = (props) => {
  const { data } = props;
  return (
    <Card title="产品流程配置" className={globalStyle.mt30}>
      <Tabs>
        <Tabs.TabPane tab="进件" key="1">
          <Incoming />
        </Tabs.TabPane>
        <Tabs.TabPane tab="放款" key="2">
          <Loan loanData={data?.loanDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="账单" key="3">
          <Bill billData={data?.billDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="债权转让" key="4">
          <Claim claimData={data?.debentureTransferDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="还款" key="5">
          <PayBack
            payBackData={data?.repaymentDTO}
            repayDateCalculateEnum={data?.billDTO?.repayDateCalculateEnum}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="提前结清" key="6">
          <SettleEarly settleEarlyData={data?.inAdvanceRepaymentDTO} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="逾期" key="7">
          <Overdue overDueData={data?.overdueDTO} />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};

export default ProProcessConfig;
