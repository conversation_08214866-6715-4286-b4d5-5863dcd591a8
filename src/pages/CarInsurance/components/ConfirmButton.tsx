import { Button, Popconfirm } from 'antd';
import React, { memo, useState } from 'react';

interface IConfirmButtonProps {
  key: string;
  loading: boolean;
  danger: boolean;
  disable: boolean;
  show?: boolean;
  needConfirm: boolean;
  title?: string;
  description?: string;
  buttonText: string | Element;
  onConfirm: () => void;
  onCancel?: () => void;
}

const ConfirmButton: React.FC<IConfirmButtonProps> = ({
  key,
  loading,
  danger,
  disable,
  show = true,
  needConfirm,
  title,
  description,
  onConfirm,
  onCancel,
  buttonText,
}) => {
  const [open, setOpen] = useState(false);

  // 二次包装方法
  const confirm = () => {
    onConfirm();
  };
  const cancel = () => {
    onCancel?.();
  };

  // 打开确认弹窗
  const handleOpenChange = (status: boolean) => {
    if (!status) {
      setOpen(status);
      return;
    }
    if (needConfirm) {
      setOpen(status); // 打开确认弹窗
    } else {
      confirm(); // 直接触发按钮事件
    }
  };

  if (!show) {
    return null;
  }

  return (
    <Popconfirm
      title={title}
      description={description}
      open={open}
      onOpenChange={handleOpenChange}
      onConfirm={confirm}
      onCancel={cancel}
      okText="是"
      cancelText="否"
    >
      <Button type="primary" key={key} loading={loading} danger={danger} disabled={disable}>
        {buttonText}
      </Button>
    </Popconfirm>
  );
};

export default memo(ConfirmButton);
