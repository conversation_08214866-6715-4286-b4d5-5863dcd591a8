/*
 * @Date: 2024-01-25 17:11:27
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2024-02-29 10:36:10
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/CollectDetailSmallLoan.tsx
 * @Description:
 */
import { DividerTit, ShowInfo } from '@/components';
import {
  CLASSIFICATION_CODE_LABEL,
  PRODUCT_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_CODE,
  SECONDARY_CLASSIFICATION_MAP_ALL,
} from '@/enums';
import globalStyle from '@/global.less';
// import { getOssPath } from '@/services/global';
import { desensitizationPhone, getBlob, isExternalNetwork, previewAS, saveAs } from '@/utils/utils';
import { DownOutlined, QuestionCircleOutlined, UpOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-components';
import ProTable from '@ant-design/pro-table';
import { Popover, Table, Tooltip } from 'antd';
import React, { useState } from 'react';
import { Link } from 'umi';
import { getSmallLoanContract } from '../service';
import { reportLog } from '../services';
import type { I_collectDetail } from '../types';
import './index.less';

const CollectDetail = (props: any) => {
  const { data } = props;
  // const [overdueAmount, setOverDueAmount] = useState<number | string>();
  const [showContractDetail, setShowContractDetail] = useState<string[]>([]);
  // 保理没有的属性；
  const collectDetailSmallLoan = {
    orderNo: '订单号',
    orderAmount: '订单金额',
    totalOverdueAmount: '逾期总额',
    bankName: '银行卡开户行',
    bankPhoneNumber: '银行卡预留手机号',
    contract: '合同',
  };

  // const finalData: any = [];

  // type=1总计 拎出来到父层, type = 2 period已还, type= 3 period初始  key的第一位总数组所在的index+1, key的第二位为type-1
  // costType 1, 逾期总额overdueAll 2,逾期本金overduePrinciple 3，逾期利息overdueInterest 4，逾期罚息overduePenalty 5，逾期滞纳金lateFee
  const mapTitle: Record<number, string> = {
    99: 'overdueAll', // 逾期总额
    1: 'overduePrinciple', // 逾期本金
    2: 'overdueInterest', // 逾期利息
    3: 'overduePenalty', // 逾期罚息o
    4: 'lateFee', // 逾期滞纳金
  };

  // 下载合同
  const download = (url: string, filename: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
    // });
  };
  const previewPDF = (url: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      previewAS(blob);
    });
    // });
  };
  const tableDom = (item: any) => {
    return (
      <ProTable
        style={{ display: 'table-row', width: 500 }}
        search={false}
        // pagination={{ pageSize: 5 }}
        pagination={false}
        toolBarRender={false}
        request={() => getSmallLoanContract(item?.orderNo, item.productCode.substring(0, 4))}
        dataSource={item?.contractFileDTOList}
        columns={[
          { title: '合同名称', dataIndex: 'fileDesc' },
          {
            title: '操作',
            render: (_, record: { netWorkPath: string; fileDesc: string }) =>
              !isExternalNetwork() && (
                <>
                  <a
                    onClick={() => {
                      download(record.netWorkPath, `${record.fileDesc}.pdf`);
                    }}
                  >
                    下载
                  </a>
                  <a
                    className={globalStyle.ml10}
                    onClick={() => {
                      previewPDF(record.netWorkPath);
                    }}
                  >
                    预览
                  </a>
                </>
              ),
          },
        ]}
      />
    );
  };
  const finalDataFunc = (rollItem: any) => {
    const finalData: any = [];
    rollItem?.repayOverdueCostBO?.forEach((item: any, index: any) => {
      let otherDynamicColumnItem: any = {};
      let otherDynamicColumnItem2: any = {
        key: `${index + 1}1`,
        periods: '已还',
        billMonth: '已还',
      };
      let otherDynamicColumnItem3: any = {
        key: `${index + 1}2`,
        periods: '初始',
        billMonth: '初始',
      };
      item?.costItemBOList?.forEach(
        (costItemBOListItem: { costType: number; amount: number; type: number }) => {
          // return costItemBOListItem?.type === 3总数；type= 2 已还 type= 1初始
          // otherDynamicColumn = {[``]}
          if (costItemBOListItem?.type === 3) {
            otherDynamicColumnItem = {
              ...otherDynamicColumnItem,
              [mapTitle[costItemBOListItem?.costType]]: costItemBOListItem?.amount,
            };
            // // 算逾期总额
            // if (costItemBOListItem?.costType === 99) {
            //   setOverDueAmount(costItemBOListItem?.amount || '-');
            // }
            // children的第一行
          } else if (costItemBOListItem?.type === 2) {
            // type= 2 已还 type= 3初始 变成长度为2的数组，塞到children里边。
            otherDynamicColumnItem2 = {
              ...otherDynamicColumnItem2,
              [mapTitle[costItemBOListItem?.costType]]: costItemBOListItem?.amount,
            };
            // children的第二行
          } else if (costItemBOListItem?.type === 1) {
            otherDynamicColumnItem3 = {
              ...otherDynamicColumnItem3,
              [mapTitle[costItemBOListItem?.costType]]: costItemBOListItem?.amount,
            };
          }
        },
      );

      // 组装children
      const childrenTemp = [];
      childrenTemp.push(otherDynamicColumnItem2, otherDynamicColumnItem3);
      const itemTemp = {
        key: index + 1,
        periods: item?.periods,
        billMonth: item?.billMonth,
        overdueDays: item?.overdueDays,
        overdueLevel: item?.overdueLevel,
        billType: data?.billType,
        billNo: item?.billNo || '-',
        ...otherDynamicColumnItem,
        children: childrenTemp,
      };
      finalData.push(itemTemp);
      // console.log(finalData);
    });
    return finalData;
  };
  //  const
  const selfDefineFunc = (item: any) => {
    const { productCode = '', orderNo } = item;
    const isCarInsureProduct =
      productCode?.substring(0, 4) === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE;
    const toUrlMap = {
      toCarInsuranceUrl: `/businessMng/car-insurance/detail?orderNo=${orderNo}`,
      toSmallLoanUrl: `/businessMng/cash-detail?orderNo=${orderNo}`,
    };
    return {
      orderNo: (
        <Link to={toUrlMap[isCarInsureProduct ? 'toCarInsuranceUrl' : 'toSmallLoanUrl']}>
          {item?.orderNo}
        </Link>
      ),
      bankName: (
        <>
          {item?.bankName} {item.bankCode}
        </>
      ),
      contract: (
        <>
          {showContractDetail.includes(item?.orderNo) ? (
            tableDom(item)
          ) : (
            <span
              onClick={() => {
                // const expandFunc = (key: string) => {
                const showContractDetailTemp = showContractDetail;
                if (showContractDetailTemp.includes(item?.orderNo)) {
                  showContractDetailTemp.splice(showContractDetailTemp.indexOf(key), 1);
                } else {
                  showContractDetailTemp.push(item?.orderNo);
                }
                // setActivityKeys([...activeKeysTemp]);
                setShowContractDetail([...showContractDetailTemp]);
                // };
              }}
              className="color-opt"
            >
              查看详情
            </span>
          )}
        </>
      ),
      // overdueAmount,
    };
  };

  // console.log(data?.repayOverdueCostBO);

  const smallLoanColumn = [
    {
      title: '期数',
      dataIndex: 'periods',
      key: 'periods',
    },
    {
      title: '逾期总额',
      dataIndex: 'overdueAll',
      key: 'overdueAll',
    },
    {
      title: '逾期本金',
      dataIndex: 'overduePrinciple',
      key: 'overduePrinciple',
    },
    {
      title: '逾期利息',
      dataIndex: 'overdueInterest',
      key: 'overdueInterest',
    },
    {
      title: '逾期罚息',
      dataIndex: 'overduePenalty',
      key: 'overduePenalty',
    },
    {
      title: '逾期滞纳金',
      dataIndex: 'lateFee',
      key: 'lateFee',
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      key: 'overdueDays',
    },
    {
      title: '逾期等级',
      dataIndex: 'overdueLevel',
      key: 'overdueLevel',
    },
  ];

  // 风险判断函数
  const riskLevelMate = (days: any) => {
    if (days > 3 && days <= 15) {
      return 'B-中低风险客户';
    }
    if (days > 15 && days <= 30) {
      return 'C-中风险客户';
    }
    if (days > 30 && days <= 60) {
      return 'D-中高风险客户';
    }
    if (days > 60) {
      return 'E-高风险客户';
    }
    return 'A-低风险客户';
  };

  // 判断最大逾期天数
  const maxTermMate = () => {
    let item: any = { overdueDays: 0 };
    if (data?.repayDetailList?.length > 0) {
      data?.repayDetailList.forEach((val: any) => {
        if (val.overdueDays > item.overdueDays) {
          item = val;
        }
      });
    }
    return item;
  };
  const maxTerm = maxTermMate();

  // 历史预期明细列表
  const repayDetailListDom = () => {
    const historyColumn = [
      {
        title: '期数',
        dataIndex: 'term',
        key: 'term',
      },
      {
        title: '订单号',
        dataIndex: 'orderNo',
        key: 'orderNo',
      },
      {
        title: '历史逾期天数',
        dataIndex: 'overdueDays',
        key: 'overdueDays',
        render: (_: any, record: any) => (
          <span
            style={{
              color:
                (maxTerm.billDate && maxTerm.billDate === record.billDate) ||
                (maxTerm.term && maxTerm.term === record.term)
                  ? 'red'
                  : 'unset',
            }}
          >
            {record.overdueDays}
          </span>
        ),
      },
      {
        title: '逾期等级',
        dataIndex: 'overdueLevel',
        key: 'overdueLevel',
      },
      {
        title: '还款状态',
        dataIndex: 'repayStatus',
        key: 'repayStatus',
      },
    ];
    const repayDetailListAll = data?.reduce((pre: any[], cur: { repayDetailList: [] }) => {
      return pre.concat(cur?.repayDetailList);
    }, []);
    return <Table dataSource={repayDetailListAll} columns={historyColumn} pagination={false} />;
  };

  function getInfoColumns() {
    const columns1: ProColumns<I_collectDetail>[] = [
      {
        dataIndex: 'productClassicCode',
        valueType: 'select',
        valueEnum: CLASSIFICATION_CODE_LABEL,
        title: '产品一级分类',
      },
      {
        dataIndex: 'productSecondaryCode',
        valueType: 'select',
        valueEnum: SECONDARY_CLASSIFICATION_MAP_ALL,
        title: '产品二级分类',
      },
      {
        dataIndex: 'productName',
        title: '产品名称',
      },
      {
        dataIndex: 'productCode',
        title: '产品ID',
      },
    ];

    const columns2: ProColumns<I_collectDetail>[] = [
      {
        dataIndex: 'orderNo',
        title: '订单号',
        render(_, record) {
          return (
            <Link to={`/businessMng/cash-detail?orderNo=${record?.orderNo}`}>
              {record?.orderNo}
            </Link>
          );
        },
      },
      {
        dataIndex: 'orderAmount',
        title: '订单金额（元）',
      },
    ];
    const columns3: ProColumns<I_collectDetail>[] = [
      {
        dataIndex: 'bankName',
        title: '银行卡开户行',
      },
      {
        dataIndex: 'bankCode',
        title: '银行卡号',
      },
      {
        dataIndex: 'bankPhoneNumber',
        title: '银行卡预留手机号',
        render(_, record) {
          return (
            <>
              {desensitizationPhone(record?.bankPhoneNumber)}
              <Popover content={_} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (record?.bankPhoneNumber) {
                      await reportLog(record?.bankPhoneNumber);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          );
        },
      },
    ];

    const columns: ProColumns<I_collectDetail>[] =
      // eslint-disable-next-line no-nested-ternary
      data?.productCode?.substring(0, 4) === '0303'
        ? columns1
        : data?.productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING
        ? columns2
        : [...columns2, ...columns1, ...columns3];
    return columns;
  }
  const [activeKeys, setActivityKeys] = useState<string[]>([]);
  const expandFunc = (key: string) => {
    const activeKeysTemp = activeKeys;
    if (activeKeysTemp.includes(key)) {
      activeKeysTemp.splice(activeKeysTemp.indexOf(key), 1);
    } else {
      activeKeysTemp.push(key);
    }
    setActivityKeys([...activeKeysTemp]);
  };

  return (
    <div>
      <DividerTit title="逾期明细">
        {data?.map((item: { orderNo: string }) => {
          const orderNo = item?.orderNo;
          return (
            <>
              <div className="info-overdue-wrap" key={orderNo}>
                <div>
                  {isExternalNetwork() ? (
                    <ProDescriptions dataSource={item} columns={getInfoColumns()} />
                  ) : (
                    <ShowInfo
                      data={item}
                      noCard
                      infoMap={collectDetailSmallLoan}
                      selfDefineFunc={selfDefineFunc(item)}
                    />
                  )}
                </div>
                <div
                  className="collapse"
                  onClick={() => {
                    //
                    expandFunc(orderNo);
                  }}
                >
                  <>
                    {activeKeys.includes(orderNo) ? (
                      <>
                        <span>折叠</span>
                        <DownOutlined style={{ color: '#1677ff' }} />
                      </>
                    ) : (
                      <>
                        <span className="color-opt">展开 </span>
                        <UpOutlined style={{ color: '#1677ff' }} />
                      </>
                    )}
                  </>
                </div>
              </div>
              {activeKeys.includes(orderNo) && (
                <div className="overdue-table">
                  <Table
                    dataSource={finalDataFunc(item)}
                    columns={smallLoanColumn}
                    pagination={false}
                  />
                </div>
              )}
            </>
          );
        })}
      </DividerTit>
      <DividerTit title="历史逾期明细">
        <div style={{ margin: 20 }}>
          <div style={{ marginBottom: 10 }}>
            <span style={{ fontWeight: 500 }}>客户预期等级评分</span>
            <Tooltip
              placement="top"
              title={() => (
                <>
                  <p>- A 低风险客户-历史逾期天数在[1-3]天内未超过三天，或未逾期过客户</p>
                  <p>- B 中低风险客户-历史最长逾期天数在{`{3-15]`}天内</p>
                  <p>- C 中风险客户-历史最长逾期天数在{`{15-30]`}天内</p>
                  <p>- D 中高风险客户-历史逾期最长天数在{`{30-60]`}天内</p>
                  <p>- E 高风险客户-历史长逾期天数长达61天以上客户</p>
                </>
              )}
              overlayStyle={{ maxWidth: 1000 }}
            >
              <QuestionCircleOutlined style={{ margin: 10 }} />
            </Tooltip>
            <strong style={{ fontSize: 16 }}>{riskLevelMate(maxTerm.overdueDays)}</strong>
          </div>
          {/* 保理和其他产品表格展示不一致 */}
          {repayDetailListDom()}
        </div>
      </DividerTit>
    </div>
  );
};

export default React.memo(CollectDetail);
