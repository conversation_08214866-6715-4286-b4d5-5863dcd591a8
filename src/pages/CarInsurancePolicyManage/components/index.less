& {
  .ant-menu-item-group .ant-menu-item-group-title {
    padding-left: 0;
    color: #000;
    font-weight: 600;
  }

  .ant-layout-sider.ant-layout-sider-light {
    flex: 0 0 30% !important;
    width: 30% !important;
    max-width: 30% !important;
  }

  .ant-menu-item-group-list {
    max-height: 360px;
    overflow-y: auto;

    .ant-menu-title-content {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;

      & span:nth-child(1) {
        display: block;
        width: 100%;
        max-width: calc(220 / 300 * 100%); // 300px 是 sider 的宽度
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .review-pdf-content-menu-item-extra {
        position: absolute;
        top: 0;
        right: 10px;
        color: red;
        font-weight: 600;
        cursor: pointer;

        &:active {
          color: gray;
        }
      }
    }

    li.ant-menu-item {
      padding-left: 12px !important;
    }
  }
}

.danger-button {
  & button {
    background-color: #f00;

    &:hover {
      background-color: #ff7875 !important;
    }
  }
}
