/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2023-03-17 17:53:58
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/CallPay/components/PassModel.ts
 */
import React, { useState } from 'react';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { callPayReview, collectReliefReview, compensateAudit, offlineRemit } from '../service';
import { ACTIVE_TAB, OFFLINE_TAB } from '../const';

export type RefuseModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  refuseModalVisible: boolean | undefined;
  values: string;
  onVisibleChange: any;
  title: string;
  keyTitle: string; //2为线下回款审批
  offlineRemitOrderNo?: string;
  compensateNo?: string;
  repayKey?: string;
};

const RefuseModal: React.FC<RefuseModalProps> = ({
  repayKey,
  title,
  refuseModalVisible,
  onVisibleChange,
  keyTitle,
  values,
  compensateNo,
  offlineRemitOrderNo,
  onOk,
}) => {
  const [loading, setLoading] = useState(false);
  return (
    <>
      <ModalForm
        title={`${title}审核`}
        width="500px"
        layout="horizontal"
        visible={refuseModalVisible}
        onVisibleChange={onVisibleChange}
        modalProps={{
          centered: true,
          destroyOnClose: true,
          okButtonProps: { loading: loading },
        }}
        onFinish={async (value) => {
          let fun;
          setLoading(true);
          if (keyTitle === ACTIVE_TAB.OFFLINE_REPAYMENT) {
            if (repayKey === OFFLINE_TAB.SINGLE_REPAYMENT) {
              fun = offlineRemit({
                auditResult: 0,
                offlineRemitOrderNo,
                errorMsg: value.errorMsg,
              });
            } else {
              fun = compensateAudit({ auditResult: 0, compensateNo, reason: value.errorMsg });
            }
          } else {
            if (title === '催收减免') {
              fun = collectReliefReview({
                paymentId: values,
                status: 2,
                errorMsg: value.errorMsg,
              });
            } else {
              fun = callPayReview({
                paymentId: values,
                status: 2,
                errorMsg: value.errorMsg,
              });
            }
          }
          fun
            .then(() => {
              setLoading(false);
              message.success('审批成功');
              onOk();
            })
            .finally(() => {
              setLoading(false);
            });
          // return true;
        }}
      >
        <p
          style={{
            textAlign: 'center',
            marginBottom: '10px',
          }}
        >
          {' '}
          <ExclamationCircleOutlined
            style={{
              fontSize: '20px',
              marginRight: 10,
              color: '#faad14',
            }}
          />{' '}
          是否确认驳回该笔{title}？
        </p>
        <ProFormTextArea
          rules={[
            {
              required: true,
              message: '驳回原因为必填',
            },
            {
              whitespace: true,
            },
          ]}
          fieldProps={{
            maxLength: 500,
          }}
          width="md"
          label="驳回原因"
          name="errorMsg"
        />
      </ModalForm>
    </>
  );
};

export default RefuseModal;
