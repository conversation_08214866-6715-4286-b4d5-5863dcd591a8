import { disableFutureDate } from '@/utils/utils';
import { CloseCircleTwoTone, PlusOutlined } from '@ant-design/icons';
import {
  ModalForm,
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormText,
} from '@ant-design/pro-components';
import { Form, Image, message, Upload } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import { claimRecharge } from '../../services';
import './index.less';

interface ClaimRechargeModalProps {
  claimRechargeRef: React.RefObject<any>;
  externalOwnerId: string | undefined;
  productSecondCode: '0303' | '0201'; // 0303 车险， 0201 融租
  accountName: string;
  targetRemitAccountName: string;
}

const ClaimRechargeModal: React.FC<ClaimRechargeModalProps> = (props) => {
  const {
    claimRechargeRef,
    externalOwnerId,
    productSecondCode,
    accountName,
    targetRemitAccountName,
  } = props;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [form] = Form.useForm();

  useImperativeHandle(claimRechargeRef, () => ({
    show: () => {
      setOpen(true);
    },
    hide: () => {
      setOpen(false);
      setLoading(false);
      setFileList([]);
      form.resetFields();
    },
  }));

  // 自定义file
  const customRequest = async (options) => {
    const { file, onSuccess, onError } = options;
    try {
      onSuccess(file);
    } catch (err) {
      onError(err);
    }
  };

  return (
    <ModalForm
      key="claimRechargeModal"
      title="认领充值"
      layout="horizontal"
      width={450}
      open={open}
      form={form}
      loading={loading}
      onOpenChange={(value) => {
        setOpen(value);
        setLoading(false);
        setFileList([]);
        form.resetFields();
      }}
      modalProps={{
        okText: '提交',
      }}
      submitter={{
        render: (_props, doms) => {
          return doms[1];
        },
      }}
      onFinish={async (values) => {
        console.log('values', values);
        if (!fileList.length) {
          form.resetFields(['file']);
          form.validateFields();
          return false;
        }
        setLoading(true);
        const formatValues = {
          ...values,
          externalOwnerId,
          accountName,
          file: fileList[0]?.originFileObj,
          productSecondCode,
        };

        return await claimRecharge(formatValues)
          .then(() => {
            message.success('提交成功');
            return true;
          })
          .finally(() => {
            setLoading(false);
          });
      }}
    >
      <ProForm.Group style={{ marginTop: 20 }}>
        <ProFormDigit
          rules={[
            {
              required: true,
            },
            {
              validator: (_, value) => {
                if (value <= 0) {
                  return Promise.reject('认领金额不能小于等于0');
                }
                return Promise.resolve();
              },
            },
          ]}
          width="md"
          name="remitAmount"
          min={0}
          label="认领金额"
          fieldProps={{
            precision: 2,
          }}
          placeholder="请输入认领金额"
        />

        <ProFormText
          name="remitBankAccountName"
          rules={[
            {
              required: true,
            },
          ]}
          initialValue={targetRemitAccountName}
          disabled
          width="md"
          label="汇款账户名称"
          placeholder="请输入汇款账户名称"
        />

        <ProFormText
          name="remitBankCardNo"
          required
          rules={[
            {
              validator: (_, value) => {
                if (!value) {
                  return Promise.reject('请输入汇款卡号');
                }
                if (!/^\d+$/.test(value)) {
                  return Promise.reject('只能输入数字');
                }
                return Promise.resolve();
              },
            },
          ]}
          width="md"
          label="汇款卡号"
          placeholder="请输入汇款卡号"
        />

        <ProFormText
          name="remitBankName"
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          label="汇款银行名称"
          placeholder="请输入汇款银行名称"
        />

        <ProFormText
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          name="bankFlowNo"
          label="银行流水号"
          placeholder="请输入银行流水号"
        />

        <ProFormDatePicker
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          fieldProps={{
            disabledDate: disableFutureDate,
          }}
          name="remitTime"
          label="汇款日期"
          placeholder="请选择汇款日期"
        />

        <Form.Item
          label="汇款凭据"
          rules={[{ required: true, message: '请上传汇款凭据' }]}
          required
          name="file"
        >
          {fileList.length > 0 ? (
            <div className="file-preview">
              <Image
                src={fileList[0]?.originFileObj && URL.createObjectURL(fileList[0]?.originFileObj)}
                width={100}
                height={100}
              />
              <CloseCircleTwoTone
                className="close-icon"
                onClick={() => {
                  setFileList([]);
                  form.resetFields(['file']);
                }}
              />
            </div>
          ) : (
            <Upload
              accept=".png,.jpg,.jpeg,.bmp"
              listType="picture-card"
              customRequest={customRequest}
              fileList={fileList}
              onChange={(info) => {
                setFileList(info.fileList);
              }}
              maxCount={1}
            >
              <PlusOutlined />
            </Upload>
          )}
        </Form.Item>
      </ProForm.Group>
    </ModalForm>
  );
};

export default React.memo(ClaimRechargeModal);
