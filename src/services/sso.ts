import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
// import { isExternalNetwork } from '@/utils/utils';

interface SSOLoginParams {
  identifier: string;
  extAccount: string;
  loginSource?: string;
}
interface SSOLogoutParams {
  callbackUrl: string;
  pid?: string;
}
// sso生成签名
export const ssoCreateLoginSign = (callbackUrl: string = window.location.href) => {
  request(`/bizadmin/auth/createLoginSign`, {
    method: 'GET',
    params: {
      callbackUrl,
    },
    headers,
  }).then((res: any) => {
    window.location.href = res?.data;
  });
};

// sso授权登录
export const ssoLogin = (data: SSOLoginParams) => {
  return request(`/bizadmin/auth/ssoLogin`, {
    method: 'POST',
    data,
    headers,
  });
};
export const ssoLogout = (data: SSOLogoutParams) => {
  return request(`/bizadmin/employee/SSOLogout`, {
    method: 'POST',
    data,
    headers,
  });
};
