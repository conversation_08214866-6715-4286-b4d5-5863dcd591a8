import { request } from '@umijs/max';

export interface IpolicyItem {
  id: number;
  orderNo: string; // 贷款订单号
  plateNo: string; // 车牌号/新车合格证号
  vin: string; // 车架号
  engineCode: string; // 发动机号
  assuredName: string; // 被保险人
  policyHolder: string; // 投保人
  insuranceCompanySource: string; // 保险公司来源
  insuranceTime: string; // 投保时间
  commercialNo: string; // 商业险保单号
  commercialInsuranceStartDate: string; // 商业险开始日期
  commercialInsuranceEndDate: string; // 商业险结束日期
  commercialInsurancePeriod: number; // 商业险保单期限（天）
  compulsoryNo: string; // 交强险保单号
  insuranceCompany: string; // 保险公司名称
  createdAt: string; // 保单导入时间
}

// 删除保单
export async function deletePolicy(id: number) {
  return request(`/bizadmin/policy/${id}`, {
    method: 'DELETE',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

// 保单列表
export async function getPolicyList(params: any) {
  const {
    current: page = 1,
    pageSize: size = 20,
    plateNo,
    vin,
    engineCode,
    insuranceTime,
    commercialInsuranceStartDate,
  } = params;
  const timeFilter = insuranceTime?.length ? insuranceTime : commercialInsuranceStartDate;
  const timeFilterFlag = insuranceTime?.length
    ? 'insuranceTime'
    : commercialInsuranceStartDate?.length
    ? 'commercialInsuranceStartDate'
    : undefined;
  const startTime = timeFilter?.length ? `${timeFilter?.[0]} 00:00:00` : undefined;
  const endTime = timeFilter?.length ? `${timeFilter?.[1]} 00:00:00` : undefined;
  return request(`/bizadmin/policy/list`, {
    method: 'GET',
    params: {
      page,
      size,
      plateNo,
      vin,
      engineCode,
      timeFilterFlag,
      startTime,
      endTime,
    },
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

// 下载模版
export async function downloadTemplate() {
  return request(`/bizadmin/policy/template`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    responseType: 'blob',
    getResponse: true,
  });
}

// l列表导出
export async function exportPolicyList(params: { startTime: string; endTime: string }) {
  return request(`/bizadmin/policy/list/excel`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
    params,
    responseType: 'blob',
    getResponse: true,
  });
}
