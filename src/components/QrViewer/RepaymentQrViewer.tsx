// 还款二维码
import globalStyle from '@/global.less'; // @ts-ignore
import { ModalForm } from '@ant-design/pro-form';
import { QRCodeCanvas } from 'qrcode.react';
import React from 'react';

export const RePaymentQrViewer = (props: {
  qrVisible: boolean;
  handleQRVisible: (status: boolean) => void;
  title?: string;
  data: any;
  availableRepay?: any;
}) => {
  const { data, qrVisible, handleQRVisible, title } = props;
  const availableRepay = props?.availableRepay || ['支付宝', '微信'];
  const qrCodeAddress = data?.h5RepayUrl || '';
  console.log('availableRepay', availableRepay);
  return (
    <>
      <ModalForm
        title={title || '获取二维码'}
        width="400px"
        modalProps={{
          centered: true,
          destroyOnClose: true,
        }}
        open={qrVisible}
        onOpenChange={handleQRVisible}
        onFinish={async () => {
          handleQRVisible(false);
        }}
      >
        <div className={globalStyle?.textCenter}>
          <QRCodeCanvas
            value={qrCodeAddress} //生成二维码的链接
            size={200} //二维码尺寸
            fgColor="#000000" //二维码颜色
            style={{ margin: '25px auto' }}
          />
          {data?.accountName && <div>客户名称：{data?.accountName}</div>}
          {data?.businessNo && <div>还款单ID：{data?.businessNo}</div>}
          <div style={{ color: 'red' }}>
            请用{availableRepay?.join('、') || '支付宝、微信'}扫码支付
          </div>
          {/* <div style={{ color: 'gray', opacity: 0.5, marginTop: 10 }}>过期后请刷新页面重新获取二维码</div> */}
        </div>
      </ModalForm>
    </>
  );
};
