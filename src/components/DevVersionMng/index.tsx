import { setDevXHllGrayVersionHeaderByStorage, xHllGrayVersionStorageKeyName } from '@/utils/auth';
import { getVanEvn } from '@/utils/utils';
import {
  CheckOutlined,
  CloseOutlined,
  ExperimentOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { Button, Form, Input, message, Modal, Switch, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const DevVersionMng: React.FC = () => {
  const [visible, setVisible] = useState(false);
  //
  const [form] = Form.useForm();
  //
  const [devVersionActive, setDevVersionActive] = useState(true);
  //
  const [devVersionInfo, setDevVersionInfo] = useState<any>({});
  // 从localStorage获取数据
  const initDevVersion = () => {
    const data = localStorage.getItem(xHllGrayVersionStorageKeyName);
    let formData = {
      version: '',
      remark: '',
      active: false,
    };
    try {
      const info = JSON.parse(data || '{}');
      if (info && info?.version) {
        setDevVersionInfo(info);
        setDevVersionActive(info?.active || false);
        formData = {
          version: info?.version,
          remark: info?.remark,
          active: info?.active || false,
        };
      } else {
        setDevVersionActive(false);
      }
    } catch (e) {
      console.log(e);
      setDevVersionActive(false);
    } finally {
      form.setFieldsValue(formData);
    }
  };
  //
  useEffect(() => {
    initDevVersion();
  }, []);

  // 存放在localStorage
  const saveDevVersion = async (needReload = false) => {
    try {
      await form?.validateFields();
    } catch (e) {
      return;
    }
    const formValues = form.getFieldsValue();
    const { version, remark, active } = formValues;
    //
    // 数据结构
    const data = {
      version,
      remark: remark || '',
      active,
    };
    try {
      localStorage.setItem(xHllGrayVersionStorageKeyName, JSON.stringify(data));
      // 设置多版本
      setDevXHllGrayVersionHeaderByStorage();
      // 根据localStorage更新工具状态
      initDevVersion();
      setVisible(false);
      message.success(needReload ? '操作成功, 即将刷新页面..' : '操作成功');
      // 刷新页面
      if (needReload) {
        try {
          const timerId = setTimeout(() => {
            clearTimeout(timerId);
            window?.location?.reload();
          }, 360);
        } catch (err) {}
      }
    } catch (e) {
      message.error('操作失败');
    }
    // 更新状态
  };
  // 判断是否是pre环境或本地开发环境
  const isPreOrLocal = getVanEvn() === 'pre' || window.location.hostname === 'localhost';
  // 非pre、本地环境不显示
  if (!isPreOrLocal) {
    return null;
  }

  return (
    <div className="dev-version-wrapper">
      <div
        className={devVersionActive ? 'btn active' : 'btn deactive'}
        onClick={() => {
          initDevVersion();
          setVisible(true);
        }}
      >
        {devVersionActive ? (
          <div className="version-tag">
            <span title={'多版本管理'}>
              <ExperimentOutlined className="success" />
              &nbsp;{devVersionInfo?.version}
            </span>
          </div>
        ) : (
          <span className="version-btn">
            <ExperimentOutlined />
            &nbsp;多版本管理
          </span>
        )}
      </div>
      {/*  */}
      {/* 弹窗 */}
      <Modal
        title={`多版本管理 (${
          window.location.hostname === 'localhost' ? 'localhost' : getVanEvn()
        })`}
        open={visible}
        footer={(_, { OkBtn, CancelBtn }) => (
          <>
            <CancelBtn />
            <Button
              onClick={() => {
                try {
                  saveDevVersion();
                } catch (err) {
                  console.log(err);
                }
              }}
            >
              确认
            </Button>
            <Button
              type="primary"
              onClick={() => {
                try {
                  saveDevVersion(true);
                } catch (err) {
                  console.log(err);
                }
              }}
            >
              确认并刷新
            </Button>
          </>
        )}
        onCancel={() => {
          setVisible(false);
          try {
            initDevVersion();
          } catch (err) {
            console.log(err);
          }
        }}
      >
        <Form
          form={form}
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 19 }}
          initialValues={{
            version: devVersionInfo?.version,
            remark: devVersionInfo?.remark,
            active: devVersionActive,
          }}
        >
          <Form.Item name="active" label="是否启用">
            <Switch checkedChildren={<CheckOutlined />} unCheckedChildren={<CloseOutlined />} />
          </Form.Item>
          <Form.Item
            noStyle
            dependencies={['active']}
            shouldUpdate={(prevValues, curValues) => prevValues?.active !== curValues?.active}
          >
            {({ getFieldValue }) => {
              const active = getFieldValue('active');
              return (
                <Form.Item
                  name="version"
                  label={
                    <>
                      多版本号
                      <Tooltip
                        placement="topLeft"
                        title={
                          'API多版本信息, 如: v2333, 该值将添加到API请求头x-hll-gray-version中'
                        }
                      >
                        <QuestionCircleOutlined style={{ marginLeft: '5px' }} />
                      </Tooltip>
                    </>
                  }
                  rules={[
                    { required: active, message: '请输入多版本号' },
                    { max: 15, message: '最多15个字符' },
                  ]}
                >
                  <Input placeholder="请输入多版本号, 如: v2333" />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DevVersionMng;
