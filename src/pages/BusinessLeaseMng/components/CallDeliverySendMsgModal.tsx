// 交车催促短信弹窗
import { downLoadExcel } from '@/utils/utils';
import { message, Modal } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import type { SendMsgListInfo } from '../data';
import { exportSendMsgExcel, getSendMsgOrder, sendMsgOrder } from '../service';

type PropsType = {
  visible: boolean;
  orderList: string[];
  onClose: () => void;
};

const CallDeliverySendMsgModal = (props: PropsType) => {
  const [showModal, setShowModal] = useState(false);
  const [dataInfo, setDataInfo] = useState<SendMsgListInfo>({});
  useEffect(() => {
    setShowModal(props.visible);
  }, [props.visible]);

  useMemo(() => {
    if (props.orderList.length && props.visible) {
      getSendMsgOrder(props.orderList).then((res: any) => {
        setDataInfo(res.data);
      });
    }
  }, [props.orderList, props.visible]);

  const onCancel = () => {
    setShowModal(false);
    props.onClose();
  };

  const onOk = () => {
    if (dataInfo?.orderList?.length) {
      sendMsgOrder(dataInfo?.orderList).then(() => {
        message.success('短信发送成功！');
        props.onClose();
      });
    } else {
      props.onClose();
    }
  };

  const handleExport = () => {
    if (dataInfo?.orderList?.length) {
      exportSendMsgExcel(dataInfo.orderList).then((res) => {
        downLoadExcel(res);
      });
    }
  };

  return (
    <Modal open={showModal} title="交车催促短信提醒确认" onCancel={onCancel} onOk={onOk}>
      <div>
        <p>
          仅支持提交【融租状态=<b>“放款审批通过”</b>且当日未成功发送过交车催促短信】的客户
        </p>
        <p>
          已过滤不满足要求的用户，已筛选出用户数据<span>{dataInfo?.accordNum || 0}</span>个
        </p>
        {dataInfo?.accordNum && (
          <>
            <p>
              详细信息如下：<a onClick={handleExport}>成功提交交车催促短信用户.xslx</a>
            </p>
            <p>请确认是否触发短信通知？</p>
          </>
        )}
      </div>
    </Modal>
  );
};

export default CallDeliverySendMsgModal;
