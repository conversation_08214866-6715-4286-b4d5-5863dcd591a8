import { bizadminHeader } from '@/services/consts';
import { SYSTEM_FLAG_HEADER, SYSTEM_FLAG_HEADER_VALUE } from '@/utils/auth';
import { extend } from 'umi-request';
import { envEnum } from './const';

// request实例、不需要鉴权，区别其他业务接口
const syncRequest = extend({});
// 环境映射
const apiHostMap = {
  [envEnum.stg]: 'https://finance-api-stg.lalafin.net',
  [envEnum.pre]: 'https://finance-api-pre.lalafin.net',
  [envEnum.prd]: 'https://finance-api.lalafin.net',
};
// 其他bizadmin业务接口的公共header
const commonHeaders = {
  ...bizadminHeader,
  [SYSTEM_FLAG_HEADER]: SYSTEM_FLAG_HEADER_VALUE,
};
// 获取接口环境
const getSyncApiHost = (env: envEnum) => {
  return apiHostMap[env];
};

// 同步功能
export async function syncFeatures(env: envEnum, data: any) {
  return syncRequest(`${getSyncApiHost(env)}/bizadmin/role/privilege/syncPrivilege`, {
    method: 'POST',
    data,
    headers: commonHeaders,
    hideToast: true,
  });
}
// 同步角色
export async function syncRoles(env: envEnum, data: any) {
  return syncRequest(`${getSyncApiHost(env)}/bizadmin/role/privilege/syncRole`, {
    method: 'POST',
    data,
    headers: commonHeaders,
    hideToast: true,
  });
}
// 同步角色-权限绑定关系
export async function syncRolePermissions(env: envEnum, data: any) {
  return syncRequest(`${getSyncApiHost(env)}/bizadmin/role/privilege/syncBindRolePrivilege`, {
    method: 'POST',
    data,
    headers: commonHeaders,
    hideToast: true,
  });
}
