// @ts-ignore
import { getStoreAndApplyCityAll } from '@/pages/AfterLoan/services';
import { getAllChannelNameEnum } from '@/services/enum';
import { isChannelStoreUser } from '@/utils/utils'; // @ts-ignore
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { Link } from '@umijs/max';
import dayjs from 'dayjs';
import React from 'react';
import { IbillListItem, statusMap } from '../types';

/**
 * 融租-订单总账
 */

export function getFinanceTotalBillListColumn({
  access,
  channelCode,
}: {
  access: any;
  channelCode: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
}) {
  const FinanceTotalBillListColumn: ProColumns<IbillListItem>[] = [
    {
      title: '融租账单ID',
      dataIndex: 'billNo',
      key: 'billNo',
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      render: (_, row) => {
        return <Link to={`/businessMng/lease-detail?orderNo=${row.orderNo}`}>{row.orderNo}</Link>;
      },
    },
    {
      title: '车架号',
      dataIndex: 'subjectMatterNo',
      key: 'subjectMatterNo',
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      key: 'channelName',
      search: false,
    },
    {
      title: '资金渠道',
      dataIndex: 'funderChannelCodeName',
      key: 'funderChannelCodeName',
      search: false,
    },
    {
      title: '门店',
      dataIndex: 'storeName',
      search: false,
    },
    // {
    //   title: '放款资方',
    //   dataIndex: 'capitalChannelName',
    //   search: false,
    // },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      key: 'userName',
      hideInTable: true,
    },
    {
      title: '用户姓名',
      dataIndex: 'accountName',
      key: 'accountName',
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
    },
    {
      title: '渠道名称',
      dataIndex: 'channelCode',
      key: 'channelCode',
      params: { key: 'channelCode' },
      hideInTable: true,
      request: getAllChannelNameEnum,
      debounceTime: 60000,
      initialValue: isChannelStoreUser(access) && !!channelCode ? channelCode : undefined,
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        disabled: isChannelStoreUser(access) && !!channelCode,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
        popupMatchSelectWidth: false,
      },
    },
    {
      title: '门店',
      dataIndex: 'storeIdList',
      key: 'storeIdList',
      hideInTable: true,
      debounceTime: 60000,
      request: () => {
        return getStoreAndApplyCityAll().then((res) => {
          return (
            res?.data?.storeList?.map((item: { storeName: string; id: string }) => {
              return { value: item.id?.toString(), label: item.storeName };
            }) || []
          );
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.extSource?.storeId,
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
        popupMatchSelectWidth: false,
      },
    },
    {
      title: '账单状态',
      dataIndex: 'statusName',
      key: 'statusName',
      search: false,
    },
    {
      title: '已还期数',
      dataIndex: 'settleTermNumber',
      key: 'settleTermNumber',
      search: false,
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDateNumber',
      key: 'overdueDateNumber',
      search: false,
    },
    {
      title: '账单状态',
      dataIndex: 'statusList',
      key: 'statusList',
      hideInTable: true,
      valueType: 'select',
      valueEnum: statusMap,
      fieldProps: {
        mode: 'multiple',
      },
    },

    {
      title: '未还总额(元)',
      dataIndex: 'totalAmountUnpaid',
      key: 'totalAmountUnpaid',
      search: false,
    },
    {
      title: '未还本金(元)',
      dataIndex: 'totalPrincipalUnpaid',
      key: 'totalPrincipalUnpaid',
      search: false,
    },
    {
      title: '未还利息(元)',
      dataIndex: 'totalInterestUnpaid',
      key: 'totalInterestUnpaid',
      search: false,
    },
    {
      title: '未还罚息(元)',
      dataIndex: 'totalOverduePenaltyUnpaid',
      key: 'totalOverduePenaltyUnpaid',
      search: false,
    },
    {
      title: '已还总额(元)',
      dataIndex: 'totalAmountPaid',
      key: 'totalAmountPaid',
      search: false,
    },
    {
      title: '已还本金(元)',
      dataIndex: 'totalPrincipalPaid',
      key: 'totalPrincipalPaid',
      search: false,
    },
    {
      title: '已还利息(元)',
      dataIndex: 'totalInterestPaid',
      key: 'totalInterestPaid',
      search: false,
    },
    {
      title: '已还罚息(元)',
      dataIndex: 'totalOverduePenaltyPaid',
      key: 'totalOverduePenaltyPaid',
      search: false,
    },
    {
      title: '未还滞纳金(元)',
      dataIndex: 'totalLate',
      key: 'totalLate',
      search: false,
    },
    {
      title: '应还滞纳金(元)',
      dataIndex: 'totalLateDue',
      key: 'totalLateDue',
      search: false,
    },
    {
      title: '应结清日期',
      dataIndex: 'dueDate',
      key: 'dueDate',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(2, 'month'), dayjs().add(1, 'month')],
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            dueDateStart: `${value[0]} 00:00:00`,
            dueDateEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.dueDate || '-';
      },
    },
    {
      title: '实际结清日期',
      dataIndex: 'clearTime',
      key: 'clearTime',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            clearTimeStart: `${value[0]} 00:00:00`,
            clearTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.clearTime || '-';
      },
    },
    {
      title: '入账日期',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            updateAtStart: `${value[0]} 00:00:00`,
            updateAtEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.updatedAt || '-';
      },
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
      key: 'lendingTime',
      valueType: 'dateRange',
      fieldProps: {
        getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
        placement: 'bottomRight',
      },
      search: {
        transform: (value: any) => {
          return {
            lendingTimeStart: `${value[0]} 00:00:00`,
            lendingTimeEnd: `${value[1]} 23:59:59`,
          };
        },
      },
      render(_, record) {
        return record.lendingTime || '-';
      },
    },
    // {
    //   title: '放款资方',
    //   dataIndex: 'capitalChannel',
    //   hideInTable: true,
    //   valueType: 'select',
    //   valueEnum: LoanChannelEnum,
    // },
    // {
    //   title: '操作',
    //   dataIndex: 'option',
    //   valueType: 'option',
    //   fixed: 'right',
    //   width: 80,
    //   render: (_, record: IbillListItem) => {
    //     const { expectCancelStatus } = record;
    //     const text = expectCancelStatus ? '取消退保' : '预备退保';
    //     return (
    //       access.hasAccess('bill_cancel_car_insurance') && (
    //         <Button type="link" onClick={() => delConfirm(record)}>
    //           {text}
    //         </Button>
    //       )
    //     );
    //   },
    // },
  ];
  return FinanceTotalBillListColumn;
}
