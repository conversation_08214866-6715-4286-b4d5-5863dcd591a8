/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-01-29 15:03:43
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2025-02-27 19:33:54
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/CarInfo/index.tsx
 * @Description: CarInfo
 */
// import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import { CheckCircleTwoTone, ExclamationCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Spin } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { INCOME_STATUS, ORDER_STATUS, PRE_STATUS } from '../../consts';
import { preCreditAmountInfo, saveRentCarPlan, submitRentCarPlan } from '../../service';
import ChooseCar from './ChooseCar';
import './index.less';
import LeaseScheme from './LeaseScheme';
import type { LatchPlanProps } from './LeaseScheme/types';

type CarInfoProps = {
  orderNo: string;
  userNo: string;
  userName: string;
  orderStatus: number;
  incomeStatus: number;
  carReadOnly: boolean;
  schemeReadOnly: boolean;
  remarkForm?: any;
  onSubmit: () => void;
};

const CarInfo = forwardRef((props: CarInfoProps, ref) => {
  const {
    orderNo,
    userName,
    incomeStatus,
    orderStatus,
    userNo,
    remarkForm,
    carReadOnly,
    schemeReadOnly,
  } = props;
  const chooseCarRef: any = useRef(null);
  const leaseSchemeRef: any = useRef(null);
  const [latchPlanParams, setLatchPlanParams] = useState<LatchPlanProps>({});
  const [loading, setLoading] = useState(false);
  const [preAuditData, setPreAuditData] = useState<Record<string, any>>();
  const [successTip, setSuccessTip] = useState<boolean>(false);
  const [showTryAgain, setShowTryAgain] = useState<boolean>(false);
  const [preCreditRefresh, setPreCreditRefresh] = useState<boolean>(false);

  // const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);

  const tempSave = (showMsg?: boolean) => {
    const carParams = chooseCarRef?.current?.save();
    const schemeParams = leaseSchemeRef?.current?.save();
    const params = {
      carInfo: carParams,
      financePlan: schemeParams,
      orderNo,
      userNo,
      remark: remarkForm?.getFieldValue('remark'),
    };
    // 发送请求
    saveRentCarPlan(params).then(() => {
      if (showMsg) {
        message.success('保存成功！');
      }
    });
  };
  //获取预审结果
  const { run, cancel } = useRequest(
    () => {
      setLoading(true);
      return preCreditAmountInfo(orderNo);
    },
    {
      pollingInterval: 1000,
      pollingWhenHidden: false,
      manual: true,
      onSuccess: async (res: any) => {
        // console.log(res, '0----00');
        //  0-不弹窗-结束轮询 10-loading&弹窗 20-展示额度&结束轮询（或者60s超时结束）
        if ([PRE_STATUS.AUDIT_SUCCESS].includes(res?.data?.preCreditStatus)) {
          setPreAuditData(res?.data);
          setLoading(false);
          cancel();
          await setTimeout(() => {
            props.onSubmit();
            //更新价格信息
            //
          }, 1000);
          setPreCreditRefresh(true);
          chooseCarRef?.current?.updatePriceInfo();
          setPreCreditRefresh(false);
        }
        // 10风控中,20评估完成展示弹窗
        // if ([PRE_STATUS.RISKING, PRE_STATUS.AUDIT_SUCCESS].includes(res?.data?.preCreditStatus)) {

        // }
      },
    },
  );
  const [countdown, setCountdown] = useState<number>(-1); // 倒计时
  // 倒计时逻辑
  useEffect(() => {
    // console.log('倒计时', countdown);
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);

      return () => clearTimeout(timer);
    }

    if (countdown === 0) {
      if (![PRE_STATUS.AUDIT_SUCCESS].includes(preAuditData?.preCreditStatus)) {
        message.warning('预授信额度查询超时，请稍后重试');
        setShowTryAgain(true);
        cancel(); // 取消轮询
        setLoading(false);
      }
      // setSuccessTip(false);
      // console.log('轮询结束');
    }
  }, [countdown, cancel, preAuditData]);
  //轮询
  const rolling = async () => {
    setShowTryAgain(false);
    setCountdown(60);
    //轮询预审额度相关信息
    await run();
  };
  const submit = async () => {
    try {
      if (loading) return;
      setLoading(true);
      const carParams = await chooseCarRef?.current?.submit();
      const schemeParams = await leaseSchemeRef?.current?.submit();
      const remark = remarkForm?.getFieldValue('remark');
      const params: any = {
        orderNo,
        userNo,
        remark,
      };
      if (incomeStatus !== INCOME_STATUS.RESELECT_LEASE_PROGRAM) {
        params.carInfo = carParams;
      }
      if (schemeParams) {
        params.financePlan = schemeParams;
      }
      console.log('submit', params);
      // 发送请求
      submitRentCarPlan(params)
        .then(async () => {
          //预审通过，再跑预授信额度结果,或者重新选择车辆,修改金融方案
          if (
            orderStatus === ORDER_STATUS.PRE_AUDIT_PASS ||
            [INCOME_STATUS.RESELECT_VEHICLE, INCOME_STATUS.RESELECT_LEASE_PROGRAM].includes(
              incomeStatus,
            )
          ) {
            // 清空上次预审数据
            setPreAuditData(undefined);
            setSuccessTip(true);
            rolling();
          } else {
            message.success('提交成功！');
            // 通知父组件已提交
            setTimeout(() => {
              props.onSubmit();
            }, 1000);
            setLoading(false);
          }
        })
        .catch(() => {
          setLoading(false);
        });
      // .finally(() => {
      //   setLoading(false);
      // });
    } catch {
      setLoading(false);
    }
  };

  const clear = () => {
    chooseCarRef?.current?.clear();
  };
  // console.log(showTryAgain);
  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    clear: () => {
      clear();
      tempSave();
    },
    save: () => {
      tempSave(true);
    },
    submit: () => {
      submit();
    },
  }));

  return (
    <Spin spinning={loading}>
      {[INCOME_STATUS.RESELECT_VEHICLE, INCOME_STATUS.RESELECT_LEASE_PROGRAM].includes(
        incomeStatus,
      ) && (
        <div className="carinfo-price-info">
          <span className="carinfo-price-tips">
            {incomeStatus === INCOME_STATUS.RESELECT_VEHICLE && (
              <span>
                <ExclamationCircleOutlined />
                重新选择车辆
              </span>
            )}
            {incomeStatus === INCOME_STATUS.RESELECT_LEASE_PROGRAM && (
              <span>
                <ExclamationCircleOutlined />
                重新选择租赁方案
              </span>
            )}
            {incomeStatus === INCOME_STATUS.RECONFIRM_LEASE_PLAN && (
              <span>
                <ExclamationCircleOutlined />
                重新确认租赁方案
              </span>
            )}
          </span>
          {/* <div>
          <span>车辆转让价(元):</span>
          <span className="carinfo-price-num">
            {latchPlanParams?.transferPrice >= 0 ? latchPlanParams?.transferPrice : '-'}
          </span>
          <Popover
            content={
              <div>
                <p>全包价:¥{latchPlanParams?.totalPrice || '-'}</p>
                <p>优惠金额:¥{latchPlanParams?.preferentialPrice || '-'}</p>
              </div>
            }
          >
            <Tag bordered={false} color="orange">
              优惠明细
            </Tag>
          </Popover>
        </div> */}
        </div>
      )}
      <ChooseCar
        ref={chooseCarRef}
        orderNo={orderNo}
        userName={userName}
        readOnly={carReadOnly}
        orderStatus={orderStatus}
        incomeStatus={incomeStatus}
        onLatchPlanChange={setLatchPlanParams}
      />
      <LeaseScheme
        ref={leaseSchemeRef}
        orderNo={orderNo}
        preCreditRefresh={preCreditRefresh}
        userNo={userNo}
        orderStatus={orderStatus}
        incomeStatus={incomeStatus}
        readOnly={schemeReadOnly}
        latchPlanParams={latchPlanParams}
      />
      <Modal
        open={successTip}
        // open={true}
        title="提交修改成功"
        centered
        footer={[
          <Button
            key="confirm"
            type="primary"
            // loading={loading}
            onClick={() => {
              setSuccessTip(false);
            }}
          >
            确认
          </Button>,
        ]}
        closable={false}
      >
        <div>
          <p>
            预授信额度：
            <span style={{ color: 'red' }}>
              {preAuditData?.preCreditAmount || <LoadingOutlined style={{ color: '#aaa' }} />}（元）
            </span>
            {showTryAgain && (
              <Button
                type="primary"
                color="danger"
                style={{
                  marginLeft: 30,
                  display: showTryAgain ? 'inline-block' : 'none',
                }}
                variant="solid"
                onClick={() => {
                  rolling();
                }}
              >
                获取结果
              </Button>
            )}
          </p>
          <p style={{ display: 'flex', flexDirection: 'row' }}>
            <CheckCircleTwoTone
              twoToneColor="#52c41a"
              style={{ fontSize: '32px', marginRight: 15 }}
            />
            {/* <ExclamationCircleTwoTone
              twoToneColor="#52c41a"
              style={{ fontSize: '32px', marginRight: 15 }}
            /> */}
            <div style={{ width: '80%' }}>
              你好，该客户获得<span style={{ color: 'red' }}>预授信额度</span>
              ，办理结果及融资额度以我司最终审批为准。
            </div>
          </p>
          <p>
            月供金额：
            <span style={{ color: 'red' }}>
              {preAuditData?.preRentPrice || <LoadingOutlined style={{ color: '#aaa' }} />}（元）
            </span>
          </p>
          <p>客户姓名：{preAuditData?.userName}</p>
          <p>身份证号码：{preAuditData?.idNo}</p>
          <p>车型名称：{preAuditData?.carModel}</p>
        </div>
      </Modal>
    </Spin>
  );
});

export default CarInfo;
