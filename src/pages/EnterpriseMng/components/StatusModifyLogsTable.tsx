/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-11-08 14:44:33
 * @modify date 2021-11-08 14:44:33
 * @desc 企业用户-企业用户管理-产品数据-状态变更-变更日志
 */

import { getBlob, previewAS, saveAs } from '@/utils/utils';
import { useRequest } from '@umijs/max';
import { Table, Tag } from 'antd';
import type { ReactNode } from 'react';
import React, { useEffect } from 'react';
import { getStatusModifyLogs } from '../service';
// import { getOssPath } from '@/services/global';
interface StatusModifyLogsTableProps {
  userNo: string;
  subAccountNo: string;
}

function statusRender(text: null | number): ReactNode {
  const statusMap = {
    1: <Tag color="green">激活</Tag>,
    0: <Tag color="red">冻结</Tag>,
    '-1': <Tag color="grey">注销</Tag>,
  };
  if (text !== null) {
    return statusMap[text];
  }
  return <>-</>;
}

const previewPDF = (url: string, name: string) => {
  // getOssPath(url).then((res) => {
  getBlob(url, (blob: Blob) => {
    const exc = name.substring(name.lastIndexOf('.') + 1);
    if ('.jpg.jpeg.gif'.includes(exc)) {
      previewAS(blob, 'image/jpeg;chartset=UTF-8');
    } else if (exc === 'pdf') {
      previewAS(blob);
    } else {
      saveAs(blob, name);
    }
  });
  // });
};

const itemTableEnclosure = (fileInfo: { netWorkPath: string; name: string }[] = []) => {
  return fileInfo?.length
    ? fileInfo.reduce((pre: React.ReactNode, cur: any) => {
        return (
          <>
            {pre}
            <a
              target="_blank"
              onClick={() => {
                previewPDF(cur?.netWorkPath, cur?.name);
              }}
            >
              {cur?.name}
            </a>
            <br />
          </>
        );
      }, <></>)
    : '-';
};

const StatusModifyLogsTable: React.FC<StatusModifyLogsTableProps> = (props) => {
  const { tableProps, refresh } = useRequest(
    (params) => {
      return getStatusModifyLogs({
        ...params,
        userNo: props.userNo,
        subAccountNo: props.subAccountNo,
      });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      formatResult: (response) => {
        return { list: response.data, total: response.total };
      },
    },
  );

  useEffect(() => {
    refresh();
  }, [props?.subAccountNo]);
  return (
    <Table
      rowKey="id"
      scroll={{ x: 'max-content' }}
      columns={[
        {
          title: '产品名称',
          dataIndex: 'productSecondTypeName',
          // width: '180px',
          render: (_, record) => {
            return `${record.productSecondTypeName}（${record.productSecondTypeCode}）`;
          },
        },
        {
          title: '变更前',
          dataIndex: 'modifyBeforeStatus',
          // width: '80px',
          render: statusRender,
        },
        {
          title: '变更后',
          dataIndex: 'modifyAfterStatus',
          // width: '80px',
          render: statusRender,
        },
        {
          title: '变更时间',
          // width: '200px',
          dataIndex: 'updateTime',
        },
        {
          title: '变更人',
          // width: '130px',
          dataIndex: 'modifyPerson',
        },
        {
          title: '备注',
          dataIndex: 'remark',
          onCell: () => {
            return {
              style: { overflowWrap: 'break-word', wordBreak: 'break-word' },
            };
          },
        },
        {
          title: '附件',
          dataIndex: 'fileInfo',
          render: itemTableEnclosure,
        },
      ]}
      {...tableProps}
    />
  );
};

export default React.memo(StatusModifyLogsTable);
