// 资产保理管理-转让资产管理
import HeaderTab from '@/components/HeaderTab';
import type { ColumnsItem } from '@/components/ShowMap';
import ShowMap from '@/components/ShowMap';
import { saveAs } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-components';
import { useRequest, useSearchParams } from '@umijs/max';
import { Card } from 'antd';
import React, { useRef } from 'react';
import OperateRecordTable from './components/OperateRecord';
import { exportTransferPackage, getTransferDetail } from './service';
const AssetTransferDetail = () => {
  const [searchParams] = useSearchParams();
  const recordNo = searchParams.get('recordNo') || '';
  const isLoading = useRef(false);

  const { data: detailInfo } = useRequest(() => {
    if (recordNo) {
      return getTransferDetail(recordNo);
    }
    return Promise.resolve({});
  });

  const handleDownload = async (name: string) => {
    if (!recordNo) return;
    console.log('isLoading', isLoading);
    if (isLoading.current) return;
    isLoading.current = true;
    try {
      const data = await exportTransferPackage(recordNo);
      isLoading.current = false;
      if (data) {
        saveAs(data, name);
      }
    } catch {
      isLoading.current = false;
    }
  };

  const baseInfoColumns: ColumnsItem[] = [
    {
      title: '转让流水号',
      dataIndex: 'recordNo',
      showCopy: true,
    },
    {
      title: '出让方',
      dataIndex: 'transferorName',
    },
    {
      title: '受让方',
      dataIndex: 'transfereeName',
    },
    {
      title: '转让时间',
      dataIndex: 'transferClaimTime',
    },
    {
      title: '放款时间',
      dataIndex: 'lendingTime',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
    },
  ];

  const transferInfoColumns: ColumnsItem[] = [
    {
      title: '转让资产',
      dataIndex: 'transferClaimName',
      render: (key, value) => {
        return (
          <a
            onClick={() => {
              handleDownload(value);
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      title: '资产金额',
      dataIndex: 'totalAmount',
    },
    {
      title: '融资金额',
      dataIndex: 'financingAmount',
    },
    {
      title: '转让比例',
      dataIndex: 'transferRatio',
    },
    {
      title: '融资利率',
      dataIndex: 'financingInterestRate',
    },
    {
      title: '融资期限',
      dataIndex: 'financingTerm',
    },
    {
      title: '回购机制',
      dataIndex: 'repurchaseConfig',
    },
    {
      title: '存款账户',
      dataIndex: 'depositAccountEncrypt',
    },
    {
      title: '收款账户',
      dataIndex: 'collectionAccountEncrypt',
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Card title="基础信息" style={{ marginBottom: 20 }}>
          <ShowMap columns={baseInfoColumns} data={detailInfo} />
        </Card>
        <Card title="转让信息" style={{ marginBottom: 20 }}>
          <ShowMap columns={transferInfoColumns} data={detailInfo} />
        </Card>
        <Card title="操作记录信息">
          <OperateRecordTable recordNo={recordNo} />
        </Card>
      </PageContainer>
    </>
  );
};

export default AssetTransferDetail;
