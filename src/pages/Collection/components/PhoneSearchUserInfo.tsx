import { CLASSIFICATION_CODE_LABEL } from '@/enums';
import { ModalForm } from '@ant-design/pro-form';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { Button, message } from 'antd';
import React, { useRef } from 'react';
import type { IphoneInfoItem } from '../data';
import { getInfoFromPhone } from '../service';
const PhoneSearchUserInfo = () => {
  const columns: ProColumns<IphoneInfoItem>[] = [
    {
      title: '手机号',
      dataIndex: 'phone',
      hideInTable: true,
    },
    {
      title: '身份证号',
      dataIndex: 'idNo',
      hideInTable: true,
    },
    {
      title: '关联业务(产品一级分类)',
      dataIndex: 'productCode',
      search: false,
      valueType: 'select',
      valueEnum: CLASSIFICATION_CODE_LABEL,
    },
    {
      title: '关联业务编号(订单号/账单号)',
      dataIndex: 'orderNo',
      search: false,
      render(text, record) {
        return (
          <a
            onClick={() => {
              // 判断是订单的类型 账单 融租 小贷 且弄上借款主体ID的查询参数
              const { orderNo, productCode } = record;
              const url = {
                '01': `/businessMng/bill-list?orderNo=${orderNo}&isCache=0`,
                '02': `/businessMng/lease-list?orderNo=${orderNo}&isCache=0`,
                '03': `/businessMng/cash-list?orderNo=${orderNo}&isCache=0`,
              };
              history.push(url[productCode]);
              //   window.location.href = url[productCode] // 这种方式会刷新整个页面
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '借款客户名称',
      dataIndex: 'userName',
      search: false,
    },
    {
      title: '来电客户是否借款本人',
      dataIndex: 'isSelf',
      search: false,
      valueType: 'select',
      valueEnum: {
        1: '否',
        0: '是',
      },
    },
    {
      title: '借款主体ID',
      dataIndex: 'userNo',
      search: false,
      render(text, record) {
        return (
          <a
            onClick={() => {
              // 判断是订单的类型 账单 融租 小贷 且弄上借款主体ID的查询参数
              const { userNo, productCode } = record;
              const url = {
                '01': `/businessMng/bill-list?userNo=${userNo}&isCache=0`,
                '02': `/businessMng/lease-list?userNo=${userNo}&isCache=0`,
                '03': `/businessMng/cash-list?userNo=${userNo}&isCache=0`,
              };
              history.push(url[productCode]);
              // window.location.href = url[productCode]
            }}
          >
            {text}
          </a>
        );
      },
    },
    {
      title: '催收案件编号',
      dataIndex: 'overdueCaseNo',
      search: false,
      render(text, record) {
        return (
          <a
            onClick={() => {
              if (record?.overdueCaseNo) {
                const url = `/businessMng/postLoanMng/collection-detail?overdueCaseNo=${text}&isCache=0`;
                // window.location.href = url
                history.push(url);
              }
            }}
          >
            {text}
          </a>
        );
      },
    },
  ];
  const ref = useRef<HTMLDivElement>(null);
  return (
    <div ref={ref}>
      <ModalForm
        layout="inline"
        width={1500}
        trigger={<Button type="primary">来电客户身份查询</Button>}
        title={
          <div>
            来电客户身份查询
            <span style={{ color: '#ccc', fontSize: 12, marginLeft: 20 }}>
              注: 查询仅包括小贷/融租/保理数据,不包括车险分期
            </span>
          </div>
        }
        modalProps={{
          cancelText: '关闭',
          getContainer: () => ref.current!,
          destroyOnClose: true,
        }}
        submitter={{
          submitButtonProps: {
            style: { display: 'none' },
          },
        }}
      >
        <ProTable<IphoneInfoItem>
          style={{ width: '100%' }}
          columns={columns}
          // search={{
          //     labelWidth: 'auto',
          //     layout:"inline"
          //   }}
          request={async (params) => {
            try {
              const { phone, idNo, current = 1, pageSize = 10 } = params;
              const data =
                (phone || idNo) &&
                (await getInfoFromPhone({
                  phone,
                  page: current,
                  size: pageSize,
                  idNo,
                }));
              return {
                data: data?.data,
                success: true,
                total: data?.total,
              };
            } catch (error: any) {
              message.error(error?.message);
              return {
                data: [],
                success: true,
                total: 0,
              };
            }
          }}
          size={'small'}
          rowKey="id"
        />
      </ModalForm>
    </div>
  );
};

export default PhoneSearchUserInfo;
