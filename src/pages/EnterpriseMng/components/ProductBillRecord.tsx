/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 14:22:05
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-29 10:24:23
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/ProductBillRecord.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { downLoadExcel } from '@/utils/utils';
import { history, Link } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Button, Table } from 'antd';
import React, { useState } from 'react';
import { billDetailExport, getBillList } from '../service';

interface ProductBillRecordProps {
  secondaryClassification: string;
  // accountNo: string;
}
const ProductBillRecord = (props: ProductBillRecordProps) => {
  const [exportLoading, setExportLoading] = useState(false);
  const userNo = history?.location?.query?.userNo as string;
  const { tableProps } = useRequest(
    (params) => {
      return getBillList({
        ...params,
        // userNo,
        secondaryClassification: props?.secondaryClassification,
        accountNumber: userNo,
      });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      formatResult: (response) => {
        return { list: response.data, total: response.total };
      },
    },
  );
  const mapStatus = {
    1: '待出账',
    2: '待确认',
    3: '待债转',
    4: '待还款',
    5: '部分还款',
    6: '已结清',
    7: '逾期结清',
    70: '逾期',
  };
  const columns = [
    {
      title: '账单ID',
      dataIndex: 'billNo',
    },
    {
      title: '账单周期',
      dataIndex: 'billCycle',
    },
    {
      title: '账单金额',
      dataIndex: 'totalAmount',
    },
    {
      title: '账单日',
      dataIndex: 'billingDate',
    },
    {
      title: '账期天数',
      dataIndex: 'billRepayDate',
    },
    {
      title: '正常还款日',
      dataIndex: 'repaymentDate',
    },
    {
      title: '保理服务日费率（%）',
      dataIndex: 'factoringServiceFee',
    },
    {
      title: '保理服务费',
      dataIndex: 'factoringServiceCharge',
    },
    {
      title: '放款金额',
      dataIndex: 'lendingAmount',
    },
    {
      title: '账单状态',
      dataIndex: 'status',
      render: (val) => {
        return mapStatus[val] || '-';
      },
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDas',
    },
    {
      title: '操作',
      type: 'option',
      fixed: 'right',
      render: (record) => {
        return (
          <>
            <Link
              to={`/businessMng/bill-detail?billNo=${record.billNo}&accountNumber=${record.accountNumber}`}
            >
              查看详情
            </Link>
          </>
        );
      },
    },
  ];
  const queryBillDetailExport = (params: {
    accountNumber: string;
    secondaryClassification: string;
  }) => {
    setExportLoading(true);
    billDetailExport(params)
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          onClick={() =>
            queryBillDetailExport({
              accountNumber: userNo,
              secondaryClassification: props?.secondaryClassification,
            })
          }
          loading={exportLoading}
          type="primary"
        >
          导出
        </Button>
      </div>
      <Table columns={columns} {...tableProps} scroll={{ x: 'max-content' }} />
    </div>
  );
};

export default ProductBillRecord;
