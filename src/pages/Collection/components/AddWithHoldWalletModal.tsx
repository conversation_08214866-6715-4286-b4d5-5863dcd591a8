/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 15:23:08
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-19 17:07:47
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/AddWithHoldWalletModal.tsx
 * @Description: AddWithHoldWalletModal
 */
import { LEASE_LOAN_CHANNEL, PRODUCT_CLASSIFICATION_CODE, REPURCHASE_STATUS } from '@/enums';
import globalStyle from '@/global.less';
import { getUuid } from '@/utils/utils';
import { ModalForm, ProFormDigit, ProFormSelect } from '@ant-design/pro-form';
import { Col, Form, message, Row, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { WithHoldWalletParams } from '../data';
import { fetchDriverWalletInfo, fetchOverdueBillInfo, postWithHoldWallet } from '../service';

export type AddReliefModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  overDueAmount: number;
  overdueCaseNo: string;
  channelCode: string;
  repurchaseStatus: number; // 上海银行回购标识
  dataWithHoldInfo:
    | {
        userName: string;
        bankNo: string;
        phone: string;
        orderNo: string;
        productCode?: string;
        repayOverdueCostBO: any[];
      }
    | undefined;
  driverWalletInfo: any;
  customerType: string;
};

const AddWithHoldWalletModal: React.FC<AddReliefModalProps> = (props) => {
  const {
    overdueCaseNo,
    dataWithHoldInfo,
    channelCode,
    repurchaseStatus,
    modalVisible,
    driverWalletInfo,
    customerType,
  } = props;
  const [form] = Form.useForm();
  // 资方渠道是上海银行，且没有被易人行回购
  const isShangHaiYinHang =
    channelCode === LEASE_LOAN_CHANNEL.SHANG_HAI_YING_HANG &&
    ![
      REPURCHASE_STATUS.BUY_CHANNEL,
      REPURCHASE_STATUS.BUY_CHANNEL_ING,
      REPURCHASE_STATUS.BUY_SELF,
      REPURCHASE_STATUS.BUY_SELF_ING,
    ].includes(repurchaseStatus);

  const [amountDisabled, setAmountDisabled] = useState(false);

  // type 1为总计行
  const [totalItem, setTotalItem]: any = useState({});
  const overdueList: any = useRef({});

  const [currentDriverWalletInfo, setCurrentDriverWalletInfo] = useState(driverWalletInfo);

  useEffect(() => {
    if (!modalVisible) return;
    setAmountDisabled(false);

    //  发起钱包还款时即使更新下钱包余额信息
    fetchDriverWalletInfo({
      driverId: driverWalletInfo?.driverId,
      customerType,
      overdueCaseNo,
    })
      .then((res) => {
        if (res?.success && res?.data) {
          setCurrentDriverWalletInfo(res?.data);
        }
      })
      .catch(() => {});
    // 账单列表剩余应还记录
    fetchOverdueBillInfo({ caseNo: overdueCaseNo, orderNo: dataWithHoldInfo?.orderNo || '' })
      .then((res) => {
        const dataInfo = res?.data || {};
        overdueList.current = dataInfo;
        setTotalItem(dataInfo);
        form.setFieldsValue({ withholdMoney: dataInfo?.overdueAll });
        // 上海银行资方协议支付金额不能修改
        if (isShangHaiYinHang && dataInfo?.overdueAll) {
          setAmountDisabled(true);
        }
        setTimeout(() => {
          form.validateFields(['withholdMoney']);
        }, 500);
      })
      .catch(() => {});
  }, [modalVisible]);

  const columns = [
    {
      title: '剩余应还总额',
      dataIndex: 'overdueAll',
      key: 'overdueAll',
    },
    {
      title: '剩余应还本金',
      dataIndex: 'overduePrinciple',
      key: 'overduePrinciple',
    },
    {
      title: '剩余应还利息',
      dataIndex: 'overdueInterest',
      key: 'overdueInterest',
    },
    {
      title: '剩余应还罚息',
      dataIndex: 'overduePenalty',
      key: 'overduePenalty',
    },
    {
      title: '剩余应还滞纳金',
      dataIndex: 'lateFee',
      key: 'lateFee',
    },
  ];

  return (
    <>
      <ModalForm
        title="发起协议支付-钱包还款"
        width={800}
        layout="horizontal"
        open={modalVisible}
        onOpenChange={props.onVisibleChange}
        initialValues={{ withholdType: '1' }}
        form={form}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            form.resetFields();
            setTotalItem(overdueList.current);
          },
        }}
        onFinish={async (value) => {
          try {
            await postWithHoldWallet({
              overdueCaseNo,
              ...value,
              orderNo: dataWithHoldInfo?.orderNo,
              freightCustomerType: '1',
              freightCustomerId: currentDriverWalletInfo?.driverId,
              customerPhone: dataWithHoldInfo?.phone,
            } as WithHoldWalletParams);
            message.success('提交成功');
            props.onOk();
          } catch (e) {}
          return true;
        }}
      >
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            <span>姓名:</span>
          </Col>
          <span className={globalStyle.ml10}>{dataWithHoldInfo?.userName}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            司机ID:
          </Col>
          <span className={globalStyle.ml10}>{currentDriverWalletInfo?.driverId}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            钱包余额:
          </Col>
          <span className={globalStyle.ml10}>{currentDriverWalletInfo?.totalAmount}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            订单号:
          </Col>
          <span className={globalStyle.ml10}>{dataWithHoldInfo?.orderNo}</span>
        </Row>
        <ProFormSelect
          label="还款场景"
          name="withholdType"
          labelCol={{ span: 5 }}
          width="md"
          options={[
            { label: '逾期金额协议还款', value: '1' },
            {
              label: '提前结清',
              value: '2',
              disabled: true,
            },
          ]}
          rules={[{ required: true }]}
        />
        {dataWithHoldInfo?.productCode?.substring(0, 2) !==
          PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING && (
          <Row className={globalStyle.mb20}>
            <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
              剩余应还记录:
            </Col>
            <Col span={17}>
              <Table
                dataSource={[totalItem]}
                columns={columns}
                key={getUuid()}
                pagination={false}
                scroll={{ x: 'max-content' }}
              />
            </Col>
          </Row>
        )}

        <ProFormDigit
          name="withholdMoney"
          width="md"
          labelCol={{ span: 5 }}
          label="还款金额(元)"
          placeholder="请输入还款金额"
          fieldProps={{ min: 0, precision: 2 }}
          disabled={amountDisabled}
          tooltip={
            <div>
              <p>①逾期金额协议还款时，仅支持协议支付已逾期应还金额。</p>
              {/* <p>②提前结清时，协议支付金额=逾期期数逾期还款应还金额+剩余期数提前结清应还金额。</p> */}
            </div>
          }
          extra={
            isShangHaiYinHang && (
              <p>
                因协议支付到不同账户，系统提交的扣款金额包含尾款500，发起协议支付时，会自动减去500发起。剩余尾款500需人工再操作一次协议支付。
              </p>
            )
          }
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                if (val <= 0) {
                  return Promise.reject(new Error('还款金额必须大于0'));
                }
                if (
                  /^\d+(\.\d+)?$/.test(val) &&
                  val &&
                  val > Number(currentDriverWalletInfo?.totalAmount)
                ) {
                  return Promise.reject(new Error('还款金额只能≤钱包余额'));
                }
                // 有坑，同时出现很多个error,待优化
                if (/^\d+(\.\d+)?$/.test(val) && val && val > totalItem.overdueAll) {
                  // callBack();
                  return Promise.reject(new Error('协议支付金额不能大于当前剩余应还总额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < totalItem.overdueAll) {
                  // callBack('请输入数字');
                  return Promise.reject(new Error('请输入数字'));
                }
                return Promise.resolve();
              },
            },
          ]}
        />
      </ModalForm>
    </>
  );
};

export default AddWithHoldWalletModal;
