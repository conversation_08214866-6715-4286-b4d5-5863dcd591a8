/*
 * @Author: your name
 * @Date: 2020-12-18 14:18:25
 * @LastEditTime: 2020-12-18 14:31:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Debt/data.d.ts
 */
export interface DebtListParams {
  accountName?: string; // 申请单号
  accountNumber?: boolean; //
  conversionNo?: string;
  loadFrom?: number;
  loadTo?: string;
  orderNo?: string;
  startingTimeEnd?: string;
  startingTimeStart?: string;
  status?: number;
  pageNumber?: number;
  pageSize?: number;
}

export interface DebtListItem {
  accountName: string;
  accountNumber: string;
  accountsReceivable: number;
  conversionNo: string;
  loadFromName: string;
  loadToName: string;
  startingTime: string;
  status: number;
}

export interface DebtListPagination {
  total: number;
  size: number;
  current: number;
}
export interface DebtListData {
  list: DebtListItem[];
  pagination: Partial<DebtListPagination>;
}
