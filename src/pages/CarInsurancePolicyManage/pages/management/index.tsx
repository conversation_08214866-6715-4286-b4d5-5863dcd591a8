/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:49:13
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-26 09:49:13
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/index.tsx
 * @Description: index.tsx
 */

import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import type { ProColumns } from '@ant-design/pro-table';
import { history, useAccess } from '@umijs/max';
import { Button, message } from 'antd';
import dayjs from 'dayjs';
import React, { useRef } from 'react';

import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import HeaderTab from '@/components/HeaderTab';
import { getChannelStoreRequestParams } from '@/pages/AfterLoan/utils';
import type { UploadOcrModalRef } from '@/pages/CarInsurancePolicyManage/components/UploadOcrModal';
import UploadOcrModal from '@/pages/CarInsurancePolicyManage/components/UploadOcrModal';
import { useCarInsuranceManagementContext } from '@/pages/CarInsurancePolicyManage/context/CarInsuranceManagementContext';
import type { CarInsurancePolicyItem } from '@/pages/CarInsurancePolicyManage/data';
import {
  InsuranceSubjectMap,
  PolicyPagePaths,
  PolicyStatusMap,
} from '@/pages/CarInsurancePolicyManage/enum';
import {
  carInsurancePolicyListExportAsync,
  carInsurancePolicyZipExport,
  getCarInsurancePolicyList,
  updateCarInsurancePolicyFile,
} from '@/pages/CarInsurancePolicyManage/services';
import { filterProps, getSortOrder, removeBlankFromObject } from '@/utils/tools';
import { UploadDestPathEnum } from '../../components/DraggerOCRUploadForm';

// 列表sort顺序
const orderList = [
  'orderNo',
  'approveStatus',
  'channelName',
  'plateNo',
  'vin',
  'userName',
  'idNo',
  'insuranceCompany',
  'insuranceSubject',
  'lendingTime',
];

const CarInsurancePolicyManageProviderContainer: React.FC = () => {
  const { loading, request } = useCarInsuranceManagementContext();
  const access = useAccess();
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const uploadOtherMaterialModalRef = useRef<UploadOcrModalRef>(null);

  // 上传其他资料
  const uploadOtherMaterial = async (files: any[]) => {
    if (
      Array.isArray(files) &&
      files.some((file) => file.status === 'uploading' || file.status === 'error')
    ) {
      message.error('文件存在异常，请检查后重新提交');
      return;
    }
    const record: CarInsurancePolicyItem = uploadOtherMaterialModalRef.current?.getRecord();

    const params = {
      id: record.id,
      extendInfo: JSON.stringify({
        otherFileList: [
          ...((record?.otherFileList as string) || []),
          ...files.map((file) => ({
            ossPath: file?.response?.data?.netWorkPath, // 网络路径
            shortPath: file?.response?.data?.filePath, // 短路径
            fileName: file?.name, // 文件名
          })),
        ],
      }),
    };
    updateCarInsurancePolicyFile(params)
      .then(() => {
        message.success('上传成功');
        uploadOtherMaterialModalRef.current?.hide();
        actionRef.current?.reload();
      })
      .catch(() => {
        message.error('上传失败');
      });
  };

  const columns: ProColumns<CarInsurancePolicyItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      order: getSortOrder('orderNo', orderList),
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      order: getSortOrder('channelName', orderList),
    },
    {
      title: '客户名称',
      dataIndex: 'userName',
      order: getSortOrder('userName', orderList),
    },
    {
      title: '客户身份证/信用代码',
      dataIndex: 'idNo',
      order: getSortOrder('idNo', orderList),
      formItemProps: {
        labelCol: {
          offset: 0,
        },
      },
    },
    {
      title: '投保主体',
      dataIndex: 'insuranceSubject',
      order: getSortOrder('insuranceSubject', orderList),
      valueEnum: InsuranceSubjectMap,
    },
    {
      title: '保司名称',
      dataIndex: 'insuranceCompany',
      order: getSortOrder('insuranceCompany', orderList),
      formItemProps: {
        labelCol: {
          offset: 0,
        },
      },
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
      valueType: 'dateRange',
      order: getSortOrder('lendingTime', orderList),
      fieldProps: {
        ranges: {
          最近一年: [dayjs().subtract(1, 'year'), dayjs()],
        },
      },
      // 默认查一年，否则后端不走索引会有问题
      initialValue: [
        dayjs().subtract(1, 'years').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      search: {
        transform: (value: any) => {
          const startDate = value[0];
          const endDate = value[1];
          return {
            lendingTimeStart: dayjs(startDate).format('YYYY-MM-DD 00:00:00'),
            lendingTimeEnd: dayjs(endDate).format('YYYY-MM-DD 23:59:59'),
          };
        },
      },
      render: (_, record) => {
        return record.lendingTime;
      },
    },
    {
      title: '车牌号/新车合格证号',
      dataIndex: 'plateNo',
      order: getSortOrder('plateNo', orderList),
      formItemProps: {
        labelCol: {
          offset: 0,
        },
      },
    },
    {
      title: '车架号',
      dataIndex: 'vin',
      order: getSortOrder('vin', orderList),
    },
    {
      title: '订单商业险金额',
      dataIndex: 'orderCommercialInsurance',
      search: false,
    },
    {
      title: '保单剩余价值',
      dataIndex: 'remainingValueOfPolicy',
      search: false,
    },
    {
      title: '保单上传逾期天数',
      dataIndex: 'uploadOverdueDays',
      search: false,
    },
    {
      title: '其他资料',
      dataIndex: 'otherDataCount',
      search: false,
    },
    {
      title: '保单号',
      dataIndex: 'policyNo',
      search: false,
    },
    {
      title: '投保人',
      dataIndex: 'policyHolder',
      search: false,
    },
    {
      title: '联系电话',
      dataIndex: 'borrowerPhone',
      search: false,
    },
    {
      title: '行驶证车主',
      dataIndex: 'licenseOwner',
      search: false,
    },
    {
      title: '被保险人',
      dataIndex: 'assuredName',
      search: false,
    },
    {
      title: '保单商业险金额',
      dataIndex: 'policyCommercialInsurance',
      search: false,
    },
    {
      title: '商业险实际开始日期',
      dataIndex: 'commercialInsuranceStartDate',
      search: false,
    },
    {
      title: '商业险实际结束日期',
      dataIndex: 'commercialInsuranceEndDate',
      search: false,
    },
    {
      title: '厂牌型号',
      dataIndex: 'factoryPlateModel',
      search: false,
    },
    {
      title: '保单状态',
      dataIndex: 'approveStatusList',
      order: getSortOrder('approveStatus', orderList),
      valueEnum: PolicyStatusMap,
      fieldProps: {
        allowClear: true,
        mode: 'multiple',
      },
      hideInTable: true,
    },
    {
      title: '保单状态',
      dataIndex: 'approveStatus',
      order: getSortOrder('approveStatus', orderList),
      fixed: 'right',
      width: 'max-content',
      valueEnum: PolicyStatusMap,
      fieldProps: {
        allowClear: true,
      },
      render: (node, record) => {
        return node?.props?.valueEnum[`${record.approveStatus}`] || record.approveStatus;
      },
      search: false,
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      width: 'max-content',
      fixed: 'right',
      render: (_, record) => [
        access?.hasAccess('upload_other_biz_businessMng_policyMng') ? (
          <Button
            key="upload-other"
            type="link"
            onClick={() => {
              // 保存选中的订单号
              uploadOtherMaterialModalRef.current?.saveRecord(record);
              // 打开弹窗
              uploadOtherMaterialModalRef.current?.show();
            }}
          >
            上传其他资料
          </Button>
        ) : null,
        access?.hasAccess('query_biz_businessMng_policyMng') ? (
          <Button
            key="view-detail"
            type="link"
            onClick={() => {
              history.push(`${PolicyPagePaths.DETAIL}?id=${record.id}`);
            }}
          >
            查看详情
          </Button>
        ) : null,
      ],
    },
  ];

  // 获取当前过滤条件的数据总数
  const getSearchDataTotal = async () => {
    const searchParams = filterProps(formRef.current?.getFieldsFormatValue?.());
    const data = await getCarInsurancePolicyList({
      ...searchParams,
    });
    return data?.total;
  };

  // 获取当前过滤条件的数据总数
  const getSearchParams = () => {
    const params = formRef.current?.getFieldsFormatValue?.();
    const channelStoreParams = getChannelStoreRequestParams(access);
    return removeBlankFromObject(
      filterProps({
        ...channelStoreParams,
        ...params,
      }),
    );
  };
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<CarInsurancePolicyItem>
          formRef={formRef}
          actionRef={actionRef}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          loading={loading}
          columns={columns}
          request={request}
          search={{
            defaultCollapsed: false,
          }}
          toolBarRender={() => {
            return [
              <>
                {access.hasAccess('biz_businessMng_policyMng_policy_zip_export') && (
                  <>
                    <AsyncExport
                      key="carInsurancePolicyManageExport"
                      getSearchDataTotal={getSearchDataTotal}
                      getSearchParams={getSearchParams}
                      trigger={<Button type="primary">导出保单</Button>}
                      exportAsync={carInsurancePolicyZipExport}
                      taskCode={[ItaskCodeEnValueEnum.INSURANCE_POLICY_MGR_EXPORT]}
                    />
                  </>
                )}
                {access.hasAccess('biz_businessMng_policyMng_policy_export') && (
                  <>
                    <AsyncExport
                      key="carInsurancePolicyManageList"
                      getSearchDataTotal={getSearchDataTotal}
                      getSearchParams={getSearchParams}
                      trigger={<Button type="primary">导出列表</Button>}
                      exportAsync={carInsurancePolicyListExportAsync}
                      taskCode={[ItaskCodeEnValueEnum.INSURANCE_POLICY_MGR]}
                    />
                  </>
                )}

                {access.hasAccess('biz_businessMng_policyMng_batch_upload_policy') && (
                  <Button
                    key="bulk-upload"
                    type="primary"
                    onClick={() => {
                      history.push(PolicyPagePaths.BULK_UPLOAD);
                    }}
                  >
                    批量上传保单
                  </Button>
                )}
              </>,
            ];
          }}
        />
        <UploadOcrModal
          visibleRef={uploadOtherMaterialModalRef}
          onFinish={async (values) => {
            uploadOtherMaterial(values.carInsuranceOtherFiles);
            return false;
          }}
          title="上传其他资料"
          draggerProps={{
            isNeedLimit: true,
            destPath: UploadDestPathEnum.POLICY_EXTEND_FILE,
            name: 'carInsuranceOtherFiles',
            title: '请将其他资料拖拽到此处',
            description: '或点击上传',
            buttonText: '上传其他资料',
            rules: [{ required: true, message: '请上传其他资料!' }],
          }}
        />
      </PageContainer>
    </>
  );
};

export default CarInsurancePolicyManageProviderContainer;
