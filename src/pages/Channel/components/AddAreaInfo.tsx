/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-10-18 19:47:50
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-10-27 20:44:33
 * @FilePath: /lala-finance-biz-web/src/pages/Channel/components/AddLicensedInfo.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import React from 'react';
import { addArea, modifyAreaInfo } from '../service';
import type { AddArea } from '../data';
import { message } from 'antd';
export type AddAreaInfoProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean | undefined;
  values: string;
  onVisibleChange: any;
  title: string;
  refresh: () => void;
  channelId?: string;
  curRow?: Record<string, any>;
  cityList?: Record<string, any>;
  // keyTitle: string; //2为线下回款审批
  // offlineRemitOrderNo?: string;
};
const AddAreaInfo: React.FC<AddAreaInfoProps> = ({
  visible,
  onVisibleChange,
  // onOk,
  close,
  title,
  refresh,
  channelId,
  curRow,
  cityList,
}) => {
  return (
    <div>
      <ModalForm
        title={`${title}展业区域`}
        width="500px"
        layout="horizontal"
        visible={visible}
        initialValues={{
          ...curRow,
          saleCity: curRow?.saleCity
            ? { label: curRow?.saleCity, value: curRow?.saleCityCode }
            : null,
        }}
        onVisibleChange={onVisibleChange}
        modalProps={{
          centered: true,
          onCancel: close,
        }}
        onFinish={async (formValues) => {
          // console.log(formValues);
          const { storeName, storeAddress, saleCity } = formValues;
          const commonObj = {
            saleCity: saleCity?.label,
            saleCityCode: saleCity?.value,
            storeName,
            storeAddress,
            channelId,
          };
          const addPost = {
            ...commonObj,
          } as AddArea;

          const editPost = {
            id: curRow?.id,
            ...commonObj,
          };
          const finalPostInfo = title === '新增' ? addPost : editPost;
          const finalPost = title === '新增' ? addArea : modifyAreaInfo;
          // console.log(finalPost)
          // return;
          await finalPost(finalPostInfo).then(() => {
            message.success(`${title}成功`);
            // onOk()
            refresh();
            close();
          });

          return true;
        }}
      >
        <ProFormText
          name="storeName"
          rules={[{ required: true }]}
          width="sm"
          labelCol={{ span: 7 }}
          fieldProps={{ maxLength: 20 }}
          label="门店名称"
          placeholder="请输入门店名称"
        />
        <ProFormSelect
          rules={[{ required: true }]}
          request={async () => cityList}
          placeholder="请选择销售城市"
          name="saleCity"
          width="sm"
          labelCol={{ span: 7 }}
          label="销售城市"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            labelInValue: true,
          }}
        />
        <ProFormText
          name="storeAddress"
          rules={[{ required: true }]}
          labelCol={{ span: 7 }}
          width="sm"
          fieldProps={{ maxLength: 50 }}
          label="门店地址"
          placeholder="请输入门店地址"
        />
      </ModalForm>
    </div>
  );
};

export default AddAreaInfo;
