import ImagePreview from '@/components/ImagePreview';
import { isPdfUrl } from '@/utils/utils';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Form, Space, Table, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { getCallFailRecord, getSupplementRecord } from '../../../service';
import { PDF_PREVIEW_IMG } from '../AddSupplement/consts';
import DisplayItem from '../AddSupplement/DisplayItem';

type RiskRejectRecordProps = {
  orderNo: string;
  riskOrderNo: string;
};

//反馈信息状态 0-未反馈 1-已反馈
enum FEEDBACK_STATUS {
  NOT = 0,
  DONE = 1,
}

const RiskRejectRecord = (props: RiskRejectRecordProps) => {
  const { orderNo, riskOrderNo } = props;

  const [supplementRecordList, setSupplementRecordList] = useState([]);

  const { data: callFeedbackList } = useRequest(() => {
    return getCallFailRecord({ riskOrderNo, status: FEEDBACK_STATUS.DONE });
  });

  const recordColumns = [
    {
      title: '电联记录',
      dataIndex: 'riskRefuseReason',
      render: (_, row) => {
        return JSON.parse(row.riskRefuseReason).join('、');
      },
    },
    {
      title: '电联失败反馈',
      dataIndex: 'remark',
    },
    {
      title: '反馈提交日期',
      dataIndex: 'updatedAt',
    },
  ];

  // 补件记录
  const initPatchRecord = () => {
    if (!orderNo) return;
    getSupplementRecord(orderNo).then((res) => {
      if (res && res?.data) {
        const { data } = res || [];
        const newDataList = data.map((item) => {
          const { riskEnterFieldList } = item;
          const newFieldList = riskEnterFieldList?.map((field) => {
            return {
              ...field,
              attribute: JSON.parse(field.attribute || ''),
            };
          });
          item.riskEnterFieldList = newFieldList;

          return item;
        });
        setSupplementRecordList(newDataList);
      }
    });
  };

  useEffect(() => {
    if (orderNo) {
      initPatchRecord();
    }
  }, [orderNo]);

  return (
    <>
      {supplementRecordList.map((item) => {
        if (item) {
          return (
            <ProCard
              key={item.supplementTime}
              title={`补件记录 ${dayjs(item.supplementTime).format('YYYY-MM-DD')}`}
              bordered
              headerBordered
              collapsible
              style={{ marginTop: 10 }}
            >
              <Form
                style={{ width: 800 }}
                initialValues={{ remark: item.remark }}
                labelWrap={true}
                layout="horizontal"
              >
                {item.riskEnterFieldList.map((field) => {
                  return (
                    <div key={field.fieldCode}>
                      <DisplayItem
                        label={field.attribute.fieldDesc}
                        name={field.fieldCode}
                        renderType={field.attribute.componentType}
                        options={field.attribute.fieldOptions}
                        readonly={true}
                        defaultValue={field.fieldValue}
                      />
                    </div>
                  );
                })}
                {item?.riskSupplementList?.map((img) => {
                  return (
                    <Form.Item label={img?.supplementDesc} key={img?.supplementDesc}>
                      <Space wrap>
                        {img?.supplementValueList?.map((url) => {
                          return (
                            <ImagePreview key={url} url={url} urlList={img?.supplementValueList}>
                              {isPdfUrl(url) ? (
                                <Tooltip title="点击预览PDF">
                                  <img
                                    width={64}
                                    height={64}
                                    src={PDF_PREVIEW_IMG}
                                    crossOrigin="anonymous"
                                  />
                                </Tooltip>
                              ) : (
                                <img
                                  width={64}
                                  height={64}
                                  src={url}
                                  style={{ objectFit: 'cover' }}
                                />
                              )}
                            </ImagePreview>
                          );
                        })}
                      </Space>
                    </Form.Item>
                  );
                })}
                <Form.Item label="备注">
                  <div>{item.remark}</div>
                </Form.Item>
              </Form>
            </ProCard>
          );
        }
      })}
      {callFeedbackList?.length > 0 && (
        <ProCard
          title="电联失败反馈记录"
          bordered
          headerBordered
          collapsible
          style={{ marginTop: 10 }}
        >
          <Table columns={recordColumns} dataSource={callFeedbackList} />
        </ProCard>
      )}
    </>
  );
};

export default RiskRejectRecord;
