import { debounce } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { CancelStatusEnum, DetailInfo, ListItem } from '../const';
import '../index.less';
import { queryBank, queryCancellationDetail } from '../service';
//
import Cancel from './Cancel';
import RefundConfirm from './RefundConfirm';
import RefundFail from './RefundFail';
import RefundLoading from './RefundLoading';
import RefundReject from './RefundReject';
import RefundSuccess from './RefundSuccess';
import WaitConfirm from './WaitConfirm';
import WaitProcess from './WaitProcess';
import WaitRefund from './WaitRefund';

// import { ModalFormProps } from '@ant-design/pro-form';
export type ModalProps = {
  closeModal: () => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  setModalInitLoading: (loading: boolean) => void;
  refreshTable: () => void;
};

const ModalContentWrapper: React.FC<ModalProps> = ({
  visible,
  closeModal,
  currentUser,
  record,
  setModalInitLoading,
  refreshTable,
}) => {
  const [detailInfo, setDetailInfo] = useState<DetailInfo>({} as DetailInfo);
  //
  // 公共银行搜索数据
  const debounceTimeout = 500;
  const [fetchBankListLoading, setFetchBankListLoading] = useState(false);
  const [asyncBankList, setAsyncBankList] = useState<any[]>();
  const [asyncBankListBankNoMap, setAsyncBankListBankNoMap] = useState<any>({});
  const fetchBankListRef = useRef(0);
  const handleSearchBank = useMemo(() => {
    const loadOptions = (value: string) => {
      const keyWords = value?.trim();
      console.log('value', value);
      fetchBankListRef.current += 1;
      const fetchId = fetchBankListRef.current;
      setAsyncBankList([]);
      setFetchBankListLoading(true);
      if (keyWords === '') {
        setFetchBankListLoading(false);
        return;
      }

      queryBank(keyWords).then((res: any) => {
        if (fetchId !== fetchBankListRef.current) {
          // for fetch callback order
          return;
        }
        setAsyncBankList(
          res?.data?.map((item: any) => {
            return {
              value: item?.bankNo,
              label: item?.subBranchName,
            };
          }),
        );
        const bankNoMap: any = {};
        res?.data?.forEach((item: any) => {
          bankNoMap[item?.bankNo] = item;
        });
        setAsyncBankListBankNoMap((prev: any) => {
          return {
            ...prev,
            ...bankNoMap,
          };
        });
        setFetchBankListLoading(false);
      });
    };

    return debounce(loadOptions, debounceTimeout);
  }, [queryBank, debounceTimeout]);
  useEffect(() => {
    if (!visible) {
      setAsyncBankListBankNoMap({});
    }
  }, [visible]);
  // 查询详情
  const queryDetail = async (bizNo: string) => {
    const res = (await queryCancellationDetail({ bizNo }).catch(() => {})) || {};
    //
    setDetailInfo(res?.data || {});
    // 设置客户收款支行回显选项
    const { userBankInfo, orderStatus } = res?.data || {};
    // 退款失败回显，客户银行信息
    if (
      orderStatus === CancelStatusEnum.REFUND_FAIL &&
      userBankInfo &&
      userBankInfo?.bankNumber &&
      userBankInfo?.subBranchName
    ) {
      const defaultOption = {
        label: userBankInfo?.subBranchName,
        value: userBankInfo?.bankNumber,
      };
      // handleSearchBank(userBankInfo?.subBranchName)
      setAsyncBankList([defaultOption]);
      setAsyncBankListBankNoMap((prev: any) => {
        return {
          ...prev,
          [userBankInfo?.bankNumber]: {
            bankNo: userBankInfo?.bankNumber,
            subBranchName: userBankInfo?.subBranchName,
            bankName: userBankInfo?.bankName,
            bankCode: userBankInfo?.bankCode,
          },
        };
      });
    }
  };
  //
  const bizNo = record?.bizNo;
  useEffect(() => {
    if (bizNo && visible) {
      setModalInitLoading(true);
      queryDetail(bizNo)
        .catch(() => {})
        .finally(() => setModalInitLoading(false));
    } else {
      setDetailInfo({} as DetailInfo);
      setAsyncBankList(undefined);
      setAsyncBankListBankNoMap({});
    }
  }, [bizNo, visible]);

  //
  // 关闭弹窗时，可选是否刷新列表
  const handleCloseModal = (needRefreshTable: boolean = false) => {
    if (needRefreshTable) {
      try {
        refreshTable();
      } catch (e) {
        console.log(e);
      }
    }
    //
    closeModal();
  };

  // 各个状态公共参数
  const modalProps = {
    closeModal: handleCloseModal,
    visible,
    currentUser,
    record,
    detailInfo,
  };
  // 银行搜索参数
  const bankSearchProps = {
    handleSearchBank,
    fetchBankListLoading,
    asyncBankList,
    asyncBankListBankNoMap,
  };

  //
  return (
    <>
      {/* 按照不同orderStatus: 1-9 展示不同内容 */}
      {/* orderStatus: CancelStatusEnum.WAIT_PROCESS = 1,  */}
      {detailInfo.orderStatus === CancelStatusEnum.WAIT_PROCESS && <WaitProcess {...modalProps} />}
      {/* orderStatus: CancelStatusEnum.WAIT_CONFIRM = 2 */}
      {detailInfo.orderStatus === CancelStatusEnum.WAIT_CONFIRM && <WaitConfirm {...modalProps} />}
      {/* orderStatus: CancelStatusEnum.WAIT_REFUND = 3 */}
      {detailInfo.orderStatus === CancelStatusEnum.WAIT_REFUND && (
        <WaitRefund {...modalProps} bankSearchProps={bankSearchProps} />
      )}
      {/* orderStatus: CancelStatusEnum.REFUND_CONFIRM = 4 */}
      {detailInfo.orderStatus === CancelStatusEnum.REFUND_CONFIRM && (
        <RefundConfirm {...modalProps} />
      )}
      {/* orderStatus: CancelStatusEnum.REFUND_REJECT = 5 */}
      {detailInfo.orderStatus === CancelStatusEnum.REFUND_REJECT && (
        <RefundReject {...modalProps} bankSearchProps={bankSearchProps} />
      )}
      {/* orderStatus: CancelStatusEnum.REFUND_SUCCESS = 6 */}
      {detailInfo.orderStatus === CancelStatusEnum.REFUND_SUCCESS && (
        <RefundSuccess {...modalProps} />
      )}
      {/* orderStatus: CancelStatusEnum.REFUND_FAIL = 7 */}
      {detailInfo.orderStatus === CancelStatusEnum.REFUND_FAIL && (
        <RefundFail {...modalProps} bankSearchProps={bankSearchProps} />
      )}
      {/* orderStatus: CancelStatusEnum.REFUND_LOADING = 8 */}
      {detailInfo.orderStatus === CancelStatusEnum.REFUND_LOADING && (
        <RefundLoading {...modalProps} />
      )}
      {/* orderStatus: CancelStatusEnum.CANCEL = 9 */}
      {detailInfo.orderStatus === CancelStatusEnum.CANCEL && <Cancel {...modalProps} />}
    </>
  );
};

export default ModalContentWrapper;
