import { exportRecord } from '@/components/AsyncExport/services';
import type { IexportRecord, ItaskCode } from '@/components/AsyncExport/types';
import {
  canDownloadStatus,
  exportStatusValueZnMap,
  taskCodeValueZnMap,
} from '@/components/AsyncExport/types'; // @ts-ignore
import type { ActionType } from '@ant-design/pro-components'; // @ts-ignore
import { ModalForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Radio, Tabs, Tag } from 'antd';
import dayjs from 'dayjs';
import qs from 'qs';
import React, { memo, useRef, useState } from 'react';

/**
 * 异步导出组件 -- 提交导出exportAsync, 在导出记录中下载
 * 支持导出勾选的 和 搜索条件下全部的
 */

type ExportTab = 'range' | 'record';

type Props = {
  getSearchDataTotal: () => Promise<number>; // 获取筛选条件的数据总量的函数
  getSearchParams: () => any; // 获取筛选的参数

  getSelectedTotal?: () => number; // 勾选的时候的数据量
  getSelectedParams?: () => any; // 勾选的时候 需要提交的参数

  taskCode: ItaskCode[]; // 允许导出的范围
  exportAsync: (searchParams: any) => Promise<any>; // 提交导出的接口

  trigger?: React.ReactElement; // 外面可能做一些判断 不去打开弹窗
};
const AsyncExport: React.FC<Props> = (props) => {
  const {
    getSearchDataTotal,
    getSearchParams,

    getSelectedParams,
    getSelectedTotal,

    taskCode,
    exportAsync,

    trigger,
  } = props;
  const paramsRef = useRef<{ searchParams?: any; selectedParams?: any }>({});

  const [searchDataTotal, setSearchDataTotal] = useState(0);
  const [selectedDataTotal, setSelectedDataTotal] = useState(0);
  const [exportRange, setExportRange] = useState('search');
  const recordActionRef = useRef<ActionType>();
  const [activeKey, setActiveKey] = useState<ExportTab>('range');

  return (
    <ModalForm
      width={1000}
      title="导出"
      onOpenChange={(val) => {
        if (val) {
          // 获取搜索参数
          const searchParams = getSearchParams();

          // 获取选中参数
          const selectedParams = getSelectedParams?.();
          paramsRef.current.searchParams = searchParams;
          paramsRef.current.selectedParams = selectedParams;

          // 获取搜索数据总量
          getSearchDataTotal()
            .then((num) => setSearchDataTotal(num))
            .catch(() => {});

          // 获取勾选数据总量
          const total = getSelectedTotal?.();
          if (total && total > 0) {
            // 如果有勾选的则以勾选的为准
            setExportRange('selected');
          } else {
            //
            setExportRange('search');
          }
          setSelectedDataTotal(total || 0);
        }
      }}
      trigger={<div>{trigger ? trigger : <Button type="primary">导出</Button>}</div>}
      onFinish={async () => {
        if (activeKey === 'record') {
          // 如果是导出记录 点击确定 则不做任何处理
          return true;
        }

        const { searchParams, selectedParams } = paramsRef.current;

        if (exportRange === 'selected') {
          if (!selectedDataTotal) {
            message.error('您还未选中数据');
            return false;
          } else {
            await exportAsync(selectedParams);
          }
        } else {
          if (searchDataTotal === 0) {
            message.error('数据总量为0');
            return false;
          }
          await exportAsync(searchParams);
        }
        message.success('导出中，稍后可在导出记录中下载导出文件');
        setActiveKey('record');
        setTimeout(() => {
          recordActionRef?.current?.reload();
        }, 2000);
        return false;
      }}
    >
      <Tabs
        onChange={(tabActiveKey) => {
          setActiveKey(tabActiveKey as ExportTab);
          if (tabActiveKey === 'record') {
            recordActionRef?.current?.reload();
          }
        }}
        activeKey={activeKey}
        items={[
          {
            label: '导出范围',
            key: 'range',
            children: (
              <Radio.Group
                onChange={(e) => {
                  setExportRange(e.target.value);
                }}
                value={exportRange}
              >
                <Radio value={'selected'} disabled={!selectedDataTotal}>
                  导出已选数据,共计{selectedDataTotal}条记录
                </Radio>
                <Radio value={'search'}>
                  导出筛选条件下的全量数据,共计{searchDataTotal}条记录,实际导出条数以导出文件为准
                </Radio>
              </Radio.Group>
            ),
          },
          {
            label: '导出记录',
            key: 'record',
            forceRender: true,
            children: (
              <ProTable<IexportRecord>
                search={false}
                actionRef={recordActionRef}
                request={async (params) => {
                  const { current = 1, pageSize = 10 } = params;
                  return exportRecord({ current, pageSize, taskCode });
                }}
                pagination={{
                  defaultPageSize: 10,
                }}
                columns={[
                  { dataIndex: 'totalCount', title: '数据量' },
                  {
                    dataIndex: 'taskCode',
                    title: '导出内容',
                    valueType: 'select',
                    valueEnum: taskCodeValueZnMap,
                  },
                  { dataIndex: 'submitDatetime', title: '开始时间' },
                  { dataIndex: 'finishDatetime', title: '结束时间' },
                  { dataIndex: 'exporterName', title: '操作人' },
                  {
                    dataIndex: 'status',
                    title: '状态',
                    valueType: 'select',
                    valueEnum: exportStatusValueZnMap,
                  },
                  {
                    dataIndex: 'status',
                    title: '操作',
                    render(_, record) {
                      try {
                        const { ossPath } = record;
                        let Expires = dayjs().valueOf() / 1000 + '10';
                        if (ossPath) {
                          const url = new URL(record.ossPath);
                          const query = qs.parse(url.search.slice(1));
                          Expires = query.Expires + '';
                        }
                        return (
                          <Button
                            download
                            href={record.ossPath}
                            type="link"
                            disabled={
                              !canDownloadStatus.includes(record.status) ||
                              dayjs().valueOf() / 1000 > Number(Expires)
                            }
                          >
                            下载
                          </Button>
                        );
                      } catch (error) {
                        return <Tag color="error">ossPath有误</Tag>;
                      }
                    },
                  },
                ]}
              />
            ),
          },
        ]}
      />
    </ModalForm>
  );
};

export default memo(AsyncExport);
