/*
 * @Author: your name
 * @Date: 2021-06-30 15:06:29
 * @LastEditTime: 2022-09-21 17:56:57
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/WithHold.tsx
 */
import globalStyle from '@/global.less';
import { getUuid } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { Button, Card } from 'antd';
import React, { useRef, useState } from 'react';
import type { WithHoldListItem } from '../data';
import { withHoldList } from '../service';
import { AddWithHoldModal } from './index';

const WithHold: React.FC<any> = (props) => {
  const { overDueAmount, hideCard } = props;
  const { overdueId } = history.location.query;
  // const { mapUserList } = useModel('userList');

  // const { data:Info } = useRequest(() => {
  //   return queryWithHoldInfo(overdueId);
  // });
  const columns: ProColumns<WithHoldListItem>[] = [
    {
      title: '代扣时间',
      dataIndex: 'withholdTime',
      key: 'withholdTime',
    },
    {
      title: '代扣金额',
      dataIndex: 'withholdMoney',
      render: (_, row) => {
        return (row.withholdMoney / 100).toFixed(2) || '-';
      },
    },
    {
      title: '代扣结果',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        1: '待受理',
        2: '已受理',
        3: '代扣成功',
        4: '代扣失败',
      },
    },
    {
      title: '代扣信息',
      dataIndex: 'reason',
      key: 'reason',
    },
    {
      title: '申请人',
      dataIndex: 'withholdOperator',
    },
  ];

  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  function contentElement() {
    return (
      <>
        <Button
          type="primary"
          onClick={() => {
            handleModalVisible(true);
          }}
          // disabled={props.isSettle===40}
          disabled={!props.isSettle || props.isSettle === 40 || props.isSettle === 50}
          className={globalStyle.mb10}
        >
          发起代扣
        </Button>
        <ProTable<WithHoldListItem>
          columns={columns}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey={getUuid}
          search={false}
          pagination={{ pageSize: 10 }}
          options={false}
          toolBarRender={false}
          // dataSource={callPayList}
          request={(params) => withHoldList({ ...params, overdueId })}
          // pagination={false}
        />
      </>
    );
  }

  return (
    <>
      {!hideCard && (
        <Card title="代扣记录" className={globalStyle.mt20}>
          {contentElement()}
        </Card>
      )}
      {hideCard && <>{contentElement()}</>}
      <AddWithHoldModal
        onOk={async () => {
          handleModalVisible(false);
          // refresh();
          actionRef?.current?.reload();
        }}
        onCancel={() => {
          handleModalVisible(false);
        }}
        overDueAmount={overDueAmount}
        onVisibleChange={handleModalVisible}
        modalVisible={modalVisible}
      />
    </>
  );
};

export default WithHold;
