import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { getRepayTypeEnum } from '@/services/enum';
import { filterProps } from '@/utils/tools';
import { disableFutureDate, downLoadExcel } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import { Button, DatePicker } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import type { DebtListItem, DebtListParams } from './data';
import { debtExport, queryDebt } from './service';

const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
  return (
    <DatePicker.RangePicker
      {...rest}
      // placeholder="请选择"
      className={globalStyle.w100}
      disabledDate={disableFutureDate}
    />
  );
};

const DebtList: React.FC<any> = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [exportLoading, setExportLoading] = useState(false);

  const getExport = (form: DebtListParams) => {
    setExportLoading(true);
    debtExport(filterProps(form))
      .then((res) => {
        downLoadExcel(res);
        setExportLoading(false);
      })
      .finally(() => {
        setExportLoading(false);
      });
  };
  const columns: ProColumns<DebtListItem>[] = [
    {
      title: '债转流水号',
      dataIndex: 'conversionNo',
    },
    {
      title: '应收账款金额（元）',
      dataIndex: 'accountsReceivable',
      search: false,
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
      // search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
    },
    {
      title: '出让方',
      dataIndex: 'loadFromName',
      valueType: 'select',
      request: getRepayTypeEnum,
      search: {
        transform: (value: any) => ({ loadFrom: value }),
      },
    },
    {
      title: '受让方',
      dataIndex: 'loadToName',
      valueType: 'select',
      request: getRepayTypeEnum,
      search: {
        transform: (value: any) => ({ loadTo: value }),
      },
    },
    {
      title: '发起时间',
      dataIndex: 'startingTime',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      render(dom, row) {
        return row?.startingTime || '-';
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          startingTimeStart: `${value[0].split(' ')[0]} 00:00:00`,
          startingTimeEnd: `${value[1].split(' ')[0]} 23:59:59`,
        }),
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      // valueType: 'dateTime',
      valueEnum: {
        1: '已发起',
        2: '已受理',
        3: '待放款',
        4: '放款成功',
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => (
        <>
          <Link to={`/businessMng/debt-detail?debtNo=${record.conversionNo}`}>查看详情</Link>
        </>
      ),
    },
  ];

  return (
    <>
      <PageContainer>
        <ProTable<DebtListItem>
          actionRef={actionRef}
          formRef={formRef}
          scroll={{ x: 'max-content' }}
          rowKey="conversionNo"
          request={(params) => queryDebt(filterProps(params))}
          columns={columns}
          dateFormatter="string"
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                loading={exportLoading}
                onClick={() => {
                  const {
                    startingTime,
                    loadFromName,
                    loadToName,
                    ...data
                  } = formRef?.current?.getFieldsValue();
                  let newForm = {
                    ...data,
                    loadFrom: loadFromName,
                    loadTo: loadToName,
                  };
                  if (startingTime?.length) {
                    const startingTimeStart = `${startingTime[0].format('YYYY-MM-DD')} 00:00:00`;
                    const startingTimeEnd = `${startingTime[1].format('YYYY-MM-DD')} 23:59:59`;
                    newForm = { ...data, startingTimeStart, startingTimeEnd };
                  }
                  getExport(newForm);
                }}
              >
                导出
              </Button>,
            ];
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'businessMng/debt-list'}>
      <DebtList />
    </KeepAlive>
  </>
);
