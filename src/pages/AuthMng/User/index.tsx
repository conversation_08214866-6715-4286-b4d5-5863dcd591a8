import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Col, Drawer, message, Row, Space, Tree, Typography } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useEffect, useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import { useAccess } from 'umi';
import { employeeTypeMap, statusMap } from '../const';
import '../index.less';
import { bindUserRoles, queryRoleList, queryUserList, queryUserRoleList } from '../service';

const { Paragraph } = Typography;
interface ListItem {
  pid: string;
  employeeName: string;
  phone?: string;
  employeeId: string;
}
const Page: React.FC<any> = () => {
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  // 当前抽屉编辑的数据
  const [currentDrawerRecord, setCurrentDrawerRecord] = useState<ListItem>();
  // 关闭抽屉
  // 获取所有角色
  const [roleList, setRoleList] = useState<any[]>([]);
  //
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const onExpand = (expandedKeysValue: string[]) => {
    console.log('onExpand', expandedKeysValue);
    setExpandedKeys(expandedKeysValue);
  };
  //
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const onCheck = (checkedKeysValue: any) => {
    console.log('onCheck', checkedKeysValue);
    setCheckedKeys(checkedKeysValue?.checked);
  };
  const getAllRoleInfo = async () => {
    const list = await queryRoleList({ current: 1, pageSize: 200 }).catch(() => ({}));
    setRoleList(list?.data);
    return list?.data || [];
  };
  const getRoleInfo = async (pid: string) => {
    await getAllRoleInfo().catch(() => []);
    const checkedList = await queryUserRoleList(pid).catch(() => []);
    const checkedRoleList = checkedList?.data?.roleList?.map((value: any) => value.roleCode);
    setCheckedKeys(checkedRoleList);
    setExpandedKeys(checkedRoleList);
  };
  const [open, setOpen] = useState(false);
  const handleClose = () => {
    setCurrentDrawerRecord(undefined);
    setCheckedKeys([]);
    setOpen(false);
  };
  const handleOpen = async (record: ListItem) => {
    console.log(record);
    setCurrentDrawerRecord(record);
    await getRoleInfo(record?.pid);
    setOpen(true);
  };
  const [sumbitLoading, setSumbitLoading] = useState(false);
  // 确定操作
  const saveAction = async () => {
    setSumbitLoading(true);
    const { employeeId } = (currentDrawerRecord as ListItem) || {};
    const roleCodeList: any = checkedKeys;
    const res: any =
      (await bindUserRoles({
        employeeId,
        roleCodes: roleCodeList,
      }).catch(() => {})) || {};
    if (res?.success === true) {
      message.success('绑定成功');
      actionRef.current?.reload();
      handleClose();
    }
    setSumbitLoading(false);
  };
  // 弹窗
  // const [isEdit, setIsEdit] = useState(false);
  // const [visible, setVisible] = useState(false);
  // const [listItem, setListItem] = useState<ListItem>();
  // const closeModal = () => {
  //   setVisible(false);
  // };
  // const openModal = (isEdit: boolean, item?: ListItem) => {
  //   setIsEdit(isEdit);
  //   setListItem(undefined);
  //   if (isEdit) {
  //     setListItem(item);
  //   }
  //   //
  //   setVisible(true);
  // };
  // 表格列
  const columns: ProColumns<ListItem>[] = [
    {
      title: 'PID',
      dataIndex: 'pid',
      width: 300,
      search: false,
      // search: {
      //   transform: (value: string) => {
      //     return {
      //       pidList: value ? [value?.trim()] : [],
      //     };
      //   },
      // },
    },
    {
      title: '员工姓名',
      dataIndex: 'employeeName',
      search: {
        transform: (value: string) => {
          return {
            employeeName: value?.trim(),
          };
        },
      },
    },
    // {
    //   title: '手机号',
    //   dataIndex: 'phone',
    //   width: 200,
    // },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: statusMap,
    },
    {
      title: '员工类型',
      dataIndex: 'employeeType',
      valueEnum: employeeTypeMap,
      search: false,
    },
    {
      title: '角色',
      dataIndex: 'roleList',
      search: {
        transform: (value: string) => {
          return {
            roleCodes: value ? [value?.trim()] : [],
          };
        },
      },
      valueType: 'select',
      request: async () => {
        const list = await getAllRoleInfo().catch(() => []);
        console.log('list', list);
        return (
          list?.map((item: any) => ({
            label: `${item.roleName}(${item.roleCode})`,
            value: item.roleCode,
          })) || []
        );
      },
      width: 300,
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 160,
      hideInTable: !access.hasAccess('bind_employee_manage'),
      render: (_, record) => (
        <>
          <Row>
            {access.hasAccess('bind_employee_manage') && (
              <Col span={8}>
                <Button type="link" onClick={() => handleOpen(record)}>
                  绑定角色
                </Button>
              </Col>
            )}
          </Row>
        </>
      ),
    },
  ];
  //
  useEffect(() => {
    getAllRoleInfo();
  }, []);

  return (
    <>
      <PageContainer className={globalStyle.mt16}>
        <ProTable<ListItem>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="pid"
          scroll={{ x: 'max-content' }}
          request={(params: any) => {
            console.log(params);
            return queryUserList(params);
          }}
          search={{
            defaultCollapsed: false,
            labelWidth: 90,
          }}
          columns={columns}
          // toolBarRender={() => {
          //   return [
          //     <Button key="button" type="primary" onClick={() => openModal(false)}>
          //       新建
          //     </Button>,
          //   ];
          // }}
        />
      </PageContainer>
      {/* 抽屉 */}
      <Drawer
        title={`绑定角色`}
        placement="right"
        size="large"
        onClose={handleClose}
        open={open}
        extra={
          <Space>
            {/* <Button onClick={handleClose}>关闭</Button> */}
            <Button onClick={handleClose}>取消</Button>
            <Button type="primary" loading={sumbitLoading} onClick={saveAction}>
              保存
            </Button>
          </Space>
        }
      >
        <div className="tree-wrapper">
          <div className="tree-header">
            <span className="tree-title">当前用户：</span>
            {currentDrawerRecord?.employeeName}({currentDrawerRecord?.pid})
          </div>
          <div className="tree-body">
            <Tree
              checkable
              selectable={false}
              checkStrictly={true}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              onCheck={onCheck}
              checkedKeys={checkedKeys}
              treeData={roleList}
              fieldNames={{ title: 'roleName', key: 'roleCode', children: 'children' }}
              titleRender={(node: any) => {
                return (
                  <Paragraph copyable={{ text: node.roleCode }} style={{ marginBottom: 0 }}>
                    {`${node.roleName}(${node.roleCode})`}
                  </Paragraph>
                );
              }}
            />
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'/authMng/user'}>
      <Page />
    </KeepAlive>
  </>
);
