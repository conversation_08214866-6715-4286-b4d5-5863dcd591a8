/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2021-07-22 10:15:09
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/enterpriseMng/service.ts
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from 'umi';
import type { StoreConfigListParams } from './data';

// 获取用户管理列表数据
export async function getCarStoreList(params?: StoreConfigListParams) {
  return request('/bizadmin/lease/carStore/pageList', {
    method: 'GET',
    params: { ...params },
    headers: bizAdminHeader,
  });
}

// export async function getWitholdConfig(productCode: string) {
//   return request(`/loan/product/detail/${productCode}`);
// }

export async function addCarStore(storeName: string) {
  return request('/bizadmin/lease/carStore/add', {
    method: 'POST',
    data: { storeName },
    headers: bizAdminHeader,
  });
}
// 删除车型库
export async function delCarStore(id: string) {
  return request(`/bizadmin/lease/carStore/delete/${id}`, {
    method: 'DELETE',
    headers: bizAdminHeader,
  });
}
// 修改车型库
export async function modifyCarStore(data: { id?: number; status?: number; storeName?: string }) {
  return request(`/bizadmin/lease/carStore/modify`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}
