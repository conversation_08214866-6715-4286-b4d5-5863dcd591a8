/* eslint-disable no-param-reassign */
import { CommonImageUpload } from '@/components/ReleaseCom';
import { convertUploadFileList } from '@/utils/utils';
import ProForm, {
  ModalForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-form';
import { history } from '@umijs/max';
import type { FormInstance } from 'antd';
import { Card, message } from 'antd';
import dayajs from 'dayjs';
import React, { memo, useRef, useState } from 'react';
import { offlineRepay } from '../services';
import type { IgetIncommingInfo, IofflineRepayParams, IspecialCarPolicyRepayItem } from '../types';
import { carPolicyRepayModeMap } from '../types';
import { isExist } from '../utils';
import RepayAmountDetail from './RepayAmountDetail';
import SpecialCarTable from './SpecialCarTable';

// import { EcarStatus } from '@/utils/bankend/enum';
// import { EcarStatus } from '@/utils/bankend/enum';
type Props = {
  setOfflineRepayOpen: (val: boolean) => void;
  offlineRepayOpen: boolean;
  incomingInfo: IgetIncommingInfo;
};
const OfflineRepayModal: React.FC<Props> = (props) => {
  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  const [repayAmount, setRepayAmount] = useState<null | undefined | number>();
  const [allCarRepayInfo, setAllCarRepayInfo] = useState<any>([]);
  const [specialCarRepayInfo, setSpecialCarRepayInfo] = useState([]);
  const { orderNo } = (history as any).location.query;
  const { offlineRepayOpen, setOfflineRepayOpen } = props;
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };
  const formRef = useRef<FormInstance>();
  /**
   * @param specialCarPolicyRepayList 特殊处理的车辆信息
   * @returns {boolean} true 通过校验 false 校验有误
   */
  function verifySpecialCarInfo(specialCarPolicyRepayList: IspecialCarPolicyRepayItem[]): boolean {
    if (specialCarPolicyRepayList?.length) {
      // 只要加了一行 那么这一行就是必填的
      for (let i = 0; i < specialCarPolicyRepayList?.length; i++) {
        const {
          repaySchemaItemNo,
          isPreSettle,
          repayAmount: repayAmount1,
        } = specialCarPolicyRepayList[i];
        if (!repaySchemaItemNo) {
          message.error('车辆是必填的');
          return false;
        }
        if (!isPreSettle) {
          message.error('是否提前结清是必填的');
          return false;
        }
        if ([undefined, null, ''].includes(repayAmount1 as any) || (repayAmount1 as number) <= 0) {
          message.error('还款金额是必填的且大于0');
          return false;
        }
        // const oneCar = allCarRepayInfo.find((item: any) => item.schemaItemNo === repaySchemaItemNo);
        // const { licensePlateNo } = oneCar || {};
        // const amount = oneCar?.carPolicyTermRspBOS?.reduce((pre: number, cur: any) => {
        //   const {
        //     status,
        //     remainingPrincipal,
        //     remainingInterest,
        //     overdueInterest,
        //     overdueLatePaymentFee,
        //   } = cur;

        //   if ([EcarStatus.OVERDUE, EcarStatus.PENDING_REPAYMENT].includes(status)) {
        //     pre =
        //       pre +
        //       Number(remainingPrincipal) +
        //       Number(remainingInterest) +
        //       Number(overdueInterest) +
        //       +Number(overdueLatePaymentFee);
        //   }
        //   return pre;
        // }, 0);
        // const amount1 = Number(amount.toFixed(2));
        // if (repayAmount1 > amount1 && (isPreSettle as any) == 0) {
        //   message.error(`车牌号-${licensePlateNo}的还款金额不能超过¥${amount.toFixed(2)}`);
        //   return false;
        // }
      }
      return true;
    }
    // 说明没选这个 或者选了没加数据
    return true;
  }
  const onFinish = async (values: IofflineRepayParams) => {
    // 校验特殊车辆信息 如果数空或者空数组说明没有添加数据 只要添加了数据那所有项就是必填的
    if (verifySpecialCarInfo(values.specialCarPolicyRepayList)) {
      const mapUploadFile = convertUploadFileList(allFileList, ['attach']);
      const { userNo, userName } = props.incomingInfo;
      const params = {
        ...values,
        specialCarPolicyRepayList: values.specialCarPolicyRepayList.map((item) => ({
          ...item,
          id: undefined,
          remissionDetailList: item.remissionDetailList?.map((item1) => ({
            ...item1,
            id: undefined,
          })),
        })),
        ...mapUploadFile,
        //  attach,
        accountName: userName,
        accountNumber: userNo,
        orderNo,
      };
      console.log('params', params);
      await offlineRepay(params);
      message.success('操作成功');
      setOfflineRepayOpen(false);
    }
  };

  return (
    <ModalForm<IofflineRepayParams>
      title="线下还款"
      visible={offlineRepayOpen}
      width={1000}
      onVisibleChange={() => {
        handleFileList({});
      }}
      layout="horizontal"
      onFinish={onFinish}
      modalProps={{
        destroyOnClose: true,
        onCancel: () => {
          setOfflineRepayOpen(false);
        },
      }}
    >
      <Card title="还款信息" size="small">
        <ProForm.Group>
          <ProFormSelect
            width="sm"
            name="actualRepayRole"
            label="实际还款方"
            placeholder="请选择"
            valueEnum={{
              1: '用户本人',
              2: '第三方',
            }}
            rules={[{ required: true, message: '这是必填项' }]}
          />
          <ProFormDatePicker
            name="repayDate"
            label="还款日期"
            placeholder="请输入"
            rules={[{ required: true, message: '这是必填项' }]}
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormRadio.Group
            name="isAllowJumpTerm"
            disabled
            rules={[{ required: true }]}
            label="是否支持跳期还款"
            initialValue={false}
            options={[
              {
                label: '是',
                value: true,
              },
              {
                label: '否',
                value: false,
              },
            ]}
          />
          <ProFormDependency name={['actualRepayRole']}>
            {/* 实际还款方为第三方时需要展示 */}
            {({ actualRepayRole }) => {
              return actualRepayRole === '2' ? (
                <ProFormText
                  name="compensateOrgName"
                  label="代偿第三方名称"
                  labelCol={{ span: 11 }}
                  placeholder="请输入"
                  rules={[{ required: true, message: '这是必填项' }]}
                />
              ) : (
                <></>
              );
            }}
          </ProFormDependency>
        </ProForm.Group>
      </Card>
      <Card
        title={
          <div>
            还款金额 &nbsp;&nbsp;&nbsp;
            <a>¥&nbsp;{isExist(repayAmount) ? repayAmount?.toFixed(2) : '-'}</a>
            &nbsp;&nbsp;&nbsp;<span style={{ color: '#ccc' }}>等于实际金额+减免金额</span>
          </div>
        }
        extra={
          <RepayAmountDetail
            allCarRepayInfo={allCarRepayInfo}
            specialCarRepayInfo={specialCarRepayInfo}
          />
        }
        size="small"
      >
        <ProForm.Group>
          <ProFormSelect
            width="lg"
            name="carPolicyRepayMode"
            label="还款方式"
            placeholder="请选择"
            valueEnum={carPolicyRepayModeMap}
            rules={[{ required: true, message: '这是必填项' }]}
          />
        </ProForm.Group>
        <ProFormDependency name={['carPolicyRepayMode', 'repayDate']}>
          {/* 还款方式为2 和 3时需要展示 */}
          {({ carPolicyRepayMode, repayDate }) => {
            console.log('carPolicyRepayMode', carPolicyRepayMode);
            const display = !carPolicyRepayMode || carPolicyRepayMode === '1' ? 'none' : 'block';
            return (
              <div style={{ display }}>
                <ProForm.Item name="specialCarPolicyRepayList">
                  <SpecialCarTable
                    formRef={formRef}
                    setRepayAmount={setRepayAmount}
                    repayDate={dayajs(repayDate).format('YYYY-MM-DD')}
                    carPolicyRepayMode={carPolicyRepayMode}
                    setAllCarRepayInfo={setAllCarRepayInfo}
                    setSpecialCarRepayInfo={setSpecialCarRepayInfo}
                  />
                </ProForm.Item>
              </div>
            );
          }}
        </ProFormDependency>
      </Card>

      <Card title="还款凭证" size="small">
        <CommonImageUpload
          extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
          label="上传附件"
          name="attach"
          rules={[{ required: true }]}
          // labelCol={{ span: 5 }}
          max={5}
          listType="text"
          size={10}
          fileListEdit={allFileList?.attach || []}
          desPath="EP_AUTH_INFO"
          mapFileList={mapFileList}
          accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
        />
      </Card>
    </ModalForm>
  );
};

export default memo(OfflineRepayModal);
