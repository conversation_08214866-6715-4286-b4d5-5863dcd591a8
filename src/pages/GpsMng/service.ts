import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { CancelGpsOrderParams, GpsCreateOrderParams, GpsOrderListParams } from './type';

// 查询GPS默认信息
export function getGpsDefaultData(orderNo?: string) {
  return request('/bizadmin/lease/gpsOrder/queryDefaultData', {
    params: { orderNo },
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 创建GPS工单
export function createGpsOrder(data: GpsCreateOrderParams) {
  return request('/bizadmin/lease/gpsOrder/createOrder', {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 查询订单的GPS工单
export function getGpsOrderByOrderNo(params: { orderNo: string; orderType: string }) {
  return request('/bizadmin/lease/gpsOrder/queryGpsOrderList', {
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 查询GPS工单列表
export function getGpsOrderList(params: GpsOrderListParams) {
  return request('/bizadmin/lease/gpsOrder/listPage', {
    params,
    headers: {
      ...bizAdminHeader,
    },
    ifTrimParams: true,
  });
}

// 查询GPS工单详情
export function getGpsOrderDetial(gpsOrderNo: string) {
  return request(`/bizadmin/lease/gpsOrder/queryGpsOrderDetail/${gpsOrderNo}`, {
    headers: {
      ...bizAdminHeader,
    },
    skipGlobalErrorTip: true,
  });
}

// 取消gps安装
export function cancelGpsOrder(data: CancelGpsOrderParams) {
  return request('/bizadmin/lease/gpsOrder/cancelOrder', {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

export function exportList(params: GpsOrderListParams) {
  return request(`/bizadmin/lease/gpsOrder/export`, {
    params,
    responseType: 'blob',
    getResponse: true,
    ifTrimParams: true,
    headers: {
      ...bizAdminHeader,
    },
  });
}
