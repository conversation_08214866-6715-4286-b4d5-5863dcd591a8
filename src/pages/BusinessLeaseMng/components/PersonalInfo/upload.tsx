import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import type { ProFormInstance } from '@ant-design/pro-components';
import type { UploadProps } from 'antd';
import { message, Modal, Upload } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { asyncReadFileToDataUrl, compressImg, isImageFile, readDataUrlToImg } from './utils';

type UploadImageProps = {
  disabled: boolean;
  value?: string;
  onUploadChange?: (url: string) => void;
  data: any[];
  form?: ProFormInstance;
};

const MAX_SIZE = 10 * 1024 * 1024; // 图片限制大小10M

const baseUrl = getBaseUrl();

const UploadImage = (props: UploadImageProps) => {
  const { onUploadChange, disabled, value, data, form } = props;

  const [fileList, setFileList] = useState<any[]>([]);
  const [previewObj, handlePreviewObj] = useState<any>();

  useEffect(() => {
    if (value) {
      if (value !== fileList[0]?.response?.data?.netWorkPath) {
        setFileList([{ url: value }]);
      }
    } else {
      setFileList([]);
    }
  }, [value]);

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传</div>
    </button>
  );

  const handleChange: UploadProps['onChange'] = async (info) => {
    // if (info.file.status === 'uploading') {
    //   setLoading(true);
    //   return;
    // }
    setFileList(info.fileList);
    // 删除后要通知父组件清空表单
    if (!info.fileList.length) {
      onUploadChange?.('');
    }
    if (info.file.status === 'done') {
      const fileUrl = info.file.response?.data?.netWorkPath;
      if (fileUrl) {
        info.file.url = fileUrl;
        onUploadChange?.(fileUrl);
      }
    }
  };

  const beforeUpload = async (file: any) => {
    if (!isImageFile(file)) {
      message.warning('请选择图片文件!');
      return false;
    }
    try {
      const dataUrl: string = await asyncReadFileToDataUrl(file);
      if (dataUrl) {
        const img = await readDataUrlToImg(dataUrl);
        const blob = await compressImg(img, file.type, 1920, 1080);
        if (blob) {
          const imgFile = new File([blob], file.name, { type: file.type });
          if (imgFile.size > MAX_SIZE) {
            message.warning('超出图片大小10M限制!');
            return false;
          }
          return imgFile;
        }
      }
      return file;
    } catch (error) {
      return false;
    }
  };
  const imagePreviewRef = useRef<ImagePreviewInstance>(null);
  const handlePreview = async (file: any) => {
    const values = form?.getFieldsValue();
    imagePreviewRef.current?.previewFile({
      url: file.url,
      urlList: data.map((item) => values[item?.fieldCode]),
    });
  };

  return (
    <>
      <Upload
        disabled={disabled}
        maxCount={1}
        accept=".pdf,.png,.jpg,.jpeg,.bmp"
        action={`${baseUrl}/repayment/oss/common/uploadfile`}
        name="file"
        beforeUpload={beforeUpload}
        onChange={handleChange}
        multiple={false}
        listType="picture-card"
        headers={{ ...getAuthHeaders() }}
        data={{ acl: 'PUBLIC_READ', destPath: 'USER_ORDER_APPLY_MONEY' }}
        onPreview={handlePreview}
        fileList={fileList}
      >
        {fileList.length >= 1 ? null : uploadButton}
      </Upload>
      <ImagePreview ref={imagePreviewRef} />
      <Modal
        open={previewObj?.previewVisible}
        title={previewObj?.previewTitle}
        footer={null}
        onCancel={() => {
          handlePreviewObj({ ...previewObj, previewVisible: false });
        }}
      >
        <img alt="该文件不支持预览" width={'100%'} src={previewObj?.previewImage} />
      </Modal>
    </>
  );
};

export default UploadImage;
