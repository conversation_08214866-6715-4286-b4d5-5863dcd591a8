import ImagePreview, { ImagePreviewInstance } from '@/components/ImagePreview';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, message, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import React, { useEffect, useRef, useState } from 'react';
import './UploadImagePannel.less';

// const beforeUpload = (file: RcFile) => {
//   const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
//   if (!isJpgOrPng) {
//     message.error('You can only upload JPG/PNG file!');
//   }
//   const isLt2M = file.size / 1024 / 1024 < 2;
//   if (!isLt2M) {
//     message.error('Image must smaller than 2MB!');
//   }
//   return isJpgOrPng && isLt2M;
// };

type Props = {
  onChange?: (filePath: string) => void;
  disabled?: boolean;
  defaultFile?: any;
};
const UploadImage: React.FC<Props> = (props) => {
  const { onChange, disabled = false, defaultFile } = props;
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const [isPdf, setIsPdf] = useState<boolean>(false);
  const handleChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      const { code, data = {} } = info.file.response || {};
      const { filePath, netWorkPath, errMsg } = data;
      if (code === 200) {
        setIsPdf(info.file?.type === 'application/pdf' ? true : false);
        setImageUrl(netWorkPath);
        onChange?.(filePath);
        message.success('上传成功');
      } else {
        message.error('上传有误' + errMsg);
      }
      setLoading(false);
    }
  };
  useEffect(() => {
    if (defaultFile) {
      console.log('defaultFile', defaultFile);
      try {
        if (defaultFile?.includes('.pdf')) {
          setIsPdf(true);
        } else {
          setIsPdf(false);
        }
        setImageUrl(defaultFile);
      } catch (e) {
        console.log('error', e);
      }
    }
  }, [defaultFile]);

  function beforeUpload(file: RcFile) {
    const format = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
    if (!format.includes(file?.type)) {
      message.error('格式为仅支持.pdf,.png,.jpg,.jpeg');
      return false || Upload.LIST_IGNORE;
    } else if (file?.size > 5 * 1024 * 1024) {
      message.error('图片需小于5M');
      return false || Upload.LIST_IGNORE;
    } else {
      return true;
    }
  }
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      {/* <div style={{ marginTop: 8 }}>Upload</div> */}
    </div>
  );
  const baseUrl = getBaseUrl();
  const action = baseUrl ? baseUrl : '';
  //
  const previewRef = useRef<ImagePreviewInstance>();
  const handleUploadPreview = async (url: any, urlList: string[]) => {
    if (url) {
      previewRef.current?.previewFile({
        url,
        urlList,
      });
    }
  };
  //
  return (
    <>
      <Upload
        listType="picture-card"
        className="avatar-uploader"
        showUploadList={false}
        action={`${action}/base/oss/common/uploadfile`}
        // action={`/repayment/repay/uploadRepayPic`}
        headers={{ ...getAuthHeaders() }}
        disabled={disabled}
        name="file"
        accept=".pdf,.png,.jpg,.jpeg"
        data={{
          destPath: 'insurance/policy/vehicleLicense/',
          acl: 'PUBLIC_READ',
          attachment: false,
        }} // 后端商量默认格式
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        {imageUrl ? (
          isPdf ? (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                handleUploadPreview(imageUrl, [imageUrl]);
              }}
            >
              预览该PDF文件
            </Button>
          ) : (
            <img
              src={imageUrl}
              title="重新上传"
              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            />
          )
        ) : (
          uploadButton
        )}
      </Upload>
      <ImagePreview ref={previewRef as any} />
    </>
  );
};

export default UploadImage;
