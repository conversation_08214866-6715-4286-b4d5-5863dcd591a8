/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-shadow */
// 申请进件表单
import HeaderTab from '@/components/HeaderTab/index';
import { ProTable } from '@ant-design/pro-components';
import { ProFormTextArea } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import { useRequest, useSearchParams } from '@umijs/max';
import { Button, Card, Col, Form, Modal, Row, Space, Spin, Tabs, Typography } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import CarInfo from './components/CarInfo';
import PersonalInfo from './components/PersonalInfo';
import RiskReject from './components/RiskReject';
import { INCOME_STATUS, ORDER_STATUS } from './consts';
import {
  getCallFailRecord,
  getSupplementRecord,
  orderOperateLog,
  queryOrderDetail,
} from './service';

const { Paragraph } = Typography;

enum ACTION_MODE {
  NEW = 'new', // 用户新增
  EDIT = 'edit', // 修改某个模块
  ADD = 'add', // 补充字段
  VIEW = 'view', // 查看，不能编辑
}

//反馈信息状态 0-未反馈 1-已反馈
enum FEEDBACK_STATUS {
  NOT = 0,
  DONE = 1,
}

enum TAB {
  CAR = 'car', // 车辆信息和租赁方案
  PERSOANL = 'persoanl', // 个人信息
  RISK_REJECT = 'riskReject', // 风控驳回
}
let tabCahce = TAB.CAR;

const LeaseComeForm = () => {
  const [searchParams] = useSearchParams();
  const orderNo = searchParams.get('orderNo') as string;
  const userNo = searchParams.get('userNo') as string;
  // const { orderNo, userNo }: any = history.location.query;
  const [incomeStatus, setIncomeStatus] = useState();
  const [carReadOnly, setCarReadOnly] = useState(false);
  const [schemeReadOnly, setSchemeReadOnly] = useState(false);
  const [personalReadOnly, setPersonalReadOnly] = useState(false);
  const [supplementReadOnly, setSupplementReadOnly] = useState(false);
  const [callFailReadOnly, setCallFailReadOnly] = useState(false);

  const [curTab, setCurTab] = useState(tabCahce);
  const [tabItems, setTabItems] = useState<any>([]);
  const [form] = Form.useForm();
  const [title, setTitle] = useState('');
  const carInfoRef = useRef<any>(null);
  const personalRef = useRef<any>(null);
  const riskRejectRef = useRef<any>(null);
  const operateActionRef = useRef<any>(null);

  const { data: detailInfo, run: refreshOrderDetail, loading } = useRequest(() => {
    return queryOrderDetail(orderNo);
  });

  const columns = [
    {
      title: '操作',
      dataIndex: 'operateNode',
      width: 100,
    },
    {
      title: '审批意见',
      dataIndex: 'operateResult',
      width: 100,
    },
    {
      title: '审批备注',
      dataIndex: 'remark',
      ellipsis: true,
      // render: (value: string) => (
      //   <Tooltip title={value}>
      //     <div style={{ overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
      //       {value}
      //     </div>
      //   </Tooltip>
      // ),
    },
    {
      title: '操作账号',
      dataIndex: 'operatedBy',
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'operatedAt',
      width: 120,
    },
  ];

  // 提交金融方案后更新详情数据，获取到productCode
  const refreshPage = () => {
    refreshOrderDetail();
  };

  const onRiskRejectSubmit = () => {
    setSupplementReadOnly(true);
    setCallFailReadOnly(true);
  };

  // 修改车辆信息和金融方案后，切换到个人信息tab
  const goNextStep = () => {
    tabCahce = TAB.PERSOANL;
    refreshOrderDetail();
  };

  const initTabs = async (detailInfo) => {
    if (detailInfo) {
      let items = [];
      const { orderStatus, incomeStatus } = detailInfo;
      let carReadOnly = true;
      let schemeReadOnly = true;
      let personalReadOnly = true;
      let supplementReadOnly = true;
      let callFailReadOnly = true;

      let actionMode = ACTION_MODE.VIEW; // 个人信息操作类型，默认查看

      // 签约完成，新进件状态
      if (orderStatus === ORDER_STATUS.SIGN_DONE) {
        actionMode = ACTION_MODE.NEW;
        // 已经完成选车
        if (detailInfo.productCode) {
          tabCahce = TAB.PERSOANL;
          carReadOnly = true;
          personalReadOnly = false;
          schemeReadOnly = true;
          callFailReadOnly = true;
        } else {
          tabCahce = TAB.CAR;
          carReadOnly = false;
          personalReadOnly = false;
          schemeReadOnly = false;
          callFailReadOnly = false;
        }
      }

      // 预审通过状态可以修改进件信息
      if (orderStatus === ORDER_STATUS.PRE_AUDIT_PASS) {
        actionMode = ACTION_MODE.EDIT;
        carReadOnly = false;
        schemeReadOnly = false;
        personalReadOnly = false;
      }

      // 进件状态=重新选车
      if (incomeStatus === INCOME_STATUS.RESELECT_VEHICLE) {
        carReadOnly = false;
        personalReadOnly = true;
        //
        schemeReadOnly = false;
      }

      // 进件状态=重新选金融方案
      if (incomeStatus === INCOME_STATUS.RESELECT_LEASE_PROGRAM) {
        schemeReadOnly = false;
        personalReadOnly = true;
      }

      // 进件状态=方案待调整
      if (incomeStatus === INCOME_STATUS.RECONFIRM_LEASE_PLAN) {
        schemeReadOnly = false;
        personalReadOnly = true;
      }

      // 进件状态=退回补件
      if (
        [INCOME_STATUS.ADDITIONAL_MATERIALS, INCOME_STATUS.ADDITIONAL_MATERIALS_AGAIN].includes(
          incomeStatus,
        )
      ) {
        supplementReadOnly = false;
      }

      //  进件状态=电联失败
      if (incomeStatus === INCOME_STATUS.CALL_FAIL) {
        callFailReadOnly = false;
      }

      const carItem = {
        label: '车辆租赁方案',
        key: TAB.CAR,
        children: (
          <CarInfo
            ref={carInfoRef}
            orderNo={orderNo}
            userNo={userNo}
            userName={detailInfo?.userName}
            orderStatus={orderStatus}
            incomeStatus={incomeStatus}
            carReadOnly={carReadOnly}
            schemeReadOnly={schemeReadOnly}
            remarkForm={form}
            onSubmit={goNextStep}
          />
        ),
      };
      const personalItem = {
        label: '个人信息',
        key: TAB.PERSOANL,
        children: (
          <PersonalInfo
            ref={personalRef}
            orderNo={orderNo}
            actionMode={actionMode}
            readOnly={personalReadOnly}
            remarkForm={form}
            productCode={detailInfo?.productCode}
            onSubmit={refreshPage}
          />
        ),
      };
      // const supplementItem = {
      //   label: '补充资料',
      //   key: TAB.SUPPLEMENT,
      //   children: (
      //     <AddSupplement
      //       ref={addSupplementRef}
      //       orderNo={orderNo}
      //       riskOrderNo={detailInfo?.riskOrderNo}
      //       isLook={supplementReadOnly}
      //       onSubmit={onSupplementSubmit}
      //     />
      //   ),
      // };

      const rejectItem = {
        label: '风控驳回',
        key: TAB.RISK_REJECT,
        children: (
          <RiskReject
            ref={riskRejectRef}
            orderNo={orderNo}
            riskOrderNo={detailInfo?.riskOrderNo}
            incomeStatus={incomeStatus}
            supplementReadOnly={supplementReadOnly}
            callFailReadOnly={callFailReadOnly}
            onSubmit={onRiskRejectSubmit}
          />
        ),
      };

      switch (incomeStatus) {
        case INCOME_STATUS.RESELECT_LEASE_PROGRAM: // 重新选方案
        case INCOME_STATUS.RESELECT_VEHICLE: // 重新选车
        case INCOME_STATUS.RECONFIRM_LEASE_PLAN: // 重新确认方案
          tabCahce = TAB.CAR;
          items = [carItem, personalItem];
          setTitle('修改租赁计划');
          break;
        case INCOME_STATUS.ADDITIONAL_MATERIALS: // 补充资料
        case INCOME_STATUS.ADDITIONAL_MATERIALS_AGAIN: // 再次补充资料
        case INCOME_STATUS.CALL_FAIL: // 电联失败
          tabCahce = TAB.RISK_REJECT;
          items = [carItem, personalItem, rejectItem];
          setTitle('风控驳回');
          break;
        default:
          // 已经选好方案才会有productCode
          if (detailInfo?.productCode) {
            tabCahce = TAB.PERSOANL;
            items = [carItem, personalItem];
          } else {
            tabCahce = TAB.CAR;
            items = [carItem];
          }
          // 判断是否有补件记录
          const supplementRecord = await getSupplementRecord(orderNo);
          // 电联反馈记录
          let callFailRecord = {};
          if (detailInfo?.riskOrderNo) {
            callFailRecord = await getCallFailRecord({
              riskOrderNo: detailInfo?.riskOrderNo,
              status: FEEDBACK_STATUS.DONE,
            });
          }
          if (supplementRecord?.data?.length || callFailRecord?.data?.length) {
            rejectItem.label = '风控驳回记录';
            items.push(rejectItem);
          }
          setTitle('申请进件');
          break;
      }

      setCurTab(tabCahce);
      setIncomeStatus(incomeStatus);

      setCarReadOnly(carReadOnly);
      setSchemeReadOnly(schemeReadOnly);
      setPersonalReadOnly(personalReadOnly);
      setSupplementReadOnly(supplementReadOnly);
      setCallFailReadOnly(callFailReadOnly);

      setTabItems([...items]);
      if (detailInfo.incomeRemark && detailInfo.orderStatus !== ORDER_STATUS.ADD_SUPPLEMENT) {
        form.setFieldValue('remark', detailInfo.incomeRemark);
      } else {
        form.setFieldValue('remark', undefined);
      }
    }
  };

  useEffect(() => {
    tabCahce = TAB.CAR;
    refreshOrderDetail();
    operateActionRef.current?.reload();
  }, [orderNo]);

  useEffect(() => {
    initTabs(detailInfo);
  }, [detailInfo]);

  const handleClear = () => {
    Modal.confirm({
      title: '是否确认清空草稿',
      onOk: () => {
        if (curTab === TAB.CAR) {
          carInfoRef?.current?.clear();
        }
        if (curTab === TAB.PERSOANL) {
          personalRef?.current?.clear();
        }
      },
    });
  };

  const handleSave = () => {
    if (curTab === TAB.CAR) {
      carInfoRef?.current?.save();
    }
    if (curTab === TAB.PERSOANL) {
      personalRef?.current?.save();
    }
  };

  const handleNextStep = () => {
    if (curTab === TAB.CAR) {
      carInfoRef?.current?.submit();
    }
    if (curTab === TAB.PERSOANL) {
      personalRef?.current?.submit();
    }
  };

  const handleRiskReject = () => {
    riskRejectRef?.current?.submit();
  };

  const ActionButtons = () => {
    let node = null;
    if (carReadOnly && schemeReadOnly && curTab === TAB.CAR) {
      return null;
    }
    if (personalReadOnly && curTab === TAB.PERSOANL) {
      return null;
    }
    if (supplementReadOnly && callFailReadOnly && curTab === TAB.RISK_REJECT) {
      return null;
    }

    // 默认按钮
    node = (
      <Space>
        <Button type="primary" onClick={handleClear}>
          清空草稿
        </Button>
        <Button type="primary" onClick={handleSave}>
          保存草稿
        </Button>
        <Button type="primary" onClick={handleNextStep}>
          {curTab === TAB.CAR ? '下一步' : '提交风控预审'}
        </Button>
      </Space>
    );
    switch (incomeStatus) {
      case INCOME_STATUS.RESELECT_VEHICLE:
      case INCOME_STATUS.RESELECT_LEASE_PROGRAM:
      case INCOME_STATUS.RECONFIRM_LEASE_PLAN:
        node = (
          <Button type="primary" onClick={handleNextStep}>
            提交修改
          </Button>
        );
        break;
      case INCOME_STATUS.ADDITIONAL_MATERIALS:
      case INCOME_STATUS.ADDITIONAL_MATERIALS_AGAIN:
      case INCOME_STATUS.CALL_FAIL:
        node = (
          <Button type="primary" onClick={handleRiskReject}>
            提交
          </Button>
        );
        break;
      default:
        break;
    }

    // 预审通过后修改信息
    if (detailInfo?.orderStatus === ORDER_STATUS.PRE_AUDIT_PASS) {
      node = (
        <Button type="primary" onClick={handleNextStep}>
          提交修改
        </Button>
      );
    }
    return node;
  };

  const onTabChange = (tab: string) => {
    setCurTab(tab);
    if (detailInfo?.incomeRemark && tab !== TAB.SUPPLEMENT) {
      form.setFieldValue('remark', detailInfo?.incomeRemark);
    }
  };

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Spin spinning={loading}>
          <Card title="申请信息">
            <Row>
              <Col span={8}>
                <b>姓名: </b>
                <span>{detailInfo?.userName}</span>
              </Col>
              <Col span={8}>
                <b>用户ID: </b>
                <Paragraph copyable style={{ display: 'inline' }}>
                  {userNo}
                </Paragraph>
              </Col>
              <Col span={8}>
                <b>订单号: </b>
                <Paragraph copyable style={{ display: 'inline' }}>
                  {orderNo}
                </Paragraph>
              </Col>
            </Row>
          </Card>
          <Card title={title} style={{ marginTop: 20 }} extra={<ActionButtons />}>
            <Tabs
              className="lease-come-tabs"
              onChange={onTabChange}
              items={tabItems}
              activeKey={curTab}
            />

            {curTab !== TAB.RISK_REJECT && (
              <Form
                form={form}
                layout="vertical"
                style={{ width: 800, marginTop: 12 }}
                wrapperCol={{ span: 12 }}
                labelCol={{ span: 6 }}
              >
                <h4>备注</h4>
                <ProFormTextArea
                  name="remark"
                  disabled={
                    (curTab === TAB.CAR && (carReadOnly || schemeReadOnly)) ||
                    (curTab === TAB.PERSOANL && personalReadOnly)
                  }
                />
              </Form>
            )}

            <h4 style={{ marginTop: 10 }}>操作记录</h4>
            <ProTable
              actionRef={operateActionRef}
              search={false}
              toolBarRender={false}
              columns={columns}
              pagination={{ pageSize: 10 }}
              request={(params) => {
                return orderOperateLog({ ...params, orderNo });
              }}
            />
          </Card>
        </Spin>
      </PageContainer>
    </>
  );
};

export default LeaseComeForm;
