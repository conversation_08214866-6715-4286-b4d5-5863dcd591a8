/*
 * @Date: 2023-08-29 16:45:21
 * @Author: elisa.<PERSON><PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-09-22 10:34:23
 * @FilePath: /lala-finance-biz-web/src/pages/IncreaseDetail/service.ts
 * @Description:
 */

import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { IncreaseDetailParams } from './data';

export async function getIncreaseDetailList(data: IncreaseDetailParams) {
  return request(`/bizadmin/config/increase/queryList`, {
    method: 'POST',
    data,
    headers,
  });
}

export async function addIncrease(data: Omit<IncreaseDetailParams, 'current' | 'pageSize'>) {
  return request(`/bizadmin/config/increase/add`, {
    method: 'POST',
    data,
    headers,
  });
}

//冻结
export async function freezeIncrease(id: string) {
  return request(`/bizadmin/config/increase/freeze`, {
    method: 'POST',
    data: { id },
    headers,
  });
}

//解冻
export async function unfreezeIncrease(id: string) {
  return request(`/bizadmin/config/increase/unfreeze`, {
    method: 'POST',
    data: { id },
    headers,
  });
}

//查询变更记录
export async function getModifyRequest(configId: string) {
  return request(`/bizadmin/config/increase/queryList`, {
    method: 'get',
    params: { configId },
    headers,
  });
}

//根据
export async function queryEnterpriseById(paId: string, productSecondTypeCode: string) {
  return request(`/bizadmin/config/increase/queryEnterprise`, {
    method: 'get',
    params: { paId, productSecondTypeCode },
    headers,
  });
}
