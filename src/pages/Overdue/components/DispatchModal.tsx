/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2024-12-10 16:26:40
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/DispatchModal.tsx
 */
import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { useAccess, useModel } from '@umijs/max';
import { message } from 'antd';
import React from 'react';
import { dispatch } from '../service';

export type DispatchModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  onVisibleChange: any;
  overdueId: string;
  type: number | string;
  overdueCaseNo: string;
};

const DispatchModal: React.FC<DispatchModalProps> = (props) => {
  const { urgePersonList: userList } = useModel('userList');
  const accessData = useAccess();

  return (
    <>
      <ModalForm
        title="催收派单"
        width={400}
        layout="horizontal"
        visible={props.modalVisible}
        onVisibleChange={props.onVisibleChange}
        modalProps={{
          centered: true,
          okText: '确认',
          destroyOnClose: true,
        }}
        onFinish={async (value) => {
          const currentUser: any = userList.filter((item: any) => value.id === item.value);
          //首派传参overdueIdList overdueCaseNoList;
          const overdueList =
            props?.type === '1'
              ? {
                  overdueIdList:
                    Object.prototype.toString.call(props.overdueId) === '[object Array]'
                      ? props.overdueCaseNo
                      : [props.overdueCaseNo],
                }
              : {
                  overdueCaseNoList:
                    Object.prototype.toString.call(props.overdueCaseNo) === '[object Array]'
                      ? props.overdueCaseNo
                      : [props.overdueCaseNo],
                };
          await dispatch({
            operator: accessData.currentUser.userName,
            type: props.type,
            urgeUserId: value.id,
            urgeUserName: currentUser[0]?.label,
            ...overdueList,
          });
          message.success('派单成功！');
          props.onOk();
          return true;
        }}
      >
        <ProFormSelect
          rules={[{ required: true }]}
          options={userList}
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            filterOption: (input: string, option: { label: string }) =>
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
          }}
          width="sm"
          name="id"
          label="催收员"
        />
      </ModalForm>
    </>
  );
};

export default DispatchModal;
