/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-10-09 16:25:57
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-16 16:03:31
 * @FilePath: /code/lala-finance-biz-web/src/pages/Channel/components/VehicleBasicInfo.tsx
 * @Description: 车辆基本信息
 */

import CityPicker from '@/components/CityPicker';
import HeaderTab from '@/components/HeaderTab/index';
import ImagePreview, { ImagePreviewInstance } from '@/components/ImagePreview';
import { CommonImageUpload } from '@/components/ReleaseCom';
import globalStyle from '@/global.less';
import { vehicleIdentifyOcrInfo } from '@/pages/Loan/service';
import { getCityListEnum } from '@/services/map';
import { disableFutureDate } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { But<PERSON>, Card, Col, DatePicker, Form, Input, message, Row, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { history } from 'umi';
import type { CarAssetsDetailItem } from '../data';
import { carAssetsModify, getCarAssetsDetail } from '../service';

const VehicleBasicInfoCom: React.FC = () => {
  const headerTabRef = useRef<any>(null);
  const [form] = Form.useForm();
  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  const { orderNo, type }: any = history.location?.query;
  const [orderDetail, setOrderDetail]: any = useState({});
  const [fileListEdit, setFileListEdit]: any = useState({
    registrationPage: [],
    registrationColumnPage: [],
  });
  const [initStatus, setInitStatus] = useState(false); //  初始化状态
  const [ocrLoading, setOcrLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  const carBasicMap = {
    carModelCode: '车型码',
    vin: '车架号',
    engineCode: '发动机号',
    vehicleRegisterCode: '车辆登记证号',
    licenseCode: '车牌号',
    mortgagorName: '抵押人姓名',
    mortgagorType: '抵押人证件类型',
    mortgagorCode: '抵押人证件号码',
    mortgagorDate: '抵押日期',
    mortgagorCity: '抵押城市',
    licenseAddress: '车牌所属省市',
    // licenseProvince: '车牌所属省份',
    // licenseCity: '车牌所属城市',
    // vehicleRegisterFile: '登记证书',
  };

  // 非必填字段
  const notRequeirdItem = ['carModelCode', 'vin'];

  const carOrderMap = {
    channelName: '渠道',
    affiliatedCompany: '挂靠公司',
    userName: '姓名',
    orderNo: '订单编号',
  };

  // 映射
  const mortgagorTypeMap = {
    1: '统一社会信用代码',
    2: '身份证',
  };

  // ocr映射
  const ocrFieldNameMap = {
    登记证编号: { key: 'vehicleRegisterCode', label: '车辆登记证号' },
    机动车登记证编号: { key: 'licenseCode', label: '车牌号' },
    日期: { key: 'mortgagorDate', label: '抵押日期' },
    //
    车架号: { key: 'vin', label: '车架号' },
    抵押人姓名: { key: 'mortgagorName', label: '抵押人姓名' },
  };

  const closePage = () => {
    headerTabRef.current?.handleDelete?.(
      history.location?.pathname,
      history.location?.search,
      false,
      () => {
        history.push(`/operation-manager/vehicle-asset-management`);
      },
    );
  };

  // 注册页、登记页对别数据
  const [vehicleRegisterCodeRecord, setVehicleRegisterCodeRecord] = useState({
    registrationPage: undefined,
    registrationColumnPage: undefined,
  });

  // OCR处理函数
  const ocrHandleFun = (value: any, fileSource: string) => {
    // console.log('ocrHandleFun', value, fileSource)
    // 处理删除操作
    try {
      if (value.length === 0) {
        setVehicleRegisterCodeRecord({ ...vehicleRegisterCodeRecord, [fileSource]: undefined });
      }
    } catch (e) {
      console.log(e);
    }
    if (value?.[0]?.status === 'done') {
      const { filePath } = value[0].response?.data;
      message.loading({
        content: 'OCR识别中',
        duration: 10,
        key: 'ocr_loading_key',
      });
      setOcrLoading(true);
      vehicleIdentifyOcrInfo({
        filePath,
        lendingNo: orderDetail?.lendingNo,
        fileType: 4,
      })
        .then((data) => {
          const updateItems: string[] = [];
          if (data?.ocrValueList?.length) {
            data.ocrValueList.forEach(({ fieldName, fieldValue }) => {
              const item = ocrFieldNameMap[fieldName as '日期'];
              if (!fieldValue || !item) return;
              // if (fieldName === '登记证编号') {
              //   form.setFieldValue('vehicleRegisterCode', fieldValue);
              //   updateItems.push('车辆登记证号');
              // }
              // if (fieldName === '机动车登记证编号') {
              //   form.setFieldValue('licenseCode', fieldValue);
              //   updateItems.push('车牌号');
              // }
              // 车架号、抵押人姓名，和ocr结果进行比对，不返显，不一致则提醒
              // 不需要返显的字段
              if (['车架号', '抵押人姓名'].includes(fieldName)) {
                // 现有信息
                const vinValue = orderDetail['vin'];
                const mortgagorNameValue = form.getFieldValue('mortgagorName');
                const currentValue = fieldName === '车架号' ? vinValue : mortgagorNameValue;
                // ocr返回的信息
                const ocrValue = fieldValue;
                if (currentValue !== ocrValue) {
                  message.error(`${fieldName}不一致，请检查`);
                }
                return;
              } else if (fieldName === '登记证编号') {
                // 判断是否，注册页和登记栏页都有值
                const { registrationPage, registrationColumnPage } = vehicleRegisterCodeRecord;
                //
                if (fileSource === 'registrationPage' && registrationColumnPage === undefined) {
                  setVehicleRegisterCodeRecord({
                    ...vehicleRegisterCodeRecord,
                    registrationPage: fieldValue as any,
                  });
                  console.log('vehicleRegisterCodeRecord', vehicleRegisterCodeRecord);
                  return;
                }
                if (fileSource === 'registrationColumnPage' && registrationPage === undefined) {
                  setVehicleRegisterCodeRecord({
                    ...vehicleRegisterCodeRecord,
                    registrationColumnPage: fieldValue as any,
                  });
                  console.log('vehicleRegisterCodeRecord', vehicleRegisterCodeRecord);
                  return;
                }
                // 都有值，判断是否一致,一致则更新，否则则、提示
                if (
                  (fileSource === 'registrationPage' && registrationColumnPage === fieldValue) ||
                  (fileSource === 'registrationColumnPage' && registrationPage === fieldValue)
                ) {
                  form.setFieldValue('vehicleRegisterCode', fieldValue);
                  // updateItems.push('车辆登记证号');
                  if (fileSource === 'registrationPage') {
                    setVehicleRegisterCodeRecord({
                      ...vehicleRegisterCodeRecord,
                      registrationPage: fieldValue as any,
                    });
                  } else if (fileSource === 'registrationColumnPage') {
                    setVehicleRegisterCodeRecord({
                      ...vehicleRegisterCodeRecord,
                      registrationColumnPage: fieldValue as any,
                    });
                  }
                } else {
                  message.error(`车辆登记证号不一致，请检查`);
                  return;
                }
              }
              // 需要返显的字段
              else if (fieldName === '日期') {
                form.setFieldValue('mortgagorDate', dayjs(fieldValue));
                // updateItems.push('抵押日期');
              } else {
                form.setFieldValue(item.key, fieldValue);
              }
              form.validateFields([item.key]);
              updateItems.push(item.label);
            });
          }
          message.success(
            `OCR识别完成！${updateItems.length ? '已更新字段：' + updateItems.toString() : ''}`,
          );
        })
        .finally(() => {
          setOcrLoading(false);
          message.destroy('ocr_loading_key');
        });
    }
  };

  // 格式化licenseAddress
  const formatAddress = (item: CarAssetsDetailItem, mode?: string) => {
    const data: any = [];
    const { licenseProvince, licenseCity } = item || {};
    if (licenseProvince && licenseProvince.includes('_')) data.push(licenseProvince);
    if (licenseCity && licenseProvince !== licenseCity && licenseCity.includes('_'))
      data.push(licenseCity);
    // 详情页格式
    if (mode === 'detail') {
      return (
        data
          .map((i: string) => {
            return i?.split('_')?.[0];
          })
          .join('/') || '-'
      );
    }
    // 默认编辑格式
    return data;
  };

  // 编辑模式下，抵押城市mortgagorCity有值，车牌所属省市没值,则补充
  const handleLicenseAddress = async (mortgagorCity: string) => {
    if (!mortgagorCity) return;
    const cityData = await getCityListEnum({ inLabel: true, level: 2 }).catch(() => {});
    console.log('cityData', cityData);

    // cityData格式：[[], []]
    // label = item.name;
    // value = `${item.name}_${item.adcode}`

    // 匹配到的省市列表
    const res: any = [];
    // 拿mortgagorCity去匹配label
    try {
      cityData?.forEach((item: any) => {
        // 匹配直辖市
        if (
          item?.label?.includes(mortgagorCity) &&
          (item?.children?.length === 0 || !item?.children)
        ) {
          res.push([item?.value]);
        }
        // 匹配地级市
        if (item?.children && item?.children?.length > 0) {
          item.children?.forEach((child: any) => {
            if (child?.label?.includes(mortgagorCity)) {
              res.push([item?.value, child?.value]);
            }
          });
        }
      });
    } catch (e) {
      console.log(e);
    }
    // 只匹配到一个结果时，直接赋值，其他情况不处理
    if (res?.length === 1) {
      try {
        form.setFieldsValue({
          licenseAddress: res[0],
        });
      } catch (e) {
        console.log(e);
      }
    }
  };

  useEffect(() => {
    getCarAssetsDetail({ orderNo }).then(({ data }: { data: CarAssetsDetailItem }) => {
      if (data) {
        setOrderDetail(data);
        form.setFieldsValue({
          ...data,
          mortgagorDate: data?.mortgagorDate ? dayjs(data?.mortgagorDate) : '',
          licenseAddress: formatAddress(data),
        });
        // 编辑模式下，抵押城市mortgagorCity有值，车牌所属省市没值
        if (
          type === 'edit' &&
          data?.mortgagorCity &&
          !data?.licenseProvince &&
          !data?.licenseCity
        ) {
          //
          handleLicenseAddress(data?.mortgagorCity).catch(() => {});
        }
        setFileListEdit({
          registrationPage: data?.registrationPage
            ? [{ url: data?.registrationPage, name: '注册页', status: 'done' }]
            : [],
          registrationColumnPage: data?.registrationColumnPage
            ? [{ url: data?.registrationColumnPage, name: '登记栏页', status: 'done' }]
            : [],
        });
        setInitStatus(true);
      }
    });
  }, [orderNo, type]);

  // 自定义图片预览
  const previewRef = useRef<ImagePreviewInstance>();
  const handlePreviewAction = async (file: any) => {
    console.log('file', file);
    if (!file) {
      return;
    }
    const url = file?.url || file?.response?.data?.netWorkPath;
    if (url) {
      previewRef.current?.previewFile({
        url: url,
        fileName: file?.name,
        urlList: [],
      });
    }
  };

  return (
    <>
      <HeaderTab onRef={headerTabRef} />
      <PageContainer className={globalStyle.mt16}>
        <Card title="车辆基本信息">
          <Form
            form={form}
            onFinishFailed={() => {
              setSubmitLoading(false);
              message.warning('请必填项完成后重新提交！');
            }}
            onFinish={(params: any) => {
              const req = {
                ...orderDetail,
                ...params,
                mortgagorDate: dayjs(params?.mortgagorDate).format('YYYY-MM-DD HH:mm:ss'),
                registrationPage:
                  params?.registrationPage?.[0].response?.data?.filePath ||
                  orderDetail?.registrationPage,
                registrationColumnPage:
                  params?.registrationColumnPage?.[0].response?.data?.filePath ||
                  orderDetail?.registrationColumnPage,
                licenseProvince: params?.licenseAddress?.[0] || '',
                licenseCity: params?.licenseAddress?.[1] || params?.licenseAddress?.[0] || '', // 直辖市时省市都传同一个
              };

              try {
                if (req.registrationPage.includes('?'))
                  req.registrationPage = new URL(req.registrationPage).pathname;
                if (req.registrationPage?.[0] === '/')
                  req.registrationPage = req.registrationPage.slice(1);
                if (req.registrationColumnPage.includes('?'))
                  req.registrationColumnPage = new URL(req.registrationColumnPage).pathname;
                if (req.registrationColumnPage?.[0] === '/')
                  req.registrationColumnPage = req.registrationColumnPage.slice(1);
              } catch (e) {
                console.log(e);
              }

              delete req.licenseAddress; //  该字段不需要提交，删除

              carAssetsModify(req)
                .then(() => {
                  message.success('提交成功！');
                  setTimeout(() => {
                    closePage();
                  }, 300);
                })
                .finally(() => {
                  setSubmitLoading(false);
                });
            }}
            labelAlign="left"
          >
            <Row gutter={24}>
              {Object.keys(carBasicMap).map((key) => {
                return (
                  <Col span={8} key={key}>
                    {type === 'edit' ? (
                      <Form.Item
                        label={carBasicMap[key as 'vin']}
                        name={key}
                        rules={[{ required: !notRequeirdItem.includes(key) }]}
                      >
                        {['carModelCode', 'vin'].includes(key) ? (
                          <span>{orderDetail[key] || '-'}</span>
                        ) : ['mortgagorDate'].includes(key) ? (
                          <DatePicker style={{ width: '100%' }} disabledDate={disableFutureDate} />
                        ) : ['mortgagorType'].includes(key) ? (
                          <Select
                            options={[
                              { value: 1, label: '统一社会信用代码' },
                              { value: 2, label: '身份证' },
                            ]}
                            popupMatchSelectWidth={false}
                          />
                        ) : ['licenseAddress'].includes(key) ? (
                          <CityPicker
                            disabled={false}
                            showSearch={true}
                            level={2}
                            placeholder="请选择"
                          />
                        ) : (
                          <Input />
                        )}
                      </Form.Item>
                    ) : (
                      <Form.Item label={carBasicMap[key as 'vin']} name={key}>
                        {['mortgagorType'].includes(key) ? (
                          <span>{mortgagorTypeMap[orderDetail[key] as 1] || '-'}</span>
                        ) : ['licenseAddress'].includes(key) ? (
                          <span>{formatAddress(orderDetail, 'detail')}</span>
                        ) : (
                          <span>{orderDetail[key] || '-'}</span>
                        )}
                      </Form.Item>
                    )}
                  </Col>
                );
              })}
            </Row>
            {initStatus && (
              <Row gutter={24}>
                <Col span={8} key="registrationPageKey">
                  <Form.Item
                    // label="注册页"
                    name="registrationPage"
                    // rules={[{ required: true, message: '请上传注册页' }]}
                  >
                    <CommonImageUpload
                      extra="注册页"
                      extraTrack={false}
                      label="登记证书"
                      icon={<PlusOutlined />}
                      labelCol={{ span: 8 }}
                      name="registrationPage"
                      max={1}
                      mapFileList={mapFileList}
                      buttonProps={{ type: 'text' }}
                      rules={[{ required: type === 'edit', message: '请上传注册页' }]}
                      listType="picture-card"
                      accept=".pdf,.png,.jpg,.jpeg,.bmp"
                      isShowDownloadIcon={true}
                      handleExtraChange={(v: any) => ocrHandleFun(v, 'registrationPage')}
                      size={20}
                      fileListEdit={fileListEdit?.registrationPage}
                      disabled={type !== 'edit' || ocrLoading}
                      //
                      handlePreviewAction={handlePreviewAction}
                    />
                  </Form.Item>
                </Col>
                <Col span={8} key="registrationColumnPageKey">
                  <Form.Item
                    // label="登记栏页"
                    name="registrationColumnPage"
                    // rules={[{ required: true, message: '请上传登记栏页' }]}
                  >
                    <CommonImageUpload
                      extra="登记栏页"
                      extraTrack={false}
                      label=""
                      icon={<PlusOutlined />}
                      labelCol={{ span: 8 }}
                      name="registrationColumnPage"
                      max={1}
                      mapFileList={mapFileList}
                      buttonProps={{ type: 'text' }}
                      rules={[{ required: true, message: '请上传登记栏页' }]}
                      listType="picture-card"
                      accept=".pdf,.png,.jpg,.jpeg,.bmp"
                      isShowDownloadIcon={true}
                      handleExtraChange={(v: any) => ocrHandleFun(v, 'registrationColumnPage')}
                      size={20}
                      fileListEdit={fileListEdit?.registrationColumnPage}
                      disabled={type !== 'edit' || ocrLoading}
                      //
                      handlePreviewAction={handlePreviewAction}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}
          </Form>
        </Card>
        <Card title="订单信息" style={{ marginTop: 20 }}>
          <Form labelCol={{ span: 8 }} labelAlign="left">
            <Row gutter={24}>
              {Object.keys(carOrderMap).map((key) => {
                return (
                  <Col span={8} key={key}>
                    <Form.Item label={carOrderMap[key as 'orderNo']} name={key}>
                      {orderDetail[key] && ['orderNo'].includes(key) ? (
                        <a
                          onClick={() => {
                            history.push(`/businessMng/lease-detail?orderNo=${orderDetail[key]}`);
                          }}
                        >
                          {orderDetail[key]}
                        </a>
                      ) : (
                        <span>{orderDetail[key] || '-'}</span>
                      )}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
          </Form>
          {type === 'edit' && (
            <div style={{ textAlign: 'center', marginTop: 35 }}>
              <Button
                style={{ marginRight: 20 }}
                onClick={() => {
                  closePage();
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                loading={submitLoading}
                disabled={ocrLoading}
                onClick={() => {
                  setSubmitLoading(true);
                  form.submit();
                }}
              >
                提交
              </Button>
            </div>
          )}
        </Card>
        {/* 图片预览 */}
        <ImagePreview ref={previewRef as any} />
      </PageContainer>
    </>
  );
};

export default VehicleBasicInfoCom;
