import { Request, Response } from 'express';
import { parse } from 'url';
import { TableListItem, TableListParams } from '@/pages/CreditAudit/data';

// mock tableListDataSource
const genList = (current: number, pageSize: number) => {
  const tableListDataSource: TableListItem[] = [];

  for (let i = 0; i < pageSize; i += 1) {
    const index = (current - 1) * 10 + i;
    tableListDataSource.push({
      orderNo: `100120200929${index}`, // 申请单号
      productType: '保理', // 产品类型
      productName: '买方保理', // 产品名称
      userName: '深圳依人型有限公司', // 企业名称
      orgCode: '91440300MA5DK6LK0M', // 统一机构信用代码
      authorizedPhone: '137****8072', // 授权人联系方式
      authorizedIdCard: '440883********0989', // 授权人身份证号
      status: [0, 10, 20, 31, 40, 41, 42][Math.floor(Math.random() * 7)], // "状态：0:默认状态 10:待风控 30:待初审 31:待终审  40:审核通过 41:审核拒绝 42:撤销",
      applyAmountFen: Math.floor(Math.random() * 10000), // 申请金额
      creditAmountFen: Math.floor(Math.random() * 10000), // 授权人金额
      riskLevel: ['AAA', 'AA', 'A', 'BBB', 'BB', 'B', 'CCC', 'CC', 'C'][
        Math.floor(Math.random() * 9)
      ], // 信用等级
      applyTime: new Date(+new Date() + i * 10000), // 申请时间
      approvalTime: new Date(+new Date() + i * 12000), // 审批时间
      firstCheckTime: new Date(+new Date() + i * 19000), // 初审时间
      trialExaminer: '兰源', // 初审人
      recheckTime: new Date(), // 终审时间
      finalAdjudicator: '程乐', // 终审人
    });
  }
  tableListDataSource.reverse();
  return tableListDataSource;
};

const tableListDataSource = genList(1, 100);

function queryList(req: Request, res: Response, u: string) {
  let realUrl = u;
  if (!realUrl || Object.prototype.toString.call(realUrl) !== '[object String]') {
    realUrl = req.url;
  }
  const { current = 1, pageSize = 10 } = req.query;
  const params = (parse(realUrl, true).query as unknown) as TableListParams;

  let dataSource = [...tableListDataSource].slice(
    ((current as number) - 1) * (pageSize as number),
    (current as number) * (pageSize as number),
  );
  const sorter = JSON.parse(params.sorter as any);
  if (sorter) {
    dataSource = dataSource.sort((prev, next) => {
      let sortNumber = 0;
      Object.keys(sorter).forEach((key) => {
        if (sorter[key] === 'descend') {
          if (prev[key] - next[key] > 0) {
            sortNumber += -1;
          } else {
            sortNumber += 1;
          }
          return;
        }
        if (prev[key] - next[key] > 0) {
          sortNumber += 1;
        } else {
          sortNumber += -1;
        }
      });
      return sortNumber;
    });
  }
  if (params.filter) {
    const filter = JSON.parse(params.filter as any) as {
      [key: string]: string[];
    };
    if (Object.keys(filter).length > 0) {
      dataSource = dataSource.filter((item) => {
        return Object.keys(filter).some((key) => {
          if (!filter[key]) {
            return true;
          }
          if (filter[key].includes(`${item[key]}`)) {
            return true;
          }
          return false;
        });
      });
    }
  }

  // if (params.name) {
  //   dataSource = dataSource.filter((data) => data.name.includes(params.name || ''));
  // }
  const result = {
    data: dataSource,
    total: tableListDataSource.length,
    success: true,
    pageSize,
    current: parseInt(`${params.current}`, 10) || 1,
  };

  return res.json(result);
}

// function postRule(req: Request, res: Response, u: string, b: Request) {
//   let realUrl = u;
//   if (!realUrl || Object.prototype.toString.call(realUrl) !== '[object String]') {
//     realUrl = req.url;
//   }

//   const body = (b && b.body) || req.body;
//   const { method, name, desc, key } = body;

//   switch (method) {
//     /* eslint no-case-declarations:0 */
//     case 'delete':
//       tableListDataSource = tableListDataSource.filter((item) => key.indexOf(item.key) === -1);
//       break;
//     case 'post':
//       (() => {
//         const i = Math.ceil(Math.random() * 10000);
//         const newRule = {
//           key: tableListDataSource.length,
//           href: 'https://ant.design',
//           avatar: [
//             'https://gw.alipayobjects.com/zos/rmsportal/eeHMaZBwmTvLdIwMfBpg.png',
//             'https://gw.alipayobjects.com/zos/rmsportal/udxAbMEhpwthVVcjLXik.png',
//           ][i % 2],
//           name,
//           owner: '曲丽丽',
//           desc,
//           callNo: Math.floor(Math.random() * 1000),
//           status: Math.floor(Math.random() * 10) % 2,
//           updatedAt: new Date(),
//           createdAt: new Date(),
//           progress: Math.ceil(Math.random() * 100),
//         };
//         tableListDataSource.unshift(newRule);
//         return res.json(newRule);
//       })();
//       return;

//     case 'update':
//       (() => {
//         let newRule = {};
//         tableListDataSource = tableListDataSource.map((item) => {
//           if (item.key === key) {
//             newRule = { ...item, desc, name };
//             return { ...item, desc, name };
//           }
//           return item;
//         });
//         return res.json(newRule);
//       })();
//       return;
//     default:
//       break;
//   }

//   const result = {
//     list: tableListDataSource,
//     pagination: {
//       total: tableListDataSource.length,
//     },
//   };

//   res.json(result);
// }

export default {
  'GET /api/loan/audit/creditAudit': queryList,
  // 'POST /api/rule': postRule,
};
