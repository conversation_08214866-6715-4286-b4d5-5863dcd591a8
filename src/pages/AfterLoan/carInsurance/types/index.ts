export const channelTypeMap = {
  1: '货拉拉',
  2: 'LLP',
  3: '其他',
};

export const userTypeEnCodeMap = {
  COMPANY: 1,
  PERSONAL: 2,
};

export const userTypeMap = {
  1: '企业',
  2: '个人',
};

export type IuserType = keyof typeof userTypeMap;

// 账单状态枚举
export const statusMap = {
  20: '待还款',
  30: '提前结清',
  40: '正常结清',
  50: '逾期',
  60: '逾期结清',
  70: '退保结项',
  80: '单期代偿',
};

export type Istatus = keyof typeof statusMap;

export enum IdimensionEnCode {
  TERM_BILL = 1, // 车辆纬度
  ORDER_TERM_BILL = 2, // 订单纬度
  SUBJECT_MATTER_BILL = 3, //总账
}

export type Idimension = 1 | 2 | 3; // TERM_BILL(1, "期账"), ORDER_TERM_BILL(2, "订单期账")    SUBJECT_MATTER_BILL(3, "标的物总账");

/**
 * 列表请求参数
 */
export interface IbillListParams {
  current: number;
  pageSize: number;

  dimension: Idimension;
  secondaryClassification: '0303'; // 车险固定是 0303

  productCode?: string; // 产品code 030301
  billNoList?: string[]; // 账单编号 订单分期账单ID
  termList?: number[]; // 期数
  orderNo?: string; // 订单号

  userName?: string; // 客户名称 个人 企业的时候传 userNo
  userNo?: string; // 用户id
  channelCode?: string; // 渠道名称
  statusList?: Istatus[]; // 账单状态

  updateAtStart?: string; // 更新开始日期
  updateAtEnd?: string; // 更新结束日期
  lendingTimeStart?: string; // 放款开始日期
  lendingTimeEnd?: string; // 放款结束日期
  dueDateStart?: string; // 应还开始日期
  dueDateEnd?: string; // 应还结束日期
  clearTimeStart?: string; // 结清开始日期
  clearTimeEnd?: string; // 结清结束日期

  userType?: IuserType; // 客户类型 // 1 企业  2 个人
  channelType?: string; // 渠道类型
}

export interface IbillListItem {
  id: number;
  billNo: string;
  secondaryClassification: string;
  startTime: string;
  endTime: string;
  billingDate: string;
  totalAmount: string;
  amountPaid: string;
  freezeAmount: string;
  clearTime: string;
  status: number;
  statusName: string;
  subjectMatterNo: string;
  refNo: string;
  refType: number;
  billType: number;
  ownerId: string;
  ownerRole: number;
  scene: string;
  payee: string;
  payeeRole: number;
  lender: string;
  lenderRole: number;
  billCostNo: string[];
  billCost: BillCost[];
  dimension: Idimension;
  extendInfo: {};
  termNumber: string;
  carCount: number;
  accountName: string;
  customerType: IuserType;
  customerTypeName: string;
  channelName: string;
  channelType: number;
  channelTypeName: string;
  channelCode: string;
  orderNo: string;
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalAmountDue: string;
  totalPrincipalDue: string;
  totalInterestDue: string;
  totalOverduePenaltyDue: string;
  totalAmountPaid: string;
  totalPrincipalPaid: string;
  totalInterestPaid: string;
  totalOverduePenaltyPaid: string;
  totalBreach: string;
  totalBreachDue: string;
  totalBreachPaid: string;
  totalLate: string;
  totalLateDue: string;
  totalLatePaid: string; // Generated by https://quicktype.io
  totalAmountExemption: string;
  totalPrincipalExemption: string;
  totalInterestExemption: string;
  totalOverduePenaltyExemption: string;
  totalBreachExemption: string;
  totalLateExemption: string;
  productCode: string;
  productName: string;
  productCodeLevelOne: string;
  productCodeLevelTwo: string;
  updatedAt: string;
  lendingTime: string;
  dueDate: string;
  overdueDateNumber: number;
  expectCancelStatus: boolean;
}

export interface BillCost {
  costType: number;
  amountDue: string;
  amountPaid: string;
  freezeAmount: string;
  paidDetail: PaidDetail[];
}

export interface PaidDetail {
  paymentMode: string;
  amount: string;
}

export interface ExtendInfo {
  count: number;
}

export interface IsearchUserListItem {
  certiName: string;
  legalPerson: string;
  tradingCertificateFilePath: string;
  enterpriseInfo: any;
  authNo: string;
  productSecondCode: string;
  epAccreditDate: string;
  epAuthDate: string;
  createdAt: string;
  authStatus: number;
  businessLicenseUrl: string;
  extraInfo: any;
}

export interface IsubmitBillRepay {
  billNoList?: string[];
  remissionPrincipal?: number; // 减免本金
  remissionInterest?: number; // 减免利息
  remissionPenalty?: number; // 减免罚息
  remissionAdvanceSettleLiquidatedDamages?: number; // 减免提前结清违约金
  remissionDelayAmount?: number; // 减免滞纳金 减免滞纳金
  repayAmount: number; // 还款金额
  attach: string[]; // 绝对定制
  remissionList: IremissionItem[];
  billInfoList?: IbillRspDTOItem[];
  // repayDate: string // 还款日期
  dimension: Idimension; // 还款来源 1车辆分期账单还款 2订单分期账单还款
  settleType?: boolean;
}
export interface IremissionItem {
  billNo?: string; //账单id
  remissionPrincipal?: number; //减免本金
  remissionInterest?: number; // 减免利息
  remissionPenalty?: number; // 减免罚息
  remissionAdvanceSettleLiquidatedDamages?: string; // 减免提前结清违约金
  remissionDelayAmount?: string; // 减免滞纳金 减免滞纳金
}

export interface IbillInfoParams {
  billNoList: string[];
  dimension: Idimension; // TERM_BILL(1, "期账") ORDER_TERM_BILL(2, "订单期账")
  secondaryClassification: '0303';
  settleType?: boolean;
  onlineRepay?: boolean;
}

export interface IbillInfo {
  id: number;
  businessNo: string;
  orderNo: string;
  orderNoList: string[];
  billNo: string;
  billNoList: string[];
  applyPerson: string;
  applyName: string;
  secondProductCode: string;
  productCode: string;
  productName: string;
  repayAmount: number;
  exemptionAmount: string;
  repayAmountDetail: string;
  repayDate: string;
  repayDateStart: string;
  repayDateEnd: string;
  repayMode: number;
  repayModeName: string;
  remitType: number;
  repaySerialNo: string;
  channelName: string;
  channelTypeName: string;
  channelId: string;
  accountName: string;
  accountNameList: string[];
  accountNumber: string;
  customerType: number;
  customerTypeName: string;
  remark: string;
  attach: string;
  attachList: string[];
  creditDate: string;
  creditDateStart: string;
  creditDateEnd: string;
  repayStatus: number;
  repayStatusName: string;
  updatedAt: string;
  createdAt: string;
  status: number;
  statusName: string;
  reason: string;
  approvalLog: string;
  approvalId: string;
  approvalPerson: string;
  approvalTime: string;
  subjectUniqueNo: string;
  subjectUniqueNoList: string[];
  overdueCaseNo: string;
  overdueAmount: string;
  orderAmount: string;
  compensation: number;
  compensationName: string;
  bankName: string;
  bankNo: string;
  bankAccount: string;
  subBranchBank: string;
  smallId: number;
  maxId: number;
  billRspDTOList: IbillRspDTOItem[];
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalBreach: string;
  totalLate: string;
  totalPrincipalNotRepaid: string; //合计未还本金
  totalInterestNotRepaid: string; //合计未还利息
  totalBreachNotRepaid: string; //合计提前结清违约金
  totalOverduePenaltyNotRepaid: string; // 未还罚息
}

export interface IbillRspDTOItem {
  expectRepayAmount: number;
  current: number;
  pageSize: number;
  total: number;
  data: any[];
  id: number;
  billNo: string;
  secondaryClassification: string;
  startTime: string;
  endTime: string;
  billingDate: string;
  totalAmount: string;
  amountPaid: string;
  freezeAmount: string;
  clearTime: string;
  status: number;
  statusName: string;
  subjectMatterNo: string;
  refNo: string;
  refType: number;
  billType: number;
  ownerId: string;
  ownerRole: number;
  scene: string;
  payee: string;
  payeeRole: number;
  lender: string;
  lenderRole: number;
  billCostNo: string[];
  billCost: IbillCostItem[];
  dimension: Idimension;
  extendInfo: ExtendInfo;
  termNumber: string;
  carCount: number;
  accountName: string;
  customerType: number;
  customerTypeName: string;
  channelName: string;
  channelType: number;
  channelTypeName: string;
  orderNo: string;
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalAmountDue: string;
  totalPrincipalDue: string;
  totalInterestDue: string;
  totalOverduePenaltyDue: string;
  totalAmountPaid: string;
  totalPrincipalPaid: string;
  totalInterestPaid: string;
  totalOverduePenaltyPaid: string;
  totalBreach: string;
  totalBreachDue: string;
  totalBreachPaid: string;
  totalLate: string;
  totalLateDue: string;
  totalLatePaid: string;
  totalAmountExemption: string;
  totalPrincipalExemption: string;
  totalInterestExemption: string;
  totalOverduePenaltyExemption: string;
  totalBreachExemption: string;
  totalLateExemption: string;
  productCode: string;
  productName: string;
  productCodeLevelOne: string;
  productCodeLevelTwo: string;
  updateAt: string;
  lendingTime: string;
  dueDate: string;
  overdueDateNumber: number;
  normalExpectRepayAmount?: number;
}

export interface IbillCostItem {
  costType: number;
  amountDue: string;
  amountPaid: string;
  freezeAmount: string;
  paidDetail: IpaidDetailItem[];
}

export interface IpaidDetailItem {
  paymentMode: string;
  amount: string;
}
