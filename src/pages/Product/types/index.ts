// enum中的枚举有点乱,所以在这重新写一下,适用于 ./Product 路由下的

// classification 分类

// 一级
export const firstClassificationValueEnum = {
  '01': '商业保理',
  '02': '融资租赁',
  '03': '小额贷款',
};

// 二级
export const secondClassificationValueEnum = {
  '0101': '明保',
  '0102': '暗保',
  '0103': '应收账款',
  '0104': '共享明保',
  '0105': '共享暗保',
  '0106': '共享应收账款',
  '0201': '小圆车融',
  '0301': '圆易借',
  '0303': '小圆车险分期',
  '0304': '圆商贷',
};

// 二级分类 数字 英文对照
export const secondClassificationMap = {
  '0101': 'BUSINESS_ACCOUNTING_PERIOD',
  '0102': 'DARK_FACTORING',
  '0103': 'ACCOUNTS_RECEIVABLE',
  '0104': 'BUSINESS_ACCOUNTING_PERIOD_SHARED',
  '0105': 'DARK_FACTORING_SHARED',
  '0106': 'ACCOUNTS_RECEIVABLE_SHARED',
  '0201': 'FINANCE_LEASE',
  '0301': 'LOAN_INSTALLMENT_CREDIT',
  '0303': 'CAR_INSURANCE',
  '0304': 'ENTERPRISE_LOAN',
};

// 一级分类 数字 英文对照
export const firstClassificationMap = {
  '01': 'SELLER_FACTORING',
  '02': 'FINANCE_LEASE',
  '03': 'SMALL_LOAN',
};
