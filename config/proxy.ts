/*
 * @Author: your name
 * @Date: 2021-03-20 15:03:17
 * @LastEditTime: 2025-04-16 14:34:49
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/config/proxy.ts
 */
/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */

// 为了快速切换 stg 和 pre 环境,只需要更改一下 targetEnv的值

// const targetEnv = 'stg';
const targetEnv = 'pre';

const target = {
  auth: `https://finance-api-${targetEnv}.lalafin.net`,
  loan: `https://finance-api-${targetEnv}.lalafin.net`,
  quota: `https://finance-api-${targetEnv}.lalafin.net`,
  repayment: `https://finance-api-${targetEnv}.lalafin.net`,
  risk: `https://finance-api-${targetEnv}.lalafin.net`,
  bizadmin: `https://finance-api-${targetEnv}.lalafin.net`,
  factoring: `https://finance-api-${targetEnv}.lalafin.net`,
  // factoring:`http:*************:8082`,
  insurance: `https://finance-api-${targetEnv}.lalafin.net`,
  base: `http://lalafin-base-${targetEnv}.myhll.cn`,
  'bme-call-center': `https://call-api-${targetEnv}.huolala.cn`,
};

const proxy: Record<string, any> = {
  dev: {
    '/auth/': {
      // target: 'https://finance-api-stg.lalafin.net',
      target: target.auth,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/loan/': {
      // target: 'https://finance-api-stg.lalafin.net',
      target: target.loan,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/quota/': {
      // target: 'https://finance-api-stg.lalafin.net',
      target: target.quota,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/repayment/': {
      // target: 'https://finance-api-stg.lalafin.net',
      target: target.repayment,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/risk/': {
      // target: 'https://finance-api-stg.lalafin.net',
      target: target.risk,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/bizadmin/': {
      // target: 'https://finance-api-stg.lalafin.net',
      target: target.bizadmin,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/factoring/': {
      // target: 'https://finance-api-stg.lalafin.net',
      target: target.factoring,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/bme-call-center': {
      target: target['bme-call-center'],
      changeOrigin: true,
    },
    '/insurance/': {
      target: target.insurance,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
    '/base': {
      target: target.base,
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  test: {
    '/api/': {
      target: 'https://stgview.pro.ant.design',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  stg: {
    '/api/': {
      target: 'your stg url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};

export default proxy;
