/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 16:02:08
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-03-22 14:20:40
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/Contact.tsx
 * @Description: Contact
 */
import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import globalStyle from '@/global.less';
import { desensitizationPhone, isExternalNetwork } from '@/utils/utils';
import { PhoneFilled } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { Access, useAccess, useModel } from '@umijs/max';
import { useMount } from 'ahooks';
import { Button, Form, message, Modal, Popconfirm, Popover, Select, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { STATUS_CASE } from '../data.d';
import {
  addContact,
  deleteContact,
  getContactList,
  getOverdueCaseSMSTemplate,
  sendOverdueSMS,
} from '../service';
import { reportLog } from '../services';
interface BasicInfoProps {
  // basicInfo: OverdueItem;
  contactPhoneBOList: any[];
  refresh?: () => void;
  overdueCaseNo: string;
  data: any;
  dataDetail: any;
  isError: boolean;
  productCode: string;
  userNo: string;
  // urgeUserChangeDTOList: any[];
  // addContactBack: any;
  // productCode: string;
}

const Contact: React.FC<BasicInfoProps> = (props) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [smsVisible, setSmsVisible] = useState(false);
  const {
    contactPhoneBOList,
    refresh,
    data,
    isError,
    productCode,
    userNo,
    dataDetail,
  }: any = props;
  const [loading, setLoading] = useState(false);
  const access = useAccess();
  const selectedPeople = useRef({ rows: [], select: {} });
  const smsTemplate = useRef([]);
  const smsThirdTemplate = useRef([]); //  只允许第三方发送的模板
  const [smsLoading, setSmsLoading] = useState(false);

  const [formRef] = Form.useForm();
  function onVisibleChange(visible: any) {
    setModalVisible(visible);
  }
  function deleteContactFn(id: string) {
    if (!id) return;
    deleteContact(id).then(() => {
      message.success('操作成功！');
      refresh(); //刷新操作
    });
  }
  const { setDialOuterCallNumber, setHelpStr } = useModel('caller');

  const getLatestContact = () => {
    try {
      setLoading(true);
      if (userNo && productCode) {
        getContactList(userNo, productCode).then(() => {
          refresh();
          setLoading(false);
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const getSMSTemplate = async () => {
    const res = await getOverdueCaseSMSTemplate({
      overdueMaxLevel: data?.overdueMaxLevel,
      secondaryClassification: data?.productSecondaryCode,
    });
    const option: any = [];
    const optionThird: any = [];
    if (res.data?.length) {
      res.data.forEach((item: any) => {
        const { noOneself, tripartite, excludesRoles } = item;
        option.push({
          value: item.smsTemplateId,
          label: item.smsTemplateName,
          noOneself,
          tripartite,
          excludesRoles,
        });
        if (item.tripartite) {
          optionThird.push({
            value: item.smsTemplateId,
            label: item.smsTemplateName,
            noOneself,
            tripartite,
            excludesRoles,
          });
        }
      });
    }
    smsTemplate.current = option;
    smsThirdTemplate.current = optionThird;
  };
  // console.log(dataDetail);

  const onOksendOverdueSMS = () => {
    if (selectedPeople.current.rows.length === Object.keys(selectedPeople.current.select).length) {
      const urgeList: any = [];
      selectedPeople.current.rows.forEach((item: any) => {
        const templateId = selectedPeople.current.select[item.rowKeyId];
        // 数据整合处理，相同id的模板归类用户

        if (
          urgeList.some((val: any, index: any) => {
            if (val.templateId === templateId) {
              urgeList[index]?.userInfoList.push({
                userName: item.contactPerson,
                userTel: item.contactPhone,
              });
              return true;
            } else {
              return false;
            }
          })
        ) {
          console.log(`${templateId}: ${item.contactPerson}`);
        } else {
          urgeList.push({
            templateId: templateId,
            userInfoList: [
              {
                userName: item.contactPerson,
                userTel: item.contactPhone,
              },
            ],
          });
        }
      });
      setSmsLoading(true);

      sendOverdueSMS({
        accountName: data?.accountName,
        urgePersonName: access.userName as string,
        urgePersonId: access.userId as string,
        productName: dataDetail?.productName,
        userNo: data?.userNo,
        productSecondCode: data?.productSecondaryCode,
        overDueCaseNo: data?.overdueCaseNo,
        totalOverdueAmount: Number(data?.totalOverdueAmount),
        overdueLongestDays: Number(data?.overdueLongestDays),
        // 为啥要减一， 因为 index == 0 是数据的总和
        overdueNumber:
          dataDetail?.repayOverdueCostBO?.length > 0
            ? dataDetail?.repayOverdueCostBO?.length - 1
            : 0,
        smsUrgeList: urgeList,
      })
        .then(() => {
          message.success('短信发送成功！');
        })
        .catch(() => {})
        .finally(() => {
          setSmsLoading(false);
          setSmsVisible(false);
          selectedPeople.current.select = {};
        });
      // console.log(selectedPeople.current);
    } else {
      message.warning('请选择发送对象的短信模板！');
    }
  };

  useEffect(() => {
    message.destroy();
    if (data?.error) {
      message.error(data?.errorMsg);
    }
  }, [data?.error]);

  useMount(() => {
    getSMSTemplate();
  });

  const contactPhoneBOListColumns: any = [
    {
      title: '联系人',
      dataIndex: 'contactPerson',
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      render: (value: string) => {
        const oldJsx = (
          <span className={globalStyle.pointer}>
            {value}
            {[STATUS_CASE.REVOKE, STATUS_CASE.SETTLE].indexOf(data?.state) < 0 && (
              <Access accessible={access.hasAccess('biz_caller')}>
                &nbsp;
                <PhoneFilled
                  onClick={() => {
                    // useEffect(() => {
                    setHelpStr(
                      JSON.stringify({
                        scene: 'BUSINESS_COLLECTION_CALL',
                        productCode: dataDetail?.productCode,
                        productName: dataDetail?.productName,
                      }),
                    );

                    setDialOuterCallNumber(value);
                  }}
                />
              </Access>
            )}
          </span>
        );

        const externalNetworkJsx = (
          <>
            {desensitizationPhone(value)}
            <Popover content={value} trigger="click">
              <a
                onClick={async () => {
                  // 发送查看日志
                  if (value) {
                    await reportLog(value);
                  }
                }}
              >
                查看
              </a>
            </Popover>
          </>
        );

        return isExternalNetwork() ? externalNetworkJsx : oldJsx;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
    },
    {
      title: '来源',
      dataIndex: 'sourceDesc',
    },
    {
      title: '与借贷人关系',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      valueType: 'option',
      render: (_: any, row: any) => (
        <Popconfirm
          placement="left"
          disabled={row.source === 1}
          title="请再次确认是否删除？"
          onConfirm={() => deleteContactFn(row.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" disabled={row.source === 1}>
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  const contactSmsColumns: any = [
    {
      title: '发送对象',
      dataIndex: 'contactPerson',
      width: 200,
      align: 'center',
      render: (_: any, row: any) => (
        <>
          {row.contactPerson}-{row.contactPhone}
        </>
      ),
    },
    {
      title: '所属类别',
      dataIndex: 'contactType',
      width: 150,
      align: 'center',
      render: (_: any, row: any) => {
        if (
          productCode?.substring(0, 4) === SECONDARY_CLASSIFICATION_CODE.SELLER_FACTORING_SECOND
        ) {
          // 明保 直接返回
          return row.remark;
        }
        // 融租小贷
        return <>{row.remark === '本人' ? '客户本人' : '第三方'}</>;
      },
    },
    {
      title: (
        <>
          <span style={{ color: 'red' }}>* </span>短信模板
        </>
      ),
      dataIndex: 'smsModal',
      align: 'center',
      render: (_: any, row: any) => {
        // console.log('CASE2906699819202498986', row);
        let smsTemplate1 = smsTemplate.current.filter(
          (item: any) => !item?.excludesRoles?.includes(row.remark),
        );
        // console.log('smsTemplate1', smsTemplate1);
        let smsThirdTemplate1 = smsThirdTemplate.current.filter(
          (item: any) => !item?.excludesRoles?.includes(row.remark),
        );
        if (row.remark === '本人') {
          // 如果是本人 为 true 的模版要排除
          smsTemplate1 = smsTemplate1.filter((item: any) => item?.noOneself === false);
          smsThirdTemplate1 = smsThirdTemplate1.filter((item: any) => item?.noOneself === false);
        }

        return (
          <Select
            options={
              // 明保 0101 不区分 模版 可以选择所有模版
              productCode?.substring(0, 4) === SECONDARY_CLASSIFICATION_CODE.SELLER_FACTORING_SECOND
                ? [...smsTemplate1, ...smsThirdTemplate1]
                : row.remark === '本人'
                ? smsTemplate1
                : smsThirdTemplate1
            }
            style={{ minWidth: 300 }}
            onChange={(value) => {
              selectedPeople.current.select[`${row.rowKeyId}`] = value;
            }}
          />
        );
      },
    },
  ];

  return (
    <>
      <div style={{ margin: 20 }} id="add-contact-scroll">
        联系人：
        <Button
          onClick={() => setModalVisible(true)}
          type="primary"
          disabled={
            [Number(STATUS_CASE.REVOKE), Number(STATUS_CASE.SETTLE)].indexOf(data?.status) > -1
          }
        >
          添加联系人
        </Button>
        {/* 暗保不能发送短信 */}
        {!(productCode?.substring(0, 4) === '0102') && (
          <Button
            onClick={() => {
              if (selectedPeople.current.rows.length) {
                setSmsVisible(true);
              } else {
                message.warning('请先选中本次需要触达的联系人！');
              }
            }}
            type="primary"
            style={{
              background: '#32cb32',
              borderColor: '#32cb32',
              marginLeft: 20,
            }}
          >
            发送催收短信
          </Button>
        )}
        {isError && productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING && (
          <Button
            onClick={() => {
              getLatestContact();
            }}
            style={{ background: '#32cb32', color: 'white', marginLeft: 20 }}
            loading={loading}
            // type="primary"
          >
            获取企业联系人
          </Button>
        )}
      </div>
      <Table
        style={{ margin: 20 }}
        columns={contactPhoneBOListColumns}
        dataSource={contactPhoneBOList}
        pagination={false}
        rowKey="rowKeyId"
        rowSelection={{
          onSelectAll: (selected, selectedRows) => {
            console.log('selectedRows', selectedRows);
            if (!selected) {
              selectedPeople.current = { rows: [], select: {} };
            } else {
              selectedPeople.current.rows = selectedRows as [];
            }
          },
          getCheckboxProps: (record: any) => {
            const { contactPhone } = record;
            const isPhone = /^\d{11}$/.test(contactPhone);
            return {
              disabled: !isPhone,
            };
          },
          onSelect: (record, selected, selectedRows) => {
            selectedPeople.current.rows = selectedRows as [];
            if (!selected) delete selectedPeople.current.select[`${record.rowKeyId}`];
          },
        }}
      />
      <ModalForm
        title="新建联系人"
        width={600}
        form={formRef}
        layout="horizontal"
        visible={modalVisible}
        onVisibleChange={(visible) => onVisibleChange(visible)}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          maskClosable: false,
        }}
        onFinish={async (values: any) => {
          const params = {
            ...values,
            operator: access?.currentUser?.userName,
            overdueCaseNo: props?.overdueCaseNo,
          };

          const res = await addContact(params);
          if (res.success) {
            message.success('添加成功');
            setModalVisible(false);
            refresh();
          } else {
            message.error(res.msg);
          }
        }}
      >
        <ProFormText
          name="contactPerson"
          labelCol={{ span: 4 }}
          width="md"
          label="联系人姓名"
          rules={[{ required: true, message: '联系人姓名' }]}
        />
        <ProFormText
          name="contactPhone"
          labelCol={{ span: 4 }}
          width="md"
          label="联系人电话"
          rules={[
            { required: true, message: '请输入号码' },
            // 座机也可以
            {
              validator: (_, value) => {
                if (value) {
                  const phoneReg = /^[0-9()\-]+$/;
                  if (!phoneReg.test(value)) {
                    return Promise.reject(new Error('号码格式不正确，仅支持手机号和座机号码'));
                  }
                }
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormText name="remark" labelCol={{ span: 4 }} width="md" label="与借贷人关系" />
      </ModalForm>
      <Modal
        title="手动下发催收短信"
        width={750}
        open={smsVisible}
        destroyOnClose={true}
        onCancel={() => {
          selectedPeople.current.select = {};
          setSmsVisible(false);
        }}
        onOk={onOksendOverdueSMS}
        okText="确定发送"
        confirmLoading={smsLoading}
      >
        <Table
          scroll={{ y: 350 }}
          columns={contactSmsColumns}
          dataSource={selectedPeople.current.rows}
          pagination={false}
        />
      </Modal>
    </>
  );
};

export default React.memo(Contact);
