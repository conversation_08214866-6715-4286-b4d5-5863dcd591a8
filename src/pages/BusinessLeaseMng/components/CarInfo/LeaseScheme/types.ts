/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-01-29 15:03:43
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-02-02 18:10:47
 * @FilePath: /code/lala-finance-biz-web/src/pages/BusinessLeaseMng/components/CarInfo/LeaseScheme/types.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export type ProductItem = {
  productCode: string;
  productName: string;
};

export type PickerColumnItem = {
  label: React.ReactNode;
  value: string;
  key?: string | number;
};

export type LatchPlanProps = {
  orderNo: string;
  carNo: string;
  channelId: string;
  cityName: string;
  carType?: number;
  transferPrice?: number;
  preferentialPrice?: number;
  totalPrice?: number;
};
