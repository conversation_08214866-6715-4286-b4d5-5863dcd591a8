export const repayModeMap = {
  CAR_INSURANCE: {
    3: '车辆分期账单还款',
    4: '订单分期账单还款',
    5: '订单账期还款',
    6: '车辆账单提前结清',
    7: '车辆账单普通还款',
    8: '催收减免',
  },
  FINANCE_LEASE: {
    3: '订单分期账单还款',
    6: '订单账单提前结清',
    8: '催收减免',
    10: '催收回款',
  },
  LOAN_INSTALLMENT_CREDIT: {
    3: '订单分期账单还款',
    6: '订单账单提前结清',
    8: '催收减免',
    10: '催收回款',
  },
};

export type IrepayMode = keyof typeof repayModeMap;

export const customerTypeMap = {
  1: '个人',
  2: '企业',
};

export type IcustomerType = keyof typeof customerTypeMap;

// 审核状态枚举（原来的还款单状态）
export const statusMap = {
  0: '待审批',
  1: '驳回',
  2: '审批通过',
};

// 支付状态枚举
export const payStatusMap = {
  '-1': '取消',
  '0': '待支付',
  '1': '支付中',
  '2': '支付成功',
  '3': '支付失败',
};

// 还款状态枚举
export const repayStatusMap = {
  0: '待入账', //  WAITING_REPAY
  1: '入账中', //  PENDING_REPAY
  2: '已入账', //  DONE_REPAY
  3: '已取消', //  CANCEL_REPAY
};

export enum IstatusEnCode {
  PendingApproval = 0,
  Reject = 1,
  Approved = 2,
}

export type Istatus = keyof typeof statusMap;
export interface IofflineRepayReviewListParams {
  current?: number;
  pageSize?: number;
  businessNo?: string; // 还款单ID
  billNo?: string; // 还款账ID
  orderNo?: string; // 订单号
  subjectUniqueNo?: string; // 车架号
  repayMode?: string; // 线下还款场景
  channelId?: string; // 渠道名称
  accountName?: string; // 客户名称
  repayDateStart?: string; // 还款开始日期 还款日期
  repayDateEnd?: string; // 还款结束日期 还款日期
  creditDateStart?: string; // 入账开始日期 入账日期
  creditDateEnd?: string; // 入账结束日期 入账日期
  customerType?: IcustomerType; // 客户类型 1个人、2企业
  status?: Istatus; // 还款单状态 0 - 待审核 - 驳回 - 审批通过
}

export interface IofflineRepayReviewItem {
  id: number;
  businessNo: string;
  orderNo: string;
  orderNoList: string[];
  billNo: string;
  billNoList: string[];
  applyPerson: string;
  applyName: string;
  secondProductCode: string;
  productCode: string;
  productName: string;
  repayAmount: string;
  exemptionAmount: string;
  repayAmountDetail: string;
  repayDate: string;
  repayDateStart: string;
  repayDateEnd: string;
  repayMode: number;
  repayModeName: string;
  remitType: number;
  repaySerialNo: string;
  channelName: string;
  channelTypeName: string;
  channelId: string;
  accountName: string;
  accountNameList: string[];
  accountNumber: string;
  customerType: number;
  customerTypeName: string;
  remark: string;
  attach: string;
  attachList: { url: string; name: string; uid: string; filePath: string }[];
  creditDate: string;
  creditDateStart: string;
  creditDateEnd: string;
  repayStatus: number;
  repayStatusName: string;
  updatedAt: string;
  createdAt: string;
  status: number;
  statusName: string;
  reason: string;
  approvalLog: string;
  approvalId: string;
  approvalPerson: string;
  approvalTime: string;
  subjectUniqueNo: string;
  subjectUniqueNoList: string[];
  overdueCaseNo: string;
  overdueAmount: string;
  orderAmount: string;
  compensation: number;
  compensationName: string;
  bankName: string;
  bankNo: string;
  bankAccount: string;
  subBranchBank: string;
  smallId: number;
  maxId: number;
  billRspDTOList: BillRspDTOItem[];
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalBreach: string;
  totalLate: string;
}
export interface BillRspDTOItem {
  current: number;
  pageSize: number;
  total: number;
  data: any[];
  id: number;
  billNo: string;
  secondaryClassification: string;
  startTime: string;
  endTime: string;
  billingDate: string;
  totalAmount: string;
  amountPaid: string;
  freezeAmount: string;
  clearTime: string;
  status: number;
  statusName: string;
  subjectMatterNo: string;
  refNo: string;
  refType: number;
  billType: number;
  ownerId: string;
  ownerRole: number;
  scene: string;
  payee: string;
  payeeRole: number;
  lender: string;
  lenderRole: number;
  billCostNo: string[];
  billCost: BillCost[];
  dimension: number;
  extendInfo: ExtendInfo;
  termNumber: string;
  carCount: number;
  accountName: string;
  customerType: number;
  customerTypeName: string;
  channelName: string;
  channelType: number;
  channelTypeName: string;
  orderNo: string;
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalAmountDue: string;
  totalPrincipalDue: string;
  totalInterestDue: string;
  totalOverduePenaltyDue: string;
  totalAmountPaid: string;
  totalPrincipalPaid: string;
  totalInterestPaid: string;
  totalOverduePenaltyPaid: string;
  totalBreach: string;
  totalBreachDue: string;
  totalBreachPaid: string;
  totalLate: string;
  totalLateDue: string;
  totalLatePaid: string;
  totalAmountExemption: string;
  totalPrincipalExemption: string;
  totalInterestExemption: string;
  totalOverduePenaltyExemption: string;
  totalBreachExemption: string;
  totalLateExemption: string;
  productCode: string;
  productName: string;
  productCodeLevelOne: string;
  productCodeLevelTwo: string;
  updateAt: string;
  lendingTime: string;
  dueDate: string;
  overdueDateNumber: number;
}

export interface BillCost {
  costType: number;
  amountDue: string;
  amountPaid: string;
  freezeAmount: string;
  paidDetail: PaidDetail[];
}

export interface PaidDetail {
  paymentMode: string;
  amount: string;
}

export interface ExtendInfo {
  count: number;
}

export interface IsubmitBillAudit {
  businessNo: string;
  auditStatus?: number;
  auditRemark?: string;
}

export interface IapplyOrderInfo {
  remissionList?: [];
  bankAmountDate?: string;
  bankAmount?: number;
  current: number;
  pageSize: number;
  id: number;
  businessNo: string;
  orderNo: string;
  orderNoList: string[];
  billNo: string;
  billNoList: string[];
  applyPerson: string;
  applyName: string;
  secondProductCode: string;
  productCode: string;
  productName: string;
  repayAmount: string;
  exemptionAmount: string;
  repayAmountDetail: string;
  repayDate: string;
  repayDateStart: string;
  repayDateEnd: string;
  repayMode: number;
  repayModeName: string;
  remitType: number;
  repaySerialNo: string;
  channelName: string;
  channelTypeName: string;
  channelId: string;
  accountName: string;
  accountNameList: string[];
  accountNumber: string;
  customerType: number;
  customerTypeName: string;
  remark: string;
  attach: { name: string; url: string; uid: string; filePath: string }[];
  attachList: { name: string; url: string; uid: string; filePath: string }[];
  creditDate: string;
  creditDateStart: string;
  creditDateEnd: string;
  repayStatus: number;
  repayStatusName: string;
  updatedAt: string;
  createdAt: string;
  status: number;
  statusName: string;
  reason: string;
  approvalLog: string;
  approvalId: string;
  approvalPerson: string;
  approvalTime: string;
  subjectUniqueNo: string;
  subjectUniqueNoList: string[];
  overdueCaseNo: string;
  overdueAmount: string;
  orderAmount: string;
  compensation: number;
  compensationName: string;
  bankName: string;
  bankNo: string;
  bankAccount: string;
  subBranchBank: string;
  smallId: number;
  maxId: number;
  billRspDTOList: IbillRspDTOItem[];
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalBreach: string;
  totalLate: string;
  payStatus: string; //  支付状态
  payRspInfo: any; //  二维码链接
  notifyPhone: string; //  短信下发手机号
  actualRepayRole: string; //  实际还款方
  remissionApprovalStatusName: string; //  减免审核状态
  remissionApprovalStatus?: number; //  减免审核状态
}

export interface IbillRspDTOItem {
  current: number;
  pageSize: number;
  total: number;
  data: any[];
  id: number;
  billNo: string;
  secondaryClassification: string;
  startTime: string;
  endTime: string;
  billingDate: string;
  totalAmount: string;
  amountPaid: string;
  freezeAmount: string;
  clearTime: string;
  status: number;
  statusName: string;
  subjectMatterNo: string;
  subjectMatterNoList: string[];
  refNo: string;
  refType: number;
  billType: number;
  ownerId: string;
  ownerRole: number;
  scene: string;
  payee: string;
  payeeRole: number;
  lender: string;
  lenderRole: number;
  billCostNo: string[];
  billCost: IbillCost[];
  dimension: number;
  extendInfo: ExtendInfo;
  termNumber: string;
  carCount: number;
  accountName: string;
  customerType: number;
  customerTypeName: string;
  channelName: string;
  channelType: number;
  channelTypeName: string;
  orderNo: string;
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalAmountDue: string;
  totalPrincipalDue: string;
  totalInterestDue: string;
  totalOverduePenaltyDue: string;
  totalAmountPaid: string;
  totalPrincipalPaid: string;
  totalInterestPaid: string;
  totalOverduePenaltyPaid: string;
  totalBreach: string;
  totalBreachDue: string;
  totalBreachPaid: string;
  totalLate: string;
  totalLateDue: string;
  totalLatePaid: string;
  totalAmountExemption: string;
  totalPrincipalExemption: string;
  totalInterestExemption: string;
  totalOverduePenaltyExemption: string;
  totalBreachExemption: string;
  totalLateExemption: string;
  productCode: string;
  productName: string;
  productCodeLevelOne: string;
  productCodeLevelTwo: string;
  updateAt: string;
  lendingTime: string;
  dueDate: string;
  overdueDateNumber: number;
}

export interface IbillCost {
  costType: number;
  amountDue: string;
  amountPaid: string;
  freezeAmount: string;
  paidDetail: IpaidDetail[];
}

export interface IpaidDetail {
  paymentMode: string;
  amount: string;
}

export interface IapplyOrderExportParams extends IofflineRepayReviewListParams {
  businessNoList: string[];
}
