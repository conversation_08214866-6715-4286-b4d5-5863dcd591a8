export type EditRuleParams = {
  configId?: string;
  capital: string;
  ruleCode: string;
  ruleDay: number;
  productCode: string;
  productType: string;
};

export type PayRuleListItem = {
  configId: string;
  capital: string;
  rule: string;
  productCode: string;
  status: number;
  ruleDay: number;
};

export type QuertPayRuleParams = {
  current?: number;
  pageSize?: number;
  productType?: string;
  productCode?: string;
  capital?: string;
  rule?: string;
  ruleDay?: number;
};

export type SwitchPayRuleConfigParams = {
  id: string;
  status: number;
};

export type PayBlackListItem = {
  id: string;
  productType: string;
  productCode: string;
  type: string;
  userId: string;
  userName: string;
  channel: string;
};

export type EditPayBlackListParams = {
  configId?: string;
  type: string;
  item: {
    userId?: string;
    channel?: string;
  }[];
  productCode: string;
  productType: string;
};

export type QuertPayBlackListParams = {
  current?: number;
  pageSize?: number;
  productType?: string;
  productCode?: string;
  type?: string;
  userId?: string;
  userName?: string;
  channel?: string;
};

export type EditBuyParams = {
  configId?: string;
  capital: string;
  rule: string;
  ruleDay: number;
  capitalRuleCode: string;
  capitalRuleDay: number;
  productCode: string;
  productType: string;
};

export type EditBuyBlackListParams = {
  configId?: string;
  type: string;
  item: {
    userId?: string;
    channel?: string;
  }[];
  productCode: string;
  productType: string;
};

export type BuyRuleListItem = {
  configId: string;
  capital: string;
  ruleCode: string;
  ruleDay: number;
  capitalRuleCode: string;
  capitalRuleDay: number;
  productCode: string;
  productType: string;
  status: number;
};
