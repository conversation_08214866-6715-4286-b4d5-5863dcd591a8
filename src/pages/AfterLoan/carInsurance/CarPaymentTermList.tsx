/**
 * 车险 - 车辆账期 支持跨页勾选和虚拟列表
 */ // @ts-ignore
import type { ActionType, ProFormInstance } from '@ant-design/pro-components'; // @ts-ignore
import { ProTable } from '@ant-design/pro-components';
import React, { memo, useEffect, useRef, useState } from 'react';

import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import { useAccess, useModel } from '@umijs/max';
import BigNumber from 'bignumber.js';
import { VList } from 'virtuallist-antd';
import { getCarPaymentTermListColumn } from './columns/CarPaymentTermListColumn';
import OfflineRepay from './components/OfflineRepay';
import { billExport, billList } from './services';
import type { IbillListItem, IbillListParams } from './types';
import { IdimensionEnCode } from './types';

type Props = {};
const CarInsuranceRepayDetailList: React.FC<Props> = () => {
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Record<string, string[]>>({});
  // 无论分页怎么变化 只要含有key 就会被勾选
  const [allSelectedRowKeys, setAllSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<string, IbillListItem[]>>({});
  const [allSelectedRows, setAllSelectedRows] = useState<IbillListItem[]>([]);
  const { initialState = {} }: any = useModel<any>('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode, channelLevel } = currentUser;
  const [currentPage, setCurrentPage] = useState(1);

  const [isBigData, setIsBigData] = useState(false); // 是否是大数据量 大数据量下会卡顿 所以启用虚拟列表

  const actionRef = useRef<ActionType>();

  useEffect(() => {
    setAllSelectedRows(
      Array.from(
        new Map(
          Object.values(selectedRows)
            .flat(2)
            ?.map((item) => [item.id, item]),
        ).values(),
      ) as any,
    );
    setAllSelectedRowKeys([...new Set(Object.values(selectedRowKeys).flat(2))] as any);
  }, [selectedRows, selectedRowKeys]);

  function caculate(rows: any) {
    const amount = rows.reduce((pre: any, cur: any) => {
      return new BigNumber(cur.totalAmountUnpaid || 0).plus(pre);
    }, 0);
    const applyAmount = Number(new BigNumber(amount));
    return applyAmount;
  }

  function getSelectedAlertRender() {
    const amount = caculate(allSelectedRows);
    return allSelectedRows.length ? (
      <div style={{ padding: '0 24px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          已经选择 {allSelectedRows.length} 条数据, 正常还款情况下剩余未还总额
          <span style={{ color: 'red' }}>{amount} 元</span>
        </div>
        <a
          onClick={() => {
            setAllSelectedRows([]);
            setSelectedRowKeys({});
            setSelectedRows({});
          }}
        >
          取消选择
        </a>
      </div>
    ) : null;
  }

  return (
    <ProTable
      search={{
        defaultCollapsed: false,
        labelWidth: 'auto',
      }}
      columns={getCarPaymentTermListColumn({ channelCode, access, channelLevel })}
      request={async (values) => {
        const { current = 1, pageSize = 20, billNo, termList } = values;
        const params: IbillListParams = {
          ...values,
          dimension: IdimensionEnCode.TERM_BILL,
          current,
          pageSize,
          secondaryClassification: '0303',
          billNoList: billNo ? [billNo] : undefined,
          termList: termList ? [termList] : undefined,
        };
        return billList(removeBlankFromObject(filterProps(params)));
      }}
      expandable={{}}
      rowKey="id"
      scroll={isBigData ? { y: window.innerHeight - 224 } : { x: 'max-content' }}
      components={
        isBigData
          ? VList({
              height: window.innerHeight - 224,
            })
          : null
      }
      pagination={{
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onShowSizeChange: (currentPage, pageSize) => {
          setCurrentPage(currentPage);
          if (pageSize >= 500) {
            setIsBigData(true);
          } else {
            setIsBigData(false);
          }
        },
      }}
      tableExtraRender={() => {
        return getSelectedAlertRender();
      }}
      rowSelection={{
        selectedRowKeys: allSelectedRowKeys,
        onChange: (_selectedRowKeys, _selectedRowsArg) => {
          setSelectedRowKeys({ ...selectedRowKeys, [currentPage]: _selectedRowKeys });
          setSelectedRows({ ...selectedRows, [currentPage]: _selectedRowsArg });
        },
      }}
      tableAlertOptionRender={false}
      tableAlertRender={false}
      formRef={formRef}
      toolBarRender={() => {
        return [
          access.hasAccess('bill_apply_export_postLoanMng_afterLoanList') && (
            <AsyncExport
              getSearchDataTotal={async () => {
                const values = formRef.current?.getFieldsFormatValue?.();
                const { billNo, termList } = values;
                const params: IbillListParams = {
                  ...values,
                  billNoList: billNo ? [billNo] : undefined,
                  termList: termList ? [termList] : undefined,
                  dimension: IdimensionEnCode.TERM_BILL,
                  current: 1,
                  pageSize: 1,
                  secondaryClassification: '0303',
                };
                const data = await billList(removeBlankFromObject(filterProps(params)));
                return data?.total;
              }}
              getSearchParams={() => {
                const values = formRef.current?.getFieldsFormatValue?.();
                const { current = 1, pageSize = 20, billNo, termList } = values;
                const params: IbillListParams = {
                  ...values,
                  billNoList: billNo ? [billNo] : undefined,
                  termList: termList ? [termList] : undefined,
                  dimension: IdimensionEnCode.TERM_BILL,
                  current,
                  pageSize,
                  secondaryClassification: '0303',
                };
                return removeBlankFromObject(filterProps(params));
              }}
              getSelectedParams={() => {
                const values = formRef.current?.getFieldsFormatValue?.();
                return {
                  billNoList: allSelectedRows.map((item) => item.billNo),
                  dimension: IdimensionEnCode.TERM_BILL,
                  secondaryClassification: '0303',
                  channelCode: values?.channelCode,
                };
              }}
              getSelectedTotal={() => {
                return allSelectedRows.length;
              }}
              exportAsync={billExport}
              taskCode={[ItaskCodeEnValueEnum.REPAY_CAR_INSURANCE_BILL]}
            />
          ),
          access.hasAccess('bill_order_info_postLoanMng_afterLoanList') && (
            <OfflineRepay
              selectedRows={allSelectedRows}
              dimension={IdimensionEnCode.TERM_BILL}
              actionRef={actionRef}
            />
          ),
        ];
      }}
    />
  );
};

export default memo(CarInsuranceRepayDetailList);
