import { ECARINSURANCE_OPERATE_ACTION } from '@/pages/CarInsurance/type';
/**
 * 车险渠道类型
 */
export enum EcarInsuranceChannelType {
  HUO_LA_LA = '货拉拉',
  LLP = '啦啦拍档',
  OTHER = '其他',
}

/**
 * 还款方式
 */
export enum ErepaymentMode {
  ALL_AT_ONCE = '一次本息',
  PRINCIPAL_EQUALS_INTEREST = '等额本息',
}
/**
 * 车险订单状态
 */
export const CarInsuranceStatusMap = {
  0: '草稿',
  2: '待领取',
  10: '待审核',
  11: '审核驳回',
  20: '待签约',
  30: '待首付',
  31: '待首付审核',
  32: '首付审核驳回',
  40: '待放款',
  51: '还款中',
  60: '已逾期',
  70: '正常结清',
  71: '逾期结清',
  72: '提前结清',
  80: '已退保',
  81: '已取消',
  '-1': '失效',
};

/**
 * 车险订单所有的操作文案 枚举
 */
export const EcarInsuranceOperate = {
  [ECARINSURANCE_OPERATE_ACTION.DELETE_DRAFT]: '删除草稿',
  [ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT]: '保存草稿',
  [ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW]: '提交审核',
  [ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER]: '上传首付凭证',
  [ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW_REJECT]: '审核驳回',
  [ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_REJECT]: '首付款驳回',
  [ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS]: '审核通过',
  [ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_CHECK_PASS]: '首付审核通过',
  [ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION]: '取消申请',
  [ECARINSURANCE_OPERATE_ACTION.CHECK_VEHICLE]: '校验车辆',
  [ECARINSURANCE_OPERATE_ACTION.SIGN_SUCCESS]: '签约成功',
  // 以上全部走 doAction 接口

  [ECARINSURANCE_OPERATE_ACTION.CLOSED]: '关闭', // 返回列表页面
  [ECARINSURANCE_OPERATE_ACTION.MONTHLY_CALCU]: '月供测算', // 从详情获取信息展示
  [ECARINSURANCE_OPERATE_ACTION.SIGN_QR_CODE]: '签约二维码', // 从详情获取信息展示
  [ECARINSURANCE_OPERATE_ACTION.WITHDRAW]: '撤回',
  [ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_QR_CODE]: '首付二维码', // 从详情获取信息展示
};
// DELETE_DRAFT,
// SAVE_DRAFT,SUBMIT_FOR_REVIEW,UPLOAD_DOWN_PAYMENTS_VOUCHER,REJECT,SUBMIT_PASS,SIGN_SUCCESS,UPLOAD_DOWN_CHECK_PASS
/**
 * 车险订单
 * ui中车险订单状态只有11个
 */
export enum EcarInsuranceStatus {
  DRAFT = 0, // 1草稿
  TO_BE_COLLECTED = 2, // '待领取',
  PENDING = 10, // 2'待审核',
  REJECT = 11, // 3'审核驳回',
  SIGN = 20, //  4'待签约'
  TO_BE_DOWN_PAYMENT = 30, // 5'待首付',
  TO_BE_DOWN_PAYMENT_AUDIT = 31, // 6'待首付审核',
  TO_BE_DOWN_PAYMENT_REJECT = 32, // 7 首付款驳回
  LOAN_PENDING = 40, //  8'待放款',
  REPAYING = 51, // 9 '还款中',
  OVERDUE = 60, //10 '已逾期',
  SETTLE = 70, // 11'已结清',
  OVERDUE_SETTLE = 71, //逾期结清
  EARLY_SETTLE = 72, //提前结清
  SURRENDERED = 80, // 12'已退保',
  CANCELED = 81, //13 '已取消',
  INVALIDATION = -1, // 14 失效
}

// 获取放款回单状态
export const LoanReceiptTicketStatusMap = [
  EcarInsuranceStatus.REPAYING,
  EcarInsuranceStatus.OVERDUE,
  EcarInsuranceStatus.SETTLE,
  EcarInsuranceStatus.OVERDUE_SETTLE,
  EcarInsuranceStatus.EARLY_SETTLE,
  EcarInsuranceStatus.SURRENDERED,
];

// 需要轮询的订单状态 -> 其他状态无法走到取消，如果新增需要补充
export const needPollingOrderStatus = [
  EcarInsuranceStatus.TO_BE_COLLECTED, // 待领取
  EcarInsuranceStatus.PENDING, // 待审核
  EcarInsuranceStatus.SIGN, // 待签约
  EcarInsuranceStatus.TO_BE_DOWN_PAYMENT, // 待首付
  EcarInsuranceStatus.TO_BE_DOWN_PAYMENT_AUDIT, // 待首付审核
  EcarInsuranceStatus.TO_BE_DOWN_PAYMENT_REJECT, // 首付款驳回
];

/**
 * 车险订单 - 借款人类型
 */
export enum EloanUserType {
  COMPANY = '企业',
  PERSONAL = '个人',
}

export type UloanUserType = keyof typeof EloanUserType;
/**
 * 车险订单 - 借款人身份
 */
export enum EloanUseridentity {
  DRIVE_LICENCE_OWNER = '行驶证车主',
  ACTUAL_USER = '实际使用人',
}

/**
 * 车险订单 - 保险公司来源
 * 大写一眼看上去不好理解其意思
 */
export enum EinsuranceCompanySource {
  FINANCE_RECOMMEND = '金融推荐',
  CHANNEL_RECOMMEND = '渠道推荐',
}

//投保主体枚举
export enum EinsuranceSubject {
  HUO_MAN_MAN = '广州货满满汽车咨询有限公司',
  YI_LI_XIN = '易立信区块链科技(广州)有限公司',
}

/**
 * 车辆状态 - 在贷后管理 还款管理-车险 详情中 按照车辆查看用到
 */
export const carStatusMap = {
  0: '待放款',
  1: '待还款',
  2: '提前结清',
  3: '结清',
  4: '逾期',
  5: '逾期结清',
  6: '坏账',
  7: '待完单',
  8: '单期代偿',
  9: '代偿结清',
  10: '退保结项',
};
/**
 * 车辆状态 - 在贷后管理 还款管理-车险 详情中 按照车辆查看用到 枚举
 */
export enum EcarStatus {
  PENDING_LOAN = 0, // 待放款
  PENDING_REPAYMENT = 1, // '待还款',
  SETTLE_EARLY = 2, // 提前结清
  SETTLE = 3, //  '结清',
  OVERDUE = 4, // '逾期',
  OVERDUE_SETTLEMENT = 5, // '逾期结清',
  BAD_DEBT = 6, // '坏账',
  TO_BE_COMPLETE = 7, // '待完单',
  COMPENSATE_TERM = 8, // '单期代偿',
  COMPENSATE_SETTLEMENT = 9, // '代偿结清',
  POLICY_CANCELLATION = 10, // '退保结项',
}
