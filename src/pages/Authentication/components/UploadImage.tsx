import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { message, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import React, { useState } from 'react';

// const beforeUpload = (file: RcFile) => {
//   const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
//   if (!isJpgOrPng) {
//     message.error('You can only upload JPG/PNG file!');
//   }
//   const isLt2M = file.size / 1024 / 1024 < 2;
//   if (!isLt2M) {
//     message.error('Image must smaller than 2MB!');
//   }
//   return isJpgOrPng && isLt2M;
// };

type Props = {
  onChange?: (filePath: string) => void;
};
const UploadImage: React.FC<Props> = (props) => {
  const { onChange } = props;
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const [isPdf, setIsPdf] = useState<boolean>(false);
  const handleChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      const { code, data = {} } = info.file.response || {};
      const { filePath, netWorkPath, errMsg } = data;
      if (code === 200) {
        setIsPdf(info.file?.type === 'application/pdf' ? true : false);
        setImageUrl(netWorkPath);
        onChange?.(filePath);
        message.success('上传成功');
      } else {
        message.error('上传有误' + errMsg);
      }
      setLoading(false);
    }
  };

  function beforeUpload(file: RcFile) {
    const format = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
    if (!format.includes(file?.type)) {
      message.error('格式为仅支持.pdf,.png,.jpg,.jpeg,.webp');
      return false || Upload.LIST_IGNORE;
    } else if (file?.size > 5 * 1024 * 1024) {
      message.error('图片需小于5M');
      return false || Upload.LIST_IGNORE;
    } else {
      return true;
    }
  }
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );
  const baseUrl = getBaseUrl();
  const action = baseUrl ? baseUrl : '';
  return (
    <>
      <Upload
        listType="picture-card"
        className="avatar-uploader"
        showUploadList={false}
        action={`${action}/base/oss/common/uploadfile`}
        // action={`/repayment/repay/uploadRepayPic`}
        headers={{ ...getAuthHeaders() }}
        name="file"
        accept=".pdf,.png,.jpg,.jpeg,.webp"
        data={{
          destPath: 'insurance/policy/vehicleLicense/',
          acl: 'PUBLIC_READ',
          attachment: false,
        }} // 后端商量默认格式
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        {imageUrl ? (
          isPdf ? (
            <a
              href={imageUrl}
              target="_blank"
              rel="noreferrer"
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              预览该文件
            </a>
          ) : (
            <img src={imageUrl} alt="avatar" style={{ width: '100%', height: '100%' }} />
          )
        ) : (
          uploadButton
        )}
      </Upload>
    </>
  );
};

export default UploadImage;
