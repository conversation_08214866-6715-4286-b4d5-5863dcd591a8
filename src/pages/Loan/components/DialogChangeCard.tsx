import { ModalForm } from '@ant-design/pro-form';
import { history } from '@umijs/max';
import { Button, message } from 'antd';
import React, { useState } from 'react';
import { changeCard } from '../service';

export type DialogChangeCardProps = {
  status: number | undefined;
  visible: boolean;
  onVisibleChange: (visible: boolean, isShowOfflineLoan?: boolean) => void;
  productCode: string;
  refresh: () => void;
};

const DialogChangeCard: React.FC<DialogChangeCardProps> = ({
  status,
  visible,
  onVisibleChange,
  productCode,
  refresh,
}) => {
  const { lendingNo } = history.location.query as { lendingNo: string };
  const [limitVisible, setLimitVisible] = useState(false);
  const contentSty = {
    fontSize: '14px',
  };

  return (
    <ModalForm
      title={status === 0 ? '是否确认触发更换银行卡环节？' : '提示'}
      layout="horizontal"
      visible={visible}
      width={520}
      onVisibleChange={(value) => {
        onVisibleChange(value);
      }}
      onFinish={async () => {
        try {
          const res = await changeCard(lendingNo, productCode);
          if (res.ret === 10024) {
            setLimitVisible(true);
            return;
          }
          refresh();
          message.success('更换操作成功');
          onVisibleChange(false);
        } catch (err: any) {
          message.error({
            content: err.message,
            duration: 5,
          });
        }

        return true;
      }}
      submitter={{
        render: (props) => {
          const actions =
            status === 0
              ? [
                  <Button
                    key="ok"
                    type="primary"
                    onClick={() => {
                      if (!limitVisible) {
                        props.submit();
                      }
                      onVisibleChange(false, limitVisible);
                    }}
                  >
                    {!limitVisible ? '确定' : '线下放款'}
                  </Button>,
                  <Button
                    key="cancel"
                    onClick={() => {
                      onVisibleChange(false);
                    }}
                  >
                    取消
                  </Button>,
                ]
              : [
                  <Button
                    key="cancel"
                    type="primary"
                    onClick={() => {
                      onVisibleChange(false);
                    }}
                  >
                    确定
                  </Button>,
                ];

          return actions;
        },
      }}
    >
      {status === 0 && (
        <>
          <p style={contentSty}>触发后，客户需完成银行卡更换流程后才能重新发起放款</p>
        </>
      )}
      {status !== 0 && (
        <>
          <p style={contentSty}>仅放款失败时支持触发更换银行卡</p>
        </>
      )}
      {limitVisible && (
        <>
          <p style={contentSty}>更换银行卡次数已达5次上限，如需继续放款，建议使用线下放款</p>
        </>
      )}
    </ModalForm>
  );
};

export default DialogChangeCard;
