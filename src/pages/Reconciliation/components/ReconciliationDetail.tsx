/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-20 16:59:11
 * @LastEditors: shiyong.la
 * @LastEditTime: 2024-07-19 15:06:09
 * @FilePath: /lala-finance-biz-web/src/pages/Reconciliation/components/ReconciliationDetail.tsx
 * @Description:
 */
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Button, message, Modal, Space, Tooltip } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { ReconciliationDetailList } from '../data';

import {
  applySomeAdjustment,
  applySomeAdjustmentAll,
  getDetailList,
  reconciliationExportAsync,
} from '../service';

import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { filterProps } from '@/utils/tools';
import type { ProFormInstance } from '@ant-design/pro-components';
import { VList } from 'virtuallist-antd';
import AsyncExport from './AsyncExport';

const ReconciliationDetail = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const [selectedRowKeys, setSelectedRowKeys] = useState<Record<string, string[]>>({});
  const [selectedRows, setSelectedRows] = useState<Record<string, ReconciliationDetailList[]>>({});
  const [allSelectedRows, setAllSelectedRows] = useState<ReconciliationDetailList[]>([]);

  // 无论分页怎么变化 只要含有key 就会被勾选
  const [allSelectedRowKeys, setAllSelectedRowKeys] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const columns: ProColumns<ReconciliationDetailList>[] = [
    {
      title: '线下回款流水号',
      key: 'uid',
      dataIndex: 'uid',
      search: false,
      fixed: 'left',
    },
    {
      title: '用户ID',
      key: 'accountNumber',
      dataIndex: 'accountNumber',
    },
    {
      title: '姓名/名称',
      key: 'accountName',
      dataIndex: 'accountName',
    },
    {
      title: '账单ID',
      key: 'billNo',
      dataIndex: 'billNo',
      search: false,
      render: (billNo) => {
        let node: React.ReactNode = <></>;
        if ((billNo as any)?.length > 18) {
          node = (
            <Tooltip title={billNo} trigger="hover">
              <div
                style={{
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  width: 170,
                }}
              >
                {billNo}
              </div>
            </Tooltip>
          );
        } else {
          node = <>{billNo || '-'}</>;
        }

        return node;
      },
    },
    {
      title: '汇款人姓名/名称',
      key: 'bankName',
      dataIndex: 'bankName',
    },
    {
      title: '汇款金额（元）',
      key: 'actualRepaymentAmount',
      dataIndex: 'actualRepaymentAmount',
      search: false,
      render: (_, record) => {
        return Number(new BigNumber(record?.actualRepaymentAmount as any).div(100));
      },
    },
    {
      title: '汇款银行',
      key: 'accountBank',
      dataIndex: 'accountBank',
      search: false,
    },
    {
      title: '汇款账号',
      key: 'bankNumber',
      dataIndex: 'bankNumber',
    },
    {
      title: '收款账号',
      key: 'receiveCardNo',
      dataIndex: 'receiveCardNo',
    },
    {
      title: '收款银行流水号',
      key: 'bankSerialNo',
      dataIndex: 'bankSerialNo',
    },
    {
      title: '收款到账时间',
      dataIndex: 'repayTime',
      key: 'repayTime',
      initialValue: [
        dayjs().subtract(3, 'month').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      fieldProps: {
        allowClear: false,
      },
      valueType: 'dateRange',
      search: {
        transform: (value: any) => ({
          repayTimeStart: `${value[0].split(' ')[0]}`,
          repayTimeEnd: `${value[1].split(' ')[0]}`,
        }),
      },
      render: (_, record) => {
        return record?.repayTime;
      },
    },
    {
      title: '调账状态',
      dataIndex: 'reconciliationStatus',
      key: 'reconciliationStatus',
      valueEnum: {
        0: '不需要调账',
        1: '待调账',
        2: '已发起调账',
      },
    },
    {
      title: '调账流水号',
      dataIndex: 'reconciliationFlowNo',
      key: 'reconciliationFlowNo',
    },
  ];

  const [isBigData, setIsBigData] = useState(false); // 是否是大数据量 大数据量下会卡顿 所以启用虚拟列表

  console.log('selectedRowKeys', selectedRowKeys);
  console.log('allSelectedRows', allSelectedRows);

  useEffect(() => {
    setAllSelectedRows(Object.values(selectedRows).flat(2) as any);
    setAllSelectedRowKeys(Object.values(selectedRowKeys).flat(2) as any);
  }, [selectedRows, selectedRowKeys]);

  function caculate(rows: any) {
    const amount = rows.reduce((pre: any, cur: any) => {
      return new BigNumber(cur.actualRepaymentAmount).plus(pre);
    }, 0);
    const applyAmount = Number(new BigNumber(amount).div(100));
    return applyAmount;
  }

  // 金额的限制 发起调帐和全部发起调帐后端已经限制
  async function handleIsAllowOpen() {
    if (allSelectedRows?.length) {
      //优先以勾选的
      const applyAmount = caculate(allSelectedRows);
      if (applyAmount > 2000 * 10000) {
        message.error('金额大于2000万,不支持导出');
        return false;
      }
    }
    return true;
  }

  function getSelectedAlertRender() {
    const amount = caculate(allSelectedRows);
    return allSelectedRows.length ? (
      <div style={{ padding: '0 24px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          已经选择 {allSelectedRows.length} 条数据
          <span style={{ color: 'red' }}>
            涉及汇款金额 {amount} 元{amount > 2000 * 10000 ? '超过2000万元,不支持批量操作' : ''}
          </span>
        </div>
        <a
          onClick={() => {
            setAllSelectedRows([]);
            setSelectedRowKeys({});
            setSelectedRows({});
          }}
        >
          取消选择
        </a>
      </div>
    ) : null;
  }
  const vComponents = useMemo(() => {
    // 使用VList 即可有虚拟列表的效果
    return isBigData
      ? VList({
          height: window.innerHeight - 224,
          resetTopWhenDataChange: false,
        })
      : null;
  }, [isBigData]);
  return (
    <div>
      <ProTable
        components={vComponents}
        actionRef={actionRef}
        pagination={{
          pageSizeOptions: [10, 20, 50, 100, 500],
          showSizeChanger: true,
          onShowSizeChange: (currentPage, pageSize) => {
            setCurrentPage(currentPage);
            if (pageSize >= 500) {
              setIsBigData(true);
            } else {
              setIsBigData(false);
            }
          },
        }}
        formRef={formRef}
        rowKey="uid"
        scroll={isBigData ? { y: window.innerHeight - 224 } : { x: 'max-content' }}
        search={{ labelWidth: 120, defaultCollapsed: false }}
        request={async (params) => {
          return getDetailList(params);
        }}
        tableExtraRender={() => {
          return getSelectedAlertRender();
        }}
        tableAlertRender={() => null}
        tableAlertOptionRender={() => null}
        rowSelection={{
          selectedRowKeys: allSelectedRowKeys,
          onChange: (_selectedRowKeys, _selectedRowsArg) => {
            setSelectedRowKeys({ ...selectedRowKeys, [currentPage]: _selectedRowKeys });
            setSelectedRows({ ...selectedRows, [currentPage]: _selectedRowsArg });
          },
          // getCheckboxProps: (record: ReconciliationDetailList) => {
          //   return {
          //     // disabled: record.reconciliationStatus === 2, // 已发起调账需要禁用
          //   };
          // },
        }}
        headerTitle={
          <>
            <Space size={24}>
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  if (allSelectedRows?.length > 0) {
                    const applyNum = allSelectedRows?.length;
                    const uids = allSelectedRows?.map((item) => (item as any)?.uid);
                    const applyAmountTemp = allSelectedRows?.reduce(
                      (pre, cur: any) => new BigNumber(cur.actualRepaymentAmount).plus(pre) as any,
                      0,
                    );
                    const applyAmount = Number(new BigNumber(applyAmountTemp).div(100));
                    Modal.confirm({
                      title: '发起调账',
                      centered: true,
                      icon: <></>,
                      content: (
                        <>
                          <div>
                            <span>调账笔数：{applyNum}</span>
                          </div>
                          <div>
                            <span>调账金额：{applyAmount}元</span>
                          </div>
                        </>
                      ),
                      okText: '确认',
                      cancelText: '取消',
                      onOk: () => {
                        const params = {
                          applyNum,
                          uids,
                          operator: currentUser?.accountName,
                          applyAmount: Number(applyAmountTemp),
                        };
                        applySomeAdjustment(params as any).then(() => {
                          message.success('操作成功');
                          actionRef?.current?.reload();
                        });
                      },
                    });
                  } else {
                    message.warning('至少勾选一项');
                  }
                }}
              >
                发起调账
              </Button>
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  Modal.confirm({
                    title: '全部发起调账',
                    icon: <></>,
                    content: (
                      <>
                        <div>是否对所有待调账状态流水发起调账操作？</div>
                      </>
                    ),
                    centered: true,
                    okText: '确认',
                    cancelText: '取消',
                    onOk: () => {
                      applySomeAdjustmentAll().then(() => {
                        message.success('操作成功');
                        actionRef?.current?.reload();
                      });
                    },
                  });
                }}
              >
                全部发起调账
              </Button>
            </Space>
          </>
        }
        toolBarRender={() => {
          return [
            <AsyncExport
              key="AsyncExport"
              getSearchDataTotal={async () => {
                const searchParams = filterProps(formRef.current?.getFieldsFormatValue?.());
                const data: any = await getDetailList({
                  ...searchParams,
                  current: 1,
                  pageSize: 10,
                });
                return data?.total;
              }}
              getSelectedTotal={() => {
                return allSelectedRows?.length;
              }}
              handleIsAllowOpen={handleIsAllowOpen}
              exportAsync={reconciliationExportAsync}
              getSearchParams={() => filterProps(formRef.current?.getFieldsFormatValue?.())}
              taskCode={[ItaskCodeEnValueEnum.RECONCILIATION_MGR]}
              getSelectedParams={() => {
                return { uidList: Object.values(selectedRowKeys).flat(2) };
              }}
            />,
          ];
        }}
        dateFormatter="string"
        columns={columns}
      />
    </div>
  );
};

export default ReconciliationDetail;
