/*
 * @Date: 2023-08-29 17:26:49
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-10-23 10:44:06
 * @FilePath: /lala-finance-biz-web/src/pages/HeavyQuotaWhiteList/components/AddHeavyQuotaWhiteListModal.tsx
 * @Description:
 */
import React from 'react';
import ProForm, { ModalForm, ProFormDigit, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Button, Form, message } from 'antd';
import { addWhiteList } from '../service';
import { CLASSIFICATION_CODE_LABEL, SCENE, SECOND_PRODUCT_SOME } from '@/enums';
import { DividerTit } from '@/components';
import OptList from '@/pages/IncreaseDetail/components/OptList';
import globalStyle from '@/global.less';
import { RISK_LEVEL } from '../const';

export type AddConfigModalProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  showMode?: boolean;
  currentRow?: Record<string, string>;
};

const AddHeavyQuotaWhiteListModal: React.FC<AddConfigModalProps> = ({
  visible,
  close,
  onOk,
  showMode = false,
  currentRow,
}) => {
  const [form] = Form.useForm();
  return (
    <>
      <ModalForm
        title={`${showMode ? '查看' : '新建'}配置`}
        layout="horizontal"
        className={globalStyle.formModal}
        visible={visible}
        form={form}
        initialValues={currentRow}
        modalProps={{
          centered: true,
          onCancel: close,
          okText: '提交',
        }}
        submitter={{
          render: (_, defaultDoms) => {
            let doms = defaultDoms;
            if (showMode) {
              doms = [
                <Button
                  key="update"
                  onClick={() => {
                    close();
                  }}
                >
                  关闭
                </Button>,
              ];
            }
            return doms;
          },
        }}
        onFinish={async (values) => {
          // const { userName, userType } = currentRow;
          // const mapUploadFile = convertUploadFileList(allFileList, ['attach']);
          addWhiteList({
            ...values,
            productCode: values?.productCode.map((item: { value: string }) => item.value).join(','),
            // userName,
            // userType,
          }).then(() => {
            message.success('添加成功');
            close();
            onOk();
          });
          return true;
        }}
      >
        <DividerTit title="产品信息" style={{ marginTop: 0 }} />
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: true }]}
            disabled
            initialValue={'01'}
            // request={getUserListEnum}
            options={Object.keys(CLASSIFICATION_CODE_LABEL).map((key) => ({
              value: key,
              label: CLASSIFICATION_CODE_LABEL[key],
            }))}
            width="sm"
            name="productType"
            label="产品一级分类"
          />
          <ProFormSelect
            rules={[{ required: true }]}
            disabled
            initialValue={'whitelist'}
            options={Object.keys(SCENE).map((key) => ({
              value: key,
              label: SCENE[key],
            }))}
            width="sm"
            name="configScene"
            label="配置场景"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: true }]}
            options={Object.keys(SECOND_PRODUCT_SOME).map((key) => ({
              value: key,
              label: SECOND_PRODUCT_SOME[key],
            }))}
            width="sm"
            name="productCode"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
              labelInValue: true,
              mode: 'multiple',
            }}
            disabled={showMode}
            label="产品名称"
          />
        </ProForm.Group>
        <DividerTit title="配置规则" />
        <ProForm.Group>
          <ProFormText
            rules={[{ required: true }]}
            width="sm"
            disabled={showMode}
            name="userName"
            label="企业名称"
          />
          <ProFormText
            rules={[{ required: true }]}
            width="sm"
            disabled={showMode}
            name="idNo"
            label="统一信用代码"
          />
        </ProForm.Group>
        {/* <ProForm.Group>
          <ProFormText
            rules={[{ required: true }]}
            width="sm"
            name="userNo"
            disabled={showMode}
            label="企业ID"
            fieldProps={{
              onChange: (e) => {
                //联动
                // console.log(val);
                queryEnterpriseById(e.target.value).then((res) => {
                  form.setFieldsValue({
                    idNo: res?.data?.certiNo,
                    userName: res?.data?.certiName,
                  });
                });
              },
            }}
          />
        </ProForm.Group> */}
        <ProForm.Group>
          <ProFormDigit
            rules={[{ required: true }]}
            width="sm"
            disabled={showMode}
            name="creditAmount"
            label="额度"
          />
          <ProFormSelect
            rules={[{ required: true }]}
            width="sm"
            disabled={showMode}
            options={Object.keys(RISK_LEVEL).map((key) => ({
              value: key,
              label: RISK_LEVEL[key],
            }))}
            name="riskLevel"
            label="等级"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormDigit
            name="payDays"
            label="还款日"
            rules={[
              { required: true },
              {
                validator: (_, val) => {
                  if (val < 1) {
                    return Promise.reject(new Error('请输入正整数'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
            fieldProps={{ precision: 0 }}
            disabled={showMode}
            width="sm"
          />
        </ProForm.Group>
        {showMode && <OptList configId={currentRow?.configId} />}
      </ModalForm>
    </>
  );
};

export default AddHeavyQuotaWhiteListModal;
