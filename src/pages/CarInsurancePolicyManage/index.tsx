/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:49:13
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-26 09:49:13
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/index.tsx
 * @Description: index.tsx
 */

import React from 'react';
import { CarInsuranceManagementProvider } from './context/CarInsuranceManagementContext';
import CarInsurancePolicyManageProviderContainer from './pages/management';
const CarInsurancePolicyManage: React.FC = () => {
  return (
    <CarInsuranceManagementProvider>
      <CarInsurancePolicyManageProviderContainer />
    </CarInsuranceManagementProvider>
  );
};

export default CarInsurancePolicyManage;
