/*
 * @Author: your name
 * @Date: 2021-10-19 17:32:53
 * @LastEditTime: 2025-04-10 13:54:07
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/product-draft-list.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { getProductNameEnum } from '@/services/enum';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { ModalForm } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import { message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import type { ProductListItem } from './data';
import { delProductFromDraft, queryProduct } from './service';

const ProductDraftList: React.FC<any> = () => {
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [curRow, setCurrentRow] = useState<ProductListItem>();
  const [optVisible, handleOptVisible] = useState<boolean>(false);
  const handleConfirm = () => {
    const func = delProductFromDraft(curRow?.id as number);
    return func.then(() => true);
  };

  const columns: ProColumns<ProductListItem>[] = [
    {
      title: '产品ID',
      dataIndex: 'productCode',
    },

    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      search: false,
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondaryClassification',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      valueType: 'select',
      request: getProductNameEnum,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      hideInTable: true,
    },
    {
      title: '创建时间',
      valueType: 'dateRange',
      dataIndex: 'createdAt',
      render: (row) => {
        return row.createdAt;
      },
    },
    {
      title: '上一次保存时间',
      dataIndex: 'lastUpdatedAt',
      valueType: 'dateRange',
      render: (row) => {
        return row.lastUpdatedAt;
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => {
        const { productCode, id } = record;
        return (
          <>
            <Link
              to={`/businessMng/add-product?productCode=${productCode}&fromDraft=1&id=${id}&secondProductCode=${productCode.substring(
                0,
                4,
              )}`}
            >
              查看详情
            </Link>
            {/* <a
              onClick={() => {
                handleOptVisible(true);
                setCurrentRow(record);
              }}
              className={globalStyle?.ml10}
            >
              删除
            </a> */}
          </>
        );
      },
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<ProductListItem>
          // headerTitle="查询表格"
          actionRef={actionRef}
          formRef={formRef}
          rowKey="productCode"
          scroll={{ x: 'max-content' }}
          search={{
            labelWidth: 110,
          }}
          request={async ({ current, ...rest }) => {
            const { data, total } = await queryProduct({
              pageNumber: current,
              status: -1,
              ...rest,
            });
            return {
              data: data,
              success: true,
              // 不传会使用 data 的长度，如果是分页一定要传
              total: total,
            };
          }}
          // toolBarRender={false}
          columns={columns}
        />
        <ModalForm
          title="提示"
          width="400px"
          modalProps={{
            centered: true,
            destroyOnClose: true,
            afterClose: () => {
              setCurrentRow(undefined);
            },
          }}
          visible={optVisible}
          onVisibleChange={handleOptVisible}
          onFinish={async () => {
            const success: boolean = await handleConfirm();
            if (success) {
              message.success(`删除成功`);
            }
            actionRef?.current?.reload();
            handleOptVisible(false);
            return true;
          }}
        >
          <div>
            <ExclamationCircleOutlined className={globalStyle.iconCss} />
            是否确认删除该产品?
          </div>
        </ModalForm>
      </PageContainer>
    </>
  );
};

export default React.memo(ProductDraftList);
