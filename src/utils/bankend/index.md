# 后端枚举 - 做个记录

## 1. 企业认证状态

> > > > > > > Stashed changes

```java
// 页面路径:  用户管理 - 企业用户 - 认证管理
// 状态
NOT_CERTIFIED((byte) 1, "未认证"),
CERTIFICATION_SUCCESSFUL((byte) 2, "企业认证成功"),
AUTHENTICATION_FAILED((byte) 3, "企业认证失败"),
WAITING_AUTHORIZATION((byte) 4, "待授权"),
AUTHORIZED((byte) 5, "已授权"),
```

## 2. 车险渠道类型

```java
HUO_LA_LA(1, "货拉拉"),
LLP(2, "啦啦拍档"),
OTHER(3, "其他");
```

## 3. 车险订单状态

```java
// 有些状态车险订单不可能会有
DRAFT(0, "草稿", "保存草稿"),

PENDING(10, "待审核", "提交审核"),
REJECT(11, "审核驳回", "审核不通过"),

SIGN(20, "待签约", "审核通过"),
SIGN_FAIL(21, "签约失败", ""),

TO_BE_DOWN_PAYMENT(30, "待首付", "完成签约"),
TO_BE_DOWN_PAYMENT_AUDIT(31, "待首付审核", "上传首付凭证"),
TO_BE_DOWN_PAYMENT_REJECT(32, "首付款驳回", "首付凭证不通过"),

LOAN_PENDING(40, "待放款", "首付凭证通过"),
LOAN_REJECT(41, "放款拒绝", "放款拒绝"),
LOAN_FAIL(42, "放款失败", "放款失败"),
LOAN_ALLEGED(43, "已放款", "放款成功"),

TO_BE_REPAID(50, "待还款", "放款成功"),
REPAYING(51, "还款中", ""),
REPAYING_FAIL(52, "还款失败", ""),

OVERDUE(60, "已逾期", "已逾期"),

SETTLE(70, "已结清", "已结清"),
SETTLE_OVERDUE(71, "逾期结清", "已结清"),
SETTLE_EARLY(72,"提前结清", "已结清"),

SURRENDERED(80,"已退保", "已退保"),
CANCELED(81,"已取消", "取消申请"),

INVALIDATION(-1, "失效", ""),
```

## 4. 车险订单操作枚举

```java
CHECK_VEHICLE("校验车辆"),
DELETE_DRAFT("删除草稿"),
SAVE_DRAFT("保存草稿"),
SUBMIT_FOR_REVIEW("提交审核"),
UPLOAD_DOWN_PAYMENTS_VOUCHER("上传首付凭证"),
SUBMIT_FOR_REVIEW_REJECT("审核驳回"),
DOWN_PAYMENTS_REJECT("首付款驳回"),
SUBMIT_PASS("审核通过"),
SIGN_SUCCESS("签约成功"),
UPLOAD_DOWN_CHECK_PASS("首付审核通过"),
WITHDRAW_APPLICATION("取消申请"),
```

## 4. 车辆状态枚举

```java
PENDING_LOAN(0, "待放款"),
PENDING_REPAYMENT(1, "待还款"),
SETTLE_EARLY(2, "提前结清"),
SETTLE(3, "结清"),
OVERDUE(4, "逾期"),
OVERDUE_SETTLEMENT(5, "逾期结清"),
BAD_DEBT(6, "坏账"),
TO_BE_COMPLETE(7, "待完单"),
COMPENSATE_TERM(8, "单期代偿"),
COMPENSATE_SETTLEMENT(9, "代偿结清"),
POLICY_CANCELLATION(10, "退保结项（车险分期专用）"),
```

## 5.车险 - 线下还款 - 减免费项

```java
INTEREST(1,"利息(年利率)")
IN_ADVANCE_LIQUIDATED(2,"提前结清违约金(日利率)")
OVERDUE_PENALTY_INTEREST(3,"逾期罚息(日利率)")
PRINCIPAL(4,"本金")
LATE_PAYMENT_FEE(5,"逾期滞纳金")
INSURANCE_CANCELLATION_PROFIT(6,"退保盈余")
```

## 6. 车险 - 线下还款 - 还款方式

```java
DEFAULT(1,"全部车辆还款至最新一期")
ALL_CAR_NOT_REPAY_OTHER(2,"其他车辆不还款，部分车辆特殊处理")
ALL_CAR_REPAY_OTHER(3,"其他车辆还款至最新一期，部分车辆特殊处理")
```
