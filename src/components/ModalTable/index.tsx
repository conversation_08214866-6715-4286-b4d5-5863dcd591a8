/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * @Author: your name
 * @Date: 2021-04-15 18:01:12
 * @LastEditTime: 2023-03-14 14:57:18
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/components/ModalTable/index.ts
 */
import { Modal, Table } from 'antd';
import React from 'react';
import DividerTit from '../DividerTit';
import ImagePreview from '../ImagePreview';

interface ModalTableProps {
  title: string;
  modalVisible: boolean;
  dataSource?: any[];
  columns?: any[];
  onCancel: () => void;
  remissionColumn: [];
  remissionDataSource: { costType: string; remissionAmount: number }[];
  repayAttachList: { netWorkPath: string; name: string }[];
}

const ModalTable: React.FC<ModalTableProps> = (props) => {
  const {
    title,
    modalVisible,
    onCancel,
    dataSource,
    columns,
    remissionColumn,
    remissionDataSource,
    repayAttachList,
  } = props;

  return (
    <Modal
      destroyOnClose
      title={title}
      open={modalVisible}
      onCancel={() => onCancel()}
      width="auto"
      centered
      footer={null}
    >
      {dataSource && (
        <Table
          dataSource={dataSource}
          columns={columns}
          rowKey={(record) => {
            return record?.recordingNo + record?.principal + record?.interest;
          }}
        />
      )}
      <DividerTit title="减免信息" style={{ marginBottom: 10 }}>
        <Table
          columns={remissionColumn}
          dataSource={remissionDataSource}
          pagination={false}
          style={{ width: 400 }}
        />
      </DividerTit>
      <DividerTit title="还款凭证" style={{ marginBottom: 10 }}>
        <div style={{ padding: 10 }}>
          {repayAttachList?.length
            ? repayAttachList.reduce(
                (pre: React.ReactNode, cur: { netWorkPath: string; name: string }) => {
                  return (
                    <>
                      {pre}
                      <ImagePreview
                        url={cur?.netWorkPath}
                        fileName={cur?.name}
                        urlList={repayAttachList.map((item) => item.netWorkPath)}
                      >
                        <a target="_blank">{cur?.name}</a>
                      </ImagePreview>

                      <br />
                    </>
                  );
                },
                <></>,
              )
            : '-'}
        </div>
      </DividerTit>
      {props.children}
    </Modal>
  );
};
export default ModalTable;
