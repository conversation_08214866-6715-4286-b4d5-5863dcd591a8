/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2023-03-14 15:21:25
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-12-26 17:52:33
 * @FilePath: /lala-finance-biz-web/src/pages/CallPay/const.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

//tab
export const ACTIVE_TAB = {
  COLLECT_COLLECT: '1', //催收回款
  OFFLINE_REPAYMENT: '2', //线下回款，
  EARLY_SETTLEMENT: '3', //提前结清
};

//线下回款
export const OFFLINE_TAB = {
  SINGLE_REPAYMENT: '44', //单期还款
  COMPENSATE: '55', //代偿结清
  car_insurance_offline_repay_review: '66', // 车险分期线下还款审核（还款单）
  reduction_repay_review: '77', // 减免单
};

export const PAY_TYPE = {
  // 1 : '微信打款',
  2: '普通对公打款',
  3: '线下二维码还款',
  4: '收银台支付',
  5: '汇款余额支付',
  6: '钱包代扣',
};
