/*
 * @Date: 2023-08-29 16:37:49
 * @Author: elisa.z<PERSON>
 * @LastErditors: elisa.zhao
 * @LastEditTime: 2024-05-17 09:46:20
 * @FilePath: /lala-finance-biz-web/src/pages/OperateConfig/index.tsx
 * @Description:
 */
import HeaderTabs from '@/components/HeaderTab';
import { CLASSIFICATION_CODE_LABEL, SCENE } from '@/enums';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, useAccess } from '@umijs/max';
import { Button } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useRef } from 'react';
import AddConfigModal from './components/AddConfigModal';
import type { OperateConfigItems } from './data';
import { getOperateList } from './service';

const itemToLinkMap = {
  increase: 'increase-detail',
  share_enterprise: 'primary-secondary-accounts',
  whitelist: 'heavy-quota-whitelist',
  prerisk_whitelist: 'prerisk-whitelist',
};
const allPermission = [
  'form_increase_operate_manage',
  'form_share_enterprise_operate_manage',
  'form_whitelist_operate_manage',
  'form_prerisk_whitelist_operate_manage',
];

const financePermission = ['form_share_enterprise_operate_manage', 'form_whitelist_operate_manage'];

const OperateConfig: React.FC = () => {
  const access = useAccess();
  // 业务系统-企业财务 只能查看高额白名单和主次账号
  let isFinaceRole = false;

  const hasAllPermission = () => {
    return allPermission.every((p) => access.hasAccess(p));
  };

  const hasFinancePermission = () => {
    return financePermission.every((p) => access.hasAccess(p));
  };

  if (hasFinancePermission()) {
    isFinaceRole = true;
  }

  if (hasAllPermission()) {
    isFinaceRole = false;
  }

  const columns: ProColumns<OperateConfigItems>[] = [
    {
      title: '产品一级分类',
      dataIndex: 'productType',
      key: 'productType',
      valueType: 'select',
      valueEnum: CLASSIFICATION_CODE_LABEL,
      fieldProps: {
        disabled: isFinaceRole,
      },
      initialValue: isFinaceRole ? '01' : undefined,
    },
    {
      title: '配置场景',
      dataIndex: 'configScene',
      key: 'configScene',
      valueType: 'select',
      valueEnum: SCENE,
      fieldProps: {
        mode: 'multiple',
        disabled: isFinaceRole,
      },
      initialValue: isFinaceRole ? ['whitelist', 'share_enterprise'] : undefined,
    },

    {
      title: '操作',
      key: 'option',
      width: 200,
      fixed: 'right',
      valueType: 'option',
      render: (_, row) => (
        <>
          {row?.productType === '01' ? (
            <a
              onClick={() => {
                const basePath = '/operation-manager/operate-config/';

                const path = itemToLinkMap[row.configScene] || '';
                history.push(`${basePath}${path}`);
              }}
            >
              查看详情
            </a>
          ) : (
            '-'
          )}
        </>
      ),
    },
  ];

  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  return (
    <>
      <HeaderTabs />
      <PageContainer>
        <ProTable<OperateConfigItems>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="configId"
          search={{ labelWidth: 120 }}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  optimizationModalWrapper(AddConfigModal)({
                    onOk: () => {
                      actionRef?.current?.reload();
                    },
                    isFinaceRole,
                  });
                }}
              >
                添加
              </Button>,
            ];
          }}
          request={(params) => getOperateList(params)}
        />
      </PageContainer>
    </>
  );
};

export default OperateConfig;
