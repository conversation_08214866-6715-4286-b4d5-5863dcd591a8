import { useAccess } from '@umijs/max';
import { Button, Col, Form, Input, message, Modal, Radio, Row } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { DetailInfo, ListItem, RefundTypeEnum, refundTypeOptions } from '../const';
import '../index.less';
import { cancellationAllocation } from '../service';
import BasicInfo from './BasicInfo';
import RefundAllocationFormItem from './RefundAllocationFormItem';
import UploadPannel from './UploadPannel';

interface ModalProps {
  closeModal: (refresh?: boolean) => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  detailInfo: DetailInfo;
  bankSearchProps: any;
}

const Page: React.FC<ModalProps> = ({
  visible,
  closeModal,
  currentUser,
  record,
  detailInfo,
  bankSearchProps,
}) => {
  const { orderNo, bizNo } = record;
  const access = useAccess();
  const [submitLoading, setSubmitLoading] = useState(false);
  const { asyncBankListBankNoMap } = bankSearchProps;
  const [formOfRefundReject] = Form.useForm();
  //
  const uploadRef = useRef<any>();
  const [uploadLoading, setUploadLoading] = useState(false);
  const handleUploadSubmit = async () => {
    setUploadLoading(true);
    const res: any = await uploadRef?.current?.submitUpload().catch(() => {});
    console.log(res);
    setUploadLoading(false);
  };
  //
  const [refundAllocation, setRefundAllocation] = useState();
  const onRefundAllocationChange = (e: any) => {
    setRefundAllocation(e.target.value);
  };

  const handleRefundRejectSubmit = async () => {
    if (!refundAllocation) {
      message.error('请选择退款分配方式');
      return;
    }
    try {
      await formOfRefundReject.validateFields();
    } catch {
      return Promise.reject();
    }
    //
    const formValues = formOfRefundReject?.getFieldsValue();
    const currentBankInfo: any = asyncBankListBankNoMap[formValues?.bankNo];
    // 入参组装
    const data: any = {
      bizNo,
      createdBy: currentUser?.accountName,
      refundAllocation: refundAllocation,
      // 易人行收款金额
      yirenxingRefundAmount: formValues?.yirenxingRefundAmount || 0,
      // 客户收款账号信息
      userRefundAmount: formValues?.userRefundAmount || 0,
      accountName: formValues?.accountName,
      bankCardNo: formValues?.bankCardNo,
      // 回款确认
      transactionNo: formValues?.transactionNo,
      actualPaymentAmount: formValues?.actualPaymentAmount,
      // 客户银行信息
      bankNumber: formValues?.bankNo,
      subBranchName: currentBankInfo?.subBranchName,
      bankName: currentBankInfo?.bankName,
    };
    console.log('params', data, currentBankInfo);
    setSubmitLoading(true);
    const res: any = (await cancellationAllocation(data).catch(() => {})) || {};
    setSubmitLoading(false);
    if (res?.success) {
      message.success('操作成功');
      closeModal(true);
    }
  };
  useEffect(() => {
    if (visible) {
      formOfRefundReject.setFieldsValue({
        actualPaymentAmount: detailInfo?.actualPaymentAmount,
        transactionNo: detailInfo?.transactionNo,
      });
    }
  }, []);

  return (
    <>
      <Modal
        width={960}
        destroyOnClose
        title={`退保详情`}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            {access?.hasAccess('upload_file_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" onClick={handleUploadSubmit} loading={uploadLoading} ghost>
                保存附件
              </Button>
            )}
            {access?.hasAccess('allocation_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" onClick={handleRefundRejectSubmit} loading={submitLoading}>
                提交
              </Button>
            )}
          </>
        }
      >
        {/*  */}
        <BasicInfo detailInfo={detailInfo} orderStatus={detailInfo.orderStatus} />
        {/*  */}
        <Form
          form={formOfRefundReject}
          labelCol={{ span: 3 }}
          labelAlign="left"
          wrapperCol={{ span: 18 }}
        >
          {/*  */}
          <Row gutter={[16, 0]}>
            {/* <Col span={24}>
              <p className="red-text">
                系统会每小时自动确认一次回款是否到账，自动更新退保状态，如果有其他异常情况，可使用手动确认到账
              </p>
            </Col> */}
            <Col span={12}>
              <Form.Item
                label="银行流水号"
                labelCol={{ span: 6 }}
                name="transactionNo"
                rules={[{ required: true, message: '请输入银行流水号' }]}
              >
                <Input
                  disabled={
                    !access.hasAccess('upload_bank_biz_businessMng_postLoanMng_cancellation')
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="实际到账金额"
                labelCol={{ span: 6 }}
                name="actualPaymentAmount"
                rules={[
                  { required: true, message: '请输入实际到账金额' },
                  {
                    // 大于0的数字，最多两位小数,不能填写除小数点和数字以外的字符
                    validator: (_, val) => {
                      if (isNaN(val) || val?.includes('e')) {
                        return Promise.reject('请输入数字');
                      }
                      if (val !== '' && val <= 0) {
                        return Promise.reject('请输入大于0的数字');
                      }
                      if (val.includes('.') && val.toString().split('.')[1].length > 2) {
                        return Promise.reject('最多两位小数');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  type="number"
                  disabled={
                    !access.hasAccess('upload_bank_biz_businessMng_postLoanMng_cancellation')
                  }
                />
              </Form.Item>
            </Col>
            {/* <Col span={12}>
                  <Form.Item
                    label="实际到账时间"
                    name="actualPaymentTime"
                    rules={[{ required: true, message: '请选择实际到账时间' }]}
                  >
                    <DatePicker
                      style={{ width: '100%' }}
                      disabledDate={(current: any) => {
                        return current && current < dayjs().endOf('day').subtract(1, 'days');
                      }}
                    />
                  </Form.Item>
                </Col> */}
          </Row>
          {/*  */}
          <UploadPannel
            ref={uploadRef}
            detailInfo={detailInfo}
            defaultFileList={detailInfo?.fileInfoAttach ? detailInfo?.fileInfoAttach : []}
          />
          {/*  */}
          <Row gutter={[0, 12]}>
            <Col span={24}>
              <span className="detail-label">驳回原因：</span>
              <span className="red-text">{detailInfo?.refundRejectionReason || '-'}</span>
            </Col>
            <Col span={3}>
              <span className="detail-label">
                <span className="red-text">*</span>退款分配：
              </span>
            </Col>
            <Col span={21}>
              <Radio.Group
                options={refundTypeOptions.map((item: any) => {
                  return {
                    ...item,
                    // 未结清，不可选全部退款至客户
                    disabled: item.value === RefundTypeEnum.ALLOCATION_4 && !detailInfo.settle,
                  };
                })}
                disabled={!access?.hasAccess('allocation_biz_businessMng_postLoanMng_cancellation')}
                value={refundAllocation}
                onChange={onRefundAllocationChange}
              />
            </Col>
          </Row>
          {/* 可编辑表格 */}
          <Form.Item
            noStyle
            shouldUpdate={(preValues, nextValues) =>
              nextValues.actualPaymentAmount !== preValues.actualPaymentAmount
            }
          >
            {({ getFieldValue }) => {
              const actualPaymentAmount = getFieldValue('actualPaymentAmount');
              return (
                <>
                  <RefundAllocationFormItem
                    refundAllocation={refundAllocation}
                    detailInfo={{
                      ...detailInfo,
                      actualPaymentAmount,
                    }}
                    bankSearchProps={bankSearchProps}
                    formInstance={formOfRefundReject}
                  />
                </>
              );
            }}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default Page;
