/* eslint-disable @typescript-eslint/no-use-before-define */
// 进件-选车
/* eslint-disable @typescript-eslint/no-unused-vars */
import { ProCard } from '@ant-design/pro-components';
import { ProFormDependency, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import type { RadioChangeEvent } from 'antd';
import { Form, message, Radio, Spin } from 'antd';
import classNames from 'classnames';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { ORDER_STATUS } from '../../../consts';
import {
  getApplyStoreList,
  getCarBaseList,
  getChannel,
  getCompanyList,
  getPriceInfoByCarId,
  getUserCarInfo,
} from '../../../service';
import type { LatchPlanProps } from '../LeaseScheme/types';
import {
  CAR,
  LICENSE_TYPE,
  LICENSE_TYPE_NAME,
  newCarData,
  NEW_TREE_NAME,
  OLD_TREE_NAME,
  OPTIONS_STATUS,
  usedCarData,
} from './data';
import FormPage from './FormPage';
import './index.less';
import type { CarNode, Channel, ChildOptionsType, PickerData, PriceInfo } from './types';

// 原始渠道列表
let originChannelList: Channel[] = [];

const defaultPriceInfo = {
  preferentialPrice: 0,
  totalPrice: 0,
  transferPrice: 0,
};

const defaultPlanParams: Omit<LatchPlanProps, 'orderNo'> = {
  carNo: '',
  channelId: '',
  cityName: '',
  carType: -1,
  transferPrice: -1,
};

type ChooseCarProps = {
  orderNo: string;
  userName: string;
  orderStatus: number;
  incomeStatus: number;
  readOnly?: boolean;
  onLatchPlanChange: (data: LatchPlanProps) => void;
};

const ChooseCar = forwardRef((props: ChooseCarProps, ref) => {
  const { initialState } = useModel('@@initialState');
  const { orderNo, readOnly, onLatchPlanChange, orderStatus, incomeStatus } = props;
  const [form] = Form.useForm();
  const [newCarForm] = Form.useForm();
  const [oldCarForm] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [userCarInfo, setUserCarInfo] = useState<any>({});
  const [channelList, setChannelList] = useState<any>([]);
  const [channelIdDisabled, setChannelIdDisabled] = useState(false);
  const [newCarList, setNewCarList] = useState<CarNode[]>([]); // 新车辆列表
  const [oldCarList, setOldCarList] = useState<CarNode[]>([]); // 二手车辆列表
  const [licenseTypeOptions, setLicenseTypeOptions] = useState<PickerData[]>([]);

  const [newViewMode, setNewViewMode] = useState(false); // 新车查看模式
  const [oldViewMode, setOldViewMode] = useState(false); // 二手车查看模式

  const [companyOptions, setCompanyOptions] = useState([]); // 上牌公司数据
  const [carCityOptions, setCarCityOptions] = useState([]); // 上牌城市枚举

  const [applyStoreOptions, setApplyStoreOptions] = useState([]); // 申请门店数据
  const [applyCityOptions, setApplyCityOptions] = useState([]); // 申请城市数据
  const [storeDisabled, setStoreDisabled] = useState(false);
  // 价格信息
  const [priceInfo, setPriceInfo] = useState<PriceInfo>(defaultPriceInfo);

  const initStatus = () => {
    setChannelIdDisabled(false);
    setStoreDisabled(false);
  };

  // 初始化渠道列表
  const initChannelList = async (userChannelId?: string) => {
    const { data } = await getChannel({ orderNo });
    let userChannelType;
    // 记录下原始的渠道数据
    originChannelList = data || [];

    const options: any[] = [];
    originChannelList.forEach((item: any) => {
      // 找到用户选择的渠道类型
      if (item.channelId === userChannelId) {
        userChannelType = `${item.channelType}`;
      }
      // 如果已经选过车且选择的渠道被禁用，为了显示出来，也要添加到列表中
      if (
        userChannelId &&
        userChannelId === item.channelId &&
        item.status !== OPTIONS_STATUS.ENABLE
      ) {
        options.push({ label: item.channelName, value: item.channelId });
      } else if (item.status === OPTIONS_STATUS.ENABLE) {
        options.push({ label: item.channelName, value: item.channelId });
      }
    });
    // 设置渠道选项
    setChannelList(options);
  };

  // 初始化上牌公司数据
  const initCompanyOptions = (channelId: number | string, licenseType: number) => {
    if (!channelId && !licenseType) {
      return;
    }
    const options: PickerData[] = [];
    let cityOptions: ChildOptionsType[] = []; // 上牌城市数据
    return new Promise((resovle, reject) => {
      getCompanyList(channelId, licenseType)
        .then((res: any) => {
          const { data } = res;
          (data || []).forEach((item: any) => {
            options.push({ label: item.licenseCompany, value: item.id });
            // 个人上牌类型组装上牌城市
            if (licenseType === LICENSE_TYPE.PERSONAL) {
              cityOptions = item.licenseCityList?.map((city) => {
                return {
                  label: city.licenseCity,
                  value: city.licenseCityCode,
                  parentId: item.id,
                };
              });
            } else {
              const cityItem = item.licenseCityList?.map((city) => {
                return {
                  label: city.licenseCity,
                  value: city.licenseCityCode,
                  parentId: item.id,
                };
              });
              cityOptions = cityOptions.concat(cityItem);
            }
          });
          setCompanyOptions(options);
          setCarCityOptions(cityOptions);
          if (licenseType === LICENSE_TYPE.COMPANY) {
            //如果只有一个选项默认选中
            if (data.length === 1) {
              const company = data[0];
              form.setFieldsValue({
                licenseCompanyId: company.id,
                carCity: company.licenseCityList[0]?.licenseCityCode,
              });
            }
          } else {
            const company = data[0];
            // 只有一个上牌城市时默认选中
            if (cityOptions.length === 1) {
              const carCity = cityOptions[0];
              form.setFieldsValue({
                carCity: carCity.value,
              });
            }
            // 个户也要设置上牌方id
            form.setFieldsValue({
              licenseCompanyId: company.id,
            });
          }

          resovle(options);
        })
        .catch(() => {
          reject();
        });
    });
  };

  // 初始化申请门店
  const initApplyStore = (channelId: number | string) => {
    if (!channelId) {
      return;
    }
    const { extSource } = initialState?.currentUser;
    const options: PickerData[] = [];
    const list: ChildOptionsType[] = []; // 申请城市数据
    return new Promise((resolve, reject) => {
      getApplyStoreList(channelId)
        .then((res: any) => {
          const { data } = res;
          (data || []).forEach((item: any) => {
            options.push({ label: item.storeName, value: item.id });
            list.push({
              label: item.saleCity,
              value: item.saleCityCode,
              parentId: item.id,
            });
          });
          setApplyStoreOptions(options);
          setApplyCityOptions(list);
          //如果只有一个选项默认选中
          if (data.length === 1) {
            const store = data[0];
            form.setFieldsValue({
              applyStoreId: store.id,
              applyCity: store.saleCityCode,
            });
          }
          //申请门店有可能被禁用，判断是否还存在，否则赋空值，让用户重新选择
          if (extSource?.storeId) {
            const fixStoreId = Number(extSource?.storeId);
            const storeId = options.find((item: any) => item.value === fixStoreId);
            if (!storeId) {
              message.warning('门店已被禁用！');
              reject();
              return;
            }
            const chooseApplyCity: any = list.find((item: any) => item.parentId === fixStoreId);
            if (chooseApplyCity) {
              form.setFieldsValue({ applyCity: chooseApplyCity.value });
            }
          }
          resolve(options);
        })
        .catch(() => {
          reject();
        });
    });
  };

  // 初始化汽车列表
  const initCarList = (type: number, channelId: number | string, licenseType: number) => {
    if (!channelId && !licenseType) {
      return;
    }
    return getCarBaseList(type, { channelId, orderNo, licenseType })
      .then((res: any) => {
        if (type === CAR.NEW) {
          setNewCarList(res.data?.carNodeList || []);
        } else {
          setOldCarList(res.data?.carNodeList || []);
        }
      })
      .catch(() => {
        if (type === CAR.NEW) {
          setNewCarList([]);
        } else {
          setOldCarList([]);
        }
      });
  };

  const initUserCarInfo = () => {
    setLoading(true);
    const { channelCode, extSource } = initialState?.currentUser;
    getUserCarInfo(orderNo)
      .then(async (res: any) => {
        const { data } = res;
        if (!data?.channelId) {
          // 如果是渠道账户，需要固定渠道
          if (channelCode) {
            const values: any = {
              channelId: channelCode,
              carType: CAR.NEW,
            };
            if (extSource?.storeId) {
              values.applyStoreId = Number(extSource?.storeId);
              setStoreDisabled(true);
            }
            form.setFieldsValue(values);
            setChannelIdDisabled(true);
            // 初始化渠道列表
            await initChannelList(channelCode);
            // 固定渠道后触发选择渠道逻辑，初始化后续其他数据
            handleChannelChange(channelCode);
          } else {
            // 初始化渠道信息
            initChannelList();
            // 默认选择新车
            form.setFieldsValue({ carType: CAR.NEW });
          }
        } else {
          const {
            channelId,
            licenseType,
            carType,
            totalPrice,
            transferPrice,
            preferentialPrice,
            applyStoreId, //申请门店
            applyCity,
            licenseCompany, // 上牌方名称
            licenseCompanyId, // 上牌公司id
            carCity, // 上牌城市
          } = data;

          // 如果是渠道账户，需要固定渠道
          if (channelCode) {
            setChannelIdDisabled(true);
          }

          // 预审通过禁止修改渠道
          if (orderStatus === ORDER_STATUS.PRE_AUDIT_PASS) {
            setChannelIdDisabled(true);
          }
          // 初始化渠道信息
          await initChannelList(channelId);
          // 初始化上牌类型枚举
          initLicenseTypeOptions(channelId);

          // 渠道和上牌类型都有的话，需要查询车辆列表
          let companyId = '';
          if (data.channelId && data.licenseType) {
            // 初始化上牌公司
            const companys: any = await initCompanyOptions(channelId, licenseType);
            //上牌公司有可能被禁用，判断是否还存在，否则赋空值，让用户重新选择
            companyId = companys.find((item: any) => item.value === licenseCompanyId)
              ? licenseCompanyId
              : undefined;
          }

          // 初始化申请门店
          const stores: any = await initApplyStore(data.channelId);
          //申请门店有可能被禁用，判断是否还存在，否则赋空值，让用户重新选择
          const storeId = stores.find((item: any) => item.value === applyStoreId)
            ? applyStoreId
            : undefined;
          // 设置表单数据
          form.setFieldsValue({
            channelId,
            licenseType,
            carType,
            licenseCompany,
            licenseCompanyId: companyId, //上牌公司
            carCity, // 上牌城市
            applyCity, //申请城市
            applyStoreId: storeId, //申请门店
            totalPrice,
            transferPrice,
            preferentialPrice,
          });
          // 存储用户选车信息
          setUserCarInfo(data);
          // 设置价格信息
          setPriceInfo({ totalPrice, transferPrice, preferentialPrice });

          // 初始化全部车辆列表
          if (channelId && licenseType) {
            await initCarList(carType, channelId, licenseType);
          }

          if (carType === CAR.NEW) {
            // 格式化picker数据
            newCarForm.setFieldsValue(data);
            if (data.carNo) {
              setNewViewMode(true);
            }
          } else if (carType === CAR.OLD) {
            oldCarForm.setFieldsValue(data);
            if (data.carNo) {
              setOldViewMode(true);
            }
          }
          // 如果已经选好车，初始化租赁方案参数
          if (data.carNo) {
            // 没有转让价说明只是保存了草稿没有提交，需要再次查一下车辆价格
            if (!transferPrice) {
              updatePriceInfo();
            } else {
              onLatchPlanChange({
                orderNo,
                channelId,
                carNo: data.carNo,
                carType,
                transferPrice,
                totalPrice,
                preferentialPrice,
                cityName:
                  carType === CAR.NEW
                    ? newCarForm.getFieldValue('cityName')
                    : oldCarForm.getFieldValue('cityName'),
              });
            }
          }
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 初始化
  useEffect(() => {
    if (orderNo) {
      // 先清除一下表单数据再更新
      resetForm();
      initStatus();
      initUserCarInfo();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderNo]);

  //更新价格信息
  const updatePriceInfo = () => {
    const values = form.getFieldsValue();
    const { applyCity, carType, channelId } = values;
    // 没有选择申请城市
    if (!applyCity) {
      setPriceInfo(defaultPriceInfo);
      onLatchPlanChange(defaultPlanParams);
      return;
    }
    let carNo = '';
    let cityName = ' ';
    if (carType === CAR.NEW) {
      carNo = newCarForm.getFieldValue('carNo');
      cityName = newCarForm.getFieldValue('cityName');
    } else {
      carNo = oldCarForm.getFieldValue('carNo');
      cityName = oldCarForm.getFieldValue('cityName');
    }

    if (!carNo) {
      setPriceInfo(defaultPriceInfo);
      onLatchPlanChange(defaultPlanParams);
      return;
    }
    const params = {
      applyCityId: applyCity,
      carId: carNo,
      type: carType,
    };
    getPriceInfoByCarId(params).then((res: any) => {
      setPriceInfo(res.data);
      // 更新租赁计划产品方案
      onLatchPlanChange({
        orderNo,
        channelId,
        carNo,
        carType,
        cityName,
        ...res.data,
      });
    });
  };

  // 上牌类型变更时
  const handleLicenseTypeChange = (value: number) => {
    const licenseType = value;
    if (licenseType === LICENSE_TYPE.PERSONAL) {
      form.setFieldsValue({
        licenseCompany: props.userName,
      });
    } else {
      form.setFieldsValue({
        licenseCompany: undefined,
      });
    }
    const { channelId, carType } = form.getFieldsValue();
    // 重新查询车辆数据
    initCarList(carType, channelId, licenseType);
    // 查询上牌公司
    initCompanyOptions(channelId, licenseType);
    // 保存草稿
    tempSave();
  };
  const initLicenseTypeOptions = (channelId: string) => {
    const channel = originChannelList.find((item) => item.channelId === channelId);
    if (channel) {
      const licenseTypeOpts: any = (channel.licenseTypeList || []).map((value) => {
        return {
          label: LICENSE_TYPE_NAME[value],
          value,
        };
      });
      setLicenseTypeOptions(licenseTypeOpts);
      return licenseTypeOpts;
    }
    return [];
  };
  //渠道变更时
  const handleChannelChange = async (value: string) => {
    // 重置城市表单数据
    form.setFieldsValue({
      licenseCompanyId: undefined, //上牌公司
      carCity: undefined, // 上牌城市
      applyCity: undefined, //申请城市
      applyStoreId: undefined, //申请门店
    });
    const channel = originChannelList.find((item) => item.channelId === value);
    // 找出可选上牌类型
    if (channel) {
      const licenseTypeOpts = initLicenseTypeOptions(channel?.channelId);
      // 只有一个类型，默认选中
      if (licenseTypeOpts.length === 1) {
        const type = licenseTypeOpts[0].value as number;
        form.setFieldsValue({
          licenseType: type,
        });
        handleLicenseTypeChange(type);
      }
    }

    // 查询申请门店
    initApplyStore(value);
    // 重置车辆价格信息
    updatePriceInfo();
  };

  const handleCarTypeChange = (e: RadioChangeEvent) => {
    const { channelId, licenseType } = form.getFieldsValue();
    const value = e.target.value;
    initCarList(value, channelId, licenseType);
    // 更新一下价格信息
    updatePriceInfo();
  };

  // 当表单输入车型码变化时
  const handleCarCodeChange = (carType: number, data: { carCode: string }) => {
    const { channelId, licenseType } = form.getFieldsValue();
    if (!data.carCode) {
      // 如果清空了车型码，需要重新筛选车辆列表
      initCarList(carType, channelId, licenseType);
    }
    // 更新价格信息
    updatePriceInfo();
  };

  // 上牌公司变更时，选择上牌城市
  const handleCompanyChange = (value: string) => {
    console.log(value);
    const chooseCompanyId = value;
    const chooseCarCity: any = carCityOptions.find(
      (item: any) => item.parentId === chooseCompanyId,
    );
    if (chooseCarCity) {
      form.setFieldsValue({ carCity: chooseCarCity.value });
    }
  };

  // 申请门店变更时，选择申请城市
  const handleApplyStoreChange = (value: number) => {
    const chooseStoreId = value;
    const chooseApplyCity: any = applyCityOptions.find(
      (item: any) => item.parentId === chooseStoreId,
    );
    if (chooseApplyCity) {
      form.setFieldsValue({ applyCity: chooseApplyCity.value });
      updatePriceInfo();
    }
  };

  const validateChannel = (obj: any, value: string): Promise<any> => {
    // 校验渠道是否被禁用
    let errorMsg = '';
    const findChannel = originChannelList.find((item) => item.channelId === value);
    if (!findChannel) {
      errorMsg = '当前渠道已被删除，请重新选择！';
      return Promise.reject(new Error(errorMsg));
    }
    if (findChannel && findChannel.status !== OPTIONS_STATUS.ENABLE) {
      errorMsg = '当前渠道已被禁用，请重新选择！';
      return Promise.reject(new Error(errorMsg));
    }
    return Promise.resolve();
  };

  // 组织表单数据
  const getFormValues = () => {
    const values = form.getFieldsValue();
    const {
      channelId,
      licenseType,
      carType,
      carCity,
      applyCity,
      licenseCompany,
      licenseCompanyId,
      applyStoreId,
    } = values;
    // 读取选车数据
    let carValues = {};
    if (carType === CAR.NEW) {
      carValues = newCarForm.getFieldsValue();
    } else {
      carValues = oldCarForm.getFieldsValue();
    }
    // 处理一下申请城市
    const applyCityItem: any = applyCityOptions.find((item: any) => item.value === applyCity);
    const company: any = companyOptions.find((item: any) => item.value === licenseCompanyId);
    const companyName = licenseType === LICENSE_TYPE.PERSONAL ? licenseCompany : company?.label;
    const store: any = applyStoreOptions.find((item: any) => item.value === applyStoreId);

    const params = {
      applyCity, // 申请城市code
      applyCityName: applyCityItem?.label, //申请城市name
      applyStoreId, // 申请门店id
      applyStore: store?.label || '', // 申请门店
      licenseCompany: companyName || '', //上牌公司名
      licenseCompanyId, // 上牌公司code
      carCity, // 上牌城市
      channelId, // 渠道id
      licenseType, // 上牌类型
      carType, // 车辆类型 1.新车 2.二手车
      ...carValues, // 车辆信息
    };
    console.log(params);
    return params;
  };

  // 经过车辆信息校验
  const handleSubmit = async () => {
    const carType = form.getFieldValue('carType');
    try {
      if (carType === CAR.NEW) {
        await form.validateFields();
        await newCarForm.validateFields();
      } else {
        await form.validateFields();
        await oldCarForm.validateFields();
      }
    } catch {
      return Promise.reject();
    }

    const params = getFormValues();
    return Promise.resolve(params);
  };

  // 保存草稿
  const tempSave = () => {
    const params = getFormValues();
    return params;
  };

  const resetForm = () => {
    const values = form.getFieldsValue();
    const { channelId, applyStoreId, applyCity } = values;

    form.resetFields();
    setNewCarList([]);
    setOldCarList([]);
    setCompanyOptions([]);
    // 更新价格信息
    updatePriceInfo();
    // 如果渠道固定，渠道要重新写回去
    if (channelIdDisabled && channelId) {
      form.setFieldsValue({
        channelId,
      });
      handleChannelChange(channelId);
    }
    // 如果门店固定，申请门店和申请城市都要写回表单
    if (!storeDisabled && applyStoreId) {
      form.setFieldsValue({
        applyStoreId,
        applyCity,
      });
    } else {
      setApplyStoreOptions([]);
      setCarCityOptions([]);
    }
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    clear: () => {
      resetForm();
    },
    save: () => {
      return tempSave();
    },
    submit: () => {
      return handleSubmit();
    },
    updatePriceInfo: () => {
      updatePriceInfo();
    },
  }));

  return (
    <Spin spinning={loading}>
      <ProCard title="车辆信息" bordered headerBordered collapsible className="choose-car">
        <Form
          form={form}
          style={{ width: 800 }}
          wrapperCol={{ span: 12 }}
          labelCol={{ span: 4 }}
          initialValues={{
            carType: CAR.NEW,
          }}
        >
          <div>
            <h4>渠道信息</h4>
            <ProFormSelect
              label="选择渠道"
              name="channelId"
              options={channelList}
              readonly={readOnly}
              rules={[{ required: true }, { validateChannel }]}
              fieldProps={{
                onChange: handleChannelChange,
              }}
              disabled={channelIdDisabled}
            />
            <ProFormSelect
              label="上牌类型"
              name="licenseType"
              options={licenseTypeOptions}
              readonly={readOnly}
              rules={[{ required: true }]}
              fieldProps={{
                onChange: handleLicenseTypeChange,
              }}
              disabled={true}
            />
          </div>

          <div>
            <h4>车辆信息</h4>
            <Form.Item name="carType" label="车辆类型">
              <Radio.Group onChange={handleCarTypeChange} disabled={readOnly}>
                <Radio.Button value={CAR.NEW}>新车</Radio.Button>
                <Radio.Button value={CAR.OLD}>二手车</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(preValues, nextValues) => {
                return (
                  nextValues.carType !== preValues.carType ||
                  nextValues.channelId !== preValues.channelId
                );
              }}
            >
              {({ getFieldValue }) => {
                const carType = getFieldValue('carType');
                const channelId = getFieldValue('channelId');
                return (
                  <>
                    <FormPage
                      key="newcar"
                      className={classNames({
                        'form-hidden ': carType === CAR.OLD,
                      })}
                      preCarCode={userCarInfo?.carCode}
                      preCarType={userCarInfo?.carType}
                      orderNo={orderNo}
                      form={newCarForm}
                      readOnly={readOnly}
                      viewMode={newViewMode}
                      carList={newCarList}
                      formData={newCarData}
                      orderStatus={orderStatus}
                      incomeStatus={incomeStatus}
                      formTree={NEW_TREE_NAME}
                      carType={CAR.NEW}
                      channelId={channelId}
                      onCarCodeConfirm={(data) => {
                        handleCarCodeChange(CAR.NEW, data);
                      }}
                      onSubmit={() => {}}
                    />
                    <FormPage
                      key="oldcar"
                      className={classNames({
                        'form-hidden ': carType === CAR.NEW,
                      })}
                      preCarCode={userCarInfo?.carCode}
                      preCarType={userCarInfo?.carType}
                      orderNo={orderNo}
                      readOnly={readOnly}
                      viewMode={oldViewMode}
                      form={oldCarForm}
                      carList={oldCarList}
                      formData={usedCarData}
                      orderStatus={orderStatus}
                      incomeStatus={incomeStatus}
                      formTree={OLD_TREE_NAME}
                      carType={CAR.OLD}
                      channelId={channelId}
                      onCarCodeConfirm={(data) => {
                        handleCarCodeChange(CAR.OLD, data);
                      }}
                      onSubmit={() => {}}
                    />
                  </>
                );
              }}
            </Form.Item>

            {/* <ProFormText
              readonly
              label="车辆转让价"
              fieldProps={{ value: priceInfo.transferPrice || '-' }}
            />
            <ProFormText
              readonly
              label="全包价"
              fieldProps={{ value: priceInfo.totalPrice || '-' }}
            />
            <ProFormText
              readonly
              label="优惠金额"
              fieldProps={{
                value: priceInfo.preferentialPrice ? `-${priceInfo.preferentialPrice}` : '-',
              }}
            /> */}
          </div>
          <div>
            <h4>城市信息</h4>
            <ProFormDependency name={['licenseType']}>
              {({ licenseType }) => {
                if (licenseType === LICENSE_TYPE.PERSONAL) {
                  return (
                    <>
                      <ProFormText label="上牌方" name="licenseCompany" disabled />
                      <ProFormSelect
                        hidden
                        name="licenseCompanyId"
                        options={companyOptions}
                        rules={[{ required: true }]}
                      />
                    </>
                  );
                }
                return (
                  <ProFormSelect
                    label="上牌方"
                    name="licenseCompanyId"
                    readonly={readOnly}
                    options={companyOptions}
                    rules={[{ required: true }]}
                    placeholder="请选择"
                    showSearch
                    fieldProps={{
                      onChange: handleCompanyChange,
                      optionFilterProp: 'label',
                    }}
                  />
                );
              }}
            </ProFormDependency>

            <ProFormSelect
              label="上牌城市"
              name="carCity"
              readonly={readOnly}
              options={carCityOptions}
              rules={[{ required: true }]}
              placeholder="请选择"
              disabled={form.getFieldValue('licenseType') === LICENSE_TYPE.COMPANY}
            />
            {readOnly ? (
              <ProFormText
                readonly
                label="申请门店"
                fieldProps={{ value: userCarInfo.applyStore || '-' }}
              />
            ) : (
              <ProFormSelect
                label="申请门店"
                name="applyStoreId"
                readonly={readOnly}
                options={applyStoreOptions}
                rules={[{ required: true }]}
                showSearch
                fieldProps={{
                  onChange: handleApplyStoreChange,
                  optionFilterProp: 'label',
                }}
                disabled={storeDisabled}
              />
            )}
            {readOnly ? (
              <ProFormText
                readonly
                label="申请城市"
                fieldProps={{ value: userCarInfo.applyCityName || '-' }}
              />
            ) : (
              <ProFormSelect
                label="申请城市"
                name="applyCity"
                readonly={readOnly}
                options={applyCityOptions}
                rules={[{ required: true }]}
                disabled
              />
            )}
          </div>
        </Form>
      </ProCard>
    </Spin>
  );
});

export default ChooseCar;
