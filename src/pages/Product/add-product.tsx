/* eslint-disable react-hooks/exhaustive-deps */

import HeaderTab from '@/components/HeaderTab';
import { CLASSIFICATION, SECONDARY_CLASSIFICATION } from '@/enums';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useModel } from '@umijs/max';
import { Select, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import AddProductOld from './add-product-old';
import InsuranceAddProduct from './insurance/InsuranceAddProduct';
import { queryDefaultDetail, queryProductDetail } from './service';
import type { TsecondaryClassification } from './type';

const AddProduct: React.FC<any> = () => {
  const [classification, setClassification] = useState();
  const [secondaryClassification, setSecondaryClassification] = useState<TsecondaryClassification>(
    'FINANCE_LEASE',
  );
  const { productCode, isAddProduct, secondProductCode } = (history.location.query as unknown) as {
    productCode: string; // 三级产品code 也是最终的code 能够唯一识别 产品
    isAddProduct: boolean;
    secondProductCode: string; // 二级产品code 0301
  };
  const [loading, setLoading] = useState(false);
  const [oldData, setOldData] = useState<any>();

  const {
    setDetailData,
    setCostConfigDTO,
    setPayBackData,
    defaultConfig,
    setDefaultConfig,
    clearModeState,
  } = useModel('Product.insuranceProduct');

  // code 二级产品code 0303 0201
  // 初始化默认配置 获取详情数据
  async function initData(code: string) {
    try {
      setLoading(true);
      // 无论是新增还是编辑都需要获取一份默认数据
      let result = await queryDefaultDetail(code);
      // 先保存一份默认配置
      setDefaultConfig(result?.data);

      if (!isAddProduct && productCode) {
        // 编辑的时候 获取详情数据
        result = await queryProductDetail(productCode);
      }

      setClassification(result?.data?.classification);
      setSecondaryClassification(result?.data?.secondaryClassification);

      // 费用配置需要抽离出来， 否则在与保证金和手续费勾选联动上会有bug
      setCostConfigDTO(result?.data?.costConfigDTO || []);
      // 车险 -- 如果是新增 将默认数据赋给 页面上
      setDetailData(result?.data);
      setPayBackData(result?.data?.repaymentDTO);
      // 以前 --  老的
      setOldData(result?.data);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    initData(secondProductCode);
  }, []);

  useEffect(() => {
    return () => {
      // 由于费用配置保存草稿 再查看详情 费用配置有bug  猜测由于全局状态管理中影响到了
      // 所以在组件卸载的时候清空全局状态管理中的数据
      // 只会执行一次 更新不会执行
      // 卸载时 清空所有全局状态管理中的数据
      clearModeState();
    };
  }, []);
  function renderContent() {
    switch (secondaryClassification) {
      case 'CAR_INSURANCE':
        return <InsuranceAddProduct />; // 小圆车险分期
      default:
        return <AddProductOld data={oldData} defaultConfig={defaultConfig} />;
    }
  }
  return (
    <>
      <HeaderTab />
      <PageContainer
        title={isAddProduct ? '创建产品' : '产品详情'}
        extra={[
          <Select
            key="firstProduct"
            placeholder="请选择一级产品"
            value={classification}
            onChange={(value) => {
              if (value === 'SMALL_LOAN') {
                initData('0303');
                setSecondaryClassification('CAR_INSURANCE');
              }
              if (value === 'FINANCE_LEASE') {
                initData('0201');
                setSecondaryClassification('FINANCE_LEASE');
              }
            }}
            style={{ width: 150 }}
            disabled={!isAddProduct}
          >
            {Object.keys(CLASSIFICATION).map((key) => (
              <Select.Option key={key} value={key} disabled={key === 'SELLER_FACTORING'}>
                {CLASSIFICATION[key]}
              </Select.Option>
            ))}
          </Select>,
          <Select
            key="secondProduct"
            value={secondaryClassification}
            style={{ width: 150 }}
            options={Object.keys(SECONDARY_CLASSIFICATION).map((key) => {
              return {
                value: key,
                label: SECONDARY_CLASSIFICATION[key],
              };
            })}
            placeholder="请选择二级产品"
            disabled
            filterOption
            showSearch
            optionFilterProp="label"
            onChange={(value: TsecondaryClassification) => {
              setSecondaryClassification(value);
            }}
          >
            {/* {Object.keys(SECONDARY_CLASSIFICATION).map((key) => (
              <Select.Option key={key} value={key} >
                {SECONDARY_CLASSIFICATION[key]}
              </Select.Option>
            ))} */}
          </Select>,
        ]}
      >
        <Spin spinning={loading}>{renderContent()}</Spin>
      </PageContainer>
    </>
  );
};

export default React.memo(AddProduct);
