// 下单安装
import AddNewOrderModal from '@/pages/GpsMng/components/AddNewOrderModal';
import { Button } from 'antd';
import { useState } from 'react';

type AddNewOrderProps = {
  orderNo: string;
  refresh: () => void;
};

const AddNewOrder = (props: AddNewOrderProps) => {
  const { orderNo } = props;
  const [showModal, setShowModal] = useState(false);

  return (
    <div style={{ textAlign: 'center', marginTop: 10 }}>
      <Button
        onClick={() => {
          setShowModal(true);
        }}
        type="primary"
      >
        下单安装GPS设备
      </Button>
      <p style={{ marginTop: 10 }}>请绑定车辆通过和签署合同成功后进行GPS安装下单</p>
      <AddNewOrderModal
        open={showModal}
        orderNo={orderNo}
        onSuccess={() => {
          setShowModal(false);
          props.refresh();
        }}
        onCancel={() => {
          setShowModal(false);
        }}
      />
    </div>
  );
};

export default AddNewOrder;
