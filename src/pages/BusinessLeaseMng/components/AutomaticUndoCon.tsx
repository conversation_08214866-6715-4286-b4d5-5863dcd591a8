/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-11-20 16:08:25
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2025-02-17 13:47:09
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/AutomaticUndoCon.tsx
 * @Description: AutomaticUndoCon
 */
import {
  CheckCircleOutlined,
  CheckCircleTwoTone,
  CloseCircleOutlined,
  DownOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { Button, Divider, Dropdown, message, Modal } from 'antd';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { PRE_STATUS } from '../consts';
import { preApprovalCheck, preApprovalSubmit, preCreditAmountInfo } from '../service';

interface BatchPushToRiskProps {
  selectedOrders: any;
  preCheckRef: any;
  onCallback: () => void;
}

export const BatchPushToRisk = React.memo(
  ({ selectedOrders, preCheckRef, onCallback }: BatchPushToRiskProps) => {
    const [visibleFlag, setVisibleFlag] = useState(false);
    const [orderData, setOrderData]: any = useState({});
    const [singleOrder, setSingleOrder] = useState('');
    const [modalTit, setModalTit] = useState<React.ReactNode>('');
    const [loading, setLoading] = useState({ check: false, submit: false });
    const [preAuditData, setPreAuditData] = useState<Record<string, any>>();
    const limitNum = 1;
    // 预审校验按钮点击
    const checkPreBtn = async (order?: string) => {
      if (!selectedOrders.length && !order) {
        message.error('请先选中本次需要处理的订单数据');
        return;
      }
      setSingleOrder(order || '');
      //获取预审数据
      if (order) {
        const preAuditDataRes = await preCreditAmountInfo(order);
        setPreAuditData(preAuditDataRes?.data);
      }
      if (!order) setLoading({ ...loading, check: true });
      const queryData = order
        ? order
        : selectedOrders
            .map((item: any) => {
              return item.orderNo;
            })
            .join(',');
      const res = await preApprovalCheck(queryData).catch((e) => {
        console.log(e);
      });
      if (!order) setLoading({ ...loading, check: false });
      if (res?.msg && res.ret !== 0) {
        message.error(res?.msg);
      }
      if (res?.data && res?.success) {
        setOrderData(res.data);
        setVisibleFlag(true);
        return;
      }
    };
    // 预审提交按钮点击
    const submitPreBtn = async () => {
      setLoading({ ...loading, submit: true });
      const queryData = singleOrder
        ? singleOrder
        : selectedOrders
            .map((item: any) => {
              return item.orderNo;
            })
            .join(',');
      const res = await preApprovalSubmit(queryData).catch((e) => {
        console.log(e);
      });
      setLoading({ ...loading, submit: false });
      setVisibleFlag(false);
      console.log(res);
      if (res?.success) {
        onCallback();
        message.success('提交成功！', 5);
        return;
      }
      if (res?.msg && res?.ret !== 0) {
        message.error(res?.msg);
      }
    };
    useEffect(() => {
      if (singleOrder) {
        if (
          orderData.notTimeoutCount > 0 &&
          preAuditData?.preCreditStatus !== PRE_STATUS.NO_EXECUTE
        ) {
          //未逾期的订单
          setModalTit(
            <>
              <div style={{ fontWeight: 600, fontSize: 16 }}>提交风控确认</div>
              <div style={{ fontWeight: 600, fontSize: 20 }}>是否确认订单提交至风控？</div>
            </>,
          );
        } else {
          setModalTit('提交风控确认');
        }
      } else {
        setModalTit('批量提交风控确认');
      }
    }, [singleOrder, orderData]);
    // 暴露给父组件接口
    useImperativeHandle(preCheckRef, () => ({
      checkPreBtn: (order: string) => {
        checkPreBtn(order);
      },
    }));

    return (
      <>
        <Button
          type="primary"
          loading={loading.check}
          onClick={() => {
            checkPreBtn();
          }}
        >
          批量提交风控
        </Button>
        <Modal
          title={modalTit}
          open={visibleFlag}
          width={orderData.notComplianceCount > 0 ? 600 : 450}
          okText="确认提交"
          confirmLoading={loading.submit}
          okButtonProps={{
            // 没有可以提交的订单，确认提交按钮置灰
            disabled:
              orderData?.timeoutOrders?.length === 0 && orderData?.notTimeoutOrders?.length === 0,
          }}
          onCancel={() => {
            setVisibleFlag(false);
          }}
          onOk={() => {
            submitPreBtn();
          }}
        >
          <>
            <div>
              {/* 单笔订单未超时且不是历史单 */}
              {singleOrder &&
                orderData.notTimeoutCount > 0 &&
                preAuditData?.preCreditStatus !== PRE_STATUS.NO_EXECUTE && (
                  <div>
                    <p>
                      预授信额度：
                      <span style={{ color: 'red' }}>
                        {preAuditData?.preCreditAmount || '-'}（元）
                      </span>
                    </p>
                    {/* <p>
                      你好，该客户获得<span style={{ color: 'red' }}>预授信额度</span>
                      ，办理结果及融资额度以我司最终审批为准。
                    </p> */}
                    <p style={{ display: 'flex', flexDirection: 'row' }}>
                      <CheckCircleTwoTone
                        twoToneColor="#52c41a"
                        style={{ fontSize: '32px', marginRight: 15 }}
                      />
                      {/* <ExclamationCircleTwoTone
                      twoToneColor="#52c41a"
                      style={{ fontSize: '32px', marginRight: 15 }}
                    /> */}
                      <div style={{ width: '80%' }}>
                        你好，该客户获得<span style={{ color: 'red' }}>预授信额度</span>
                        ，办理结果及融资额度以我司最终审批为准。
                      </div>
                    </p>
                    <p>
                      月供金额：
                      <span style={{ color: 'red' }}>
                        {preAuditData?.preRentPrice || '-'}（元）
                      </span>
                    </p>
                    <p>客户姓名：{preAuditData?.userName || '-'}</p>
                    <p>身份证号码：{preAuditData?.idNo || '-'}</p>
                    <p>车型名称：{preAuditData?.carModel || '-'}</p>
                  </div>
                )}
            </div>
            <div>
              {/* 批量订单未超时，超时的（单笔和批量操作）订单 ，单笔未超时历史订单*/}
              {((!singleOrder && orderData.notTimeoutCount > 0) ||
                orderData.notComplianceCount > 0 ||
                (singleOrder &&
                  orderData.notTimeoutCount > 0 &&
                  preAuditData?.preCreditStatus === PRE_STATUS.NO_EXECUTE)) && (
                <div>
                  <span>
                    仅支持【融租状态=<span style={{ fontWeight: 'bold' }}>预审通过</span>
                    ，且未超时】订单
                    {orderData.notComplianceCount > 0 && (
                      <>
                        ，已过滤掉状态不符的订单
                        <span style={{ color: 'red', fontWeight: 'bold' }}>
                          {orderData.notComplianceCount}
                        </span>
                        笔
                      </>
                    )}
                  </span>
                </div>
              )}
            </div>
            {/* 未超时的批量提交风控、 未超时历史未预审单个订单 */}
            {((!singleOrder && orderData.notTimeoutCount > 0) ||
              (singleOrder &&
                orderData.notTimeoutCount > 0 &&
                preAuditData?.preCreditStatus === PRE_STATUS.NO_EXECUTE)) && (
              <div style={{ marginTop: 6 }}>
                <Dropdown
                  overlayStyle={{ maxHeight: 300, overflowY: 'scroll', color: 'grey' }}
                  disabled={orderData?.notTimeoutOrders?.length === limitNum}
                  menu={{
                    items: orderData?.notTimeoutOrders?.map((item: any) => {
                      return {
                        label: (
                          <span style={{ color: 'gray' }}>
                            {item.userName}：{item.orderNo}
                          </span>
                        ),
                        key: item.id,
                      };
                    }),
                  }}
                >
                  <div>
                    <CheckCircleOutlined style={{ color: 'rgb(50, 203, 50)', marginRight: 5 }} />
                    <span>
                      是否提交
                      {singleOrder ? (
                        '该'
                      ) : (
                        <>
                          <span style={{ color: 'rgb(50, 203, 50)', fontWeight: 'bold' }}>
                            以下{orderData.notTimeoutCount}
                          </span>
                        </>
                      )}
                      笔订单进入风控？
                    </span>
                    {orderData?.notTimeoutOrders?.length > limitNum && (
                      <DownOutlined style={{ color: 'gray', fontWeight: 'unset' }} />
                    )}
                    {orderData?.notTimeoutOrders?.length === limitNum && (
                      <div style={{ color: 'gray', marginTop: 10 }}>
                        {orderData.notTimeoutOrders[0].userName}：
                        {orderData.notTimeoutOrders[0].orderNo}
                      </div>
                    )}
                  </div>
                </Dropdown>
              </div>
            )}

            {(orderData.notComplianceCount > 0 || orderData.notTimeoutCount > 0) &&
              orderData.timeoutCount > 0 && <Divider />}
            {orderData.timeoutCount > 0 && (
              <div style={{ marginTop: 6 }}>
                <div style={{ display: 'inline-block', verticalAlign: 'top' }}>
                  <Dropdown
                    overlayStyle={{ maxHeight: 300, overflowY: 'scroll' }}
                    disabled={orderData?.timeoutOrders?.length === limitNum}
                    menu={{
                      items: orderData?.timeoutOrders?.map((item: any) => {
                        return {
                          label: (
                            <span style={{ color: 'red' }}>
                              {item.userName}：{item.orderNo}
                            </span>
                          ),
                          key: item.id,
                        };
                      }),
                    }}
                  >
                    <div>
                      <span>
                        <InfoCircleOutlined style={{ color: 'orange', marginRight: 5 }} />
                        {singleOrder ? (
                          '该订单已超时'
                        ) : (
                          <>
                            包含超时订单
                            <span style={{ color: 'red', fontWeight: 'bold' }}>
                              {orderData.timeoutCount}
                            </span>
                            笔
                          </>
                        )}
                        ，点击确认提交进行二次预审：
                      </span>
                      {orderData?.timeoutOrders?.length > limitNum && (
                        <DownOutlined style={{ color: 'gray', fontWeight: 'unset' }} />
                      )}
                      {orderData?.timeoutOrders?.length === limitNum && (
                        <div style={{ color: 'red', marginTop: 10 }}>
                          {orderData.timeoutOrders[0].userName}：
                          {orderData.timeoutOrders[0].orderNo}
                          ，已超时 {orderData.timeoutOrders[0].timeoutDays} 天
                        </div>
                      )}
                    </div>
                  </Dropdown>
                </div>
              </div>
            )}
            {
              // 修改车型后未重选租赁方案的订单
              orderData.carUpdateOrders?.length > 0 && (
                <div style={{ marginTop: 6 }}>
                  <CloseCircleOutlined style={{ color: 'red', marginRight: 5 }} />
                  <div style={{ display: 'inline-block', verticalAlign: 'top' }}>
                    <Dropdown
                      overlayStyle={{ maxHeight: 300, overflowY: 'scroll' }}
                      menu={{
                        items: orderData?.carUpdateOrders?.map((item: any) => {
                          return {
                            label: (
                              <span style={{ color: 'red' }}>
                                {item.userName}：{item.orderNo}
                              </span>
                            ),
                            key: item.id,
                          };
                        }),
                      }}
                    >
                      <div>
                        <span>
                          {singleOrder ? (
                            '该订单修改车型后未重选租赁方案'
                          ) : (
                            <>
                              包含修改车型后未重选租赁方案
                              <span style={{ color: 'red', fontWeight: 'bold' }}>
                                {orderData.carUpdateOrders?.length}
                              </span>
                              笔订单，无法提交
                            </>
                          )}
                        </span>
                        {orderData?.carUpdateOrders?.length > limitNum && (
                          <DownOutlined style={{ color: 'gray', fontWeight: 'unset' }} />
                        )}
                      </div>
                    </Dropdown>
                  </div>
                </div>
              )
            }
            {
              // 修改产品方案后未补充进件资料
              orderData.productUpdateOrders?.length > 0 && (
                <div style={{ marginTop: 6 }}>
                  <CloseCircleOutlined style={{ color: 'red', marginRight: 5 }} />
                  <div style={{ display: 'inline-block', verticalAlign: 'top' }}>
                    <Dropdown
                      overlayStyle={{ maxHeight: 300, overflowY: 'scroll' }}
                      menu={{
                        items: orderData?.productUpdateOrders?.map((item: any) => {
                          return {
                            label: (
                              <span style={{ color: 'red' }}>
                                {item.userName}：{item.orderNo}
                              </span>
                            ),
                            key: item.id,
                          };
                        }),
                      }}
                    >
                      <div>
                        <span>
                          {singleOrder ? (
                            '该订单修改产品方案后未补充进件资料'
                          ) : (
                            <>
                              包含修改产品方案后未补充进件资料
                              <span style={{ color: 'red', fontWeight: 'bold' }}>
                                {orderData.productUpdateOrders?.length}
                              </span>
                              笔订单，无法提交
                            </>
                          )}
                        </span>
                        {orderData?.carUpdateOrders?.length > limitNum && (
                          <DownOutlined style={{ color: 'gray', fontWeight: 'unset' }} />
                        )}
                      </div>
                    </Dropdown>
                  </div>
                </div>
              )
            }
          </>
        </Modal>
      </>
    );
  },
);
