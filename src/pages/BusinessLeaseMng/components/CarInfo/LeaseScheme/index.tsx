/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-01-29 15:03:43
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2025-02-27 18:53:05
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/CarInfo/LeaseScheme/index.tsx
 * @Description: LeaseScheme
 */
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import EditScheme from './EditScheme';
import LookScheme from './LookScheme';
import type { LatchPlanProps } from './types';

type LeaseSchemeProps = {
  orderNo: string;
  userNo: string;
  readOnly: boolean;
  incomeStatus: number;
  orderStatus: number;
  latchPlanParams: LatchPlanProps;
  preCreditRefresh: boolean;
  ref: any;
};

const LeaseScheme = forwardRef((props: LeaseSchemeProps, ref) => {
  const {
    orderNo,
    userNo,
    readOnly,
    orderStatus,
    incomeStatus,
    latchPlanParams,
    preCreditRefresh,
  } = props;
  const editSchemeRef = useRef();

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    // clear: () => {
    //   editSchemeRef?.current?.clear();
    // },
    save: () => {
      return editSchemeRef?.current?.save();
    },
    submit: () => {
      if (editSchemeRef?.current) {
        return editSchemeRef?.current?.submit();
      }
      return Promise.resolve(null);
    },
  }));

  return readOnly ? (
    <LookScheme orderNo={orderNo} />
  ) : (
    <EditScheme
      ref={editSchemeRef}
      orderNo={orderNo}
      userNo={userNo}
      preCreditRefresh={preCreditRefresh}
      orderStatus={orderStatus}
      incomeStatus={incomeStatus}
      readOnly={readOnly}
      latchPlanParams={latchPlanParams}
    />
  );
});

export default LeaseScheme;
