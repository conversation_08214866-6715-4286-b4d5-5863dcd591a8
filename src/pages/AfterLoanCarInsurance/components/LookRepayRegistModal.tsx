/* eslint-disable react-hooks/exhaustive-deps */
import ImagePreview from '@/components/ImagePreview';
import { ReloadOutlined } from '@ant-design/icons';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { But<PERSON>, Modal, Spin } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import { remissionColumns, repaymentRegistColumns } from '../columns/repaymentRegist';
import { getRecord, getRemission, getRepayRegist } from '../services';

type Props = {
  lookRepayRegistModalVisible: boolean;
  setLookRepayRegistModalVisible: (val: boolean) => void;
  repayPlanNo: string;
};
const LookCarInfoModal: React.FC<Props> = (props) => {
  const { orderNo } = (history as any).location.query;
  const { lookRepayRegistModalVisible, setLookRepayRegistModalVisible, repayPlanNo } = props;
  console.log('repayPlanNo', repayPlanNo);
  const [repayVoucherList, setRepayVoucherList] = useState<{ netWorkPath: string; name: string }[]>(
    [],
  );
  const [repayVoucherLoading, setRepayVoucherLoading] = useState(false);

  function getRepayVoucher() {
    // 获取还款凭证信息
    setRepayVoucherLoading(true);
    getRecord(repayPlanNo)
      .then((res) => {
        setRepayVoucherList(
          res?.data?.map((item: Record<string, any>) => item.attach)?.flat(Infinity),
        );
      })
      .finally(() => {
        setRepayVoucherLoading(false);
      });
  }
  useEffect(() => {
    if (repayPlanNo) getRepayVoucher();
  }, [repayPlanNo]);
  return (
    <Modal
      footer={null}
      open={lookRepayRegistModalVisible}
      onCancel={() => {
        setLookRepayRegistModalVisible(false);
      }}
      destroyOnClose
      width={1000}
    >
      <div style={{ border: '1px solid #f0f5ff', marginTop: 20, marginBottom: 10 }}>
        <ProTable
          headerTitle={`还款信息`}
          search={false}
          size="small"
          columns={repaymentRegistColumns}
          pagination={false}
          request={async () => getRepayRegist(repayPlanNo)}
        />
      </div>
      <div style={{ border: '1px solid #f0f5ff' }}>
        <ProTable
          headerTitle={`减免信息`}
          search={false}
          size="small"
          columns={remissionColumns}
          pagination={false}
          request={async () => {
            const data = await getRemission(orderNo, repayPlanNo);
            const list = data.reduce((pre: any[], cur) => {
              const { licensePlateNo, vin, remissionDetailCostBOS } = cur;
              const newItem = remissionDetailCostBOS.map((item) => ({
                ...item,
                licensePlateNo,
                vin,
              }));

              return pre.concat(newItem);
            }, []);
            return {
              data: list,
              success: true,
            };
          }}
        />
      </div>
      <div style={{ border: '1px solid #f0f5ff', marginTop: 20, padding: 20 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <h3>还款凭证</h3>
          <Button
            icon={<ReloadOutlined />}
            style={{ border: 'none' }}
            onClick={() => {
              getRepayVoucher();
            }}
          />
        </div>

        <Spin spinning={repayVoucherLoading}>
          {repayVoucherList?.length
            ? repayVoucherList.reduce(
                (pre: React.ReactNode, cur: { netWorkPath: string; name: string }) => {
                  return (
                    <>
                      {pre}
                      <ImagePreview
                        url={cur?.netWorkPath}
                        fileName={cur?.name}
                        urlList={repayVoucherList.map((item) => item.netWorkPath)}
                      >
                        <a target="_blank">{cur?.name}</a>
                      </ImagePreview>
                    </>
                  );
                },
                <></>,
              )
            : '-'}
        </Spin>
      </div>
    </Modal>
  );
};

export default memo(LookCarInfoModal);
