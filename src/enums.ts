import dayjs from 'dayjs';
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-09-06 14:09:59
 * @modify date 2021-09-06 14:09:59
 * @desc global enums
 */

export const LONG_TIME = dayjs('2999/12/30');
// 产品一级分类
export enum CLASSIFICATION {
  SELLER_FACTORING = '商业保理',
  FINANCE_LEASE = '融资租赁',
  SMALL_LOAN = '小额贷款',
}
// 产品二级分类
export enum SECONDARY_CLASSIFICATION {
  BUSINESS_ACCOUNTING_PERIOD = '明保',
  FINANCE_LEASE = '小圆车融',
  // LOAN_INSTALLMENT_CREDIT = '小易速贷',
  DARK_FACTORING = '暗保',
  ACCOUNTS_RECEIVABLE = '应收账款',
  BUSINESS_ACCOUNTING_PERIOD_SHARED = '共享明保',
  DARK_FACTORING_SHARED = '共享暗保',
  ACCOUNTS_RECEIVABLE_SHARED = '共享应收账款',
  LOAN_INSTALLMENT_CREDIT = '圆易借',
  CAR_INSURANCE = '小圆车险分期',
  ENTERPRISE_LOAN = '圆商贷',
}

// 是否担保
export enum GUARANTEE_TYPES {
  NON_GUARANTEED = '非担保',
  PART_GUARANTEED = '部分担保',
  ALL_PART = '全担保',
}

// 担保类型数字枚举
export const GUARANTEE_TYPES_CODE = {
  1: '非担保',
  2: '部分担保',
  3: '全担保',
};

// 上牌类型ENUM
export enum LICENSE_TYPES {
  AFFILIATE = '1', //  挂靠
  CUSTOMER = '2', //  个户
}

// 上牌类型MAP
export const LICENSE_TYPES_MAP = {
  '1': '挂靠', //  挂靠
  '2': '个户', //  个户
};

// 上牌类型MAP-ENUM
export const LICENSE_TYPES_MAP_ENUM = {
  '1': 'AFFILIATE', //  挂靠
  '2': 'CUSTOMER', //  个户
};

// 上牌类型Key/Lable形式
export const LICENSE_TYPES_OPTIONS = [
  { value: '1', label: '挂靠' },
  { value: '2', label: '个户' },
];

// 只有企业账期的
export enum SECONDARY_CLASSIFICATION_ENTERPRISE {
  BUSINESS_ACCOUNTING_PERIOD = '明保',
  DARK_FACTORING = '暗保',
  ACCOUNTS_RECEIVABLE = '应收账款',
  BUSINESS_ACCOUNTING_PERIOD_SHARED = '共享明保',
  DARK_FACTORING_SHARED = '共享暗保',
  ACCOUNTS_RECEIVABLE_SHARED = '共享应收账款',
}

// 产品名称
// to-do: 这里应该是还款方式枚举，但是后端将产品名称枚举耦合了，所以只能如此
export enum PRODUCT_NAME {
  PRINCIPAL_AND_INTEREST = '明保-一次本息',
  PRINCIPAL_EQUALS_INTEREST = '融资租赁-等额本息',
  PRINCIPAL_EQUALS_INTEREST_LOAN = '小易速贷-等额本息',
}

// 放款主体
export enum LOAD_FROM_ENUM {
  LA_LA_TIAN_JIN_TAXI = '啦啦（天津）汽车科技有限公司',
  GUANG_ZHOU_YI_REN = '广州易人行商业保理有限公司',
  GUANG_ZHOU_YI_REN_LEASE = '广州易人行融资租赁有限公司',
  GUANG_ZHOU_YI_REN_LOAN = '广州易人行小额贷款有限公司',
  BAI_XIN_BANK = '百信银行',
  GUANG_ZHOU_HUO_MAN_MAN = '广州货满满汽车咨询有限公司',
  MA_SHANG_XIAO_FEI = '马上消费',
  XIAO_YING_KA_DAI = '小赢卡贷',
  TIAN_CHEN_JIN_RONG = '甜橙金融',
  INSURANCE_SUBJECT = '投保主体',
  HUO_SHAN_RONG = '火山融',
  JING_DONG_YUN_GONG_CHANG = '京银融',
}

// 还款期限
export enum REPAYMENT_TERM_ENUMS {
  FIXED_REPAYMENT_DATE = '固定还款日',
  FIXED_REPAYMENT_3 = '3',
  FIXED_REPAYMENT_6 = '6',
  FIXED_REPAYMENT_9 = '9',
  FIXED_REPAYMENT_12 = '12',
  FIXED_REPAYMENT_24 = '24',
  FIXED_REPAYMENT_36 = '36',
}

// 还款周期起始时间
export enum REPAYMENT_CYCLESTART_TIME_ENUM {
  AFTER_LOAN = '放款后',
  AFTER_BILLING = '出账后',
  AFTER_CONFIRMING_THE_BILL = '确认账单后',
  SIGN_DEBT_SWAP_RECEIPT = '签署债转回执',
  DEBT_SWAP_LOAN_SUCCESS = '债转放款成功',
  SWAP_LOAN_SUCCESS = '放款成功',
  SIGN_LOAN_SUCCESS = '签约成功',
}
// 还款日
export enum REPAY_DAY_ENUM {
  LOAN_LEND_DATE = '以放款日为准',
  SAME_FIRST = '以账户开立日为准',
}

// 提前结清条件
export enum EARLY_SETTLE_IF_ENUM {
  LOAN_LEND_DATE = '以放款日为条件',
  LOAN_REPAY_DATE = '以还款日为条件',
}

export enum SUBSEQUENT_ENUM {
  SAME_FIRST = '与首笔一致',
  NO_RELATED = '没有关联',
}
// 代扣时间
export enum WITH_HOLDING_TIME {
  AM0 = '00:00',
  AM1 = '01:00',
  AM2 = '02:00',
  AM3 = '03:00',
  AM4 = '04:00',
  AM5 = '05:00',
  AM6 = '06:00',
  AM7 = '07:00',
  AM8 = '08:00',
  AM9 = '09:00',
  AM10 = '10:00',
  AM11 = '11:00',
  AM12 = '12:00',
  PM0 = '00:00',
  PM13 = '13:00',
  PM14 = '14:00',
  PM15 = '15:00',
  PM16 = '16:00',
  PM17 = '17:00',
  PM18 = '18:00',
  PM23 = '23:00',
}

// 费用项
export enum EXPENSE_ITEMS {
  IN_ADVANCE_LIQUIDATED_DAMAGES = '提前结清违约金',
  OVERDUE_PENALTY_INTEREST = '逾期罚息',
  OVERDUE_FACTORING_INTEREST = '保理逾期追索利息（日利率）',
  FACTORING_SERVICE_FEE = '保理服务费（日利率）',
  DEPOSIT = '定金',
  COMMISSION = '手续费',
  BAIL = '保证金',
  LOW_DOWN_PAYMENT = '首付',
  LEASE_INTEREST = '利息',
  INTEREST = '利息',
  LEASE_IN_ADVANCE_LIQUIDATED_DAMAGES = '提前结清违约金(日利率)',
  LEASE_OVERDUE_PENALTY_INTEREST = '逾期罚息（日利率）',
  OVERDUE_DELAY_AMOUNT = '逾期滞纳金',
}

// CAR_TRANSFER_PRICE = '车辆转让价',
// REPAY_PRINCIPAL = '每期应还',
// PRINCIPAL_AND_INTEREST = '（本金+利息）',
// PRINCIPAL = '本金',
// SURPLUS_REPAY_PRINCIPAL = '剩余应还本金',
// DEBT_TO_VALUE = '应收账款',
export const EXPENSE_ITEMS_CONFIG = {
  LOW_DOWN_PAYMENT: {
    whetherByPriceTable: false,
    baseEnum: 'CAR_TRANSFER_PRICE',
    relationFlage: false,
  }, // 首付不支持分离定价，车辆转让价
  DEPOSIT: {
    whetherByPriceTable: false,
    calculationEnum: 'AMOUNT_PROPORTION',
    relationFlage: false,
  }, // 定金,不支持分离定价，计算方式按金额
  BAIL: {
    proportionNumberEnum: 'BY_FIXED_RATION',
    relationFlage: false,
    amountTypeEnum: 'FIXED_AMOUNT',
    priceTableParams: ['REPAYMENT_TERM', 'RISK_LEVEL'],
    PRINCIPAL_AND_INTEREST: 'PRINCIPAL_AND_INTEREST',
    baseEnum: 'PRINCIPAL_AND_INTEREST',
  }, // 保证金，比例类型：固定比例，金额类型：固定金额,分离定价不可编辑,选中还款期限(分期数)，风控等级
  COMMISSION: {
    whetherByPriceTable: false,
    calculationEnum: 'AMOUNT_PROPORTION',
    amountTypeEnum: 'FIXED_AMOUNT',
  }, // 手续费 不支持分离定价，按金额，固定金额
  LEASE_INTEREST: {
    calculationEnum: 'PROPORTIONALLY',
    priceTableParams: ['RISK_LEVEL', 'CAR_TYPE'],
    proportionNumberEnum: 'BY_FIXED_RATION',
    relationFlage: false,
    baseEnum: 'PRINCIPAL',
  }, // 利息 按比例，，支持分离定价，本金
  LEASE_OVERDUE_PENALTY_INTEREST: {
    calculationEnum: 'PROPORTIONALLY',
    whetherByPriceTable: false,
    proportionNumberEnum: 'BY_FIXED_RATION',
    relationFlage: false,
    baseEnum: 'PRINCIPAL_INTEREST_DELAYPAYMENT',
  }, // 罚息 按比例，分离定价否
  OVERDUE_DELAY_AMOUNT: {
    calculationEnum: 'PROPORTIONALLY',
    whetherByPriceTable: false,
    proportionNumberEnum: 'BY_FIXED_RATION',
    relationFlage: false,
    baseEnum: 'PRINCIPAL_AND_INTEREST',
  }, // 逾期滞纳金
  // 提前结清违约金
  LEASE_IN_ADVANCE_LIQUIDATED_DAMAGES: {
    relationFlage: false,
  },
  // 利息
  INTEREST: {
    whetherByPriceTable: false,
    relationFlage: false,
  },
  // 提前结清
  IN_ADVANCE_LIQUIDATED_DAMAGES: {
    whetherByPriceTable: false,
    relationFlage: false,
  },
  // '逾期罚息（日利率）'
  OVERDUE_PENALTY_INTEREST: {
    whetherByPriceTable: false,
  },
};

export const RANK = ['RANKA', 'RANKB', 'RANKC'];
// {
//   RANKA:'A',
//   RANKB:'B',
//   RANKC:'C',
// }
export const CAR_TYPE = ['NEW_CAR', 'OLD_CAR'];
// export enum CAR_TYPE  {
//   NEW_CAR='新车',
//   OLD_CAR='二手车',
// }

export enum BASE_PROPORTION {
  CAR_TRANSFER_PRICE = '车辆转让价',
  CAR_DEAL_PRICE = '车辆成交价',
  REPAY_PRINCIPAL = '每期应还',
  PRINCIPAL_AND_INTEREST = '（本金+利息）',
  PRINCIPAL = '本金',
  SURPLUS_REPAY_PRINCIPAL = '剩余应还本金',
  DEBT_TO_VALUE = '应收账款',
  PRINCIPAL_INTEREST_DELAYPAYMENT = '（本金+利息+滞纳金）',
}

export enum BASE_PROPORTION_NO_BRACKETS {
  PRINCIPAL_AND_INTEREST = '本金+利息',
  CAR_TRANSFER_PRICE = '车辆转让价',
  CAR_DEAL_PRICE = '车辆成交价',
  PRINCIPAL = '本金',
  SURPLUS_REPAY_PRINCIPAL = '剩余应还本金',
  DEBT_TO_VALUE = '应收账款',
  REPAY_PRINCIPAL = '每期应还',
  PRINCIPAL_INTEREST_DELAYPAYMENT = '本金+利息+滞纳金',
  COMMERCIAL_INSURANCE = '商业险保费',
  NO_INTEREST_PAID = '未还利息',
}

export enum USER_TYPE {
  PERSONAGE = '个人',
  COMPANY = '企业',
}
export enum MAX_OPEN_CLOSE {
  'OPEN_RANGE' = ')',
  'CLOSE_RANGE' = ']',
}

export enum MIN_OPEN_CLOSE {
  'OPEN_RANGE' = '(',
  'CLOSE_RANGE' = '[',
}
export enum PRODUCT_CLASSIFICATION_CODE {
  SELLER_FACTORING = '01',
  FINANCE_LEASE = '02',
  SMALL_LOAN = '03',
}

export enum PRODUCT_CLASSIFICATION_CODE_VALUE {
  '01' = 'SELLER_FACTORING',
  '02' = 'FINANCE_LEASE',
  '03' = 'SMALL_LOAN',
}

export enum SECONDARY_CLASSIFICATION_INCOME {
  '0102' = '暗保',
  '0103' = '应收账款',
}

export enum SECONDARY_CLASSIFICATION_CODE_LABEL {
  '0101' = '明保',
  '0201' = '小圆车融',
  // '0301' = '小易速贷',
  '0301' = '圆易借',
  '0303' = '小圆车险分期',
  '0304' = '圆商贷',
}

export enum SECONDARY_CLASSIFICATION_CODE_REMISSION {
  '0201' = '小圆车融',
  '0301' = '圆易借',
  '0303' = '小圆车险分期',
}

export enum SECONDARY_CLASSIFICATION_CODE_LABEL_ENTERPRISE {
  '0101' = '明保',
  '0102' = '暗保',
  '0103' = '应收账款',
  '0104' = '共享明保',
  '0105' = '共享暗保',
  '0106' = '共享应收账款',
  '0303' = '小圆车险分期',
}

export enum CLASSIFICATION_CODE_LABEL {
  '01' = '商业保理',
  '02' = '融资租赁',
  '03' = '小额贷款',
}

export const SECONDARY_CLASSIFICATION_MAP = {
  '0101': '明保',
  '0102': '暗保',
  '0103': '应收账款',
  '0104': '共享明保',
  '0105': '共享暗保',
  '0106': '共享应收账款',
};

// all
export enum SECONDARY_CLASSIFICATION_MAP_ALL {
  '0101' = '明保',
  '0102' = '暗保',
  '0103' = '应收账款',
  '0104' = '共享明保',
  '0105' = '共享暗保',
  '0106' = '共享应收账款',
  '0201' = '小圆车融',
  // '0301' = '小易速贷',
  '0301' = '圆易借',
  '0303' = '小圆车险分期',
  '0304' = '圆商贷',
}

//all
export enum SECONDARY_CLASSIFICATION_CODE {
  SELLER_FACTORING_SECOND = '0101',
  FINANCE_LEASE_SECOND = '0201',
  // SMALL_LOAN_SECOND = '0301',
  PRIVATE_MONEY = '0301',
  CAR_INSURANCE = '0303',
  MERCHANT = '0304',
}

export enum SECONDARY_CLASSIFICATION_CODE_VALUE_MAP_ALL {
  '0101' = 'BUSINESS_ACCOUNTING_PERIOD',
  '0102' = 'DARK_FACTORING',
  '0103' = 'ACCOUNTS_RECEIVABLE',
  '0104' = 'BUSINESS_ACCOUNTING_PERIOD_SHARED',
  '0105' = 'DARK_FACTORING_SHARED',
  '0106' = 'ACCOUNTS_RECEIVABLE_SHARED',
  '0201' = 'FINANCE_LEASE',
  '0301' = 'LOAN_INSTALLMENT_CREDIT',
  '0303' = 'CAR_INSURANCE',
  '0304' = 'ENTERPRISE_LOAN',
}
export const ENERGY_TYPE_MAP = {
  1: '燃油',
  2: '纯电动',
  3: '油电混合',
  4: '插电式混动',
  5: '柴油',
};

export enum SCENE {
  'increase' = '大额提额',
  'share_enterprise' = '主次账号',
  'whitelist' = '高额白名单',
  'prerisk_whitelist' = '应收账款风控预跑白名单',
}

export enum FINACNE_SCENE {
  'share_enterprise' = '主次账号',
  'whitelist' = '高额白名单',
}

export enum SECOND_PRODUCT_SOME {
  '0101' = '明保',
  '0102' = '暗保',
  '0103' = '应收账款',
  '0105' = '共享暗保',
  '0106' = '共享应收账款',
}

export const carSceneMap = {
  1: '点对点',
  2: '中短途运输',
  3: '公司到工厂',
  4: '工厂到仓库',
  5: '同城',
  6: '跨城',
};

export const needsTypeMap = {
  1: '线上非计划',
  2: '线下计划',
  3: '定制城配',
};

export const CHANNEL_TYPES_MAP = {
  10: '货拉拉',
  20: '啦啦拍档',
  30: '拉拉车销',
  40: '主机厂',
  50: '标服',
  0: '其他',
};

export const CHANNEL_TYPES_LABEL_MAP = Object.entries(CHANNEL_TYPES_MAP).map((item) => {
  return { value: Number(item[0]), label: item[1] };
});

// 放款管理状态CODE
export enum LOAN_MANAGE_CODE {
  FAIL_LENDERS = 0, //  放款失败
  WAIT_LENDERS = 1, //  待放款
  SUCCESS_LENDERS = 2, //  放款成功
  WAIT_CAPTURE = 3, //  待请款
  WAIT_RISK_CONTROL = 4, //  待风控
  ON_RISK_CONTROL = 5, //  风控中
  HANG_UP = 7, // 挂起
  REFUSE_LENDERS = 2, //  放款拒绝
  WAIT_FIRST_INSTANCE = 10, //  待一审
  WAIT_SECOND_INSTANCE = 11, //  待二审
  REJECTED = 12, //  驳回
  WAIT_REPLACE = 13, //  待换卡
  WAIT_AUDIT = 14, //  待审核
  INVALID = '-1',
}

// 放款状态枚举: 小圆车融, 圆易借
export const leaseStatusMap = {
  1: '待放款',
  0: '放款失败',
  2: '放款成功',
  3: '待请款',
  7: '挂起',
  10: '待一审',
  11: '待二审',
  12: '驳回',
  13: '待换卡',
  '-1': '失效',
  4: '待风控',
  5: '风控中',
  6: '放款拒绝',
};
// 放款状态枚举: 保理
export const loanStatusMap = {
  0: '放款失败',
  1: '待放款',
  2: '放款成功',
  '-1': '已取消',
};
// 放款状态枚举: 车险
export const loanCarInsuranceStatusMap = {
  0: '放款失败',
  1: '待放款',
  2: '放款成功',
  14: '待审核',
  '-1': '已取消',
};

// 融租资金渠道
export const LEASE_LOAN_CHANNEL = {
  SHANG_HAI_YING_HANG: 'SHANG_HAI_YING_HANG',
  YI_REN_XING: 'YI_REN_XING',
  BO_HAI_YIN_HANG: 'BO_HAI_YIN_HANG',
};

export const LEASE_LOAN_CHANNEL_NAME = {
  SHANG_HAI_YING_HANG: '上海银行',
  YI_REN_XING: '易人行',
  BO_HAI_YIN_HANG: '渤海银行',
};

// 融租借款合同类型
export const LEASE_LOAN_FILE_TYPE = {
  YI_REN_XING: '易人行',
  SHANG_HAI_YING_HANG: '上海银行租金贷',
  KE_SHANG_YIN_HANG: '客商银行租金贷',
  // BO_HAI_YIN_HANG: '渤海银行-保理', // 暂时不使用
};

// 易人行回购标识
export enum REPURCHASE_STATUS {
  DEFAULAT = 0,
  CHANNEL = 1,
  BUY_CHANNEL = 2,
  BUY_CHANNEL_ING = 3,
  BUY_SELF = 4,
  BUY_SELF_ING = 5,
}

// 回购枚举
export const REPURCHASE_STATUS_MAP = {
  0: '-',
  1: '未回购',
  2: '已回购（资方规则）',
  3: '回购中（资方规则）',
  4: '已回购（内部规则）',
  5: '回购中（内部规则）',
};

// 垫付状态枚举
export const ADVANCED_STATUS = new Map([
  [0, '-'],
  [1, '是'],
  [2, '垫付中'],
]);

export enum RECIEVE_BANK_CHANNEL {
  WEIXIN = '0',
  COMPANY = '1',
}
// 还款渠道卡号
export const CHANNEL_BANK_NO = {
  [RECIEVE_BANK_CHANNEL.WEIXIN]: '**********', //  微信二维码
  [RECIEVE_BANK_CHANNEL.COMPANY]: '***************', //  对公打款
};

// 小圆车融渤海银行(保理模式)收款账号
export const BOHAI_BANK_NO = '****************';

// 赋强公证状态
export enum NOTARIZATION_STATUS {
  CARD_DONE = 190, // 制证完成
}

export const NOTARIZATION_STATUS_MAP = new Map([
  [10, '待公证'],
  [30, '待签署'],
  [50, '签署中'],
  [70, '签署失败'],
  [90, '待人工视频'],
  [110, '人工视频中'],
  [130, '人工视频失败'],
  [150, '待审核'],
  [170, '初审通过'],
  [190, '制证完成'],
  [210, '拒证'],
]);

// 钱包提现状态枚举
export const FREEZON_STATUS = {
  1: '-',
  2: '正常',
  3: '逾期冻结',
  4: '人工冻结',
  5: '结清解冻',
  6: '人工解冻',
};

// 还款节点枚举
export const REPAY_NODE_STATUS = {
  0: '-',
  1: '回购前',
  2: '回购后',
};

// 放款方式
export enum LENDING_MODEL_ENUM {
  ONLINE = 1, // 线上
  OFFLINE = 2, // 线下
}

// 放款方式映射
export const LENDING_MODEL_MAP = {
  [LENDING_MODEL_ENUM.ONLINE]: '线上',
  [LENDING_MODEL_ENUM.OFFLINE]: '线下',
};
