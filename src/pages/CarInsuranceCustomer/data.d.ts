/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2025-03-11 10:42:28
 * @LastEditors: alan771.tu
 * @LastEditTime: 2025-03-11 10:42:28
 * @FilePath: lala-finance-biz-web/src/pages/CarInsuranceCustomer/data.d.ts
 * @Description: data.d.ts
 */

export interface CarInsuranceCustomerFields {
  userId: string; // 用户id
  name: string; // 客户名称
  idNo: string; // 身份证号
  channelName: string; // 所属渠道
  authStatus: string; // 状态
  createdAt: string; // 创建时间
  authDate: string; // 认证时间
  authRecordId: string; // 认证记录id
}

export interface CarInsuranceCustomerDetailFields {
  name: string; // 客户名称
  idNo: string; // 身份证号
  channelName: string; // 所属渠道
  authStatus: string; // 认证状态
  createdAt: string; // 创建时间
  authDate: string; // 认证时间
  bankName: string; // 外部账号银行名称
  epBankNo: string; // 外部账号银行id
  accountName: string; // 外部账号名
  externalOwnerId: string; // 外部账号id
  accountId: string; // 账号id
}

export interface IgetListParams {
  pageSize?: number;
  current?: number;
  keyword?: string;
  idNo: string;
  authStatus: string;
  startCreatedAt: string;
  endCreatedAt: string;
  verifyTime: string;
  productSecondCodeList: string[];
}

export interface TransationItemDT {
  externalOwnerId?: string; // 账户主体Id
  secondProductCode?: string; // 产品二级分类
}

export interface IclaimRechargeParams {
  externalOwnerId?: string; // 钱包id
  accountName: string; // 账户/用户名称
  remitAmount: number; // 认领金额
  remitBankAccountName: string; // 汇款账号名称
  remitBankName: string; // 汇款银行卡
  remitBankCardNo: string; // 汇款卡号
  remitTime: string; // 汇款时间
  bankFlowNo: number; // 银行流水号
  file: string;
}
