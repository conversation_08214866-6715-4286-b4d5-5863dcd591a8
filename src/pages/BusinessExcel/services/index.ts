import { request } from '@umijs/max';
import type {
  IcommitToFinance,
  IfileType,
  IqueryCommitListParams,
  IuploadExcelParams,
  IuploadExcelRes,
} from '../types';
export const headers = {
  'hll-appid': 'bme-finance-bizadmin-svc',
};

// 导出提交记录表格
export async function exportCommit(id: number) {
  return request('/bizadmin/loancash/datareport/exportCommit', {
    params: { id },
    headers,
    // responseType: 'blob',
    // getResponse: true,
  });
}

// 获取提交记录
export async function queryCommitList(params: IqueryCommitListParams) {
  return request('/bizadmin/loancash/datareport/queryCommitList', {
    method: 'POST',
    data: params,
    headers,
  });
}

// 获取统计数据
export async function queryCommitStatistics(params: {
  dataMonthList: string[];
  fileType: IfileType;
}) {
  return request('/bizadmin/loancash/datareport/queryCommitStatistics', {
    method: 'POST',
    data: params,
    headers,
  });
}

// 导出
export async function queryReportFile(params: { dataMonth: string; productSecondCode: string }) {
  const data = await request('/bizadmin/loancash/datareport/queryReportFile', {
    method: 'POST',
    data: params,
    headers,
  });
  return data?.data;
}

// 上传报表
export async function uploadExcel(params: IuploadExcelParams): Promise<IuploadExcelRes[]> {
  const data = await request('/bizadmin/loancash/datareport/uploadFile', {
    method: 'POST',
    data: params,
    headers,
  });
  return data?.data;
}

// 上传报表到金融系统
export async function commitToFinance(params: IcommitToFinance) {
  return request('/bizadmin/loancash/datareport/commitFileList', {
    method: 'POST',
    data: params,
    headers,
  });
}
