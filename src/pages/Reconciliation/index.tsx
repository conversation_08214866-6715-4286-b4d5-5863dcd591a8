/*
 * @Date: 2023-07-17 10:40:53
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-07-17 13:37:29
 * @FilePath: /lala-finance-biz-web/src/pages/Reconciliation/index.tsx
 * @Description:
 */
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import { Tabs } from 'antd';
import React from 'react';
import { KeepAlive } from 'react-activation';
import ReconciliationDetail from './components/ReconciliationDetail';
import ReconciliationRecord from './components/ReconciliationRecord';

const Reconciliation: React.FC<any> = () => {
  return (
    <>
      <PageContainer>
        <Tabs
          defaultActiveKey="1"
          style={{ background: '#fff', paddingLeft: 10 }}
          items={[
            { label: '调账明细', key: '1', children: <ReconciliationDetail /> },
            { label: '调账记录', key: '2', children: <ReconciliationRecord /> },
          ]}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'/businessMng/postLoanMng/reconciliation'}>
      <Reconciliation />
    </KeepAlive>
  </>
);
