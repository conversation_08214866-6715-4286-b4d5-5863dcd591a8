/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2024-04-19 14:26:04
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/service.ts
 */
import { bizadminHeader } from '@/services/consts';
import { bizAdminHeader, headers } from '@/utils/constant';
import { request } from 'umi';
import type {
  BindCarParams,
  DeliveryCarParams,
  IexportContractRes,
  OffLineParams,
  OrderListParams,
  RegistList,
} from './data';
import type { NewIbillListItem, NewIbillListParams } from './types';

/**
 * 获取所有的渠道信息
 * @returns
 */
export async function billList(
  params: NewIbillListParams,
): Promise<{ total: number; data: NewIbillListItem[] }> {
  const data = await request(`/bizadmin/bill/billList`, {
    method: 'POST',
    data: params,
    headers,
  });
  return data;
}

/**
 * 车险 - 订单账期 和 车辆期账的导出 - 异步
 */

export async function billExport(params: any) {
  return request(`/bizadmin/bill/billExport`, {
    headers: bizadminHeader,
    method: 'POST',
    data: params,
  });
}

// 订单列表
export async function queryOrder(params?: OrderListParams) {
  return request('/loan/cash/order/cms/list', {
    params,
    ifTrimParams: true,
  });
}

// 订单详情
export async function queryOrderDetail(orderNo: string) {
  return request(`/loan/cash/order/cms/detail/${orderNo}`);
}

// 订单扩展
export async function queryOrderExtend(orderNo: string) {
  return request(`/bizadmin/cash/order/extend/${orderNo}`, {
    headers,
  });
}

// 订单管理导出
export async function orderExport(params: OrderListParams) {
  return request(`/loan/cash/order/cms/export`, {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

// 订单页绑车
export async function bindingCar(data: BindCarParams) {
  return request(`/bizadmin/lease/car/bindingCar`, {
    method: 'POST',
    data,
    headers,
  });
}

// 提交交车资料
export async function deliveryCarAdd(data: DeliveryCarParams) {
  return request(`/loan/user/order/deliveryCarAdd`, {
    method: 'POST',
    data,
  });
}

// 获取
export async function getOssPath(ossPath: string) {
  return request(`/repayment/getNetUrl`, {
    method: 'GET',
    params: { ossPath },
  });
}

export async function getOrderDudectDetail(orderNo: string, costType: number) {
  return request(`/loan/user/order/getOrderDudectDetail/${orderNo}/${costType}`);
}

export async function orderStatus(orderNo: string) {
  return request(`/loan/cash/order/cms/statuslogs/${orderNo}`);
}

export async function validateCarCodeEqual(
  newApplyCityId: string | number | undefined,
  newCarNo: string,
  orderNo: string,
) {
  return request(`/loan/user/order/checkCarNoBindCar`, {
    method: 'get',
    params: {
      newApplyCityId,
      newCarNo,
      orderNo,
    },
    skipErrorHandler: true,
  });
}

export async function getLicenseCityEnum() {
  return request(`/bizadmin/lease/licenseCity/getLicenseCityEnum`, {
    headers: bizAdminHeader,
  });
}

// 根据车型码获取车辆优惠信息
export async function getPromotionByCarNoAndApplyCityCode(params: {
  applyCityCode: string;
  carNo: string;
}) {
  return request(`/bizadmin/lease/car/getPromotionByCarNoAndApplyCityCode`, {
    method: 'get',
    params,
    headers: bizAdminHeader,
  });
}

export async function revokeOrder(orderNo: string) {
  return request(`/loan/user/order/revoke/${orderNo}`, {
    method: 'POST',
    skipErrorHandler: true,
  });
}

// 获取银行卡变更日志
export async function getCardRecord(orderNo: string) {
  return request(`/loan/user/order/get/card/log/${orderNo}`, {
    method: 'get',
  });
}

/**
 * 获取还款记录
 * @param {string} repayPlanNo
 */
export async function getRecord(params: any) {
  return request(`/repayment/offlineRemit/getRecord`, {
    method: 'GET',
    params,
  });
}

/**
 * @Date: 2022-02-14 16:29:13
 * @Author: elisa.zhao
 * 提交线下还款申请
 * @param {RegistList} data
 */
export async function applyRepay(data: RegistList) {
  return request(`/repayment/offlineRemit/apply`, {
    method: 'POST',
    data,
  });
}

//提交线下还款接口
export async function submitOffLine(data: OffLineParams) {
  return request(`/repayment/offlineRemit/apply/v2`, {
    method: 'POST',
    data,
  });
}

//获取还款中的金额
export async function getRepayingMon(orderNo: string) {
  return request(`/repayment/offlineRemit/getAmountInfoByOrderNo/${orderNo}`, {
    method: 'GET',
  });
}

//获取代偿结清下某订单的减免明细
export async function getDetailRemission(orderNo: string) {
  return request(`/repayment/offlineRemit/getRemissionDetail/${orderNo}`, {
    method: 'GET',
  });
}

//减免费项接口
export async function getRemissionItem(orderNo: string, repayPlanNo: string) {
  return request(`/repayment/offlineRemit/getRemission/${orderNo}/${repayPlanNo}`);
}
//二期改造，提交线下还款接口
export async function submitOfflineRepay(data: OffLineParams) {
  return request(`/bizadmin/repayment/offlineRemit/apply/v2`, {
    method: 'POST',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

/**
 * 导出合同
 */
export async function exportContract(params: OrderListParams): Promise<IexportContractRes> {
  const data = await request(`/bizadmin/cash/order/contract/export`, {
    method: 'POST',
    data: params,
    headers,
    ifTrimParams: true,
  });
  return data?.data || {};
}
