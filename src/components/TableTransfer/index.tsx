/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-12-16 17:35:41
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-21 10:02:28
 * @FilePath: /lala-finance-biz-web/src/components/TableTransfer/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Table, Transfer } from 'antd';
import { difference } from 'lodash';
import React from 'react';
import { STATUS } from './conts';
import './index.less';

export type TTransferItems = {
  direction: 'left' | 'right';
  selectedKeys: string[];
  onItemSelectAll: (key: string[], selected: boolean) => void;
  onItemSelect: (key: string, selected: boolean) => void;
  filteredItems: [];
  disabledOptions?: { key: string; val: string };
};

function TableTransfer({ disabledOptions, leftColumns, rightColumns, ...restProps }: any) {
  const getDisabledStatus = (
    disabledOptions: { key: string; val: string },
    record: any,
    direction,
  ) => {
    if (direction === 'right' || !disabledOptions?.val) {
      return false;
    }
    if (record?.[disabledOptions?.key] == disabledOptions?.val) {
      return false;
    }
    if (Array.isArray(record?.[disabledOptions?.key])) {
      if (record?.[disabledOptions?.key]?.some((item: string) => item == disabledOptions?.val)) {
        return false;
      }
      return true; //  确定了上牌类型（val有值，并且在list里都匹配不上，就禁止选）
    }
    return true;
  };

  return (
    <Transfer {...restProps} rowKey={(row) => row.businessKey}>
      {({
        direction,
        filteredItems,
        onItemSelectAll,
        onItemSelect,
        selectedKeys: listSelectedKeys,
      }: TTransferItems) => {
        const columns = direction === 'left' ? leftColumns : rightColumns;

        const rowSelection: any = {
          onSelectAll(selected: boolean, selectedRows: { businessKey: string }[]) {
            const treeSelectedKeys = selectedRows.map(
              ({ businessKey }: { businessKey: string }) => businessKey,
            );
            const diffKeys = selected
              ? difference(treeSelectedKeys, listSelectedKeys)
              : difference(listSelectedKeys, treeSelectedKeys);
            onItemSelectAll(diffKeys, selected);
          },
          onSelect({ businessKey }: { businessKey: string }, selected: boolean) {
            onItemSelect(businessKey, selected);
          },
          selectedRowKeys: listSelectedKeys,
          getCheckboxProps: disabledOptions
            ? (record: any) => {
                return {
                  disabled: getDisabledStatus(disabledOptions, record, direction),
                };
              }
            : undefined,
        };

        return (
          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={filteredItems}
            rowClassName={(record: any) => {
              //0 禁用 -1 删除
              if (record?.status === STATUS.DEL || record?.status === STATUS.DISABLE) {
                return 'disable-row';
              }
              return '';
            }}
            size="small"
            onRow={(record) => ({
              onClick: () => {
                const { businessKey } = record;
                console.log(
                  444,
                  getDisabledStatus(disabledOptions, record, direction),
                  disabledOptions,
                );
                if (getDisabledStatus(disabledOptions, record, direction)) return;
                onItemSelect(businessKey, !listSelectedKeys.includes(businessKey));
              },
            })}
          />
        );
      }}
    </Transfer>
  );
}

export default TableTransfer;
