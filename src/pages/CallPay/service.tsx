/*
 * @Author: your name
 * @Date: 2021-01-12 17:01:32
 * @LastEditTime: 2024-12-20 18:01:35
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/service.ts
 */

import { request } from '@umijs/max';

import { getAllChannelNameEnum } from '@/services/enum';
import { FUNDER_CHANNEL_MAP } from '../BusinessCashMng/const';

import { useMemo } from 'react';
import { getChannelInfo } from '../CarInsurance/services';
import type {
  CallPayParams,
  CallPayReviewParams,
  CollectReviewParams,
  OffLineCompensateAuditParams,
  OffLineRemitAuditParams,
  OfflineRemitList,
} from './data';

// 获取催收减免
export async function getCallPayList(params?: CallPayParams) {
  return request('/repayment/cms/overdue/management/payment', {
    method: 'GET',
    params: { ...params },
    ifTrimParams: true,
  });
}

// 催收减免导出
export async function callPayExport(params?: CallPayParams) {
  return request('/repayment/cms/overdue/management/payment/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

export async function callPayReview(data?: CallPayReviewParams) {
  return request('/repayment/cms/overdue/management/payment/review', {
    method: 'POST',
    data,
  });
}

export async function collectReliefReview(data?: CollectReviewParams) {
  return request('/repayment/cms/overdue/management/remission/review', {
    method: 'POST',
    data,
  });
}
/**
 * @Date: 2022-02-12 15:16:14
 * 线下还款列表
 * @param {OfflineRemitList} params
 */
export async function offlineRemitList(params?: OfflineRemitList) {
  return request('/repayment/offlineRemit/list', {
    method: 'get',
    params,
    ifTrimParams: true,
  });
}

/**
 * @Date: 2022-02-12 15:17:44
 * 线下还款审批
 * @Author: elisa.zhao
 * @param {OffLineRemitAuditParams} data
 */
export async function offlineRemit(data: OffLineRemitAuditParams) {
  return request('/repayment/offlineRemit/audit', {
    method: 'post',
    data,
  });
}

export async function offLineExport(params?: CallPayParams) {
  return request('/repayment/offlineRemit/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

export async function getCompensate(params: OffLineRemitAuditParams) {
  return request('/repayment/offlineRemit/compensate/list', {
    method: 'get',
    params,
    ifTrimParams: true,
  });
}

//代偿结清的导出
export async function getCompensateExport(params: OffLineRemitAuditParams) {
  return request('/repayment/offlineRemit/compensate/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

//代偿结清的审核
export async function compensateAudit(data: OffLineCompensateAuditParams) {
  return request('/repayment/offlineRemit/compensate/audit', {
    method: 'post',
    data,
  });
}

// 融租和车险的渠道有差异，这里统一处理
export const useChannelListSummarizeSer = ({
  secondaryClassification,
  channelCode,
  channelLevel,
}) => {
  return useMemo(async () => {
    if (secondaryClassification === 'CAR_INSURANCE') {
      return await getChannelInfo({ channelCode, channelLevel }).then((res) => {
        return res.map((item) => ({
          value: item.channelCode,
          label: item.channelName,
          title: item.channelName,
        }));
      });
    } else if (secondaryClassification === 'FINANCE_LEASE') {
      return await getAllChannelNameEnum();
    } else if (secondaryClassification === 'LOAN_INSTALLMENT_CREDIT') {
      // 小贷
      return Object.keys(FUNDER_CHANNEL_MAP).map((value) => ({
        value,
        label: FUNDER_CHANNEL_MAP[value],
        title: FUNDER_CHANNEL_MAP[value],
      }));
    } else {
      return [];
    }
  }, [secondaryClassification]);
};
