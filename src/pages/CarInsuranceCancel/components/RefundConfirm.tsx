import { useAccess } from '@umijs/max';
import { <PERSON><PERSON>, Col, Divider, Form, Input, message, Modal, Popconfirm, Row } from 'antd';
import React, { useRef, useState } from 'react';
import { DetailInfo, ListItem, RefundTypeEnum, refundTypeEnumMap } from '../const';
import '../index.less';
import { cancellationAudit } from '../service';
import BasicInfo from './BasicInfo';
import UploadPannel from './UploadPannel';

interface ModalProps {
  closeModal: (refresh?: boolean) => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  detailInfo: DetailInfo;
}

const Page: React.FC<ModalProps> = ({ visible, closeModal, currentUser, record, detailInfo }) => {
  const { orderNo, bizNo } = record;
  const access = useAccess();
  //
  const uploadRef = useRef<any>();
  const [uploadLoading, setUploadLoading] = useState(false);
  const handleUploadSubmit = async () => {
    setUploadLoading(true);
    const res: any = await uploadRef?.current?.submitUpload().catch(() => {});
    console.log(res);
    setUploadLoading(false);
  };

  const [formOfRefundConfirm] = Form.useForm();
  // 驳回原因弹窗
  const [rejectionReasonVisible, setRejectionReasonVisible] = useState(false);
  const [rejectionReasonLoading, setRejectionReasonLoading] = useState(false);
  const openRejectionReasonModal = () => setRejectionReasonVisible(true);
  const closeRejectionReasonModal = () => setRejectionReasonVisible(false);
  // 驳回
  const handleRefundConfirmReject = async () => {
    try {
      await formOfRefundConfirm.validateFields();
    } catch {
      return Promise.reject();
    }
    //
    setRejectionReasonLoading(true);
    //
    //
    const formValues = formOfRefundConfirm.getFieldsValue();
    const params: any = {
      bizNo,
      createdBy: currentUser?.accountName,
      rejectReason: formValues?.rejectReason,
      auditStatus: false,
    };
    const res = (await cancellationAudit(params).catch(() => {})) || {};
    setRejectionReasonLoading(false);
    if (res?.success) {
      message.success('操作成功');
      closeModal(true);
    }
  };
  // 通过二次确认
  const [approveConfirmVisible, setApproveConfirmVisible] = useState(false);
  const [approveConfirmLoading, setApproveConfirmLoading] = useState(false);
  const openApproveConfirm = () => setApproveConfirmVisible(true);
  const closeApproveConfirm = () => setApproveConfirmVisible(false);
  // 通过
  const handleRefundConfirmApprove = async () => {
    const params: any = {
      bizNo,
      createdBy: currentUser?.accountName,
      auditStatus: true,
    };
    setApproveConfirmLoading(true);
    const res = await cancellationAudit(params).catch(() => {});
    setApproveConfirmLoading(false);
    if (res?.success) {
      message.success('操作成功');
      closeApproveConfirm();
      closeModal(true);
    }
  };

  return (
    <>
      <Modal
        width={960}
        destroyOnClose
        title={`退保详情`}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            {access?.hasAccess('upload_file_biz_businessMng_postLoanMng_cancellation') && (
              <Button type="primary" onClick={handleUploadSubmit} loading={uploadLoading} ghost>
                保存附件
              </Button>
            )}
            {access?.hasAccess('audit_biz_businessMng_postLoanMng_cancellation') && (
              <>
                <Button type="primary" danger onClick={openRejectionReasonModal}>
                  驳回
                </Button>
                <Popconfirm
                  title="确认通过？"
                  open={approveConfirmVisible}
                  onCancel={closeApproveConfirm}
                  onConfirm={handleRefundConfirmApprove}
                  okButtonProps={{ loading: approveConfirmLoading }}
                >
                  <Button
                    type="primary"
                    loading={approveConfirmLoading}
                    onClick={openApproveConfirm}
                  >
                    通过
                  </Button>
                </Popconfirm>
              </>
            )}
          </>
        }
      >
        {/*  */}
        <BasicInfo detailInfo={detailInfo} orderStatus={detailInfo.orderStatus} />
        <UploadPannel
          ref={uploadRef}
          detailInfo={detailInfo}
          defaultFileList={detailInfo?.fileInfoAttach ? detailInfo?.fileInfoAttach : []}
        />
        {/*  */}
        <>
          <Row gutter={[16, 12]}>
            <Col span={24}>
              <span className="detail-label">退款分配：</span>
              <span>
                {refundTypeEnumMap[detailInfo?.refundAllocation as RefundTypeEnum] || '-'}
              </span>
            </Col>
          </Row>
          {/* 表头 */}
          <Row gutter={[0, 0]} className="mt-14">
            <Col span={2} className="gray-text">
              收款方
            </Col>
            <Col span={3} className="gray-text">
              回款金额
            </Col>
            <Col span={6} className="gray-text">
              收款账户名
            </Col>
            <Col span={6} className="gray-text">
              收款账号
            </Col>
            <Col span={7} className="gray-text">
              收款银行
            </Col>
          </Row>
          {[
            RefundTypeEnum.ALLOCATION_1,
            RefundTypeEnum.ALLOCATION_2,
            RefundTypeEnum.ALLOCATION_3,
            RefundTypeEnum.ALLOCATION_5,
          ].includes(detailInfo?.refundAllocation) && (
            <>
              {/* 分割线 */}
              <div className="row-divider-wrap">
                <Divider className="row-divider" />
              </div>
              {/* 易人行 */}
              <Row gutter={[0, 0]} className="table-row-wrap">
                <Col span={2}>{detailInfo?.yiRenXingBankInfo?.userName || '-'}</Col>
                <Col span={3}>
                  {detailInfo?.yirenxingRefundAmount
                    ? `￥${detailInfo?.yirenxingRefundAmount}`
                    : '-'}
                </Col>
                <Col span={6}>{detailInfo?.yiRenXingBankInfo?.accountName || '-'}</Col>
                <Col span={6}>{detailInfo?.yiRenXingBankInfo?.bankCardNo || '-'}</Col>
                <Col span={7}>{detailInfo?.yiRenXingBankInfo?.subBranchName || '-'}</Col>
              </Row>
            </>
          )}
          {[
            RefundTypeEnum.ALLOCATION_1,
            RefundTypeEnum.ALLOCATION_2,
            RefundTypeEnum.ALLOCATION_4,
            RefundTypeEnum.ALLOCATION_5,
          ].includes(detailInfo?.refundAllocation) && (
            <>
              {/* 分割线 */}
              <div className="row-divider-wrap">
                <Divider className="row-divider" />
              </div>
              {/* 客户 */}
              <Row gutter={[0, 0]} className="table-row-wrap">
                <Col span={2}>客户</Col>
                <Col span={3}>
                  {detailInfo?.userRefundAmount ? `￥${detailInfo?.userRefundAmount}` : '-'}
                </Col>
                <Col span={6}>{detailInfo?.userBankInfo?.accountName || '-'}</Col>
                <Col span={6}>{detailInfo?.userBankInfo?.bankCardNo || '-'}</Col>
                <Col span={7}>{detailInfo?.userBankInfo?.subBranchName || '-'}</Col>
              </Row>
            </>
          )}
          {/* 分割线 */}
          <div className="row-divider-wrap">
            <Divider className="row-divider" />
          </div>
        </>
      </Modal>
      {/* 驳回弹窗 */}
      <Modal
        open={rejectionReasonVisible}
        title="驳回"
        destroyOnClose
        onCancel={closeRejectionReasonModal}
        footer={
          <>
            <Button onClick={closeRejectionReasonModal}>取消</Button>
            <Button
              type="primary"
              loading={rejectionReasonLoading}
              onClick={handleRefundConfirmReject}
            >
              提交
            </Button>
          </>
        }
      >
        <Form
          form={formOfRefundConfirm}
          labelCol={{ span: 4 }}
          labelAlign="left"
          wrapperCol={{ span: 20 }}
        >
          <Form.Item
            label="驳回原因"
            name="rejectReason"
            rules={[
              { required: true, message: '请输入驳回原因' },
              { max: 100, message: '不能超过100字' },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
export default Page;
