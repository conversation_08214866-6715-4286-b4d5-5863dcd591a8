import { Button, DatePicker, Form, Input, message, Modal, Radio, Select, Table } from 'antd';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { carCheckErrorTypeEnumMap, CarPolicyTypeEnum, carTypeEnumOptions } from '../const';
import '../index.less';
import { createCancellation, createCancellationCheck } from '../service';

// import { ModalFormProps } from '@ant-design/pro-form';
export type ModalProps = {
  closeModal: () => void;
  currentUser: any;
  visible: boolean;
  refreshTable: () => void;
};
const repayStatusMap = {
  '1': '待还款',
  '2': '提前结清',
  '4': '逾期',
  '5': '逾期结清',
};

const ModalContent: React.FC<ModalProps> = ({ visible, closeModal, currentUser, refreshTable }) => {
  const [form] = Form.useForm();
  // const [loading, setLoading] = useState(false);
  // console.log(repayKey);
  // 错误信息
  const [checkErrorMsgList, setCheckErrorMsgList] = useState<string[]>([]);
  // 第二步，列表元数据
  const [validCarInfoList, setValidCarInfoList] = useState<any[]>([]);
  // 步骤信息
  const [step, setStep] = useState(1);
  const [checkLoading, setCheckLoading] = useState(false);
  // 下一步
  console.log('currentUser', currentUser);
  const nextStep = async () => {
    try {
      await form.validateFields();
    } catch {
      return Promise.reject();
    }
    //
    const fromValues = form.getFieldsValue();
    // 处理车牌号、车架号输入
    const carPolicyNoListStr = fromValues?.carPolicyNoList?.trim() || '';
    console.log('carPolicyNoListStr', carPolicyNoListStr);
    if (carPolicyNoListStr === '') {
      message.warning('请输入有效车牌号或车架号');
      return;
    }
    const carPolicyNoList: string[] =
      carPolicyNoListStr
        ?.split(`\n`)
        ?.map((item: string) => item?.trim())
        ?.filter((value: string) => value !== '') || [];
    console.log('carPolicyNoList', carPolicyNoList);
    if (!carPolicyNoList || carPolicyNoList?.length === 0) {
      message.warning('请输入有效车牌号或车架号');
      return;
    }

    // 检查车架号或车牌号
    setCheckLoading(true);
    setCheckErrorMsgList([]);
    const res =
      (await createCancellationCheck({
        channelCode: currentUser?.channelCode ? currentUser?.channelCode : undefined,
        carPolicyType: fromValues.carPolicyType,
        carPolicyNoList,
        createdBy: currentUser?.accountName,
      }).catch(() => {})) || {};
    setCheckLoading(false);
    if (res?.data?.checkSuccess) {
      // setValidCarInfoList(res?.data?.carPolicyCancellationInfoList || []);
      // 处理存在多个订单的情况
      const temp: any[] = res?.data?.carPolicyCancellationInfoMultipleList?.map((item: any) => {
        // 当前行不存在多个(>1)订单
        if (item?.multipleOrder === false) {
          const tempItem = item?.carPolicyCancellationInfoList?.[0] || {};
          return {
            ...tempItem,
            multipleOrder: false,
          };
        } else if (item?.multipleOrder === true) {
          // 当前行存在多个订单，将
          const tempItem = item || {};
          return {
            ...tempItem,
            currentSelectedItem: {},
          };
        }
      });
      setValidCarInfoList(temp || []);
      setStep(2);
      return;
    } else if (res?.data && res?.data?.errorMsgMap) {
      const msgValueMap = res?.data?.errorMsgMap || {};
      const errMsgList: string[] = [];
      //
      Object.keys(carCheckErrorTypeEnumMap)?.forEach((key: string) => {
        if (msgValueMap[key] && msgValueMap[key]?.length > 0) {
          const checkErrorMsg: string = `车辆: ${msgValueMap[key]?.join(', ')} 无法发起退保, ${
            (carCheckErrorTypeEnumMap as any)[key]
          }`;
          errMsgList.push(checkErrorMsg);
        }
      });
      setCheckErrorMsgList(errMsgList);
    }
  };
  const prevStep = () => {
    setValidCarInfoList([]);
    setStep(1);
  };
  // 可编辑表格列
  const changeListData = (name: string, value: any, record: any) => {
    const newList: any = [...validCarInfoList];
    try {
      if (record?.multipleOrder === true) {
        // 当前行存在多个订单的情况
        const index: number = newList.findIndex((item: any) => {
          return item?.id === record?.id;
        });
        if (index >= 0 && newList[index].currentSelectedItem) {
          newList[index].currentSelectedItem[name] = value;
          setValidCarInfoList(newList);
        }
      } else if (record?.multipleOrder === false) {
        const index: number = newList.findIndex(
          (item: any) => item?.cashOrderPolicyExtendId === record?.cashOrderPolicyExtendId,
        );
        if (index >= 0) {
          newList[index][name] = value;
          setValidCarInfoList(newList);
        }
      }
    } catch (e) {}
  };
  // 可编辑表格列,选择订单唯一标识更改当前行数据
  const changeListDataOfMultipleOrder = (value: any, record: any) => {
    const newList: any = [...validCarInfoList];
    try {
      if (record?.multipleOrder === true) {
        // 当前行存在多个订单的情况
        // 最外面一层数组，当前行序号
        const index: number = newList.findIndex((item: any) => {
          return item?.id === record?.id;
        });
        console.log('index', index);
        if (index >= 0) {
          // 当前行多条订单数据，格式为数组
          const currentRowList: any = newList[index]?.carPolicyCancellationInfoList || [];
          // 选择的订单
          const selectedListItem: any =
            currentRowList?.find((item: any) => {
              return item?.cashOrderPolicyExtendId === value;
            }) || {};
          console.log('selectedListItem', selectedListItem);
          // 改变元数据，引用
          // newList[index].currentSelectedItem = selectedListItem；
          // 不改变元数据，值
          newList[index].currentSelectedItem = { ...selectedListItem };
          setValidCarInfoList(newList);
        }
      }
    } catch (e) {
      console.log(e);
    }
  };
  // 提交表单
  const [submitLoading, setSubmitLoading] = useState(false);
  const handleSubmit = async () => {
    // 组装参数
    const cancellationVinList: any[] =
      validCarInfoList?.map((item: any) => {
        if (item?.multipleOrder === true) {
          // 存在多个订单的情况
          return {
            // vin: item?.currentSelectedItem?.vin,
            expectedCancellationTime: item?.currentSelectedItem?.expectedCancellationTime,
            cancellationReasonStatus: item?.currentSelectedItem?.cancellationReasonStatus,
            id: item?.currentSelectedItem?.cashOrderPolicyExtendId,
          };
        }
        return {
          // vin: item.vin,
          expectedCancellationTime: item.expectedCancellationTime,
          cancellationReasonStatus: item.cancellationReasonStatus,
          id: item?.cashOrderPolicyExtendId,
        };
      }) || [];
    const requiredFieldsValid =
      cancellationVinList?.every((item: any) => {
        return item.expectedCancellationTime && item.cancellationReasonStatus && item?.id;
      }) || false;
    if (!requiredFieldsValid) {
      message.error('请选择订单号、预期退保日期、退保原因');
      return;
    }
    const params: any = {
      cancellationInfoList: cancellationVinList,
      createdBy: currentUser?.accountName,
      channelCode: currentUser?.channelCode,
    };
    console.log('params', params);
    setSubmitLoading(true);
    const res = (await createCancellation(params).catch(() => {})) || {};
    setSubmitLoading(false);
    if (res?.success) {
      message.success('创建成功');
      try {
        refreshTable();
      } catch (e) {
        console.log(e);
      }
      closeModal();
    }
  };
  //
  const columns = [
    {
      title: '车辆',
      dataIndex: 'vin',
      width: 240,
      render: (_: any, record: any) => {
        // 当前行存在多个订单，且未选择订单
        // cashOrderPolicyExtendId: 订单唯一标识
        if (record?.multipleOrder === true) {
          const carPolicyType: CarPolicyTypeEnum = record?.carPolicyType;
          const firstOrder: any = record?.carPolicyCancellationInfoList?.[0] || {};
          if (
            record?.currentSelectedItem?.cashOrderPolicyExtendId !== undefined &&
            record?.currentSelectedItem?.cashOrderPolicyExtendId !== null
          ) {
            return (
              <>
                <div>{record?.currentSelectedItem?.plateNo || '-'}</div>
                <div className="gray-text">{record?.currentSelectedItem?.vin || '-'}</div>
              </>
            );
          }
          if (carPolicyType === CarPolicyTypeEnum.VIN) {
            // 识别输入为车架号，车架号对应多个订单
            return (
              <>
                <div className="red-text">匹配到多个车辆订单信息</div>
                <div className="gray-text">{firstOrder?.vin || '-'}</div>
              </>
            );
          } else if (carPolicyType === CarPolicyTypeEnum.LPN) {
            // 识别输入为车牌号，车牌号对应多个订单
            return (
              <>
                <div>{firstOrder?.plateNo || '-'}</div>
                <div className="red-text">匹配到多个车辆订单信息</div>
              </>
            );
          }
        }
        // 当前行不存在多个(>1)订单、或当前行存在多个订单，且已选择订单
        return (
          <>
            <div>{record.plateNo || '-'}</div>
            <div className="gray-text">{record.vin || '-'}</div>
          </>
        );
      },
    },
    {
      // title: '订单号',
      title: () => {
        return (
          <>
            <span className="warn-text-color">*</span>&nbsp;订单号
          </>
        );
      },
      width: 300,
      dataIndex: 'orderNo',
      editable: true,
      render: (_: any, record: any) => {
        let selectValue = record?.orderNo;
        // 当前行存在多个订单，且未选择订单
        if (record?.multipleOrder === true) {
          // 唯一标识
          selectValue = record?.currentSelectedItem?.cashOrderPolicyExtendId;
          const optionsOfOrder = record?.carPolicyCancellationInfoList?.map((item: any) => {
            return {
              value: item?.cashOrderPolicyExtendId,
              title: item?.orderNo,
              label: (
                <>
                  <div className="order-option-wrapper">
                    <div className="order">
                      <div className="order-no">订单号: {item?.orderNo || '-'}</div>
                      <div className="order-status">{repayStatusMap[item?.repayStatus] || '-'}</div>
                    </div>
                    <div className="car-info">{`${item?.plateNo || '-'}  ${item?.vin || '-'}`}</div>
                    <div className="other-info">放款时间: {item?.lendingTime || '-'}</div>
                  </div>
                </>
              ),
            };
          });
          return (
            <>
              <Select
                style={{ width: '100%' }}
                onChange={(value) => changeListDataOfMultipleOrder(value, record)}
                value={selectValue}
                options={optionsOfOrder}
                optionLabelProp="title"
                dropdownStyle={{ width: '380px' }}
              />
            </>
          );
        } else {
          return <>{record?.orderNo || '-'}</>;
        }
      },
    },
    {
      title: () => {
        return (
          <>
            <span className="warn-text-color">*</span>&nbsp;预期退保日期
          </>
        );
      },
      dataIndex: 'expectedCancellationTime',
      editable: true,
      width: 220,
      render: (_: any, record: any) => {
        let selectValue = record?.expectedCancellationTime;
        // 当前行存在多个订单，且未选择订单
        if (record?.multipleOrder === true) {
          selectValue = record?.currentSelectedItem?.expectedCancellationTime;
        }
        return (
          <>
            <DatePicker
              format={'YYYY-MM-DD'}
              onChange={(value: any, dateString: any) =>
                changeListData('expectedCancellationTime', dateString, record)
              }
              value={selectValue ? dayjs(selectValue, 'YYYY-MM-DD') : null}
              disabledDate={(current: any) =>
                current && current < dayjs().endOf('day').subtract(1, 'days')
              }
            />
          </>
        );
      },
    },
    {
      // title: '退保原因',
      title: () => {
        return (
          <>
            <span className="warn-text-color">*</span>&nbsp;退保原因
          </>
        );
      },
      width: 160,
      dataIndex: 'cancellationReasonStatus',
      editable: true,
      render: (_: any, record: any) => {
        let selectValue = record?.cancellationReasonStatus;
        // 当前行存在多个订单，且未选择订单
        if (record?.multipleOrder === true) {
          selectValue = record?.currentSelectedItem?.cancellationReasonStatus;
        }
        return (
          <>
            <Select
              style={{ width: '90%' }}
              onChange={(value) => changeListData('cancellationReasonStatus', value, record)}
              value={selectValue}
              options={[
                { label: '逾期退保', value: '1' },
                { label: '结清退保', value: '2' },
              ]}
            />
          </>
        );
      },
    },
    {
      title: '放款日期',
      width: 180,
      dataIndex: 'lendingTime',
      render: (_: any, record: any) => {
        // 当前行存在多个订单，且未选择订单
        if (record?.multipleOrder === true) {
          return (
            <>
              {record?.currentSelectedItem?.lendingTime
                ? dayjs(record?.currentSelectedItem?.lendingTime).format('YYYY-MM-DD')
                : '-'}
            </>
          );
        }
        return <>{record?.lendingTime ? dayjs(record?.lendingTime).format('YYYY-MM-DD') : '-'}</>;
      },
    },
    {
      title: '结清发起期数',
      width: 160,
      dataIndex: 'currentSettleTerm',
      render: (_: any, record: any) => {
        if (record?.multipleOrder === true) {
          return (
            <>
              <div>
                {record?.currentSelectedItem?.settle
                  ? `${record?.currentSelectedItem?.currentSettleTerm || '-'}/${
                      record?.currentSelectedItem?.loanTerm || '-'
                    }`
                  : '-'}
              </div>
            </>
          );
        }
        return (
          <>
            <div>
              {record?.settle
                ? `${record?.currentSettleTerm || '-'}/${record?.loanTerm || '-'}`
                : '-'}
            </div>
          </>
        );
      },
    },
    {
      title: '是否结清',
      width: 120,
      dataIndex: 'settle',
      render: (_: any, record: any) => {
        if (record?.multipleOrder === true) {
          if (
            record?.currentSelectedItem?.settle !== true &&
            record?.currentSelectedItem?.settle !== false
          ) {
            return <>-</>;
          }
          return <>{record?.currentSelectedItem?.settle ? '是' : '否'}</>;
        }
        return <>{record?.settle ? '是' : '否'}</>;
      },
    },
    {
      title: '预估退保金额',
      width: 220,
      dataIndex: 'expectedCancellationAmount',
      render: (_: any, record: any) => {
        if (record?.multipleOrder === true) {
          return (
            <>
              {record?.currentSelectedItem?.expectedCancellationAmount
                ? `¥${record?.currentSelectedItem?.expectedCancellationAmount}`
                : '-'}
            </>
          );
        }
        return (
          <>{record?.expectedCancellationAmount ? `¥${record?.expectedCancellationAmount}` : '-'}</>
        );
      },
    },
    {
      title: '结清金额',
      width: 220,
      dataIndex: 'actualSettleAmount',
      render: (_: any, record: any) => {
        if (record?.multipleOrder === true) {
          return (
            <>
              {record?.currentSelectedItem?.actualSettleAmount
                ? `¥${record?.currentSelectedItem?.actualSettleAmount}`
                : '-'}
            </>
          );
        }
        return <>{record?.actualSettleAmount ? `¥${record?.actualSettleAmount}` : '-'}</>;
      },
    },
  ];
  return (
    <>
      <Modal
        width={960}
        // bodyStyle={{ padding: '32px 40px 48px' }}
        destroyOnClose
        title={`新增退保`}
        open={visible}
        // okButtonProps={{ loading: loading }}
        // onOk={async () => {
        //   setLoading(true);
        // }}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            {/* step:1 */}
            {step === 1 && (
              <>
                <Button type="primary" loading={checkLoading} onClick={nextStep}>
                  下一步
                </Button>
              </>
            )}
            {/* step:2 */}
            {step === 2 && (
              <>
                <Button disabled={submitLoading} onClick={prevStep}>
                  上一步
                </Button>
                <Button type="primary" loading={submitLoading} onClick={handleSubmit}>
                  提交
                </Button>
              </>
            )}
          </>
        }
      >
        {/* 步骤: 1 */}
        {step === 1 && (
          <Form
            form={form}
            // wrapperCol={{ span: 12 }}
            labelCol={{ span: 4 }}
          >
            <div className="modal-wrapper">
              <div className="step-wrapper">
                {/* 标题 */}
                <div className="step-title">
                  <span className="warn-text-color">*</span>退保车辆
                </div>
                {/* 车架、车牌 */}
                <div className="">
                  <Form.Item name="carPolicyType" initialValue={1} noStyle>
                    <Radio.Group options={carTypeEnumOptions} />
                  </Form.Item>
                </div>
                {/* 文本框 */}
                <div>
                  <Form.Item name="carPolicyNoList" rules={[{ required: true, message: '必填项' }]}>
                    {/* 多个车，换行输入 */}
                    <Input.TextArea
                      placeholder={`每行只支持输入一个车架号或车牌号，多辆车请换行输入`}
                      autoSize={{ minRows: 3, maxRows: 9 }}
                      // allowClear
                    />
                  </Form.Item>
                </div>
                {/* 错误提示 */}
                {checkErrorMsgList && checkErrorMsgList?.length > 0 && (
                  <div className="error-msg warn-text-color">
                    {checkErrorMsgList?.map((item, index) => {
                      return <p key={item}>{item}</p>;
                    })}
                  </div>
                )}
              </div>
            </div>
          </Form>
        )}
        {/* 步骤: 2 */}
        {step === 2 && (
          <Table
            dataSource={validCarInfoList}
            // scroll={{ x: 1200 }}
            scroll={{ x: 'max-content' }}
            key={'id'}
            columns={columns}
            pagination={false}
          />
        )}
      </Modal>
    </>
  );
};

export default ModalContent;
