import React, { useState } from 'react';

import { <PERSON><PERSON>, Card, Col, Row, Select, Table } from 'antd';
// import type { DataBoardType } from '../data';
import { transformMoney } from '@/utils/utils';
import { useRequest } from '@umijs/max';
import { getDataStatistic } from '../service';
import './databoard.less';

const { Option } = Select;

enum PRODUCE_CODE {
  rent = '0201', //  小圆车融
}

const tablist = [
  {
    key: PRODUCE_CODE.rent,
    tab: '融资租赁',
  },
];

const transformMoneyDom = (val: string | number, unit: string, key?: string) => {
  return (
    <>
      <span className="databoard-number">
        {key === 'totalCreditAmount' ? transformMoney(val as number)[0] : val || 0}
      </span>
      <span className="databoard-unit">
        {key === 'totalCreditAmount' ? `${transformMoney(val as number)[1]}${unit}` : unit}
      </span>
    </>
  );
};

const dataConf = [
  {
    dataIndex: 'channelName',
    render: (val: string) => {
      return <span style={{ fontWeight: 'bold' }}>{val}</span>;
    },
  },
  {
    title: '订单创建成功',
    dataIndex: 'orderCreatedSuccessCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '预审通过',
    dataIndex: 'preAuditPassCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '审核中',
    dataIndex: 'underAuditCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '降额待确认',
    dataIndex: 'deratingConfirmedCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '审核通过',
    dataIndex: 'auditPassCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '待签约',
    dataIndex: 'staySignedCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '待放款',
    dataIndex: 'stayLendingCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '放款成功',
    dataIndex: 'lendingPassCount',
    render: (val: string) => {
      return transformMoneyDom(val, '单');
    },
  },
  {
    title: '审核通过率(%)',
    dataIndex: 'auditPassPercent',
    render: (val: string) => {
      return transformMoneyDom(val, '%');
    },
  },
  {
    title: '用信转化率(%)',
    dataIndex: 'creditConversionRate',
    render: (val: string) => {
      return transformMoneyDom(val, '%');
    },
  },
];

const TimeOptions = [
  {
    value: 'today',
    label: '今日',
  },
  {
    value: 'yesterday',
    label: '昨日',
  },
  {
    value: 'thisWeek',
    label: '本周',
  },
  {
    value: 'thisMonth',
    label: '本月',
  },
  // {
  //   value: 'lastMonth',
  //   label: '上月',
  // },
  // {
  //   value: 'lastThirdDay',
  //   label: '近3天',
  // },
  {
    value: 'lastSevenDay',
    label: '近7天',
  },
  {
    value: 'lastThirtyDay',
    label: '近30天',
  },
  {
    value: 'fiscalYear',
    label: '财年',
  },
];

const DataBoard = () => {
  // const [data] = useState<DataBoardType>({});
  const [activeTab, setActiveTab] = useState(PRODUCE_CODE.rent);
  const [time, setTime] = useState('thisMonth');

  const { data = [], loading, run: refreshData } = useRequest(
    (params = { thisMonth: true, productSecondCode: activeTab }) => {
      return getDataStatistic(params);
    },
  );

  const handleTabChange = (key: any) => {
    if (activeTab === key) return;
    setActiveTab(key);
    refreshData({ [time]: true, productSecondCode: key });
  };

  const handleSelectChange = (value: string) => {
    setTime(value);
    refreshData({ [value]: true, productSecondCode: activeTab });
  };

  return (
    <Card>
      <Row className="databoard-title">
        <Col xxl={2} lg={2} md={3} sm={6}>
          <span className="title ant-typography-ellipsis-single-line">数据看板</span>
        </Col>
        <Col xxl={16} lg={16} md={15} sm={10}>
          <Button.Group className="btn-group">
            {tablist.map((item) => {
              return (
                <Button
                  onClick={() => handleTabChange(item.key)}
                  key={item.key}
                  type={activeTab === item.key ? 'primary' : undefined}
                >
                  {item.tab}
                </Button>
              );
            })}
          </Button.Group>
        </Col>
        <Col xxl={6} lg={6} md={6} sm={8}>
          <Select defaultValue="thisMonth" style={{ width: 200 }} onChange={handleSelectChange}>
            {TimeOptions.map((item) => {
              return (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              );
            })}
          </Select>
        </Col>
      </Row>
      <Row justify="center">
        <Table
          loading={loading}
          columns={dataConf}
          dataSource={data}
          pagination={false}
          // pagination={{ pageSize: 20 }}
          // scroll={{ y: 1000 }}
          size="large"
          sticky={{ offsetHeader: 82 }}
        />
      </Row>
    </Card>
  );
};

export default DataBoard;
