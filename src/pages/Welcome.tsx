/*
 * @Author: your name
 * @Date: 2021-03-20 15:03:17
 * @LastEditTime: 2024-05-21 10:58:37
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Welcome.tsx
 */
import HeaderTab from '@/components/HeaderTab/index';
import globalStyle from '@/global.less';
import { PageContainer } from '@ant-design/pro-layout';
import { Alert, Card } from 'antd';
import React from 'react';

export default (): React.ReactNode => {
  return (
    <>
      <HeaderTab />
      <PageContainer className={globalStyle.mt16}>
        <Card>
          <Alert
            message={`欢迎使用小圆金科业务系统👏👏👏`}
            type="success"
            showIcon
            banner
            style={{
              margin: -12,
              marginBottom: 24,
            }}
          />
        </Card>
      </PageContainer>
    </>
  );
};
