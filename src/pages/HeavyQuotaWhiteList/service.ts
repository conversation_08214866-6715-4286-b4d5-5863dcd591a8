/*
 * @Date: 2023-08-29 16:45:21
 * @Author: elisa.<PERSON><PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-09-04 17:46:00
 * @FilePath: /lala-finance-biz-web/src/pages/HeavyQuotaWhiteList/service.ts
 * @Description:
 */

import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { WhiteListParams } from './data';

export async function getWhiteListList(data: WhiteListParams) {
  return request(`/bizadmin/config/white/queryList`, {
    method: 'POST',
    data,
    headers,
  });
}

export async function addWhiteList(data: Omit<WhiteListParams, 'current' | 'pageSize'>) {
  return request(`/bizadmin/config/white/add`, {
    method: 'POST',
    data,
    headers,
  });
}

//冻结
export async function freezeWhiteList(id: string) {
  return request(`/bizadmin/config/white/freeze`, {
    method: 'POST',
    data: { id },
    headers,
  });
}

//解冻
export async function unfreezeWhiteList(id: string) {
  return request(`/bizadmin/config/white/unfreeze`, {
    method: 'POST',
    data: { id },
    headers,
  });
}
