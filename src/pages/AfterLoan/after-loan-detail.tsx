/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2023-03-13 16:44:53
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/after-loan-detail.tsx
 */
import DetailCards from '@/components/DetailCards';
import HeaderTab from '@/components/HeaderTab/index';
import { PRODUCT_CLASSIFICATION_CODE } from '@/enums';
import { getRepayPlan, queryOrderDetail } from '@/services/global';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import React, { useEffect } from 'react';
import { queryOrderDetail as queryCashOrderDetail } from '../BusinessCashMng/service';

// 保理
import LoanInfo from '../BusinessMng/components/LoanInfo';
import RepayInfo from '../BusinessMng/components/RepayInfo';
// 融租
import IncomingLeaseInfo from '../BusinessLeaseMng/components/IncomingInfo';
import LoanLeaseInfo from '../BusinessLeaseMng/components/LoanLeaseInfo';
import RepayLeaseInfo from '../BusinessLeaseMng/components/RepayInfo';
// 小贷
import IncomingCashInfo from '../BusinessCashMng/components/IncomingInfo';
import LoanCashInfo from '../BusinessCashMng/components/LoanCashInfo';
import RepayCashInfo from '../BusinessCashMng/components/RepayInfo';

import { Card } from 'antd';
import { getLoanInfo } from './services';

const AfterLoanDetail: React.FC<any> = () => {
  const { orderNo, productCode, termDetail } = history.location.query as any;
  const { data } = useRequest(() => {
    return getLoanInfo(orderNo);
  });
  // const { data: incomeNew } = useRequest(() => {
  //   return queryOrderDetail(orderNo);
  // });
  const { data: repayPlanData } = useRequest(() => {
    return getRepayPlan(orderNo);
  });
  const { data: income, run } = useRequest(
    () => {
      return productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE
        ? queryOrderDetail(orderNo)
        : queryCashOrderDetail(orderNo);
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (
      productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE ||
      productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN
    ) {
      run();
    }
  }, []);

  const mapProductRepayInfo = {
    [PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING]: <RepayInfo orderNo={orderNo} />,
    [PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE]: (
      <RepayLeaseInfo
        curId={termDetail}
        orderNo={orderNo}
        productCode={productCode}
        accountNumber={income?.userNo}
        accountName={income?.userName}
      />
    ),
    [PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN]: (
      <RepayCashInfo
        curId={termDetail}
        orderNo={orderNo}
        productCode={productCode}
        accountNumber={income?.userNo}
        accountName={income?.userName}
      />
    ),
  };
  const mapLoanInfo = {
    [PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING]: <LoanInfo dataInfo={data} />,
    [PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE]: <LoanLeaseInfo loanData={data} />,
    [PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN]: <LoanCashInfo loanData={data} />,
  };
  // 进件
  const mapIncomingInfo = {
    [PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING]: <DetailCards.IncomingInfo orderNo={orderNo} />,
    [PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE]: (
      <IncomingLeaseInfo data={income} tableData={repayPlanData?.listRspList || []} />
    ),
    [PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN]: (
      <Card title="进件信息">
        <IncomingCashInfo data={income} tableData={repayPlanData?.listRspList || []} />
      </Card>
    ),
  };
  return (
    <>
      <HeaderTab />
      <PageContainer>
        {/* <IncomingInfo orderNo={orderNo} /> */}
        {/* <IncomingInfo data={income} tableData={repayPlanData?.listRspList || []} /> */}
        {mapIncomingInfo[productCode.substring(0, 2)]}
        {/* <LoanInfo dataInfo={data} /> */}
        {mapLoanInfo[productCode.substring(0, 2)]}
        {/* <ShowInfo title="放款信息" infoMap={loanInfoMap} itemMap={itemLoanMap} data={loanData} /> */}
        {mapProductRepayInfo[productCode.substring(0, 2)]}
      </PageContainer>
    </>
  );
};

export default AfterLoanDetail;
