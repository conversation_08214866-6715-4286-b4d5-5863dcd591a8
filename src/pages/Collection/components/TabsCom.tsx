/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-08-09 10:44:52
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-08-09 18:23:33
 * @FilePath: /code/lala-finance-biz-web/src/pages/Collection/components/TabsCom.tsx
 * @Description: TabsCom
 */
import React, { memo } from 'react';
import { Tabs } from 'antd';

type TabsType = {
  tabsClickCall: (arg0: string, arg1: string) => void;
  activeKey: string;
  totalNum: any;
  type: string;
  columns: any;
  keyTypes?: any;
  defaultActiveKey?: string;
};
const { TabPane } = Tabs;

const TabsCom: React.FC<TabsType> = ({
  tabsClickCall,
  activeKey,
  totalNum,
  keyTypes,
  type,
  defaultActiveKey,
  columns,
}) => {
  console.log('2220');
  return (
    <Tabs
      type="card"
      onChange={(key) => {
        tabsClickCall(key, type);
      }}
      style={{ lineHeight: '30px' }}
      activeKey={activeKey}
      defaultActiveKey={defaultActiveKey}
    >
      {columns.map((data: any) => {
        return (
          <TabPane
            tab={`${data[0]}${totalNum[data[1]] === undefined ? '' : `(${totalNum[data[1]]})`}`}
            key={keyTypes[data[2]]}
          />
        );
      })}
    </Tabs>
  );
};

export default memo(TabsCom);
