/** 上传购车发票 */
import type { FormInstance } from 'antd';
import React, { memo } from 'react';
import { STEP_FILE_TYPE } from '../../type';
import CarOcrContent from './CarOcrContent';

interface UploadPurchaseInvoiceIF {
  setLoading: (val: boolean) => void;
  onUploadStatusChange?: (hasUploading: boolean) => void;
  onErrorStatusChange?: (hasError: boolean) => void;
  form: FormInstance;
  otdName: string;
}

const UploadPurchaseInvoice: React.FC<UploadPurchaseInvoiceIF> = (props) => {
  const { setLoading, onUploadStatusChange, onErrorStatusChange, form, otdName } = props;

  return (
    <CarOcrContent
      name="purchase"
      form={form}
      otdName={otdName}
      fileType={STEP_FILE_TYPE.CAR_PURCHASE_INVOICE}
      variant="purchase"
      setLoading={setLoading}
      onUploadStatusChange={onUploadStatusChange}
      onErrorStatusChange={onErrorStatusChange}
    />
  );
};

export default memo(UploadPurchaseInvoice);
