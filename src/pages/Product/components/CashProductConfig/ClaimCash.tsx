/*
 * @Date: 2024-01-27 15:33:39
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-01-27 15:36:20
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/CashProductConfig/ClaimCash.tsx
 * @Description:
 */
import { LOAD_FROM_ENUM } from '@/enums';
import { Col, Divider, Form, Input, Radio, Row, Select } from 'antd';
import React, { useEffect } from 'react';

const loanFromOpts = Object.keys(LOAD_FROM_ENUM).map((key) => (
  <Select.Option ke={key} value={key}>
    {LOAD_FROM_ENUM[key]}
  </Select.Option>
));
type ClaimCashProps = {
  name?: string;
  claimData?: any;
};
const ClaimCash: React.FC<ClaimCashProps> = ({ name, claimData }) => {
  // const { claimData } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue(claimData);
  }, [claimData]);

  return (
    <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 10 }} name={name}>
      <Row gutter={20}>
        <Col span={10} offset={1}>
          <Form.Item name="debtSwap" label="是否债转">
            <Radio.Group value={1} disabled>
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
        {claimData?.debtSwap && (
          <>
            <Col span={10} offset={1}>
              <Form.Item name="debtInitiateEnum" label="债转发起条件">
                <Radio.Group disabled>
                  <Radio value="USER">用户</Radio>
                  <Radio value="SYSTEM">系统</Radio>
                  <Radio value="SYSTEM_INITIATE_USER_CONFIRM">系统发起需用户确认</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>

            <Col span={10} offset={1}>
              <Form.Item name="debtTransferTo" label="债转受让方">
                {/* <Input />
                 */}
                <Select disabled>{loanFromOpts}</Select>
              </Form.Item>
            </Col>
            <Col span={10} offset={1}>
              <Form.Item label="债转周期" name="debtCycle">
                <Input disabled addonBefore="T+" />
              </Form.Item>
            </Col>
            <Col span={10} offset={1}>
              <Form.Item name="debtTransferFrom" label="债转出让方">
                <Select disabled>{loanFromOpts}</Select>
              </Form.Item>
            </Col>
          </>
        )}
      </Row>
      {claimData?.debtSwap && (
        <>
          <Divider type="vertical" style={{ borderLeft: '3px solid #1677ff', height: '1.5em' }} />
          <h3 style={{ display: 'inline-block' }}>债权转让放款配置</h3>
          <Row>
            <Col span={10} offset={1}>
              <Form.Item name="loadFrom" label="放款主体">
                <Select disabled>{loanFromOpts}</Select>
              </Form.Item>
            </Col>
            <Col span={10} offset={1}>
              <Form.Item name="loadTo" label="收款主体">
                {/* <Input placeholder="" /> */}
                <Select disabled>{loanFromOpts}</Select>
              </Form.Item>
            </Col>
            <Col span={10} offset={1}>
              <Form.Item name="haveCashFlow" label="是否有资金流">
                <Radio.Group value={1} disabled>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={10} offset={1}>
              {/* <Form.Item > */}
              {/* <div style={{ display: 'inline-block', marginTop: '5px' }}</div> */}
              {/* <span></span> */}
              <Form.Item
                label="放款周期"
                name="loadCycle"
                // style={{ display: 'inline-block', width: '85%', marginLeft: 10 }}
              >
                <Input addonBefore="T+" disabled={claimData.loadModeEnum === 'OFF_LINE'} />
              </Form.Item>
              {/* </Form.Item> */}
            </Col>
            <Col span={10} offset={1}>
              <Form.Item name="loadModeEnum" label="放款方式">
                <Select disabled>
                  <Select.Option value="ON_LINE">线上</Select.Option>
                  <Select.Option value="OFF_LINE">线下</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </>
      )}
    </Form>
  );
};

export default ClaimCash;
