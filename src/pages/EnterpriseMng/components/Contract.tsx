// import { getOssPath } from '@/services/global';
import globalStyle from '@/global.less';
import { getBlob, previewAS, saveAs } from '@/utils/utils';
import { Table } from 'antd';
import React, { useMemo, useState } from 'react';
import { getContractList } from '../service';

interface ContractProps {
  userNo: string;
  productCode?: string;
}
interface ContractItems {
  contractType: string;
  contractPath: string;
}

const Contract: React.FC<ContractProps> = (props) => {
  const { userNo, productSecondTypeCode } = props;

  const [dataList, setDataList] = useState([]);

  useMemo(() => {
    if (userNo && productSecondTypeCode) {
      const params = {
        userNo,
        productSecondTypeCode,
      };
      getContractList(params).then((res) => {
        if (res && res.data) {
          setDataList(res.data);
        }
      });
    }
  }, [userNo]);

  const download = (url: string, filename: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      saveAs(blob, filename);
    });
    // });
  };
  const previewPDF = (url: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      previewAS(blob);
    });
    // });
  };

  const columns = [
    {
      title: '合同名称',
      dataIndex: 'contractName',
      key: 'contractName',
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      // width: 140,
      render: (_: React.ReactNode, record: ContractItems) => (
        <>
          <a
            onClick={() => {
              download(record.contractPath, record.contractName);
            }}
          >
            下载
          </a>
          <a
            className={globalStyle.ml10}
            onClick={() => {
              previewPDF(record.contractPath);
            }}
          >
            预览
          </a>
        </>
      ),
    },
  ];

  return (
    <Table
      rowKey={(record) => {
        return record.contractName;
      }}
      style={{ width: 400 }}
      dataSource={dataList}
      columns={columns}
      pagination={false}
    />
  );
};

export default Contract;
