import type { ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import { Modal } from 'antd';
import React, { useImperativeHandle, useState } from 'react';
import { getClaimRechargeList } from '../../services';
import './index.less';
interface ClaimRecordProps {
  claimRecordRef: React.RefObject<any>;
  externalOwnerId: string | number | undefined;
}

const ClaimRecordModal: React.FC<ClaimRecordProps> = (props) => {
  const { claimRecordRef, externalOwnerId } = props;

  const [open, setOpen] = useState(false);

  useImperativeHandle(claimRecordRef, () => ({
    show: () => {
      setOpen(true);
    },
    hide: () => {
      setOpen(false);
    },
  }));

  const columns: ProColumns<any>[] = [
    {
      title: '认领金额',
      dataIndex: 'amountFen',
      valueType: 'money',
    },
    {
      title: '银行流水号',
      dataIndex: 'bankFlowNo',
    },
    {
      title: '汇款名称',
      dataIndex: 'remitAccount',
    },
    {
      title: '汇款卡号',
      dataIndex: 'bankCardNo',
    },
    {
      title: '钱包入账时间',
      dataIndex: 'billTime',
      valueType: 'dateTime',
    },
    {
      title: '认领发起时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    {
      title: '汇款凭证',
      dataIndex: 'voucherUrl',
      render: (_, record) => {
        return (
          <div key={record.voucherUrl}>
            {record?.voucherUrl ? (
              // <a onClick={() => downLoad(record.voucherUrl, '凭证')}>点击下载</a> // 等凭证跨域优化，目前rpay没做跨域处理，只能通过浏览器窗口查看资源
              <a href={record.voucherUrl} target="_blank" rel="noreferrer">
                点击查看
              </a>
            ) : (
              '-'
            )}
          </div>
        );
      },
    },
    {
      title: '认领状态',
      dataIndex: 'status',
      valueEnum: {
        0: '入账中',
        1: '待确认',
        2: '已入账',
        3: '驳回',
      },
    },
  ];

  const request = async (params) => {
    if (!externalOwnerId) {
      return {
        data: [],
        total: 0,
        success: true,
      };
    }
    const res = await getClaimRechargeList({ externalOwnerId: externalOwnerId!, ...params });
    return {
      data: res?.data || [],
      total: res?.total || 0,
      success: true,
    };
  };

  return (
    <Modal
      key="claimRecordModal"
      title="认领记录"
      width={1000}
      open={open}
      cancelText="关闭"
      okButtonProps={{
        style: {
          display: 'none',
        },
      }}
      onCancel={() => setOpen(false)}
      closable={false}
      destroyOnClose
    >
      <ProTable
        className="claim-record-list"
        columns={columns}
        search={false}
        toolBarRender={false}
        rowKey="bankFlowNo"
        scroll={{ x: 'max-content' }}
        request={request}
        pagination={{
          defaultPageSize: 10,
          position: ['topRight'],
          className: 'claim-list-pagination',
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </Modal>
  );
};
export default React.memo(ClaimRecordModal);
