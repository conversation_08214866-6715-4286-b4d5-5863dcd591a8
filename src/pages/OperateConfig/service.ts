/*
 * @Date: 2023-08-29 16:45:21
 * @Author: elisa.<PERSON><PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-08-29 17:38:44
 * @FilePath: /lala-finance-biz-web/src/pages/OperateConfig/service.ts
 * @Description:
 */

import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import type { OperateConfigParams } from './data';

export async function getOperateList(data: OperateConfigParams) {
  return request(`/bizadmin/config/scene/queryList`, {
    method: 'POST',
    data,
    headers,
    ifTrimParams: true,
  });
}

export async function addOperate(data: Omit<OperateConfigParams, 'current' | 'pageSize'>) {
  return request(`/bizadmin/config/scene/add`, {
    method: 'POST',
    data,
    headers,
  });
}
