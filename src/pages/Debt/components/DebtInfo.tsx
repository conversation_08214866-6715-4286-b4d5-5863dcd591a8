/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-04-28 15:38:08
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Debt/components/DebtInfo.tsx
 */
import globalStyle from '@/global.less';
import { Link } from '@umijs/max';
import { Card, Col, Row } from 'antd';
import React from 'react';

interface DebtInfoProps {
  // orderNo: string;
  dataInfo: {
    conversionNo: string;
    accountsReceivable: number;
    principal: number;
    interest: number;
    accountName: string;
    accountNumber: string;
    loadFromName: string;
    loadToName: string;
    status: number;
    startingTime: string;
    dealTime: string;
    billNo: string;
  };
}

const DebtInfo: React.FC<DebtInfoProps> = (props) => {
  const data = props.dataInfo;
  const debtInfo = {
    conversionNo: '债转流水号',
    accountsReceivable: '应收账款金额（元）',
    principal: '本金',
    interest: '利息',
    accountName: '用户名称',
    accountNumber: '用户ID',
    loadFromName: '出让方',
    loadToName: '受让方',
    status: '状态',
    startingTime: '发起时间',
    dealTime: '成交时间',
    billNo: '关联账单',
  };
  const itemMap = {
    status: {
      1: '已发起',
      2: '已受理',
      3: '待放款',
      4: '放款成功',
    },
  };
  return (
    <Card title="债权转让" className={globalStyle.mt30}>
      <Row>
        {Object.keys(debtInfo).map((item) => {
          let value = '';
          if (data && data[item]) {
            value = (itemMap[item] ? itemMap[item][data[item]] : data[item]) || '';
          }
          return (
            <Col span={8} key={`${item}a`}>
              <div style={{ lineHeight: '40px' }}>
                <span>{debtInfo[item]}:</span>
                {item === 'billNo' ? (
                  <Link
                    className={globalStyle.ml20}
                    to={`/businessMng/bill-detail?billNo=${value}&accountNumber=${data?.accountNumber}`}
                  >
                    {value}
                  </Link>
                ) : (
                  <span className={globalStyle.ml20}>{value}</span>
                )}
              </div>
            </Col>
          );
        })}
      </Row>
    </Card>
  );
};

export default DebtInfo;
