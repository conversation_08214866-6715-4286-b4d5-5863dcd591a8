/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-29 10:27:59
 * @LastEditors: alan771.tu <EMAIL>
 * @LastEditTime: 2024-11-13 10:40:29
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/CallPayList.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import globalStyle from '@/global.less';
import { withGeneratorTable } from '@/hoc/withGeneratorTable';
import { desensitizationPhone, isExternalNetwork } from '@/utils/utils';
import type { ActionType, ProColumns, ProTableProps } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel, useRequest } from '@umijs/max';
import { Button, Popover, Tabs } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type {
  HistorySMSListByOverdueCaseItem,
  ICallDetail,
  PhoneListByOverdueCaseWithHistoryItem,
  SMSListByOverdueCaseItem,
  SmsRecordDetail,
} from '../data';
import {
  getCallerDetail,
  getHistorySMSOverdueRecord,
  getPhoneOverdueRecordWithHistory,
  getSMSListByOverdueCaseNo,
} from '../service';
import { reportLog } from '../services';
import { AddCallPayModal, CallDetailModal } from './index';

interface ICallPayListProps {
  isSettle: number;
  userNo: string;
  callPayList: PhoneListByOverdueCaseWithHistoryItem[];
  refresh: (type?: string) => void;
  businessType: string;
  productCode: string;
  productSecondCode: string;
  overdueCaseNo: string;
  btnRef: React.RefObject<any>;
}

const CallPayList: React.FC<ICallPayListProps> = (props) => {
  const {
    businessType,
    productCode,
    productSecondCode,
    overdueCaseNo,
    btnRef,
    callPayList,
    userNo,
  } = props;
  const { TabPane } = Tabs;

  // state
  const {
    dialAction,
    dialTime,
    calledNumber,
    isDialOuter,
    callId,
    setDialOuter,
    dialOff,
  } = useModel('caller');
  const { initialState } = useModel('@@initialState');
  const [callRecordAddModalVisible, handleModalVisible] = useState<boolean>(false);
  const [callDetailModalVisible, handleCallDetailModalVisible] = useState<boolean>(false);

  const phoneRecordActionRef = useRef<ActionType>();
  const smsRecordActionRef = useRef<ActionType>();
  const historyPhoneRecordActionRef = useRef<ActionType>();
  const historySMSRecordActionRef = useRef<ActionType>();

  const callPayModalSource = {
    productCode,
    productName: businessType,
    callRecord: callId,
    isDialOuter,
    dialTime,
    dialNumber: calledNumber,
    urgePersonName: initialState?.currentUser?.accountName || '',
    urgePersonId: initialState?.currentUser?.userId || '',
  };
  const { data: callDetailModalSource, run } = useRequest(
    (callRecord) => {
      return getCallerDetail(callRecord);
    },
    {
      manual: true,
    },
  );

  // table actions
  const tableActions = {
    // 电话催收记录
    phone_record: phoneRecordActionRef,
    // 短信催收记录
    sms_record: smsRecordActionRef,
    // 历史电话催收记录
    phone_record_history: historyPhoneRecordActionRef,
    // 历史短信催收记录
    sms_record_history: historySMSRecordActionRef,
  };

  // 表格列配置拓展,同类型的表单字段共用
  const columnsMapper = useMemo(() => {
    return {
      // 电话催收记录
      phone_record: [
        {
          title: '催收员',
          dataIndex: 'urgePersonName',
        },
        {
          title: '行动标识',
          dataIndex: 'actionSignName',
        },
        {
          title: '行动结果',
          dataIndex: 'actionResultsName',
        },
        {
          title: '拨打号码',
          dataIndex: 'dialNumber',
          render(
            dom:
              | boolean
              | React.ReactChild
              | React.ReactFragment
              | React.ReactPortal
              | null
              | undefined,
            record: { dialNumber: string | undefined },
          ) {
            return isExternalNetwork() ? (
              <>
                {desensitizationPhone(record?.dialNumber)}
                <Popover content={dom} trigger="click">
                  <a
                    onClick={async () => {
                      // 发送查看日志
                      if (record?.dialNumber) {
                        await reportLog(record?.dialNumber);
                      }
                    }}
                  >
                    查看
                  </a>
                </Popover>
              </>
            ) : (
              <>{record?.dialNumber}</>
            );
          },
        },
        {
          title: '拨打时间',
          dataIndex: 'dialTime',
        },
        {
          title: '备注',
          dataIndex: 'remarks',
          ellipsis: true,
        },
        {
          title: '操作',
          key: 'option',
          valueType: 'option',
          customOrder: 7,
          render: (_: any, record: { callRecord: any }) => [
            <a
              key="detail"
              onClick={async () => {
                const { callRecord } = record;
                handleCallDetailModalVisible(true);
                await run(callRecord);
              }}
            >
              查看
            </a>,
          ],
        },
      ],
      // 短信催收记录
      sms_record: [
        {
          title: 'ID',
          dataIndex: 'urgeId',
          hideInTable: true,
        },
        {
          title: '客户姓名',
          dataIndex: 'sendUserName',
          width: 100,
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          render(
            _:
              | boolean
              | React.ReactChild
              | React.ReactFragment
              | React.ReactPortal
              | null
              | undefined,
            record: { phone: string | undefined },
          ) {
            return isExternalNetwork() ? (
              <>
                {desensitizationPhone(record?.phone)}
                <Popover content={_} trigger="click">
                  <a
                    onClick={async () => {
                      // 发送查看日志
                      if (record?.phone) {
                        await reportLog(record?.phone);
                      }
                    }}
                  >
                    查看
                  </a>
                </Popover>
              </>
            ) : (
              <>{record?.phone}</>
            );
          },
        },
        {
          title: '短信模板名称',
          dataIndex: 'templateName',
        },
        {
          title: '短信模板内容',
          dataIndex: 'templateContent',
        },
        {
          title: '下发方式',
          dataIndex: 'sendingMode',
          width: 100,
        },
        {
          title: '发送状态',
          dataIndex: 'sendStatus',
          width: 100,
          valueEnum: {
            1: '未发送',
            2: '发送中',
            3: '发送成功',
            4: '发送失败',
          },
        },
        {
          title: '发送时间',
          width: 200,
          dataIndex: 'sendTime',
        },
      ],
    };
  }, [run]);

  // 多表格request统一管理, 使用memo缓存state，这样做好处是仅在组件初始化阶段监听到deps变化时生成mapper缓存
  // 当前模块该memo下三个dep基本都是初始化阶段后为静态变量（state，所以此memo不会存在多次更新（除非上级父组件多次render触发deps
  const tableRequestMapper = useMemo(() => {
    return {
      phone_record: async (params: PhoneListByOverdueCaseWithHistoryItem) => {
        try {
          const newData = await getPhoneOverdueRecordWithHistory({
            ...params,
            overdueCaseNo,
            productSecondCode,
            userNo,
          });
          return {
            data: newData.data ?? [],
            total: newData.total ?? 0,
            success: true,
          };
        } catch (error) {
          return {
            data: callPayList ?? [],
            total: callPayList?.length ?? 0,
            success: true,
          };
        }
      },
      sms_record: async (params: SMSListByOverdueCaseItem) => {
        return getSMSListByOverdueCaseNo({ ...params, overdueCaseNo });
      },
      phone_record_history: async (params: PhoneListByOverdueCaseWithHistoryItem) => {
        return getPhoneOverdueRecordWithHistory({
          ...params,
          overdueCaseNo,
          productSecondCode,
          userNo,
          queryHistoryFlag: true,
        });
      },
      sms_record_history: async (params: HistorySMSListByOverdueCaseItem) => {
        return getHistorySMSOverdueRecord({ ...params, overdueCaseNo, productSecondCode, userNo });
      },
    };
  }, [overdueCaseNo, productSecondCode, userNo, callPayList]);

  // 基础table props, 公共参数归一化
  const baseTableProps = useMemo(() => {
    return {
      search: false as const,
      options: false as const,
      toolBarRender: false as const,
      pagination: {
        defaultPageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      },
    };
  }, []);

  // 电话催收记录表格
  const PhoneRecordTable = useMemo(() => {
    return withGeneratorTable<ICallDetail>(ProTable, baseTableProps, {
      actionRef: tableActions.phone_record,
      request: tableRequestMapper.phone_record,
      columns: columnsMapper.phone_record as ProColumns<ICallDetail>[],
      rowKey: 'callRecord',
    });
  }, [
    baseTableProps,
    columnsMapper.phone_record,
    tableRequestMapper.phone_record,
    tableActions.phone_record,
  ]);

  // 短信催收记录表格
  const SMSRecordTable = useMemo(() => {
    return withGeneratorTable<ProTableProps<SmsRecordDetail, any>>(ProTable, baseTableProps, {
      actionRef: tableActions.sms_record,
      request: tableRequestMapper.sms_record,
      columns: columnsMapper.sms_record as ProColumns<SmsRecordDetail>[],
      rowKey: 'urgeId',
    });
  }, [
    baseTableProps,
    columnsMapper.sms_record,
    tableRequestMapper.sms_record,
    tableActions.sms_record,
  ]);

  // 历史电话催收记录表格
  const HistoryPhoneRecordTable = useMemo(() => {
    return withGeneratorTable<ProTableProps<ICallDetail, any>>(ProTable, baseTableProps, {
      actionRef: tableActions.phone_record_history,
      request: tableRequestMapper.phone_record_history,
      columns: columnsMapper.phone_record as ProColumns<ICallDetail>[],
      rowKey: 'callRecord',
    });
  }, [
    baseTableProps,
    columnsMapper.phone_record,
    tableRequestMapper.phone_record_history,
    tableActions.phone_record_history,
  ]);

  // 历史短信催收记录表格
  const HistorySMSRecordTable = useMemo(() => {
    return withGeneratorTable<ProTableProps<SmsRecordDetail, any>>(ProTable, baseTableProps, {
      actionRef: tableActions.sms_record_history,
      request: tableRequestMapper.sms_record_history,
      columns: columnsMapper.sms_record as ProColumns<SmsRecordDetail>[],
      rowKey: 'urgeId',
    });
  }, [
    baseTableProps,
    tableActions.sms_record_history,
    tableRequestMapper.sms_record_history,
    columnsMapper.sms_record,
  ]);

  // watch
  useEffect(() => {
    // 40: '催收结案',最新的状态
    // 50: '催收撤销',
    // 40: '催收结清', 50: '逾期撤销' 状态下，不弹窗
    if ([40, 50].indexOf(props.isSettle) > -1) return;
    // 挂断之后，自动弹出【添加催收记录】框
    if (dialAction === 'hangupBySys' || dialAction === 'hangup') {
      handleModalVisible(true);
    }
    // eslint-disable-next-line
  }, [dialAction]);

  return (
    <>
      <div style={{ margin: 20 }}>
        <div>
          <Button
            type="primary"
            onClick={() => {
              setDialOuter(false);
              handleModalVisible(true);
            }}
            disabled={!props.isSettle || props.isSettle === 40 || props.isSettle === 50}
            className={globalStyle.mb10}
          >
            添加催收记录
          </Button>
        </div>
        <Tabs defaultActiveKey="phone_record">
          {/* 电话催收记录 */}
          <TabPane tab="电话催收记录" key="phone_record">
            <PhoneRecordTable />
            {/* <ProTable<ICallDetail>
              columns={phoneColumns as ProColumns<ICallDetail>[]}
              rowKey="callRecord"
              request={tableRequestMapper.phone_record as any}
              {...baseTableProps}
            /> */}
          </TabPane>
          {/* 短信催收记录 */}
          <TabPane tab="短信催收记录" key="sms_record">
            <SMSRecordTable />
          </TabPane>
          {/* 历史电话催收记录 */}
          <TabPane tab="历史电话催收记录" key="phone_record_history">
            <HistoryPhoneRecordTable />
          </TabPane>
          {/* 历史短信催收记录 */}
          <TabPane tab="历史短信催收记录" key="sms_record_history">
            <HistorySMSRecordTable />
          </TabPane>
        </Tabs>
      </div>
      <AddCallPayModal
        onOk={async (type?: string) => {
          dialOff();
          // 更新当前催收案件信息内容
          props.refresh(type);
          // 更新电话催收记录
          tableActions.phone_record.current?.reload();
          handleModalVisible(false);
        }}
        onVisibleChange={(value: boolean) => {
          if (!value) dialOff();
          handleModalVisible(value);
        }}
        overdueCaseNo={overdueCaseNo}
        modalVisible={callRecordAddModalVisible}
        source={callPayModalSource}
        btnRef={btnRef}
      />
      <CallDetailModal
        visible={callDetailModalVisible}
        source={callDetailModalSource}
        onCancel={() => handleCallDetailModalVisible(false)}
      />
    </>
  );
};

export default CallPayList;
