import { <PERSON>Container } from '@ant-design/pro-layout';
import React, { useState } from 'react';
// import ProCard from '@ant-design/pro-card';
import HeaderTab from '@/components/HeaderTab';
import ShowInfo from '@/components/ShowInfo';
import { PRODUCT_CLASSIFICATION_CODE } from '@/enums';
import globalStyle from '@/global.less';
import { history, useRequest } from '@umijs/max';
import { Button, Card, Table } from 'antd';
import CreateForm from './components/CreateForm';
import IncomeTable from './components/IncomeTable';
import PaybackList from './components/PaybackList';
import ProductData from './components/ProductData';
import QuotaDetail from './components/quota-detail';
import SmallLoanQuotaIncome from './components/SmallLoanQuotaIncome';
import { getBaseInfo, getRiskManagementData } from './service';

const ComDetail: React.FC<{}> = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [subAccountNo, setSubAccountNo] = useState<string>('');

  // const item = { color: '#5e6d82', fontSize: '14px', fontWeight: 500, padding: '10px 0' };

  const { userNo, productSecondCode } = history.location.query;

  // 获取企业用户详情---基础信息
  const { data: baseData, loading: baseLoading } = useRequest(() => {
    return getBaseInfo(userNo);
  });

  // 获取企业用户详情---风控数据
  const { data: riskManagementData, loading: riskManagementLoading } = useRequest(() => {
    return getRiskManagementData(userNo);
  });

  // const statusMap = {
  //   status: {
  //     '1': '激活',
  //     '2': '冻结',
  //   },
  // };
  const basicMap = {
    userNo: '账号ID',
    userName: '用户名称',
    idNo: '证件号码',
    mobile: '手机号',
    channelName: '渠道商户',
    creditEndTime: '授信时间',
    // status: '状态',
  };
  // 风控数据的列表
  const columns = [
    {
      title: '产品一级分类',
      dataIndex: 'productFirstTypeName',
      key: 'productFirstTypeName',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeName',
      key: 'productSecondTypeName',
    },
    {
      title: '评级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
    },
    {
      title: '授信额度（元）',
      dataIndex: 'creditAmount',
      key: 'creditAmount',
    },
    {
      title: '授信时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: { subAccountNo: string }) => (
        <Button
          type="link"
          onClick={() => {
            setSubAccountNo(record.subAccountNo);
            handleModalVisible(true);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];

  const noSmallLoanDom = (
    <>
      <Card title="风控数据" loading={riskManagementLoading} className={globalStyle.mt20}>
        <div>总授信额度（元）：{riskManagementData?.totalCreditAmount}</div>
        <Table
          columns={columns}
          dataSource={riskManagementData?.dataDetails}
          pagination={false}
          rowKey="subAccountNo"
        />
      </Card>
      <Card title="进件记录" bordered className={globalStyle.mt20}>
        <IncomeTable userNo={userNo} />
      </Card>
      <ProductData userNo={userNo} />
    </>
  );

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ShowInfo
          title="基础信息"
          data={baseData}
          infoMap={basicMap}
          loading={baseLoading}
          // itemMap={statusMap}
        />
        {/* 小贷单独一套UI */}
        {productSecondCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN ? (
          <SmallLoanQuotaIncome />
        ) : (
          noSmallLoanDom
        )}
        <PaybackList userNo={userNo} />
        <CreateForm
          onCancel={() => {
            handleModalVisible(false);
          }}
          modalVisible={createModalVisible}
          title="额度详情"
        >
          <QuotaDetail userNo={userNo} subAccountNo={subAccountNo} />
        </CreateForm>
      </PageContainer>
    </>
  );
};

export default ComDetail;
