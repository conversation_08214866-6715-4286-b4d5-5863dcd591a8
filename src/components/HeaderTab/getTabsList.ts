import Router from '../../../config/routes';
import type { PathItem } from './type';

export const pathList: PathItem[] = [{ path: '/dashboard', name: '首页', query: '' }];

// 找出path对应的name
export const findRouterName = (pathName: string, routerList: any[]): string => {
  const queue: any[] = [];
  queue.push(routerList);
  for (let i = 0; i < queue.length; ) {
    for (let j = 0; j < queue[i].length; j += 1) {
      if (queue[i][j].path !== pathName) {
        if (Object.prototype.hasOwnProperty.call(queue[i][j], 'routes')) {
          queue.push(queue[i][j].routes);
        }
      } else {
        return queue[i][j].name;
      }
    }
    queue.shift();
  }
  return '';
};

// 增加path
export const addPath = (location: any, tabName?: string, diffQuery?: boolean) => {
  const { pathname, search, state } = location;
  // 不存在该路由，重新添加该路由
  const findPanel = pathList.find((item) => {
    if (diffQuery) {
      return item.path === pathname && item.query === search;
    }
    return item.path === pathname;
  });
  if (!findPanel) {
    const routeName = findRouterName(pathname, Router);
    pathList.push({
      path: pathname,
      name: tabName || routeName,
      query: search,
      state,
      diffQuery,
    });
  } else {
    // 如果已经存在该路由，需要判断query是否相同，不相同要进行替换
    pathList.forEach((item) => {
      if (item.path === pathname && item.query !== search && !item.diffQuery) {
        item.query = search;
      }
    });
  }
};

// 删除path
export const deletePath = (pathName: string, query: string) => {
  pathList.forEach((item, index) => {
    if (item.diffQuery) {
      // 需要区分query的
      if (item.path === pathName && item.query === query) {
        pathList.splice(index, 1);
      }
    } else if (item.path === pathName) {
      pathList.splice(index, 1);
    }
  });
};
