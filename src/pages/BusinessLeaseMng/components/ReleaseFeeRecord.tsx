/*
 * @Author: your name
 * @Date: 2021-04-06 11:01:13
 * @LastEditTime: 2021-11-11 15:29:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessReleaseMng/components/ReleaseFeeRecord.tsx
 */
import globalStyle from '@/global.less';
import ProTable from '@ant-design/pro-table';
import { Card, Modal } from 'antd';
import React, { useState } from 'react';
import type { LeaseFeeItem } from '../data';
import { getOrderDudectDetail } from '../service';

interface DebtProps {
  orderFeeRecord: LeaseFeeItem[];
  orderNo: string;
}

const ReleaseFeeRecord: React.FC<DebtProps> = (props) => {
  const { orderFeeRecord: data, orderNo } = props;
  const [currentRow, setCurrentRow] = useState<LeaseFeeItem>();
  const [visible, handleFeeModalVisible] = useState<boolean>();
  const [dataDeduct, handleTalbleDudect] = useState<[]>();
  const getOrderDudect = (feeType: number) => {
    getOrderDudectDetail(orderNo, feeType).then((res) => {
      handleTalbleDudect(res.data);
    });
  };
  const mapFeeType = {
    1: '定金',
    2: '保证金',
    3: '手续费',
    4: '首付',
  };
  const columnsDeduct = [
    {
      title: '抵扣费用项',
      dataIndex: 'deductItem',
    },
    {
      title: '金额',
      dataIndex: 'deductAmount',
    },
    {
      title: '抵扣时间',
      dataIndex: 'deductTime',
    },
  ];
  const columns = [
    {
      title: '费用项',
      dataIndex: 'feeType',
      render: (_: any, record: LeaseFeeItem) => (
        <span>
          {mapFeeType[record.feeType]}
          {record?.isRefund ? '退款' : ''}
        </span>
      ),
    },
    {
      title: '收取节点',
      dataIndex: 'payStage',
    },
    {
      title: '收取时间',
      dataIndex: 'payDate',
      render: (_: any, record: LeaseFeeItem) => (
        <span>{record?.isRefund ? record.refundDate : record.payDate}</span>
      ),
    },
    {
      title: '出资方',
      dataIndex: 'payer',
    },
    {
      title: '收取方',
      dataIndex: 'payee',
    },
    {
      title: '收取金额',
      dataIndex: 'payAmount',
    },
    {
      title: '收取方式',
      dataIndex: 'payType',
      valueEnum: {
        1: { text: '线上' },
        2: { text: '线下' },
      },
    },
    {
      title: '收取途径',
      dataIndex: 'payChannel',
      valueEnum: {
        1: { text: '微信' },
        2: { text: '支付宝' },
        7: { text: '云闪付' },
        9: { text: '招商银行一网通' },
        10: { text: '京东支付' },
        99: { text: '其他' },
      },
    },
    {
      title: '收取流水号',
      dataIndex: 'payChannelSerialId',
    },
    {
      title: '是否返还',
      dataIndex: 'returnFlag',
      valueEnum: {
        1: { text: '否' },
        2: { text: '是' },
      },
    },
    {
      title: '是否抵扣',
      dataIndex: 'deduct',
      valueEnum: {
        false: { text: '否' },
        true: { text: '是' },
      },
    },
    {
      title: '抵扣项',
      dataIndex: 'deductDesc',
    },
    {
      title: '抵扣金额',
      dataIndex: 'deductAmount',
      render: (_: any, record: LeaseFeeItem) => (
        <>
          <a
            onClick={() => {
              handleFeeModalVisible(true);
              // todo获取详情
              setCurrentRow(record);
              getOrderDudect(record.feeType);
            }}
          >
            {record.deductAmount}
          </a>
        </>
      ),
    },
  ];

  return (
    <Card title="费用记录" className={globalStyle.mt30}>
      <ProTable<LeaseFeeItem>
        rowKey={(record) => {
          return record.payAmount + record.payDate + record.feeType;
        }}
        scroll={{ x: 'max-content' }}
        dataSource={data}
        search={false}
        toolBarRender={false}
        columns={columns}
        pagination={false}
      />
      <Modal
        destroyOnClose
        title="抵扣明细"
        open={visible}
        onCancel={() => {
          handleFeeModalVisible(false);
        }}
        width={900}
        footer={null}
      >
        {/* {props.children}
         */}
        <p>
          费用项：{' '}
          <span>
            {mapFeeType[currentRow?.feeType || 1]}
            {currentRow?.isRefund ? '退款' : ''}
          </span>
        </p>
        <p>抵扣明细：</p>
        <ProTable
          rowKey={(record: any) => {
            return record.deductAmount + record.deductTime + record.deductItem;
          }}
          toolBarRender={false}
          scroll={{ x: 'max-content' }}
          dataSource={dataDeduct}
          search={false}
          columns={columnsDeduct}
          pagination={false}
        />
      </Modal>
    </Card>
  );
};

export default ReleaseFeeRecord;
