/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-06-18 10:37:44
 * @LastEditors: alan771.tu <EMAIL>
 * @LastEditTime: 2024-12-09 10:44:25
 * @FilePath: /code/lala-finance-biz-web/src/pages/AwardManager/index.tsx
 * @Description: AwardManager
 */
import ConfirmModal from '@/components/ConfirmModal';
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { valueEnumMap } from '@/utils/tools';
import { getUuid } from '@/utils/utils';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Form, InputNumber, message, Modal } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import { ModalInfoCon } from './components/ModalInfo';
import { awardTypes, awardUnitMap, statusEnum } from './data.d';
import './index.less';
import { deleteStandardPrize, getStandardPrizePage, updateStandardPrizeInventory } from './service';

const AwardManager: React.FC<any> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const confirmModalRef = useRef<any>();
  const [editformRef] = Form.useForm();
  const [editNumRef] = Form.useForm();
  const [modalData, setModalData] = useState<any>('');
  // 删除操作
  const delConfirm = (id: string) => {
    Modal.confirm({
      maskClosable: true,
      title: '确认删除此奖品？',
      centered: true,
      okText: '确认删除',
      cancelText: '取消',
      cancelButtonProps: {
        type: 'primary',
      },
      okButtonProps: {
        danger: true,
        style: { background: '#ff4d4f' },
      },
      onOk: async () => {
        const res = await deleteStandardPrize(id);
        if (res?.success) {
          actionRef.current?.reload();
          message.success('删除成功！');
          Modal.destroyAll();
        }
      },
      content: '删除后不可恢复，但不影响已配置的流程画布策略',
    });
  };

  // 修改数量提交事件
  const handleModifySubmit = async (record: any) => {
    // 先验证表单
    editNumRef.validateFields().then(() => {
      // 设置确认弹窗数据
      confirmModalRef.current?.setConfirmData({
        当前数量: (
          <span style={{ color: 'black' }}>
            {record?.prizeOccupyNum ?? 0}/{record?.prizeTotalNum}
          </span>
        ),
        修改后数量: (
          <span style={{ color: 'black' }}>
            {record?.prizeOccupyNum ?? 0}/
            <span style={{ color: '#ff0923' }}>{editNumRef.getFieldValue('newTotalNum')}</span>
          </span>
        ),
      });
      // 打开二次确认弹窗
      confirmModalRef.current?.show();
    });
    return false;
  };

  // 修改数量
  const modifyInfo = (record: any) => {
    Modal.confirm({
      title: '修改数量',
      centered: true,
      maskClosable: true,
      closable: true,
      okText: '保存',
      width: 600,
      icon: false,
      afterClose: () => {
        editNumRef.resetFields();
      },
      okButtonProps: {
        style: { background: '#1890ff' },
        onClick: () => {
          handleModifySubmit(record);
        },
      },
      content: (
        <>
          <Form
            form={editNumRef}
            onKeyDown={(e) => {
              // 阻止回车键提交表单
              if (e.key === 'Enter' || e.keyCode === 13) {
                e.preventDefault();
                return;
              }
            }}
            onFinish={async (params) => {
              const res = await updateStandardPrizeInventory({
                id: record?.id,
                newTotalNum: params?.newTotalNum,
              });
              if (res?.success) {
                actionRef.current?.reload();
                message.success('修改数量成功！');
                Modal.destroyAll();
              }
            }}
            labelCol={{ span: 4 }}
            requiredMark={false}
            labelAlign="left"
            style={{ maxWidth: 600, marginTop: 25 }}
          >
            <Form.Item label="当前数量">
              <div>
                {record?.prizeOccupyNum ?? 0}/{record?.prizeTotalNum}
              </div>
            </Form.Item>
            <Form.Item
              label="修改后数量"
              name="newTotalNum"
              rules={[
                {
                  required: true,
                  message: '请输入1 - 1000000之间的整数，且不得小于当前已发放数量',
                  min: record?.prizeOccupyNum || 1,
                  max: 1000000,
                  type: 'number',
                },
              ]}
            >
              <InputNumber
                style={{ width: 445 }}
                precision={0}
                placeholder="在此填写修改后的奖品数量，不得小于当前已发放数量"
              />
            </Form.Item>
          </Form>
        </>
      ),
    });
  };
  console.log('ModalInfo render now!');
  const columns: ProColumns[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      search: false,
      hideInTable: true,
    },
    {
      title: '奖品ID',
      dataIndex: 'prizeId',
    },
    {
      title: '奖品名称',
      dataIndex: 'prizeName',
    },
    {
      title: '奖品类型',
      dataIndex: 'prizeType',
      valueEnum: valueEnumMap(awardTypes),
      fieldProps: {
        showSearch: true,
        showArrow: true,
      },
    },
    {
      title: '价值',
      dataIndex: 'prizePrice',
      search: false,
      render: (_, record) => {
        return `${record?.prizePrice}${awardUnitMap[record?.prizeType]?.unit}`;
      },
    },
    {
      title: '限制',
      dataIndex: 'grantLimitValue',
      search: false,
      render: (_, record) => {
        return awardUnitMap[record?.prizeType]?.limitTpl?.replace('[N]', record?.grantLimitValue);
      },
    },
    {
      title: '有效期',
      dataIndex: 'prizeValidDays',
      search: false,
      render: (_, record) => {
        return `发放后${record?.prizeValidDays}天内有效`;
      },
    },
    {
      title: '数量',
      dataIndex: 'prizeTotalNum',
      search: false,
      render: (_, record) => {
        return `${record?.prizeOccupyNum ?? 0}/${record?.prizeTotalNum}`;
      },
    },
    {
      title: '状态',
      dataIndex: 'prizeUseStatus',
      valueEnum: statusEnum,
      fieldProps: {
        showSearch: true,
        showArrow: true,
      },
    },
    {
      title: '关联策略ID',
      dataIndex: 'activityId',
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (_, record) => (
        <>
          {/* 已使用状态不能删除 */}
          {record?.prizeUseStatus !== 1 && (
            <a
              type="link"
              style={{ marginRight: 15 }}
              onClick={() => {
                delConfirm(record?.id);
              }}
            >
              删除
            </a>
          )}
          {/* 使用状态不能编辑只能修改数量 */}
          {record?.prizeUseStatus === 1 ? (
            <a
              type="link"
              onClick={() => {
                modifyInfo(record);
              }}
            >
              修改数量
            </a>
          ) : (
            <a
              type="link"
              onClick={() => {
                setModalData(record);
              }}
            >
              编辑
            </a>
          )}
        </>
      ),
    },
  ];

  const handleConfirmCommit = () => {
    editNumRef.submit();
    confirmModalRef.current?.setOpen(false);
  };

  return (
    <>
      <PageContainer className={globalStyle.mt16} header={{ title: '奖品管理' }}>
        <ProTable
          actionRef={actionRef}
          formRef={formRef}
          rowKey={getUuid()}
          scroll={{ x: 'max-content' }}
          request={(params) => {
            return getStandardPrizePage(params);
          }}
          search={{
            labelWidth: 100,
            defaultCollapsed: false,
          }}
          columns={columns}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                onClick={() => {
                  setModalData('new');
                }}
              >
                新增
              </Button>,
            ];
          }}
        />
        <ModalInfoCon
          editformRef={editformRef}
          modalData={modalData}
          setModalData={setModalData}
          onCallback={() => {
            actionRef.current?.reload();
          }}
        />
      </PageContainer>
      <ConfirmModal ref={confirmModalRef} onOk={() => handleConfirmCommit()} />
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name={'awardManager/list'}>
      <AwardManager />
    </KeepAlive>
  </>
);
