/*
 * @Author: your name
 * @Date: 2021-07-20 16:55:43
 * @LastEditTime: 2024-10-17 11:10:26
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Channel/service.ts
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type {
  AccountEnableProps,
  AccountListProps,
  AccountUpdateProps,
  AddArea,
  AddLicense,
  AddParams,
  CarAssetsDetailItem,
  CarAssetsListItem,
  ChannelListItem,
  ChannelListParams,
  GpsChannelConfigParams,
  ModifyAreaInfo,
  ModifyLicenseInfo,
} from './data';

export async function queryChannel(params?: ChannelListParams) {
  return request('/bizadmin/channel/lease/list4Page', {
    params,
    headers: bizAdminHeader,
    ifTrimParams: true,
  });
}

export async function channelDel(id?: string) {
  return request(`/bizadmin/channel/lease/delete/${id}`, {
    method: 'DELETE',
    headers: bizAdminHeader,
  });
}

export async function updateStates(id: string, status: number) {
  return request(`/bizadmin/channel/lease/enable/${id}/${status}`, {
    method: 'PUT',
    headers: bizAdminHeader,
  });
}

// 添加渠道
export async function addChannel(data: AddParams) {
  return request(`/bizadmin/channel/lease/add`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 编辑渠道
export async function modifyChannel(data: AddParams) {
  return request(`/bizadmin/channel/lease/modify`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 获取当前配置详情
export async function getCurConfigInfo(id: number) {
  return request(`/bizadmin/channel/lease/detail/${id}`, {
    method: 'GET',
    headers: bizAdminHeader,
  });
}

// 获取银行卡列表信息
export async function getBankCardList(params: { subBranchName: string }) {
  return request(`/bizadmin/premium/company/queryBank`, {
    method: 'GET',
    params,
    headers: bizAdminHeader,
  });
}

//添加上牌方信息
export async function addLicense(data: AddLicense) {
  return request(`/bizadmin/channel/lease/add/license`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

//编辑上牌方信息

//修改上牌方信息（启用或者禁用）
export async function modifyLicenseStatus(id: string, status: number) {
  return request(`/bizadmin/channel/lease/enable/license/${id}/${status}`, {
    method: 'PUT',
    headers: bizAdminHeader,
  });
}

//修改上牌方信息
export async function modifyLicenseInfo(data: ModifyLicenseInfo) {
  return request(`/bizadmin/channel/lease/modify/license`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

//添加展业区域信息
export async function addArea(data: AddArea) {
  return request(`/bizadmin/channel/lease/add/area`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

//修改展业区域信息（启用或者禁用）
export async function modifyAreaStatus(id: string, status: number) {
  return request(`/bizadmin/channel/lease/enable/area/${id}/${status}`, {
    method: 'PUT',
    headers: bizAdminHeader,
  });
}

//修改展业区域信息
export async function modifyAreaInfo(data: ModifyAreaInfo) {
  return request(`/bizadmin/channel/lease/modify/area`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 获取全部渠道
export async function getChannel(): Promise<ChannelListItem[]> {
  const data = await request(`/bizadmin/channel/lease/list`, {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data?.data;
}

// 查询融租渠道账号列表
export async function getLeaseAccountList(params: AccountListProps) {
  return request(`/bizadmin/lease/account/list`, {
    method: 'GET',
    params,
    headers: bizAdminHeader,
    ifTrimParams: true,
  });
}

//启用禁用账号
export async function leaseAccountEnable(data: AccountEnableProps) {
  return request(`/bizadmin/lease/account/enable`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

//更新账号（密码）
export async function leaseAccountUpdate(data: AccountUpdateProps) {
  return request(`/bizadmin/lease/account/update`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

export function gpsChannelConfig(data: GpsChannelConfigParams) {
  return request(`/bizadmin/channel/lease/batchModify`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 车辆资产管理列表
export function getCarAssetsList(params: CarAssetsListItem) {
  return request(`/bizadmin/lease/carAssets/list`, {
    method: 'GET',
    params,
    headers: bizAdminHeader,
    ifTrimParams: true,
  });
}

// 车辆资产管理导出
export function carAssetsExport(params: CarAssetsListItem) {
  return request(`/bizadmin/lease/carAssets/export`, {
    method: 'GET',
    params,
    headers: bizAdminHeader,
    responseType: 'blob',
    getResponse: true,
    ifTrimParams: true,
  });
}

// 车辆资产管理详情
export function getCarAssetsDetail(params: { orderNo: string }) {
  return request(`/bizadmin/lease/carAssets/detail`, {
    method: 'GET',
    params,
    headers: bizAdminHeader,
  });
}

// 车辆资产管理编辑
export function carAssetsModify(data: CarAssetsDetailItem) {
  return request(`/bizadmin/lease/carAssets/modify`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

// 渠道权限列表
export function getLeaseChannelAccessList(channelCode?: string) {
  return request(`/bizadmin/channel/lease/channelPrivilegeRelation`, {
    method: 'GET',
    params: { channelCode },
    headers: bizAdminHeader,
  });
}
// 车辆资产管理编辑
export function updateLeaseChannelAccess(data: {
  channelCode: string;
  roleCode: string;
  status: any;
}) {
  return request(`/bizadmin/channel/lease/privilegeRelation`, {
    method: 'PUT',
    data,
    headers: bizAdminHeader,
  });
}
