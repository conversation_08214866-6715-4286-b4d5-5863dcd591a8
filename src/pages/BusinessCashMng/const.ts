/*
 * @Author: your name
 * @Date: 2022-05-05 14:05:44
 * @LastEditTime: 2024-09-26 14:26:03
 * @LastEditors: oak.yang <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/const.ts
 */

export const cashOrderStatusMap = {
  0: '订单创建成功',
  5: '人脸认证成功',
  11: '失效(实名认证超限)',
  19: '审核中',
  20: '待初审',
  21: '待复审',
  22: '退回补件',
  23: '风控撤销',
  40: '审核通过',
  41: '审核拒绝',
  53: '银行卡绑定成功',
  54: '签约成功',
  '-1': '失效',
  '-2': '撤销',
  60: '待放款',
  61: '放款成功',
  80: '待还款',
  81: '逾期',
  82: '结清',
  83: '逾期结清',
  84: '提前结清',
};

export const privateOrderStatusMap = {
  0: '订单创建成功',
  5: '人脸认证成功',
  11: '失效(实名认证超限)',
  19: '审核中',
  20: '待初审',
  21: '待复审',
  22: '退回补件',
  23: '风控撤销',
  40: '审核通过',
  41: '审核拒绝',
  53: '银行卡绑定成功',
  54: '签约成功',
  '-1': '失效',
  '-2': '撤销',
  60: '待放款',
  61: '放款成功',
  80: '待还款',
  81: '逾期',
  82: '结清',
  83: '逾期结清',
  84: '提前结清',
  62: '放款拒绝',
  63: '放款失败',
};

//圆商贷
export const merchantMap = {
  '-2': '已绑卡',
  0: '订单创建成功',
  60: '待放款',
  62: '放款拒绝',
  80: '待还款',
  81: '逾期',
  82: '结清',
  83: '逾期结清',
  84: '提前结清',
  '-1': '失效',
};

export const COST_TYPE = {
  '1': '利息',
  '2': '提前结清违约金',
  '3': '逾期罚息',
  '4': '本金',
  '5': '逾期滞纳金',
};
export const COST_TYPE_OPT = [
  { value: 1, label: '利息' },
  { value: 2, label: '提前结清违约金' },
  { value: 4, label: '本金' },
  { value: 3, label: '逾期罚息' },
  { value: 5, label: '逾期滞纳金' },
];

export const mapStatusZh: Record<number, string> = {
  0: '待放款',
  1: '待还款',
  2: '提前结清',
  3: '正常还款',
  4: '逾期',
  5: '逾期结清',
  6: '坏账',
  8: '单期代偿',
  9: '代偿结清',
  10: '退保结项',
  11: '回购结清',
  12: '结清中',
  13: '还款中',
};

// 用户类型来源
export const CUSTOMER_TYPE_MAP = {
  1: '司机',
  2: '搬家小哥',
  3: '用户',
  4: '企业员工个人用户',
  5: '微信用户',
  6: '自有小程序',
  7: '用户小程序',
  9: '外部浏览器用户',
  11: 'LALA_driver',
  12: 'LALA_rider',
};

// 还款渠道卡号
export const CHANNEL_BANK_NO = {
  '0': '**********', //  微信二维码
  '1': '***************', //  对公打款
};

// 资金渠道map
export const FUNDER_CHANNEL_MAP = {
  BAI_XIN: '百信',
  YI_REN_XING: '易人行',
  LE_XIANG_JIE: '乐享借',
  WEI_XIN_JIN_KE: '维信金科',
  MA_SHANG_XIAO_FEI: '马上消费',
  XIAO_YING_KA_DAI: '小赢卡贷',
  TIAN_CHEN_JIN_RONG: '甜橙金融',
  FEI_QUAN: '飞泉',
  JING_DONG_YUN_GONG_CHANG: '京银融',
};
