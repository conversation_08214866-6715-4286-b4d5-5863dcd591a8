/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/service.ts
 */
import { headers } from '@/utils/constant';
import { request } from 'umi';
import type {
  AccoutFlowParams,
  AuthInfoLog,
  BillListParams,
  EnterpriseProductParams,
  EnterpriseUserListParams,
  ModifyData,
  ModifyStatusParams,
  OrderListParams,
  QuotaDetailParams,
  RiskLevelChangeRecordParams,
  StatusModifyLog,
  UpdateAuthor,
} from './data';

// 获取企业用户管理列表数据
export async function getEnterpriseUserList(params?: EnterpriseUserListParams) {
  return request('/quota/user/enterprise/list', {
    method: 'GET',
    params: { ...params },
    ifTrimParams: true,
  });
}

// h获取企业用户管理列表--产品维度
export async function getEnterpriseProductList(params: EnterpriseProductParams) {
  return request('/quota/user/enterprise/product/list', {
    method: 'GET',
    params: { ...params },
    ifTrimParams: true,
  });
}

// 获取企业用户详情---基础信息
export async function getBaseIofo(userNo: string) {
  return request(`/quota/user/enterprise/getBaseInfo/${userNo}`, {
    method: 'GET',
  });
}

//获取产品维度用户详情--基础信息
export async function getProductBaseInfo(subAccountNo: string) {
  return request(`/quota/user/enterprise/product/getBaseInfo/${subAccountNo}`);
}

// 获取企业用户详情---风控数据
export async function getRiskManagementData(userNo: string) {
  return request(`/quota/user/enterprise/getRiskData/${userNo}`, {
    method: 'GET',
  });
}

// 获取企业用户详情---进件记录
export async function getRiskRecordList(userNo?: string, secondClassification?: string) {
  return request(`/loan/riskOrder/userRecord/${userNo}`, {
    method: 'GET',
    params: { secondClassification },
  });
}

// 获取企业用户详情---额度详情
export async function getQuotaDetail(params?: QuotaDetailParams) {
  return request('/quota/account/getOperatorLogs', {
    method: 'GET',
    params: { ...params },
  });
}

// 企业用户导出
export async function userExport(params?: EnterpriseUserListParams) {
  return request('/quota/user/enterprise/excel', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}
// 企业产品视角导出
export async function productExport(params?: EnterpriseProductParams) {
  return request('/quota/user/enterprise/product/excel', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

// 还款计划
export async function getRepayPlan(orderNo: string) {
  return request(`/repayment/cms/bill/repay/plan/${orderNo}`);
}

// 产品数据
export async function getProductData(userNo: string) {
  return request(`/quota/user/enterprise/getProductData/${userNo}`);
}

// 冻结或者激活
export async function modifyAccountStatus(data: ModifyStatusParams) {
  return request(`/quota/user/enterprise/modifyAccountStatus`, {
    method: 'POST',
    data,
  });
}

// 催收详情
export async function getPayListByAccount(params: {
  accountNumber: string;
  pageSize: number;
  current: number;
}) {
  return request(`/repayment/cms/overdue/management/${params.accountNumber}`, {
    params: {
      pageSize: params.pageSize,
      current: params.current,
    },
  });
}

// 企业授权人信息
export async function getAuthInfo(params: { enterpriseId?: string; productSecondTypeCode?: any }) {
  return request(`/loan/enterprise/cms/getAuthInfo`, {
    params,
  });
}

// 更新企业授权人信息
export async function updateAuthInfo(data: UpdateAuthor) {
  return request(`/loan/enterprise/cms/modifyAuthInfo`, {
    data,
    method: 'POST',
  });
}

// 企业授权人日志
export async function getAuthInfoLog(params: AuthInfoLog) {
  return request(`/loan/enterprise/cms/getAuthInfoLog`, {
    method: 'get',
    params,
  });
}

// 修改还款日期
export async function modifyRepayData(data: ModifyData) {
  return request('/bizadmin/repayDate/record/modifyRepayDate', {
    method: 'post',
    data,
    headers,
  });
}

// 获取客户状态变更日志
export async function getStatusModifyLogs(params: StatusModifyLog) {
  return request(`/quota/user/enterprise/getStatusModifyLogs`, {
    method: 'get',
    params,
  });
}

//查询额度变更流水
export async function getAccountFlow(data: AccoutFlowParams) {
  return request(`/quota/user/enterprise/product/getAccountFlow`, {
    method: 'post',
    data,
  });
}

//查询账单明细
export async function getBillList(params: BillListParams) {
  return request('/repayment/cms/bill', {
    params,
    ifTrimParams: true,
  });
}

//
export async function queryOrder(params?: OrderListParams) {
  return request('/repayment/cms/order/list4Page', {
    params,
    ifTrimParams: true,
  });
}

//订单明细导出
export async function orderExport(params: { userNo: string; secondClassification: string }) {
  return request('/repayment/cms/order/exportExcel', {
    responseType: 'blob',
    params,
    getResponse: true,
    method: 'get',
  });
}

//额度流水明细导出
export async function accountFlowExport(data: { userNo: string; secondClassification: string }) {
  return request('/quota/user/enterprise/product/accountFlow/excel', {
    responseType: 'blob',
    data,
    getResponse: true,
    method: 'post',
  });
}

//账单明细导出
export async function billDetailExport(params: {
  accountNumber: string;
  secondaryClassification: string;
}) {
  return request('/repayment/cms/bill/excel', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

//溢缴款明细导出
export async function overpaymentExport() {
  return request('/quota/user/enterprise/product/userAccountRecord/excel', {
    responseType: 'blob',
    getResponse: true,
    method: 'post',
  });
}

//此账号列表导出
export async function subAccountExport() {
  return request('/quota/user/enterprise/product/slaveAccount/excel', {
    responseType: 'blob',
    getResponse: true,

    method: 'post',
  });
}

//根据企业id获取产品列表
export async function getEnterpriseProduct(enterpriseId: string) {
  return request(`/loan/enterprise/cms/getEnterpriseProduct/${enterpriseId}`);
}

//关联子企业
export async function getRelated(data: {
  subAccountNo: string;
  current?: number;
  pageSize: number;
}) {
  return request(`/quota/user/enterprise/product/getRelated`, {
    data,
    method: 'post',
  });
}

//关联子企业导出
export async function relatedSubExport(data: {
  subAccountNo: string;
  current?: number;
  pageSize?: number;
}) {
  return request('/quota/user/enterprise/product/related/excel', {
    responseType: 'blob',
    data,
    getResponse: true,
    method: 'post',
  });
}

//溢缴款列表
export async function getUserAccount(data: {
  subAccountNo: string;
  current?: number;
  pageSize?: number;
}) {
  return request('/quota/user/enterprise/product/getUserAccountRecord', {
    data,
    method: 'post',
  });
}

//溢缴款导出
export async function getUserAccountExport(data: {
  subAccountNo: string;
  current?: number;
  pageSize?: number;
}) {
  return request('/quota/user/enterprise/product/userAccountRecord/excel', {
    responseType: 'blob',
    data,
    getResponse: true,
    method: 'post',
  });
}

//次账号列表
export async function getSlaveAccount(data: {
  subAccountNo: string;
  current?: number;
  pageSize?: number;
}) {
  return request('/quota/user/enterprise/product/getSlaveAccount', {
    data,
    method: 'post',
  });
}

//次账号列表导出
export async function getSlaveAccountExport(data: {
  subAccountNo: string;
  current?: number;
  pageSize?: number;
}) {
  return request('/quota/user/enterprise/product/slaveAccount/excel', {
    responseType: 'blob',
    data,
    getResponse: true,
    method: 'post',
  });
}

//获取风控等级更变历史记录
export async function getRiskChangeRecordList(params: RiskLevelChangeRecordParams) {
  return request('/risk/changeHistory/getPageList', {
    method: 'POST',
    data: params,
  });
}

//变更还款日日志
export async function getUpdateRepayDayLog(data: { productSecondCode: string; userNo: string }) {
  return request('/bizadmin/repayDate/record/queryList', {
    method: 'POST',
    data,
    headers,
  });
}

// 获取合同
export function getContractList(params: { userNo: string; productSecondTypeCode: string }) {
  return request('/bizadmin/factoring/getContractFile', {
    method: 'GET',
    params,
    headers,
  });
}
