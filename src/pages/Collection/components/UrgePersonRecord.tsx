/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 16:42:16
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-08-11 14:08:02
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/urgePersonRecord.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import React from 'react';

import { Modal, Steps } from 'antd';

export type UrgePersonRecordProps = {
  // onCancel: () => void;
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  urgeChangeHistory: [];
};

const UrgePersonRecord: React.FC<UrgePersonRecordProps> = ({
  visible,
  close,
  urgeChangeHistory,
}) => {
  const { Step } = Steps;
  return (
    <>
      <Modal title="催收员变更记录" open={visible} centered={true} onCancel={close} footer={null}>
        <div style={{ maxHeight: 400, overflowY: 'scroll' }}>
          {urgeChangeHistory?.length ? (
            <Steps progressDot current={urgeChangeHistory?.length} direction="vertical">
              {urgeChangeHistory?.map(
                (
                  item: { urgePersonName: string; operateEvent: string; dispatchTime: string },
                  index,
                ) => {
                  return (
                    <Step
                      title={item?.urgePersonName}
                      description={
                        <>
                          <div>{item?.dispatchTime}</div>
                          <div>{item.operateEvent}</div>
                        </>
                      }
                      key={index + item.dispatchTime}
                    />
                  );
                },
              )}
            </Steps>
          ) : (
            '暂无数据'
          )}
        </div>
      </Modal>
    </>
  );
};

export default UrgePersonRecord;
