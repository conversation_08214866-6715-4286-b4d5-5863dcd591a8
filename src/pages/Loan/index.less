.showAttatment {
  padding: 2px;
}

.showAttatment:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.iconShow {
  // display: none;
  opacity: 0;
}

.showAttatment:hover .iconShow {
  opacity: 1;
}

//   }
// }

.wait-auditing-text-div {
  display: inline-block;
  width: 205px;
  overflow: hidden;
  color: gray;
  white-space: nowrap;
  text-align: center;
  text-overflow: ellipsis;
  vertical-align: bottom;
}

.upload {
  :global {
    .ant-upload.ant-upload-drag {
      display: block;
      height: 156px;
    }
  }
}

.uploadNoHeader {
  :global {
    .ant-upload.ant-upload-drag {
      display: block;
      height: 200px;
    }
  }
}

.previewInfo {
  width: 800px;
  margin-top: 12px;
  padding: 0 24px;
  color: #fff;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
}

.previewText {
  color: #fff;
}
