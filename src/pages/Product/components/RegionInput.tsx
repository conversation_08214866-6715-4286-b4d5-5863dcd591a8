/*
 * @Author: your name
 * @Date: 2021-10-20 15:19:12
 * @LastEditTime: 2023-08-18 09:49:54
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/RegionInput.tsx
 */

import { Input, InputNumber, Select } from 'antd';
import React, { useState } from 'react';

// type Currency = 'rmb' | 'dollar';

type RegionItem = 'CLOSE_RANGE' | 'OPEN_RANGE';
interface RegionValue {
  minOpenOrCloseRange?: RegionItem; // 最小值开闭区间
  minValue?: number; // 最小值
  maxOpenOrCloseRange?: RegionItem; // 最大值开闭区间
  maxValue?: number; // 最大值开闭区间
}
interface RegionInputProps {
  value?: RegionValue;
  onChange?: (value: RegionValue) => void;
  disabledForm?: boolean;
  minOpenOrCloseRangeReplace?: string; // 字段替换名
  maxOpenOrCloseRangeReplace?: string; // 字段替换名
  minValueReplace?: string; // 字段替换名
  maxValueReplace?: string; // 字段替换名
  precision?: number;
}

const RegionInput: React.FC<RegionInputProps> = ({
  value = {},
  onChange,
  disabledForm,
  minOpenOrCloseRangeReplace,
  maxOpenOrCloseRangeReplace,
  minValueReplace,
  maxValueReplace,
  precision,
}) => {
  console.log('RegionInput', value);

  const { Option } = Select;
  const [minOpenOrCloseRange, setMinOpenOrCloseRange] = useState<RegionItem>('CLOSE_RANGE');
  const [minValue, setMinValue] = useState<number>();
  const [maxOpenOrCloseRange, setMaxOpenOrCloseRange] = useState<RegionItem>('CLOSE_RANGE');
  const [maxValue, setMaxValue] = useState<number>();

  const triggerChange = (changedValue: {
    minOpenOrCloseRange?: RegionItem; // 最小值开闭区间
    minValue?: number; // 最小值
    maxOpenOrCloseRange?: RegionItem; // 最大值开闭区间
    maxValue?: number;
  }) => {
    onChange?.({
      [minOpenOrCloseRangeReplace || 'minOpenOrCloseRange']: minOpenOrCloseRange,
      [minValueReplace || 'minValue']: minValue,
      [maxOpenOrCloseRangeReplace || 'maxOpenOrCloseRange']: maxOpenOrCloseRange,
      [maxValueReplace || 'maxValue']: maxValue,
      ...value,
      [minOpenOrCloseRangeReplace || 'minOpenOrCloseRange']:
        value?.minOpenOrCloseRange || 'CLOSE_RANGE', // 如果初始值为null,默认闭区间
      [maxOpenOrCloseRangeReplace || 'maxOpenOrCloseRange']:
        value?.maxOpenOrCloseRange || 'CLOSE_RANGE', // 如果初始值为null,默认闭区间
      ...changedValue,
    });
  };

  // 加载初始值
  const onMinValueChange = (newNumber: any) => {
    // console.log(value);
    // console.log('minValue' in value);
    if (Number.isNaN(minValue)) {
      return;
    }
    if (!('minValue' in value && `${minValueReplace}` in value)) {
      setMinValue(newNumber);
    }
    triggerChange({ [minValueReplace || 'minValue']: newNumber });
  };

  const onMaxValueChange = (newNumber: any) => {
    if (Number.isNaN(maxValue)) {
      return;
    }

    if (!('maxValue' in value && `${maxValueReplace}` in value)) {
      setMaxValue(newNumber);
    }
    triggerChange({ [maxValueReplace || 'maxValue']: newNumber });
  };

  const onMinOpenOrCloseRangeChange = (newValue: RegionItem) => {
    if (!('minOpenOrCloseRange' in value && `${minOpenOrCloseRangeReplace}` in value)) {
      setMinOpenOrCloseRange(newValue);
    }
    triggerChange({ [minOpenOrCloseRangeReplace || 'minOpenOrCloseRange']: newValue });
  };

  //
  const onMaxOpenOrCloseRangeChange = (newValue: RegionItem) => {
    if (!('maxOpenOrCloseRange' in value && `${maxOpenOrCloseRangeReplace}` in value)) {
      setMaxOpenOrCloseRange(newValue);
    }
    triggerChange({ [maxOpenOrCloseRangeReplace || 'maxOpenOrCloseRange']: newValue });
  };

  return (
    <div style={{ display: 'flex' }}>
      <Input.Group compact>
        <Select
          value={
            value?.[minOpenOrCloseRangeReplace || 'minOpenOrCloseRange'] ||
            minOpenOrCloseRange ||
            'CLOSE_RANGE'
          }
          onChange={onMinOpenOrCloseRangeChange}
          disabled={disabledForm}
        >
          <Option value="OPEN_RANGE">开区间</Option>
          <Option value="CLOSE_RANGE">闭区间</Option>
        </Select>
        <InputNumber
          style={{ width: '50%' }}
          // defaultValue=""
          value={value?.[minValueReplace || 'minValue']}
          precision={precision}
          min={0}
          disabled={disabledForm}
          onChange={onMinValueChange}
        />
      </Input.Group>
      {/* <div>———</div> */}
      <Input.Group compact>
        <Select
          value={
            value?.[maxOpenOrCloseRangeReplace || 'maxOpenOrCloseRange'] ||
            maxOpenOrCloseRange ||
            'CLOSE_RANGE'
          }
          onChange={onMaxOpenOrCloseRangeChange}
          disabled={disabledForm}
        >
          <Option value="OPEN_RANGE">开区间</Option>
          <Option value="CLOSE_RANGE">闭区间</Option>
        </Select>
        <InputNumber
          style={{ width: '50%' }}
          // defaultValue=""
          value={value?.[maxValueReplace || 'maxValue']}
          precision={precision}
          min={0}
          disabled={disabledForm}
          onChange={onMaxValueChange}
        />
      </Input.Group>
    </div>
  );
};

export default React.memo(RegionInput);
