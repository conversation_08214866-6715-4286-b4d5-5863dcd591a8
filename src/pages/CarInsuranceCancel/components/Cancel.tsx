import { Button, Modal } from 'antd';
import React, { useRef } from 'react';
import { DetailInfo, ListItem } from '../const';
import '../index.less';
import BasicInfo from './BasicInfo';
import UploadPannel from './UploadPannel';

interface ModalProps {
  closeModal: (refresh?: boolean) => void;
  visible: boolean;
  currentUser: any;
  record: ListItem;
  detailInfo: DetailInfo;
}

const Page: React.FC<ModalProps> = ({ visible, closeModal, currentUser, record, detailInfo }) => {
  // const { orderNo, bizNo } = record;
  const uploadRef = useRef<any>();

  return (
    <>
      <Modal
        width={960}
        destroyOnClose
        title={`退保详情`}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            <Button onClick={() => closeModal()}>关闭</Button>
          </>
        }
      >
        {/*  */}
        <BasicInfo detailInfo={detailInfo} orderStatus={detailInfo?.orderStatus} />
        <UploadPannel
          ref={uploadRef}
          detailInfo={detailInfo}
          defaultFileList={detailInfo?.fileInfoAttach ? detailInfo?.fileInfoAttach : []}
        />
      </Modal>
    </>
  );
};
export default Page;
