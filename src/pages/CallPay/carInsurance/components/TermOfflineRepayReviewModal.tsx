import type { FullScreenLoadingInstance } from '@/components/FullScreenLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import LoadingButton from '@/components/LoadingButton';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { ActionType } from '@ant-design/pro-components';
import { ModalForm } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Descriptions, message, Space, Spin, Table } from 'antd';
import type { MutableRefObject } from 'react';
import React, { memo, useRef, useState } from 'react';
import { PAY_TYPE } from '../../const';
import { TermOfflineRepayReviewModalColumns } from '../columns/TermOfflineRepayReviewModalColumns';
import { applyOrderInfo, submitBillAudit } from '../services';
import type { IapplyOrderInfo, IofflineRepayReviewItem } from '../types';
import { IstatusEnCode } from '../types';
import './index.less';

type Props = {
  record?: IofflineRepayReviewItem;
  termOfflineRepayReviewOpen: boolean;
  setTermOfflineRepayReviewOpen: (val: boolean) => void;
  actionRef: MutableRefObject<ActionType | undefined>;
};
const TermOfflineRepayReviewModal: React.FC<Props> = (props) => {
  const { record, termOfflineRepayReviewOpen, setTermOfflineRepayReviewOpen, actionRef } = props;
  const fullScreenLoadingRef = useRef<FullScreenLoadingInstance>(null);
  const [applyOrderInfoData, setApplyOrderInfoData] = useState<IapplyOrderInfo>();
  const [loading, setLoading] = useState(false);
  const imagePreviewRef = useRef<ImagePreviewInstance>(null);
  const access = useAccess();

  async function getApplyOrderInfo() {
    try {
      if (record?.businessNo) {
        setLoading(true);
        const data = await applyOrderInfo({ businessNo: record?.businessNo });
        setApplyOrderInfoData(data);
        setTermOfflineRepayReviewOpen(true);
      }
    } finally {
      setLoading(false);
    }
  }

  return (
    <>
      <ModalForm
        title={<div className="modal_tit">线下还款</div>}
        open={termOfflineRepayReviewOpen}
        onOpenChange={(val) => {
          console.log('valvalval', val);
          if (!val) {
            // 当弹窗被关闭的时候 清空数据
            setApplyOrderInfoData(undefined);
          } else {
            getApplyOrderInfo();
          }
        }}
        width={1000}
        modalProps={{
          onCancel: () => {
            setTermOfflineRepayReviewOpen(false);
          },
        }}
        submitter={{
          render() {
            return (
              // 只有待审批 才展示
              applyOrderInfoData?.status === IstatusEnCode.PendingApproval &&
              access.hasAccess('bill_submit_audit_postLoanMng_callpay') && (
                <Space>
                  <LoadingButton
                    type="primary"
                    danger
                    onClick={async () => {
                      try {
                        if (record?.businessNo) {
                          fullScreenLoadingRef.current?.open();
                          await submitBillAudit({
                            auditStatus: 1,
                            businessNo: record?.businessNo,
                          });
                          message.success('提交成功');
                          setTermOfflineRepayReviewOpen(false);
                          actionRef.current?.reload();
                        }
                      } catch (error) {
                      } finally {
                        fullScreenLoadingRef.current?.close();
                      }
                    }}
                  >
                    驳回
                  </LoadingButton>
                  <LoadingButton
                    type="primary"
                    onClick={async () => {
                      try {
                        if (record?.businessNo) {
                          fullScreenLoadingRef.current?.open();
                          await submitBillAudit({
                            auditStatus: 2,
                            businessNo: record?.businessNo,
                          });
                          message.success('提交成功');
                          setTermOfflineRepayReviewOpen(false);
                          actionRef.current?.reload();
                        }
                      } catch (error) {
                      } finally {
                        fullScreenLoadingRef.current?.close();
                      }
                    }}
                  >
                    审核通过
                  </LoadingButton>
                </Space>
              )
            );
          },
        }}
      >
        <Spin spinning={loading}>
          <div className="block_area">
            <Descriptions bordered column={2} size={'small'}>
              <Descriptions.Item span={1} label={<div className="label_width">还款ID</div>}>
                {applyOrderInfoData?.businessNo || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">渠道名称</div>}>
                {applyOrderInfoData?.channelName || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={2} label={<div className="label_width">客户名称</div>}>
                {applyOrderInfoData?.accountNameList?.join(',') || '-'}
              </Descriptions.Item>
            </Descriptions>
          </div>
          <div className="tit">请审核减免金额：</div>
          <div className="block_area">
            <div>
              减免总金额:
              <span style={{ color: 'red' }}>{applyOrderInfoData?.exemptionAmount || '-'}</span>元
            </div>
            <Table
              pagination={false}
              dataSource={applyOrderInfoData?.remissionList}
              columns={[
                {
                  title: '减免本金(元)',
                  dataIndex: 'remissionPrincipal',
                },
                {
                  title: '减免利息(元)',
                  dataIndex: 'remissionInterest',
                },
                {
                  title: '减免罚息(元)',
                  dataIndex: 'remissionPenalty',
                },
                {
                  title: '减免违约金(元)',
                  dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
                },
                {
                  title: '减免滞纳金(元)',
                  dataIndex: 'remissionDelayAmount',
                },
              ]}
            />
          </div>

          <div className="tit">请审核还款金额：</div>
          <div className="block_area">
            <div>
              还款总金额:
              <span style={{ color: 'red' }}>{applyOrderInfoData?.repayAmount || '-'}</span>元
            </div>
            <Table
              title={() => (
                <>
                  <ExclamationCircleOutlined
                    style={{ fontSize: 16, color: 'orange', marginRight: 5 }}
                  />
                  以下为操作还款时，账单快照数据。
                </>
              )}
              columns={TermOfflineRepayReviewModalColumns}
              dataSource={applyOrderInfoData?.billRspDTOList}
              scroll={{ x: 'max-content' }}
              pagination={false}
            />
          </div>

          <div className="tit">付款信息</div>
          <div className="block_area pt_0">
            <Descriptions bordered column={2} size={'small'} style={{ marginTop: 20 }}>
              <Descriptions.Item span={2} label="付款凭证">
                {applyOrderInfoData?.attachList?.length
                  ? applyOrderInfoData?.attachList?.map((file) => {
                      const { url } = file;
                      const urlList = applyOrderInfoData?.attachList?.map((item) => item.url);
                      return (
                        <div>
                          <a
                            onClick={() => {
                              imagePreviewRef.current?.previewFile({ url, urlList });
                            }}
                          >
                            {file.name}
                          </a>
                        </div>
                      );
                    })
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label="支付方式">
                <span>{PAY_TYPE?.[applyOrderInfoData?.remitType] || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="金额">
                <span>{applyOrderInfoData?.bankAmount || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="打款日期">
                <span>{applyOrderInfoData?.bankAmountDate || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="付款账号">
                <span>{applyOrderInfoData?.bankNo || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="付款户名">
                <span>{applyOrderInfoData?.bankAccount || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="付款银行">
                <span>{applyOrderInfoData?.bankName || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="付款银行支行">
                <span>{applyOrderInfoData?.subBranchBank || '-'}</span>
              </Descriptions.Item>
              <Descriptions.Item span={1} label="银行到账流水号">
                <span>{applyOrderInfoData?.repaySerialNo || '-'}</span>
              </Descriptions.Item>
            </Descriptions>
          </div>
        </Spin>
      </ModalForm>

      <ImagePreview ref={imagePreviewRef} />
      <FullScreenLoading ref={fullScreenLoadingRef} />
    </>
  );
};
export default memo(TermOfflineRepayReviewModal);
