/*
 * @Author: your name
 * @Date: 2021-01-12 17:01:50
 * @LastEditTime: 2023-02-02 14:08:41
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/data.d.ts
 */

export interface CollectReliefItem {
  createdAt: string; // 创建时间
  overdueId: string; // 催收单号
  remissionId: string; // 催收减免单号
  status: number;
  urgePerson: string; // 催收员
  reason: string;
  reviewTime: string;
  reviewPerson: string;
  overdueCaseNo: string;
  orderNo?: string;
}

export interface CollectReliefParams {
  createdAtEnd?: string; // 结束时间
  createdAtStart?: string; // 开始时间
  overdueId?: string; // 催收单号
  remissionId?: string; // 催收减免单号
  urgePerson?: string; // 催收员
  status?: number; // 状态
  current?: number; // 当前页
  pageSize?: number; // 页大小
}
