/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2024-09-19 16:18:29
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/MailList/service.ts
 */
import { request } from 'umi';
import type { IMailListItem } from './data';

// import type { OrderListParams } from './data';

export async function queryMailList(data?: IMailListItem) {
  return request('/bizadmin/repayment/cms/email/getList', {
    method: 'POST',
    data,
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
    ifTrimParams: true,
  });
}

export async function mailExport(data?: IMailListItem) {
  return request('/bizadmin/repayment/cms/email/record/exportAsync', {
    method: 'POST',
    data,
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
    ifTrimParams: true,
  });
}
