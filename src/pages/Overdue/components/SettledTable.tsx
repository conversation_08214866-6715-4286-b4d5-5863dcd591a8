import { message } from 'antd';
import React, { memo, useEffect, useRef, useState } from 'react';

import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_MAP_ALL } from '@/enums';
import { getProductNameEnum, getUserListEnum } from '@/services/enum';
import {
  billNoToOrderOrBillDetail,
  createDetailPath,
  disableFutureDate,
  downLoadExcel,
  isExternalNetwork,
} from '@/utils/utils';
import { history, Link, useAccess, useModel } from '@umijs/max';
import dayjs from 'dayjs';
import type { OverdueItem } from '../data';
import { getOverdueList, overdueExport } from '../service';

import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';

import LoadingButton from '@/components/LoadingButton';

const SettledTable: React.FC<any> = () => {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const access = useAccess();

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const [productCodeValueEnum, setProductCodeValueEnum] = useState<Record<string, string>>({});

  const [urgePersonValueEnum, setUrgePersonValueEnum] = useState({});
  const [dataSource, setDataSource] = useState<any[]>([]);
  const overdueCaseNoList = dataSource?.map((item) => {
    return item.overdueCaseNo;
  });

  useEffect(() => {
    getUserListEnum().then((data) => {
      const value = data.reduce((pre: any, cur: any) => {
        return { ...pre, [cur.value]: cur.label };
      }, {});
      setUrgePersonValueEnum(value);
    });
  }, []);

  useEffect(() => {
    getProductNameEnum().then((data) => {
      setProductCodeValueEnum(
        data.reduce((pre, cur) => {
          return {
            ...pre,
            [cur.value]: cur.label,
          };
        }, {}),
      );
    });
  }, []);

  useEffect(() => {
    if (isExternalNetwork()) {
      formRef.current?.setFieldsValue({ urgePerson: currentUser?.userId });
    }
  }, [currentUser?.userId]);

  const columns: ProColumns<OverdueItem>[] = [
    {
      title: '订单号/账单号',
      dataIndex: 'billNo',
      render: (_, row: OverdueItem) => (
        <Link
          to={billNoToOrderOrBillDetail({
            productCode: row.productCode,
            billNo: row.billNo,
            accountNumber: row.accountNumber,
          })}
        >
          {row.billNo}
        </Link>
      ),
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondary',
      valueType: 'select',
      valueEnum: SECONDARY_CLASSIFICATION_MAP_ALL,
      hideInTable: true,
      fieldProps: {
        onChange: () => {
          formRef.current?.setFieldValue('productCode', undefined);
        },
      },
    },
    {
      title: '产品ID',
      dataIndex: 'productCode',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      valueEnum: () => {
        const productSecondCode = formRef.current?.getFieldValue('secondary');
        const productCodeMap: any = {};
        for (const productCode in productCodeValueEnum) {
          const secondCode = productCode.substring(0, 4);
          if (productSecondCode === secondCode) {
            productCodeMap[productCode] = productCodeValueEnum[productCode];
          }
        }
        return productSecondCode ? productCodeMap : productCodeValueEnum;
      },
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) => {
          return option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
        },
        onChange(val: string) {
          formRef.current?.setFieldValue('secondary', val.substring(0, 4));
        },
        onSelect: (val: string) => {
          if (
            val.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING ||
            !formRef.current?.getFieldValue('productCode')
          ) {
            formRef.current?.setFieldsValue({ repayPlanNo: '' });
          } else {
            formRef.current?.setFieldsValue({ billNo: '' });
          }
        },
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '还款期数',
      dataIndex: 'termDetail',
      search: false,
    },
    {
      title: '应还日期',
      dataIndex: 'repaymentDate',
      valueType: 'dateRange',
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      search: {
        transform: (value: any) => ({
          repayStartTime: `${value[0]} 00:00:00`,
          repayEndTime: `${value[1]} 23:59:59`,
        }),
      },
      render(_, record) {
        return record.repaymentDate || '-';
      },
    },
    {
      title: '待还逾期金额（元）',
      dataIndex: 'overdueAmount',
      search: false,
      hideInTable: true,
    },
    {
      title: '逾期等级',
      dataIndex: 'overdueLevel',
      valueEnum: {
        M1: 'M1',
        M2: 'M2',
        M3: 'M3',
        M4: 'M4',
        M5: 'M5',
        M6: 'M6',
        'M6+': 'M6+',
      },
    },
    {
      title: '逾期天数',
      dataIndex: 'daysOverdue',
      search: false,
    },
    {
      title: '催收员',
      dataIndex: 'urgePerson',
      valueType: 'select',
      valueEnum: urgePersonValueEnum,
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        disabled: isExternalNetwork(), // 外网禁止修改 只展示当前角色下的单子
        filterOption: (input: string, option: { label: string }) => {
          return option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
        },
      },
      render(_, record) {
        // 数据量大的情况下 执行的js时间很长 阻塞ui渲染
        return urgePersonValueEnum?.[record?.urgePerson as keyof typeof urgePersonValueEnum] || '-';
      },
    },
    {
      title: '催收状态',
      dataIndex: 'urgeState',
      key: 'urgeState',
      valueEnum: {
        20: '待跟进',
        30: '跟进中',
        40: '催收结案',
        50: '催收撤销',
      },
    },
    {
      title: '结清时间',
      dataIndex: 'actualSettleDate',
      valueType: 'dateRange',
      search: {
        transform: (value: [string, string]) => ({
          settleStartTime: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
          settleEndTime: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
        }),
      },
      render(_, record: OverdueItem) {
        return record?.actualSettleDate || '-';
      },
    },
    {
      title: '操作',
      width: 180,
      align: 'center',
      fixed: 'right',
      valueType: 'option',
      render: (_, row: OverdueItem, dataIndex) => (
        <>
          {!row?.overdueCaseNo ? (
            <a
              onClick={() => {
                if (!row.overdueId) {
                  message.warning('催收单号有误！');
                  return;
                }
                history.push(
                  createDetailPath({
                    productCode: row?.productCode,
                    overdueId: row?.overdueId,
                  }),
                );
              }}
            >
              查看逾期详情
            </a>
          ) : (
            <Link
              to={{
                pathname: `/businessMng/postLoanMng/collection-detail`,
                search: `?overdueCaseNo=${row?.overdueCaseNo}`,
              }}
              state={{
                // 在详情页的右上脚可以快速点击上一页 下一页
                curList: overdueCaseNoList || [],
                curItemIndex: dataIndex,
              }}
            >
              查看催收案件详情
            </Link>
          )}
        </>
      ),
    },
  ];

  return (
    <>
      <ProTable<OverdueItem>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        scroll={{ x: 'max-content' }}
        rowKey="overdueId"
        search={{
          labelWidth: 100,
          defaultCollapsed: false,
        }}
        form={{
          style: { paddingTop: 0 },
        }}
        toolBarRender={() => {
          return [
            access.hasAccess('biz_download') && (
              <LoadingButton
                key="button"
                type="primary"
                onClick={async () => {
                  const params = formRef?.current?.getFieldsFormatValue?.();
                  const res = await overdueExport({
                    ...params,
                    beAssigned: true, // 已分配
                    urgeStateList: [40, 50],
                  });
                  downLoadExcel(res);
                }}
              >
                导出
              </LoadingButton>
            ),
          ];
        }}
        request={async (params) => {
          // 会有初始值 催收员 外网情况下
          if (isExternalNetwork()) {
            if (!params?.urgePerson) {
              params.urgePerson = currentUser?.userId;
            }
          }

          const data = await getOverdueList({
            ...params,
            beAssigned: true,
            urgeStateList: [40, 50].join(','),
          });
          setDataSource(data?.data);
          return {
            data: data?.data,
            success: true,
            total: data?.total,
          };
        }}
      />
    </>
  );
};

export default memo(SettledTable);
