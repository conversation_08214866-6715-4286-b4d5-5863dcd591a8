/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import { PRODUCT_CLASSIFICATION_CODE } from '@/enums';
import globalStyle from '@/global.less';
import { PhoneFilled } from '@ant-design/icons';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import ProTable from '@ant-design/pro-table';
import { Access, history, Link, useAccess, useModel } from '@umijs/max';
import { Button, Card, Col, Form, message, Popconfirm, Row, Tabs } from 'antd';
import React, { useState } from 'react';
import type { OverdueItem } from '../data';
import { addContact, deleteContact } from '../service';

interface BasicInfoProps {
  basicInfo: OverdueItem;
  contactPhoneBOList: any[];
  urgeUserChangeDTOList: any[];
  addContactBack: any;
  // productCode: string;
}

const { TabPane } = Tabs;

const BasicInfo: React.FC<BasicInfoProps> = (props) => {
  const { mapUrgePersonList: mapUserList } = useModel('userList');
  const { setDialOuterCallNumber } = useModel('caller');
  const { basicInfo, contactPhoneBOList, urgeUserChangeDTOList, addContactBack } = props;
  const access = useAccess();
  const basicMap = {
    overdueId: '催收单号',
    accountNumber: '用户ID',
    accountName: '用户名称',
    productName: '产品名称',
    billNo: '账单编号',
    repayPlanNo: '还款编号',
    createdAt: '创建时间',
    daysOverdue: '逾期天数',
    overdueAmount: '逾期金额',
    overdueInterest: '逾期利息',
    overduePenaltyInterest: '逾期罚息',
    returnedAmount: '已还金额',
    // contactPerson: '联系人',
    // contactPhone: '联系人电话',
    // remainingAmount: '剩余未还',
    repaymentDate: '应还日期',
    urgePerson: '催收员',
    urgeState: '催收状态',
  };
  if (basicInfo?.productCode.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING) {
    delete basicMap.repayPlanNo;
  } else {
    delete basicMap.billNo;
    delete basicMap.overdueInterest;
    delete basicMap.overduePenaltyInterest;
  }
  const itemMap = {
    urgeState: {
      10: '待分配',
      20: '待跟进',
      30: '跟进中',
      40: '催收结清',
      50: '逾期撤销',
    },
    urgePerson: mapUserList,
    contactPhone: (value: string) => {
      return (
        <span className={globalStyle.pointer}>
          {value}
          {[40, 50].indexOf(basicInfo.urgeState) < 0 && (
            <Access accessible={access.hasAccess('biz_caller')}>
              &nbsp;
              <PhoneFilled onClick={() => setDialOuterCallNumber(value)} />
            </Access>
          )}
        </span>
      );
    },
  };

  const [tabType, setTabType] = useState('1');
  const [modalVisible, setModalVisible] = useState(false);
  const [formRef] = Form.useForm();

  const contactPhoneBOListColumns = [
    {
      title: '联系人',
      dataIndex: 'contactPerson',
    },
    {
      title: '联系电话',
      dataIndex: 'contactPhone',
      render: (value) => {
        return (
          <>
            <span className={globalStyle.pointer}>
              {value}
              {[40, 50].indexOf(basicInfo.urgeState) < 0 && (
                <Access accessible={access.hasAccess('biz_caller')}>
                  &nbsp;
                  <PhoneFilled onClick={() => setDialOuterCallNumber(value)} />
                </Access>
              )}
            </span>
          </>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
    },
    {
      title: '来源',
      dataIndex: 'sourceDesc',
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      valueType: 'option',
      render: (_, row: any) => (
        <Popconfirm
          placement="left"
          disabled={row.source === 1}
          title="请再次确认是否删除？"
          onConfirm={() => deleteContactFn(row.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" disabled={row.source === 1}>
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];
  const urgeUserChangeDTOListColumns = [
    {
      title: '催收员姓名',
      dataIndex: 'urgePersonName',
    },
    {
      title: '派单时间',
      dataIndex: 'dispatchTime',
    },
    {
      title: '操作事件',
      dataIndex: 'operateEvent',
    },
  ];
  function tabsClickCallback(key: any) {
    setTabType(key);
  }
  function onVisibleChange(visible: any) {
    setModalVisible(visible);
  }
  function deleteContactFn(id: string) {
    if (!id) return;
    deleteContact(id).then(() => {
      message.success('操作成功！');
      addContactBack();
    });
  }
  return (
    <Card title="基础信息">
      <Row>
        {Object.keys(basicMap).map((item) => {
          const value =
            (basicInfo &&
              // eslint-disable-next-line no-nested-ternary
              (itemMap[item]
                ? typeof itemMap[item] === 'function'
                  ? itemMap[item](basicInfo[item], basicInfo)
                  : itemMap[item][basicInfo[item]]
                : basicInfo[item])) ||
            '';

          const mapItemLink = {
            billNo: (
              <Link
                to={`/businessMng/bill-detail?billNo=${value}&accountNumber=${basicInfo?.accountNumber}`}
                className={globalStyle.ml20}
              >
                {value}
              </Link>
            ),
            repayPlanNo: (
              <Link
                to={`/businessMng/postLoanMng/after-loan-detail?orderNo=${basicInfo?.orderNo}&productCode=${basicInfo?.productCode}`}
                className={globalStyle.ml20}
              >
                {value}
              </Link>
            ),
          };
          return (
            <Col span={8} key={item}>
              <div style={{ lineHeight: '40px' }}>
                <span>{basicMap[item]}:</span>
                {item === 'billNo' || item === 'repayPlanNo' ? (
                  mapItemLink[item]
                ) : (
                  <span className={globalStyle.ml20}>{value}</span>
                )}
              </div>
            </Col>
          );
        })}
      </Row>

      <Tabs defaultActiveKey={tabType} onChange={tabsClickCallback}>
        <TabPane tab="联系人" key="1">
          <p>
            <Button
              type="primary"
              onClick={() => setModalVisible(true)}
              disabled={[40, 50].indexOf(basicInfo?.urgeState) > -1}
            >
              新增
            </Button>
          </p>
          <ProTable
            search={false}
            columns={contactPhoneBOListColumns}
            dataSource={contactPhoneBOList}
            toolBarRender={false}
          />
        </TabPane>
        <TabPane tab="催收员变动记录" key="2">
          <ProTable
            search={false}
            columns={urgeUserChangeDTOListColumns}
            dataSource={urgeUserChangeDTOList}
            toolBarRender={false}
          />
        </TabPane>
      </Tabs>

      <ModalForm
        title="新建联系人"
        width={600}
        form={formRef}
        layout="horizontal"
        visible={modalVisible}
        onVisibleChange={(visible) => onVisibleChange(visible)}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          maskClosable: false,
        }}
        onFinish={async (values: any) => {
          const params = {
            ...values,
            operator: access.currentUser?.userName,
            overdueId: history.location.query.overdueId,
          };

          const res = await addContact(params);
          if (res.success) {
            message.success('添加成功');
            setModalVisible(false);
            addContactBack();
          } else {
            message.error(res.msg);
          }
        }}
      >
        <ProFormText
          name="contactPerson"
          labelCol={{ span: 4 }}
          width="md"
          label="联系人姓名"
          rules={[{ required: true, message: '联系人姓名' }]}
        />
        <ProFormText
          name="contactPhone"
          labelCol={{ span: 4 }}
          width="md"
          label="联系人电话"
          rules={[
            { required: true, message: '请输入号码' },
            {
              validator: (_, value) => {
                if (value) {
                  const phoneReg = /^1\d{10}$/;
                  if (!phoneReg.test(value)) {
                    return Promise.reject(new Error('号码格式不正确'));
                  }
                }
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormText name="remark" labelCol={{ span: 4 }} width="md" label="备注" />
      </ModalForm>
    </Card>
  );
};

export default BasicInfo;
