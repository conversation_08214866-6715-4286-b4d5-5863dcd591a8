/*
 * @Author: your name
 * @Date: 2021-04-19 15:34:33
 * @LastEditTime: 2024-04-19 10:42:16
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/components/LoanLeaseInfo.tsx
 */

import { DividerTit } from '@/components';
import ShowInfo from '@/components/ShowInfo';
import StepProgress from '@/components/StepProgress/index';
import { LOAN_MANAGE_CODE, NOTARIZATION_STATUS, NOTARIZATION_STATUS_MAP } from '@/enums';
import globalStyle from '@/global.less';
import { downLoadExcel } from '@/utils/utils';
import { ModalForm, ProFormTextArea } from '@ant-design/pro-form';
import { useAccess } from '@umijs/max';
import { Button, message, Modal, Row, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import type { AddApplyDataParams, DataInfo } from '../data.d';
import { exportLeaseInfo, leaseAudit } from '../service';
import LoanResource from './LoanResource';
import LoanResourceUploadInfo from './LoanResourceUploadInfo';

enum LOAN_TYPE {
  INCOME = 1, // 进件
  THIRD_LOAN = 9, //资方放款
}

enum LICENSE_TYPE {
  COMPANY = 1, // 挂靠
  PERSONAL = 2, // 个户
}
interface LoanLeaseInfoProps {
  dataInfo: AddApplyDataParams;
  dataLending: DataInfo;
  orderDetail: Record<string, any>;
  productCode: string;
  userNo: string;
  userName?: string;
  loanTerm?: string;
  loanType: string;
  auditLogs: [];
  guaranteeType: string;
  refresh: () => void;
}

export enum ORDER_STATUS {
  HANG_UP = 7,
  FIRST_APPROVE = 10,
  SECOND_APPROVE = 11,
}

enum ACTION_STATUS {
  PASS = 1,
  REJECT = 2,
  HANG_UP = 3,
  UN_HANG_UP = 4,
}

const LoanLeaseInfo: React.FC<LoanLeaseInfoProps> = (props) => {
  const access = useAccess();
  const {
    dataInfo: data,
    dataLending,
    orderDetail,
    productCode,
    userNo,
    userName,
    loanType,
    loanTerm,
    auditLogs,
    guaranteeType,
    refresh,
  } = props;

  const [rejectVisible, setRejectVisible] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [showLoanMap, setShowLoanMap] = useState({});

  const loanInfoMap = {
    lendingNo: '放款流水号',
    type: '放款类型',
    amount: '放款金额',
    lendingMaster: '放款主体',
    receiptMaster: '收款主体',
    re: '放款周期',
    lendingModel: '放款方式',
    fundFlow: '是否发生资金流',
    lendingTime: '放款时间',
    status: '放款状态',
    rejectionReason: '驳回原因',
    rejectionTime: '驳回时间',
    lendingMsg: '备注',
  };
  const shBankInfo = {
    failReason: '审批失败原因',
    failReasonRemark: '失败原因描述',
  };

  useEffect(() => {
    if (dataLending) {
      if (dataLending.type === LOAN_TYPE.THIRD_LOAN) {
        setShowLoanMap({ ...loanInfoMap, ...shBankInfo });
      } else {
        setShowLoanMap({ ...loanInfoMap });
      }
    }
  }, [dataLending]);

  const itemMap = {
    type: {
      1: '进件',
      2: '债转',
      3: '代偿',
      9: '资方放款',
    },
    fundFlow: {
      false: '否',
      true: '是',
    },
    status: {
      1: '待放款',
      0: '放款失败',
      2: '放款成功',
      3: '待请款',
      7: '挂起',
      10: '待一审',
      11: '待二审',
      12: '驳回',
      13: '待换卡',
      '-1': '失效',
    },
    lendingModel: {
      1: '线上',
      2: '线下',
    },
  };

  const handleApprove = async (action: number, remark?: string) => {
    const params = {
      auditNo: new Date().getTime(),
      auditType: data?.lendingStatus === ORDER_STATUS.FIRST_APPROVE ? 1 : 2,
      auditResult: action,
      remark,
      productCode,
      lendingNos: [dataLending.lendingNo],
      gpsLessOrderNos: [dataLending.orderNo],
    };
    console.log('params', params);
    await leaseAudit(params);
    message.success('操作成功!');
    setRejectVisible(false);
    // 刷新页面数据
    refresh();
  };

  // 放款弹窗确认
  const onPass = () => {
    Modal.confirm({
      title: '放款',
      content: (
        <div>
          <div>
            本次放款<b>1</b>条，总金额
            <span style={{ color: 'red' }}>{dataLending?.amount}</span>
            元，是否确认放款？
          </div>
          {orderDetail?.licenseType === LICENSE_TYPE.PERSONAL && (
            <div
              style={{
                marginTop: '10px',
              }}
            >
              注：赋强公证结果：
              <span
                style={{
                  fontSize: 16,
                  color:
                    data?.notarizationStatus === NOTARIZATION_STATUS.CARD_DONE ? 'green' : 'red',
                }}
              >
                {NOTARIZATION_STATUS_MAP.get(data?.notarizationStatus)}
              </span>
            </div>
          )}
        </div>
      ),
      onOk: () => {
        handleApprove(ACTION_STATUS.PASS);
      },
    });
  };

  // 放款驳回
  const handleReject = (values: any) => {
    const { remark } = values;
    handleApprove(ACTION_STATUS.REJECT, remark);
  };

  // 挂起
  const onHangUp = () => {
    Modal.confirm({
      title: '是否确认挂起?',
      onOk: () => {
        handleApprove(ACTION_STATUS.HANG_UP);
      },
    });
  };

  // 解除挂起
  const onOpenHangUp = () => {
    Modal.confirm({
      title: '是否确认解除挂起?',
      onOk: () => {
        handleApprove(ACTION_STATUS.UN_HANG_UP);
      },
    });
  };

  const handleExport = async () => {
    setBtnLoading(true);
    try {
      exportLeaseInfo(dataLending?.lendingNo, dataLending?.orderNo).then((res: any) => {
        const type = 'application/octet-stream;charset=UTF-8';
        downLoadExcel(res, type);
      });
    } finally {
      setBtnLoading(false);
    }
  };

  return (
    <ShowInfo title="放款信息" infoMap={showLoanMap} data={dataLending} itemMap={itemMap}>
      {/* 资方放款不展示请款状态*/}
      {![LOAN_TYPE.THIRD_LOAN].includes(Number(loanType)) && (
        <>
          <DividerTit title="请款状态">
            <Space>
              {!(access.hasRole('leaseChannelUser') || access.hasRole('leaseStoreUser')) && (
                <>
                  {data?.lendingStatus === ORDER_STATUS.FIRST_APPROVE && (
                    <>
                      <Button type="primary" onClick={onPass}>
                        通过
                      </Button>
                      <Button
                        danger
                        onClick={() => {
                          setRejectVisible(true);
                        }}
                      >
                        驳回
                      </Button>
                      <Button onClick={onHangUp}>挂起</Button>
                    </>
                  )}
                  {data?.lendingStatus === ORDER_STATUS.HANG_UP && (
                    <>
                      <Button onClick={onOpenHangUp}>解除挂起</Button>
                    </>
                  )}
                </>
              )}
              <Button type="primary" onClick={handleExport} loading={btnLoading}>
                导出资料
              </Button>
            </Space>
            {auditLogs?.length ? (
              <Row>
                <StepProgress
                  className={globalStyle.stepItemDesc}
                  progressDot
                  lineLength={7}
                  stepStatus={auditLogs?.map(
                    (item: {
                      desc: string;
                      statusDesc: string;
                      time: string;
                      operatorBy: string;
                      remark: string;
                    }) => {
                      return {
                        bol: false,
                        desc: item.desc,
                        localDate: `${item.time} ${item.operatorBy ? item.operatorBy : '-'}`,
                        subStatusDesc: item.remark,
                      };
                    },
                  )}
                />
              </Row>
            ) : null}
          </DividerTit>

          <div>
            <DividerTit title="请款资料">
              {[
                LOAN_MANAGE_CODE.WAIT_CAPTURE,
                LOAN_MANAGE_CODE.WAIT_FIRST_INSTANCE,
                LOAN_MANAGE_CODE.REJECTED,
                LOAN_MANAGE_CODE.HANG_UP,
              ].includes(dataLending?.status as any) ? (
                // 待请款 WAIT_FIRST_INSTANCE REJECTED 需要展示表单
                <LoanResource
                  userNo={userNo}
                  refresh={refresh}
                  data={data as any}
                  orderDetail={orderDetail}
                  status={dataLending?.status as any}
                  vin={dataLending?.vin}
                  noGuaranteeTypes={guaranteeType ? guaranteeType === 'NON_GUARANTEED' : true}
                />
              ) : (
                <LoanResourceUploadInfo
                  data={data as any}
                  baseInfo={{ userName, loanTerm }}
                  orderDetail={orderDetail}
                  dataLending={dataLending}
                />
              )}
            </DividerTit>
          </div>
        </>
      )}

      <ModalForm
        title="驳回"
        width="500px"
        layout="horizontal"
        visible={rejectVisible}
        onVisibleChange={setRejectVisible}
        modalProps={{
          centered: true,
        }}
        onFinish={async (values) => {
          handleReject(values);
        }}
      >
        <ProFormTextArea
          rules={[
            {
              required: true,
              message: '驳回原因为必填',
            },
            {
              whitespace: true,
            },
          ]}
          fieldProps={{
            maxLength: 300,
          }}
          width="md"
          label="驳回原因"
          name="remark"
        />
      </ModalForm>
    </ShowInfo>
  );
};

export default LoanLeaseInfo;
