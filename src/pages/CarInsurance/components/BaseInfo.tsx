/* eslint-disable react-hooks/exhaustive-deps */
import { EcarInsuranceChannelType, EloanUseridentity, EloanUserType } from '@/utils/bankend/enum';
import { useModel } from '@umijs/max';
import type { FormInstance } from 'antd';
import { Form, Input, Select } from 'antd';
import React, { memo, useEffect } from 'react';
import type { IbaseInfo, IrefData } from '../type';
import { LEVEL } from '../type';
// import { isCarInsuranceStoreUser } from '@/utils/utils';

type Props = {
  baseInfoForm: FormInstance;
  productInfoForm: FormInstance;
  enterpriseInfoForm: FormInstance;
  companyInfoForm: FormInstance;
  refData: IrefData;
};
const BaseInfo: React.FC<Props> = (props) => {
  const { baseInfoForm, productInfoForm, enterpriseInfoForm, companyInfoForm, refData } = props;

  const formItemStyle = { width: 180 };

  const { optionsData, isEditable, detailData, setRelationOptions, setBorrowerType } = useModel(
    'CarInsurance.carInsurance',
  );
  const { initialState = {} } = useModel<any>('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode, channelLevel } = currentUser;
  // 如果用户的角色还有channelUser 说明是渠道用户
  // const access = useAccess();
  // const isChannelUser =isCarInsuranceStoreUser(access);
  const { channelList } = optionsData;
  useEffect(() => {
    const { baseInfo = {}, orderNo } = detailData;
    // 只要有值 说名是编辑或者复用
    if (orderNo) baseInfoForm.setFieldsValue(baseInfo);
  }, [baseInfoForm, detailData]);

  useEffect(() => {
    if (channelCode && channelLevel === LEVEL.SECOND_CHANNEL) {
      // 渠道用户有初始值
      const { channelType, channelName } =
        channelList.find((item) => item.channelCode === channelCode) || {};
      baseInfoForm.setFieldsValue({ channelType, channelName, channelCode });
    }
  }, [channelList, channelCode]);
  return (
    <>
      <h3>基本信息</h3>
      <Form<IbaseInfo> layout={'inline'} form={baseInfoForm}>
        {/* 光写这个 required 无法触发校验 要写rule */}
        <Form.Item label="借款人类型" name={'borrowerType'} rules={[{ required: true }]}>
          <Select
            options={Object.keys(EloanUserType).map((item) => {
              return {
                label: EloanUserType[item],
                value: item,
              };
            })}
            onChange={(value) => {
              setBorrowerType(value);
              setRelationOptions({
                channelCode: baseInfoForm.getFieldValue('channelCode'),
                borrowerType: value,
              });
              productInfoForm.resetFields();
            }}
            disabled={!isEditable}
            style={formItemStyle}
          />
        </Form.Item>
        <Form.Item label="渠道类型" required name={'channelType'} rules={[{ required: true }]}>
          <Select
            showSearch
            optionFilterProp="label"
            options={Object.keys(EcarInsuranceChannelType).map((item) => {
              return {
                label: EcarInsuranceChannelType[item],
                value: item,
              };
            })}
            disabled={!isEditable || channelLevel === LEVEL.SECOND_CHANNEL} // 二级渠道用户也是默认的不可选
            style={formItemStyle}
          />
        </Form.Item>
        <Form.Item
          label="渠道名称"
          required
          name={'channelName'}
          rules={[{ required: true }]}
          tooltip="选择后才会关联出渠道对应的企业,公司,产品"
        >
          <Select
            showSearch
            optionFilterProp="label"
            onChange={(value, options: any) => {
              baseInfoForm.setFieldsValue({ channelCode: options.channelCode });
              setRelationOptions({
                channelCode: options.channelCode,
                borrowerType: baseInfoForm.getFieldValue('borrowerType'),
              });
              productInfoForm.resetFields();
              enterpriseInfoForm.resetFields();
              companyInfoForm.resetFields();
              refData.channelIsOnChange = true;
              // 重新选择渠道后 要清空关联的 产品 企业 公司信息
            }}
            options={channelList.map((item) => {
              const { channelName, channelCode: code } = item;
              return {
                label: channelName,
                value: channelName,
                channelCode: code,
                key: code,
              };
            })}
            disabled={!isEditable || channelLevel === LEVEL.SECOND_CHANNEL} // 二级渠道用户也是默认的不可选
            style={formItemStyle}
          />
        </Form.Item>
        <div style={{ display: 'none' }}>
          <Form.Item label="渠道code" required name={'channelCode'} rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        </div>
        <Form.Item
          label="借款人身份"
          required
          name={'borrowerIdentity'}
          rules={[{ required: true }]}
        >
          <Select
            options={Object.keys(EloanUseridentity).map((item) => {
              return {
                label: EloanUseridentity[item],
                value: item,
              };
            })}
            disabled={!isEditable}
            style={formItemStyle}
          />
        </Form.Item>
      </Form>
    </>
  );
};
export default memo(BaseInfo);
