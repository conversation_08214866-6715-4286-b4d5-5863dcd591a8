/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 14:23:54
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-27 16:31:34
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/Overpayment.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { downLoadExcel } from '@/utils/utils';
import { history, useRequest } from '@umijs/max';
import { Button, Table } from 'antd';
import React, { useState } from 'react';
import { getUserAccount, getUserAccountExport } from '../service';

const ProductOverpayment = () => {
  const subAccountNo = history?.location?.query?.subAccountNo as string;
  const [totalOverPay, setTotalOverPay] = useState(0);
  enum MAP_TYPE {
    'INCRESE' = 1,
    'DECRESE' = 2,
  }
  const { tableProps } = useRequest(
    (params) => {
      return getUserAccount({ ...params, subAccountNo }).then((res) => {
        setTotalOverPay(res?.data[0]?.postBalance);
        return res;
      });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      formatResult: (response) => {
        return { list: response.data, total: response.total };
      },
    },
  );
  //导出
  const queryProductOverpaymentExport = (params: { subAccountNo: string }) => {
    getUserAccountExport(params).then((res) => {
      downLoadExcel(res);
    });
  };
  const columns = [
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '操作金额（元）',
      dataIndex: 'changeBalance',
      key: 'changeBalance',
      render: (_, row) => {
        if (row?.type === MAP_TYPE.INCRESE) {
          return `+${row.changeBalance}`;
        } else if (row?.type === MAP_TYPE.DECRESE) {
          return `-${row.changeBalance}`;
        }
        return row.changeBalance || '-';
      },
    },
    {
      title: '变动类型',
      dataIndex: 'customizationMsg',
      key: 'customizationMsg',
    },
    {
      title: '操作后余额（元）',
      dataIndex: 'postBalance',
      key: 'postBalance',
    },
  ];
  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <span>溢缴款余额（元）：</span>
          <span>{totalOverPay}</span>
        </div>
        <Button onClick={() => queryProductOverpaymentExport({ subAccountNo })} type="primary">
          导出
        </Button>
      </div>
      <Table columns={columns} {...tableProps} />
    </div>
  );
};

export default ProductOverpayment;
