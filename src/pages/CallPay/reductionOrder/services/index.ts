import { headers } from '@/utils/constant';
import { request } from '@umijs/max';
import {
  IapplyOrderExportParams,
  IofflineRepayReviewListParams,
  IsubmitBillAudit,
} from '../../carInsurance/types';
import { IapplyOrderInfo, IofflineRepayReviewItem } from '../types';

// 减免单列表
export async function applyOrderRemissionList(
  params: IofflineRepayReviewListParams,
): Promise<{ total: number; data: IofflineRepayReviewItem[] }> {
  return request('/bizadmin/bill/applyOrderRemissionList', {
    method: 'post',
    data: params,
    headers,
    ifTrimParams: true,
  });
}

// 减免单详情
export async function applyOrderRemissionInfo(params: {
  businessNo: string;
}): Promise<IapplyOrderInfo> {
  return request('/bizadmin/bill/applyOrderRemissionInfo', {
    method: 'post',
    data: params,
    headers,
  });
}

// 提交减免审核 审核通过
export async function submitRepayApplyOrderRemissionApprovalPass(params: IsubmitBillAudit) {
  return request('/bizadmin/bill/submitRepayApplyOrderRemissionApproval/pass', {
    method: 'post',
    data: params,
    headers,
  });
}

// 提交减免审核 审核驳回
export async function submitRepayApplyOrderRemissionApprovalRefuse(params: IsubmitBillAudit) {
  return request('/bizadmin/bill/submitRepayApplyOrderRemissionApproval/refuse', {
    method: 'post',
    data: params,
    headers,
  });
}

// 减免单导出
export async function applyOrderRemissionExport(params: IapplyOrderExportParams) {
  return request('/bizadmin/bill/applyOrderRemissionExport', {
    method: 'post',
    data: params,
    headers,
    ifTrimParams: true,
  });
}
