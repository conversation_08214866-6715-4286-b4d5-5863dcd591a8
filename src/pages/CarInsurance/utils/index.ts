import type {
  IcompanyItem,
  IenterpriseItem,
  IproductItem,
  TgetRelationList,
} from '@/pages/CarInsuranceChannel/services';
import type { UloanUserType } from '@/utils/bankend/enum';
import { EcarInsuranceStatus } from '@/utils/bankend/enum';
import { filterProps } from '@/utils/tools';
import { getVanEvn } from '@/utils/utils';
import dayjs from 'dayjs';
import { abandonOrder, reassignOrder, receiveOrder } from '../services';
import type { IcarChannelItem, IcarInfoItem, IcarUploadFileItem, TfileType } from '../type';
/**
 * 获取 base 服务 上传图片的完整路径
 * base 服务 对应的域名 http://lalafin-base-stg.myhll.cn
 * 由于antd上传图片默认的action不经过umi-request,所以app.tsx中设置的base url线上不生效
 * @param path 域名后面的路径 /base/oss/common/uploadfiles
 * @returns 完整的路径
 */
export function getBaseUploadAction(path: string) {
  console.log('path112233', path);
  // http://lalafin-base-stg.myhll.cn
  const { vanEnv } = document.documentElement.dataset;
  // vanEnv 存在 stg pre prod
  // 不存在 本地走代理 直接返回路径
  let baseUrl = '';
  if (vanEnv) {
    const urlEvn = vanEnv === 'prod' ? '' : `-${vanEnv}`;
    baseUrl = `https://finance-api${urlEvn}.lalafin.net`;
  }
  return baseUrl + path;
}

// 数字0 存在
function isExist(value: any): boolean {
  if (value || value === 0) {
    return true;
  } else {
    return false;
  }
}

// function verifyImg(value: any) {
//   if (!value) {
//     return '缺失';
//   } else {
//     return undefined;
//   }
// }

// 校验商业险保费 交强险保费 车船税金额 其他代付金额
function verifyPremium(value: any) {
  if (value >= 0 && value <= 80000) {
    return undefined;
  } else {
    return '范围0-80000,最多保留两位小数';
  }
}
// function verifyVin(vin: string) {
//   if (vin) {
//     if (/^[A-HJ-NPR-Z1-9]{17}$/.test(vin)) {
//       return undefined;
//     } else {
//       return '只能由大写字母,数字,I/O/Q除外的17位字符组成';
//     }

//   } else {
//     return '缺失';
//   }
// }

export function verifyCarItem(item: IcarInfoItem) {
  const {
    assuredName, // 被投保人姓名
    commercialInsuranceEndDate, // 商业险结束日期
    commercialInsurancePeriod, // 商业险保单期限
    commercialInsurancePremium, // 商业险保费(元)
    commercialInsuranceStartDate, // 商业险开始日期
    compulsoryInsurancePremium, // 交强险保费(元)
    engineNumber, // 发动机号
    otherPaymentAmount, // 其他代付金额(元)
    plateNo, // 车牌号
    vehicleLicenseOwner, // 行驶证车主
    vehicleLicenseUrlList, // 车辆行驶证/登记证/合格证url
    insuranceSlipUrlList, // 投保材料
    vehicleTaxAmount, // 车船税金额(元)
    vin, // 车架号
  } = item;

  const startTime = dayjs(commercialInsuranceStartDate).valueOf();
  const endTime = dayjs(commercialInsuranceEndDate).valueOf();
  console.log('startTime14', startTime);
  console.log('endTime14', endTime);
  const error = {
    assuredName: !assuredName ? '缺失' : assuredName?.length < 2 ? '不能少于两个字' : undefined,
    commercialInsuranceEndDate: !commercialInsuranceEndDate
      ? '缺失'
      : startTime >= endTime || endTime - startTime > 367 * 24 * 60 * 60 * 1000
      ? '结束日期必须大于开始日期且结束日期不能大于开始日期367天'
      : undefined,
    commercialInsuranceStartDate: !commercialInsuranceStartDate
      ? '缺失'
      : startTime >= endTime || endTime - startTime > 367 * 24 * 60 * 60 * 1000
      ? '结束日期必须大于开始日期且结束日期不能大于开始日期367天'
      : undefined,
    commercialInsurancePremium: !isExist(commercialInsurancePremium)
      ? '缺失'
      : verifyPremium(commercialInsurancePremium),
    compulsoryInsurancePremium: !isExist(compulsoryInsurancePremium)
      ? '缺失'
      : verifyPremium(compulsoryInsurancePremium),
    vehicleTaxAmount: !isExist(vehicleTaxAmount) ? '缺失' : verifyPremium(vehicleTaxAmount),
    otherPaymentAmount: !isExist(compulsoryInsurancePremium)
      ? '缺失'
      : verifyPremium(otherPaymentAmount),
    vin: !vin
      ? '缺失'
      : /^[A-HJ-NPR-Z0-9]{17}$/.test(vin)
      ? undefined
      : '只能由大写字母,数字,I/O/Q除外的17位字符组成',
    engineNumber: !engineNumber
      ? '缺失'
      : /^[0-9a-zA-Z\u4e00-\u9fa5]+$/.test(engineNumber)
      ? undefined
      : '只能由数字,字母,汉字组成',
    commercialInsurancePeriod: !isExist(commercialInsurancePeriod) ? '缺失' : undefined,
    vehicleLicenseOwner: !vehicleLicenseOwner
      ? '缺失'
      : vehicleLicenseOwner?.length < 2
      ? '不能少于两个字'
      : undefined,
    plateNo: !plateNo
      ? '缺失'
      : /^[0-9a-zA-Z\u4e00-\u9fa5]+$/.test(plateNo)
      ? undefined
      : '只能由数字,字母,汉字组成',
    vehicleLicenseUrlList: !vehicleLicenseUrlList?.length ? '缺失' : undefined,
    insuranceSlipUrlList: !insuranceSlipUrlList?.length ? '缺失' : undefined,
  };
  const newError = filterProps(error);
  return Object.keys(newError)?.length ? newError : undefined;
}

export function verifyCarInfo(cars: IcarInfoItem[] = []): IcarInfoItem[] {
  const newCars = cars.map((item) => {
    const error = verifyCarItem(item);
    item.error = error;
    return item;
  });
  return newCars;
}

/**
 * 判断是否是一个http 或者 https 的url
 * return true 是 false 否
 */
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const { protocol } = urlObj;
    // 能走到这里说明是有效的url
    if (['http:', 'https:'].includes(protocol)) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
}

/**
 * 从一个完整的url中获取文件名称
 */
export function getFileNameFromUrl(url: string): string {
  if (isValidUrl(url)) {
    const urlObj = new URL(decodeURI(url));
    const { pathname } = urlObj;
    const pathnameArr = pathname?.split('/');
    return decodeURI(pathnameArr[pathnameArr.length - 1]);
  } else {
    return '';
  }
}

/**
 * 从一个完整的url中获取文件相对路径
 * 注意没有 开头的/
 */
export function getOssPathFromUrl(url: string): string {
  if (isValidUrl(url)) {
    const urlObj = new URL(decodeURI(url));
    const { pathname } = urlObj;
    return decodeURI(pathname).slice(1);
  } else {
    return '';
  }
}

/**
 *
 * @param num 需要多少个字符 不穿返回全部 ， 超出返回全部
 * @returns
 */
export function getRandomUUID(num?: number) {
  // any 会报ts错误 高版本ts 会支持
  return (crypto as any).randomUUID().replaceAll('-', '').slice(0, num);
}

/**
 * 从一个url中获取文件类型
 */
export function getFileTypeFromUrl(url: string): TfileType {
  const fileTypeMap = ['image/jpg', 'image/jpeg', 'image/png', 'image/webp', 'application/pdf'];
  const fileName = getFileNameFromUrl(url);
  const arr = fileName.split('.');
  const ext = arr[arr.length - 1];
  return fileTypeMap.find((item) => item.includes(ext)) as TfileType;
}

// 将 a_b_c 的字符串 转为驼峰命名
export function underlineToHump(str: string) {
  const arr = str.split('_');
  let newStr = '';
  arr.forEach((item, index) => {
    if (index !== 0) {
      newStr = newStr + item.slice(0, 1).toLocaleUpperCase() + item.slice(1).toLocaleLowerCase();
    } else {
      // 第一个首字母不需要转换
      newStr = newStr + item.toLocaleLowerCase();
    }
  });
  return newStr;
}

/**
 * 将 ["url1","url2"] 转成前端组件所需要的格式
 */
export function transformUrlTofileList(urlList: string[]): IcarUploadFileItem[] {
  return urlList.map((url) => {
    return {
      name: getFileNameFromUrl(url),
      type: getFileTypeFromUrl(url),
      url,
      uid: getRandomUUID(),
    };
  });
}

// 获取地图接口的域名
export function getHost() {
  let baseURL = 'https://map-api-pre.huolala.cn';
  const vanEnv = getVanEvn();
  if (vanEnv) {
    const urlEvn = vanEnv === 'prd' ? '' : `-${vanEnv}`;
    baseURL = `https://map-api${urlEvn}.huolala.cn`;
  }
  return baseURL;
}

/**
 * 根据数组中某一属性 去除重复对象 默认根据id去重
 * {id: 1},{id:2},{id:1} 返回 {id:1},{id:2}
 */
export function removeRepeat<T extends { id: string | number }>(arr: T[], attr = 'id'): T[] {
  return Object.values(
    arr.reduce((pre: any, cur: any) => {
      pre[cur?.[attr]] = cur;
      return pre;
    }, {}),
  );
}

/**
 *  根据获取 productList 的id 匹配获取 relation 中的全部信息
 */
export function getProductList(
  productList: IproductItem[] = [],
  allProductList: IproductItem[] = [],
) {
  const newProductList: IproductItem[] = [];
  productList?.forEach((company) => {
    const { id } = company;
    const one = allProductList?.find((item) => item.id === id);
    if (one) newProductList.push(one);
  });
  return newProductList;
}

/**
     获取渠道关联的企业和个人 产品 公司 企业的 select 选项
     企业和个人关联的 字段是不同的，独立开来

     当渠道发生变化 关联的选项重新生成
     当借款人类型发生变化 关联的选项重新生成
 */

export function getRelationOptions(params: {
  channelCode: string; // 渠道code
  borrowerType?: UloanUserType; // 渠道类型
  channelList: IcarChannelItem[]; // 渠道列表 包括渠道信息和渠道关联的信息，可能有些信息不全，比如关联的产品，所以需要从 relation 中根据id匹配获取
  relation?: TgetRelationList; // 所有的 产品 公司 企业 信息
  productCode?: string;
}): {
  productList: IproductItem[];
  companyList: IcompanyItem[];
  enterpriseList: IenterpriseItem[];
} {
  const { channelCode, borrowerType = '', channelList, relation, productCode } = params;
  const currentChannel = channelList.find((item) => item.channelCode === channelCode);
  const {
    productList = [],
    enterpriseList = [],
    companyList = [],
    personalCompanyList = [],
    personalProductList = [],
    productAndEnterpriseFlat = [],
  } = currentChannel || {};

  //  productList= [
  //     {id: "4294968309", productName: "李思琪测试", productCode: "03030417",enterpriseList:[{

  //     }]},
  //     {id: "4294968310", productName: "李思琪测试", productCode: "03030417",enterpriseList:[{

  //     }]}
  //   ]
  // 处理productAndEnterpriseFlat 格式
  const productListTemp = [];
  productAndEnterpriseFlat?.map(({ enterprise, product }) => {
    product?.forEach(({ costConfig, repayment, ...rest }) => {
      productListTemp.push({
        ...rest,
        costConfigs: JSON.parse(costConfig),
        repayment: JSON.parse(repayment),
        enterprise,
      });
    });
  });
  console.log(productListTemp);

  const productListMap = {
    // COMPANY: getProductList(productList, relation?.productList),
    COMPANY: productListTemp,
    PERSONAL: getProductList(personalProductList, relation?.productList),
  };
  const companyListMap: Record<string, any> = {
    COMPANY: companyList,
    PERSONAL: personalCompanyList,
  };
  return {
    productList: productListMap[borrowerType] || [],
    companyList: companyListMap[borrowerType] || [],
    enterpriseList:
      productListTemp?.find((item) => item?.productCode === productCode)?.enterprise || [],
  };
}

// 根据状态决定请求领取还是放弃，或者重新分派
export function receiveOrAbandonOrReassignActionRequest(
  status: EcarInsuranceStatus,
  params: {
    operatorUserId: string;
    orderNo: string;
  },
  action?: 'receive' | 'abandon' | 'reassign',
) {
  switch (status) {
    case EcarInsuranceStatus.TO_BE_COLLECTED:
      // 领取
      return receiveOrder(params);
    case EcarInsuranceStatus.PENDING:
      // 运营管理员才会有 重新分派
      if (action === 'reassign') {
        // 重新分派
        return reassignOrder(params);
      } else {
        // 放弃
        return abandonOrder(params);
      }
    default:
      return Promise.reject('状态错误');
  }
}
