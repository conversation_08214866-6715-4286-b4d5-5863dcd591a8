import { Button, message } from 'antd';
import React, { memo, useCallback, useState } from 'react';
import './index.less';
import { getWalletAccountInfo } from './services';

interface WalletActionsProps {
  actions: {
    showTransaction: () => void;
    showClaimRecharge: () => void;
    showClaimRecord: () => void;
  };
  externalOwnerId: string | undefined;
  secondProductCode: '0303' | '0201';
}

const WalletActions: React.FC<WalletActionsProps> = (props) => {
  const { actions, externalOwnerId, secondProductCode } = props;

  const [loading, setLoading] = useState<boolean>(false);
  const [beClick, setBeClick] = useState<boolean>(false);
  const [walletAccountInfo, setWalletAccountInfo] = useState<{ totalAmount: number } | null>(null);

  const takeWalletAccountInfo = useCallback(() => {
    if (!externalOwnerId) {
      message.error('专属充值账户不存在');
      return;
    }
    if (beClick) return;
    setBeClick(true);
    setLoading(true);
    getWalletAccountInfo({
      externalOwnerId,
      secondProductCode,
    })
      .then((res) => {
        console.log('res', res);
        setWalletAccountInfo(res?.data);
      })
      .catch(() => {
        setBeClick(false);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [externalOwnerId, beClick]);

  // 触发函数时的前置校验
  const coverValidateHandler = (callback: () => void) => {
    if (!externalOwnerId) {
      message.error('专属充值账户不存在');
      return;
    }
    callback();
  };

  return (
    <div className="account-container">
      <div className="account_info">
        {walletAccountInfo ? `¥${walletAccountInfo?.totalAmount}` : '******'}
      </div>

      <div className="channel-btn-ls">
        <Button
          type="link"
          loading={loading}
          className="take_amount"
          onClick={() => takeWalletAccountInfo?.()}
        >
          查看余额
        </Button>
        <span
          onClick={() => {
            coverValidateHandler(actions?.showTransaction);
          }}
        >
          流水明细
        </span>
        <span
          onClick={() => {
            coverValidateHandler(actions?.showClaimRecharge);
          }}
        >
          认领充值
        </span>
        <span
          onClick={() => {
            coverValidateHandler(actions?.showClaimRecord);
          }}
        >
          认领记录
        </span>
      </div>
    </div>
  );
};

export default memo(WalletActions);
