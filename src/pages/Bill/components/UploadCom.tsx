/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-29 10:42:17
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-07-29 10:42:22
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/UploadCom.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import { ProFormUploadButton } from '@ant-design/pro-form';
import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';

const UploadCom: React.FC<any> = (props) => {
  const base_url = getBaseUrl();
  const upload_url = `${base_url}/repayment/repay/uploadRepayPic`;
  return (
    <>
      <ProFormUploadButton
        labelCol={{ span: props.span || 5 }}
        extra="支持扩展名：.png .jpg .jpeg"
        label="上传付款凭证"
        name="fileList"
        fieldProps={{ headers: { ...getAuthHeaders() }, name: 'file' }}
        action={upload_url}
        accept=".png,.jpg,.jpeg"
        max={1}
        rules={[
          { required: true, message: '请上传付款凭证' },
          {
            validator: (rule, val, callBack) => {
              val?.map((item: any) => {
                // 校验是否超过10兆
                const isLt10M = item.size / 1024 / 1024 < 10;
                if (!isLt10M) {
                  callBack(`【${item.name}】文件大小超过10MB!`);
                }
                const suffix = item.name.substring(item.name.lastIndexOf('.') + 1);
                const suffixList = ['png', 'jpg', 'jpeg'];
                if (!suffixList.includes(suffix)) {
                  callBack('【此文件类型不在允许范围内】');
                }
                return item;
              });
              callBack();
            },
          },
        ]}
        title="选择文件"
      />
    </>
  );
};
export default UploadCom;
