import { forwardRef, useImperativeHandle, useRef } from 'react';
import { INCOME_STATUS } from '../../consts';
import AddSupplement from './AddSupplement';
import CallFail from './CallFail';
import Record from './Record';

type RiskRejectProps = {
  orderNo: string;
  riskOrderNo: string;
  incomeStatus: number;
  supplementReadOnly: boolean;
  callFailReadOnly: boolean;
  onSubmit: () => void;
};

const RiskReject = forwardRef((props: RiskRejectProps, ref) => {
  const { orderNo, riskOrderNo, incomeStatus, supplementReadOnly, callFailReadOnly } = props;

  const addSupplementRef = useRef<any>(null);
  const callFailRef = useRef<any>(null);

  const handleSubmit = () => {
    // 补件
    if (
      [INCOME_STATUS.ADDITIONAL_MATERIALS, INCOME_STATUS.ADDITIONAL_MATERIALS_AGAIN].includes(
        incomeStatus,
      )
    ) {
      addSupplementRef.current?.submit();
    }
    // 电联反馈
    if (incomeStatus === INCOME_STATUS.CALL_FAIL) {
      callFailRef.current?.submit();
    }
  };

  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submit: () => {
      handleSubmit();
    },
  }));

  return (
    <>
      <AddSupplement
        ref={addSupplementRef}
        orderNo={orderNo}
        riskOrderNo={riskOrderNo}
        isLook={supplementReadOnly}
        onSubmit={() => {
          props.onSubmit();
        }}
      />
      <CallFail
        ref={callFailRef}
        orderNo={orderNo}
        riskOrderNo={riskOrderNo}
        isLook={callFailReadOnly}
        onSubmit={() => {
          props.onSubmit();
        }}
      />
      <Record orderNo={orderNo} riskOrderNo={riskOrderNo} />
    </>
  );
});

export default RiskReject;
