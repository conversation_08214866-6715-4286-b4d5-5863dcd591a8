/*
 * @Author: your name
 * @Date: 2020-11-23 16:33:05
 * @LastEditTime: 2024-09-24 15:57:54
 * @LastEditors: elisa.z<PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/services/index.ts
 */
import { bizadminHeader } from '@/services/consts';
import { request } from '@umijs/max';
import type { AfterLoanListParams, IrepayExportAsyncParams } from '../types';
// 还款列表
export async function getAfterLoanList(params: AfterLoanListParams) {
  return request('/bizadmin/repayment/detail/list', {
    params,
    headers: {
      ...bizadminHeader,
    },
    ifTrimParams: true,
  });
}

// 放款信息
export async function getLoanInfo(orderNo: string) {
  return request(`/quota/lending/getLendingInfo/${orderNo}/1`);
}

// 导出
export async function afterLoanExcel(params: AfterLoanListParams) {
  return request(`/bizadmin/repayment/detail/list/export`, {
    responseType: 'blob',
    params,
    getResponse: true,
    headers: {
      ...bizadminHeader,
    },
  });
}

// 产品名称
export async function productList() {
  return request(`/repayment/query/productName`);
}

// 还款管理
export async function orderRepayList(data: any) {
  return request(`/bizadmin/repayment/cms/bill/order/repay/list`, {
    data,
    headers: {
      ...bizadminHeader,
    },
    method: 'POST',
    ifTrimParams: true,
  });
}

// 还款管理导出
export async function orderRepayExport(params: any) {
  return request(`/repayment/cms/bill/order/repay/export`, {
    responseType: 'blob',
    params,
    getResponse: true,
  });
}

//还款登记
export async function repayRegistList(data: any) {
  return request(`/bizadmin/repayment/cms/bill/order/repay/recording`, {
    data,
    headers: {
      ...bizadminHeader,
    },
    method: 'POST',
    ifTrimParams: true,
  });
}

//导出
export async function repayRegistExport(params: any) {
  return request(`/repayment/cms/bill/order/recording/export`, {
    responseType: 'blob',
    params,
    getResponse: true,
  });
}

//查询所有展业区域记录
export async function getStoreAndApplyCityAll() {
  return request(`/bizadmin/channel/lease/getStoreAndApplyCityAll`, {
    headers: bizadminHeader,
  });
}

// 还款管理导出-异步
export async function repayExportAsync(params: IrepayExportAsyncParams) {
  return request(`/bizadmin/repayment/order/repay/exportAsync`, {
    headers: bizadminHeader,
    data: params,
    method: 'POST',
    ifTrimParams: true,
  });
}

// 还款登记导出-异步
export async function recordingExportAsync(params: any) {
  return request(`/bizadmin/repayment/order/recording/exportAsync`, {
    headers: bizadminHeader,
    method: 'POST',
    data: params,
    ifTrimParams: true,
  });
}

//还款明细导出-异步
export async function repayDetailExportAsync(params: any) {
  return request(`/bizadmin/repayment/detail/list/submit/export`, {
    headers: bizadminHeader,
    method: 'POST',
    data: params,
    ifTrimParams: true,
  });
}
