.bulk-custom-btn {
  color: #3176fd;
  background-color: #cdf;

  &:hover {
    color: #3176fd !important;
    background-color: #e2ecfe !important;
  }
}

.transition-container-show {
  display: block;
  height: 100%;
}

.transition-container-hide {
  display: none;
}

/* 点击下一步时的动画 */
.next-step-hide {
  animation: slideLeftOut 0.3s ease-in-out forwards;
}

.next-step-show {
  animation: slideLeftIn 0.3s ease-in-out forwards;
}

/* 点击上一步时的动画 */
.prev-step-hide {
  animation: slideRightOut 0.3s ease-in-out forwards;
}

.prev-step-show {
  animation: slideRightIn 0.3s ease-in-out forwards;
}

/* 向左滑出 */
@keyframes slideLeftOut {
  0% {
    transform: translateX(0);
    opacity: 1;
  }

  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* 从右滑入 */
@keyframes slideLeftIn {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 向右滑出 */
@keyframes slideRightOut {
  0% {
    transform: translateX(0);
    opacity: 1;
  }

  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 从左滑入 */
@keyframes slideRightIn {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
