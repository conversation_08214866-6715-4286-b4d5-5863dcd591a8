/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 15:23:08
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-13 18:08:55
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/AddWithHoldModal.tsx
 * @Description: AddWithHoldModal
 */
import { LEASE_LOAN_CHANNEL, PRODUCT_CLASSIFICATION_CODE, REPURCHASE_STATUS } from '@/enums';
import globalStyle from '@/global.less';
import { fen2yuan, getUuid } from '@/utils/utils';
import {
  ModalForm,
  // ProFormUploadButton,
  ProFormDigit,
  ProFormSelect,
} from '@ant-design/pro-form';
import { Col, Form, message, Row, Table } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { WithHoldParams } from '../data';
import { calcSettleAmount, postWithHold } from '../service';

export type AddReliefModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  // values: CallPayForm|{};
  onVisibleChange: any;
  overDueAmount: number;
  // dataAll: any;
  overdueCaseNo: string;
  channelCode: string;
  repurchaseStatus: number; // 上海银行回购标识
  dataWithHoldInfo:
    | {
        userName: string;
        bankNo: string;
        phone: string;
        orderNo: string;
        productCode?: string;
        repayOverdueCostBO: any[];
      }
    | undefined;
  // orderNo: string;
};

const AddWithHoldModal: React.FC<AddReliefModalProps> = (props) => {
  // const { overdueCaseNo } = history.location.query;
  const { overdueCaseNo, dataWithHoldInfo, channelCode, repurchaseStatus } = props;
  const [form] = Form.useForm();
  const withholdMoney = useRef();
  // 资方渠道是上海银行，且没有被易人行回购
  const isShangHaiYinHang =
    channelCode === LEASE_LOAN_CHANNEL.SHANG_HAI_YING_HANG &&
    ![
      REPURCHASE_STATUS.BUY_CHANNEL,
      REPURCHASE_STATUS.BUY_CHANNEL_ING,
      REPURCHASE_STATUS.BUY_SELF,
      REPURCHASE_STATUS.BUY_SELF_ING,
    ].includes(repurchaseStatus);

  const [isRequesting, setIsRequesting] = useState(false);
  const [amountDisabled, setAmountDisabled] = useState(false);

  // type 1为总计行
  const [totalItem, setTotalItem] = useState({});
  const overdueList = useRef({});
  const settleList = useRef({});

  useEffect(() => {
    // initTotalItem();
    let dataInfo = {};
    // console.log(dataWithHoldInfo);
    const mapTitle: Record<number, string> = {
      99: 'overdueAll', // 逾期总额
      1: 'overduePrinciple', // 逾期本金
      2: 'overdueInterest', // 逾期利息
      3: 'overduePenalty', // 逾期罚息o
      4: 'lateFee', // 逾期滞纳金
    };
    dataWithHoldInfo?.repayOverdueCostBO
      ?.find((item) => {
        return item.total;
      })
      ?.costItemBOList?.forEach((sonTotalItem) => {
        if (sonTotalItem?.type === 3) {
          // console.log(sonTotalItem, 'son');
          dataInfo = {
            ...dataInfo,
            [mapTitle[sonTotalItem?.costType]]: sonTotalItem?.amount,
          };
        }
        // return
        // return sonTotalItem.total;
      });
    overdueList.current = dataInfo;
    setTotalItem(dataInfo);
    // form?.setFieldsValue({ withholdMoney: dataWithHoldInfo?.totalOverdueAmount });
  }, [dataWithHoldInfo]);

  useEffect(() => {
    // 上海银行资方协议支付金额不能修改
    if (isShangHaiYinHang) {
      setAmountDisabled(true);
    } else {
      setAmountDisabled(false);
    }

    if (props.modalVisible && Object.keys(overdueList.current).length) {
      setTotalItem(overdueList.current);
    }
  }, [props.modalVisible]);

  const columns = [
    {
      title: '剩余应还总额',
      dataIndex: 'overdueAll',
      key: 'overdueAll',
    },
    {
      title: '剩余应还本金',
      dataIndex: 'overduePrinciple',
      key: 'overduePrinciple',
    },
    {
      title: '剩余应还利息',
      dataIndex: 'overdueInterest',
      key: 'overdueInterest',
    },
    {
      title: '剩余应还罚息',
      dataIndex: 'overduePenalty',
      key: 'overduePenalty',
    },
    {
      title: '剩余应还滞纳金',
      dataIndex: 'lateFee',
      key: 'lateFee',
    },
    {
      title: '提前结清违约金',
      dataIndex: 'advanceSettleLiquidatedDamages',
      key: 'advanceSettleLiquidatedDamages',
    },
  ];

  return (
    <>
      <ModalForm
        title="发起协议支付-银行卡还款"
        width={800}
        layout="horizontal"
        open={props.modalVisible}
        onOpenChange={props.onVisibleChange}
        initialValues={{ withholdType: '1', withholdMoney: overdueList.current?.overdueAll }}
        form={form}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          afterClose: () => {
            form.resetFields();
          },
        }}
        onFinish={async (value) => {
          try {
            await postWithHold({
              overdueCaseNo,
              ...value,
              orderNo: dataWithHoldInfo?.orderNo,
            } as WithHoldParams);
            message.success('添加成功');
            props.onOk();
            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            <span>姓名:</span>
          </Col>
          <span className={globalStyle.ml10}>{dataWithHoldInfo?.userName}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            银行卡号:
          </Col>
          <span className={globalStyle.ml10}>{dataWithHoldInfo?.bankNo}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            银行预留手机号:
          </Col>
          <span className={globalStyle.ml10}>{dataWithHoldInfo?.phone}</span>
        </Row>
        <Row className={globalStyle.mb20}>
          <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
            订单号:
          </Col>
          <span className={globalStyle.ml10}>{dataWithHoldInfo?.orderNo}</span>
        </Row>
        <ProFormSelect
          label="还款场景"
          name="withholdType"
          labelCol={{ span: 5 }}
          width="md"
          fieldProps={{
            loading: isRequesting,
            onChange: async (value: string) => {
              if (value === '1' && !isShangHaiYinHang) {
                setAmountDisabled(false);
                setTotalItem(overdueList.current);
                form.setFieldsValue({ withholdMoney: overdueList.current?.overdueAll });
              } else if (value === '2' && !isRequesting.current) {
                //选择提前结清
                setAmountDisabled(true);
                const params = {
                  orderNo: dataWithHoldInfo.orderNo,
                  productCode: dataWithHoldInfo.productCode,
                };
                if (withholdMoney.current) {
                  setTotalItem(settleList.current);
                  form.setFieldsValue({ withholdMoney: withholdMoney.current });
                  return;
                }
                setIsRequesting(true);
                const res = await calcSettleAmount(params);
                setIsRequesting(false);
                const {
                  overdueInterest,
                  remainInterest,
                  remainPrincipal,
                  overdueLatePaymentFee,
                  remainTotal,
                  advanceSettleLiquidatedDamages,
                } = res.data || {};
                settleList.current = {
                  overdueAll: fen2yuan(remainTotal),
                  overduePrinciple: fen2yuan(remainPrincipal),
                  overdueInterest: fen2yuan(remainInterest),
                  overduePenalty: fen2yuan(overdueInterest),
                  lateFee: fen2yuan(overdueLatePaymentFee),
                  advanceSettleLiquidatedDamages: fen2yuan(advanceSettleLiquidatedDamages),
                };
                setTotalItem(settleList.current);
                if (remainTotal) {
                  withholdMoney.current = fen2yuan(remainTotal);
                  form.setFieldsValue({ withholdMoney: withholdMoney.current });
                }
              }
            },
          }}
          options={[
            { label: '逾期金额协议还款', value: '1' },
            {
              label: '提前结清',
              value: '2',
              disabled: isShangHaiYinHang,
            },
          ]}
          rules={[{ required: true }]}
        />
        {dataWithHoldInfo?.productCode?.substring(0, 2) !==
          PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING && (
          <Row className={globalStyle.mb20}>
            <Col span={5} className={`${globalStyle.textRight} ${globalStyle.pr8}`}>
              剩余应还记录:
            </Col>
            <Col span={17}>
              <Table
                dataSource={[totalItem]}
                columns={columns}
                key={getUuid()}
                pagination={false}
                scroll={{ x: 'max-content' }}
              />
            </Col>
          </Row>
        )}

        <ProFormDigit
          name="withholdMoney"
          width="md"
          labelCol={{ span: 5 }}
          label="还款金额(元)"
          placeholder="请输入还款金额"
          fieldProps={{ min: 0, precision: 2 }}
          disabled={amountDisabled}
          tooltip={
            <div>
              <p>①逾期金额协议还款时，仅支持协议支付已逾期应还金额。</p>
              <p>②提前结清时，协议支付金额=逾期期数逾期还款应还金额+剩余期数提前结清应还金额。</p>
            </div>
          }
          extra={
            isShangHaiYinHang && (
              <p>
                因协议支付到不同账户，系统提交的扣款金额包含尾款500，发起协议支付时，会自动减去500发起。剩余尾款500需人工再操作一次协议支付。
              </p>
            )
          }
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 有坑，同时出现很多个error,待优化
                if (val <= 0) {
                  return Promise.reject(new Error('还款金额必须大于0'));
                }
                if (/^\d+(\.\d+)?$/.test(val) && val && val > totalItem.overdueAll) {
                  // callBack();
                  return Promise.reject(new Error('协议支付金额不能大于当前剩余应还总额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < totalItem.overdueAll) {
                  // callBack('请输入数字');
                  return Promise.reject(new Error('请输入数字'));
                }
                return Promise.resolve();
              },
            },
          ]}
        />
      </ModalForm>
    </>
  );
};

export default AddWithHoldModal;
