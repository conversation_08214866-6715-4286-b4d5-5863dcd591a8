/*
 * @Author: your name
 * @Date: 2021-01-12 17:01:32
 * @LastEditTime: 2023-03-08 17:49:43
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/service.ts
 */

import { request } from '@umijs/max';
import type { CollectReliefParams } from './data';

// 获取催收减免
export async function getCollectReliefList(params?: CollectReliefParams) {
  return request('/repayment/cms/overdue/management/remission', {
    method: 'GET',
    params: { ...params },
    ifTrimParams: true,
  });
}

// 催收减免导出
export async function collectExport(params?: CollectReliefParams) {
  return request('/repayment/cms/overdue/management/remission/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}

// 获取催收减免
export async function getOffLineCollectReliefList(params?: CollectReliefParams) {
  return request('/repayment/offlineRemit/remission/list', {
    method: 'GET',
    params: { ...params },
    ifTrimParams: true,
  });
}

// 催收减免导出
export async function offLineCollectExport(params?: CollectReliefParams) {
  return request('/repayment/offlineRemit/remission/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}
