/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-04-19 10:02:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-04-22 17:08:31
 * @FilePath: /code/lala-finance-biz-web/src/pages/SystemOverview/service.ts
 * @Description: SystemOverview/service
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { StatisticParams } from './data';

export async function getDataStatistic(params: StatisticParams) {
  return request('/bizadmin/sysOverview/query/dataStatistic', {
    params,
    method: 'GET',
    headers: bizAdminHeader,
  });
}
