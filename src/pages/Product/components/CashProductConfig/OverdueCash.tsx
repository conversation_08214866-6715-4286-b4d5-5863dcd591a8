/*
 * @Date: 2024-01-27 15:34:06
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2024-01-27 16:03:25
 * @FilePath: /lala-finance-biz-web/src/pages/Product/components/CashProductConfig/OverdueCash.tsx
 * @Description:
 */
import { Col, Form, Input, Row, Select, TimePicker } from 'antd';
import React, { useEffect } from 'react';
// import {Link} from '@umijs/max'
import dayjs from 'dayjs';
// import {OverdueConfig} from '../../data';

type OverdueCashProps = {
  overDueData?: any;
  name?: string;
};

const OverdueCash: React.FC<OverdueCashProps> = ({ overDueData, name }) => {
  // const { overDueData, isLease } = props;
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue({
      ...overDueData,
      overdueTime: overDueData?.overdueTime ? dayjs(overDueData?.overdueTime, 'HH:mm:ss') : null,
    });
  }, [overDueData]);
  return (
    <>
      <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 14 }} name={name}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="逾期时间" name="overdueTime">
              <TimePicker style={{ minWidth: '80%' }} disabled placeholder="24:00:00" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="坏账">
              <span>T+</span>
              <Form.Item name="badDebtDay" noStyle>
                <Input placeholder="90" style={{ width: '70%', marginLeft: '10px' }} disabled />
              </Form.Item>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="逾期罚息最大值" name="overduePenaltyInterest">
              {/* <Input placeholder="不超过本金的50%" style={{ width: '80%' }} disabled />
               */}
              <Select disabled style={{ width: '80%' }} placeholder="无">
                <Select.Option value="NO_MORE_THAN_PRINCIPAL_FIFTY_PERCENTAGE">
                  不超过本金的50%
                </Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="催收单生成日期">
              <span>T+</span>
              <Form.Item name="overdueBillDay" noStyle>
                <Input placeholder="1" style={{ width: '70%', marginLeft: '10px' }} disabled />
              </Form.Item>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </>
  );
};

export default OverdueCash;
