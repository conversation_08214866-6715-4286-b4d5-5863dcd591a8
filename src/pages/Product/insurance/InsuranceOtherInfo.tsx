import globalStyle from '@/global.less';
import { useModel } from '@umijs/max';
import { Card, Form, Input } from 'antd';
import type { MutableRefObject } from 'react';
import React, { useEffect, useImperativeHandle } from 'react';

type OtherInfoProps = {
  otherInfoRef?: MutableRefObject<{ otherInfoSubmit: () => void } | undefined>;
};

const OtherInfo: React.FC<OtherInfoProps> = ({ otherInfoRef }) => {
  const { detailData } = useModel('Product.insuranceProduct');
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue({
      productDesc: detailData?.productDesc,
    });
  }, [detailData, form]);

  useImperativeHandle(otherInfoRef, () => ({
    otherInfoSubmit: () => {
      form.submit();
    },
  }));
  return (
    <Card title="其他信息" className={globalStyle.mt30}>
      <Form form={form} labelCol={{ span: 2 }} wrapperCol={{ span: 20 }} name={'otherInfo'}>
        <Form.Item label="产品介绍" name="productDesc" rules={[{ required: true }]}>
          <Input.TextArea placeholder="输入描述" rows={4} />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default React.memo(OtherInfo);
