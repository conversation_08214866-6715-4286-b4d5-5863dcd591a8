import { getVanEvn } from '@/utils/utils';
import { extend } from 'umi-request';

const envMap: any = {
  stg: 'https://bme-finance-worker--stable-1-workers-v-stg.huolala.work',
  pre: 'https://bme-finance-worker--stable-1-workers-v-pre.huolala.work',
  prd: 'https://bme-finance-worker-workers-v.huolala.work',
};
export const getWorkerApiHost = () => envMap[getVanEvn()];

export const workerRequest = extend({
  timeout: 5000,
  timeoutMessage: '网络请求超时，请稍后重试',
});
