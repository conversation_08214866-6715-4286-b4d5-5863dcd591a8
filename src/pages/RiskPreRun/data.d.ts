/*
 * @Author: your name
 * @Date: 2021-09-28 15:11:45
 * @LastEditTime: 2021-10-18 18:02:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /hll-finrisk-web/src/pages/RiskPreRun/data.d.ts
 */

export type RiskPreRunListItem = {
  createdAt?: string;
  createdBy?: string;
  finishTime?: string;
  flowNo?: string;
  id?: string;
  productSecondCode?: string;
  status?: number;
  taskName?: string;
  taskType?: number;
  updatedAt?: string;
  minAmount?: string;
  maxAmount?: string;
};

export type RiskListParams = RiskPreRunListItem & RiskPreRunPagination;

export type RiskDetailParams = RiskDetailListItem & RiskPreRunPagination;

export type RiskDetailListItem = {
  createdAt?: string;
  enterpriseName?: string;
  filePath?: string;
  filePathName?: string;
  flag?: number;
  flowNo?: string;
  id?: number;
  level?: string;
  orgCode?: string;
  productSecondCode?: string;
  result?: number;
  score?: number;
  status?: number;
  taskNo?: string;
  updatedAt?: string;
};

export type RiskPreRunPagination = {
  pageSize?: number;
  total?: number;
  current?: number;
};

export type SingleFormParam = {
  enterpriseCode?: string;
  enterpriseName?: string;
  productSecondCode?: string;
  taskName?: string;
  taskType?: string;
  timeCode?: string;
};

export type MultipleFormParam = {
  enterpriseCode?: string;
  enterpriseName?: string;
  productSecondCode?: string;
  taskName?: string;
  taskType?: string;
  file?: File;
  timeCode?: string;
};

export type DataBasicInfo = {
  filePath?: string;
  filePathName?: string;
  flag?: number;
  status?: number;
  data?: any[];
  taskName?: string;
};
