/*
 * @Author: your name
 * @Date: 2021-04-19 15:19:48
 * @LastEditTime: 2023-06-15 11:30:47
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/loan-cash-detail.tsx
 */
import { ShowInfo } from '@/components';
import HeaderTab from '@/components/HeaderTab';
import { queryOrderDetail, queryOrderExtend } from '@/pages/BusinessCashMng/service';
import { getLoanInfo, getRepayPlan } from '@/services/global';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { Card, Modal, Tabs } from 'antd';
import React, { useState } from 'react';
import { Contract } from '../BusinessCashMng/components';
import CardRecordTable from '../BusinessCashMng/components/CardRecordTable';
import IncomingInfo from '../BusinessCashMng/components/IncomingInfo';
import LoanCashInfo from './components/LoanCashInfo';
import { getAuditLogs } from './service';

const LoanDetail: React.FC<any> = () => {
  const { orderNo, lendingNo, productCode } = history.location.query as {
    orderNo: string;
    lendingNo: string;
    productCode: string;
  };
  const [cardRecordModalVisible, setCardRecordModalVisible] = useState<boolean>(false);
  // 进件详情
  const { data } = useRequest(() => {
    return queryOrderDetail(orderNo);
  });
  // 放款信息
  const { data: dataLending, run } = useRequest(() => {
    return getLoanInfo(orderNo);
  });
  // 获取还款计划
  const { data: repayPlanData } = useRequest(() => {
    return getRepayPlan(orderNo);
  });
  // 获取请款进度
  const { data: auditLogs, run: runAuditLogs } = useRequest(() => {
    return getAuditLogs(lendingNo);
  });
  // 刷新接口
  const refresh = () => {
    run();
    runAuditLogs();
  };
  // 银行卡信息配置
  const cardInfoMap = {
    bankName: '银行',
    bankCode: '银行卡号',
    phone: '银行预留手机号',
  };
  const { data: dataExtend } = useRequest(() => {
    return queryOrderExtend(orderNo);
  });
  const selfDefine = {
    orderNo: (
      <a
        onClick={() => {
          history.push(`/businessMng/cash-detail?orderNo=${data.orderNo}`);
        }}
      >
        {data?.orderNo}
      </a>
    ),
  };
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Card title="借款信息" style={{ marginTop: 20 }}>
          <Tabs>
            <Tabs.TabPane key="1" tab="基础信息">
              <IncomingInfo
                data={data}
                tableData={repayPlanData?.listRspList || []}
                selfDefine={selfDefine}
              />
            </Tabs.TabPane>
            <Tabs.TabPane key="2" tab="银行卡">
              {/* 银行卡 */}
              <a
                href="javascript:;"
                style={{ display: 'block', textAlign: 'right' }}
                onClick={() => setCardRecordModalVisible(true)}
              >
                银行卡日志
              </a>
              <ShowInfo noCard infoMap={cardInfoMap} data={dataExtend?.bankcardInfo} />
            </Tabs.TabPane>
            <Tabs.TabPane key="3" tab="合同">
              <Contract
                dataContract={dataExtend?.contractInfo}
                orderNo={orderNo}
                isXiaoYiSifang={data?.productName.indexOf('乐享借') > -1}
              />
            </Tabs.TabPane>
          </Tabs>
        </Card>
        {/* <IncomingInfo data={data} tableData={repayPlanData?.listRspList || []} /> */}
        <LoanCashInfo
          productCode={productCode}
          dataLending={dataLending}
          userNo={data?.userNo}
          auditLogs={auditLogs}
          lendingNo={lendingNo}
          orderNo={orderNo}
          refresh={refresh}
        />
        {/* 银行卡日志 */}
        <Modal
          title="银行卡日志"
          width="60%"
          forceRender
          open={cardRecordModalVisible}
          footer={null}
          onCancel={() => setCardRecordModalVisible(false)}
        >
          <CardRecordTable orderNo={orderNo} />
        </Modal>
      </PageContainer>
    </>
  );
};

export default LoanDetail;
