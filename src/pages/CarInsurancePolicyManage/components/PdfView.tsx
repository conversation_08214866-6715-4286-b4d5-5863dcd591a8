/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:49:45
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-26 09:49:45
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/components/PdfView.tsx
 * @Description: PdfView.tsx
 */

import React, { memo } from 'react';

const PdfView = (props: { url: string }) => {
  const { url } = props;

  const iframeRef = React.useRef<HTMLIFrameElement>(null);

  React.useEffect(() => {
    const handleResize = () => {
      if (iframeRef.current) {
        iframeRef.current.style.height = `${window.innerHeight}px`;
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // 初始化时设置高度

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <iframe
        ref={iframeRef}
        src={url}
        width="100%"
        height="100%"
        title="PDF预览"
        // sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
        referrerPolicy="no-referrer"
        loading="lazy"
        style={{ border: 'none', height: '100vh' }}
      />
    </div>
  );
};

export default memo(PdfView);
