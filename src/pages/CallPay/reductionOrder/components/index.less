.modal_tit{
    font-size: 24px;
}

.label_width{
  width: 70px;
  min-width: 70px;
}

.block_area{
    padding-left: 20px;
    padding-top: 10px;
  &.pt_0{
    padding-top: 0px;
  }
  &.pl_0{
    padding-left: 0px;
  }
  &.mb10{
    margin-bottom: 10px;
  }
}
.tit{
    font-size: 18px;
    margin-top: 40px;
    font-weight: bold;
    &.mr5{
        margin-right: 5px;
    }
}
.line{
    line-height: 40px;

    .repay_amount{
        color: red;
        display: inline-block;
        margin-left: 10px;
        font-size: 18px;
        margin-right: 10px;
        text-align: center;
    }
    .repay_label{
        display: inline-block;
        width: 110px;
        font-size: 18px;
        text-align: right;
    }
    &.mt10{
        margin-bottom: 10px;
    }
    &::after{
        content: "";
        display: block;
        height: 0.5px; /* 设置分割线的高度为0.5px */
        background: #eee; /* 分割线的颜色 */
        margin: 10px auto; /* 上下外边距，以及自动左右外边距来居中 */
    }
    &.no_border{
      margin-bottom: 10px;
    }
    &.no_border::after {
        content: none;
    }
}
.remission_sel{
  display: flex;
  align-items: baseline;
}

.settle_mon{
    color: red;
    margin: 5px;
}





