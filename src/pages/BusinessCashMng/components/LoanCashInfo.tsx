/*
 * @Author: your name
 * @Date: 2021-06-01 18:10:55
 * @LastEditTime: 2022-06-20 15:00:37
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/LoanInfo.tsx
 */

/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-05-17 10:21:41
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/after-loan-detail.tsx
 */
import React from 'react';
import { ShowInfo } from '@/components';

const LoanCashInfo: React.FC<any> = (props) => {
  const { loanData } = props;
  // 放款信息配置
  const loanInfoMap = {
    lendingNo: '放款流水号',
    type: '放款类型',
    amount: '放款金额',
    lendingMaster: '放款主体',
    receiptMaster: '收款主体',
    fundFlow: '是否发生资金流',
    lendingModel: '放款方式',
    lendingCycle: '放款周期',
    lendingTime: '放款时间',
    status: '放款状态',
    lendingMsg: '备注',
  };
  const itemLoanMap = {
    type: {
      1: '进件',
      2: '债转',
      3: '代偿',
    },
    fundFlow: {
      false: '否',
      true: '是',
    },
    status: {
      0: '放款失败',
      1: '待放款',
      2: '放款成功',
      '-1': '已取消',
      3: '待请款',
      10: '待一审',
      11: '待二审',
      12: '驳回',
    },
    lendingModel: {
      1: '线上',
      2: '线下',
    },
  };

  return (
    <>
      <ShowInfo title="放款信息" infoMap={loanInfoMap} itemMap={itemLoanMap} data={loanData} />
    </>
  );
};

export default LoanCashInfo;
