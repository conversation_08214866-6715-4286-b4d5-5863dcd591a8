/* eslint-disable react-hooks/exhaustive-deps */
import type { IproductItem } from '@/pages/CarInsuranceChannel/services';
import { useModel } from '@umijs/max';
import type { FormInstance } from 'antd';
import { Form, Select, Space } from 'antd';
import React, { memo, useEffect, useState } from 'react';
import type { IproductInfo, IrefData } from '../type';
import FeeConfig from './FeeConfig';

type Props = {
  productInfoForm: FormInstance;
  refData: IrefData;
  baseInfoForm: FormInstance;
  enterpriseInfoForm: FormInstance;
};
const ProductInfo: React.FC<Props> = (props) => {
  const { productInfoForm, refData, baseInfoForm, enterpriseInfoForm } = props;
  const { optionsData, isEditable, detailData, setRelationOptions } = useModel(
    'CarInsurance.carInsurance',
  );
  const { productList = [] } = optionsData;
  const [productIsChange, setProductIsChange] = useState(false);

  console.log('optionsData', optionsData);

  const formItemStyle = { width: 180 };
  const [currentProductData, setCurrentProductData] = useState({} as any);

  useEffect(() => {
    const { productInfo, orderNo } = detailData;
    if (orderNo) {
      productInfoForm.setFieldsValue(productInfo);
    }
  }, [productInfoForm, detailData]);

  useEffect(() => {
    const { productInfo, orderNo } = detailData;

    if (refData.channelIsOnChange) {
      /// 说明渠道 变化了
      setCurrentProductData({});
      refData.channelIsOnChange = false;
      return;
    }
    console.log('productList', productList);
    console.log('productCode', productInfo?.productCode);

    if (orderNo) {
      // 编辑的时候 渲染的产品配置
      setCurrentProductData(
        productList.find((item) => item.productCode === productInfo?.productCode) || {},
      );
    }
  }, [detailData?.productInfo, optionsData?.productList?.length, productInfoForm]);

  // 当产品更改的时候 productInfoForm 设置一些额外的需要提交给后端的数据
  function setExtraFormValue(productData: any) {
    const { productCode, costConfigs = [], repayment = {} } = productData;
    const { carInsuranceDefaultRepaymentDate: repaymentDate, repaymentTerms } = repayment || {};
    // 公共的 -- 利息数据
    const interestData: any =
      costConfigs?.find((item1: any) => item1.expenseItem === 'INTEREST') || {};

    const { proportionNumberValue: interestRate } = interestData;
    productInfoForm.setFieldsValue({
      productCode,
      interestRate,
      repaymentDate,
      term: repaymentTerms?.[0],
      updateFlag: true,
    });
  }

  console.log('currentProductData', currentProductData);

  // 是否含有某一个产品配置

  function isHasProductConfig(currentProductData: IproductItem, name: 'BAIL' | 'COMMISSION') {
    return currentProductData?.costConfigs?.some((item) => item.expenseItem === name);
  }

  return (
    <>
      <h3>产品信息</h3>
      <Form<IproductInfo>
        layout={'horizontal'}
        form={productInfoForm}
        initialValues={{
          repaymentMode: '线下还款',
          bait: '0',
          commission: '0',
        }}
        //   onValuesChange={onFormLayoutChange}
      >
        <div style={{ display: 'flex', gap: 20, flexWrap: 'wrap' }}>
          <Form.Item label="产品名称" name="productName" rules={[{ required: true }]}>
            <Select
              disabled={!isEditable}
              showSearch
              optionFilterProp="label"
              options={productList.map((item: any) => {
                const { productName, productCode } = item;
                return {
                  label: productName,
                  value: productName,
                  key: productCode,
                  item,
                };
              })}
              style={formItemStyle}
              onChange={(value: number, options: any) => {
                setCurrentProductData(options?.item);
                setProductIsChange(true);
                setExtraFormValue(options?.item);
                // enterpriseName;
                enterpriseInfoForm.resetFields();
                const { channelCode, borrowerType } = baseInfoForm?.getFieldsValue();

                setRelationOptions({ channelCode, borrowerType, productCode: options?.key });
              }}
            />
          </Form.Item>

          <FeeConfig
            productIsChange={productIsChange}
            currentProductData={currentProductData}
            productInfoForm={productInfoForm}
            label="首付"
            name="downPayment"
            expenseItem="LOW_DOWN_PAYMENT"
          />
          {isHasProductConfig(currentProductData, 'BAIL') && (
            <FeeConfig
              productIsChange={productIsChange}
              currentProductData={currentProductData}
              productInfoForm={productInfoForm}
              label="保证金"
              name="bait"
              expenseItem="BAIL"
            />
          )}

          {isHasProductConfig(currentProductData, 'COMMISSION') && (
            <FeeConfig
              productIsChange={productIsChange}
              currentProductData={currentProductData}
              productInfoForm={productInfoForm}
              label="手续费"
              name="commission"
              expenseItem="COMMISSION"
            />
          )}
        </div>

        <Space size={60} style={{ marginTop: 20 }} wrap>
          <Form.Item label="还款期限" name="term">
            {productInfoForm.getFieldValue('term') || '/'}
          </Form.Item>
          <Form.Item label="年利率" name="interestRate">
            {productInfoForm.getFieldValue('interestRate') || '/'}
          </Form.Item>

          <Form.Item label="还款方式" name="repaymentMode">
            {productInfoForm.getFieldValue('repaymentMode')}
          </Form.Item>
          <Form.Item label="还款日" name="repaymentDate">
            每月1-15日申请,次月
            {productInfoForm.getFieldValue('repaymentDate')?.[0] || ' / '}
            日还款,每月16-31日申请,次月
            {productInfoForm.getFieldValue('repaymentDate')?.[1] || ' / '}日还款
          </Form.Item>
        </Space>

        <div style={{ display: 'none' }}>
          <Form.Item label="产品code" name="productCode" />
          <Form.Item label="产品是否有变动标识" name="updateFlag" />
          <Form.Item label="首付类型" name="downPaymentType" />
          <Form.Item label="保证金类型" name="baitType" />
          <Form.Item label="手续费类型" name="commissionType" />
        </div>
      </Form>
    </>
  );
};
export default memo(ProductInfo);
