/* eslint-disable @typescript-eslint/no-unused-expressions */
/*
 * @Author: your name
 * @Date: 2021-03-20 15:03:17
 * @LastEditTime: 2023-07-17 14:25:58
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/RepayList.tsx
 */
import { ShowInfo } from '@/components';
import globalStyle from '@/global.less';
import {
  desensitizationBankAndIdCard,
  getBlob,
  isExternalNetwork,
  previewAS,
  saveAs,
} from '@/utils/utils';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Card, Modal } from 'antd';
import React, { useState } from 'react';
import type { RepayListItem } from '../data';
// import { getOssPath } from '@/services/global';
const repay_type = {
  repayChannel: {
    1: '普通账号汇款',
    3: '专属账号汇款',
    2: '溢缴款冲抵',
    4: '溢缴款提取',
    5: '人工后台还款',
  },
};
const RepayList: React.FC<any> = (props) => {
  const data = props.repayList || [];
  const [isModalVisible, setModalVisible] = useState(false);
  const [curRow, setCurRow] = useState<RepayListItem>({});

  const mapType = {
    1: {
      repayChannel: '还款方式',
      repayBillAmount: '汇款金额（元）',
      actualRepaymentAmount: '实际还款金额（元）',
      overpayments: '产生溢缴款（元）',
      repayTime: '还款时间',
      accountName: '付款户名',
      accountNumber: '付款账号',
      accountBank: '付款银行',
      certificateUrl: '付款凭证',
      bankSerialNo: '银行流水号',
      receiveBankNo: '到账银行流水号',
      receiveTime: '到账时间',
    },
    3: {
      repayChannel: '还款方式',
      repayBillAmount: '汇款金额（元）',
      actualRepaymentAmount: '实际还款金额（元）',
      overpayments: '产生溢缴款（元）',
      repayTime: '还款时间',
      accountName: '付款户名',
      accountNumber: '付款账号',
      accountBank: '付款银行',
      receiveBankNo: '到账银行流水号',
      receiveTime: '到账时间',
    },
    2: {
      repayChannel: '还款方式',
      actualRepaymentAmount: '冲抵汇款金额（元）',
      repayTime: '还款时间',
      receiveBankNo: '到账银行流水号',
      receiveTime: '到账时间',
    },
    5: {
      repayChannel: '还款方式',
      actualRepaymentAmount: '还款金额（元）',
      repayTime: '还款时间',
      createdAt: '提交时间',
      repayBillAmount: '实际还款金额（元）',
      overpayments: '产生溢缴款（元）',
      attachInfoList: '附件',
      remark: '备注',
      receiveBankNo: '到账银行流水号',
      receiveTime: '到账时间',
    },
  };
  const previewSome = (url: string, name: string) => {
    // getOssPath(url).then((res) => {
    getBlob(url, (blob: Blob) => {
      const exc = name.substring(name.lastIndexOf('.') + 1);
      if ('.jpg.jpeg.gif'.includes(exc)) {
        previewAS(blob, 'image/jpeg;chartset=UTF-8');
      } else if (exc === 'pdf') {
        previewAS(blob);
      } else {
        saveAs(blob, name);
      }
    });
    // });
  };
  //自定义Item ReactNode
  const selfDefine = {
    certificateUrl: isExternalNetwork() ? (
      '*********'
    ) : (
      <a download target="_blank" href={curRow?.certificateUrl} rel="noopener noreferrer">
        凭证
      </a>
    ),
    attachInfoList: (
      <div style={{ display: 'inline-block' }}>
        {curRow?.attachInfoList?.map((item: { netWorkPath: string; name: string }) => (
          <div>
            <a onClick={() => previewSome(item?.netWorkPath, item?.name)}>{item.name}</a>
          </div>
        )) || '-'}
      </div>
    ),
  };

  //自定义rowSpan
  const selfRowSpan = {
    attachInfoList: 24,
    remark: 24,
  };

  const columns: ProColumns<RepayListItem>[] = [
    {
      title: '还款方式',
      dataIndex: 'repayChannel',
      valueEnum: repay_type.repayChannel,
    },
    { title: '还款金额', dataIndex: 'actualRepaymentAmount', key: 'actualRepaymentAmount' },
    { title: '还款时间', dataIndex: 'repayTime', key: 'repayTime' },
    {
      title: '操作',
      render: (_, record) => {
        return (
          <a
            onClick={() => {
              if (record?.repayChannel !== 4) {
                if (isExternalNetwork()) {
                  record.accountNumber = desensitizationBankAndIdCard(record.accountNumber!);
                }

                setCurRow(record);
                setModalVisible(true);
              }
            }}
          >
            查看详情
          </a>
        );
      },
    },
  ];
  return (
    <>
      <Modal
        title="还款记录详情"
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
        ]}
        width={700}
        open={isModalVisible}
        onCancel={() => setModalVisible(false)}
      >
        <ShowInfo
          noCard
          rowSpan={12}
          data={curRow}
          infoMap={mapType[curRow?.repayChannel as number]}
          itemMap={repay_type}
          selfDefine={selfDefine}
          selfRowSpan={selfRowSpan}
        />
      </Modal>
      <Card title="还款记录" className={globalStyle.mt30}>
        <ProTable
          search={false}
          toolBarRender={false}
          columns={columns}
          dataSource={data}
          pagination={false}
          rowKey={(record) => {
            return (record?.bankSerialNo as string) + (record?.repayTime as string);
          }}
        />
      </Card>
    </>
  );
};

export default RepayList;
