import { Card, Checkbox, message, Space, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { gpsChannelConfig } from '../service';

type GpsConfigProps = {
  channelId: string;
  checkedValue?: string;
};

const options = [{ label: '中瑞平台', value: 'ZhongRui' }];

const GpsConfig = (props: GpsConfigProps) => {
  const { channelId, checkedValue } = props;
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState();

  useEffect(() => {
    if (checkedValue) {
      setValue([checkedValue]);
    }
  }, [checkedValue]);

  const onChange = async (checkedValues) => {
    setValue(checkedValues);
    const params = {
      channelIds: [channelId],
      gpsSupplier: checkedValues[0],
    };
    try {
      setLoading(true);
      await gpsChannelConfig(params);
      message.success('GPS供应商配置成功');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title="GPS供应商配置" style={{ marginTop: 20 }}>
      <Spin spinning={loading}>
        <Space>
          <span>支持GPS供应商:</span>
          <Checkbox.Group options={options} onChange={onChange} value={value} />
        </Space>
      </Spin>
    </Card>
  );
};

export default GpsConfig;
