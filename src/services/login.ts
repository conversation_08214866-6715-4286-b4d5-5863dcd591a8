/*
 * @Author: your name
 * @Date: 2021-04-19 19:17:56
 * @LastEditTime: 2022-12-09 16:32:28
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/services/login.ts
 */
import { toRemoveAuth } from '@/utils/auth';
import { isExternalNetwork } from '@/utils/utils';
import { request } from '@umijs/max';

export interface LoginParamsType {
  username: string;
  password: string;
  // mobile: string;
  // captcha: string;
  // type: string;
}
export interface NewLoginParamsType {
  passportNo: string;
  password: string;
  loginSource?: string;
}

export interface LoginParams {
  passportNo: string;
  password: string;
}

export async function fakeAccountLogin(params: LoginParamsType) {
  const headers: any = {
    authorization: '',
  };
  if (isExternalNetwork()) {
    headers.externalNetwork = 1;
  }
  return request('/auth/operator/login/login', {
    headers,
    method: 'POST',
    data: params,
  });
}

export async function getFakeCaptcha(mobile: string) {
  return request(`/api/login/captcha?mobile=${mobile}`);
}

// export async function outLogin() {
//   return request('/auth/operator/login/logout', {
//     method: 'POST',
//   });
// },
export async function outLogin() {
  return await toRemoveAuth();
}
//
// 账号密码登录
export const login = (data: LoginParams) => {
  const headers: any = {
    authorization: '',
    'hll-appid': 'bme-finance-bizadmin-svc',
  };
  if (isExternalNetwork()) {
    headers.externalNetwork = 1;
  }
  return request(`/bizadmin/auth/login`, {
    method: 'POST',
    data,
    headers,
  });
};
// 登出
export const logout = (data: any) => {
  return request(`/bizadmin/auth/logout`, {
    method: 'POST',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
};

// 获取公钥
export const getPublicKey = () => {
  return new Promise((resolve, reject) => {
    resolve({});
  });
  return request('/bizadmin/auth/publicKey', {
    method: 'GET',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
};
