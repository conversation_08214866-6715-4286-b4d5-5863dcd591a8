/*
 * @Author: your name
 * @Date: 2021-01-11 15:43:07
 * @LastEditTime: 2023-12-07 10:09:56
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/ProductData.tsx
 */
import { ShowInfo } from '@/components';
import { CommonImageUpload } from '@/components/ReleaseCom';
import globalStyle from '@/global.less';
import { convertUploadFileList, getUuid } from '@/utils/utils';
import { ModalForm } from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, useModel, useRequest } from '@umijs/max';
import { Button, Card, Col, Form, Input, message, Modal, Radio, Row, Tabs, Tag } from 'antd';
import type { ReactNode } from 'react';
import React, { useImperativeHandle, useRef, useState } from 'react';
import type { AuthorItem, ProductDataListItem } from '../data';
import { getAuthInfo, getProductData, modifyAccountStatus, modifyRepayData } from '../service';
import StatusModifyLogsTable from './StatusModifyLogsTable';

interface ProductDataProps {
  userNo: string;
  cRef?: any;
}

const ProductData: React.FC<ProductDataProps> = (props) => {
  const [currentRow, setCurrentRow] = useState<ProductDataListItem>();
  const [visibleAuthoried, setVisibleAuthoried] = useState<boolean>(false);
  const [updateRepayDayVisible, handleUpdateRepayDayVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const { userNo } = history.location.query;
  const [formSubmitLoading, setFormSubmitLoading] = useState<boolean>(false);
  const [dataAuthor, setDataAuthor] = useState<AuthorItem>();
  const [statusModifyForm] = Form.useForm();
  const [activeKey, setActiveKey] = useState<string>('1');
  const [statusModalVisible, handleModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const authorizerInfoMap = {
    authorizedName: '姓名',
    idType: '证件类型',
    authorizedIdCard: '证件号',
    authorizedPhone: '手机号',
    email: '邮箱',
  };

  const itemMap = {
    idType: {
      1: '身份证',
    },
  };

  const { data: otherData, loading: ProductDataLoading, run } = useRequest(() => {
    return getProductData(props.userNo);
  });

  useImperativeHandle(props?.cRef, () => ({
    reload: () => {
      run();
    },
  }));

  // 获取企业授权人信息
  const getAuth = (productSecondTypeCode: string | undefined) => {
    getAuthInfo({ productSecondTypeCode, enterpriseId: props.userNo }).then((res) => {
      setDataAuthor(res.data);
    });
  };

  const handleConfirm = (values: any) => {
    const params = {
      subAccountNo: currentRow?.subAccountNo,
      userNo: currentRow?.userNo,
      token: `${new Date().getTime()}${Math.floor(Math.random() * 1000)}`, // 模仿
      ...values,
    };
    setFormSubmitLoading(true);
    modifyAccountStatus(params)
      .then(() => {
        message.success('操作成功');
        handleModalVisible(false);
        run();
      })
      .finally(() => {
        setFormSubmitLoading(false);
      });
  };

  function statusRender(_: any, record: ProductDataListItem): ReactNode {
    if (record.status !== null) {
      return (
        <Tag color={record.status === 0 ? 'red' : 'green'}>
          {record.status === 0 ? '冻结' : '激活'}
        </Tag>
      );
    }
    return <>-</>;
  }

  const [allFileList, handleFileList] = useState({});
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };
  const columns: ProColumns<ProductDataListItem>[] = [
    {
      title: '产品一级分类',
      dataIndex: 'productFirstTypeName',
    },
    {
      title: '产品二级分类',
      dataIndex: 'productSecondTypeName',
    },
    {
      title: '授权人',
      dataIndex: 'authorizedName',
      render: (_, { authorizedName, productSecondTypeCode }) => {
        return (
          <a
            onClick={() => {
              setVisibleAuthoried(true);
              getAuth(productSecondTypeCode);
            }}
          >
            {authorizedName}
          </a>
        );
      },
    },
    {
      title: '评级',
      dataIndex: 'riskLevel',
    },
    {
      title: '授信额度',
      dataIndex: 'creditAmount',
    },
    {
      title: '已用额度',
      dataIndex: 'usedAmount',
    },
    {
      title: '冻结额度',
      dataIndex: 'freezeAmount',
    },
    {
      title: '剩余可用额度',
      dataIndex: 'currAmount',
    },
    {
      title: '逾期金额',
      dataIndex: 'overdueAmount',
    },
    {
      title: '溢缴款',
      dataIndex: 'overpaymentsAmount',
    },
    {
      title: '状态',
      dataIndex: 'status',
      // valueType: 'select',
      valueEnum: {
        1: '激活',
        0: '冻结',
        '-1': '注销',
      },
      // key: 'status',
      render: statusRender,
    },
    {
      title: '还款日',
      dataIndex: 'billRepayDate',
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      width: 160,
      fixed: 'right',
      render: (_: any, record: ProductDataListItem) => (
        <>
          <Button
            type="link"
            style={{ padding: 0 }}
            onClick={() => {
              setCurrentRow(record);
              setActiveKey('1');
              handleModalVisible(true);
              statusModifyForm.setFieldsValue({
                operationType: record.status ? 0 : 1,
                productCode: record.productCode,
                productSecondTypeName: record.productSecondTypeName,
                remark: '',
                enclosure: [],
              });
            }}
          >
            状态变更
          </Button>
          <Button
            type="link"
            style={{ padding: 0, marginLeft: 5 }}
            onClick={() => {
              setCurrentRow(record);
              handleUpdateRepayDayVisible(true);
              form?.setFieldsValue({ repayDate: record?.billRepayDate });
            }}
          >
            变更还款日
          </Button>
        </>
      ),
    },
  ];

  // const { data: otherData, loading: ProductDataLoading, run } = useRequest(() => {
  //   return getProductData(props.userNo);
  // });
  // useImperativeHandle(props?.cRef, () => ({
  //   reload: () => {
  //     run();
  //   },
  // }));

  // const handleConfirm = () => {
  //   const params = {
  //     operationType: currentRow?.status ? 0 : 1,
  //     subAccountNo: currentRow?.subAccountNo,
  //     userNo: currentRow?.userNo,
  //     token: `${new Date().getTime()}${Math.floor(Math.random() * 1000)}`, // 模仿
  //     productCode: currentRow?.productCode,
  //   };
  //   modifyAccountStatus(params).then(() => {
  //     message.success('操作成功');
  //     run();
  //     return true;
  //   });
  //   return false;
  // };

  const confirm = (repayDate: number) => {
    Modal.confirm({
      // title: '提示',
      // icon: <ExclamationCircleOutlined />,
      content: (
        <>
          确认将【{currentRow?.userName}】还款日变更为
          <span style={{ color: 'red' }}>D+{repayDate}</span>
          吗？变更后将在本月账单开始生效
        </>
      ),
      centered: true,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const success = await modifyRepayData({
          operationToken: getUuid(),
          productSecondTypeCode: currentRow?.productSecondTypeCode,
          repayDate,
          userNo,
          updateAt: currentUser?.accountName,
          oldRepayDate: currentRow?.billRepayDate,
        });
        if (success) {
          // actionRef?.current?.reload();
          run();
          message.success('变更成功');
        }
      },
    });
  };

  return (
    <Card title="产品数据" bordered loading={ProductDataLoading} style={{ marginTop: 20 }}>
      <Row>
        <Col span={24} key="repay">
          <div style={{ lineHeight: '40px' }}>
            <ProTable<ProductDataListItem>
              actionRef={actionRef}
              rowKey="subAccountNo"
              scroll={{ x: 'max-content' }}
              // dataSource={[{ cost: 500, amountDue: 2 }]}
              dataSource={otherData}
              columns={columns}
              toolBarRender={false}
              search={false}
              pagination={false}
            />
          </div>
        </Col>
      </Row>
      <Modal
        title="状态变更"
        width="70%"
        forceRender
        open={statusModalVisible}
        footer={null}
        // onOk={() => handleModalVisible(false)}
        // onCancel={() => handleModalVisible(false)}
        onCancel={() => {
          handleModalVisible(false);
          handleFileList({});
        }}
        // afterClose={() => {
        //   handleFileList({});
        // }}
        destroyOnClose={true}
        maskClosable={false}
      >
        <Tabs
          type="card"
          activeKey={activeKey}
          onTabClick={(key) => {
            setActiveKey(key);
          }}
        >
          <Tabs.TabPane tab="变更" key="1">
            <Form
              form={statusModifyForm}
              labelCol={{ span: 2 }}
              wrapperCol={{ span: 16 }}
              onFinish={async (values) => {
                try {
                  // 转换数据为后端需要的格式，
                  const mapUploadFile = convertUploadFileList(allFileList, ['enclosure']);
                  // console.log(mapUploadFile);
                  // return;
                  await handleConfirm({
                    ...values,
                    ...mapUploadFile,
                  });
                  // eslint-disable-next-line no-empty
                } catch (error) {
                  message.success('稍后重试');
                }
                handleFileList({});
                return true;
              }}
              autoComplete="off"
            >
              <Form.Item label="产品ID" name="productCode" required>
                <Input disabled />
              </Form.Item>

              <Form.Item label="产品名称" name="productSecondTypeName" required>
                <Input disabled />
              </Form.Item>
              <CommonImageUpload
                extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx"
                label="附件"
                name="enclosure"
                max={5}
                listType="text"
                size={10}
                fileListEdit={[]}
                mapFileList={mapFileList}
                accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
              />
              <Form.Item label="状态" name="operationType" required>
                <Radio.Group>
                  <Radio value={0} disabled={currentRow?.status === 0}>
                    冻结
                  </Radio>
                  <Radio value={1} disabled={currentRow?.status === 1}>
                    激活
                  </Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item label="备注" name="remark">
                <Input.TextArea maxLength={300} showCount rows={4} />
              </Form.Item>

              <Form.Item wrapperCol={{ offset: 2, span: 16 }}>
                <Button type="primary" htmlType="submit" loading={formSubmitLoading}>
                  提交
                </Button>
              </Form.Item>
            </Form>
          </Tabs.TabPane>
          <Tabs.TabPane tab="变更日志" key="2">
            {currentRow?.subAccountNo && (
              <StatusModifyLogsTable userNo={props.userNo} subAccountNo={currentRow.subAccountNo} />
            )}
          </Tabs.TabPane>
        </Tabs>
      </Modal>
      <Modal
        title="授权人"
        width="550px"
        centered
        destroyOnClose
        open={visibleAuthoried}
        onCancel={() => setVisibleAuthoried(false)}
        footer={[
          <Button key="back" onClick={() => setVisibleAuthoried(false)}>
            取消
          </Button>,
        ]}
      >
        <ShowInfo
          infoMap={authorizerInfoMap}
          data={dataAuthor}
          noCard
          itemMap={itemMap}
          rowSpan={12}
          titleGap={{ marginLeft: 5 }}
        />
      </Modal>
      <ModalForm
        title="变更还款日"
        modalProps={{
          centered: true,
        }}
        form={form}
        // initialValues={{ repayDate: currentRow?.lastConfirmDate }}
        width="500px"
        layout="horizontal"
        className={globalStyle.formModal}
        visible={updateRepayDayVisible}
        onVisibleChange={handleUpdateRepayDayVisible}
        onFinish={async (values) => {
          // console.log(values);
          confirm(values?.repayDate);
          handleUpdateRepayDayVisible(false);
        }}
      >
        <Form.Item
          label="还款日"
          name="repayDate"
          rules={[
            { required: true },
            { pattern: /^[0-9]*[1-9][0-9]*$/, message: '输入正整数' },
            {
              validator: (_, value) => {
                if (currentRow && value && value <= currentRow?.lastConfirmDate) {
                  return Promise.reject(new Error('还款日需大于最晚确认账单日'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input style={{ width: '80%' }} addonBefore="D+" min={1} type="number" />
        </Form.Item>
        {/* <ProFormDigit label="还款日"/> */}
      </ModalForm>
    </Card>
  );
};

export default React.memo(ProductData);
