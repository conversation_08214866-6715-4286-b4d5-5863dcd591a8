import globalStyle from '@/global.less';
import { history } from '@umijs/max';
import { Badge, Card, message, Modal, Switch, Table } from 'antd';
import React, { useEffect } from 'react';
import { getLeaseChannelAccessList, updateLeaseChannelAccess } from '../service';

const STATUS_LABEL = {
  1: '启用',
  0: '禁用',
};

const AccessInfo: React.FC<{}> = () => {
  const channelId = history?.location?.query?.id as string;
  //

  // 获取角色权限列表
  const [roleListData, setRoleListData] = React.useState([]);
  const [roleListLoading, setRoleListLoading] = React.useState(false);
  const getRoleList = async (channelId: any) => {
    setRoleListLoading(true);
    const res = await getLeaseChannelAccessList(channelId).catch(() => {});
    console.log('getLeaseChannelAccessList', res);
    if (res && res?.data) {
      setRoleListData(res?.data);
    }
    setRoleListLoading(false);
  };

  // 处理角色权限开关action
  const [switchActioLoading, setSwitchActioLoading] = React.useState(false);
  const handleSwitchAction = async (id: string, roleCode: string, curentStatus: '1' | '0') => {
    setSwitchActioLoading(true);
    const res = await updateLeaseChannelAccess({
      channelCode: channelId,
      roleCode,
      status: curentStatus === '1' ? '0' : '1',
    }).catch(() => {});
    console.log('updateLeaseChannelAccess', res);

    if (res && [0, 200].includes(res?.ret)) {
      message.success('操作成功');
      await getRoleList(channelId).catch(() => {});
    }
    setSwitchActioLoading(false);
  };

  // 列
  const columns = [
    {
      title: '角色权限',
      key: 'roleName',
      dataIndex: 'roleName',
      render: (value) => {
        return value || '--';
      },
    },
    {
      title: '一级菜单',
      key: 'level1PrivilegeName',
      dataIndex: 'level1PrivilegeName',
      render: (value) => {
        return value || '--';
      },
    },
    {
      title: '二级菜单',
      key: 'level2PrivilegeName',
      dataIndex: 'level2PrivilegeName',
      render: (value) => {
        return value || '--';
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      render: (status: any) => {
        if (status === '1') {
          return <Badge status="success" text="启用" />;
        }
        return <Badge status="error" text="禁用" />;
      },
    },
    {
      title: '操作',
      key: 'option',
      // dataIndex: '',
      render: (_, row) => {
        // console.log(row);
        return (
          <>
            <Switch
              checkedChildren="禁用"
              unCheckedChildren="启用"
              loading={switchActioLoading}
              checked={row.status === '1' || row.status === 1}
              onChange={() => {
                const curentStatus = row.status === '1' ? '0' : '1';
                //
                Modal.confirm({
                  title: '功能权限设置',
                  centered: true,
                  okText: '确定',
                  cancelText: '取消',
                  onOk: () => {
                    handleSwitchAction(channelId, row?.roleCode, row.status).catch(() => {
                      setSwitchActioLoading(false);
                    });
                  },
                  content: (
                    <div style={{ marginTop: 15 }}>
                      {/* 你确定要禁用[{row.accountName}]的账号吗？ */}
                      {`你确定要${curentStatus === '1' ? '启用' : '禁用'}: ${
                        row?.roleName
                      }权限吗？`}
                    </div>
                  ),
                });
              }}
            />
          </>
        );
      },
    },
  ];
  //
  useEffect(() => {
    console.log(channelId);
    getRoleList(channelId);
  }, [channelId]);
  //
  return (
    <Card title="功能权限设置" extra={<></>} className={globalStyle.mt20}>
      <Table
        columns={columns}
        rowKey="roleCode"
        dataSource={roleListData}
        loading={roleListLoading}
        pagination={false}
      />
    </Card>
  );
};

export default AccessInfo;
