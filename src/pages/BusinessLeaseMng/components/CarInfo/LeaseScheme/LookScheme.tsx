// 查看金融方案
import { ProCard } from '@ant-design/pro-components';
import { ProFormText } from '@ant-design/pro-form';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';
import { queryOrderFinancePlan } from '../../../service';

const LookScheme = ({ orderNo }: { orderNo: string }) => {
  const [form] = Form.useForm();
  const [paymentInfo, setPaymentInfo] = useState({});

  useEffect(() => {
    if (!orderNo) return;
    queryOrderFinancePlan(orderNo).then((res: any) => {
      const { data } = res || {};
      const {
        dealPrice,
        numberOfLeasePeriods,
        downPaymentProportion,
        downPaymentPrice,
        rentPrice,
        financeAmount,
        firstPayAmount,
        ensureAmount,
        serviceFee,
        planTotalPrice,
      } = data || {};
      form.setFieldsValue({
        dealPrice: `¥${dealPrice}`,
        downPaymentProportion: `${(downPaymentProportion * 100).toFixed(0)}%(¥
          ${downPaymentPrice})`,
        downPaymentPrice: `¥${downPaymentPrice}`,
        numberOfLeasePeriods,
        rentPrice: `¥${rentPrice}`,
        financeAmount: `¥${financeAmount}`,
        firstPayAmount: `¥${firstPayAmount}`, // 首付金额
        ensureAmount: `¥${ensureAmount}`, //保证金
        serviceFee: `¥${serviceFee}`, // 手续费
        planTotalPrice: `¥${planTotalPrice}`, //小计
      });
      setPaymentInfo(data);
    });
  }, [orderNo]);
  return (
    <ProCard title="租赁计划" bordered headerBordered collapsible>
      <Form form={form} style={{ width: 800 }} wrapperCol={{ span: 12 }} labelCol={{ span: 3 }}>
        <h4>付款计划</h4>
        <ProFormText label="成交价" name="dealPrice" readonly />
        {paymentInfo.downPaymentConfig?.calculationEnum === 'PROPORTIONALLY' && (
          <ProFormText label="首付比例" name="downPaymentProportion" readonly />
        )}
        {paymentInfo.downPaymentConfig?.calculationEnum === 'AMOUNT_PROPORTION' && (
          <ProFormText label="首付金额" name="downPaymentPrice" readonly />
        )}
        <ProFormText label="分期期数" name="numberOfLeasePeriods" readonly />
        <ProFormText label="每月租金" name="rentPrice" readonly />
        <ProFormText label="融资额" name="financeAmount" readonly />
        {paymentInfo.prePayCostItem && (
          <>
            <h4>首次费用小计</h4>
            {paymentInfo.prePayCostItem?.indexOf('LOW_DOWN_PAYMENT') > -1 && (
              <ProFormText label="首付款" name="firstPayAmount" readonly />
            )}
            {paymentInfo.prePayCostItem?.indexOf('BAIL') > -1 && (
              <ProFormText label="保证金" name="ensureAmount" readonly />
            )}
            {paymentInfo.prePayCostItem?.indexOf('COMMISSION') > -1 && (
              <ProFormText label="手续费" name="serviceFee" readonly />
            )}
            <ProFormText label="共计" name="planTotalPrice" readonly />
          </>
        )}
      </Form>
    </ProCard>
  );
};

export default LookScheme;
