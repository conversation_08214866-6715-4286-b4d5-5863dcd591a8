/*
 * @Author: your name
 * @Date: 2021-04-13 11:20:03
 * @LastEditTime: 2021-04-21 16:15:45
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/ModelLibrary/sevice.ts
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from 'umi';
import type { AddPromotion, PromotionListParams } from './data';

// 营销方案新增
export async function addPromotion(data: AddPromotion) {
  return request(`/bizadmin/lease/car/promotion/add`, {
    data,
    method: 'POST',
    headers: bizAdminHeader,
  });
}

// 营销方案删除
export async function deletePromotion(id?: number) {
  return request(`/bizadmin/lease/car/promotion/delete`, {
    params: { id },
    headers: bizAdminHeader,
  });
}

// 后台查询车辆营销方案列表
export async function queryPromotion(params: PromotionListParams) {
  return request(`/bizadmin/lease/car/promotion/list`, {
    params,
    headers: bizAdminHeader,
    ifTrimParams: true,
  });
}
