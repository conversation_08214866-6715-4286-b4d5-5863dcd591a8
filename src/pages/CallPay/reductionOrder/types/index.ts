export interface IofflineRepayReviewItem {
  id: number;
  businessNo: string;
  orderNo: string;
  orderNoList: string[];
  billNo: string;
  billNoList: string[];
  applyPerson: string;
  applyName: string;
  secondProductCode: string;
  productCode: string;
  productName: string;
  repayAmount: string;
  exemptionAmount: string;
  repayAmountDetail: string;
  repayDate: string;
  repayDateStart: string;
  repayDateEnd: string;
  repayMode: number;
  repayModeName: string;
  remitType: number;
  repaySerialNo: string;
  channelName: string;
  channelTypeName: string;
  channelId: string;
  accountName: string;
  accountNameList: string[];
  accountNumber: string;
  customerType: number;
  customerTypeName: string;
  remark: string;
  attach: string;
  attachList: { url: string; name: string; uid: string; filePath: string }[];
  creditDate: string;
  creditDateStart: string;
  creditDateEnd: string;
  repayStatus: number;
  repayStatusName: string;
  updatedAt: string;
  createdAt: string;
  status: number;
  statusName: string;
  reason: string;
  approvalLog: string;
  approvalId: string;
  approvalPerson: string;
  approvalTime: string;
  subjectUniqueNo: string;
  subjectUniqueNoList: string[];
  overdueCaseNo: string;
  overdueAmount: string;
  orderAmount: string;
  compensation: number;
  compensationName: string;
  bankName: string;
  bankNo: string;
  bankAccount: string;
  subBranchBank: string;
  smallId: number;
  maxId: number;
  billRspDTOList: BillRspDTOItem[];
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalBreach: string;
  totalLate: string;
}
export interface BillRspDTOItem {
  current: number;
  pageSize: number;
  total: number;
  data: any[];
  id: number;
  billNo: string;
  secondaryClassification: string;
  startTime: string;
  endTime: string;
  billingDate: string;
  totalAmount: string;
  amountPaid: string;
  freezeAmount: string;
  clearTime: string;
  status: number;
  statusName: string;
  subjectMatterNo: string;
  refNo: string;
  refType: number;
  billType: number;
  ownerId: string;
  ownerRole: number;
  scene: string;
  payee: string;
  payeeRole: number;
  lender: string;
  lenderRole: number;
  billCostNo: string[];
  billCost: BillCost[];
  dimension: number;
  extendInfo: ExtendInfo;
  termNumber: string;
  carCount: number;
  accountName: string;
  customerType: number;
  customerTypeName: string;
  channelName: string;
  channelType: number;
  channelTypeName: string;
  orderNo: string;
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalAmountDue: string;
  totalPrincipalDue: string;
  totalInterestDue: string;
  totalOverduePenaltyDue: string;
  totalAmountPaid: string;
  totalPrincipalPaid: string;
  totalInterestPaid: string;
  totalOverduePenaltyPaid: string;
  totalBreach: string;
  totalBreachDue: string;
  totalBreachPaid: string;
  totalLate: string;
  totalLateDue: string;
  totalLatePaid: string;
  totalAmountExemption: string;
  totalPrincipalExemption: string;
  totalInterestExemption: string;
  totalOverduePenaltyExemption: string;
  totalBreachExemption: string;
  totalLateExemption: string;
  productCode: string;
  productName: string;
  productCodeLevelOne: string;
  productCodeLevelTwo: string;
  updateAt: string;
  lendingTime: string;
  dueDate: string;
  overdueDateNumber: number;
}

export interface BillCost {
  costType: number;
  amountDue: string;
  amountPaid: string;
  freezeAmount: string;
  paidDetail: PaidDetail[];
}

export interface PaidDetail {
  paymentMode: string;
  amount: string;
}

export interface ExtendInfo {
  count: number;
}

export interface IapplyOrderInfo {
  remissionList?: [];
  bankAmountDate?: string;
  bankAmount?: number;
  current: number;
  pageSize: number;
  id: number;
  businessNo: string;
  orderNo: string;
  orderNoList: string[];
  billNo: string;
  billNoList: string[];
  applyPerson: string;
  applyName: string;
  secondProductCode: string;
  productCode: string;
  productName: string;
  repayAmount: string;
  exemptionAmount: string;
  repayAmountDetail: string;
  repayDate: string;
  repayDateStart: string;
  repayDateEnd: string;
  repayMode: number;
  repayModeName: string;
  remitType: number;
  repaySerialNo: string;
  channelName: string;
  channelTypeName: string;
  channelId: string;
  accountName: string;
  accountNameList: string[];
  accountNumber: string;
  customerType: number;
  customerTypeName: string;
  remark: string;
  attach: { name: string; url: string; uid: string; filePath: string }[];
  attachList: { name: string; url: string; uid: string; filePath: string }[];
  creditDate: string;
  creditDateStart: string;
  creditDateEnd: string;
  repayStatus: number;
  repayStatusName: string;
  updatedAt: string;
  createdAt: string;
  status: number;
  statusName: string;
  reason: string;
  approvalLog: string;
  approvalId: string;
  approvalPerson: string;
  approvalTime: string;
  subjectUniqueNo: string;
  subjectUniqueNoList: string[];
  overdueCaseNo: string;
  overdueAmount: string;
  orderAmount: string;
  compensation: number;
  compensationName: string;
  bankName: string;
  bankNo: string;
  bankAccount: string;
  subBranchBank: string;
  smallId: number;
  maxId: number;
  billRspDTOList: IbillRspDTOItem[];
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalBreach: string;
  totalLate: string;
  planRepayAmount?: string; //  应还款金额
  remissionApprovalReason: string; //  减免单驳回原因
  remissionApprovalStatus: number; //  减免单审批状态
}

export interface IbillRspDTOItem {
  expectRepayAmount?: string;
  current: number;
  pageSize: number;
  total: number;
  data: any[];
  id: number;
  billNo: string;
  secondaryClassification: string;
  startTime: string;
  endTime: string;
  billingDate: string;
  totalAmount: string;
  amountPaid: string;
  freezeAmount: string;
  clearTime: string;
  status: number;
  statusName: string;
  subjectMatterNo: string;
  subjectMatterNoList: string[];
  refNo: string;
  refType: number;
  billType: number;
  ownerId: string;
  ownerRole: number;
  scene: string;
  payee: string;
  payeeRole: number;
  lender: string;
  lenderRole: number;
  billCostNo: string[];
  billCost: IbillCost[];
  dimension: number;
  extendInfo: ExtendInfo;
  termNumber: string;
  carCount: number;
  accountName: string;
  customerType: number;
  customerTypeName: string;
  channelName: string;
  channelType: number;
  channelTypeName: string;
  orderNo: string;
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalAmountDue: string;
  totalPrincipalDue: string;
  totalInterestDue: string;
  totalOverduePenaltyDue: string;
  totalAmountPaid: string;
  totalPrincipalPaid: string;
  totalInterestPaid: string;
  totalOverduePenaltyPaid: string;
  totalBreach: string;
  totalBreachDue: string;
  totalBreachPaid: string;
  totalLate: string;
  totalLateDue: string;
  totalLatePaid: string;
  totalAmountExemption: string;
  totalPrincipalExemption: string;
  totalInterestExemption: string;
  totalOverduePenaltyExemption: string;
  totalBreachExemption: string;
  totalLateExemption: string;
  productCode: string;
  productName: string;
  productCodeLevelOne: string;
  productCodeLevelTwo: string;
  updateAt: string;
  lendingTime: string;
  dueDate: string;
  overdueDateNumber: number;
}

export interface IbillCost {
  costType: number;
  amountDue: string;
  amountPaid: string;
  freezeAmount: string;
  paidDetail: IpaidDetail[];
}

export interface IpaidDetail {
  paymentMode: string;
  amount: string;
}

// 减免单状态枚举
export const remissionStatusMap = {
  '-2': '作废',
  // '-1': '无需减免',
  '0': '待审核',
  '1': '待结算',
  '2': '审核驳回',
  '3': '审核通过',
};
