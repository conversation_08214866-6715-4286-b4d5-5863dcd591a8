/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2021-11-17 14:05:20
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import React from 'react';
import { Card } from 'antd';
import ProTable from '@ant-design/pro-table';
import globalStyle from '@/global.less';
import { repayRecording } from '../service';

const RepaymentRecord: React.FC<any> = (props) => {
  const { hideCard, overdueId } = props;
  const columns = [
    {
      title: '还款流水号',
      dataIndex: 'recordingNo',
    },
    {
      title: '实际还款总金额',
      dataIndex: 'actualRepaymentAmount',
    },
    {
      title: '实际还款本金',
      dataIndex: 'principal',
    },
    {
      title: '实际还款利息',
      dataIndex: 'interest',
    },
    {
      title: '实际还款罚息',
      dataIndex: 'penaltyInterest',
    },
    {
      title: '实际还款费用',
      dataIndex: 'cost',
    },
    {
      title: '还款方式',
      dataIndex: 'repayType',
    },
    {
      title: '还款时间',
      dataIndex: 'repayTime',
    },
  ];

  function contentElement() {
    return (
      <ProTable<OverdueListItem>
        columns={columns}
        scroll={{ x: 'max-content' }}
        rowKey="repayNo"
        search={false}
        options={false}
        toolBarRender={false}
        request={(params) =>
          repayRecording({
            ...params,
            overdueId,
          })
        }
      />
    );
  }

  return (
    <>
      {!hideCard && (
        <Card title="历史还款记录" className={globalStyle.mt20}>
          {contentElement()}
        </Card>
      )}
      {hideCard && <>{contentElement()}</>}
    </>
  );
};

export default RepaymentRecord;
