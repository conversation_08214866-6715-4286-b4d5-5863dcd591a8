import { getAuthHeaders } from '@/utils/auth';
import { downLoadExcel, getBaseUrl } from '@/utils/utils';
import { Button, message, Modal, Table, Tag, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/lib/upload';
import type { ReactElement } from 'react';
import React, { memo, useState } from 'react';
import { downloadTemplate } from '../services';

type Tprops = {
  children: ReactElement;
  reload?: () => void;
};
const AddModal: React.FC<Tprops> = (props) => {
  const [errorList, setErrorList] = useState([] as any);
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('点击上传');

  function getUploadUrl(url: string) {
    const { vanEnv } = document.documentElement.dataset; // vanEnv prod pre stg undefined 生产  预发布  测试  本地
    const baseUrl = vanEnv ? getBaseUrl() : '';
    const actionUrl = baseUrl + url;
    return actionUrl;
  }
  const headers = {
    'hll-appid': 'bme-finance-bizadmin-svc',
    ...getAuthHeaders(),
  };
  const onChange = (info: UploadChangeParam) => {
    const {
      file: { response, status },
    } = info;
    if (status === 'done') {
      if (response?.data?.success) {
        message.success('校验无误,上传成功');
        setErrorList([]);
        props.reload?.();
      } else {
        message.error('校验有误,请核实');
        const error: string[] = response?.data?.policyCheckErrorList;
        setErrorList(
          error
            .map((item, index) => ({
              id: index + 2,
              msg: item ? <Tag color="error">{item}</Tag> : null,
            }))
            .filter((item) => item.msg),
        );
        setTitle('重新上传');
      }
    }
  };
  return (
    <>
      <div
        onClick={() => {
          setVisible(true);
        }}
      >
        {props.children}
      </div>
      <Modal
        destroyOnClose
        title="上传车险保单"
        footer={null}
        open={visible}
        onCancel={() => {
          setVisible(false);
          setErrorList([]);
        }}
      >
        <div style={{ marginBottom: 10 }}>
          <Button
            type="link"
            style={{ marginRight: 20 }}
            onClick={() => {
              downloadTemplate()
                .then((res) => {
                  downLoadExcel(res);
                })
                .catch((err) => {
                  message.error(err);
                });
            }}
          >
            下载模版
          </Button>
          <Upload
            name="file"
            action={getUploadUrl('/bizadmin/policy/upload')}
            headers={headers}
            maxCount={1}
            onChange={onChange}
            onRemove={() => {
              setErrorList([]);
            }}
          >
            <Button type="primary">{title}</Button>
          </Upload>
        </div>
        <Table
          bordered
          dataSource={errorList}
          columns={[
            { dataIndex: 'id', title: '序号', width: 80 },
            { dataIndex: 'msg', title: '错误' },
          ]}
          pagination={false}
        />
      </Modal>
    </>
  );
};

export default memo(AddModal);
