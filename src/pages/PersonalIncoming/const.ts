/*
 * @Author: your name
 * @Date: 2022-05-05 11:06:31
 * @LastEditTime: 2024-05-07 14:35:56
 * @LastEditors: elisa.zhao
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalIncoming/const.ts
 */
//圆易借
export const privateStatusMap = {
  1: '已注册',
  10: '待提交用户信息',
  11: '待同意协议',
  // 15: '待实名认证',
  16: '失效(实名认证超限)',
  20: '待人脸',
  30: '待基础前筛',
  31: '拒绝(基础前筛)',
  32: '待风控前筛',
  33: '拒绝(风控前筛)',
  34: '待风控审核',
  35: '拒绝(风控审核)',
  36: '待补件', //新增
  37: '进件撤销', //新增
  41: '审核通过',
  //当前全部融合的进件状态
  // 42: "待初审(历史小贷)", // 实际小贷融租是30
  // 43:"待终审(历史小贷)",// 实际小贷融租是31
  // 44: "退回补件(历史小贷)",//实际小贷融租是32
  // 45:"风控撤销(历史小贷)",//实际小贷融租是33
  40: '审核拒绝',
  // 62:'再次授信'

  //补充历史的小贷
  // 43: '授信过期',
  // 62: '放款失败',
  // 81: '逾期',
};

export const merchantStatusMap = {
  1: '已注册',
  10: '待提交用户信息',
  11: '待同意协议',
  // 15: '待实名认证',
  16: '失效(实名认证超限)',
  20: '待人脸',
  30: '待基础前筛',
  31: '拒绝(基础前筛)',
  32: '待风控前筛',
  33: '拒绝(风控前筛)',
  34: '待风控审核',
  35: '拒绝(风控审核)',
  41: '审核通过',
  40: '审核拒绝',
};
//除了小易私房钱
export const exPrivateStatusMap = {
  1: '已注册',
  5: '实名认证失败',
  7: '失效（实名认证超限）',
  10: '用户创建成功',
  11: '秒拒',
  20: '提交进件信息',
  23: '预审中',
  24: '预审驳回',
  25: '预审通过',
  26: '预审拒绝',
  28: '风控中',
  30: '待初审',
  31: '待终审',
  32: '风控驳回',
  33: '风控撤销',
  // 34: '降额待确认',
  40: '审核拒绝',
  41: '审核通过',
  50: '降额通过',
  60: '开户失败',
};

//新枚举
export const privateStatusMapStringCode = {
  REGISTERED: '已注册',
  WAIT_SUBMIT_USER_INFO: '待提交用户信息',
  WAIT_AGREE: '待同意协议',
  LOSE_EFFICACY: '失效(实名认证超限)',
  WAIT_FACE: '待人脸',
  WAIT_US_FILTER: '待基础前筛',
  US_REJECTED: '拒绝(基础前筛)',
  WAIT_PARTNER_FILTER: '待风控前筛',
  PARTNER_REJECTED: '拒绝(风控前筛)',
  WAIT_PARTNER_REVIEW: '待风控审核',
  PARTNER_REVIEW_REJECTED: '拒绝(风控审核)',
  WAIT_SUPPLEMENT: '待补件',
  INCOME_REVOKE: '进件撤销',
  INCOME_FINAL: '审核通过',

  //历史小贷重复的
  // 'PENDING_TRIAL':'待初审（历史小贷）',
  // 'PENDING_REVIEW':'待终审（历史小贷）',
  // 'SUPPLEMENTARY_INFORMATION':'退回补件（历史小贷）',
  // 'REVOKE':'风控撤销（历史小贷）',
};

//小贷融租旧枚举
export const exPrivateStatusMapStringCode = {
  REGISTERED: '已注册',
  USER_CREATED_SUCCESSFULLY: '用户创建成功',
  REJECT_DIRECTLY: '秒拒',
  FACE_AUTHENTICATION_SUCCEEDED: '人脸认证成功',
  SUBMIT_INCOMING_INFORMATION: '待提交进件信息',
  PENDING_TRIAL: '待初审',
  PENDING_REVIEW: '待终审',
  SUPPLEMENTARY_INFORMATION: '退回补件',
  REVOKE: '风控撤销',
  REVIEW_REJECTED: '审核拒绝',
  EXAMINATION_PASSED: '审核通过',
};
