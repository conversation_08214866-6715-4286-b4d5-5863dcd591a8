/*
 * @Author: your name
 * @Date: 2021-03-31 15:45:39
 * @LastEditTime: 2025-04-11 15:13:51
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/lease-order-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab/index';
import ShowInfo from '@/components/ShowInfo/index';
import StepProgress from '@/components/StepProgress/index';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useAccess, useRequest } from '@umijs/max';
import React, { useEffect, useState } from 'react';
// import { ModalForm } from '@ant-design/pro-form';
import { CHANNEL_TYPES_MAP, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import globalStyle from '@/global.less';
import { getRepayPlan } from '@/services/global';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import {
  desensitizationBankAndIdCard,
  desensitizationPhone,
  isExternalDesensitization,
} from '@/utils/utils';
import { ExclamationCircleOutlined, RollbackOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProDescriptions } from '@ant-design/pro-components';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { useUpdateEffect } from 'ahooks';
import { Affix, Button, Card, message, Modal, Popover, Table, Tabs } from 'antd';
import { reportLog } from '../Collection/services';
import { aiBankDetail, getIntoDetail, incomeDetail } from '../PersonalIncoming/service';
import {
  BindCar,
  IncomingInfo,
  ReduceRentConfirm,
  ReleaseFeeRecord,
  RepayInfo,
} from './components';
import Delivery from './components/Delivery';
import GpsMain from './components/GPS/GpsMain';
import LoanLeaseInfo from './components/LoanLeaseInfo';
import OldDelivery from './components/OldDelivery';
import SignInfo from './components/SignInfo';
import UnBindCar from './components/UnBindCar';
import type { BankList } from './data';
import loanStyle from './index.less';
import {
  getChangeBankRecord,
  getLoanInfo,
  orderStatus,
  queryOrderDetail,
  queryOrderExtend,
  revokeOrder,
} from './service';

enum LOAN_TYPE {
  YI_REN_XING = 1,
  THIRD_LOAN = 9,
}

const LeaseOrderDetail: React.FC<any> = () => {
  const access = useAccess();
  const { orderNo }: any = history.location.query;
  const [revokeVisible, handleRevoke] = useState<boolean>(false);
  const [bankRecordVisible, setBankRecordVisible] = useState(false);
  const [showRevokeBtn, setShowRevokeBtn] = useState(false);

  const { data, run } = useRequest(() => {
    return queryOrderDetail(orderNo);
  });
  const { data: dataExtend, run: run1 } = useRequest(() => {
    return queryOrderExtend(orderNo);
  });
  const { data: statusLogs, run: run2 } = useRequest(() => {
    return orderStatus(orderNo);
  });
  const { data: repayPlanData, run: run3 } = useRequest(() => {
    return getRepayPlan(orderNo);
  });
  const { data: loanData, run: run4 } = useRequest(() => {
    return getLoanInfo(orderNo);
  });
  const { data: bankRecord, run: run5 } = useRequest(() => {
    return getChangeBankRecord(orderNo);
  });

  useUpdateEffect(() => {
    if (orderNo) {
      console.log('[融租订单详情] useUpdateEffect');
      run();
      run1();
      run2();
      run3();
      run4();
      run5();
    }
  }, [orderNo]);

  //根据二级产品分类类型
  const getBaseInfoFunc = (history030101Income: boolean, productSecondTypeCode: string) => {
    //旧小易速贷和融租数据走老得接口，新的走圆易借
    return history030101Income ||
      productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND
      ? incomeDetail({ orderNo: data?.orderReceiptNo, access })
      : aiBankDetail({ orderNo: data?.orderReceiptNo });
  };

  const { data: inComeInfo, run: runBaseInfo } = useRequest(getBaseInfoFunc, {
    manual: true,
  }); //  获取个人信息

  // const { data: baseData } = useRequest(() => {
  //   return getIntoDetail(orderNo);
  // });
  const [baseData, setBaseData] = useState({});
  useEffect(() => {
    if (data?.orderReceiptNo) {
      // 说明 queryOrderDetail 有数据了
      getIntoDetail(data?.orderReceiptNo).then((detail) => {
        setBaseData(detail.data);
        runBaseInfo(detail.data?.history030101Income, detail.data?.productSecondTypeCode); //  获取个人信息
      });
    }
  }, [data]);

  useEffect(() => {
    if (!access.hasAccess('btn_revoke_order_businessMng_leaseList')) {
      setShowRevokeBtn(false);
    } else {
      // 交车资料提交成功=70  交车资料驳回=71  待还款=80  逾期=81
      // 失效=-1 撤销=-2 放款审批通过=61,资方放款中=62,资方放款成功=64,
      // 以上状态隐藏撤销按钮
      if ([70, 71, 80, 81, -1, -2, 61, 62, 64].includes(data?.orderStatus)) {
        setShowRevokeBtn(false);
      } else {
        setShowRevokeBtn(true);
      }
      // 订单状态=资方放款中(62),且子状态=资方返回异常(10),需要展示撤销按钮
      if ([62].includes(data?.orderStatus) && data?.subStatus === 10) {
        setShowRevokeBtn(true);
      }
      // 状态=资方审批中(code=55)，且审批超时，需要展示撤销按钮
      if (data?.funderApprovalTimeOut && [55].includes(data?.orderStatus)) {
        setShowRevokeBtn(true);
      }
    }
  }, [data?.orderStatus]);

  // const bindCarInfo = {
  //   engineCode:dataExtend?.carInfo?.engineCode,
  //   licenseCode:dataExtend?.carInfo?.licenseCode,
  //   carUniqueCode:dataExtend?.carInfo?.carUniqueCode
  // }
  // todo 获取银行卡信息
  // 银行卡信息配置
  const cardInfoMap = {
    bankName: '银行',
    bankCode: '银行卡号',
    phone: '银行预留手机号',
  };
  const operationMap = {
    2: '绑卡',
    3: '绑卡',
    4: '解绑',
  };

  const carInfoMapNew = {
    channelType: '渠道类型',
    channelName: '渠道名称',
    carNo: '车辆编码',
    carModel: '车型名称',
    carModelCode: '车型码',
    carType: '车辆类型',
    carCategory: '车辆种类',
    manufacturer: '厂商',
    // carModel: '车型',
    carYears: '年代款',
    carEmission: '排量',
    seatsNum: '座位数',
    otherMsg: '其他',
    cityName: '库存城市',
    totalPrice: '全包价',
    color: '颜色',
    preferentialPrice: '优惠金额',
    applyCityDetail: '申请城市',
    carCityDetail: '上牌城市',
  };

  const carInfoMapSecond = {
    channelType: '渠道类型',
    channelName: '渠道名称',
    carNo: '车辆编码',
    carModel: '车型名称',
    carUniqueCode: '车辆识别代码',
    carType: '车辆类型',
    carCategory: '车辆种类',
    energyType: '能源类型',
    environmentalType: '环保标准',
    manufacturer: '厂商',
    carYears: '年代款',
    carEmission: '排量',
    seatsNum: '座位数',
    color: '颜色',
    otherMsg: '其他',
    carModelCode: '车型码',
    engineCode: '发动机号',
    firstRegisterDate: '首次上牌日期',
    mileage: '公里数（KM）',
    licenseCode: '车牌',
    totalPrice: '全包价',
    cityName: '库存城市',
    preferentialPrice: '优惠金额',
    applyCityDetail: '申请城市',
    carCityDetail: '上牌城市',
  };

  const commonInfo = {
    applyStore: '门店',
    licenseCompany: '上牌方',
    // salesName: '销售姓名',
    // salesNo: '销售工号',
  };
  enum DELIVERY_TYPE { // 是否为新的交车 1.新 2.旧
    NEW = 1,
    OLD = 2,
  }
  const itemCarMap = {
    carType: {
      1: '新车',
      2: '二手',
    },
    channelType: CHANNEL_TYPES_MAP,
    carCategory: {
      1: '货车',
      2: '乘用车',
    },
    energyType: {
      1: '燃油',
      2: '纯电动',
      3: '油电混合',
      4: '柴油',
    },
    environmentalType: {
      4: '国四',
      5: '国五',
      6: '国六',
    },
  };

  const bankColumns: ProColumns<BankList>[] = [
    {
      title: '银行',
      dataIndex: 'bankName',
    },
    {
      title: '银行卡号',
      dataIndex: 'cardNo',
      render(_, record) {
        const { cardNo } = record;
        return cardNo
          ? isExternalDesensitization(access)
            ? desensitizationBankAndIdCard(cardNo)
            : cardNo
          : '-';
      },
    },
    {
      title: '银行预留手机号',
      dataIndex: 'phone',
      render(_, record) {
        const { phone } = record;
        return phone ? (
          isExternalDesensitization(access) ? (
            <>
              {desensitizationPhone(phone)}
              <Popover content={phone} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (phone) {
                      await reportLog(phone);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          ) : (
            phone
          )
        ) : (
          '-'
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'type',
      render: (_, record) => {
        return operationMap[record.type];
      },
    },
    {
      title: '操作时间',
      dataIndex: 'updatedAt',
    },
  ];

  const buyBackInfo = {
    buybackFirstTime: '首次回购时间',
    buybackSucceedTime: '回购完成时间',
    buybackAmount: '回购金额',
  };

  const refresh = () => {
    run();
    run1();
    run2(); // 解绑后需要重新更新订单的的状态
  };
  const handleRevokeOpt = (form: { revokeReason: string }) => {
    return revokeOrder(orderNo, form?.revokeReason)
      .then(() => {
        message.success(`撤销成功`);
        run();
        run2();
        handleRevoke(false);
      })
      .catch((err) => {
        handleRevoke(false);
        Modal.error({
          title: err.message || err.msg,
          width: 400,
          centered: true,
          okText: '好的',
        });
      });
  };
  let MAX_LOOP_TIME = 10;
  const confirmCallback = (cb?: () => void) => {
    setTimeout(() => {
      run().then((val) => {
        if (val?.orderStatus !== 23) {
          run1();
          run2();
          run3();
          run4();
          run5();
          if (cb) cb(); //  降额状态改变则结束轮询
          return;
        }
        if (MAX_LOOP_TIME > 0) {
          confirmCallback(cb);
          MAX_LOOP_TIME--;
          return;
        }

        if (cb) cb();
        MAX_LOOP_TIME = 10;
        message.error('网络异常，请刷新页面再试！');
      });
    }, 3 * 1000);
  };

  function getInfoColumns() {
    const columns1: ProColumns<{
      bankName: string;
      bankCode: string;
      phone: string;
    }>[] = [
      { title: '银行', dataIndex: 'bankName' },
      {
        title: '银行卡号',
        dataIndex: 'bankCode',
        render(_, record) {
          return record?.bankCode ? desensitizationBankAndIdCard(record?.bankCode) : '-';
        },
      },
      {
        title: '银行预留手机号',
        dataIndex: 'phone',
        render(_, record) {
          const { phone } = record;
          return record?.phone ? (
            <>
              {desensitizationPhone(record?.phone)}{' '}
              <Popover content={phone} trigger="click">
                <a
                  onClick={async () => {
                    // 发送查看日志
                    if (phone) {
                      await reportLog(phone);
                    }
                  }}
                >
                  查看
                </a>
              </Popover>
            </>
          ) : (
            '-'
          );
        },
      },
    ];
    return columns1;
  }

  return (
    <>
      <HeaderTab />
      <PageContainer
        className={globalStyle.mt16}
        extra={
          showRevokeBtn && [
            <a
              key={'handleRevoke'}
              onClick={() => {
                handleRevoke(true);
              }}
            >
              <RollbackOutlined />
              撤销订单
            </a>,
          ]
        }
      >
        <StepProgress
          stepStatus={statusLogs?.map(
            (item: {
              auditTime: string;
              statusDesc: string;
              subStatusDesc: string;
              status: number;
              rejectMsg: string;
              funderRejectReasons: string;
            }) => {
              return {
                bol: false,
                desc: item.statusDesc,
                localDate: item.auditTime,
                subStatusDesc: item.subStatusDesc,
                status: item.status,
                funderRejectReason: item.funderRejectReasons?.split(',')?.[0], // 只取funderRejectReasons中的第一个理由展示
                rejectMsg: item.rejectMsg,
              };
            },
          )}
        />
        {/* 进件信息 */}
        <IncomingInfo
          data={data}
          tableData={repayPlanData?.listRspList || []}
          baseData={baseData}
          inComeInfo={inComeInfo}
        />
        {/* 车辆信息 */}
        <Card
          title="车辆信息"
          style={{ marginTop: 20 }}
          extra={
            //车辆绑定成功51,银行卡绑定成功53,签约成功54,待放款60放款状态=待请款
            //资方授信审批通过=56,资方授信审批拒绝=57
            [51, 53, 54, 56, 57].includes(data?.orderStatus) ||
            (data?.orderStatus === 60 &&
              loanData?.find((item) => item.type === LOAN_TYPE.YI_REN_XING)?.status === 3) ? ( //status loanStatus.status 10 待一审  11待二审  3待请款
              <Button
                type="primary"
                danger
                onClick={() => {
                  // console.log('0000', dataExtend);
                  optimizationModalWrapper(UnBindCar)({
                    data: {
                      creditAmount: data?.creditAmount,
                      orderNo: dataExtend?.orderNo,
                      userName: data?.userName,
                      channelType: dataExtend?.carInfo?.channelType,
                      channelName: dataExtend?.carInfo?.channelName,
                      carUniqueCode: dataExtend?.carInfo?.carUniqueCode,
                      carType: dataExtend?.carInfo?.carType,
                      carFabricateDate: dataExtend?.carInfo?.carFabricateDate,
                      engineCode: dataExtend?.carInfo?.engineCode,
                    },
                    refresh,
                  });
                }}
              >
                车辆解绑
              </Button>
            ) : (
              <></>
            )
          }
        >
          <Tabs>
            <Tabs.TabPane tab="基础信息" key="item-1">
              <ShowInfo
                noCard
                infoMap={
                  dataExtend?.carInfo?.carType === 1
                    ? { ...carInfoMapNew, ...commonInfo }
                    : { ...carInfoMapSecond, ...commonInfo }
                }
                itemMap={itemCarMap}
                data={{
                  ...dataExtend?.carInfo,
                  ...dataExtend?.storeInfo,
                  carCityDetail: dataExtend?.carInfo?.carCityDetail?.label,
                  applyCityDetail: dataExtend?.carInfo?.applyCityDetail?.label,
                }}
              />
            </Tabs.TabPane>
            <Tabs.TabPane tab="绑定车辆" key="item-2">
              <BindCar
                data={dataExtend?.carInfo}
                orderDetail={data}
                orderStatus={dataExtend?.orderStatus}
                refresh={refresh}
                orderNo={orderNo}
                showItemMap={itemCarMap}
                dataShowInfo={{
                  ...dataExtend?.carInfoApplyBackup,
                  ...dataExtend?.storeInfo,
                  carCityDetail: dataExtend?.carInfoApplyBackup?.carCityDetail?.label,
                  applyCityDetail: dataExtend?.carInfoApplyBackup?.applyCityDetail?.label,
                }}
                showInfoMap={dataExtend?.carInfo?.carType === 1 ? carInfoMapNew : carInfoMapSecond}
              />
            </Tabs.TabPane>
            {access.hasAccess('model_gps_info_businessMng_leaseList') && (
              <Tabs.TabPane tab="GPS信息" key="item-3">
                <GpsMain />
              </Tabs.TabPane>
            )}
            <Tabs.TabPane tab="交车资料" key="item-4" className={loanStyle.deliveryTab}>
              {dataExtend?.carInfo?.isNewCarDelivery === DELIVERY_TYPE.OLD ? (
                /* 老交车模块 */
                <OldDelivery
                  data={data}
                  orderStatus={dataExtend?.orderStatus}
                  orderNo={orderNo}
                  refresh={refresh}
                  isNewCarDelivery={data?.isNewCarDelivery}
                />
              ) : (
                /* 新交车模块 */
                <Delivery
                  data={dataExtend?.carInfo}
                  orderStatus={dataExtend?.orderStatus}
                  carDeliveryStatus={dataExtend?.carDeliveryStatus}
                  orderNo={orderNo}
                  refresh={refresh}
                />
              )}
            </Tabs.TabPane>
          </Tabs>
        </Card>
        {/* 签约信息 */}
        <SignInfo dataExtend={dataExtend} orderNo={orderNo} data={data} />
        {/* 银行卡 */}
        {isExternalDesensitization(access) ? (
          <Card
            title="银行卡"
            extra={
              <Button
                type="link"
                onClick={() => {
                  setBankRecordVisible(true);
                }}
              >
                银行卡日志
              </Button>
            }
          >
            <ProDescriptions dataSource={dataExtend?.bankcardInfo} columns={getInfoColumns()} />
          </Card>
        ) : (
          <ShowInfo
            title="银行卡"
            infoMap={cardInfoMap}
            data={dataExtend?.bankcardInfo}
            extra={
              <Button
                type="link"
                onClick={() => {
                  setBankRecordVisible(true);
                }}
              >
                银行卡日志
              </Button>
            }
          />
        )}

        {/* 费用记录 */}
        <ReleaseFeeRecord orderFeeRecord={dataExtend?.feeRecord} orderNo={orderNo} />
        {/* <ShowInfo title="放款信息" infoMap={loanInfoMap} itemMap={itemLoanMap} data={loanData} />
         */}
        {/* 放款信息 */}
        {loanData?.map((loanItem) => {
          return (
            <LoanLeaseInfo
              key={loanItem?.lendingNo}
              loanData={loanItem}
              productCode={data?.productCode}
              statusLogs={statusLogs}
            />
          );
        })}

        {/* 还款信息 */}
        <RepayInfo
          orderNo={orderNo}
          productCode={data?.productCode}
          accountNumber={data?.userNo}
          accountName={data?.userName}
        />

        {/* 回购信息 */}
        <ShowInfo title="回购信息" infoMap={buyBackInfo} data={data} />

        <ModalForm
          title="撤销"
          visible={revokeVisible}
          layout="horizontal"
          // form={form}
          onFinish={(values: any) => handleRevokeOpt(values)}
          modalProps={{
            centered: true,
            destroyOnClose: true,
            okText: '确认撤销',
            onCancel: () => handleRevoke(false),
          }}
          width="400px"
        >
          <div className={globalStyle.textCenter}>
            <ExclamationCircleOutlined className={globalStyle.iconCss} />
            <span className={`${globalStyle.fontWBold} ${globalStyle.fontS16}`}>
              是否确认撤销该笔订单?
            </span>
            <p className={globalStyle.mt10}>撤销后，订单将无法继续进行，请谨慎操作！</p>
          </div>
          <ProFormText
            width="sm"
            labelCol={{ span: 6 }}
            name="revokeReason"
            rules={[{ required: true }]}
            label="撤销原因"
            placeholder="请输入撤销原因"
          />
        </ModalForm>
        <Modal
          title="银行卡日志"
          open={bankRecordVisible}
          footer={null}
          width="800px"
          onCancel={() => {
            setBankRecordVisible(false);
          }}
        >
          <Table columns={bankColumns as any[]} dataSource={bankRecord} />
        </Modal>
        {/* 是否接受降额通过悬浮框 */}
        <Affix style={{ position: 'fixed', bottom: 20, right: 20, zIndex: 100 }}>
          <ReduceRentConfirm
            orderNo={orderNo}
            status={data?.orderStatus}
            info={baseData}
            deratingRentPrice={data?.deratingRentPrice}
            confirmCallback={confirmCallback}
          />
        </Affix>
      </PageContainer>
    </>
  );
};

export default LeaseOrderDetail;

// export default () => (
//   <>
//     <HeaderTab />
//     <KeepAlive name={'businessMng/lease-detail'}>
//       <LeaseOrderDetail />
//     </KeepAlive>
//   </>
// );
