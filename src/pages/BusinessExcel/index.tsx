import HeaderTab from '@/components/HeaderTab';
import {
  PageContainer,
  ProForm,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  QueryFilter,
} from '@ant-design/pro-components';
import { Button, Card, DatePicker, Form, message, Modal, Space, Tabs } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import dayjs from 'dayjs';
import React, { memo, useState } from 'react';
import CommitRecord from './components/CommitRecord';
import type { IshowFileListItem } from './components/UploadExcel';
import UploadExcel from './components/UploadExcel';
import { commitToFinance, queryReportFile } from './services';
import styles from './styles/index.less';
const BusinessExcel = () => {
  const [personalFileList, setPersonalFileList] = useState<IshowFileListItem[]>([]);
  const [enterpriseFileList, setEnterpriseFileList] = useState<IshowFileListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [form] = useForm();

  const renderExcelList = () => {
    return (
      <div>
        <div style={{ display: 'flex' }}>
          <div>A55_小额贷款(个人贷款)信息表:</div>
          <div>
            {personalFileList?.length
              ? personalFileList.map((file) => {
                  const { name, url } = file;
                  return (
                    <a
                      href={url}
                      key={name}
                      target="_blank"
                      rel="noreferrer"
                      style={{ display: 'block' }}
                    >
                      {name}
                    </a>
                  );
                })
              : '无'}
          </div>
        </div>
        <div style={{ display: 'flex' }}>
          <div>A56_小额贷款(企业贷款)信息表:</div>
          <div>
            {enterpriseFileList?.length
              ? enterpriseFileList.map((file) => {
                  const { name, url } = file;
                  return (
                    <a
                      href={url}
                      key={name}
                      target="_blank"
                      rel="noreferrer"
                      style={{ display: 'block' }}
                    >
                      {name}
                    </a>
                  );
                })
              : '无'}
          </div>
        </div>
      </div>
    );
  };

  const MemoRenderExcelList = memo(renderExcelList);
  const commitToFinanceParams = JSON.parse(localStorage.getItem('commitToFinanceParams') || '{}');
  return (
    <div className={styles['business-excel']}>
      <HeaderTab />
      <PageContainer header={{ title: '小额贷款业务报表' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 20 }}>
          <Card title="导出业务明细表（月报）" size="small">
            <QueryFilter<{ dataMonth: string; productSecondCode: string }>
              layout="horizontal"
              onFinish={async (values) => {
                setLoading(true);
                const data = await queryReportFile(values);

                if (!data) {
                  message.error('没有数据');
                  setLoading(false);
                  return;
                }
                if (data?.fileNetworkUrl) {
                  // 下载
                  const a = document.createElement('a');
                  a.href = data?.fileNetworkUrl;
                  a.click();
                  // 会自动识别 如何浏览器不支持打开该文件，会变成下载，从而不用添加download属性也行，比如excel文件
                }
                setLoading(false);
              }}
              submitter={{
                render: () => (
                  // 这里的没法封装loading?
                  <Button type="primary" htmlType="submit" loading={loading}>
                    导出
                  </Button>
                ),
              }}
            >
              <ProFormDatePicker
                name="dataMonth"
                fieldProps={{ picker: 'month', format: 'YYYY-MM' }}
                label="月份"
                rules={[{ required: true }]}
              />
              <ProFormSelect
                rules={[{ required: true }]}
                label="产品二级分类"
                name="productSecondCode"
                labelCol={{
                  offset: 0,
                }}
                valueEnum={{
                  SMALL_LOAN_PERSONAL: '易人行小贷（个人贷款）', //原code => '0301': '易人行小贷（个人贷款）',
                  SMALL_LOAN_ENTERPRISE: '易人行小贷（企业贷款）', //原code => '0303': '车险分期（企业贷款）',
                  CAR_INSURANCE_PERSON: '车险分期（个人贷款老系统）',
                  // '0304': '圆商贷',
                }}
              />
            </QueryFilter>
          </Card>
          <Card title="上传业务明细表（月报）" size="small">
            <ProForm
              layout="horizontal"
              submitter={{ render: () => null }}
              form={form}
              initialValues={{
                ...commitToFinanceParams,
                dataMonth: commitToFinanceParams?.dataMonth
                  ? dayjs(commitToFinanceParams?.dataMonth)
                  : dayjs().subtract(1, 'M'),
              }}
              onValuesChange={(changedValues, values) => {
                // setFieldsValue 是无法触发此方法的，也无法更改值
                console.log('values', values);

                values.dataMonth = dayjs(values.dataMonth).format('YYYY-MM');
                localStorage.setItem('commitToFinanceParams', JSON.stringify(values));
              }}
            >
              <Form.Item name="dataMonth" label="上报期数" rules={[{ required: true }]}>
                <DatePicker
                  picker="month"
                  onChange={(value) => {
                    const clear = {
                      templateId: undefined,
                      taskId: undefined,
                      subTaskId: undefined,
                    };
                    const params = {
                      dataMonth: dayjs(value as any).format('YYYY-MM'),
                      ...clear,
                    };
                    localStorage.setItem('commitToFinanceParams', JSON.stringify(params));
                    form.setFieldsValue(clear);
                  }}
                />
              </Form.Item>
              <div style={{ display: 'flex', gap: 20 }}>
                <div style={{ flex: 2 }}>
                  <ProFormText name="templateId" label="模版id" rules={[{ required: true }]} />
                </div>
                <div style={{ flex: 1 }}>
                  <ProFormText name="taskId" label="任务id" rules={[{ required: true }]} />
                </div>
                <div style={{ flex: 1 }}>
                  {' '}
                  <ProFormText name="subTaskId" label="子任务id" rules={[{ required: true }]} />
                </div>
              </div>
              <MemoRenderExcelList />
              <div style={{ display: 'flex', justifyContent: 'end' }}>
                <Space>
                  <UploadExcel
                    setPersonalFileList={setPersonalFileList}
                    setEnterpriseFileList={setEnterpriseFileList}
                    form={form}
                  />
                  <Button
                    type="primary"
                    onClick={async () => {
                      if (!personalFileList?.length && !enterpriseFileList?.length) {
                        message.error('请至少上传一个报表');
                        return;
                      }
                      await form.validateFields();
                      Modal.confirm({
                        title: '确认提交以下报表至金融系统吗',
                        content: <MemoRenderExcelList />,
                        onOk: async () => {
                          const values = form.getFieldsValue();
                          const personalIds = personalFileList.map((item) => item.id);
                          const enterpriseIds = enterpriseFileList.map((item) => item.id);
                          await commitToFinance({
                            ...values,
                            fileIdList: [...personalIds, ...enterpriseIds],
                            dataMonth: dayjs(values?.dataMonth).format('YYYY-MM'),
                          });
                          message.success('提交成功');
                        },
                      });
                    }}
                  >
                    提交报表至金融系统
                  </Button>
                </Space>
              </div>
            </ProForm>
          </Card>
          <Card title="提交记录" size="small">
            <Tabs
              size="small"
              defaultActiveKey="1"
              destroyInactiveTabPane
              items={[
                {
                  label: '个人贷款',
                  key: '1',
                  children: <CommitRecord fileType={1} />,
                },
                {
                  label: '企业贷款',
                  key: '2',
                  children: <CommitRecord fileType={2} />,
                },
              ]}
            />
          </Card>
        </div>
      </PageContainer>
    </div>
  );
};
export default memo(BusinessExcel);
