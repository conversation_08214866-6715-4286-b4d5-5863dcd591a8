import globalStyle from '@/global.less';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Card, Col, Row } from 'antd';
import React, { useState } from 'react';
import { Link, useModel } from 'umi';
import type { PaybackListItem } from '../data';
import { getPayListByAccount } from '../service';

interface PaybackListProps {
  userNo: string;
}

const PaybackList: React.FC<PaybackListProps> = (props) => {
  // const { data: otherData, loading: paybackListLoading } = useRequest(() => {
  //   return getPayListByAccount(props.userNo);
  // });
  const { mapUrgePersonList: mapUserList } = useModel('userList');
  const { userNo } = props;
  const [dataSource, setDataSource] = useState([]);
  const overdueCaseNoList = dataSource?.map((item) => {
    return item?.overdueCaseNo;
  });
  const columns: ProColumns<PaybackListItem>[] = [
    {
      title: '催收单号',
      dataIndex: 'overdueId',
      // key:"overdueId"
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
    },
    {
      title: '联系人',
      dataIndex: 'contactPerson',
    },
    {
      title: '联系人手机号',
      dataIndex: 'contactPhone',
    },
    {
      title: '期限',
      dataIndex: 'repayTerm',
      valueEnum: {
        1: { text: '固定还款日' },
      },
    },
    {
      title: '逾期天数',
      dataIndex: 'daysOverdue',
    },
    {
      title: '逾期金额',
      dataIndex: 'overdueAmount',
    },
    {
      title: '逾期利息',
      dataIndex: 'overdueInterest',
    },
    {
      title: '逾期罚息',
      dataIndex: 'overduePenaltyInterest',
    },
    {
      title: '已还',
      dataIndex: 'returnedAmount',
    },
    {
      title: '催收员',
      dataIndex: 'urgePerson',
      render: (_: any, row: PaybackListItem) => {
        return mapUserList[row.urgePerson] || '-';
      },
    },
    {
      title: '催收状态',
      dataIndex: 'urgeState',
      valueEnum: {
        // 0: { text: '全部' },
        10: {
          text: '待分配',
        },
        20: {
          text: '待跟进',
        },
        30: {
          text: '跟进中',
        },
        40: {
          text: '催收结清',
        },
        50: {
          text: '逾期撤销',
        },
      },
    },
    {
      title: '最新催收时间',
      dataIndex: 'urgeRecentlyTime',
    },
    {
      title: '最新催收记录',
      dataIndex: 'urgeRecentlyMsg',
      width: 300,
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      width: 80,
      fixed: 'right',
      render: (_: any, record: PaybackListItem, dataIndex: number) => (
        <>
          {/* <Link to={`/businessMng/postLoanMng/overdue-detail?overdueId=${record.overdueId}`}>
            查看详情
          </Link> */}
          <Link
            to={{
              pathname: `/businessMng/postLoanMng/collection-detail`,
              search: `?overdueCaseNo=${record?.overdueCaseNo}`,
            }}
            state={{
              curList: overdueCaseNoList || [],
              curItemIndex: dataIndex,
            }}
          >
            查看详情
          </Link>
        </>
      ),
    },
  ];
  return (
    <Card title="催收记录" bordered className={globalStyle.mt20}>
      <Row>
        <Col span={24} key="repay">
          <div style={{ lineHeight: '40px' }}>
            <ProTable<PaybackListItem>
              rowKey="overdueId"
              scroll={{ x: 'max-content' }}
              // dataSource={otherData?.listRspList}
              request={async (params: any) => {
                const { data, total } = await getPayListByAccount({
                  accountNumber: userNo,
                  current: params.current,
                  pageSize: params.pageSize,
                });
                setDataSource(data);
                return {
                  data,
                  success: true,
                  total,
                };
              }}
              columns={columns}
              search={false}
              toolBarRender={false}
            />
          </div>
        </Col>
      </Row>
    </Card>
  );
};

export default PaybackList;
