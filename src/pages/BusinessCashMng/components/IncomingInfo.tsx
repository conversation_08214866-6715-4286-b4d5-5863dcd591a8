/*
 * @Author: your name
 * @Date: 2021-04-19 15:25:32
 * @LastEditTime: 2022-06-23 17:31:58
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/Incoming.tsx
 */
import React from 'react';
import ShowInfo from '@/components/ShowInfo/index';
import { Table, Col, Row } from 'antd';
import globalStyle from '@/global.less';
import type { OrderListItem, RepayItem } from '../data';
import BigNumber from 'bignumber.js';

interface IncomingInfoProps {
  // orderNo: string
  data: OrderListItem;
  tableData: RepayItem[];
  pagination?: boolean;
  rowKey?: string;
  selfDefine?: Record<string, any>;
}
const IncomingInfo: React.FC<IncomingInfoProps> = (props) => {
  const { data, tableData } = props;
  // const [computedData, updateComputedData] = useState(data);

  // // 年利率转百分比
  // useEffect(() => {
  //   if (data) {
  //     updateComputedData({ ...data, interestRate: `${parseFloat(data.interestRate) * 100}%` });
  //   }
  // }, [data]);

  let finallyTableData = [];
  // 处理为树形结构
  if (tableData.length > 1) {
    const children = tableData?.slice(1, tableData.length);
    finallyTableData = [{ ...tableData?.[0], children }] || [];
  } else {
    finallyTableData = tableData;
  }

  // todo获取进件信息
  const incomeColumn = [
    {
      title: '期数',
      dataIndex: 'termDetail',
    },
    {
      title: '应还总额',
      dataIndex: 'amountDue',
      key: 'amountDue',
    },
    {
      title: '应还本金',
      dataIndex: 'principal',
      key: 'principal',
    },
    {
      title: '应还利息',
      dataIndex: 'interest',
      key: 'interest',
    },
    // {
    //   title: '费用',
    //   dataIndex: 'cost',
    //   key: 'cost',
    // },
    {
      title: '应还日期',
      dataIndex: 'repayTime',
      key: 'repayTime',
    },
  ];

  // 进件card配置
  const incomeInfoMap = {
    orderNo: '订单号',
    userNo: '用户ID',
    userName: '用户名称',
    productName: '申请产品',
    channel: '渠道商户',
    applyAmount: '申请金额',
    loanTerm: '期限',
    repayType: '还款方式',
    interestRate: '年利率',
    createdAt: '进件时间',
    creditAmount: '授信额度',
    // status: '进件状态',
    usageOfLoan: '借款用途',
  };
  const selfDefine: Record<string, any> = {
    interestRate: `${new BigNumber(data?.interestRate).times(100)}%`,
  };
  console.log('finallyTableData', finallyTableData);
  return (
    <ShowInfo infoMap={incomeInfoMap} data={data} noCard selfDefine={selfDefine}>
      <Row className={globalStyle.pl20}>
        <Col span={2}>
          <span className={` ${globalStyle.lineHeight40}`}>还款计划：</span>
        </Col>
        <Col>
          <Table
            columns={incomeColumn}
            dataSource={finallyTableData}
            className={globalStyle.childrenTr}
            pagination={false}
            rowKey={(record) => {
              return record?.amountDue + record?.principal + record?.repayTime;
            }}
          />
        </Col>
      </Row>
    </ShowInfo>
  );
};

export default IncomingInfo;
