/*
 * @Author: your name
 * @Date: 2021-04-19 15:19:48
 * @LastEditTime: 2023-08-16 11:14:46
 * @LastEditors: elisa.zhao
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Loan/loan-lease-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import LoanLeaseInfo from '@/pages/Loan/components/LoanLeaseInfo';
import { getRepayPlan, queryOrderDetail } from '@/services/global';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import IncomingInfo from '../BusinessLeaseMng/components/IncomingInfo';
import { getIntoDetail } from '../PersonalIncoming/service';
import { getAuditLogs, getLeaseLoanInfo, leaseDetail } from './service';

const LoanDetail: React.FC<any> = () => {
  const { orderNo, lendingNo, loanType } = history.location.query as {
    orderNo: string;
    lendingNo: string;
    loanType: string;
  };
  // 进件详情
  const { data, run: runDetail } = useRequest(() => {
    return queryOrderDetail(orderNo);
  });
  // 融租放款详情
  const { data: dataInfo, run: runDataInfo } = useRequest(() => {
    return leaseDetail(lendingNo, orderNo);
  });
  // 放款信息
  const { data: dataLending, run: runLending, loading } = useRequest(() => {
    return getLeaseLoanInfo(lendingNo);
  });
  // 获取还款计划
  const { data: repayPlanData, run: runRepayPlan } = useRequest(() => {
    return getRepayPlan(orderNo);
  });
  // 获取请款进度
  const { data: auditLogs, run: runAuditLogs } = useRequest(() => {
    return getAuditLogs(lendingNo);
  });

  const [baseData, setBaseData] = useState({});
  useEffect(() => {
    if (data?.orderReceiptNo) {
      // 说明 queryOrderDetail 有数据了
      getIntoDetail(data?.orderReceiptNo).then((detail) => {
        setBaseData(detail.data);
      });
    }
  }, [data]);
  // 刷新接口
  const refresh = () => {
    runDetail();
    runDataInfo();
    runLending();
    runRepayPlan();
    runAuditLogs();
  };
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Spin spinning={loading}>
          <IncomingInfo
            data={data}
            baseData={baseData}
            tableData={repayPlanData?.listRspList || []}
          />
          <LoanLeaseInfo
            dataInfo={dataInfo}
            productCode={data?.productCode}
            guaranteeType={data?.guaranteeType}
            dataLending={dataLending}
            userName={data?.userName}
            userNo={data?.userNo}
            loanType={loanType}
            loanTerm={data?.loanTerm}
            orderDetail={data}
            auditLogs={auditLogs}
            refresh={refresh}
          />
        </Spin>
      </PageContainer>
    </>
  );
};

export default LoanDetail;
