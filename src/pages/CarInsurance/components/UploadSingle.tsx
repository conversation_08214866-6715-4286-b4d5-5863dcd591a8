import React, { useState } from 'react';
import { UploadOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import { Button } from 'antd';
import { Upload } from 'antd';
import type { UploadProps } from 'antd/es/upload';
import type { RcFile, UploadFile } from 'antd/es/upload/interface';
import { getBaseUploadAction } from '../utils';
import { getAuthHeaders } from '@/utils/auth';
type Props = {
  value?: string; //
  onChange?: (val: string) => void;
  form?: FormInstance;
  name?: string; // setFieldsValue的字段
  beforeUpload?: (file: RcFile) => any;
};
const UploadSingle: React.FC<Props> = ({ value, onChange, form, name, beforeUpload }) => {
  const [fileList, setFileList] = useState<UploadFile[]>(
    value ? [{ uid: '-1', name: getFileName(value), status: 'done', url: value }] : [],
  );
  const [loading, setLoading] = useState(false);
  function getFileName(url?: string) {
    if (!url) return '';
    const arr = decodeURI(url).split('/');
    const arr2 = arr[arr.length - 1]?.split('?');
    return arr2?.[0];
  }
  const handleChange: UploadProps['onChange'] = ({ file, fileList: newFileList }) => {
    if (file?.status === 'uploading') {
      setLoading(true);
    }
    if (file?.status === 'done') {
      setLoading(false);
      const { response = {} } = file;
      const { code, data = {} } = response;
      if (code === 200) {
        const { filePath, netWorkPath } = data;
        onChange!(netWorkPath);
        if (name) form?.setFieldsValue({ [name]: filePath });
        setFileList(newFileList.map((item) => (item.url = netWorkPath)));
      }
    }
    setFileList(newFileList);
  };
  return (
    <>
      <Upload
        beforeUpload={beforeUpload}
        action={getBaseUploadAction('/base/oss/common/uploadfile')}
        fileList={fileList}
        data={{
          attachment: false,
          acl: 'PUBLIC_READ',
          destPath: 'insurance/policy/vehicleLicense/',
        }}
        headers={{ ...getAuthHeaders() }}
        name="file"
        maxCount={1}
        onChange={handleChange}
      >
        <Button icon={<UploadOutlined />} loading={loading}>
          Upload
        </Button>
      </Upload>
    </>
  );
};

export default UploadSingle;
