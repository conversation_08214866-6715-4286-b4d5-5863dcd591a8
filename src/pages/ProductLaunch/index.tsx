import { DividerTit } from '@/components';
import HeaderTab from '@/components/HeaderTab';
import {
  CHANNEL_TYPES_MAP,
  ENERGY_TYPE_MAP,
  GUARANTEE_TYPES,
  LICENSE_TYPES,
  LICENSE_TYPES_MAP,
  LICENSE_TYPES_MAP_ENUM,
} from '@/enums';
import globalStyle from '@/global.less';
import { getProductNameEnum, productCodeEnum } from '@/services/enum';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import ProCard from '@ant-design/pro-card';
import { ModalForm } from '@ant-design/pro-form';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import { Button, message } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import BigNumber from 'bignumber.js';
import React, { useRef, useState } from 'react';
import LicenseTabsCom from '../Product/components/LicenseTabsCom';
import RegionInputInterest from '../Product/components/RegionInputInterest';
import CarInfoTransfer from './components/CarInfoTransfer';
import ChannelTransfer from './components/ChannelTransfer';
import ConfigModelButton from './components/ConfigModelButton';
import InventoryCityTransfer from './components/InventoryCityTransfer';
import { STATUS } from './conts';
import type { TCarInfoTableItem, TChannelTableItem, TProductTableItem } from './data.d';
import styles from './index.less';
import {
  findQuery,
  getCarList,
  getChannelList,
  getProductList,
  saveOrDelConfigInfo,
} from './service';

const ProductLaunch: React.FC = () => {
  /**
   * 渠道列表 ref
   */
  const actionChannelRef = useRef<ActionType>();
  /**
   * 车辆列表 ref
   */
  const actionCarRef = useRef<ActionType>();
  /**
   * 保存当前的产品编码
   */
  const [productId, setProductId] = useState<string>('');
  /**
   * 保存当前的产品名称
   */
  const [productName, setProductName] = useState<string>('');
  /**
   * 保存当前的产品上牌类型
   */
  const [currentLicenseType, setCurrentLicenseType] = useState<string>('');
  /**
   * 保存当前的渠道编码
   */
  const [channelId, setChannelId] = useState<number>(NaN);
  /**
   * 保存当前的渠道名称
   */
  const [channelName, setChannelName] = useState<string>('');
  /**
   * 控制 删除渠道配置 弹窗是否显示变量
   */
  const [channelVisible, setChannelVisible] = useState<boolean>(false);
  /**
   * 当前 删除渠道配置 的 id
   */
  const [curDelChannelId, setCurDelChannelId] = useState<number>(NaN);
  /**
   * 控制 删除车辆配置 弹窗是否显示变量
   */
  const [carlVisible, setCarVisible] = useState<boolean>(false);
  /**
   * 当前 删除车辆配置 的 id
   */
  const [curDelCarlId, setCurDelCarId] = useState<number>(NaN);

  const formRef = useRef<FormInstance>();
  const licenseTabsRef = useRef<any>(null);
  const [licenseType, setLicenseType] = useState<string>(LICENSE_TYPES.AFFILIATE);

  /**
   *  产品信息表格
   */
  const productColumns: ProColumns<TProductTableItem>[] = [
    {
      title: '产品编码',
      dataIndex: 'businessKey',
      key: 'businessKey',
      valueType: 'select',
      request: () => productCodeEnum('02'),
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_, row) => {
        return (
          <Link to={`/businessMng/product-lease-detail?productCode=${row?.businessKey}`}>
            {row?.businessKey}
          </Link>
        );
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      valueType: 'select',
      request: () => getProductNameEnum('02'),
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '产品名称',
      dataIndex: 'name',
      search: false,
      width: 350,
    },
    {
      title: '利率',
      dataIndex: 'interest',
      renderFormItem: (_: any, { type, defaultRender, ...rest }: any) => {
        return (
          <RegionInputInterest
            minValueReplace="startInterest"
            maxValueReplace="endInterest"
            {...rest}
            className={globalStyle.w100}
          />
        );
      },
      render: (_, row) => {
        return row?.interest ? (
          <span>{new BigNumber(row.interest).times(100).toString()}%</span>
        ) : (
          <>-</>
        );
      },
    },
    {
      title: '期数',
      dataIndex: 'periods',
      key: 'periods',
      search: false,
      render: (_, row) => {
        return row?.periodsList ? row?.periodsList?.join('/') : '-';
      },
    },
    {
      title: '是否担保',
      dataIndex: 'guaranteeType',
      valueType: 'select',
      valueEnum: GUARANTEE_TYPES,
    },
    {
      title: '担保期数',
      dataIndex: 'guaranteePeriods',
      key: 'guaranteePeriods',
    },
    {
      title: '状态',
      dataIndex: 'status',
      initialValue: '1',
      valueEnum: {
        0: { text: '禁用', status: 'Error' },
        1: { text: '启用', status: 'Success' },
      },
    },
  ];

  /**
   *  渠道信息表格
   */
  const channelColumns: ProColumns<TChannelTableItem>[] = [
    {
      title: '渠道编码',
      dataIndex: 'businessKey',
    },
    {
      title: '渠道类型',
      dataIndex: 'channelType',
      renderText: (val: number) => {
        return CHANNEL_TYPES_MAP[val] || '-';
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      renderText: (val: number) => {
        switch (val) {
          case 0:
            return '禁用';
          case 1:
            return '启用';
          case -1:
            return '删除';
          default:
            return '-';
        }
      },
    },
    {
      title: '操作',
      valueType: 'option',
      dataIndex: 'city',
      width: 60,
      fixed: 'right',
      render: (_, { id }: { id: number }) => {
        return (
          <Button
            type="link"
            danger
            key="channelDel"
            style={{ padding: 0 }}
            onClick={() => {
              setCurDelChannelId(id);
              setChannelVisible(true);
            }}
          >
            删除
          </Button>
        );
      },
    },
  ];

  /**
   *  车辆信息表格
   */
  const carInfoColumns: ProColumns<TCarInfoTableItem>[] = [
    {
      title: '车辆编码',
      dataIndex: 'businessKey',
      key: 'carKey',
      valueType: 'select',
      request: () => findQuery({ businessKey: 'businessKey' }),
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      search: {
        transform: (value: any) => ({
          businessKey: value,
        }),
      },
      render: (_, row) => {
        return (
          <Link
            to={`/sysMng/dataDiction/carModelLibrary?carNo=${row?.businessKey}&carType=${row?.carType}`}
          >
            {row?.businessKey}
          </Link>
        );
      },
    },
    {
      title: '车辆类型',
      dataIndex: 'carType',
      renderText: (val) => (val === 1 ? '新车' : '二手车'),
      search: false,
    },
    {
      title: '车辆名称',
      dataIndex: 'name',
      key: 'name',
      valueType: 'select',
      request: () => findQuery({ carName: 'carName' }),
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_, row) => {
        return row?.name || '-';
      },
    },
    {
      title: '能源类型',
      dataIndex: 'energyType',
      valueEnum: ENERGY_TYPE_MAP,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '价格',
      dataIndex: 'totalPrice',
      search: false,
    },
    {
      title: '库存城市',
      dataIndex: 'carCityList',
      renderText: (val) => {
        if (Array.isArray(val) && val.length > 0) {
          return val.join(',');
        }
        return '-';
      },
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (val: any) => {
        switch (val) {
          case 0:
            return '禁用';
          case 1:
            return '启用';
          case -1:
            return '删除';
          default:
            return '-';
        }
      },
    },
    {
      title: '操作',
      dataIndex: 'city',
      valueType: 'option',
      width: 160,
      fixed: 'right',
      render: (_, { id }) => [
        <ConfigModelButton
          buttonFileProps={{ type: 'link' }}
          configModelButtonParams={{
            buttonText: '配置库存城市',
            children: InventoryCityTransfer,
            getHasConfigList: () =>
              getChannelList({
                parentId: id,
                whetherHasConfig: true,
                businessType: 'CITY',
                current: 1,
                pageSize: 2000,
              }),
            getNotConfigList: () =>
              getChannelList({
                parentId: id,
                whetherHasConfig: false,
                businessType: 'CITY',
                current: 1,
                pageSize: 2000,
              }),
            saveOrDelConfigInfo: (params) =>
              saveOrDelConfigInfo({
                ...params,
                parentId: id,
                businessType: 'CITY',
              }),
            refresh: () => actionCarRef?.current?.reload(),
          }}
        />,
        <Button
          type="link"
          danger
          key="carlDel"
          style={{ padding: 0 }}
          onClick={() => {
            setCurDelCarId(id);
            setCarVisible(true);
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <div style={{ width: '100%', overflowX: 'scroll', overflowY: 'hidden' }}>
          <ProCard ghost gutter={8}>
            <ProCard bordered>
              <div className={styles['tool-bar-container']} />
              <ProTable<TProductTableItem>
                rowKey="businessKey"
                scroll={{ x: 'max-content' }}
                // search={false}
                search={{ span: 12, layout: 'horizontal' }}
                columns={productColumns}
                // toolBarRender={false}
                request={({ interest, ...rest }) => {
                  const interestTemp: any = {};
                  if (interest) {
                    const { startInterest, endInterest } = interest;
                    if (startInterest || startInterest === 0) {
                      interestTemp.startInterest = Number(new BigNumber(startInterest).div(100));
                    }
                    if (endInterest || endInterest === 0) {
                      interestTemp.endInterest = Number(new BigNumber(endInterest).div(100));
                    }
                  }
                  return getProductList({
                    ...rest,
                    ...interestTemp,
                    secondaryClassification: 'FINANCE_LEASE',
                  });
                }}
                rowSelection={{
                  type: 'radio',
                  onChange: (selectedRowKeys, selectedRows) => {
                    setProductName(selectedRows[0].name);
                    setProductId(selectedRows[0].businessKey);
                    setCurrentLicenseType(LICENSE_TYPES[selectedRows[0].registerLicenceType] || '');
                    actionChannelRef?.current?.clearSelected?.();
                  },
                }}
                tableAlertRender={false}
                formRef={formRef}
                options={false}
                params={{ registerLicenceType: LICENSE_TYPES_MAP_ENUM[licenseType] }}
                headerTitle={
                  <LicenseTabsCom
                    defaultVisible={true}
                    tabsRef={licenseTabsRef}
                    onChange={(val) => {
                      setLicenseType(val as string);
                      formRef?.current?.submit();
                    }}
                  />
                }
              />
            </ProCard>
            <ProCard bordered>
              {/* <div className={styles['tool-bar-container']}> */}
              <div>
                <DividerTit title={`产品名称：${productName || '-'}`} />
              </div>
              <div>
                <DividerTit title={`上牌类型：${LICENSE_TYPES_MAP[currentLicenseType] || '-'}`} />
              </div>
              {/* </div> */}
              <ProTable<TChannelTableItem>
                rowKey="id"
                actionRef={actionChannelRef}
                scroll={{ x: 'max-content' }}
                search={false}
                columns={channelColumns}
                options={false}
                toolBarRender={() => [
                  <ConfigModelButton
                    buttonFileProps={{ type: 'primary', disabled: !productId }}
                    configModelButtonParams={{
                      buttonText: '配置渠道',
                      titleDes: (
                        <div style={{ color: 'red', marginBottom: 8 }}>
                          注：已根据产品对应上牌类型，限制可选渠道范围
                        </div>
                      ),
                      children: ChannelTransfer,
                      disabledOptions: { key: 'licenseTypeList', val: currentLicenseType },
                      getHasConfigList: () =>
                        getChannelList({
                          productCode: productId,
                          businessType: 'CHANNEL',
                          whetherHasConfig: true,
                          current: 1,
                          pageSize: 2000,
                        }),
                      getNotConfigList: () =>
                        getChannelList({
                          productCode: productId,
                          businessType: 'CHANNEL',
                          whetherHasConfig: false,
                          current: 1,
                          pageSize: 2000,
                        }),
                      saveOrDelConfigInfo: (params) =>
                        saveOrDelConfigInfo({
                          ...params,
                          businessType: 'CHANNEL',
                          productCode: productId,
                        }),
                      refresh: () => actionChannelRef?.current?.reload(),
                    }}
                  />,
                ]}
                params={{ productCode: productId }}
                request={(params) => {
                  if (productId) {
                    return getChannelList({
                      ...params,
                      businessType: 'CHANNEL',
                      whetherHasConfig: true,
                    });
                  }
                  return Promise.resolve({
                    data: [],
                    success: true,
                  });
                }}
                rowClassName={(record) => {
                  if (record?.status === STATUS.DEL || record?.status === STATUS.DISABLE) {
                    return styles['disable-row'];
                  }
                  return '';
                }}
                rowSelection={{
                  type: 'radio',
                  onChange: (selectedRowKeys, selectedRows) => {
                    setChannelId(selectedRows[0]?.id);
                    setChannelName(selectedRows[0]?.name);
                  },
                  // getCheckboxProps: (record) => {
                  //   return {
                  //     disabled: record?.status === STATUS.DEL || record?.status === STATUS.DISABLE, // 不为待还款的不允许选中 ,实际还款人=第三方代偿
                  //   };
                  // },
                }}
                tableAlertRender={false}
              />
            </ProCard>
            <ProCard bordered>
              {/* <div className={styles['tool-bar-container']}> */}
              <DividerTit title={`渠道名称：${channelName || '-'}`} />
              {/* </div> */}
              <ProTable<TCarInfoTableItem>
                rowKey="id"
                actionRef={actionCarRef}
                scroll={{ x: 'max-content' }}
                columns={carInfoColumns}
                // toolBarRender={false}
                search={{ layout: 'horizontal', span: 12 }}
                options={false}
                rowClassName={(record) => {
                  if (record?.status === STATUS.DEL || record?.status === STATUS.DISABLE) {
                    return styles['disable-row'];
                  }
                  return '';
                }}
                toolBarRender={() => {
                  return [
                    <ConfigModelButton
                      buttonFileProps={{
                        type: 'primary',
                        disabled: !channelId,
                      }}
                      configModelButtonParams={{
                        buttonText: '配置车辆',
                        titleDes: (
                          <div style={{ color: 'red', marginBottom: 8 }}>
                            注：已根据产品对应上牌类型，限制可选车辆范围
                          </div>
                        ),
                        children: CarInfoTransfer,
                        disabledOptions: { key: 'licenseType', val: currentLicenseType },
                        getHasConfigList: () =>
                          getCarList({
                            parentId: channelId,
                            whetherHasConfig: true,
                            businessType: 'CAR',
                            current: 1,
                            pageSize: 2000,
                          }),
                        getNotConfigList: () =>
                          getCarList({
                            parentId: channelId,
                            whetherHasConfig: false,
                            businessType: 'CAR',
                            current: 1,
                            pageSize: 2000,
                          }),
                        saveOrDelConfigInfo: (params) =>
                          saveOrDelConfigInfo({
                            ...params,
                            businessType: 'CAR',
                            parentId: channelId,
                          }),
                        refresh: () => actionCarRef?.current?.reload(),
                      }}
                    />,
                  ];
                }}
                params={{ parentId: channelId }}
                request={(params) => {
                  if (channelId) {
                    return getCarList({
                      ...params,
                      parentId: channelId,
                      whetherHasConfig: true,
                      businessType: 'CAR',
                    });
                  }
                  return Promise.resolve({
                    data: [],
                    success: true,
                  });
                }}
              />
            </ProCard>
          </ProCard>
        </div>
        {/* 删除渠道配置的弹窗 */}
        <ModalForm
          title="提示"
          width="400px"
          modalProps={{
            centered: true,
            destroyOnClose: true,
          }}
          visible={channelVisible}
          onVisibleChange={setChannelVisible}
          onFinish={async () => {
            await saveOrDelConfigInfo({
              deleteIds: [curDelChannelId],
              businessType: 'CHANNEL',
            });
            message.success('删除成功！');
            setChannelVisible(false);
            if (curDelChannelId === channelId) {
              setChannelId(NaN);
              setChannelName('');
            }
            actionChannelRef?.current?.reload();
          }}
        >
          <>
            <ExclamationCircleOutlined style={{ color: 'red', marginRight: '10px' }} />
            <span>删除后该渠道无法选择此产品进件，配置的车辆数据也将丢失，请谨慎操作!</span>
            <br />
            <br />
            <div>是否确认删除?</div>
          </>
        </ModalForm>
        {/* 删除车辆配置的弹窗 */}
        <ModalForm
          title="提示"
          width="400px"
          modalProps={{
            centered: true,
            destroyOnClose: true,
          }}
          visible={carlVisible}
          onVisibleChange={setCarVisible}
          onFinish={async () => {
            await saveOrDelConfigInfo({
              deleteIds: [curDelCarlId],
              businessType: 'CAR',
            });
            message.success('删除成功！');
            setCarVisible(false);
            actionCarRef?.current?.reload();
          }}
        >
          <>
            <ExclamationCircleOutlined style={{ color: 'red', marginRight: '10px' }} />
            <span>删除后该车辆无法选择此产品进件，配置的库存城市数据也将丢失，请谨慎操作！</span>
            <br />
            <br />
            <div>是否确认删除?</div>
          </>
        </ModalForm>
      </PageContainer>
    </>
  );
};

export default ProductLaunch;
