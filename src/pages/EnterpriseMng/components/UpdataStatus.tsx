/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-06 16:20:56
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-06 17:41:29
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/UpdataStatus.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { CommonImageUpload } from '@/components/ReleaseCom';
import { convertUploadFileList } from '@/utils/utils';
import { Button, Form, Input, message, Modal, Radio, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { modifyAccountStatus } from '../service';
import StatusModifyLogsTable from './StatusModifyLogsTable';
interface UpdataStatusProps {
  statusModalVisible: boolean;
  handleModalVisible: (visible: boolean) => void;
  currentData: Record<string, any>;
  refresh: () => void;
}
const UpdataStatus: React.FC<UpdataStatusProps> = ({
  statusModalVisible,
  handleModalVisible,
  currentData,
  refresh,
}) => {
  const [allFileList, handleFileList] = useState({});
  const [activeKey, setActiveKey] = useState<string>('1');
  const [statusModifyForm] = Form.useForm();
  const [formSubmitLoading, setFormSubmitLoading] = useState<boolean>(false);
  const mapFileList = (allFile: any) => {
    handleFileList({ ...allFileList, ...allFile });
  };

  const handleConfirm = (values: any) => {
    const params = {
      subAccountNo: currentData?.subAccountNo,
      userNo: currentData?.userNo,
      token: `${new Date().getTime()}${Math.floor(Math.random() * 1000)}`, // 模仿
      ...values,
    };
    setFormSubmitLoading(true);
    modifyAccountStatus(params)
      .then(() => {
        message.success('操作成功');
        handleModalVisible(false);
        refresh();
      })
      .finally(() => {
        setFormSubmitLoading(false);
      });
  };

  useEffect(() => {
    statusModifyForm.setFieldsValue({
      operationType: currentData?.status ? 0 : 1,
      productCode: currentData?.productCode,
      productSecondTypeName: currentData?.productSecondTypeName,
      remark: '',
      enclosure: [],
    });
    setActiveKey('1');
  }, [currentData]);
  return (
    <Modal
      title="状态变更"
      width="70%"
      forceRender
      open={statusModalVisible}
      footer={null}
      // onOk={() => handleModalVisible(false)}
      // onCancel={() => handleModalVisible(false)}
      onCancel={() => {
        handleModalVisible(false);
        handleFileList({});
      }}
      // afterClose={() => {
      //   handleFileList({});
      // }}
      destroyOnClose={true}
      maskClosable={false}
    >
      <Tabs
        type="card"
        activeKey={activeKey}
        onTabClick={(key) => {
          setActiveKey(key);
        }}
      >
        <Tabs.TabPane tab="变更" key="1">
          <Form
            form={statusModifyForm}
            labelCol={{ span: 2 }}
            wrapperCol={{ span: 16 }}
            onFinish={async (values) => {
              try {
                // 转换数据为后端需要的格式，
                const mapUploadFile = convertUploadFileList(allFileList, ['enclosure']);
                // console.log(mapUploadFile);
                // return;
                await handleConfirm({
                  ...values,
                  ...mapUploadFile,
                });
                // eslint-disable-next-line no-empty
              } catch (error) {
                message.success('稍后重试');
              }
              handleFileList({});
              return true;
            }}
            autoComplete="off"
          >
            <Form.Item label="产品ID" name="productCode" required>
              <Input disabled />
            </Form.Item>

            <Form.Item label="产品名称" name="productSecondTypeName" required>
              <Input disabled />
            </Form.Item>
            <CommonImageUpload
              extra="支持扩展名：.doc.docx.txt.zip.pdf.png.jpg.jpeg.gif.xls.xlsx"
              label="附件"
              name="enclosure"
              max={5}
              listType="text"
              size={10}
              fileListEdit={[]}
              mapFileList={mapFileList}
              accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
            />
            <Form.Item label="状态" name="operationType" required>
              <Radio.Group>
                <Radio value={0} disabled={currentData?.status === 0}>
                  冻结
                </Radio>
                <Radio value={1} disabled={currentData?.status === 1}>
                  激活
                </Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item label="备注" name="remark">
              <Input.TextArea maxLength={300} showCount rows={4} />
            </Form.Item>

            <Form.Item wrapperCol={{ offset: 2, span: 16 }}>
              <Button type="primary" htmlType="submit" loading={formSubmitLoading}>
                提交
              </Button>
            </Form.Item>
          </Form>
        </Tabs.TabPane>
        <Tabs.TabPane tab="变更日志" key="2">
          {currentData?.subAccountNo && (
            <StatusModifyLogsTable
              userNo={currentData?.userNo}
              subAccountNo={currentData?.subAccountNo}
            />
          )}
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  );
};

export default UpdataStatus;
