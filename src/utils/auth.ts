/* eslint-disable no-underscore-dangle */
/*
 * @Author: your name
 * @Date: 2022-02-14 17:41:27
 * @LastEditTime: 2024-10-29 18:28:14
 * @LastEditors: oak.yang <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/utils/auth.ts
 */
// import { getCookieItem, setCookieItem } from "@/utils/utils";

// import { getCookieItem, setCookieItem } from './utils';
import { logout } from '@/services/login';
import { ssoCreateLoginSign, ssoLogout } from '@/services/sso';
import { history } from '@umijs/max';
import { getVanEvn, isExternalNetwork, isStandbyLogin, removeStandbyLoginStorage } from './utils';

// api 系统标识
export const SYSTEM_FLAG_HEADER = 'system-flag';
export const SYSTEM_FLAG_HEADER_VALUE = 'bizAdmin';

// _hll_finance_stg,  _hll_finance, _hll_finance_pre-1, _hll_finance_local
export const getIdentifierKey = (): string => {
  let identifierKey = '_hll_finance';
  const domain = window.location.host;
  const match = domain.match(/-((stg)|(pre)|(gra)|(dev))/g);
  if (match) {
    identifierKey = (identifierKey + match[0].replace('-', '_')).replace('_pre', '_pre_1');
  } else if (domain.includes('localhost')) {
    identifierKey += '_local';
  }
  console.log(identifierKey);
  return identifierKey;
};
// 移除url中sso登录相关的query参数
export const filterSSOQueryParams = (url: string) => {
  const { searchParams, origin, pathname } = new URL(url);
  searchParams.delete('identifier');
  searchParams.delete('_t');
  searchParams.delete('account');
  searchParams.delete('_sign');
  searchParams.delete('login_client');
  searchParams.delete('_sign2');
  searchParams.delete('ssoLang');
  return searchParams?.size === 0
    ? `${origin}${pathname}`
    : `${origin}${pathname}?${searchParams.toString()}`;
};
export const getRootDomain = (): string => {
  let rootDomain = '';
  const domain = window.location.host;
  if (domain.includes('localhost') || domain.includes('xxx')) {
    rootDomain = window.location.hostname;
  } else {
    const temp = domain.split('.').reverse();
    rootDomain = `.${temp[1]}.${temp[0]}`;
  }
  return rootDomain;
};

const identifierKey = getIdentifierKey();
const rootDomain = getRootDomain();
// console.log(rootDomain);
export function getToken() {
  // return getCookieItem('token');
  const name = `${identifierKey}=`;
  const ca = document.cookie.split(';');
  for (let i: number = 0; i < ca.length; i += 1) {
    let c = ca[i];
    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}
// sso token、账号密码 token
export function removeToken() {
  if (getToken()) {
    document.cookie = `${identifierKey}=;path=/;domain=${rootDomain};expires=Thu, 01 Jan 1970 00:00:01 GMT`;
  }
  removePassportName();
}
export function setToken(identifier: string) {
  // document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
  document.cookie = `${identifierKey}=${identifier};path=/;domain=${rootDomain};`;
}
// 当前顶级域名下的sso cookie
const SSO_COOKIE_NAME = `_hll_identifier`;
export function removeSSOCookie() {
  try {
    document.cookie = `${SSO_COOKIE_NAME}=;path=/;expires=Thu, 01 Jan 1970 00:00:01 GMT`;
  } catch (e) {
    console.log(e);
  }
  try {
    removeSSOVanTaskID();
  } catch (e) {
    console.log(e);
  }
}
export function setSSOCookie(val: string) {
  // 子域名cookie
  document.cookie = `${SSO_COOKIE_NAME}=${val};path=/;Expires=${new Date(
    new Date().getTime() + 8 * 60 * 60 * 1000,
  ).toUTCString()};max-age=${8 * 60 * 60};`;
}
export function getSSOCookie() {
  // return getCookieItem('token');
  const name = `${SSO_COOKIE_NAME}=`;
  const ca = document.cookie.split(';');
  for (let i: number = 0; i < ca.length; i += 1) {
    let c = ca[i];
    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}
// 存储sso cookie，供接口请求使用
const SSO_API_COOKIE_NAME = `sso_api_cookie_biz`;
export function removeSSOAPICookie() {
  try {
    document.cookie = `${SSO_API_COOKIE_NAME}=;path=/;domain=${rootDomain};expires=Thu, 01 Jan 1970 00:00:01 GMT`;
  } catch (e) {
    console.log(e);
  }
}
export function setSSOAPICookie(val: string) {
  document.cookie = `${SSO_API_COOKIE_NAME}=${val};path=/;domain=${rootDomain};`;
}
export function getSSOAPICookie() {
  // return getCookieItem('token');
  const name = `${SSO_API_COOKIE_NAME}=`;
  const ca = document.cookie.split(';');
  for (let i: number = 0; i < ca.length; i += 1) {
    let c = ca[i];
    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}
// 飞书通行证名称
export function getPassportName() {
  return localStorage.getItem('passportName') || '';
}
export function setPassportName(passportName: string) {
  if (passportName) {
    localStorage.setItem('passportName', passportName);
  }
}
export function removePassportName() {
  localStorage.removeItem('passportName');
}
// 命中灰度版本号
export function getSSOVanTaskID() {
  return localStorage.getItem('sso_van_task_id_biz') || '';
}
export function setSSOVanTaskID(id: string) {
  if (id) {
    localStorage.setItem('sso_van_task_id_biz', id);
  }
}
export function removeSSOVanTaskID() {
  localStorage.removeItem('sso_van_task_id_biz');
}
// van sso 灰度请求头
export const SSOGrayVersionHeader: any = {
  ssoHeaders: {},
  setValue(data: any) {
    this.ssoHeaders = data || {};
  },
  getValue() {
    return this.ssoHeaders;
  },
};

// 存储当前用户pid
export function setPid(pid: string) {
  localStorage.setItem('pid', pid);
}
export function getPid() {
  return localStorage.getItem('pid') || '';
}
export function removePid() {
  return localStorage.removeItem('pid');
}

// sso初始化登录态，用作sso登录后的接口加请求头，区分新旧网关
export function setLoginType() {
  localStorage.setItem('loginType', '1');
}
export function getLoginType() {
  if (localStorage.getItem('loginType') === '1') {
    return 1;
  }
  return null;
}
export function removeLoginType() {
  localStorage.removeItem('loginType');
}

export function removeLoginStorage() {
  removeLoginType();
  removePid();
  removeToken();
  removeStandbyLoginStorage();
}
// 去sso授权登录
export const toSSOAuth = (url: string = window.location.href) => {
  ssoCreateLoginSign(url);
};
//
// 未登录去登录
export const toAuth = () => {
  try {
    removeSSOAPICookie();
  } catch (e) {
    console.log(e);
  }
  if (isExternalNetwork() || isStandbyLogin()) {
    // 去登录页账号密码登录
    const loginUrl = isStandbyLogin() ? '/user/login?type=standby' : '/user/login';
    removeLoginStorage();
    if (history.location.pathname !== '/user/login') {
      history.push(loginUrl);
    }
  } else if (window.location.pathname === '/user/login') {
    // 登录页sso登录，登录成功跳回dashboard，而不是登录页，避免造成sso无限循环
    removeLoginStorage();
    toSSOAuth(`${window.location.origin}/dashboard`);
  } else {
    // 非登录页sso登录， 登录成功跳回原页面
    removeLoginStorage();
    toSSOAuth();
  }
};
// 登出
export const toRemoveAuth = async () => {
  // const passportLoginTypeEnum: string = getLoginType() === 1 ? 'SSO' : 'PASSWORD';
  try {
    removeSSOAPICookie();
  } catch (e) {
    console.log(e);
  }
  const pid: string = getPid();
  // if (isStandbyLogin()) {
  //   return request('/auth/operator/login/logout', {
  //     method: 'POST',
  //   })
  //     .then(() => {
  //       removeLoginStorage();
  //       window.location.replace(`${window.location.origin}/user/login?type=standby`);
  //     })
  //     .catch(() => {
  //       removeLoginStorage();
  //       window.location.replace(`${window.location.origin}/user/login?type=standby`);
  //     });
  // }
  if (isExternalNetwork()) {
    // 去登录页账号密码登录
    return logout({
      pid,
      passportLoginTypeEnum: 'PASSWORD',
    })
      .then(() => {
        removeLoginStorage();
        window.location.replace(`${window.location.origin}/user/login`);
      })
      .catch(() => {
        removeLoginStorage();
        window.location.replace(`${window.location.origin}/user/login`);
      });
  }
  // sso退出登录
  const loginSuccessPage: string = window.location.href;
  return ssoLogout({ callbackUrl: loginSuccessPage })
    .then((res: any) => {
      const { logoutUrl, callbackUrl, time, appid, sign } = res.data;
      const { searchParams } = new URL(logoutUrl);
      searchParams.set('appid', appid);
      searchParams.set('_t', time);
      searchParams.set('_sign', sign);
      searchParams.set('callback', callbackUrl);
      removeLoginStorage();
      window.location.replace(`${logoutUrl}?${searchParams.toString()}`);
    })
    .catch(() => {
      removeLoginStorage();
      toAuth();
    });
};

interface XHllGrayVersionHeaderParams {
  pre: {
    'x-hll-gray-version'?: string;
  };
}
// 灰度多版本请求头
export const xHllGrayVersionHeader = {
  _XHllGrayVersionHeader: {
    pre: {},
  },
  setValue(data: XHllGrayVersionHeaderParams) {
    this._XHllGrayVersionHeader = data;
  },
  getValue() {
    const vanEnv = getVanEvn() || 'prd';
    if (vanEnv === 'pre') {
      return this._XHllGrayVersionHeader.pre || {};
    }
    return {};
  },
};

// 预发环境（pre/localhost）初始化多版本信息
export const xHllGrayVersionStorageKeyName = 'bizadmin-dev-version-info';
export const setDevXHllGrayVersionHeaderByStorage = () => {
  //
  const isPreOrLocal = getVanEvn() === 'pre' || window?.location?.hostname === 'localhost';
  // 判断环境: 非pre、localhost开发环境, 不根据本地缓存设置灰度请求头
  if (!isPreOrLocal) {
    return;
  }
  //
  try {
    const versionInfoString: any = localStorage.getItem(xHllGrayVersionStorageKeyName) || '{}';
    if (versionInfoString) {
      const { version, active } = JSON.parse(versionInfoString);
      console.log('setDevXHllGrayVersionHeaderByStorage', version, active);
      if (!version) return;
      if (active === true) {
        xHllGrayVersionHeader.setValue({
          pre: {
            'x-hll-gray-version': version,
          },
        });
      } else {
        xHllGrayVersionHeader.setValue({
          pre: {},
        });
      }
    }
  } catch (err) {
    // 解析出错，清空
    console.log('setDevXHllGrayVersionHeaderByStorage error: ', err);
    xHllGrayVersionHeader.setValue({
      pre: {},
    });
    localStorage.removeItem(xHllGrayVersionStorageKeyName);
  }
};

// 不走拦截器的其他请求(主要是上传接口)，获取授权请求头、多版本请求头
export const getAuthHeaders = (): any => {
  return {
    // 多版本
    ...xHllGrayVersionHeader.getValue(),
    // sso 灰度多版本请求头
    ...SSOGrayVersionHeader.getValue(),
    authorization: getToken(),
    loginType: getLoginType(),
    [SYSTEM_FLAG_HEADER]: SYSTEM_FLAG_HEADER_VALUE,
  };
};
