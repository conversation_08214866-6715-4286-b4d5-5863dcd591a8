.configForm {
  :global {
    .ant-form-item-label {
      width: 120px;
    }
  }
}

.btnBox {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-end;
  width: 100%;
}

.add-channel-count-modal {
  .ant-pro-form-group-container {
    justify-content: end;
    padding-right: 80px;
  }
}

.edit-channel-count-modal {
  .ant-pro-form-group-container {
    justify-content: center;
  }
}

.compensation-attach {
  color: #307ddc;

  .root-image-preview {
    & div:nth-last-child(1) {
      display: flex;
    }
  }
}

.claim-record-card {
  margin-top: 20px;

  .ant-card-body {
    position: relative;
    padding-top: 42px;
  }

  .claim-record-info {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 8px 24px 0;

    .claim-record-info-item {
      font-weight: 500;

      &:nth-child(1) {
        display: flex;
        align-items: center;
      }
    }
  }
}
