import { getSensitiveData } from '@/utils/sensitiveDataUtils';
import { EyeInvisibleOutlined, EyeOutlined, LoadingOutlined } from '@ant-design/icons';
import { Typography } from 'antd';
import React, { useMemo, useState } from 'react';
// import { canViewSensitiveData } from '@/utils/permission';

const { Text } = Typography;

type DataType = 'phone' | 'idNo' | 'email' | 'bank' | 'custom';

interface SensitiveDataProps {
  /** 脱敏或明文数据 */
  value: string;
  /** 点击后异步请求明文的函数或者参数对象 */
  fetchRawValue?: (() => Promise<string>) | { [key: string]: any };
  /** 数据类型，用于默认脱敏规则 */
  type?: DataType;
  /** 自定义脱敏规则 */
  customMask?: (value: string) => string;
  /** 是否可以直接查看明文(例如区分运营和渠道权限，可在此传参) */
  canView?: boolean;
  /** 是否隐藏小眼睛，不需要解密 */
  hideEyeOutlined?: boolean;
}

const maskRules: Record<DataType, (val: string) => string> = {
  phone: (val: string) => val.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2'),
  idNo: (val: string) => val.replace(/^(\d{4})\d{10}(\w{4})$/, '$1**********$2'),
  email: (val: string) => val.replace(/^(.{2}).+(@.+)$/, '$1****$2'),
  bank: (val: string) => val.replace(/^(\d{4})\d+(\d{4})$/, '$1 **** **** $2'),
  custom: (val: string) => val,
};

const SensitiveData: React.FC<SensitiveDataProps> = ({
  value,
  fetchRawValue,
  type = 'custom',
  customMask,
  hideEyeOutlined,
  canView,
}) => {
  const [visible, setVisible] = useState(false);
  const [rawValue, setRawValue] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const hasPermission = true; //  后续全局权限扩展准备

  const isMasked = value.includes('*'); // 简单判断是否是脱敏值

  const getMasked = () => {
    if (type === 'custom' && customMask) {
      return customMask(value);
    }
    const mask = maskRules[type];
    return mask ? mask(value) : value;
  };

  const handleToggle = async () => {
    if (!hasPermission) return;

    // 如果当前是隐藏状态，且没加载过明文，传入函数fetchRawValue则覆写，参数则默认调取getSensitiveData
    if (!visible && isMasked && fetchRawValue && !rawValue) {
      setLoading(true);
      try {
        if (typeof fetchRawValue === 'function') {
          const realVal = await fetchRawValue();
          setRawValue(realVal);
        } else {
          const realVal = await getSensitiveData(fetchRawValue);
          setRawValue(realVal);
        }
      } catch (e) {
        console.error('获取敏感信息失败', e);
      } finally {
        setLoading(false);
      }
    }

    setVisible(!visible);
  };

  const displayValue = useMemo(() => {
    if (!hasPermission) return getMasked();
    if (visible || canView) return rawValue || value;
    return isMasked ? value : getMasked();
  }, [visible, rawValue, value, hasPermission, canView]);

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <Text style={{ marginRight: 5 }}>{displayValue}</Text>
      {hasPermission && !hideEyeOutlined && (
        <>
          {loading ? (
            <LoadingOutlined spin style={{ cursor: 'not-allowed' }} />
          ) : visible ? (
            <EyeInvisibleOutlined onClick={handleToggle} style={{ cursor: 'pointer' }} />
          ) : (
            <EyeOutlined onClick={handleToggle} style={{ cursor: 'pointer' }} />
          )}
        </>
      )}
    </div>
  );
};

export default SensitiveData;
