import { SECONDARY_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_MAP_ALL } from '@/enums';
import optimizationModalWrapper from '@/utils/optimizationModalWrapper';
import { ExclamationCircleOutlined } from '@ant-design/icons'; // @ts-ignore
import type { ActionType } from '@ant-design/pro-components'; // @ts-ignore
import { ModalForm, ProFormTextArea } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Alert, Button, Descriptions, message, Space, Spin, Table, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import BigNumber from 'bignumber.js';
import type { MutableRefObject } from 'react';
import React, { memo, useState } from 'react';
import RepayAttach from '../../components/RepayAttach';
import { PAY_TYPE } from '../../const';
import { applyOrderInfo, submitBillAudit } from '../services';
import type { IbillRspDTOItem } from '../types';
import {
  IapplyOrderInfo,
  IofflineRepayReviewItem,
  IstatusEnCode,
  payStatusMap,
  repayStatusMap,
} from '../types';
import './index.less';

type Props = {
  record?: IofflineRepayReviewItem;
  collectRepayReviewOpen: boolean;
  setCollectRepayReviewOpen: (val: boolean) => void;
  actionRef: MutableRefObject<ActionType | undefined>;
};

const ACTUALREPAYROL_EMAP = {
  //  实际还款方
  '1': '用户本人',
  '2': '第三方',
};

// 是否代偿
const COMPENSATION_MAP = { 0: '否', 1: '是' };

// 线上还款的详情弹窗（收银台支付，回款余额支付）
const OnlineRepayReviewModal: React.FC<Props> = (props) => {
  const { record, collectRepayReviewOpen, setCollectRepayReviewOpen, actionRef } = props;
  const [loading, setLoading] = useState(false);
  const [applyOrderInfoData, setApplyOrderInfoData] = useState<IapplyOrderInfo>();
  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const [modalType, setModalType] = useState<number>(0); // 0: 驳回弹窗；1：审核通过弹窗
  const access = useAccess();

  async function getApplyOrderInfo() {
    try {
      if (record?.businessNo) {
        setLoading(true);
        const data: any = await applyOrderInfo({ businessNo: record?.businessNo });
        try {
          if (data?.payRspInfo) {
            data.payRspInfo = JSON.parse(data?.payRspInfo);
          }
          data.remissionList.unshift(getAmount(data));
        } catch (e) {}
        console.log('data', data);
        setApplyOrderInfoData(data);
        setCollectRepayReviewOpen(true);
      }
    } finally {
      setLoading(false);
    }
  }
  // 是否隐藏期数
  // 车险分期、账单维度为总账，隐藏
  const hideTermNumber =
    applyOrderInfoData?.secondProductCode === SECONDARY_CLASSIFICATION_CODE?.CAR_INSURANCE &&
    applyOrderInfoData?.billRspDTOList?.[0]?.dimension === 3;

  const OnlineRepayReviewModalColumns: ColumnsType<IbillRspDTOItem> = [
    {
      title: '账单ID',
      dataIndex: 'billNo',
    },
    {
      title: '期数',
      dataIndex: 'termNumber',
      hidden: hideTermNumber,
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },
    {
      title: '车辆识别代码',
      dataIndex: 'subjectMatterNo',
      render(_, record) {
        return (
          <Tooltip
            title={
              <div>
                {record?.subjectMatterNoList?.map((subjectMatterNo) => {
                  return <div>{subjectMatterNo}</div>;
                })}
              </div>
            }
          >
            <div
              style={{
                width: 200,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {record.subjectMatterNo}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '账单状态',
      dataIndex: 'statusName',
    },
    {
      title: '应还日期',
      dataIndex: 'dueDate',
    },
    {
      title: '应还总额',
      dataIndex: 'totalAmountDue',
    },
    {
      title: '应还本金',
      dataIndex: 'totalPrincipalDue',
    },
    {
      title: '应还利息',
      dataIndex: 'totalInterestDue',
    },

    {
      title: '逾期罚息',
      dataIndex: 'totalOverduePenaltyDue',
    },
    {
      title: '还款金额（元）',
      dataIndex: 'expectRepayAmount',
      render: (_, record: any) => {
        return record?.expectRepayAmount || '-';
      },
    },
    {
      title: '客户名称',
      dataIndex: 'accountName',
    },
    {
      title: '未还总额',
      dataIndex: 'totalAmountUnpaid',
    },
    {
      title: '未还本金',
      dataIndex: 'totalPrincipalUnpaid',
    },
    {
      title: '未还利息',
      dataIndex: 'totalInterestUnpaid',
    },
    {
      title: '未还罚息',
      dataIndex: 'totalOverduePenaltyUnpaid',
    },
    {
      title: '未还违约金',
      dataIndex: 'totalBreach',
    },
    {
      title: '未还滞纳金',
      dataIndex: 'totalLate',
    },
    {
      title: '应还违约金',
      dataIndex: 'totalBreachDue',
    },

    {
      title: '应还滞纳金',
      dataIndex: 'totalLateDue',
    },
    {
      title: '更新日期',
      dataIndex: 'updatedAt',
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDateNumber',
    },
  ];

  // 计算各项减免金额上限
  function getAmount(originData?: IapplyOrderInfo) {
    const init = {
      totalPrincipalUnpaid: 0,
      totalInterestUnpaid: 0,
      totalOverduePenaltyUnpaid: 0,
      totalBreach: 0,
      totalLate: 0,
    };
    if (originData?.billRspDTOList?.length) {
      const result = originData?.billRspDTOList?.reduce((pre: any, cur) => {
        const _totalPrincipalUnpaid = new BigNumber(pre.totalPrincipalUnpaid)
          .plus(cur.totalPrincipalUnpaid)
          .toNumber();
        const _totalInterestUnpaid = new BigNumber(pre.totalInterestUnpaid)
          .plus(cur.totalInterestUnpaid)
          .toNumber();
        const _totalOverduePenaltyUnpaid = new BigNumber(pre.totalOverduePenaltyUnpaid)
          .plus(cur.totalOverduePenaltyUnpaid)
          .toNumber();
        const _totalBreach = new BigNumber(pre.totalBreach).plus(cur.totalBreach).toNumber();
        const _totalLate = new BigNumber(pre.totalLate).plus(cur.totalLate).toNumber();
        return {
          totalPrincipalUnpaid: isNaN(_totalPrincipalUnpaid) ? 0 : _totalPrincipalUnpaid,
          totalInterestUnpaid: isNaN(_totalInterestUnpaid) ? 0 : _totalInterestUnpaid,
          totalOverduePenaltyUnpaid: isNaN(_totalOverduePenaltyUnpaid)
            ? 0
            : _totalOverduePenaltyUnpaid,
          totalBreach: isNaN(_totalBreach) ? 0 : _totalBreach,
          totalLate: isNaN(_totalLate) ? 0 : _totalLate,
        };
      }, init);
      return {
        remissionPrincipal: result.totalPrincipalUnpaid.toFixed(2),
        remissionInterest: result.totalInterestUnpaid.toFixed(2),
        remissionPenalty: result.totalOverduePenaltyUnpaid.toFixed(2),
        remissionAdvanceSettleLiquidatedDamages: result.totalBreach.toFixed(2),
        remissionDelayAmount: result.totalLate.toFixed(2),
      };
    } else {
      return {
        remissionPrincipal: '0.00',
        remissionInterest: '0.00',
        remissionPenalty: '0.00',
        remissionAdvanceSettleLiquidatedDamages: '0.00',
        remissionDelayAmount: '0.00',
      };
    }
  }

  return (
    <>
      <ModalForm
        title={<div className="modal_tit">催收回款</div>}
        open={collectRepayReviewOpen}
        onOpenChange={(val) => {
          if (val) getApplyOrderInfo();
        }}
        width={1000}
        modalProps={{
          destroyOnClose: true,
          maskClosable: true,
          onCancel: () => {
            setCollectRepayReviewOpen(false);
          },
        }}
        submitter={{
          render() {
            return (
              // 只有待审批才展示
              applyOrderInfoData?.status === IstatusEnCode.PendingApproval && (
                <Space>
                  <Button
                    type="primary"
                    danger
                    onClick={() => {
                      setModalType(0);
                      handleModalVisible(true);
                    }}
                  >
                    驳回
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      setModalType(1);
                      handleModalVisible(true);
                    }}
                  >
                    审核通过
                  </Button>
                </Space>
              )
            );
          },
        }}
      >
        <Spin spinning={loading}>
          <div className="block_area">
            <Descriptions bordered column={2} size={'small'}>
              <Descriptions.Item span={2} label={<div className="label_width">还款单号</div>}>
                {applyOrderInfoData?.businessNo || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">业务线</div>}>
                {SECONDARY_CLASSIFICATION_MAP_ALL[
                  applyOrderInfoData?.secondProductCode as string
                ] || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">还款单金额</div>}>
                {applyOrderInfoData?.repayAmount || '-'} 元
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">支付方式</div>}>
                {PAY_TYPE?.[applyOrderInfoData?.remitType as keyof typeof PAY_TYPE] || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">还款凭证</div>}>
                {applyOrderInfoData?.attachList ? (
                  <a
                    onClick={() =>
                      optimizationModalWrapper(RepayAttach)({
                        imgList: applyOrderInfoData?.attachList,
                      })
                    }
                  >
                    查看
                  </a>
                ) : (
                  '-'
                )}
              </Descriptions.Item>
              {/* 车险不展示融租渠道 */}
              {applyOrderInfoData?.secondProductCode ===
                SECONDARY_CLASSIFICATION_CODE?.FINANCE_LEASE_SECOND && (
                <Descriptions.Item span={1} label={<div className="label_width">融租渠道</div>}>
                  {applyOrderInfoData?.channelName || '-'}
                </Descriptions.Item>
              )}
              <Descriptions.Item
                span={1}
                label={<div className="label_width">是否第三方代偿</div>}
                style={{ whiteSpace: 'nowrap', minWidth: '130px' }}
              >
                {COMPENSATION_MAP[applyOrderInfoData?.compensation ?? '0'] || '否'}
              </Descriptions.Item>
              <Descriptions.Item
                span={2}
                label={<div className="label_width">三方还款流水号</div>}
                style={{ whiteSpace: 'nowrap', minWidth: '130px' }}
              >
                {applyOrderInfoData?.repaySerialNo || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">支付状态</div>}>
                {payStatusMap[applyOrderInfoData?.payStatus as string] || '-'}
              </Descriptions.Item>
              <Descriptions.Item
                span={1}
                label={<div className="label_width">支付返回结果时间</div>}
                style={{ whiteSpace: 'nowrap', minWidth: '140px' }}
              >
                {applyOrderInfoData?.creditDate || '-'}
              </Descriptions.Item>
              <Descriptions.Item
                span={1}
                label={<div className="label_width">短信下发手机号</div>}
                style={{ whiteSpace: 'nowrap', minWidth: '130px' }}
              >
                {applyOrderInfoData?.notifyPhone || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">还款状态</div>}>
                {repayStatusMap[applyOrderInfoData?.repayStatus as number] || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">催收员</div>}>
                {applyOrderInfoData?.applyName || '-'}
              </Descriptions.Item>
              <Descriptions.Item span={1} label={<div className="label_width">创建时间</div>}>
                {applyOrderInfoData?.createdAt || '-'}
              </Descriptions.Item>
              {/* 车险不展示实际还款方 */}
              {applyOrderInfoData?.secondProductCode ===
                SECONDARY_CLASSIFICATION_CODE?.FINANCE_LEASE_SECOND && (
                <Descriptions.Item span={2} label={<div className="label_width">实际还款方</div>}>
                  {ACTUALREPAYROL_EMAP[applyOrderInfoData?.actualRepayRole as string] || '-'}
                </Descriptions.Item>
              )}
              <Descriptions.Item span={2} label={<div className="label_width">备注</div>}>
                {applyOrderInfoData?.remark || '-'}
              </Descriptions.Item>
            </Descriptions>
          </div>
          <div className="tit">账单信息:</div>
          <div className="block_area bill_info_wrao">
            <Table
              title={() => (
                <>
                  <ExclamationCircleOutlined
                    style={{ fontSize: 16, color: 'orange', marginRight: 5 }}
                  />
                  以下为操作还款时，账单快照数据。
                </>
              )}
              pagination={false}
              columns={OnlineRepayReviewModalColumns}
              dataSource={applyOrderInfoData?.billRspDTOList}
              scroll={{ x: 'max-content' }}
            />
          </div>
          {access.hasAccess('bill_apply_remission_info_postLoanMng_callpay') && (
            <>
              <div className="tit">减免信息:</div>
              <div className="block_area">
                <div className="line no_border">
                  <span className="repay_label">减免总金额:</span>
                  <span className="repay_amount">{applyOrderInfoData?.exemptionAmount || '-'}</span>
                  元
                </div>

                <Table
                  pagination={false}
                  dataSource={applyOrderInfoData?.remissionList}
                  columns={[
                    {
                      title: '费项',
                      render: (_, __, index) => {
                        if (!index) return '应还逾期金额';
                        else return '减免金额';
                      },
                    },
                    {
                      title: '本金(元)',
                      dataIndex: 'remissionPrincipal',
                    },
                    {
                      title: '利息(元)',
                      dataIndex: 'remissionInterest',
                    },
                    {
                      title: '罚息(元)',
                      dataIndex: 'remissionPenalty',
                    },
                    {
                      title: '违约金(元)',
                      dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
                    },
                    {
                      title: '滞纳金(元)',
                      dataIndex: 'remissionDelayAmount',
                    },
                  ]}
                />
              </div>
            </>
          )}
        </Spin>
      </ModalForm>
      <ModalForm
        title="催收回款审核"
        width="500px"
        layout="horizontal"
        open={modalVisible}
        onOpenChange={handleModalVisible}
        modalProps={{
          centered: true,
          destroyOnClose: true,
          okButtonProps: { loading: loading },
        }}
        onFinish={async (value) => {
          if (!applyOrderInfoData?.businessNo) {
            message.error('单号不存在');
          } else {
            setLoading(true);
            submitBillAudit({
              auditStatus: modalType ? 2 : 1,
              businessNo: applyOrderInfoData.businessNo,
              ...(!modalType && { auditRemark: value.errorMsg }), // 驳回原因
            })
              .then(() => {
                message.success('审批成功');
                handleModalVisible(false);
                setCollectRepayReviewOpen(false);
                actionRef.current?.reload();
              })
              .finally(() => {
                setLoading(false);
              });
          }
        }}
      >
        <Alert
          message={`确认${modalType ? '通过' : '驳回'}该笔催收回款？`}
          type="warning"
          showIcon
          style={{ marginBottom: 8 }}
        />
        {!modalType && (
          <ProFormTextArea
            rules={[{ required: true }, { whitespace: true }]}
            fieldProps={{ maxLength: 500 }}
            label="驳回原因"
            name="errorMsg"
          />
        )}
      </ModalForm>
    </>
  );
};
export default memo(OnlineRepayReviewModal);
