/*
 * @Author: your name
 * @Date: 2021-01-12 18:13:53
 * @LastEditTime: 2024-10-17 09:46:56
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/PassModel.ts
 */
import { isExternalNetwork } from '@/utils/utils';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { history, useModel } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import type { SubmitCallBackParams } from '../data';
import { submitCallBack } from '../service';
import { UploadCom } from './index';

export type SubmitCallBackModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  // values: CallPayForm|{};
  onVisibleChange: any;
  deadLine: string;
  overDueAmount: number;
};

const SubmitCallBackModal: React.FC<SubmitCallBackModalProps> = (props) => {
  const { urgePersonList: userList } = useModel('userList');
  const { overdueId } = history.location.query;
  const disabledDate = (current: any) => {
    return current && current < dayjs(props.deadLine).endOf('day');
  };

  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return (
    <>
      <ModalForm
        title="提交催收回款"
        width={600}
        layout="horizontal"
        initialValues={{
          urgePerson: currentUser?.userId,
        }}
        visible={props.modalVisible}
        onVisibleChange={props.onVisibleChange}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
        }}
        onFinish={async (value) => {
          const fileList = [];
          const length = value?.fileList?.length || 0;
          for (let i = 0; i < length; i += 1) {
            const item = value.fileList[i];
            if (item.response) {
              const url = item.response.data;
              if (url) {
                fileList.push({ fileUrl: url, fileName: item.name });
              }
            }
          }
          try {
            await submitCallBack({
              overdueId,
              ...value,
              fileList,
            } as SubmitCallBackParams);
            message.success('添加成功');
            props.onOk();

            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <ProFormSelect
          rules={[{ required: true }]}
          labelCol={{ span: 4 }}
          options={userList}
          placeholder="请输入催收员"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            disabled: isExternalNetwork(),
            // filterOption: (input: string, option: { label: string }) =>
            //   option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
          }}
          width="md"
          name="urgePerson"
          label="催收员"
        />
        <ProFormDigit
          name="amount"
          width="md"
          labelCol={{ span: 4 }}
          label="回款金额"
          fieldProps={{ min: 0, precision: 2 }}
          placeholder="请输入回款金额"
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 有坑，同时出现很多个error,待优化
                if (/^\d+(\.\d+)?$/.test(val) && val && val > props.overDueAmount) {
                  // callBack('回款金额不能大于逾期金额');
                  return Promise.reject(new Error('催收回款金额不能大于逾期金额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  // callBack();
                  return Promise.reject(new Error('请输入数字'));
                }

                // callBack();
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormDatePicker
          labelCol={{ span: 4 }}
          name="paymentTime"
          label="回款日期"
          fieldProps={{ disabledDate }}
          rules={[{ required: true }]}
          placeholder="请输入回款日期"
          width="md"
        />
        <ProFormText
          name="payBackType"
          labelCol={{ span: 4 }}
          width="md"
          fieldProps={{ maxLength: 20 }}
          label="回款方式"
          placeholder="请输入回款方式"
          rules={[{ required: true }]}
        />

        <ProFormTextArea
          labelCol={{ span: 4 }}
          width="md"
          fieldProps={{ maxLength: 500 }}
          label="备注"
          name="urgeRecentlyMsg"
        />
        <UploadCom />
      </ModalForm>
    </>
  );
};

export default SubmitCallBackModal;
