import LoadingButton from '@/components/LoadingButton';
import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_MAP_ALL } from '@/enums';
import { getProductNameEnum } from '@/services/enum';
import { billNoToOrderOrBillDetail, downLoadExcel, isExternalNetwork } from '@/utils/utils';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Link, useAccess, useModel } from '@umijs/max';
import { Button, message, Tabs } from 'antd';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { VList } from 'virtuallist-antd';
import { DispatchModal } from '../../Collection/components/index';
import type { OverdueItem } from '../data';
import { getOverdueList, overdueExport } from '../service';

// 待分配 的 表格列表
// 拆出来的原因：有pagesize的问题无法解决，更新时 分页配置失效。
// 待分配与其他也有一些区别，拆除来不用做很多判断了，逻辑清晰
// 使用了新的pro-components
const vNodeLimitHeight = 224;
const WaitDistributeTable: React.FC<any> = () => {
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const access = useAccess();
  const [dispatchModalVisible, handleDispatchModalVisible] = useState<boolean>(false);
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  // 表头的title需要展示
  const [total, setTotal] = useState<number>(0);
  const [currentRow, setCurrentRow] = useState<any>([]);

  const [productCodeValueEnum, setProductCodeValueEnum] = useState<Record<string, string>>({});

  // const [urgePersonValueEnum, setUrgePersonValueEnum] = useState({});

  const [activeKey, setActiveKey] = useState<string>('SMALL_LOAN');

  // 大数据量下还是得用虚拟列表 不仅是layout的减少 脚本的执行也大幅度减少
  // 在这个例子中尤为明显
  const [isBigData, setIsBigData] = useState(true);

  useEffect(() => {
    // 【催收员】相关代码关联功能未使用，暂时注释掉
    // getUserListEnum().then((data) => {
    //   const value = data.reduce((pre: any, cur: any) => {
    //     return { ...pre, [cur.value]: cur.label };
    //   }, {});
    //   setUrgePersonValueEnum(value);
    // });

    getProductNameEnum().then((data) => {
      setProductCodeValueEnum(
        data.reduce((pre, cur) => {
          return {
            ...pre,
            [cur.value]: cur.label,
          };
        }, {}),
      );
    });

    if (isExternalNetwork()) {
      formRef.current?.setFieldsValue({ urgePerson: currentUser?.userId });
    }
  }, [currentUser?.userId]);

  const columns: ProColumns<OverdueItem>[] = [
    {
      title: '订单号/账单号',
      dataIndex: 'billNo',
      width: 200,
      render: (_, row: OverdueItem) => (
        <Link
          to={billNoToOrderOrBillDetail({
            productCode: row.productCode,
            billNo: row.billNo,
            accountNumber: row.accountNumber,
          })}
        >
          {row.billNo}
        </Link>
      ),
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
      width: 200,
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
      width: 200,
    },

    {
      title: '产品二级分类',
      dataIndex: 'secondary',
      valueType: 'select',
      valueEnum: SECONDARY_CLASSIFICATION_MAP_ALL,
      hideInTable: true,
      fieldProps: {
        onChange: () => {
          formRef.current?.setFieldValue('productCode', undefined);
        },
      },
    },
    {
      title: '产品ID',
      dataIndex: 'productCode',
      search: false,
      width: 80,
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      valueEnum: () => {
        const productSecondCode = formRef.current?.getFieldValue('secondary');
        const productCodeMap: any = {};
        for (const productCode in productCodeValueEnum) {
          const secondCode = productCode.substring(0, 4);
          if (productSecondCode === secondCode) {
            productCodeMap[productCode] = productCodeValueEnum[productCode];
          }
        }
        return productSecondCode ? productCodeMap : productCodeValueEnum;
      },
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) => {
          return option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
        },
        onChange(val: string) {
          formRef.current?.setFieldValue('secondary', val.substring(0, 4));
        },
        onSelect: (val: string) => {
          if (
            val.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING ||
            !formRef.current?.getFieldValue('productCode')
          ) {
            formRef.current?.setFieldsValue({ repayPlanNo: '' });
          } else {
            formRef.current?.setFieldsValue({ billNo: '' });
          }
        },
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
      width: 100,
    },

    {
      title: '还款期数',
      dataIndex: 'termDetail',
      search: false,
      width: 100,
    },
    {
      title: '应还日期',
      dataIndex: 'repaymentDate',
      search: false,
      width: 100,
    },
    {
      title: '待还逾期金额（元）',
      dataIndex: 'overdueAmount',
      width: 180,
      search: false,
      sorter: true,
    },
    {
      title: '逾期等级',
      dataIndex: 'overdueLevel',
      sorter: true,
      width: 100,
      valueEnum: {
        M1: 'M1',
        M2: 'M2',
        M3: 'M3',
        M4: 'M4',
        M5: 'M5',
        M6: 'M6',
        'M6+': 'M6+',
      },
    },
    {
      title: '逾期天数',
      dataIndex: 'daysOverdue',
      search: false,
      width: 100,
      sorter: true,
    },
    // {
    //   title: '催收员',
    //   dataIndex: 'urgePerson',
    //   valueType: 'select',
    //   valueEnum: urgePersonValueEnum,
    //   search: false,
    //   fieldProps: {
    //     showSearch: true,
    //     optionFilterProp: 'label',
    //     disabled: isExternalNetwork(), // 外网禁止修改 只展示当前角色下的单子
    //     filterOption: (input: string, option: { label: string }) => {
    //       return option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
    //     },
    //   },
    //   hideInTable: true,
    //   render(_, record) {
    //     // 数据量大的情况下 执行的js时间很长 阻塞ui渲染
    //     return urgePersonValueEnum?.[record?.urgePerson as keyof typeof urgePersonValueEnum] || '-';
    //   },
    // },
    {
      title: '催收状态',
      dataIndex: 'urgeState',
      search: false,
      valueEnum: {
        20: '待跟进',
        30: '跟进中',
        40: '催收结案',
        50: '催收撤销',
      },
      hideInTable: true,
    },
    {
      title: '操作',
      width: 80,
      align: 'center',
      fixed: 'right',
      valueType: 'option',
      render: (_, row: OverdueItem) => (
        <>
          {access.hasAccess('biz_repay_no_dispatch') && (
            <a
              style={{ marginRight: '10px' }}
              onClick={() => {
                handleDispatchModalVisible(true);
                setCurrentRow([row.overdueId]);
              }}
            >
              派单
            </a>
          )}
        </>
      ),
    },
  ];

  // 重置模块内需要更新的state
  function clearEffect() {
    setTotal(0);
  }

  function getSortParams(sort: Record<string, string | null>) {
    const { overdueAmount, overdueLevel, daysOverdue } = sort;
    const newSort: Record<string, string> = {};

    // 待分配和已分配 排序功能
    // 待分配和已分配tab 业务切换功能
    const sortMap = {
      ascend: 'asc',
      descend: 'desc',
    };

    newSort.totalOverdueAmountSort = overdueAmount
      ? sortMap[overdueAmount as keyof typeof sortMap]
      : '';
    newSort.overdueMaxLevelSort = overdueLevel ? sortMap[overdueLevel as keyof typeof sortMap] : '';
    newSort.overdueDaySort = daysOverdue ? sortMap[daysOverdue as keyof typeof sortMap] : '';

    Object.keys(newSort).forEach((key) => {
      if (!newSort[key]) {
        delete newSort[key];
      }
    });
    return newSort;
  }

  // 将request提取出来，提高代码整洁性以及方便后续的逻辑调整
  async function tableRequest(params: any, sort: any) {
    // 添加二级tab分类key，小额贷款-融资租赁-商业保理
    params.classification = activeKey;
    // 会有初始值 催收员 外网情况下
    if (isExternalNetwork()) {
      if (!params?.urgePerson) {
        params.urgePerson = currentUser?.userId;
      }
    }

    // 处理排序
    const sortParams = getSortParams(sort);
    const data = await getOverdueList({
      ...params,
      ...sortParams,
      beAssigned: false,
    });
    setTotal(data?.total);
    return {
      data: data?.data,
      success: true,
      total: data?.total,
    };
  }

  const vComponents = useMemo(() => {
    // 使用VList 即可有虚拟列表的效果
    return isBigData
      ? VList({
          height: window.innerHeight - vNodeLimitHeight,
          resetTopWhenDataChange: false,
        })
      : null;
  }, [isBigData]);
  return (
    <>
      <ProTable<OverdueItem>
        name="overdueManagement"
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        request={tableRequest}
        pagination={{
          pageSizeOptions: [10, 20, 50, 100, 500],
          defaultPageSize: 500,
          onChange(_, pageSize) {
            setIsBigData(pageSize >= 100);
          },
        }}
        form={{
          style: { paddingTop: 0 },
        }}
        rowKey="overdueId"
        rowSelection={{
          onChange: (selectedRowKeys) => {
            setCurrentRow(selectedRowKeys);
          },
        }}
        components={vComponents}
        scroll={isBigData ? { y: window.innerHeight - vNodeLimitHeight } : { x: 'max-content' }}
        headerTitle={
          <div style={{ overflow: 'hidden' }}>
            <Tabs
              size="small"
              activeKey={activeKey}
              type="card"
              items={[
                { label: '小额贷款', key: 'SMALL_LOAN' },
                { label: '融资租赁', key: 'FINANCE_LEASE' },
                { label: '商业保理', key: 'SELLER_FACTORING' },
              ].map((item) => {
                const { label, key } = item;
                return {
                  label: label + (activeKey === key ? `(${total})` : ''),
                  key,
                };
              })}
              onChange={(key: string) => {
                setActiveKey(key);
                clearEffect();
                formRef.current?.submit();
              }}
            />
          </div>
        }
        search={{
          labelWidth: 100,
          defaultCollapsed: false,
        }}
        toolBarRender={() => {
          return [
            <>
              {access?.hasAccess('bulkOrder_overdueMng') && (
                <Button
                  key="dispatch"
                  onClick={() => {
                    if (currentRow.length === 0) {
                      message.warning('请至少选择一行数据！');
                      return;
                    }
                    handleDispatchModalVisible(true);
                  }}
                  type="primary"
                >
                  批量派单
                </Button>
              )}
            </>,
            access.hasAccess('biz_download') && (
              <LoadingButton
                key="button"
                type="primary"
                onClick={async () => {
                  const params = formRef?.current?.getFieldsFormatValue?.();
                  const res = await overdueExport({ ...params, beAssigned: false });
                  downLoadExcel(res);
                }}
              >
                导出
              </LoadingButton>
            ),
          ];
        }}
      />

      {/* 派单 */}
      <DispatchModal
        onOk={async () => {
          handleDispatchModalVisible(false);
          setCurrentRow([]);
          if (actionRef.current) {
            actionRef.current.reload();
            actionRef.current.clearSelected?.();
          }
        }}
        onCancel={() => {
          handleDispatchModalVisible(false);
          setCurrentRow([]);
        }}
        type={'1'}
        onVisibleChange={handleDispatchModalVisible}
        modalVisible={dispatchModalVisible}
        overdueCaseNo={currentRow}
      />
    </>
  );
};

export default memo(WaitDistributeTable);
