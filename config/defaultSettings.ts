/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2021-04-14 19:25:38
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/config/defaultSettings.ts
 */
import type { Settings as LayoutSettings } from '@ant-design/pro-components';

export default {
  navTheme: 'light',
  // 拂晓蓝
  colorPrimary: '#1890ff',
  layout: 'mix',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  siderWidth: 211,
  menu: {
    locale: false,
  },
  title: '小圆金科业务管理系统',
  pwa: false,
  logo: 'https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg',
  iconfontUrl: '',
} as LayoutSettings & {
  pwa: boolean;
};
