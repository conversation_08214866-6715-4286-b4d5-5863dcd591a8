import { getVanEvn } from '@/utils/utils';
import { message } from 'antd';
import axios from 'axios';

const ZXS_LIST = ['北京市', '天津市', '上海市', '重庆市'];
// 城市列表
let cityCache: any = null;
let cacheLevel: number | undefined = undefined;
const defaulLevel = 2;

let restry = 0; // 重试次数
const MAX_RETRY_NUM = 2;

function loopFun(item: any, inLabel: boolean, level: number) {
  const obj: any = {};
  obj.label = item.name;
  obj.value = inLabel ? `${item.name}_${item.adcode}` : item.adcode;
  if (item.children && item.children.length) {
    if (ZXS_LIST.indexOf(item.name) === -1 || level !== defaulLevel) {
      obj.children = item.children.map((son: any) => {
        return loopFun(son, inLabel, level);
      });
    }
  }
  return obj;
}

function getHost() {
  let baseURL = 'https://map-api-stg.huolala.cn';
  const vanEnv = getVanEvn();
  if (vanEnv) {
    const urlEvn = vanEnv === 'prd' ? '' : `-${vanEnv}`;
    baseURL = `https://map-api${urlEvn}.huolala.cn`;
  }
  return baseURL;
}

/**
 * 获取省市数据
 * @param inLabel 是否需要value中拼接上名字 true: 选择的value将为 [北京市_110000]
 */
export async function getCityListEnum(opts: { inLabel: boolean; level?: number }) {
  const host = getHost();
  const level = opts.level || defaulLevel;
  if (cityCache && cacheLevel === opts.level) {
    const citys = cityCache.map((item: any) => {
      return loopFun(item, opts.inLabel, level);
    });
    const ret = citys[0] && citys[0].children;
    return ret;
  }
  return axios
    .get(`${host}/lbs-map/geo/region/district`, {
      params: {
        adcode: 100000,
        subdistrict: level,
      },
    })
    .then((res) => {
      if (res && res.data.ret === 0) {
        restry = 0;
        const data = res.data.data || [];
        cityCache = data;
        cacheLevel = opts.level;
        const citys = data.map((item: any) => {
          return loopFun(item, opts.inLabel, level);
        });
        const ret = citys[0] && citys[0].children; // 从国家层级查询的数据 仅返回市级
        // console.log(ret)
        return ret;
      } else {
        if (restry < MAX_RETRY_NUM) {
          console.log('地图错误重试');
          restry++;
          setTimeout(() => {
            getCityListEnum(opts);
          }, 200);
        } else {
          restry = 0;
          message.warning('无法获取地图数据，请刷新页面或稍后重试！');
        }
      }
    })
    .catch(() => {
      console.log('地图catch');
      if (restry < MAX_RETRY_NUM) {
        console.log('地图catch重试');
        restry++;
        setTimeout(() => {
          getCityListEnum(opts);
        }, 200);
      } else {
        restry = 0;
        message.warning('无法获取地图数据，请刷新页面或稍后重试！');
      }
    });
}
