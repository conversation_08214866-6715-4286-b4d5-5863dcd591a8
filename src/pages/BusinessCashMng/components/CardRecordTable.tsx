/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-12-16 17:35:41
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2023-03-28 14:41:30
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessCashMng/components/CardRecordTable.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2021-11-08 14:44:33
 * @modify date 2021-11-08 14:44:33
 * @desc 企业用户-企业用户管理-产品数据-状态变更-变更日志
 */

import { reportLog } from '@/pages/Collection/services';
import {
  desensitizationBankAndIdCard,
  desensitizationPhone,
  isExternalNetwork,
} from '@/utils/utils';
import { useRequest } from '@umijs/max';
import { Popover, Table, Tag } from 'antd';
import type { ReactNode } from 'react';
import React from 'react';
import { getCardRecord } from '../service';

interface CardRecordTableProps {
  orderNo: string;
}

function statusRender(text: null | number): ReactNode {
  if (text !== null) {
    return <Tag color={text === 4 ? 'red' : 'green'}>{text === 4 ? '解绑' : '绑定'}</Tag>;
  }
  return <>-</>;
}

const CardRecordTable: React.FC<CardRecordTableProps> = (props) => {
  const { tableProps } = useRequest(
    () => {
      return getCardRecord(props.orderNo);
    },
    {
      paginated: true,
      defaultPageSize: 10,
      formatResult: (response: Record<string, any>) => {
        return { list: response.data, total: response.total };
      },
    },
  );

  return (
    <Table
      rowKey="id"
      columns={[
        {
          title: '银行',
          dataIndex: 'bankName',
        },
        {
          title: '银行卡号',
          dataIndex: 'cardNo',
          render(_, record) {
            const { cardNo } = record;
            if (!cardNo) return '-';
            return isExternalNetwork() ? desensitizationBankAndIdCard(cardNo) : cardNo;
          },
        },
        {
          title: '银行预留手机号',
          dataIndex: 'phone',
          render(_, record) {
            const { phone } = record;
            if (!phone) return '-';
            return isExternalNetwork() ? (
              <>
                {desensitizationPhone(phone)}
                <Popover content={_} trigger="click">
                  <a
                    onClick={async () => {
                      // 发送查看日志
                      if (phone) {
                        await reportLog(phone);
                      }
                    }}
                  >
                    查看
                  </a>
                </Popover>
              </>
            ) : (
              phone
            );
          },
        },
        {
          title: '操作',
          dataIndex: 'type',
          render: statusRender,
        },
        {
          title: '操作时间',
          dataIndex: 'updatedAt',
        },
      ]}
      {...tableProps}
    />
  );
};

export default React.memo(CardRecordTable);
