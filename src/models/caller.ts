/*
 * @Date: 2023-04-20 11:36:05
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-11-23 18:05:10
 * @FilePath: /lala-finance-biz-web/src/models/caller.ts
 * @Description:
 */
import { useState, useCallback } from 'react';
import dayjs from 'dayjs';

export default () => {
  const [outId, setTheOutId] = useState(''); // 催收单号
  const [callId, setCallId] = useState(''); // 通话 ID
  const [dialTime, setDialTime] = useState<null | string>(null); // 拨号时间
  const [dialAction, setAction] = useState(''); // 外呼事件标识
  const [isDialOuter, setIsDialOuter] = useState(false); // 是否外部拨号
  const [calledNumber, setCalledNumber] = useState(''); // 号码
  const [outerDialNumber, setTriggerDialOuterNum] = useState(''); // 触发外部拨号
  const [helpStr, setHelpStrFunc] = useState(
    JSON.stringify({
      scene: 'BUSINESS_TOP_CALL',
      productCode: 'LalaFinance_Collection',
      productName: '金融催收外呼',
    }),
  );

  // 结束拨号
  const dialOff = useCallback(() => {
    setTheOutId('');
    setCallId('');
    setDialTime(null);
    setAction('');
    setIsDialOuter(false);
    setCalledNumber('');
    setTriggerDialOuterNum('');
    // setHelpStrFunc('');
  }, []);

  // 开始拨号
  const dialStart = useCallback((data) => {
    setDialTime(dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'));
    setCalledNumber(data.mobile);
    setIsDialOuter(data.dialOuter);
    if (data.dialOuter) {
      setTriggerDialOuterNum(data.mobile);
    }
  }, []);

  // 设置 外呼 ActionType
  const setDialAction = useCallback((action) => {
    setAction(action);
  }, []);

  // 设置 callId
  const setDialCallId = useCallback((cid) => {
    setCallId(cid);
  }, []);

  // 设置 overdueId
  const setCallOutId = useCallback((id) => {
    setTheOutId(id);
  }, []);

  // 设置 是否外部拨打
  const setDialOuter = useCallback((dialOuter = false) => {
    setIsDialOuter(dialOuter);
  }, []);

  // 设置 外部拨号号码
  const setDialOuterCallNumber = useCallback((mobile) => {
    setTriggerDialOuterNum(mobile);
  }, []);

  const setHelpStr = useCallback((str) => {
    setHelpStrFunc(str);
  }, []);

  return {
    outId,
    callId,
    dialTime,
    dialAction,
    isDialOuter,
    calledNumber,
    outerDialNumber,
    helpStr,
    dialStart,
    dialOff,
    setDialAction,
    setDialCallId,
    setCallOutId,
    setDialOuter,
    setDialOuterCallNumber,
    setHelpStr,
  };
};
