const IMAGE_REGEXP = /\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;

export function isImageUrl(url: string) {
  return IMAGE_REGEXP.test(url);
}

export function isImageFile(file: any) {
  if (file.type) {
    return file.type.indexOf('image') === 0;
  }

  return false;
}

/**
 *  读取图片文件
 * @param file
 */
export function asyncReadFileToDataUrl(file: File) {
  return new Promise<string>((resolve, reject) => {
    try {
      console.log('FileReader', FileReader);
      const reader = new FileReader();
      reader.onload = function (evt) {
        const dataUrl = evt?.target?.result as string;
        // console.log('dataUrl onload', dataUrl)
        if (dataUrl) {
          resolve(dataUrl);
        } else {
          reject();
        }
      };
      reader.onerror = function (e: any) {
        console.log('reader onerror', e);
        reject();
      };
      reader.readAsDataURL(file);
    } catch {
      console.log('reader catch');
      reject();
    }
  });
}

/**
 * base64数据转为图片
 * @param dataUrl base64数据串
 */
export async function readDataUrlToImg(dataUrl: string) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = dataUrl;
    img.onload = function () {
      resolve(img);
    };
    img.onerror = function (e) {
      reject(e);
    };
  });
}

/**
 * 压缩图片
 */
export async function compressImg(
  img: any,
  type: string,
  mx: number,
  mh: number,
): Promise<Blob | null> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    const { width: originWidth, height: originHeight } = img;
    // 最大尺寸限制
    const maxWidth = mx;
    const maxHeight = mh;
    // 目标尺寸
    let targetWidth = originWidth;
    let targetHeight = originHeight;
    if (originWidth > maxWidth || originHeight > maxHeight) {
      if (originWidth / originHeight > 1) {
        // 宽图片
        targetWidth = maxWidth;
        targetHeight = Math.round(maxWidth * (originHeight / originWidth));
      } else {
        // 高图片
        targetHeight = maxHeight;
        targetWidth = Math.round(maxHeight * (originWidth / originHeight));
      }
    }
    canvas.width = targetWidth;
    canvas.height = targetHeight;
    context?.clearRect(0, 0, targetWidth, targetHeight);
    // 图片绘制
    context?.drawImage(img, 0, 0, targetWidth, targetHeight);
    canvas.toBlob(function (blob) {
      resolve(blob);
    }, type || 'image/png');
  });
}

/**
 * 等分字符串
 * @param str
 * @param nums 片数
 */
export function splitJsonStr(str: string, nums: number) {
  const shard = Math.ceil(str.length / nums);
  const ret = [];
  for (let i = 0; i < nums; i++) {
    const substr = str.slice(i * shard, (i + 1) * shard);
    ret.push(substr);
  }
  return ret;
}

/**
 * 格式化城市数据
 * @param cityValues
 */
export function formatCity(cityValues: string[]) {
  let cityStr = '';
  cityValues.forEach((item, index) => {
    cityStr += item.split('_')[0];
    if (index !== cityValues.length - 1) {
      cityStr += '-';
    }
  });
  return cityStr;
}
