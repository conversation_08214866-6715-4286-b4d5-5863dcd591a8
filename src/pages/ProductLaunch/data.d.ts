/**
 * 产品投放查询产品列表 params
 */
export type TProductParams = {
  secondaryClassification: string;
  current?: number; // 当前页
  pageSize?: number; // 页大小
};

/**
 * 产品投放查询产品列表 item
 */
export type TProductTableItem = {
  businessKey: string; // 产品编码
  id: number; // 产品投放配置表ID
  name: string; // 产品名称
  parentBusinessKey: string; // 父级业务标识
  parentId: number; // 产品投放父级ID
  interest: number;
  periodsList: number[];
  registerLicenceType: string; //  上牌类型
};

/**
 * 产品投放查询渠道列表 params
 */
export type TChannelListParams = {
  businessType?: string; // 业务类型
  channelType?: number; // 渠道类型 10:货拉拉 20:啦啦拍档 0:其它
  current?: number; // 当前页
  name?: string; // 名称
  pageSize?: number; // 页大小
  parentId?: number; // 产品投放父级ID
  productCode?: string; // 产品编码
  whetherHasConfig?: boolean; // 是否查询已经配置的数据,true:是,false：否
};

/**
 * 产品投放查询渠道列表 item
 */
export type TChannelTableItem = {
  businessKey: string; // 渠道编码
  channelType: number; // 渠道类型 10:货拉拉 20:啦啦拍档 0:其它
  channelTypeDesc: string; // 渠道类型描述
  id: number; // 产品投放配置表ID
  name: string; // 名称
  parentBusinessKey: string; // 父级业务标识
  parentId: number; // 产品投放父级ID
  status: number; //状态
};

/**
 * 产品投放查询车列表 params
 */
export type TCarListParams = {
  businessType?: string; // 业务类型
  carType?: number; // 车辆类型
  current?: number; // 当前页
  name?: string; // 名称
  pageSize?: number; // 页大小
  parentBusinessKey?: string; // 父级业务标识
  parentId?: number; // 产品投放父级ID
  whetherHasConfig?: boolean; // 是否查询已经配置的数据,true:是,false：否
};

/**
 * 产品投放查询车列表 item
 */
export type TCarInfoTableItem = {
  businessKey: string; // 车辆编码
  carCityList: string[]; // 库存城市
  carType: number; // 车辆类型
  name: string; // 车辆名称
  id: number; // 产品投放配置表 ID
  parentBusinessKey: string; // 父级业务标识
  parentId: number; // 产品投放父级ID
  status: number;
};

/**
 * 产品投放查询库存城市列表 params
 */
export type TInventoryCityParams = {
  businessType?: string; // 业务类型
  current?: number; // 当前页
  name?: string; // 名称
  pageSize?: number; // 页大小
  parentBusinessKey?: string; // 父级业务标识
  parentId?: number; // 产品投放父级ID
  whetherHasConfig?: boolean; // 是否查询已经配置的数据,true:是,false：否
};

/**
 * 产品投放查询库存城市列表 params
 */
export type TInventoryCityItem = {
  businessKey: string; // 车辆编码
  name: string; // 车辆名称
  id: number; // 产品投放配置表 ID
  parentBusinessKey: string; // 父级业务标识
  parentId: number; // 产品投放父级ID
};

/**
 * 配置渠道/配置车辆/配置库存城市 || 删除 params
 */
export type TSaveOrDelConfigInfoParams = {
  addList?: {
    code: string; // 编码
    name: string; // 名字
  }[]; // 添加的业务标识集合
  deleteBusinessKeys?: string[]; // 删除的业务标识集合，仅库存城市使用
  deleteIds?: (number | undefined)[]; // 单条删除、渠道配置和车辆配置
  businessType?: string; // 业务类型,可用值:PRODUCT,CHANNEL,CAR,CITY
  parentId?: number; // 父ID
  productCode?: string; // 产品编码
};

/**
 * 穿梭框的 item
 */
export type TTransferItem = {
  businessKey: string; // 编码
  name?: string; // 名称
  id?: number; // ID
};
