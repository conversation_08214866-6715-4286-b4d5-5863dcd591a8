// 保单流转所有状态
export enum PolicyStatus {
  NOT_AVAILABLE_POLICY = 10,
  PENDING_REVIEW = 20,
  APPROVED = 30,
  AUTO_APPROVED = 40,
  REJECTED = 50,
  POLICY_EXCEPTION = 60,
}

// 表格查询筛选状态
export const PolicyStatusMap = {
  [PolicyStatus.NOT_AVAILABLE_POLICY]: '暂无保单',
  [PolicyStatus.PENDING_REVIEW]: '待复核',
  [PolicyStatus.APPROVED]: '复核通过',
  [PolicyStatus.AUTO_APPROVED]: '自动通过',
  [PolicyStatus.REJECTED]: '复核驳回',
  [PolicyStatus.POLICY_EXCEPTION]: '保单异常',
};

// 页面路径
export enum PolicyPagePaths {
  MANAGEMENT = '/businessMng/car-insurance-policy/manage',
  DETAIL = '/businessMng/car-insurance-policy/detail',
  BULK_UPLOAD = '/businessMng/car-insurance-policy/bulk-upload',
}

// 保单事件类型
export enum PolicyEvent {
  POLICY_VERIFICATION = 'POLICY_VERIFICATION', // 保单校验
  SUBMIT_POLICY = 'SUBMIT_POLICY', // 保单上传
  APPROVE_POLICY = 'APPROVE_POLICY', // 复核通过
  REJECT_POLICY = 'REJECT_POLICY', // 驳回
  POLICY_EXCEPTION = 'POLICY_EXCEPTION', // 标记为保单异常
  AUTO_APPROVE_POLICY = 'AUTO_APPROVE_POLICY', // 自动通过
}

export const PostPolicyBaseParams = {
  id: '', // 保单id
  plateNo: '', // 车牌号/新车合格证号
  vin: '', // 车架号
  orderNo: '', // 贷款订单号
  policyNo: '', // 保单号
  policyHolder: '', // 投保人
  policyCommercialInsurance: '', // 保单商业险金额
  commercialInsuranceStartDate: '', // 商业险开始日期
  commercialInsuranceEndDate: '', // 商业险结束日期
  factoryPlateModel: '', // 厂牌型号
  extendInfo: {}, // 其他信息
  event: '', // 事件类型-保单校验
  requestOrderId: '', // 保单校验请求id，用以提交接口response返回做错误匹配
};

export const InsuranceSubjectMap = {
  HUO_MAN_MAN: '广州货满满汽车咨询有限公司',
  YI_LI_XIN: '易立信区块链科技(广州)有限公司',
};

export const policyHolderMap = {
  广州货满满汽车咨询有限公司: '广州货满满汽车咨询有限公司',
  '易立信区块链科技(广州)有限公司': '易立信区块链科技(广州)有限公司',
  其他: '其他',
};

export enum CHANNEL_LEVEL {
  FIRST_CHANNEL = 1,
  SECOND_CHANNEL = 2,
}

export const policyInfoTemplate = {
  carPolicyRspDTO: null,
  checkFlag: false,
  commercialInsuranceEndDate: null,
  commercialInsuranceStartDate: null,
  exceptionMsgList: null,
  factoryPlateModel: null,
  id: null,
  orderNo: null,
  orderNoList: null,
  plateNo: null,
  policyCommercialInsurance: null,
  policyHolder: null,
  policyNo: null,
  vin: null,
};
