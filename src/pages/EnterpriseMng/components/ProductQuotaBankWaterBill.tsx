/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-13 14:20:28
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-09-30 14:40:21
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/ProductQuotaBankWaterBill.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ShowInfo } from '@/components';
import globalStyle from '@/global.less';
import { downLoadExcel } from '@/utils/utils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from '@umijs/max';
import { Button, DatePicker, Tooltip } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useRef, useState } from 'react';
import type { AccoutFlowParams, ProductQuotaBankWaterListItem } from '../data';
import { accountFlowExport, getAccountFlow } from '../service';

const ProductQuotaBankWaterBill = () => {
  const bankWaterTotal = {
    creditAmount: '总额度（元）',
    usedAmount: '已用额度（元）',
    currentAmount: '剩余可用额度（元）',
  };
  const [dataBrief, setDateBrief] = useState<{
    creditAmount: number;
    usedAmount: number;
    currentAmount: number;
  }>();
  const { subAccountNo } = history?.location?.query as { subAccountNo: string };
  // console.log(history?.location?.query, 'hahah');
  const getExport = (form: AccoutFlowParams) => {
    accountFlowExport(form).then((res) => {
      downLoadExcel(res);
    });
  };

  const renderFormItem = (_: any, { type, defaultRender, ...rest }: any) => {
    return <DatePicker.RangePicker {...rest} className={globalStyle.w100} />;
  };
  const formRef = useRef<FormInstance>();
  const columns: ProColumns<ProductQuotaBankWaterListItem>[] = [
    {
      title: '操作时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      valueType: 'dateRange',
      render(dom, row) {
        return row?.createdAt;
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          createdAtStart: `${value[0].split(' ')[0]}`,
          createdAtEnd: `${value[1].split(' ')[0]}`,
        }),
      },
    },
    {
      title: '操作额度',
      dataIndex: 'operationAmount',
      key: 'operationAmount',
      search: false,
      render: (_, row) => {
        if ([10, 41, 30, 61].includes(row?.operationType)) {
          return `-${row.operationAmount}`;
        } else if ([11, 12, 31, 40, 62].includes(row?.operationType)) {
          return `+${row.operationAmount}`;
        }
        return row.operationAmount || '-';
      },
    },
    {
      title: '流水类型',
      dataIndex: 'operationType',
      valueEnum: {
        10: '下单', //-
        11: '退单', //+
        12: '还款', //+
        31: { text: '解冻', disabled: true }, // +
        40: '调额（增加）', //+
        41: '调额（减少）', //-
        30: { text: '逾期冻结', disabled: true }, //-
        61: '担保金支付', //-
        62: '担保金退款', //+
        // 6: '解冻',
      },
      fieldProps: {
        mode: 'multiple',
      },
    },
    {
      title: '操作后剩余可用额度（元）',
      dataIndex: 'subAfterAmount',
      key: 'subAfterAmount',
      search: false,
    },
    {
      title: (
        <>
          拓展信息
          <Tooltip placement="top" title="下单、退单记录关联渠道订单号；还款记录关联账单编号">
            <QuestionCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        </>
      ),
      dataIndex: 'extendInfo',
      key: 'extendInfo',
      search: false,
    },
    {
      title: '账号',
      dataIndex: 'userNo',
      key: 'userNo',
      search: false,
    },
  ];
  return (
    <div>
      <ShowInfo infoMap={bankWaterTotal} noCard data={dataBrief} />
      <ProTable<ProductQuotaBankWaterListItem>
        formRef={formRef}
        columns={columns}
        request={(params) =>
          getAccountFlow({ subAccountNo, ...params } as AccoutFlowParams).then((res) => {
            const { creditAmount, currentAmount, usedAmount } = res?.data;
            setDateBrief({ creditAmount, currentAmount, usedAmount });
            return res?.data?.rspBasePageRsp;
          })
        }
        options={false}
        toolBarRender={() => {
          return [
            <Button
              key="button"
              type="primary"
              onClick={() => {
                const { createdAt, ...data } = formRef?.current?.getFieldsValue();
                let newForm = { ...data };
                if (createdAt?.length) {
                  const createdAtStart = `${createdAt[0].format('YYYY-MM-DD')}`;
                  const createdAtEnd = `${createdAt[1].format('YYYY-MM-DD')}`;
                  newForm = { ...data, createdAtEnd, createdAtStart };
                }
                getExport({ ...newForm, subAccountNo });
              }}
            >
              导出
            </Button>,
          ];
        }}
      />
    </div>
  );
};

export default ProductQuotaBankWaterBill;
