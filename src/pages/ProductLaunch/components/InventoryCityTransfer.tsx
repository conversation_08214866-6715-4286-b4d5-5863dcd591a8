import TableTransfer from '@/components/TableTransfer';
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import type { TInventoryCityItem } from '../data.d';

export type TInventoryCityInfoTransferProps = {
  notConfigData: TInventoryCityItem[];
  hasConfigData: TInventoryCityItem[];
};

const tableColumns = [
  {
    title: '库存城市',
    dataIndex: 'name',
  },
];

const InventoryCityTransfer = forwardRef((props: TInventoryCityInfoTransferProps, ref) => {
  const { notConfigData, hasConfigData } = props;
  /**
   * 存储被勾选的 keys
   */
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  /**
   * 暴露 targetKeys 给父组件
   */
  useImperativeHandle(ref, () => ({
    targetKeys,
    type: 'city',
  }));

  useEffect(() => {
    setTargetKeys(hasConfigData.map((item) => item.businessKey));
  }, []);

  return (
    <TableTransfer
      dataSource={[...notConfigData, ...hasConfigData]}
      targetKeys={targetKeys}
      showSearch
      onChange={(nextTargetKeys: string[]) => setTargetKeys(nextTargetKeys)}
      filterOption={(inputValue: string, item: TInventoryCityItem) =>
        item.name.indexOf(inputValue) !== -1
      }
      leftColumns={tableColumns}
      rightColumns={tableColumns}
    />
  );
});

export default InventoryCityTransfer;
