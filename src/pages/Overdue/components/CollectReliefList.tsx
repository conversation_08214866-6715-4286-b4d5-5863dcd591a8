/*
 * @Author: your name
 * @Date: 2020-11-24 17:25:08
 * @LastEditTime: 2021-11-17 10:47:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Bill/components/BillDetail.ts
 */
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Button, Card } from 'antd';
import React, { useRef, useState } from 'react';
// import { getUserListEnum } from '@/services/enum';
import globalStyle from '@/global.less';
import { genSimpleName } from '@/utils/utils';
import { useModel } from '@umijs/max';
import type { CollectReliefListItem } from '../data';
import { downLoad } from '../service';
import { ApplyReliefModal } from './index';
// import { getCollectReliefList } from '../service';

const CollectReliefList: React.FC<any> = (props) => {
  const { collectReliefList, overDueAmount, hideCard } = props;
  const { mapUrgePersonList: mapUserList } = useModel('userList');

  const columns: ProColumns<CollectReliefListItem>[] = [
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    // {
    //   title: '催收员',
    //   dataIndex: 'urgePerson',
    // },
    {
      title: '催收员',
      dataIndex: 'urgePerson',
      // valueType: 'select',
      render: (_, row) => {
        return mapUserList[row?.urgePerson] || '-';
      },
      // request: getUserListEnum,
    },
    {
      title: '减免金额',
      dataIndex: 'amount',
    },
    {
      title: '备注',
      dataIndex: 'urgeRecentlyMsg',
      ellipsis: true,
    },
    {
      title: '附件',
      dataIndex: 'fileList',
      render: (_, record) => (
        <>
          {record?.fileList?.length
            ? record?.fileList?.map((item: { fileUrl: string; fileName: string }) => {
                return (
                  <div key={item.fileUrl} title={item.fileName}>
                    {item.fileName ? (
                      <a onClick={() => downLoad(item.fileUrl, item.fileName)}>
                        {genSimpleName(item.fileName)}
                      </a>
                    ) : (
                      '-'
                    )}
                  </div>
                );
              })
            : '-'}
        </>
      ),
    },
    {
      title: '审批结果',
      dataIndex: 'status',
      key: 'status',
      valueEnum: {
        0: { text: '待审核', status: 'default' },
        1: { text: '通过', status: 'success' },
        2: {
          text: '驳回',
          status: 'error',
        },
      },
    },
  ];
  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();

  function contentElement() {
    return (
      <>
        {/* <Table ></Table> */}
        <Button
          type="primary"
          disabled={reduceDisabled}
          onClick={() => {
            handleModalVisible(true);
          }}
          className={globalStyle.mb10}
        >
          申请减免
        </Button>
        <ProTable<CollectReliefListItem>
          columns={columns}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey={(row) => {
            return row.createdAt + row.amount;
          }}
          search={false}
          options={false}
          toolBarRender={false}
          dataSource={collectReliefList}
          pagination={false}
          // request={(params) => getCollectReliefList(params)}
        />
      </>
    );
  }

  return (
    <>
      {!hideCard && (
        <Card title="减免记录" className={globalStyle.mt20}>
          {contentElement()}
        </Card>
      )}
      {hideCard && <>{contentElement()}</>}
      <ApplyReliefModal
        onOk={async () => {
          handleModalVisible(false);
          props.refresh();
        }}
        onCancel={() => {
          handleModalVisible(false);
        }}
        overDueAmount={overDueAmount}
        onVisibleChange={handleModalVisible}
        modalVisible={modalVisible}
      />
    </>
  );
};

export default CollectReliefList;
