import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import { useEffect, useState } from 'react';
import { getRiskChangeRecordList } from '../service';

type RiskLevelChangeModalProps = {
  visible: boolean;
  data: {
    productSecondTypeCode: string;
    userNo: string;
  };
  onClose: () => void;
};

const RiskLevelChangeModal = (props: RiskLevelChangeModalProps) => {
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    setShowModal(props.visible);
  }, [props.visible]);

  const columns = [
    {
      title: '客户评级',
      dataIndex: 'curValue',
      key: 'curValue',
    },
    {
      title: '更新时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
  ];

  return (
    <Modal
      title="风控评级变更记录"
      centered
      open={showModal}
      onCancel={props.onClose}
      width={900}
      footer={null}
    >
      <ProTable
        columns={columns}
        toolBarRender={false}
        search={false}
        pagination={{
          pageSize: 10,
        }}
        request={(params) =>
          getRiskChangeRecordList({
            ...params,
            userNo: props.data.userNo,
            productSecondTypeCode: props.data.productSecondTypeCode,
            changeType: 'riskLevel',
          })
        }
      />
    </Modal>
  );
};

export default RiskLevelChangeModal;
