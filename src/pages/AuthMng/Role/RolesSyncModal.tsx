import { getVanEvn } from '@/utils/utils';
import { Button, Form, Input, message, Modal, Popconfirm, Radio } from 'antd';
import React, { useState } from 'react';
import { envEnum, getEnvOptions } from '../const';
import '../index.less';
import { syncRoles } from '../syncServices';

type ModalProps = {
  visible: boolean;
  closeModal: () => void;
  selectedRows: any;
};

export default (props: ModalProps) => {
  const { visible, selectedRows, closeModal } = props;
  console.log('props', props);
  const currentEnv: envEnum = getVanEvn() as envEnum;

  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const handleSync = async () => {
    try {
      await form.validateFields();
    } catch (err) {
      return Promise.reject();
    }
    const fromValues = form.getFieldsValue();
    // 目标环境
    const targetEnv: envEnum = fromValues.targetEnv;
    // 参数
    const data: any = {
      secretKey: fromValues?.secretKey,
      reqList: selectedRows,
    };
    //
    setConfirmLoading(true);
    const res: any = (await syncRoles(targetEnv, data).catch(() => {})) || {};
    setConfirmLoading(false);
    if (res?.ret === 0 || res?.ret === 200) {
      message.success('同步成功');
      closeModal();
    } else if (res?.msg) {
      message.error(res?.msg);
    } else {
      message.error('同步失败');
    }
  };
  //

  return (
    <>
      <Modal
        title={'同步'}
        open={visible}
        // width={650}
        destroyOnClose={true}
        onCancel={closeModal}
        footer={
          <>
            <Button onClick={closeModal}>取消</Button>
            <Popconfirm
              title="是否确认进行同步？"
              onConfirm={handleSync}
              okButtonProps={{ loading: confirmLoading }}
              okText="确认"
              cancelText="取消"
            >
              <Button type="primary" loading={confirmLoading}>
                确定
              </Button>
            </Popconfirm>
          </>
        }
      >
        <Form
          form={form}
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 14 }}
          layout="horizontal"
          autoComplete="off"
        >
          {/* 秘钥 */}
          <Form.Item label="秘钥" name="secretKey" rules={[{ required: true, message: '必填项' }]}>
            <Input />
          </Form.Item>
          {/* 目标环境 */}
          <Form.Item label="环境" name="targetEnv" rules={[{ required: true, message: '必选项' }]}>
            <Radio.Group>
              {getEnvOptions(currentEnv)?.map((item) => (
                <Radio disabled={item.disabled} key={item.value} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
