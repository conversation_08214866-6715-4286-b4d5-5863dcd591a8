/*
 * @Author: your name
 * @Date: 2022-02-12 10:17:57
 * @LastEditTime: 2023-03-28 14:40:08
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/AddOfflineRepay.tsx
 */
import { CommonImageUpload } from '@/components/ReleaseCom';
import { getUserListEnum } from '@/services/enum';
import { convertUploadFileList, disableFutureDate } from '@/utils/utils';
import {
  ModalForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { message } from 'antd';
import React, { useState } from 'react';
import type { RegistList } from '../data';
import { applyRepay } from '../service';

export type AddOfflineRepayProps = {
  // onCancel: () => void;
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  refuseModalVisible: boolean | undefined;
  shouldAmountDue: string;
  onVisibleChange: any;
  repayPlanNo: string;
};

const AddOfflineRepay: React.FC<AddOfflineRepayProps> = ({
  visible,
  close,
  shouldAmountDue,
  repayPlanNo,
  onOk,
}) => {
  // console.log(userList, [], 'ddd');
  const [allFileList, handleFileList] = useState<Record<string, any>>({});
  const mapFileList = (allFile: any) => {
    handleFileList(allFile);
  };

  return (
    <>
      <ModalForm
        title="新增线下还款"
        // width="500px"
        layout="horizontal"
        visible={visible}
        modalProps={{
          centered: true,
          onCancel: close,
        }}
        initialValues={{ remitAmount: shouldAmountDue }}
        onFinish={async (values) => {
          const mapUploadFile = convertUploadFileList(allFileList, ['attach']);
          applyRepay({
            ...values,
            ...mapUploadFile,
            // applyName: '杨家龙',
            repayPlayNo: repayPlanNo,
          } as RegistList).then(() => {
            message.success('添加成功');
            close();
            onOk();
          });
          return true;
        }}
      >
        <ProFormSelect
          rules={[{ required: true }]}
          labelCol={{ span: 5 }}
          request={getUserListEnum}
          placeholder="请输入申请人"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
          }}
          width="md"
          name="applyName"
          label="申请人"
        />
        <ProFormDigit
          name="remitAmount"
          width="md"
          labelCol={{ span: 5 }}
          label="回款金额(元)"
          placeholder="请输入回款金额"
          fieldProps={{ min: 0, precision: 2 }}
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 有坑，同时出现很多个error,待优化
                if (/^\d+(\.\d+)?$/.test(val) && val && val > shouldAmountDue) {
                  // callBack('减免金额不能大于逾期金额');
                  return Promise.reject(new Error('回款金额不能大于应还总额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < shouldAmountDue) {
                  // callBack();
                  return Promise.reject(new Error('请输入数字'));
                }
                // callBack();
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormDatePicker
          rules={[{ required: true }]}
          labelCol={{ span: 5 }}
          fieldProps={{
            disabledDate: disableFutureDate,
          }}
          width="md"
          style={{ width: '100%' }}
          name="remitDate"
          label="回款日期"
          placeholder="请输入回款日期"
        />
        <ProFormText
          name="remitType"
          labelCol={{ span: 5 }}
          width="md"
          rules={[{ required: true }]}
          label="回款方式"
          placeholder="请输入回款方式"
        />
        <ProFormTextArea
          labelCol={{ span: 5 }}
          placeholder="请输入备注"
          width="md"
          fieldProps={{ maxLength: 500 }}
          label="备注"
          name="remark"
        />
        <CommonImageUpload
          extra="支持扩展名：.doc.docx.txt.pdf.png.jpg.jpeg.gif.xls.xlsx"
          label="附件"
          name="attach"
          labelCol={{ span: 5 }}
          max={5}
          listType="text"
          size={10}
          fileListEdit={allFileList?.attach || []}
          desPath="EP_AUTH_INFO"
          mapFileList={mapFileList}
          accept=".doc,.docx,.txt,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx"
        />
      </ModalForm>
    </>
  );
};

export default AddOfflineRepay;
