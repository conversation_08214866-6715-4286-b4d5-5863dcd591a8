/* eslint-disable react-hooks/exhaustive-deps */
import { DatePicker, Select } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { useState } from 'react';

// 支持月份多选
type MultipleMonthProps = {
  onChange?: (val: string[]) => void;
  value?: string[];
};
const MultipleMonth: React.FC<MultipleMonthProps> = (props) => {
  const { onChange } = props;
  const [yearMonth, setYearMonth] = useState<string[]>([]);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    onChange?.(yearMonth);
  }, [yearMonth]);

  return (
    <DatePicker
      picker="month"
      onChange={(value: any) => {
        console.log('value', value);
        setYearMonth((month) => {
          month.push(dayjs(value).format('YYYY-MM'));
          return [...month];
        });
      }}
      onBlur={() => {
        console.log('DatePicker onBlur');
      }}
      allowClear={false}
      onClick={() => {
        setOpen(true);
      }}
      open={open}
      monthCellRender={(current: any, props1) => {
        console.log('props1', props1);
        const yearMonth1 = dayjs(current).format('YYYY-MM');
        return (
          <div
            style={{
              background: yearMonth.includes(yearMonth1) ? '#1F92FD' : '',
              margin: '16px 8px',
              color: yearMonth.includes(yearMonth1) ? 'white' : 'black',
            }}
          >
            {dayjs(current).format('MM')}
          </div>
        );
      }}
      //   onSelect={(value) => {
      //     console.log('asdasdasd', value);
      //   }}
      inputRender={(props1: any) => {
        // inputRender 这个属性在antd中没找到 应该是底层的 rc-component的
        console.log('props1', props1);
        return (
          <div>
            <Select
              onChange={(value) => {
                console.log('value', value);
                setYearMonth(value);
              }}
              open={false}
              showSearch
              allowClear
              mode="multiple"
              value={yearMonth}
              bordered={false}
              showArrow={false}
              onBlur={() => {
                setOpen(false);
              }}
            />
          </div>
        );
      }}
    />
  );
};

export default MultipleMonth;
