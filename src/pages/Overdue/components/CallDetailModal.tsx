/*
 * @Date: 2021-11-05 18:10:28
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-07-13 15:02:36
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/components/CallDetailModal.tsx
 * @Description:
 */
import Audio from '@/components/Audio';
import { getCallerResultType } from '@/pages/CallOutMng/service';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Modal } from 'antd';
import React from 'react';
import type { ICallDetail } from '../data';
import { getCallerAudioRecord } from '../service';

interface ICallDetailModalProps {
  visible: boolean;
  source: ICallDetail[];
  onCancel: () => void;
}
const CallDetailModal: React.FC<ICallDetailModalProps> = (props) => {
  // props
  const { visible, source, onCancel } = props;

  // state
  const columns: ProColumns<ICallDetail>[] = [
    {
      title: '通话记录ID',
      dataIndex: 'callRecord',
    },
    {
      title: '催收单号',
      dataIndex: 'accNo',
    },
    {
      title: '催收人',
      dataIndex: 'ucenterName',
    },
    {
      title: '拨打号码',
      dataIndex: 'dialNumber',
    },
    {
      title: '通话时间',
      key: 'callTimeDetail',
      render: (_, record) => {
        return (
          <>
            <p>拨打时间：{record.dialTime}</p>
            <p>接通时间：{record.turnOnTime}</p>
            <p>通话结束时间：{record.callEndTime}</p>
          </>
        );
      },
    },
    {
      title: '通话时长（秒）',
      dataIndex: 'talkTimeSecond',
    },
    {
      title: '通话状态',
      dataIndex: 'status',
      valueType: 'select',
      request: getCallerResultType,
    },
    {
      title: '通话录音',
      key: 'option',
      valueType: 'option',
      render: (_, record) => {
        return (
          <Audio
            controls
            downloadShow
            downloadHandle={() => getCallerAudioRecord(record.callRecord)}
          />
        );
      },
    },
  ];

  return (
    <Modal title="拨打详情" open={visible} footer={null} width={900} onCancel={onCancel}>
      <ProTable<ICallDetail>
        rowKey="callRecord"
        columns={columns}
        scroll={{ x: 'max-content' }}
        dataSource={source}
        search={false}
        toolBarRender={false}
        pagination={false}
      />
    </Modal>
  );
};

export default CallDetailModal;
