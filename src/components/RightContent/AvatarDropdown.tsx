import { outLogin } from '@/services/login';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Avatar, Menu, Spin } from 'antd';
import React, { useCallback } from 'react';
// import { stringify } from 'querystring';
// import { removeToken } from '@/utils/auth';
import {
  removePassportName,
  removeSSOAPICookie,
  removeSSOCookie,
  removeSSOVanTaskID,
} from '@/utils/auth';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';

export interface GlobalHeaderRightProps {
  menu?: boolean;
}

/**
 * 退出登录，并且将当前的 url 保存
 */
const loginOut = async () => {
  try {
    removeSSOCookie();
    removeSSOAPICookie();
    removeSSOVanTaskID();
    removePassportName();
  } catch (error) {
    console.log(error);
  }
  try {
    await outLogin();
    // removeToken();
    // const { query } = history.location;
    // const { redirect } = query;
    // // Note: There may be security issues, please note
    // if (window.location.pathname !== '/user/login' && !redirect) {
    //   history.replace({
    //     pathname: '/user/login',
    //     search: stringify({
    //       redirect: window.location.href,
    //       // redirect: pathname,
    //     }),
    //   });
    // }
    // eslint-disable-next-line no-empty
  } catch (error) {
  } finally {
    // history.replace('/user/login');
  }
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = () => {
  const { initialState } = useModel('@@initialState');
  const onMenuClick = useCallback(
    (event: {
      key: React.Key;
      keyPath: React.Key[];
      item: React.ReactInstance;
      domEvent: React.MouseEvent<HTMLElement>;
    }) => {
      const { key } = event;
      if (key === 'logout' && initialState?.currentUser) {
        loginOut().then(() => {
          // if (initialState?.currentUser) {
          //   setInitialState({ ...initialState, currentUser: undefined });
          // }
        });
        return;
      }
      history.push(`/account/${key}`);
    },
    [],
  );

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser }: any = initialState;

  if (!currentUser) {
    return loading;
  }

  const menuHeaderDropdown = (
    <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
      {/* {menu && (
        <Menu.Item key="center">
          <UserOutlined />
          个人中心
        </Menu.Item>
      )}
      {menu && (
        <Menu.Item key="settings">
          <SettingOutlined />
          个人设置
        </Menu.Item>
      )}
      {menu && <Menu.Divider />} */}

      <Menu.Item key="logout">
        <LogoutOutlined />
        退出登录
      </Menu.Item>
    </Menu>
  );
  return (
    <HeaderDropdown overlay={menuHeaderDropdown}>
      <span className={`${styles.action} ${styles.account}`}>
        <Avatar size="small" className={styles.avatar} icon={<UserOutlined />} />
        <span style={{ color: '#fff' }}>
          {currentUser.accountName || currentUser.extSource?.storeName || currentUser.userId}
        </span>
      </span>
    </HeaderDropdown>
  );
};

export default AvatarDropdown;
