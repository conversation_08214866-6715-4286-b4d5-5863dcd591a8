/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-29 10:45:08
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-28 16:04:02
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/CallPayMoneyList.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import globalStyle from '@/global.less';
import { offlineRepayReviewList } from '@/pages/CallPay/carInsurance/services';
import { genSimpleName } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { useModel } from '@umijs/max';
import { Button, message, Tabs } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useRef, useState } from 'react';
import { PAY_TYPE } from '../../CallPay/const';
import type { CallPayMoneyListItem } from '../data';
import { downLoad } from '../service';
import SubmitCallBackModal from './SubmitCallBackModal';

type CallPayMoneyListProps = {
  callPayMoneyList: [];
  repayOverdueCostBO: [];
  overDueAmount: number;
  overdueCaseNo: string;
  hideCard?: boolean;
  productCode: string;
  productSecondTypeCode?: string;
  funderChannelCode?: string;
  userNo: string;
  refresh: any;
  productSecondaryCode: string;
  productClassicCode: string;
  billType: number;
  accountName: string;
  isSettle: any;
  orderNoList: { orderNo: string; totalOverdueAmount: string; value: string }[];
  deadLine: any;
};

const PAYBACK_TYPE = {
  1: '线下还款',
  2: '普通对公还款',
  3: '扫码还款',
};

const { TabPane } = Tabs;
const CallPayMoneyList: React.FC<CallPayMoneyListProps> = (props) => {
  const {
    callPayMoneyList,
    overDueAmount,
    hideCard,
    overdueCaseNo,
    orderNoList,
    accountName,
    productSecondaryCode,
  } = props;
  const { mapUrgePersonList: mapUserList } = useModel('userList');
  const [modalVisible, handleModalVisible] = useState<boolean>(false);
  const [isNewTable, setIsNewTable] = useState<boolean>(true);
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<CallPayMoneyListItem>[] = [
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 200,
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    {
      title: '催收员',
      dataIndex: 'urgePerson',
      render: (_, row) => {
        return mapUserList[row?.urgePerson] || '-';
      },
      hideInTable: isNewTable,
    },
    {
      title: '催收员',
      dataIndex: 'applyName',
      hideInTable: !isNewTable,
    },
    {
      title: '回款金额',
      dataIndex: 'amount',
      hideInTable: isNewTable,
    },
    {
      title: '回款金额',
      dataIndex: 'repayAmount',
      hideInTable: !isNewTable,
    },
    {
      title: '减免金额',
      dataIndex: 'exemptionAmount',
      hideInTable: !isNewTable,
    },
    {
      title: '总金额',
      hideInTable: !isNewTable,
      render: (_, row) => {
        return new BigNumber(row.repayAmount || 0).plus(row.exemptionAmount || 0).toFixed(2);
      },
    },
    {
      title: '回款方式',
      dataIndex: 'payBackType',
      ellipsis: true,
      hideInTable: isNewTable,
      render: (_, row) => {
        return PAYBACK_TYPE[row.payBackType as string] || Number(row.payBackType) || '-';
      },
    },
    {
      title: '回款方式',
      dataIndex: 'remitType',
      ellipsis: true,
      hideInTable: !isNewTable,
      render: (_, row) => {
        return PAY_TYPE[row.remitType as number] || Number(row.remitType) || '-';
      },
    },
    {
      title: '备注',
      dataIndex: 'urgeRecentlyMsg',
      ellipsis: true,
      hideInTable: isNewTable,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      hideInTable: !isNewTable,
    },
    {
      title: '附件',
      dataIndex: 'fileList',
      render: (_, record) => {
        if (isNewTable) {
          // 新回款记录附件
          return (
            <>
              {record?.attachList?.length
                ? record?.attachList?.map((item: { url: string; name: string }) => {
                    return (
                      <div key={item.url} title={item.name}>
                        {item.name ? (
                          <a onClick={() => downLoad(item.url, item.name)}>
                            {genSimpleName(item.name)}
                          </a>
                        ) : (
                          '-'
                        )}
                      </div>
                    );
                  })
                : '-'}
            </>
          );
        }
        return (
          <>
            {record?.fileList?.length
              ? record?.fileList?.map((item: { netWorkPath: string; fileName: string }) => {
                  return (
                    <div key={item.netWorkPath} title={item.fileName}>
                      {item.fileName ? (
                        <a
                          onClick={() => downLoad(item.netWorkPath, item.fileName)}
                          title={item.fileName}
                        >
                          {genSimpleName(item.fileName)}
                        </a>
                      ) : (
                        '-'
                      )}
                    </div>
                  );
                })
              : '-'}
          </>
        );
      },
    },
    {
      title: '审批结果',
      dataIndex: 'status',
      key: 'status',
      valueEnum: !isNewTable
        ? {
            0: { text: '待审核', status: 'default' },
            1: { text: '通过', status: 'success' },
            2: {
              text: '驳回',
              status: 'error',
            },
          }
        : {
            0: { text: '待审批', status: 'default' },
            1: { text: '驳回', status: 'error' },
            2: { text: '审批通过', status: 'success' },
          },
    },
  ];

  // 新回款记录列表获取
  async function getOrderList(params: any) {
    const formatParams = {
      ...params,
      overdueCaseNo,
      accountName,
      repayMode: '10', // 催收回款
      secondProductCode: productSecondaryCode,
    };
    const data = await offlineRepayReviewList(formatParams);
    return data;
  }

  function contentElement() {
    return (
      <div style={{ margin: 20 }}>
        <div>
          <Button
            type="primary"
            onClick={() => {
              if (props.productClassicCode === '01' && props.productCode !== '010101') {
                message.warning('当前仅支持明保-一次本息账单催收回款');
              } else {
                handleModalVisible(true);
              }
            }}
            disabled={!props.isSettle || props.isSettle === 40 || props.isSettle === 50}
            className={globalStyle.mb10}
          >
            提交催收回款
          </Button>
        </div>
        <Tabs
          defaultActiveKey="1"
          onChange={(key) => {
            if (key === '1') setIsNewTable(true);
            else setIsNewTable(false);
          }}
        >
          <TabPane tab="回款记录（新）" key="1">
            <ProTable<CallPayMoneyListItem>
              columns={columns}
              actionRef={actionRef}
              scroll={{ x: 'max-content' }}
              rowKey={(row) => row.createdAt + row.amount}
              search={false}
              options={false}
              toolBarRender={false}
              request={async (params) => {
                return getOrderList(params).then((res) => {
                  return {
                    data: (res?.data as unknown) as CallPayMoneyListItem[],
                    total: res?.total,
                    success: true,
                  };
                });
              }}
            />
          </TabPane>
          <TabPane tab="回款记录（旧）" key="2">
            <ProTable<CallPayMoneyListItem>
              columns={columns}
              actionRef={actionRef}
              scroll={{ x: 'max-content' }}
              rowKey={(row) => row.createdAt + row.amount}
              search={false}
              options={false}
              toolBarRender={false}
              dataSource={callPayMoneyList}
              pagination={false}
            />
          </TabPane>
        </Tabs>
      </div>
    );
  }

  return (
    <>
      {!hideCard && <> {contentElement()}</>}
      {hideCard && <>{contentElement()}</>}
      {/* 不展示时不触发组件内部生命周期 */}
      {modalVisible && (
        <SubmitCallBackModal
          onOk={async () => {
            handleModalVisible(false);
            props.refresh();
          }}
          overdueCaseNo={overdueCaseNo}
          onCancel={() => {
            handleModalVisible(false);
          }}
          onVisibleChange={handleModalVisible}
          modalVisible={modalVisible}
          deadLine={props.deadLine}
          orderNoList={orderNoList}
          overDueAmount={overDueAmount}
          isFactoring={props.productClassicCode === '01'}
          productCode={props.productCode}
          productSecondaryCode={props.productSecondaryCode}
          repayOverdueCostBO={props.repayOverdueCostBO}
          userNo={props.userNo}
          billType={props.billType}
          accountName={props.accountName}
          funderChannelCode={props.funderChannelCode}
        />
      )}
    </>
  );
};

export default CallPayMoneyList;
