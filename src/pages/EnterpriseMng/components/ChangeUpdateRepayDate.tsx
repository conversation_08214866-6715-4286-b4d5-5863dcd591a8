/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-06 14:36:44
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-12-07 09:59:52
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/ChangeUpdateRepayDate.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import globalStyle from '@/global.less';
import { getUuid } from '@/utils/utils';
import ProForm, {
  // ProFormText,
  // ProFormSelect,
  ProFormRadio,
} from '@ant-design/pro-form';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, useModel } from '@umijs/max';
import { Button, Form, Input, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import type { LogListItem } from '../data.d';
import enterpriseMngStyle from '../index.less';
import { getUpdateRepayDayLog, modifyRepayData } from '../service';
interface ChangeUpdateRepayDateProps {
  updateRepayDayVisible: boolean;
  handleUpdateRepayDayVisible: (visible: boolean) => void;
  currentData: Record<string, any>;
  refresh: () => void;
  // repayDateInitial: number;
}
const ChangeUpdateRepayDate: React.FC<ChangeUpdateRepayDateProps> = ({
  updateRepayDayVisible,
  handleUpdateRepayDayVisible,
  currentData,
  refresh,
  // repayDateInitial,
}) => {
  const userNo = history?.location?.query?.userNo as string;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [mode, setModel] = useState<string>('变更');
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  useEffect(() => {
    form?.setFieldsValue({ repayDate: currentData?.repayDate });
  }, [currentData?.repayDate]);
  // console.log(currentData);
  const confirm = (repayDate: number) => {
    Modal.confirm({
      // title: '提示',
      // icon: <ExclamationCircleOutlined />,
      content: (
        <>
          确认将【{currentData?.userName}】还款日变更为
          <span style={{ color: 'red' }}>D+{repayDate}</span>
          吗？变更后将在本月账单开始生效
        </>
      ),
      centered: true,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const success = await modifyRepayData({
          operationToken: getUuid(),
          productSecondTypeCode: currentData?.productSecondTypeCode,
          repayDate,
          userNo,
          updateAt: currentUser?.accountName,
          oldRepayDate: currentData?.repayDate,
        });
        if (success) {
          // actionRef?.current?.reload();
          // run();
          refresh();
          message.success('变更成功');
        }
      },
    });
  };

  const contentItem = { repayDate: '账期天数变更' };
  const columns: ProColumns<LogListItem>[] = [
    {
      title: '产品名称',
      dataIndex: 'productSecondCodeName',
      // render: () => {
      //   return '明保';
      // },
    },
    {
      title: '变更前',
      dataIndex: 'preValue',
      className: enterpriseMngStyle.textTop,
      // render: (_, record) => {
      //   return itemTableMap(record?.beforeValue);
      // },
    },
    {
      title: '变更后',
      dataIndex: 'curValue',
      // className: enterpriseMngStyle.textTop,
      // render: (_, record) => {
      //   return itemTableMap(record?.afterValue);
      // },
    },
    {
      title: '变更内容',
      dataIndex: 'changeType',
      render: (_, record) => {
        return contentItem[record?.changeType as string] || '-';
      },
    },
    {
      title: '变更时间',
      dataIndex: 'updatedAt',
    },
    {
      title: '变更人',
      dataIndex: 'changeBy',
    },
  ];
  return (
    <Modal
      title="变更还款日"
      centered
      width={600}
      className={globalStyle.formModal}
      open={updateRepayDayVisible}
      afterClose={() => {
        // run(form.getFieldValue('productCode'));
        form.resetFields();
        setModel('变更');
      }}
      onCancel={() => handleUpdateRepayDayVisible(false)}
      footer={null}
      // onVisibleChange={handleUpdateRepayDayVisible}
    >
      <ProFormRadio.Group
        style={{
          margin: 16,
        }}
        radioType="button"
        fieldProps={{
          value: mode,
          onChange: (e) => {
            setModel(e.target.value);
          },
        }}
        options={['变更', '变更日志']}
      />
      {mode === '变更' && (
        <ProForm
          className={`${enterpriseMngStyle.formModal} ${globalStyle.mt20}`}
          form={form}
          layout="horizontal"
          initialValues={{ repayDate: currentData?.repayDate }}
          submitter={{
            render: (propsSubmitter) => (
              <div className="buttonCss">
                <Button
                  type="primary"
                  key="submit"
                  loading={loading}
                  onClick={() => propsSubmitter.form?.submit?.()}
                >
                  提交
                </Button>
                <Button
                  key="rest"
                  onClick={() => handleUpdateRepayDayVisible(false)}
                  className={globalStyle?.ml10}
                >
                  取消
                </Button>
              </div>
            ),
          }}
          onFinish={async (values) => {
            // console.log(values);
            setLoading(true);
            await confirm(values?.repayDate);
            setLoading(false);
            handleUpdateRepayDayVisible(false);
          }}
        >
          <Form.Item
            label="还款日"
            name="repayDate"
            rules={[
              { required: true },
              { pattern: /^[0-9]*[1-9][0-9]*$/, message: '输入正整数' },
              {
                validator: (_, value) => {
                  if (currentData && value && value <= currentData?.lastConfirmDate) {
                    return Promise.reject(new Error('还款日需大于最晚确认账单日'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input style={{ width: '80%' }} addonBefore="D+" min={1} type="number" />
          </Form.Item>
        </ProForm>
      )}
      {mode === '变更日志' && (
        <div>
          <ProTable<LogListItem>
            actionRef={actionRef}
            rowKey={getUuid}
            scroll={{ x: 'max-content' }}
            // dataSource={[{ cost: 500, amountDue: 2 }]}
            columns={columns}
            toolBarRender={false}
            search={false}
            request={(params) =>
              getUpdateRepayDayLog({
                ...params,
                userNo: userNo,
                productSecondCode: currentData?.productSecondTypeCode,
              })
            }
          />
        </div>
      )}
    </Modal>
  );
};

export default ChangeUpdateRepayDate;
