import { isPdfUrl, promiseGetBlob, saveAs } from '@/utils/utils';
import { Image, message } from 'antd';
import type { ReactNode } from 'react';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import './index.less';

export type PreviewFileParams = {
  url: string;
  fileName?: string;
  urlList?: string[];
};

export type PreviewOptions = {
  footer?: ReactNode;
};

export type ImagePreviewInstance = {
  previewFile: (val: PreviewFileParams, props?: PreviewOptions) => void;
};

type ImagePreviewProps = {
  children?: React.ReactChild;
  url?: string; // 点击的当前url
  urlList?: string[]; // 支持多图预览
  fileName?: string; //  下载可能需要一个文件名称
  footer?: ReactNode;
};

const ImagePreview = forwardRef<ImagePreviewInstance, ImagePreviewProps>((props, ref) => {
  const { children, url, urlList } = props;
  const [visible, setVisible] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const divRef = useRef<HTMLDivElement>(null);
  const [previewUrlList, setPreviewUrlList] = useState([]);
  const [current, setCurrent] = useState<number>(previewUrlList.indexOf(url));
  const [footer, setFooter] = useState(null);

  const observerRef = useRef<{ observer: MutationObserver | null }>({
    observer: null,
  });

  // 当前预览的pdf
  const [pdfUrl, setPdfUrl] = useState('');

  function setMaxWidth(previewImg: any, parity: 'odd' | 'even') {
    if (parity === 'odd') {
      previewImg.style.maxWidth = `${(window.innerHeight / window.innerWidth) * 100}%`;
    }
    if (parity === 'even') {
      previewImg.style.maxWidth = '100%';
    }
  }

  const canPreview = (url: string) => {
    if (!url) return false;
    const reg = /\.(png|jpg|jpeg|bmp|gif|pdf)/;
    return reg.test(url);
  };

  useEffect(() => {
    const observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
          const { target } = mutation;
          const transform = target.style.transform || '';
          const reg = /translate3d.+rotate\(-?(?<num>\d+)deg\)/;
          const num = transform?.match(reg)?.groups?.num;
          if ((num / 90) % 2 !== 0) {
            // 说明是奇数
            setMaxWidth(target, 'odd');
          } else {
            setMaxWidth(target, 'even');
          }
        }
      }
    });
    observerRef.current.observer = observer;
  }, []);

  useEffect(() => {
    const previewImg = document.getElementsByClassName('ant-image-preview-img');
    for (let i = 0; i < previewImg?.length; i++) {
      const img: any = previewImg[i];
      if (observerRef.current?.observer) {
        observerRef.current.observer.observe(img, {
          attributes: true,
          attributeFilter: ['style'],
        });
      }
    }
    return () => {
      if (observerRef.current?.observer) {
        observerRef.current.observer.disconnect();
      }
    };
  }, [visible]);

  useEffect(() => {
    const list = urlList?.length
      ? urlList.filter((value: string) => canPreview(value))
      : [url].filter((value1: string) => canPreview(value1));

    setPreviewUrlList(list);
    setCurrent(list.indexOf(url));
  }, [urlList, url]);

  useEffect(() => {
    if (props.footer) {
      setFooter(props.footer);
    }
  }, [props.footer]);

  const updatePdfUrl = async (url: string) => {
    message.loading('正在加载文件...');
    const blobParam = await promiseGetBlob(url);
    const blob = new Blob([blobParam], {
      type: 'application/pdf;chartset=UTF-8',
    });
    const fileURL = URL.createObjectURL(blob);
    setPdfUrl(fileURL);
    setVisible(true);
    message.destroy();
  };

  // 响应头可能是 Content-Disposition: attachment 就不能预览了
  // url 当前点击的url， urlList 多图预览
  const handlePreviewFile = async (url: string, fileName?: string) => {
    try {
      const reg = /\.(png|jpg|jpeg|bmp|gif|pdf)/;
      const pathname = new URL(url)?.pathname;
      const ext = pathname.toLocaleLowerCase().match(reg)?.[1];
      if (ext) {
        if (isPdfUrl(url)) {
          updatePdfUrl(url);
        } else {
          setVisible(true);
        }
      } else {
        // 没有匹配到 则下载
        message.loading({ content: '下载中...', duration: 0 });
        const blob = await promiseGetBlob(url);
        message.destroy();
        const name = fileName || pathname.split('/').at(-1) || '';
        saveAs(blob, name);
      }
    } catch (error) {
      console.log('error', error);
      message.error('预览或下载有误,请核实');
    }
  };

  const previewFile = (params: PreviewFileParams, options: PreviewOptions) => {
    const { url, fileName, urlList } = params;
    let list = urlList?.length ? urlList : [url];
    list = list.filter((url) => canPreview(url));
    setPreviewUrlList(list);
    setCurrent(list.indexOf(url));
    handlePreviewFile(url, fileName);
    if (options?.footer) {
      setFooter(options.footer);
    }
  };

  const isPdfNode = () => {
    const url = previewUrlList[current];
    return isPdfUrl(url);
  };

  useImperativeHandle(ref, () => {
    return {
      // ... 你的方法 ...
      previewFile,
    };
  });

  return (
    <div
      ref={divRef}
      style={{ cursor: 'pointer' }}
      onClick={() => {
        if (url) {
          handlePreviewFile(url || '');
        }
      }}
      className={`root-image-preview`}
    >
      {children}
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
        ref={containerRef}
      >
        {/* 为了防止 冒泡 执行了 _previewFile  */}
        <Image.PreviewGroup
          preview={{
            visible,
            onVisibleChange: (visible: boolean) => {
              setVisible(visible);
              if (!visible) {
                if (pdfUrl) {
                  URL.revokeObjectURL(pdfUrl);
                }
              }
            },
            getContainer: () => containerRef.current!,
            current,
            onChange: (current, prev) => {
              const url = previewUrlList[current];
              if (isPdfUrl(url)) {
                updatePdfUrl(url);
              }
              setCurrent(current);
            },
            imageRender: (originalNode, info) => {
              const { current } = info;
              const url = previewUrlList[current];
              if (isPdfUrl(url)) {
                return (
                  <iframe src={pdfUrl} width={'90%'} height={'90%'} style={{ border: 'none' }} />
                );
              }
              return originalNode;
            },
            toolbarRender: (originalNode) => {
              return (
                <div>
                  {!isPdfNode() ? originalNode : null}
                  {/* 外部自定义footer */}
                  {footer}
                </div>
              );
            },
          }}
        >
          {previewUrlList?.map((url, index) => {
            return <Image key={index} style={{ display: 'none' }} src={url} />;
          })}
        </Image.PreviewGroup>
      </div>
    </div>
  );
});

export default ImagePreview;
