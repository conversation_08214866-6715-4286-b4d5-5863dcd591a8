import {
  CarInsuranceStatusMap,
  EcarInsuranceStatus,
  LoanReceiptTicketStatusMap,
} from '@/utils/bankend/enum';
import { downLoadExcel } from '@/utils/utils';
import type { ProColumns } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Button, message, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

import { getChannelInfo, getLoanReceipt, getProductNameEnum } from '../services';
import type { IcarInsuranceItem } from '../type';
import { LEVEL } from '../type';
import { receiveOrAbandonOrReassignActionRequest } from '../utils';

// 状态对应的按钮文案
const buttonTextMap = {
  [EcarInsuranceStatus.PENDING]: '放弃',
  [EcarInsuranceStatus.TO_BE_COLLECTED]: '领取',
};

// 状态对应的审核文案
const renderRecordText = (status: EcarInsuranceStatus) => {
  if (status === EcarInsuranceStatus.PENDING) {
    return '领取';
  }
  if (status && status > EcarInsuranceStatus.PENDING) {
    return '审核';
  }
  return '';
};

function getColumns(
  access: any,
  currentUser: Record<string, any>,
  employeeList: any[],
  actionRef: any,
  showReassignModal: (record: any) => void,
) {
  const columns: ProColumns<IcarInsuranceItem>[] = [
    {
      dataIndex: 'orderNo',
      title: '订单号',
    },
    {
      title: '客户',
      dataIndex: 'companyName',
      search: false,
      render(text, record) {
        const { companyName, orgCode } = record;
        return (
          <>
            <div>{companyName}</div>
            <div>{orgCode}</div>
          </>
        );
      },
    },
    {
      title: '车辆信息',
      dataIndex: 'carInfoList',
      search: false,
      render(text, record) {
        const { carInfoList } = record;
        const title = carInfoList.map((item) => {
          const { plateNo, vin } = item;
          return (
            <>
              <pre>
                {plateNo} {vin}
              </pre>
            </>
          );
        });
        return (
          <Tooltip title={title} overlayInnerStyle={{ width: 'max-content' }}>
            <a>{carInfoList.length}辆</a>
          </Tooltip>
        );
      },
    },
    {
      title: '分期金额(元)',
      dataIndex: 'repayAmount',
      search: false,
    },
    {
      title: '总保费金额(元)',
      dataIndex: 'applyAmount',
      search: false,
    },
    {
      title: '首付款(元)',
      dataIndex: 'downPayments',
      search: false,
      render(_, record) {
        const { downPayment, paymentDate, onlinePayment } = record?.downPaymentsDTO || {};
        return (
          <div>
            <div>
              ¥ {downPayment} {onlinePayment ? '线上支付' : paymentDate ? '线下支付' : '未支付'}
            </div>
            <div>{paymentDate}</div>
          </div>
        );
      },
    },
    {
      title: '贷款期限',
      dataIndex: 'term',
      search: false,
    },

    {
      title: '状态',
      dataIndex: 'status',
      formItemProps: {
        name: 'orderStatus',
      },
      // fieldProps: {
      //   mode: 'multiple',
      // },
      valueType: 'select',
      valueEnum: CarInsuranceStatusMap,
    },
    {
      title: '投保主体',
      dataIndex: 'insuranceSubject',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelName',
      search: false,
    },
    {
      title: '渠道名称',
      dataIndex: 'channelCode',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        showSearch: true,
      },
      search: currentUser?.channelLevel === LEVEL.SECOND_CHANNEL ? false : undefined, // undefined 相当于 true  二级渠道不展示
      // 渠道用户只有一个渠道 不需要搜索
      request: async () => {
        const data = await getChannelInfo({
          channelLevel: currentUser?.channelLevel,
          channelCode: currentUser?.channelCode,
        });
        return data.map((item) => {
          const { channelName, channelCode } = item;
          return {
            label: channelName,
            value: channelCode,
            key: channelCode,
          };
        });
      },
    },
    {
      title: '审核人员',
      dataIndex: 'processRecord',
      search: false,
      render(text, record) {
        const { processRecord, status } = record;
        if (!!processRecord && status > EcarInsuranceStatus.TO_BE_COLLECTED) {
          return (
            <>
              <div>{processRecord?.processName}</div>
              <div style={{ color: 'gray' }}>
                {processRecord?.processTime} {renderRecordText(record?.status)}
              </div>
            </>
          );
        }
        return '/';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      // 最近半年
      initialValue: [
        dayjs().subtract(1, 'year').format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
      search: {
        transform: (value) => {
          return {
            createStartTime: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
            createEndTime: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
          };
        },
      },
      hideInTable: true,
    },
    {
      title: '车牌号',
      dataIndex: 'plateNo',
      hideInTable: true,
    },
    {
      title: '车架号',
      dataIndex: 'vin',
      hideInTable: true,
    },
    {
      title: '客户名称',
      dataIndex: 'companyName',
      hideInTable: true,
    },
    {
      title: '身份证/社会信用代码',
      dataIndex: 'certiNo',
      hideInTable: true,
      colSize: 1,
      formItemProps: {
        labelCol: { span: 12 },
        wrapperCol: { span: 16 },
      },
    },
    {
      title: '产品名称',
      // width: 120,
      dataIndex: 'productCode',
      // valueEnum: productNameList,
      valueType: 'select',
      request: () => {
        return getProductNameEnum('CAR_INSURANCE');
      },
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      hideInTable: true,
    },
    {
      title: '放款时间',
      dataIndex: 'lendingTime',
      valueType: 'dateRange',
      // 最近半年
      // initialValue: [
      //   dayjs().subtract(6, 'month').format('YYYY-MM-DD'),
      //   dayjs().format('YYYY-MM-DD'),
      // ],
      search: {
        transform: (value) => {
          return {
            lendingStartTime: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
            lendingEndTime: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
          };
        },
      },
      hideInTable: true,
    },
    {
      title: '借款人类型',
      dataIndex: 'channelType',
      valueType: 'select',
      valueEnum: {
        5: '企业',
        6: '个人',
      },
    },
    {
      title: '处理人员',
      hidden: true,
      dataIndex: 'operatorUserId',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options:
          employeeList?.map(({ employeeName, pid }) => ({
            label: employeeName ?? '',
            value: pid ?? '',
          })) || [],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      key: 'option',
      render: (text, record) => {
        return [
          // 运营管理员 && 状态为待审核 10
          access.hasAccess('reassign_order_car_insurance_list') &&
            record?.status === EcarInsuranceStatus.PENDING && (
              <Button
                key="reassign"
                variant="link"
                type="link"
                onClick={() => {
                  // 显示分配弹窗
                  showReassignModal(record);
                }}
              >
                重新分派
              </Button>
            ),
          // 运营管理/运营&&状态为待领取|待审核 [2, 10] 才显示
          access.hasAccess('collect_order_car_insurance_list') &&
            record?.status === EcarInsuranceStatus.TO_BE_COLLECTED && (
              <Button
                key="receive"
                variant="link"
                type="link"
                onClick={() => {
                  receiveOrAbandonOrReassignActionRequest(record?.status, {
                    orderNo: record?.orderNo,
                    operatorUserId: currentUser?.pid,
                  }).then(() => {
                    message.success('操作成功');
                    actionRef?.current?.reload();
                  });
                }}
              >
                {buttonTextMap[record?.status] || ''}
              </Button>
            ),
          // 待审核状态下的操作，操作人为非个人或运营主管时，不展示放弃按钮
          access.hasAccess('forsake_order_car_insurance_list') &&
            record?.status === EcarInsuranceStatus.PENDING &&
            // 运营管理专属权限
            (access.hasAccess('reassign_order_car_insurance_list') ||
              currentUser?.pid === record?.operatorUserId) && (
              <Button
                key="forsake"
                variant="link"
                type="link"
                onClick={() => {
                  receiveOrAbandonOrReassignActionRequest(record?.status, {
                    orderNo: record?.orderNo,
                    operatorUserId: currentUser?.pid,
                  }).then(() => {
                    message.success('操作成功');
                    actionRef?.current?.reload();
                  });
                }}
              >
                {buttonTextMap[record?.status] || ''}
              </Button>
            ),
          <Button
            key="editable"
            variant="link"
            hidden={!access.hasAccess('detail_car_insurance_list')} // 非管理员 不展示查看详情按钮
            type="link"
            onClick={() => {
              history.push(`/businessMng/car-insurance/detail?orderNo=${record.orderNo}`);
            }}
          >
            查看详情
          </Button>,
          <Button
            key="multiplex"
            variant="link"
            type="link"
            onClick={() => {
              history.push(`/businessMng/car-insurance/detail?multiplexOrderNo=${record.orderNo}`);
            }}
          >
            复用
          </Button>,
          LoanReceiptTicketStatusMap.includes(record?.status) && (
            <Button
              variant="link"
              type="link"
              onClick={async () => {
                const res = await getLoanReceipt(record?.orderNo);
                downLoadExcel(res, 'application/zip;charset=UTF-8');
              }}
            >
              放款回单
            </Button>
          ),
        ];
      },
    },
  ];

  return columns;
}
export default getColumns;
