/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-07-03 10:42:05
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-07-03 16:43:16
 * @FilePath: /code/lala-finance-biz-web/src/pages/AwardManager/service.ts
 * @Description: AwardManager
 */
import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { StandardPrizePageProps, StandardPrizeProps } from './data';

// 【标准奖品】分页查询标准奖品信息
export async function getStandardPrizePage(data: StandardPrizePageProps) {
  return request('/bizadmin/marketing/standard/prize/page', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
    ifTrimParams: true,
  });
}
// 创建标准奖品|【标准奖品】新增标准奖品
export async function createStandardPrize(data: StandardPrizeProps) {
  return request('/bizadmin/marketing/standard/prize/create', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}
// 更新标准奖品|【标准奖品】更新标准奖品
export async function updateStandardPrize(data: StandardPrizeProps) {
  return request('/bizadmin/marketing/standard/prize/update', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}
// 标准奖品库存修改
export async function updateStandardPrizeInventory(data: { id: number; newTotalNum: number }) {
  return request('/bizadmin/marketing/standard/prize/inventory/change', {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}
// 标准奖品库存删除
export async function deleteStandardPrize(id: string) {
  return request('/bizadmin/marketing/standard/prize/invalid', {
    method: 'POST',
    data: { id },
    headers: bizAdminHeader,
  });
}
