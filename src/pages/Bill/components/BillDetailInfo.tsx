import globalStyle from '@/global.less';
import { mapStatusZh } from '@/pages/BusinessCashMng/const';
import { downLoadExcel, getBlob, isExternalNetwork, saveAs } from '@/utils/utils';
import { DownloadOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history, Link } from '@umijs/max';
import { Button, Card, Dropdown, Menu, message } from 'antd';
import React from 'react';
import type { BillDetailInfoItem } from '../data';
import {
  custmerBillExport,
  custmerBillExportExcel,
  getBillListDetail,
  internalBillExport,
} from '../service';
// import { getOssPath } from '../../BusinessMng/service';

const termMap = {
  '1': '固定还款日',
};
const columns: ProColumns<BillDetailInfoItem>[] = [
  { title: '订单号', dataIndex: 'orderNo', key: 'orderNo' },
  { title: '进件时间', dataIndex: 'incomingTime', key: 'incomingTime' },
  {
    title: '还款期限',
    dataIndex: 'repayTerm',
    key: 'repayTerm',
    render: (_, record) => {
      return <div>{termMap[record.repayTerm]}</div>;
    },
  },
  { title: '应还总额', dataIndex: 'amountDue', key: 'amountDue' },
  {
    title: '剩余应还款总额',
    dataIndex: 'totalRemainingRepayment',
    key: 'totalRemainingRepayment',
  },
  { title: '应还款日', dataIndex: 'repayTime', key: 'repayTime' },
  {
    title: '还款状态',
    dataIndex: 'status',
    key: 'status',
    render: (_, record) => {
      return <div>{mapStatusZh[record.status] || '-'}</div>;
    },
  },
  // { title: '业务订单信息', dataIndex: 'bizOrderMsg', key: 'bizOrderMsg' },
  { title: '业务订单号', dataIndex: 'bizOrderNo', key: 'bizOrderNo' },
  {
    title: '操作',
    dataIndex: 'repayTime',
    fixed: 'right',
    key: 'repayTime',
    render: (_, record) => (
      <>
        <Link to={`/businessMng/detail?orderNo=${record.orderNo}`}>查看订单详情</Link>
      </>
    ),
  },
];

const BillDetailInfo: React.FC<any> = () => {
  // const data = props.billDetail || [];
  const { accountNumber, billNo } = history.location.query;
  // const [ customerUrl, setCustomer ] = useState({});
  const downOwneBill = () => {
    internalBillExport({ accountNumber, billNo }).then((res) => {
      downLoadExcel(res);
    });
  };
  // 待优化
  const downCustmerBill = () => {
    custmerBillExport({ accountNumber, billNo }).then((res) => {
      if (res.data.netWorkPath) {
        // getOssPath(res.data.filePath).then((resData) => {
        getBlob(res.data.netWorkPath, (blob: Blob) => {
          saveAs(blob, res.data.fileDesc || '客户账单-pdf');
        });
        // });
      } else {
        message.warning('账单-pdf未生成');
      }
    });
  };
  //
  const downCustmerBillExcel = () => {
    custmerBillExportExcel({ accountNumber, billNo }).then((res) => {
      if (res.data.netWorkPath) {
        // getOssPath(res.data.filePath).then((resData) => {
        getBlob(res.data.netWorkPath, (blob: Blob) => {
          saveAs(blob, res.data.fileDesc || '客户账单-excel');
        });
        // });
      } else {
        message.warning('账单-excel未生成');
      }
    });
  };
  return (
    <Card
      title="账单明细"
      className={globalStyle.mt30}
      extra={
        !isExternalNetwork() && (
          <Dropdown
            key="menu"
            overlay={
              <Menu>
                <Menu.Item
                  key="1"
                  onClick={() => {
                    downOwneBill();
                  }}
                >
                  内部账单
                </Menu.Item>
                <Menu.Item
                  key="2"
                  onClick={() => {
                    downCustmerBill();
                  }}
                >
                  客户账单-pdf
                </Menu.Item>
                <Menu.Item
                  key="3"
                  onClick={() => {
                    downCustmerBillExcel();
                  }}
                >
                  客户账单-excel
                </Menu.Item>
              </Menu>
            }
          >
            <Button type="primary">
              导出
              <DownloadOutlined />
            </Button>
          </Dropdown>
        )
      }
    >
      <ProTable
        columns={columns}
        request={async (params: { pageSize: number; current: number }) => {
          const { data, total } = await getBillListDetail({
            accountNumber,
            billNo,
            current: params.current,
            pageSize: params.pageSize,
          });
          return {
            data,
            success: true,
            total,
          };
        }}
        search={false}
        toolBarRender={false}
        rowKey="orderNo"
        scroll={{ x: 'max-content' }}
      />
    </Card>
  );
};

export default BillDetailInfo;
