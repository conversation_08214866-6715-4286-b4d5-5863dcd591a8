/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-29 10:38:32
 * @LastEditors: alan771.tu <EMAIL>
 * @LastEditTime: 2024-11-12 15:44:50
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/components/ApplyReliefModal.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {
  ModalForm,
  // ProFormUploadButton,
  ProFormDigit,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-form';
import { useModel } from '@umijs/max';
import { Button, Form, message } from 'antd';
import React, { useState } from 'react';
// import { getBaseUrl } from '@/utils/utils';
import { PRODUCT_CLASSIFICATION_CODE } from '@/enums';
import type { ApplyReliefParams } from '../data';
import { applyRelief } from '../service';
import { UploadCom } from './index';

export type AddReliefModalProps = {
  onCancel: () => void;
  onOk: () => Promise<void>;
  modalVisible: boolean;
  // values: CallPayForm|{};
  onVisibleChange: any;
  overDueAmount: number;
  overdueCaseNo: string;
  productCode: string;
  orderNoList: { orderNo: string; totalOverdueAmount: string }[];
};

const AddReliefModal: React.FC<AddReliefModalProps> = (props) => {
  // const { overdueCaseNo } = history.location.query;

  const { overdueCaseNo, orderNoList, productCode } = props;

  const { urgePersonList: userList } = useModel('userList');
  // const { data } = useRequest(() => {
  //   return getUserList();
  // });
  // const options =
  //   data?.map((item: { id: number; username: string }) => {
  //     return { value: item.id, label: item.username };
  //   }) || [];
  // const base_url = getBaseUrl();
  // const upload_url = `${base_url}/loan/audit/uploadCreditFile`;
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  return (
    <>
      <ModalForm
        title="申请减免"
        width={600}
        form={form}
        layout="horizontal"
        open={props.modalVisible}
        onOpenChange={props.onVisibleChange}
        initialValues={{ urgePerson: currentUser?.userId }}
        modalProps={{
          centered: true,
          okText: '提交',
          destroyOnClose: true,
          okButtonProps: { disabled: true },
        }}
        submitter={{
          render: (propsSubmitter, defaultDoms) => (
            <div className="buttonCss">
              <Button
                type="primary"
                key="submit"
                disabled={loading}
                onClick={() => propsSubmitter.form?.submit?.()}
              >
                提交
              </Button>
              {defaultDoms[0]}
            </div>
          ),
        }}
        onFinish={async (value) => {
          const fileList = [];
          const length = value?.fileList?.length || 0;
          for (let i = 0; i < length; i += 1) {
            const item = value.fileList[i];
            if (item.response) {
              const url = item.response.data;
              if (url) {
                fileList.push({ fileUrl: url, fileName: item.name });
              }
            }
          }
          try {
            await applyRelief({
              overdueCaseNo,
              ...value,
              orderNo: value?.orderNo?.value,
              fileList,
            } as ApplyReliefParams);
            message.success('添加成功');
            props.onOk();
            // eslint-disable-next-line no-empty
          } catch (error) {}
          return true;
        }}
      >
        <ProFormSelect
          rules={[{ required: true }]}
          labelCol={{ span: 5 }}
          request={async () => userList}
          placeholder="请输入催收员"
          fieldProps={{
            showSearch: true,
            optionFilterProp: 'label',
            disabled: true,
            // filterOption: (input: string, option: { label: string }) =>
            //   option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
          }}
          width="md"
          name="urgePerson"
          label="催收员"
        />
        {productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN && (
          <ProFormSelect
            rules={[{ required: true }]}
            labelCol={{ span: 5 }}
            request={async () => orderNoList}
            placeholder="请输入订单号"
            fieldProps={{
              showSearch: true,
              optionFilterProp: 'label',
              labelInValue: true,
            }}
            width="md"
            name="orderNo"
            label="订单号"
          />
        )}
        <ProFormDigit
          name="amount"
          width="md"
          labelCol={{ span: 5 }}
          label="减免金额(元)"
          placeholder="请输入减免金额"
          fieldProps={{ min: 0, precision: 2 }}
          rules={[
            { required: true },
            {
              validator: (_, val) => {
                // 有坑，同时出现很多个error,待优化
                // if (/^\d+(\.\d+)?$/.test(val) && val && val > props.overDueAmount) {
                //   // callBack('减免金额不能大于逾期金额');
                //   return Promise.reject(new Error('减免金额不能大于逾期金额'));
                // }
                //区分产品，小贷有多个循环额度，按订单维度区分，回款金额也会按每个订单的限度
                // 有坑，同时出现很多个error,待优化
                //融租
                if (
                  productCode?.substring(0, 2) !== PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN &&
                  /^\d+(\.\d+)?$/.test(val) &&
                  val &&
                  val > props.overDueAmount
                ) {
                  // callBack('回款金额不能大于逾期金额');
                  return Promise.reject(new Error('减免金额不能大于逾期金额'));
                }
                //小贷区分订单
                if (
                  productCode?.substring(0, 2) === PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN &&
                  /^\d+(\.\d+)?$/.test(val) &&
                  val &&
                  val > Number(form?.getFieldValue('orderNo')?.totalOverdueAmount)
                ) {
                  // console.log('---', form?.getFieldValue('orderNo')?.totalOverdueAmount);
                  return Promise.reject(new Error('减免金额不能大于逾期金额'));
                }
                if (val && !/^\d+(\.\d+)?$/.test(val) && val < props.overDueAmount) {
                  // callBack();
                  return Promise.reject(new Error('请输入数字'));
                }
                // callBack();
                return Promise.resolve();
              },
            },
          ]}
        />
        <ProFormTextArea
          labelCol={{ span: 5 }}
          placeholder="请输入备注"
          width="md"
          fieldProps={{ maxLength: 500 }}
          label="备注"
          name="urgeRecentlyMsg"
        />
        <UploadCom
          span={5}
          afterUpload={() => {
            setLoading(false);
          }}
          uploading={() => {
            setLoading(true);
          }}
        />
      </ModalForm>
    </>
  );
};

export default AddReliefModal;
