/*
 * @Author: your name
 * @Date: 2020-12-18 14:25:46
 * @LastEditTime: 2021-01-21 14:59:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Debt/service.ts
 */
import { request } from '@umijs/max';
import type { DebtListParams } from './data';

export async function queryDebt(params?: DebtListParams) {
  return request('/repayment/cms/debt', {
    params,
    ifTrimParams: true,
  });
}

export async function getDebtInfo(debtNo: string) {
  return request(`/repayment/cms/debt/${debtNo}`);
}

export async function getDebtLoanInfo(debtNo: string) {
  return request(`/quota/lending/getLendingInfoByDebtNo/${debtNo}`);
}

export async function debtExport(params: DebtListParams) {
  return request('/repayment/cms/debt/export', {
    responseType: 'blob',
    params,
    getResponse: true,
    ifTrimParams: true,
  });
}
