/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-02 17:56:51
 * @LastEditors: elisa.zhao <EMAIL>
 * @LastEditTime: 2022-10-08 17:36:03
 * @FilePath: /lala-finance-biz-web/src/pages/EnterpriseMng/components/SecondAccount.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { Alert, Table } from 'antd';
import React, { useState } from 'react';
// import { history } from '@umijs/max';
// import { getSlaveAccount } from '../service';
import AuthorizedChangeModal from './AuthorizedChangeModal';
interface SecondAccountProps {
  productCode?: string;
  tableProps?: any;
  refreshSlave: () => void;
}
const SecondAccount: React.FC<SecondAccountProps> = ({ productCode, tableProps, refreshSlave }) => {
  // const subAccountNo = history?.location?.query?.subAccountNo as string;

  const ID_TYPE_MAP = {
    1: '身份证',
  };
  const [visibleChangeAuthroized, setVisibleChangeAuthorized] = useState<boolean>(false);
  const [curUserNo, setCurUserNo] = useState<string>('');

  //导出
  // const queryProductOverpaymentExport = (params: { subAccountNo: string }) => {
  //   getSlaveAccountExport(params).then((res) => {
  //     downLoadExcel(res);
  //   });
  // };
  const columns = [
    {
      title: '次账号用户ID',
      dataIndex: 'userNo',
      key: 'userNo',
    },
    {
      title: '授权人姓名',
      dataIndex: 'authorizedName',
      key: 'authorizedName',
    },
    {
      title: '授权人证件类型',
      dataIndex: 'idType',
      key: 'idType',
      render: (idType: number) => {
        return ID_TYPE_MAP[idType] || '';
      },
    },
    {
      title: '授权人证件号',
      dataIndex: 'authorizedIdCard',
      key: 'authorizedIdCard',
    },
    {
      title: '授权人手机号',
      dataIndex: 'authorizedPhone',
      key: 'authorizedPhone',
    },
    {
      title: '授权人邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '操作',
      type: 'option',
      render: (_, row) => {
        return (
          <a
            onClick={() => {
              setVisibleChangeAuthorized(true);
              setCurUserNo(row?.userNo);
            }}
          >
            变更授权人信息
          </a>
        );
      },
    },
  ];
  return (
    <>
      {/* <div style={{ color: 'grey' }}>

      </div> */}
      <Alert
        message=" 次账号和主账号的额度、还款日、状态信息相同，仅有授权人信息不同"
        type="info"
        showIcon
      />
      <Table columns={columns} {...tableProps} />
      <AuthorizedChangeModal
        title="变更授权人信息"
        visible={visibleChangeAuthroized}
        onCancel={() => {
          setVisibleChangeAuthorized(false);
        }}
        userNo={curUserNo}
        productCode={productCode}
        onVisibleChange={setVisibleChangeAuthorized}
        refresh={() => {
          // refreshBase();
          // refreshRisk();
          //刷新
          // actionRef?.current?.reload();
          refreshSlave();
        }}
      />
    </>
  );
};

export default SecondAccount;
