/*
 * @Author: your name
 * @Date: 2021-06-28 11:17:53
 * @LastEditTime: 2021-07-13 10:03:51
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/WithHold/index.tsx
 */
import HeaderTab from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import type { FormInstance } from 'antd/lib/form';
import React, { useRef } from 'react';
import type { OverdueConfigListItem } from './data';
import { getOverdueConfigList } from './service';

const columns: ProColumns<OverdueConfigListItem>[] = [
  {
    title: '产品ID',
    dataIndex: 'productCode',
    key: 'productCode',
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
  },
  {
    title: '产品一级分类',
    dataIndex: 'productFirstName',
    key: 'productFirstName',
  },
  {
    title: '产品二级分类',
    dataIndex: 'productSecondName',
    key: 'productSecondName',
    search: false,
  },
  {
    title: '用户类型',
    dataIndex: 'userType',
    key: 'userType',
    search: false,
  },

  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    valueEnum: {
      1: { text: '启用', status: 'Success' },
      0: { text: '禁用', status: 'Processing' },
    },
  },
  {
    title: '操作',
    key: 'option',
    width: 180,
    fixed: 'right',
    valueType: 'option',
    render: (text, row) => (
      <>
        <Link to={`/businessMng/postLoanMng/withhold-detail?productCode=${row.productCode}`}>
          查看详情
        </Link>
        {/* <Button type="link" className={globalStyle.ml10}>
          {row.status === 1 ? '冻结' : '激活'}
        </Button> */}
      </>
    ),
  },
];

const ComUserList: React.FC<{}> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<OverdueConfigListItem>
          columns={columns}
          formRef={formRef}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="userNo"
          search={false}
          request={(params) => getOverdueConfigList(params)}
        />
      </PageContainer>
    </>
  );
};

export default ComUserList;
