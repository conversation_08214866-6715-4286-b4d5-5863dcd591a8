/*
 * @Date: 2023-08-29 17:26:49
 * @Author: elisa.z<PERSON>
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2023-10-23 10:44:06
 * @FilePath: /lala-finance-biz-web/src/pages/HeavyQuotaWhiteList/components/AddHeavyQuotaWhiteListModal.tsx
 * @Description:
 */
import React, { useState } from 'react';
import ProForm, { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Button, Form, message } from 'antd';
import { addWhiteList } from '../service';
import { CLASSIFICATION_CODE_LABEL, SCENE, SECOND_PRODUCT_SOME } from '@/enums';
import { DividerTit } from '@/components';
import OptList from './OptList';
import globalStyle from '@/global.less';

export type AddConfigModalProps = {
  close: () => void;
  onOk: () => Promise<void>;
  visible: boolean;
  showMode?: boolean;
  currentRow?: Record<string, string>;
};

const PreriskWhiteListModal: React.FC<AddConfigModalProps> = ({
  visible,
  close,
  onOk,
  showMode = false,
  currentRow,
}) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  return (
    <>
      <ModalForm
        title={`${showMode ? '查看' : '新建'}配置`}
        layout="horizontal"
        className={globalStyle.formModal}
        visible={visible}
        form={form}
        initialValues={currentRow}
        modalProps={{
          centered: true,
          onCancel: close,
          okText: '提交',
          confirmLoading: loading,
        }}
        submitter={{
          render: (_, defaultDoms) => {
            let doms = defaultDoms;
            if (showMode) {
              doms = [
                <Button
                  key="update"
                  onClick={() => {
                    close();
                  }}
                >
                  关闭
                </Button>,
              ];
            }
            return doms;
          },
        }}
        onFinish={async (values) => {
          setLoading(true);
          addWhiteList({
            ...values,
          })
            .then(() => {
              message.success('添加成功');
              close();
              onOk();
            })
            .finally(() => {
              setLoading(false);
            });
          return true;
        }}
      >
        <DividerTit title="产品信息" style={{ marginTop: 0 }} />
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: true }]}
            disabled
            initialValue={'01'}
            // request={getUserListEnum}
            options={Object.keys(CLASSIFICATION_CODE_LABEL).map((key) => ({
              value: key,
              label: CLASSIFICATION_CODE_LABEL[key],
            }))}
            width="sm"
            name="productType"
            label="产品一级分类"
          />
          <ProFormSelect
            rules={[{ required: true }]}
            disabled
            initialValue={'prerisk_whitelist'}
            options={Object.keys(SCENE).map((key) => ({
              value: key,
              label: SCENE[key],
            }))}
            width="sm"
            name="configScene"
            label="配置场景"
          />
        </ProForm.Group>
        <ProForm.Group>
          <ProFormSelect
            rules={[{ required: true }]}
            options={Object.keys(SECOND_PRODUCT_SOME).map((key) => ({
              value: key,
              label: SECOND_PRODUCT_SOME[key],
            }))}
            width="sm"
            disabled
            initialValue={'0103'}
            name="productCode"
            label="产品名称"
          />
        </ProForm.Group>
        <DividerTit title="配置规则" />
        <ProForm.Group>
          <ProFormText
            rules={[{ required: true }]}
            width="sm"
            disabled={showMode}
            name="enterpriseName"
            label="企业名称"
          />
          <ProFormText
            rules={[{ required: true }]}
            width="sm"
            disabled={showMode}
            name="orgCode"
            label="统一信用代码"
          />
        </ProForm.Group>
        {showMode && <OptList configId={currentRow?.configId} />}
      </ModalForm>
    </>
  );
};

export default PreriskWhiteListModal;
