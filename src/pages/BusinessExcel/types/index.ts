export const fileTypeMap = {
  1: '个人贷款',
  2: '企业贷款',
};
// 1、数据传输中，2、数据提交完成，3、文件读取失败
export const statusMap = {
  1: '数据传输中',
  2: '数据提交完成',
  3: '文件读取失败',
};

// 取出来就是数字
export type IfileType = keyof typeof fileTypeMap;
export type Istatus = keyof typeof statusMap;
export interface IcommitRecordItem {
  id: number;
  yearMonth: string; // 提交年月
  fileId: number; // 文件id
  // 报表类型：1：A55_小额贷款业务（个人贷款）信息表（月报表），2：A56_小额贷款业务（企业贷款）信息表（月报表）
  fileType: IfileType; //
  reportData: string; // 提交情况
  status: Istatus; // 报数状态
  fileName: string; // 文件名称
  successCount: number; // 成功数目
  totalAmount: number; // 成功总金额
  failCount: number; // 失败树目
  updatedAt: string; // 更新时间
  createdAt: string; // 创建时间
  successTotalAmount: number; // 成功总金额
  failTotalAmount: number; //失败总金额
  downloadFileGenerateStatus: keyof typeof downloadFileGenerateStatusLabelMap; // 文件生成状态
  downloadFileUrl: string; // 	提交结果详细表文件路径
  downloadFileNetworkUrl: string; // 	提交结果详细表文件网络路径
}

export enum EdownloadFileGenerateStatus {
  GENERATING = 1, // 正在生成
  DONE = 2, // 完成
  FAIL = 3, // 失败
}

export const downloadFileGenerateStatusLabelMap = {
  1: '正在生成', // 正在生成
  2: '完成', // 完成
  3: '失败', // 失败
};
// type ToStatusString = `${EdownloadFileGenerateStatus}`
// type ToStatusNumber = ToStatusString extends `${infer N extends number}` ? N : never;
//  在ts官网测试下来 最少得 4.8.4版本 支持，但是还是编译报错了  Unexpected token, expected "?"

export interface IfileInfoListItem {
  fileName: string;
  fileType: IfileType; // 报表类型
  fileUrl: string; // 相对路径
  fileSuffix: string; // 文件类型 后缀
}
export interface IuploadExcelParams {
  dataMonth: string;
  fileInfoList: IfileInfoListItem[];
}

export interface IcommitToFinance {
  templateId: string; // 模版id
  taskId: string; // 任务ID
  yearMonth: string; // 报送期数
  subTaskId: string; // 子任务id
  fileIdList: number[]; // 文件id 列表
}

export interface IqueryCommitListParams {
  dataMonthList: string[];
  fileType: IfileType;
  pageSize: number;
  pageNo: number;
}

export interface IuploadExcelRes {
  id: number;
  productSecondCode: string;
  dataMonth: string;
  fileName: string;
  fileUrl: string;
  fileNetworkUrl: string;
  fileType: number;
  classify: number;
  updatedAt: string;
  createdAt: string;
}
