/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2023-08-24 17:29:41
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2023-09-13 14:58:53
 * @FilePath: /code/lala-finance-biz-web/src/access.ts
 * @Description: access
 */
import type { API } from './services/API';
export default function access(initialState: { currentUser?: API.CurrentUser | undefined }) {
  const { currentUser } = initialState || {};
  if (currentUser) {
    const {
      role: roles = [],
      access: accesss = [],
      userId,
      userName,
      passportName,
      devAccessWhiteList,
    } = currentUser;
    const roleArr = roles?.map((item: { roleCode: any }) => {
      return item.roleCode;
    });
    const accessArr = accesss?.map((item: { privilegeCode: any }) => {
      return item.privilegeCode;
    });

    return {
      hasRole: (role: string) => {
        // 规则
        // |（或）：满足其中一个角色即返回 true
        // &（且）：所有角色满足才返回 true
        // 无符号 ：单个角色，正常判断
        const orBranch = role.indexOf('|') > -1;
        const evenBranch = role.indexOf('&') > -1;
        if (orBranch) {
          return role.split('|').some((r) => roleArr.includes(r.trim()));
        }
        if (evenBranch) {
          return role.split('&').every((r) => roleArr.includes(r.trim()));
        }
        return roleArr.includes(role);
      }, // 是否拥有该角色
      hasRoute: (route: { title: any }) => {
        return accessArr.includes(route.title);
      }, // 是否拥有该菜单
      hasAccess: (permission: string) => accessArr.includes(permission), // 是否拥有该权限
      isRole: (role: string) => {
        // 只是某一个角色
        return roleArr.includes(role) && roleArr.length === 1;
      },
      // hasDevAccess: () => !isExternalNetwork() && devAccess.includes(passportName), // 是否拥有开发配置权限
      hasDevAccess: () => devAccessWhiteList?.includes(passportName), // 是否拥有开发配置权限
      currentUser,
      userId,
      userName,
    };
  }
  return {
    hasRole: () => false,
    hasRoute: () => false,
    hasAccess: () => false,
    hasDevAccess: () => false,
    currentUser,
  };
}
