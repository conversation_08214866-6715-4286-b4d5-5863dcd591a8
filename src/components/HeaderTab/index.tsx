/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-05-27 11:18:16
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-04-03 16:56:06
 * @FilePath: /code/lala-finance-biz-web/src/components/HeaderTab/index.tsx
 * @Description: HeaderTab
 */
import { CloseOutlined } from '@ant-design/icons';
import { useLocation, useModel, useNavigate } from '@umijs/max';
import { Tag } from 'antd';
import React, { useImperativeHandle } from 'react';

import { deletePath, pathList } from './getTabsList';
import type { PathItem } from './type';

import './index.less';

const classPrefix = `header-tab`;
let classSuffix = 'container';

type TabItemProps = {
  onDelete?: (path: string, query: string) => void;
  onRef?: React.Ref<unknown> | undefined;
};

const HeaderTabs: React.FC<TabItemProps> = (props) => {
  // console.log('render HeaderTabs pathList', pathList, pathList.length);
  const nav = useNavigate();
  const location = useLocation();
  const { initialState } = useModel('@@initialState');
  const { navTheme } = initialState?.settings || {};
  if (navTheme === 'realDark') {
    classSuffix = 'container dark';
  }

  const currentPath = location.pathname;
  const currentQuery = location.search;
  const currentState = location.state;
  // tab切换
  const handleChange = (path: string, query: string, states?: any) => {
    if (query) {
      nav(path + query, {
        state: states || {},
      });
    } else {
      nav(path, {
        state: states || {},
      });
    }
  };

  // 删除tab选项
  const handleDelete = (
    targetKey: string,
    targetQuery: string,
    diffQuery: boolean,
    linkFun?: () => void,
  ) => {
    deletePath(targetKey, targetQuery);
    // 回调父组件
    if (props.onDelete) {
      props.onDelete(currentPath, currentQuery);
    }
    const { path, query, state } = pathList[pathList.length - 1];

    // 若有固定跳转链接，则不走下面的逻辑
    if (linkFun) {
      linkFun?.();
      return;
    }

    if (currentPath === targetKey) {
      if (diffQuery && query === targetQuery) {
        nav(path + query, {
          state: state,
        });
      } else {
        nav(path + query, {
          state: state,
        });
      }
    } else {
      nav(path + query, {
        state: state,
      });
    }
  };

  const isSameTab = (tab: PathItem) => {
    if (tab.diffQuery) {
      return tab.path == currentPath && tab.query === currentQuery;
    }
    return tab.path == currentPath;
  };

  useImperativeHandle(props.onRef, () => ({
    // 暴露给父组件的方法
    handleDelete,
  }));

  return (
    <div className={`${classPrefix}-${classSuffix}`}>
      {pathList.map((pane) => (
        <Tag
          key={pane.path + pane.query}
          color={isSameTab(pane) ? 'processing' : 'default'}
          closable={pane.path !== '/dashboard'}
          closeIcon={<CloseOutlined style={{ color: '#8B8682' }} />}
          onClose={() => {
            handleDelete(pane.path, pane.query as string, pane.diffQuery as boolean);
          }}
          onClick={() => {
            // fromHeaderTabs: 表示是从tabs点击过来的
            handleChange(pane.path, pane.query as string, {
              ...pane.state,
              fromHeaderTabs: true,
            });
          }}
          className={`${classPrefix}-item`}
        >
          {pane.name}
        </Tag>
      ))}
    </div>
  );
};

export default HeaderTabs;
