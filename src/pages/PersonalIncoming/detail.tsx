/*
 * @Author: your name
 * @Date: 2021-03-20 15:48:36
 * @LastEditTime: 2024-08-21 16:22:12
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/PersonalIncoming/detail.tsx
 */
import errorImg from '@/assets/error-img.png';
import { DividerTit } from '@/components';
import HeaderTab from '@/components/HeaderTab';
import ShowInfo from '@/components/ShowInfo';
import StepProgress from '@/components/StepProgress';
import globalStyle from '@/global.less';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useAccess, useRequest } from '@umijs/max';
import { Card, Col, Image, message, Modal, Row, Tabs } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import './index.less';
import { aiBankDetail, getIntoDetail, getOrderDetail, incomeDetail, revokeOrder } from './service';
// import { getOssPath } from '@/services/global';
import { SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { ExclamationCircleOutlined, RollbackOutlined } from '@ant-design/icons';
import ProTable from '@ant-design/pro-table';
import BigNumber from 'bignumber.js';
import {
  cashOrderStatusMap,
  CUSTOMER_TYPE_MAP,
  merchantMap,
  privateOrderStatusMap,
} from '../BusinessCashMng/const';

import { exPrivateStatusMap, merchantStatusMap, privateStatusMap } from './const';

//将数组排序,如果是实名信息需要排序
export const sortArr = (arr: [], key: string) => {
  if (['realNameInfo', 'businessLicenseInfo'].includes(key)) {
    const complarItem = ['NetWorkPath'];
    arr?.sort((a: { fieldCode: string }, b: { fieldCode: string }) => {
      return a.fieldCode.includes(complarItem) - b.fieldCode.includes(complarItem);
    });
  }
  return arr;
};

//获取各种格式
export const getFuncDom = (
  key: string,
  item: {
    fieldValue: string;
    fieldDesc: string;
    fieldCode: string;
  },
  index: number,
) => {
  let vDom = <></>;
  //表格格式
  const tableTemplate = (
    <>
      {index === 0 ? (
        <>
          <Col className="border-col header" span={6}>
            项
          </Col>
          <Col className="border-col header" span={6}>
            值
          </Col>
          <Col span={12} />
        </>
      ) : null}
      <>
        <Col className="border-col" span={6}>
          {item.fieldDesc}
        </Col>
        <Col className="border-col" span={6}>
          {item.fieldValue || '-'}
        </Col>
        <Col span={12} />
      </>
    </>
  );
  // 图片格式
  const imageTemplate = (
    <>
      <div className="col_item">
        <Image
          className="personal-img"
          width="100%"
          height={150}
          src={item.fieldValue || errorImg}
        />
        <span style={{ fontWeight: '500' }}>{item.fieldDesc}</span>
      </div>
    </>
  );
  switch (key) {
    //表格
    case 'baseInfo':
    case 'workInfo':
    case 'relativeInfo':
    case 'bankInfo':
      vDom = tableTemplate;
      break;
    //图片
    case 'imagingInfo':
      // case 'supplementInfo':
      vDom = imageTemplate;
      break;
    //图片+表格
    case 'realNameInfo':
    case 'supplementsInfo':
    case 'businessLicenseInfo':
      if (['frontFilePath', 'unFrontFilePath'].includes(item.fieldCode)) {
        return;
      }
      vDom = <>{item.fieldCode.includes('NetWorkPath') ? imageTemplate : tableTemplate}</>;
      break;
    default:
      vDom = <></>;
      break;
  }
  return vDom;
};

const PersonalIncomingDetail: React.FC<{}> = () => {
  const { orderNo } = history.location.query as { orderNo: string };
  const [listIsShow, setListIsShow] = useState(true);
  const [revokeVisible, setRevokeVisible] = useState(false);
  const access = useAccess();

  // 获取进件详情
  const { data: baseData, loading: baseLoading, run: initBaseData } = useRequest(() => {
    return getIntoDetail(orderNo);
  });

  //根据二级产品分类类型
  const getBaseInfoFunc = (history030101Income: boolean, productSecondTypeCode: string) => {
    //旧小易速贷和融租数据走老得接口，新的走圆易借
    return history030101Income ||
      productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND
      ? incomeDetail({ orderNo, access })
      : aiBankDetail({ orderNo });
  };

  const { data: baseInfo, run } = useRequest(getBaseInfoFunc, { manual: true });

  useEffect(() => {
    run(baseData?.history030101Income, baseData?.productSecondTypeCode);
  }, [baseData]);
  const [faceImgUrl, setFaceImgUrl] = useState('');

  const getFileUrl = (url: string) => {
    // getOssPath(url).then((res) => {
    setFaceImgUrl(url);
    // });
  };
  const FACE_ID = '0503';

  useMemo(() => {
    if (baseInfo && baseInfo.imagingInfo) {
      const faceItem = baseInfo.imagingInfo.find((item) => item.fieldCode === FACE_ID);
      if (faceItem) {
        getFileUrl(faceItem.fieldValue);
      }
    }
  }, [baseInfo]);

  const INCOME_BASE_INFO = {
    baseInfo: '个人信息',
    workInfo: '工作信息',
    relativeInfo: '联系信息',
    bankInfo: '银行信息',
    imagingInfo: '影像资料',
    realNameInfo: '实名信息',
    supplementsInfo: '补件资料',
    businessLicenseInfo: '营业执照信息',
  };

  // const INCOME_BASE_INFO_ARR = [
  //   'baseInfo',
  //   'workInfo',
  //   'relativeInfo',
  //   'bankInfo',
  //   'imagingInfo',
  //   'supplementsInfo',
  //   'realNameInfo',
  // ];

  // 获取进件详情---风控进件记录
  // const { data: recordData, loading: recordLoading } = useRequest(() => {
  //   return getInputDetailRecord(orderNo);
  // });
  //基本信息
  const basicMap = {
    orderNo: '进件流水号',
    userId: '用户ID',
    registerPhone: '注册手机号',
    registerTime: '注册时间',
    // riskStartTime: '风控进件时间',
    status: '进件状态',
    customerType: '用户类型',
  };
  //产品信息
  const productMap =
    baseData?.firstTypeName === '融资租赁'
      ? {
          channel: '渠道商户',
          firstTypeName: '产品一级分类',
          secondTypeName: '产品二级分类',
          productName: '产品名称',
        }
      : {
          channel: '渠道商户',
          firstTypeName: '产品一级分类',
          secondTypeName: '产品二级分类',
        };
  //申请信息
  const applyMap = {
    applyAmount: '申请额度',
    term: '申请期限',
    repayType: '还款方式',
    interestRate: '申请利率',
    usageOfLoan: '借款用途',
  };
  //实名信息
  const realNameMap = {
    userName: '用户名称',
    idNo: '证件号码',
    mobile: '实名手机号',
  };

  const incomeMap = {
    status: '风控状态',
    rejectReason: '拒绝原因',
    riskStartTime: '风控进件时间',
    creditEndTime: '风控审核时间',
    creditAmount: '授信额度',
    creditInterestRate: '授信利率',
    remark: '备注',
  };
  const incomeMapNoRejectReason = {
    status: '风控状态',
    // rejectReason: '拒绝原因',
    riskStartTime: '风控进件时间',
    creditEndTime: '风控审核时间',
    creditAmount: '授信额度',
    creditInterestRate: '授信利率',
    remark: '备注',
  };
  //风控状态为审核拒绝，才展示原因
  const incomeMapTerminal =
    baseData?.status === 35 || baseData?.status === 40 ? incomeMap : incomeMapNoRejectReason;
  const selfDefineRiskMap = {
    interestRate: (
      <>{baseData?.interestRate ? `${new BigNumber(baseData?.interestRate).times(100)}%` : '-'}</>
    ),
    creditInterestRate: (
      <>
        {baseData?.creditInterestRate
          ? `${new BigNumber(baseData?.creditInterestRate).times(100)}%`
          : '-'}
      </>
    ),
    creditAmount: (
      <>
        {baseData?.creditAmount}
        {baseData?.cycleQuota ? '(循环额度)' : '(一次性额度)'}
      </>
    ),
  };

  // useEffect(async () => {
  //   const res = await getOrderDetail({ pageSize: 20, current: 1, userId: baseData?.userId });
  // }, [baseData?.userId]);
  const getStatusMap = () => {
    if (
      baseData?.history030101Income ||
      baseData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND
    ) {
      //历史小易速贷 或者是融租
      return exPrivateStatusMap;
    } else if (baseData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.MERCHANT) {
      return merchantStatusMap;
    } else {
      return privateStatusMap;
    }
  };
  const riskMap = {
    status: getStatusMap(), //小易私房钱状态不同
    repayType: {
      1: '一次本息',
      2: '等额本息',
    },
    customerType: CUSTOMER_TYPE_MAP,
  };
  //产品类型
  enum PRODUCT_TYPE {
    SELF = 1, //自营
    UN_SELF = 2, //非自营
  }

  const getOrderStatusMap = () => {
    //如果是融租或者是小易速贷旧流程
    if (
      baseData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND ||
      baseData?.history030101Income
    ) {
      return cashOrderStatusMap;
    } else if (baseData?.productSecondTypeCode === SECONDARY_CLASSIFICATION_CODE.MERCHANT) {
      return merchantMap;
    } else {
      return privateOrderStatusMap; //小易私房钱
    }
  };

  const columns = useMemo(() => {
    return [
      {
        title: '订单号',
        dataIndex: 'orderNo',
        key: 'orderNo',
        render: (orderNoParam: string) => {
          return (
            <a
              onClick={() => {
                //跳转到小贷订单详情
                history.push(`/businessMng/cash-detail?orderNo=${orderNoParam}`);
              }}
            >
              {orderNoParam}
            </a>
          );
        },
      },
      {
        title: '进件流水号',
        dataIndex: 'orderReceiptNo',
        key: 'orderReceiptNo',
      },
      {
        title: '用户ID',
        dataIndex: 'userId',
        key: 'userId',
      },
      {
        title: '用户名称',
        dataIndex: 'userName',
        key: 'userName',
      },
      {
        title: '手机号',
        dataIndex: 'mobile',
        key: 'mobile',
      },
      {
        title: '用户类型',
        dataIndex: 'customerType',
        valueEnum: riskMap.customerType,
      },
      {
        title: '资金渠道',
        dataIndex: 'channelName',
      },
      {
        title: '产品名称',
        dataIndex: 'productName',
        key: 'productName',
      },
      {
        title: '借款金额',
        dataIndex: 'loanAmount',
        key: 'loanAmount',
      },
      {
        title: '借款期限',
        dataIndex: 'loanTerm',
        key: 'loanTerm',
      },

      {
        title: '借款利率',
        dataIndex: 'loanInterestRate',
        key: 'loanInterestRate',
      },
      {
        title: '借款时间',
        dataIndex: 'applyTime',
        key: 'applyTime',
      },
      {
        title: '放款时间',
        dataIndex: 'lendingTime',
        key: 'lendingTime',
      },
      {
        title: '订单状态',
        dataIndex: 'status',
        valueEnum: getOrderStatusMap(),
      },
      {
        title: '操作',
        type: 'option',
        render: (record: any) => {
          return (
            <a
              onClick={() => {
                //跳转到小贷订单详情
                history.push(
                  `/businessMng/cash-detail?orderNo=${record?.orderNo}&incomeNo=${orderNo}`,
                );
              }}
            >
              查看详情
            </a>
          );
        },
      },
    ];
  }, [baseData, getOrderStatusMap]);

  // 订单明细
  const cardOrder = useMemo(() => {
    // const res: any = await getOrderDetail({ current: 1, pageSize: 20, userId: baseData?.userId });
    // const res = { total: 1 };
    return (
      <>
        {listIsShow && baseData?.userId && baseData?.orderNo ? (
          <Card title="订单明细" style={{ marginTop: 20 }}>
            <ProTable
              columns={columns}
              toolBarRender={false}
              scroll={{ x: 'max-content' }}
              search={false}
              rowKey="orderNo"
              // dataSource={[]}
              request={(params) => {
                return getOrderDetail({
                  userId: baseData?.userId,
                  activateOrderNo: baseData?.orderNo,
                  ...params,
                })
                  .then((res) => {
                    console.log(res);
                    //没数据隐藏
                    setListIsShow(res.total);
                    return res;
                  })
                  .catch(() => {
                    // console.log('0000error');
                    //报错隐藏，
                    setListIsShow(false);
                  });
              }}
            />
          </Card>
        ) : (
          ''
        )}
      </>
    );
  }, [baseData, columns, listIsShow]);

  //获取是否显示
  // useEffect(() => {
  //   const featchIsShowOrder = async () => {
  //     if (baseData?.userId && baseData?.orderNo) {
  //       const res = await getOrderDetail({
  //         current: 1,
  //         pageSize: 20,
  //         userId: baseData?.userId,
  //         activateOrderNo: baseData?.orderNo,
  //       });
  //       setListIsShow(res.total);
  //     }
  //   };
  //   featchIsShowOrder();
  // }, [baseData]);

  // console.log(cardOrder);
  const { TabPane } = Tabs;

  const handleRevokeOpt = () => {
    return revokeOrder(orderNo)
      .then(() => {
        message.success(`撤销成功`);
        setRevokeVisible(false);
        initBaseData();
      })
      .catch((err) => {
        setRevokeVisible(false);
        Modal.error({
          title: err.message,
          width: 400,
          centered: true,
          okText: '好的',
        });
      });
  };

  return (
    <>
      <HeaderTab />
      <PageContainer
        extra={[
          baseData?.canRevoke && (
            <a
              onClick={() => {
                setRevokeVisible(true);
              }}
            >
              <RollbackOutlined />
              <span>撤销进件单</span>
            </a>
          ),
        ]}
      >
        {baseData?.statusLog && (
          <StepProgress
            stepStatus={
              baseData?.statusLog?.map(
                (item: { status: number; statusDesc: string; time: string }) => {
                  return {
                    bol: true,
                    desc: item.statusDesc,
                    localDate: item.time,
                  };
                },
              ) || []
            }
            index={1}
          />
        )}
        <Card title="基础信息" style={{ marginTop: 20 }}>
          <DividerTit title="基本信息" style={{ marginTop: 10 }} />
          <ShowInfo
            noCard
            infoMap={basicMap}
            data={baseData}
            loading={baseLoading}
            itemMap={riskMap}
          />
          <DividerTit title="产品信息" style={{ marginTop: 10 }} />
          <ShowInfo noCard infoMap={productMap} data={baseData} loading={baseLoading} />
          {/* 非自营和小易速贷不展示申请信息 */}
          {baseData?.selfType === PRODUCT_TYPE.SELF && baseData?.productCode !== '030101' ? (
            <>
              <DividerTit title="申请信息" style={{ marginTop: 10 }} />
              <ShowInfo
                noCard
                infoMap={applyMap}
                data={baseData}
                loading={baseLoading}
                itemMap={riskMap}
                selfDefine={selfDefineRiskMap}
              />
            </>
          ) : (
            ''
          )}
          <DividerTit title="实名信息" style={{ marginTop: 10 }} />
          <ShowInfo noCard infoMap={realNameMap} data={baseData} loading={baseLoading} />
        </Card>
        <Card title="进件信息" style={{ marginTop: 20 }}>
          <Tabs>
            {Object.keys(INCOME_BASE_INFO).map((key) => {
              return baseInfo?.[key]?.length ? (
                <TabPane key={key} tab={INCOME_BASE_INFO[key]}>
                  {baseInfo?.[key]?.length ? (
                    <Row className="personal-list">
                      {
                        <>
                          {/* {sortArr(baseInfo?.[key], key)} */}
                          {sortArr(baseInfo?.[key], key)?.map(
                            (
                              item: {
                                fieldValue: string;
                                fieldDesc: string;
                                fieldCode: string;
                              },
                              index: number,
                            ) => {
                              return getFuncDom(key, item, index);
                            },
                          )}
                        </>
                      }
                    </Row>
                  ) : (
                    '暂无数据'
                  )}
                </TabPane>
              ) : (
                ''
              );
            })}
          </Tabs>
        </Card>
        <Card title="风控信息" style={{ marginTop: 20 }}>
          <DividerTit title="基本信息" style={{ marginTop: 0 }} />
          <ShowInfo
            noCard
            infoMap={incomeMapTerminal}
            selfDefine={selfDefineRiskMap}
            data={baseData}
            itemMap={riskMap}
          />
        </Card>
        {/* 订单详情 */}
        {cardOrder}
        <Modal
          title="撤销"
          open={revokeVisible}
          onOk={() => handleRevokeOpt()}
          centered
          width="400px"
          destroyOnClose
          okText="确认撤销"
          onCancel={() => setRevokeVisible(false)}
        >
          <div className={globalStyle.textCenter}>
            <ExclamationCircleOutlined className={globalStyle.iconCss} />
            <span className={`${globalStyle.fontWBold} ${globalStyle.fontS16}`}>
              是否确认撤销该笔进件单?
            </span>
            <p className={globalStyle.mt10}>撤销后，进件单将无法继续进行，请谨慎操作！</p>
          </div>
        </Modal>
      </PageContainer>
    </>
  );
};

export default PersonalIncomingDetail;
