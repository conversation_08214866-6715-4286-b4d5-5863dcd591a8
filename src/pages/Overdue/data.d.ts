/*
 * @Author: your name
 * @Date: 2021-01-12 17:01:50
 * @LastEditTime: 2024-09-26 15:18:10
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/data.d.ts
 */

export interface OverdueItem {
  actualSettleDate: string;
  repaymentDate: string; // 还款日期
  accountName: string; // 用户名称
  accountNumber: number; // 用户id
  contactPerson: number; // 联系人
  contactPhone: string; // 联系人电话
  createdAt: string; // 创建时间
  daysOverdue: number; // 逾期天数
  billNo: string; // 订单号
  overdueAmount: number; // 逾期金额
  overdueId: string; // 催收单号
  overdueInterest: number; // 逾期利息
  overduePenaltyInterest: number; // 逾期罚息
  productName: string; // 产品名称
  returnedAmount: number; // 已还金额
  urgePerson: string; // 催收员
  urgeRecentlyMsg: string; // 最近催收记录
  urgeRecentlyTime: string; // 最近催收时间
  urgeState: number; // 催收状态
  productCode: string; // 产品编号
  orderNo: string; // 订单编号
  repayPlanNo: string; // 还款编号
  overdueCaseNo: string;
}

export interface OverdueParams {
  accountName?: string; // 用户名称
  accountNumber?: string; // 用户
  createdAtEnd?: string; // 创建时间结束时间
  createdAtStart?: string; // 创建时间开始时间
  orderDisplayId?: string; // 订单号
  overdueId?: string; // 催收单号
  productName?: string; // 产品名称
  urgeState?: number; // 催收状态
  current?: number; // 当前页
  pageSize?: number; // 页大小
  productCode?: string;
  beAssigned: boolean;
  urgeStateList?: string; //催收状态
  classification?: string;
}

/**
 * @Date: 2021-01-14 14:25:02
 * @Author: elisa.zhao
 * 添加催收记录
 */
export interface CallPayForm {}

export interface CallPayParams {
  endApplyTime?: string; // 创建结束时间
  enterpriseName?: string; // 企业名称
  idNo?: string; // 统一社会信用代码
  startApplyTime?: string; // 创建开始时间
  status?: number; // 状态
  userNo?: number; // 用户编号
  current?: number; // 当前页
  pageSize?: number; // 页大小
}
// 催收回款记录--已复制
export interface CallPayMoneyListItem {
  amount: number;
  createdAt: string;
  fileList?: any[];
  payBackType?: string;
  urgeRecentlyMsg?: string;
  urgePerson: string;
}

// 减免记录--已复制
export interface CollectReliefListItem {
  createdAt: string;
  fileList?: any[];
  status: number;
  urgeRecentlyMsg: string;
  amount: number;
  urgePerson: string;
}

// 派单--已复制
export interface DispatchParams {
  operator: string;
  overdueIdList: any[];
  overdueCaseNoList: any[];
  type: number | string;
  urgeUserId: number | string;
  urgeUserName: number | string;
}

// 提交催收回款--已复制
export interface SubmitCallBackParams {
  amount: string;
  fileList: any[];
  overdueId: string;
  payBackType: string;
  paymentTime: string;
  urgeRecentlyMsg: string;
  repayBankNo?: string;
  thirdFlowId?: string;
  repayChannel?: string;
}
// 添加催收记录
export interface AddCallPayParams {
  fileList: { fileUrl: string; fileName: string }[];
  overdueId: string;
  urgePerson?: string;
  urgeRecentlyMsg?: string;
  urgeRecentlyTime?: string;
  urgeType?: string;
}

// 申请减免--已复制
export interface ApplyReliefParams {
  amount: number;
  fileList: any[];
  overdueId: string;
  urgeRecentlyMsg: string;
}

export interface WithHoldListItem {
  reason: string;
  status: number;
  withholdMoney: number;
  withholdTime: string;
}

export interface WithHoldParams {
  overdueId: string;
  withholdMoney: number;
}

export interface OverdueListItem {
  totalRepayAmount: number;
  totalPrincipal: number;
  totalInterest: number;
  totalDelayInterest: number;
  totalPenalty: number;
}

// 催收拨打详情 & 插入--copy
export interface ICallDetail {
  actionResults?: number; // 行动结果
  actionSign?: number; // 行动标识
  callRecord: string; // 通话记录 ID
  dialNumber: string; // 拨打号码
  dialTime: string; // 拨打时间
  overdueId: string; // 催收单号
  urgePersonId?: string; // 催收员
  urgePersonName: string; // 催收员姓名
  dialBeginTime?: string; // 拨打开始时间
  dialEndTime?: string; // 拨打结束时间
  remarks?: string; // 备注
  status?: string; // 通话状态
  talkTimeSecond?: string; // 通话时长
  turnOnTime?: string; // 接通时间
  callEndTime?: string; // 通话结束时间
  turnOnTime?: string; // 接通时间
  callDateDetail?: any[]; // 拨打时间
}

// 催收记录 查询参数 & 列表
export interface ICallAction extends ICallDetail {
  id?: string; // 主键 id
  current?: number; // 当前页码
  pageSize?: number; // 每页条数
}

// 行动标识枚举 --copy
export interface IActionEnum {
  value: number;
  label: string;
  actionResultList: [];
}

export type IclassificationTabKey = 'SMALL_LOAN' | 'FINANCE_LEASE' | 'SELLER_FACTORING';
