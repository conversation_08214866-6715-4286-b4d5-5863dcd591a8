/*
 * @Author: your name
 * @Date: 2021-06-01 18:10:55
 * @LastEditTime: 2024-06-13 15:16:29
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessLeaseMng/components/LoanInfo.tsx
 */
import { ShowInfo } from '@/components';
import { history } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

enum LOAN_TYPE {
  INCOME = 1, // 进件
  THIRD_LOAN = 9, //资方放款
}
const LoanLeaseInfo: React.FC<any> = (props) => {
  const { loanData, productCode, statusLogs } = props;
  const [showLoanMap, setShowLoanMap] = useState({});

  // 放款信息配置
  const loanInfoMap = {
    lendingNo: '放款流水号',
    type: '放款类型',
    amount: '放款金额',
    lendingMaster: '放款主体',
    receiptMaster: '收款主体',
    fundFlow: '是否发生资金流',
    lendingModel: '放款方式',
    lendingCycle: '放款周期',
    lendingTime: '放款时间',
    status: '放款状态',
    rejectionReason: '驳回原因',
    rejectionTime: '驳回时间',
  };

  const shBankInfo = {
    approvalFailReason: '审批失败原因',
    failReasonDesc: '失败原因描述',
  };

  useEffect(() => {
    if (loanData) {
      if (loanData.type === LOAN_TYPE.THIRD_LOAN) {
        setShowLoanMap({ ...loanInfoMap, ...shBankInfo });
      } else {
        setShowLoanMap({ ...loanInfoMap });
      }
    }
  }, [loanData]);

  const itemLoanMap = {
    type: {
      1: '进件',
      2: '债转',
      3: '代偿',
      9: '资方放款',
    },
    fundFlow: {
      false: '否',
      true: '是',
    },
    status: {
      0: '放款失败',
      1: '待放款',
      2: '放款成功',
      '-1': '已取消',
      3: '待请款',
      10: '待一审',
      11: '待二审',
      12: '驳回',
    },
    lendingModel: {
      1: '线上',
      2: '线下',
    },
  };

  // 放款流水号待请款和驳回状态可跳转, loanData?.status === 3 || 12
  const selfDefine = {
    lendingNo:
      loanData?.status === 3 || loanData?.status === 12 ? (
        <a
          onClick={() => {
            // 订单状态=待放款（子状态：待请款），超过自然日31天，点击放款流水号跳转失败
            const isValidDays = statusLogs?.some((item: any) => {
              return (
                item?.status === 60 &&
                loanData?.status === 3 &&
                dayjs().diff(dayjs(item?.auditTime), 'day') > 31
              );
            });
            if (isValidDays) return message.warning('超时未请款订单已失效，请重新进件', 5);
            history.push(
              `/businessMng/loan-lease-detail?lendingNo=${loanData?.lendingNo}&orderNo=${loanData?.orderNo}&productCode=${productCode}`,
            );
          }}
        >
          {loanData?.lendingNo}
        </a>
      ) : (
        <span style={{ maxWidth: 270 }}>{loanData?.lendingNo}</span>
      ),
  };

  return (
    <>
      <ShowInfo
        title="放款信息"
        infoMap={showLoanMap}
        itemMap={itemLoanMap}
        data={loanData}
        selfDefine={selfDefine}
      />
    </>
  );
};

export default LoanLeaseInfo;
