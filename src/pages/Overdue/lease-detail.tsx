/*
 * @Author: your name
 * @Date: 2021-06-30 14:47:07
 * @LastEditTime: 2022-07-26 16:30:51
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/Overdue/lease-detail.ts
 */
import HeaderTab from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useRequest } from '@umijs/max';
import { Card, Tabs } from 'antd';
import React from 'react';
import Contract from '../BusinessMng/components/Contract';
import {
  BasicInfo,
  CallPayList,
  CallPayMoneyList,
  CollectReliefList,
  OverdueDetail,
  RepaymentRecord,
  WithHold,
} from './components';
import { getOverdueListDetail } from './service';

const { TabPane } = Tabs;

const OverdueLeaseDetail: React.FC<any> = () => {
  const { overdueId, businessType, productCode } = history.location.query;
  const { data, run } = useRequest(() => {
    return getOverdueListDetail(overdueId);
  });

  const refresh = () => {
    run();
  };

  return (
    <>
      <HeaderTab />
      <PageContainer>
        <BasicInfo
          basicInfo={data?.overdueManagementBaseInfoBO}
          contactPhoneBOList={data?.contactPhoneBOList}
          urgeUserChangeDTOList={data?.urgeUserChangeDTOList}
          addContactBack={() => run()}
        />
        <OverdueDetail dataList={data?.repayOverdueCostBO} />
        <Card title="催收信息" className={globalStyle.mt20}>
          <Tabs defaultActiveKey="1">
            <TabPane tab="催收记录" key="1">
              <CallPayList
                callPayList={data?.repaySmsUrgeCollectionBO}
                isSettle={data?.overdueManagementBaseInfoBO.urgeState}
                deadLine={data?.overdueManagementBaseInfoBO.repaymentDate}
                businessType={businessType}
                productCode={productCode}
                refresh={refresh}
                hideCard
              />
            </TabPane>
            <TabPane tab="减免记录" key="2">
              <CollectReliefList
                collectReliefList={data?.repayRemissionRecordBO}
                isSettle={data?.overdueManagementBaseInfoBO.urgeState}
                overDueAmount={data?.overdueManagementBaseInfoBO.overdueAmount}
                refresh={refresh}
                hideCard
              />
            </TabPane>
            <TabPane tab="催收还款记录" key="3">
              <CallPayMoneyList
                callPayMoneyList={data?.repayPaymentRecordBO}
                deadLine={data?.overdueManagementBaseInfoBO.repaymentDate}
                isSettle={data?.overdueManagementBaseInfoBO.urgeState}
                overDueAmount={data?.overdueManagementBaseInfoBO.overdueAmount}
                refresh={refresh}
                hideCard
              />
            </TabPane>
          </Tabs>
        </Card>

        <Contract dataList={data?.contractFileDTOList} />

        <Card title="还款记录" className={globalStyle.mt20}>
          <Tabs defaultActiveKey="1">
            <TabPane tab="代扣记录" key="1">
              <WithHold
                callPayMoneyList={data?.repayPaymentRecordBO}
                deadLine={data?.overdueManagementBaseInfoBO.repaymentDate}
                isSettle={data?.overdueManagementBaseInfoBO.urgeState}
                overDueAmount={data?.overdueManagementBaseInfoBO.overdueAmount}
                refresh={refresh}
                hideCard
              />
            </TabPane>
            <TabPane tab="历史还款记录" key="2">
              <RepaymentRecord hideCard overdueId={overdueId} />
            </TabPane>
          </Tabs>
        </Card>
      </PageContainer>
    </>
  );
};

export default OverdueLeaseDetail;
