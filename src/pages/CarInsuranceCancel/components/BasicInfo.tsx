import { Col, Divider, Row } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import { cancelReasonEnumMap, CancelStatusEnum, cancelStatusEnumMap, DetailInfo } from '../const';

interface ModalProps {
  detailInfo: DetailInfo;
  orderStatus: CancelStatusEnum;
}

const Page: React.FC<any> = ({ detailInfo, orderStatus }) => {
  //
  return (
    <>
      {/* 分割线 */}
      <div className="row-divider-wrap">
        <Divider className="row-divider" />
      </div>
      {/*  */}
      <Row gutter={[10, 16]}>
        {/* 状态 */}
        <Col span={24}>
          <div className="detail-label">状态: </div>
          {cancelStatusEnumMap[detailInfo?.orderStatus as CancelStatusEnum] || '-'}
          {/* <span className="red-text"> orderStatus: {detailInfo?.orderStatus}</span> */}
        </Col>
      </Row>
      {/* 分割线 */}
      <div className="row-divider-wrap">
        <Divider className="row-divider" />
      </div>
      {/* 9个字段 */}
      <Row gutter={[10, 16]}>
        {/* 展示: 车牌号、车架号、退保原因、放款日期、是否结清、当前期数、结清金额、预估退保金额、预估退保日期*/}
        <Col span={8}>
          <div className="detail-label">车牌号: </div>
          {detailInfo.plateNo || '-'}
        </Col>
        <Col span={8}>
          <div className="detail-label">车架号: </div>
          {detailInfo.vin || '-'}
        </Col>
        <Col span={8}>
          <div className="detail-label">退保原因: </div>
          {cancelReasonEnumMap[detailInfo?.cancellationReasonStatus as CancelReasonEnum] || '-'}
        </Col>
        <Col span={8}>
          <div className="detail-label">放款日期: </div>
          {/* {detailInfo.lendingTime || '-'} */}
          {detailInfo?.lendingTime ? dayjs(detailInfo?.lendingTime)?.format('YYYY-MM-DD') : '-'}
        </Col>
        <Col span={8}>
          <div className="detail-label">是否结清: </div>
          {{
            true: '是',
            false: '否',
          }[detailInfo?.settle] || '-'}
        </Col>
        <Col span={8}>
          <div className="detail-label">结清发起期数: </div>
          {detailInfo?.settle
            ? `${detailInfo?.currentSettleTerm || '-'}/${detailInfo?.loanTerm || '-'}`
            : '-'}
        </Col>
        <Col span={8}>
          <div className="detail-label">结清金额: </div>
          {detailInfo?.actualSettleAmount ? `¥${detailInfo?.actualSettleAmount}` : '-'}
        </Col>
        {![CancelStatusEnum?.WAIT_REFUND, CancelStatusEnum?.REFUND_REJECT].includes(
          detailInfo?.orderStatus,
        ) && (
          <>
            {/* 1.1 */}
            {detailInfo?.orderStatus === CancelStatusEnum?.WAIT_PROCESS && (
              <Col span={8}>
                <div className="detail-label">预估退保金额: </div>
                {detailInfo?.expectedCancellationAmount
                  ? `¥${detailInfo?.expectedCancellationAmount}`
                  : '-'}
              </Col>
            )}
            {/* 1.2 */}
            {detailInfo?.orderStatus === CancelStatusEnum?.WAIT_CONFIRM && (
              <Col span={8}>
                <div className="detail-label">批单退保金额: </div>
                {detailInfo?.actualCancellationAmount
                  ? `¥${detailInfo?.actualCancellationAmount}`
                  : '-'}
              </Col>
            )}
            {/* 1.3 */}
            {detailInfo?.orderStatus !== CancelStatusEnum?.WAIT_PROCESS &&
              detailInfo?.orderStatus !== CancelStatusEnum?.WAIT_CONFIRM && (
                <Col span={8}>
                  <div className="detail-label">实际退保金额: </div>
                  {detailInfo?.actualPaymentAmount ? `¥${detailInfo.actualPaymentAmount}` : '-'}
                </Col>
              )}
          </>
        )}
        {![CancelStatusEnum?.WAIT_REFUND, CancelStatusEnum?.REFUND_REJECT].includes(
          detailInfo?.orderStatus,
        ) && (
          <>
            {/* 2.1 */}
            {detailInfo?.orderStatus === CancelStatusEnum?.WAIT_PROCESS && (
              <Col span={8}>
                <div className="detail-label">预期退保日期: </div>
                {detailInfo?.expectedCancellationTime
                  ? dayjs(detailInfo?.expectedCancellationTime).format('YYYY-MM-DD')
                  : '-'}
              </Col>
            )}
            {/* 2.2 */}
            {(detailInfo?.orderStatus === CancelStatusEnum?.WAIT_CONFIRM ||
              detailInfo?.orderStatus === CancelStatusEnum?.CANCEL) && (
              <Col span={8}>
                <div className="detail-label">预计到账日期: </div>
                {detailInfo?.expectedPaymentTime
                  ? dayjs(detailInfo?.expectedPaymentTime).format('YYYY-MM-DD')
                  : '-'}
              </Col>
            )}
            {/* 2.3 */}
            {detailInfo?.orderStatus !== CancelStatusEnum?.WAIT_PROCESS &&
              detailInfo?.orderStatus !== CancelStatusEnum?.WAIT_CONFIRM &&
              detailInfo?.orderStatus !== CancelStatusEnum?.CANCEL && (
                <Col span={8}>
                  <div className="detail-label">实际到账日期: </div>
                  {detailInfo?.actualPaymentTime
                    ? dayjs(detailInfo?.actualPaymentTime)?.format('YYYY-MM-DD')
                    : '-'}
                </Col>
              )}
          </>
        )}
      </Row>
      {/* 分割线 */}
      <div className="row-divider-wrap">
        <Divider className="row-divider" />
      </div>
    </>
  );
};
export default Page;
