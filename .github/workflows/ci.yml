name: Node CI

on: [push, pull_request]

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        node_version: [10.x, 12.x]
        os: [ubuntu-latest, windows-latest, macOS-latest]
    steps:
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node_version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node_version }}
      - run: echo ${{github.ref}}
      - run: npm install
      - run: yarn run lint
      - run: yarn run tsc
      - run: yarn run build
      - run: yarn run test:all
        env:
          CI: true
          PROGRESS: none
          NODE_ENV: test
          NODE_OPTIONS: --max_old_space_size=4096
