.header-tab {
  &-container {
    position: fixed;
    top: 48px;
    right: 0;
    left: 213px;
    z-index: 10;
    padding-left: 5px;
    overflow-x: auto;
    line-height: 37px;
    white-space: nowrap;
    background: #fff;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);

    &.dark {
      position: absolute;
      top: 0;
      left: 0;
      background: #18181c;
    }
  }

  &-item:hover {
    cursor: pointer;
  }
}

.side-bar-collapse {
  .header-tab-container {
    left: 65px;
  }
}
