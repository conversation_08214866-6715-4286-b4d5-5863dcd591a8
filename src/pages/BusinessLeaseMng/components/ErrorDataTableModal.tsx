// 差错数据表格弹窗
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, message, Modal } from 'antd';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import type { ErrorDataListItem, ErrorDataParams } from '../data';
import { asyncExportErrorData, getErrorDataList } from '../service';

type ErrorDataTableProps = {
  open: boolean;
  onClose: () => void;
};

const CHANNEL_STATUS = new Map([
  [-1, '-'],
  [0, '待放款'],
  [1, '待还款'],
  [2, '提前结清'],
  [3, '结清'],
  [4, '逾期'],
  [5, '逾期结清'],
  [6, '坏账'],
  [7, '待完单'],
  [8, '单期代偿'],
  [9, '代偿结清'],
  [10, '退保结项'],
  [11, '回购结清'],
  [12, '结清中'],
  [13, '还款中'],
]);

const ErrorDataTable: React.FC<ErrorDataTableProps> = (props) => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const columns: ProColumns<ErrorDataListItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      width: 200,
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      width: 200,
      search: false,
    },
    {
      title: '放款时间',
      dataIndex: 'lendingTime',
      search: false,
    },
    {
      title: '还款入账时间',
      dataIndex: 'repayRecordingTime',
      search: false,
    },
    {
      title: '还款期数',
      dataIndex: 'term',
      search: false,
    },
    {
      title: '还款金额',
      dataIndex: 'repayAmount',
      search: false,
    },
    {
      title: '资方还款流水号',
      dataIndex: 'channelPlanNo',
      search: false,
    },
    {
      title: '期状态',
      dataIndex: 'status',
      valueEnum: CHANNEL_STATUS,
      search: false,
    },
    {
      title: '资方还款状态',
      dataIndex: 'channelStatus',
      valueEnum: CHANNEL_STATUS,
      search: false,
    },
    {
      title: '对比维度',
      dataIndex: 'contrast',
      valueEnum: {
        0: '按期维度',
        1: '订单维度',
      },
      search: false,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          startCreatedAt: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
          endCreatedAt: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
        }),
      },
      render: (dom, record) => {
        return record.createdAt;
      },
    },
  ];

  async function getSearchDataTotal() {
    const searchParams: ErrorDataParams = formRef?.current?.getFieldsFormatValue?.();
    const data = await getErrorDataList(searchParams);
    return data?.total;
  }

  return (
    <Modal title="差错数据" open={props.open} onCancel={props.onClose} width={1000} footer={null}>
      <ProTable<ErrorDataListItem>
        actionRef={actionRef}
        formRef={formRef}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        columns={columns}
        request={(params) => getErrorDataList(params)}
        search={{
          span: 10,
        }}
        toolBarRender={() => {
          return [
            <AsyncExport
              key="export"
              getSearchDataTotal={getSearchDataTotal}
              getSearchParams={() => {
                const params = formRef.current?.getFieldFormatValue?.();
                return params;
              }}
              trigger={
                <Button
                  type="primary"
                  onClick={(e) => {
                    const searchParams = formRef.current?.getFieldFormatValue?.();
                    const { startCreatedAt, endCreatedAt } = searchParams;
                    if (!startCreatedAt || !endCreatedAt) {
                      message.warning('请选择创建时间');
                      e.stopPropagation();
                    }
                  }}
                >
                  导出
                </Button>
              }
              exportAsync={asyncExportErrorData}
              taskCode={[ItaskCodeEnValueEnum.ERROR_DATA_LEASE]}
            />,
          ];
        }}
      />
    </Modal>
  );
};

export default ErrorDataTable;
