/*
 * @Date: 2023-09-04 16:44:19
 * @Author: elisa.<PERSON><PERSON>
 * @LastEditors: elisa.z<PERSON>
 * @LastEditTime: 2023-10-26 14:31:18
 * @FilePath: /lala-finance-biz-web/src/pages/FirstAudit/index.tsx
 * @Description:
 */

import HeaderTabs from '@/components/HeaderTab';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { Link } from '@umijs/max';
import { Button } from 'antd';
import type { FormInstance } from 'antd/es/form';
import dayjs from 'dayjs';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import { downLoadExcel } from '../../utils/utils';
import type { FirstAuditListItems } from './data';
import { exportList, getFirstAuditList } from './service';

const FirstAudit: React.FC = () => {
  const transformCompleteTime = (value: any) => {
    if (!value) return value;
    if (typeof value[0] !== 'string') {
      return {
        startCompleteTime: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
        endCompleteTime: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
      };
    }
    return {
      startCompleteTime: `${value[0].split(' ')[0]} 00:00:00`,
      endCompleteTime: `${value[1].split(' ')[0]} 23:59:59`,
    };
  };

  const transformGenerateTime = (value: any) => {
    if (!value) return value;
    if (typeof value[0] !== 'string') {
      return {
        startCreateTime: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
        endCreateTime: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
      };
    }
    return {
      startCreateTime: `${value[0].split(' ')[0]} 00:00:00`,
      endCreateTime: `${value[1].split(' ')[0]} 23:59:59`,
    };
  };

  const columns: ProColumns<FirstAuditListItems>[] = [
    {
      title: '报审编号',
      dataIndex: 'flowNo',
      key: 'flowNo',
      search: false,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      valueType: 'select',
      valueEnum: {
        1: '应收账款首次报审',
      },
      search: false,
    },
    {
      title: '企业名称',
      dataIndex: 'enterpriseName',
      key: 'enterpriseName',
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'orgCode',
      key: 'orgCode',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      valueType: 'select',
      search: false,
      valueEnum: {
        1: '创建成功',
        10: '风控中',
        19: '拒绝（风控前筛）',
        30: '待初审',
        31: '待终审',
        40: '审核通过',
        41: '审核拒绝',
      },
    },
    {
      title: '状态',
      dataIndex: 'statusInList',
      key: 'statusInList',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: {
        1: '创建成功',
        10: '风控中',
        19: '拒绝（风控前筛）',
        30: '待初审',
        31: '待终审',
        40: '审核通过',
        41: '审核拒绝',
      },
    },
    {
      title: '生成时间',
      dataIndex: 'generateTime',
      key: 'generateTime',
      initialValue: [dayjs().subtract(6, 'month'), dayjs()],
      search: {
        transform: transformGenerateTime,
      },
      render(_, record) {
        return record?.generateTime;
      },
      valueType: 'dateRange',
      // search: {
      //   transform(value) {
      //     return {
      //       startCreateTime: value[0],
      //       endCreateTime: value[1],
      //     };
      //   },
      // },
    },
    {
      title: '完成时间',
      dataIndex: 'creditEndTime',
      key: 'creditEndTime',
      valueType: 'dateRange',
      search: {
        transform: transformCompleteTime,
      },
      render(_, record) {
        return record?.creditEndTime;
      },
      // search: false,
    },
    {
      title: '操作',
      key: 'option',
      width: 200,
      fixed: 'right',
      valueType: 'option',
      render: (_, row) => (
        <>
          <>
            <Link to={`/operation-manager/first-audit/detail?flowNo=${row.flowNo}`}>查看详情</Link>
          </>
        </>
      ),
    },
  ];

  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [exportLoading, setExportLoading] = useState(false);

  return (
    <>
      <PageContainer>
        <ProTable<FirstAuditListItems>
          columns={columns}
          formRef={formRef}
          search={{ labelWidth: 120 }}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          request={(params) => getFirstAuditList(params)}
          toolBarRender={() => {
            return [
              <Button
                key="button"
                type="primary"
                loading={exportLoading}
                onClick={() => {
                  setExportLoading(true);
                  const {
                    generateTime,
                    creditEndTime,
                    ...data
                  } = formRef?.current?.getFieldsValue();
                  const generateTimeObj = transformGenerateTime(generateTime);
                  const completeTimeObj = transformCompleteTime(creditEndTime);
                  const newForm = {
                    ...data,
                    ...generateTimeObj,
                    ...completeTimeObj,
                  };
                  console.log(newForm);
                  exportList(newForm)
                    .then((res) => {
                      downLoadExcel(res);
                      setExportLoading(false);
                    })
                    .catch(() => {
                      setExportLoading(false);
                    });
                }}
              >
                导出
              </Button>,
            ];
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTabs />
    <KeepAlive name="operation-manager/first-audit">
      <FirstAudit />
    </KeepAlive>
  </>
);
