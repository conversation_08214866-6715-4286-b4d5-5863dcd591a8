/*
 * @Author: alan771.tu <EMAIL>
 * @Date:2024-11-26 09:52:28
 * @LastEditors: alan771.tu
 * @LastEditTime: 2024-11-26 09:52:28
 * @FilePath: lala-finance-biz-web/src/pages/CarInsurancePolicyManage/data.d.ts
 * @Description: data.d.ts
 */

export interface CarInsurancePolicyBaseInfoParams {
  id: string | number;
  plateNo?: string; // 车牌号/新车合格证号
  vin?: string; // 车架号
  orderNo?: string; // 贷款订单号
  policyNo?: string; // 保单号
  policyHolder?: string; // 投保人
  policyCommercialInsurance?: string | number; // 保单商业险金额
  commercialInsuranceStartDate?: string; // 商业险开始日期
  commercialInsuranceEndDate?: string; // 商业险结束日期
  factoryPlateModel?: string; // 厂牌型号
  extendInfo?: Record<string, any> | string; // 其他信息
  event?: string; // 事件类型
}
export interface CarInsurancePolicyItem extends CarInsurancePolicyBaseInfoParams {
  userName?: string; // 客户姓名
  idNo?: string; // 客户身份证/信用代码
  insuranceCompany?: string; // 保险公司名称
  insuranceSubject?: string; // 投保主体
  channelName?: string; // 渠道名称
  lendingTime?: string; // 放款日期
  orderCommercialInsurance?: number; // 订单商业险金额
  remainingValueOfPolicy?: number; // 保单剩余价值
  uploadOverdueDays?: number; // 保单上传逾期天数
  otherDataCount?: string; // 其他资料
  approveStatus?: string | number; // 保单状态
  rejectMsg?: string; // 驳回原因
  endorsementFileList?: string | string[]; // 批单资料
  otherFileList?: string | Record<string, any>[]; // 其他资料
}

export interface CarInsurancePolicyOcrParams {
  shortFilePath: string[];
}

export interface CarInsurancePolicyListParams {
  current: number;
  pageSize: number;
  plateNo: string;
  vin: string;
  orderNo: string;
  userName: string;
  idNo: string;
  insuranceCompany: string;
  insuranceSubject: string;
  channelName: string;
  approveStatus: string;
  lendingTimeStart: string;
  lendingTimeEnd: string;
}
