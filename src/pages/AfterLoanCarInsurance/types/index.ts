/**
 * 贷后还款管理车险
 */
export interface IlistItem {
  orderNo: string; // 订单编号
  repayPlanNo: string;
  accountName: string; // 账户名称
  accountNumber: string; // 账户编号
  classification: string; // 产品一级分类
  productName: string; // 产品名称
  repayMode: number; // 还款类型 1.一次本息 2.等额本息
  totalTerm: number; // 借款期限
  repayTerm: number; // 还款期限
  annualInterestRate: string; // 借款利率
  lendingTime: string; // 放款日期
  lastRepayTime: string; // 应结清日期
  lastClearDate: string; // 实际结清日期
  amountDue: string; // 应还金额
  principal: string; // 应还本金
  interest: string; // 应还利息
  overdueInterest: string; // 应还罚息
  overdueLatePaymentFee: string; // 应还逾期滞纳金
  advanceSettleLiquidatedDamages: string; // 应还提前结清违约金
  amountPaid: string; // 已还总额
  principalPaid: string; // 已还本金
  interestPaid: string; // 已还利息
  overdueInterestPaid: string; // 已还罚息
  overdueLatePaymentFeePaid: string; // 已还逾期滞纳金
  advanceSettleLiquidatedDamagesPaid: string; // 已还提前结清违约金
  remainingAmountDue: string; // 剩余应还总额
  remainingPrincipal: string; // 剩余应还本金
  remainingInterest: string; // 剩余应还利息
  remainingOverdueInterest: string; // 剩余应还罚息
  remainingOverdueLatePaymentFee: string; // 剩余应还逾期滞纳金
  remissionAmount: string; // 逾期减免金额
  remissionNormalAmount: string; // 正常减免金额
  recallAmount: string; // 催收回款金额
  cancelProfitAmount: string; // 退保盈余
  overDueDay: number; // 逾期天数
  status: number; // 还款状态 1. 待还款 2. 部分还款 3.结清
  repayTime: string; // 应还款日
  termDetail: string; // 期数
  productCode: string; // 产品code
  createTime: string; // 还款创建时间
  loanChannelCode: string; // 小贷渠道类型：易人行、百信
  leaseChannelType: string; // 融租渠道类型
  leaseChannelName: string; // 融租渠道名称
  firstRepayAmountSum: string; // 首期还款金额
}

export interface IgetListParams {
  current?: number; // 页数 页数
  productCode?: string; // 产品code
  pageSize?: number; // 条数 条数
  accountNumber?: number; // 账户编号
  orderNo?: string; // 订单编号
  accountName?: string; // 账户名称
  clearStartTime?: string; // 结清开始时间
  clearEndTime?: string; // 结清结束时间
  status?: number; // 还款状态1. 待还款 2. 提前结清 3.结清 4.逾期 5.逾期结清 6.坏账
  classification?: string; // 产品一级分类
  secondaryClassification?: string; // 为了避免后续数据量过大 sql效率过低，前端每次查询都会传该值 产品二级分类
  createStartTime?: string; // 还款单创建开始时间 还款单创建开始时间
  createEndTime?: string; // 还款单创建结束时间 还款单创建结束时间
  vin?: string; // 车架号
  licensePlateNo?: string; // 车牌号
}

export interface IgetIncommingInfo {
  id: number;
  userNo: string;
  orderNo: string;
  userName: string;
  orderReceiptNo: string;
  productName: string;
  loanTerm: string;
  interestRate: string;
  channel: string;
  applyAmount: string;
  orderStatus: number;
  repayType: string;
  createdAt: string;
  creditAmount: string;
  usageOfLoan: string;
  cashOrderStatusLog: string;
  operateLog: string;
}

export interface IlistRspListItem {
  id: number;
  amountDue: string;
  principal: string;
  interest: string;
  cost: string;
  paidAmount: string;
  repayTime: string;
  termDetail: string;
}
export interface IgetCarRepaymentRes {
  repayTerm: number;
  repayMode: number;
  annualInterestRate: string;
  listRspList: IlistRspListItem[];
}

export interface IgetCarLoanInfoItem {
  amount: string;
  fundFlow: boolean;
  lendingMaster: string;
  lendingModel: number;
  lendingNo: string;
  lendingTime: null;
  orderNo: string;
  plateNo: string;
  receiptMaster: string;
  schemaItemNo: string;
  status: number;
  type: number;
  vin: string;
}

/**
 * 放款状态
 */
export const loanStatusMap = {
  0: '放款失败',
  1: '待放款',
  2: '放款成功',
  '-1': '已取消',
  3: '待请款',
  10: '待一审',
  11: '待二审',
  12: '驳回',
};

/**
 * 放款类型
 */
export const loanTypeMap = {
  1: '进件',
  2: '债转',
  3: '代偿',
  5: '金融放款',
  6: '委托投保',
};

/**
 * 放款方式
 */
export const loanModelMap = {
  1: '线上',
  2: '线下',
};

export interface IrepaymentInfoItem {
  id: number;
  repayPlanNo: string; // 还款计划编号
  status: number; // 还款状态
  repayTime: string; // 应还日期
  shouldAmountDue: string; // 应还总额
  shouldPrincipal: string; // 应还本金
  shouldInterest: string; // 应还利息
  shouldCost: string; // 费用
  returnedAmountDue: string; // 已还总额
  returnedPrincipal: string; // 已还本金
  returnedInterest: string; // 已还利息
  returnedCost: string; // 已还费用
  remainingAmountDue: string; // 剩余应还总额
  remainingPrincipal: string; // 剩余应还本金
  remainingInterest: string; // 剩余应还利息
  remainingCost: string; // 剩余应还费用
  actualRepayTime: string; // 实际还款时间
  actualSettleTime: string; // 实际结清时间
  preSettleCost: string; // 提前结清手续费
  overdueInterest: string; // 逾期罚息
  overdueLatePaymentFee: string; // 逾期滞纳金
  advanceLiquidatedDamages: string; // 提前结清违约金
  termDetail: string; // 当前期/总期数
  term: number; // 当前期数
  overdueDay: number; // 逾期天数
  repayOverdueInterest: string; // 未知不知道啥字段 ?
  remainingOverdueInterest: string; // 剩余应还逾期罚息
  repayOverdueLatePaymentFee: string; // 未知不知道啥字段 ?
  remainingOverdueLatePaymentFee: string; // 剩余应还逾期滞纳金

  returnedOverdueInterest: string; // 已还逾期罚息
  returnedOverdueLatePaymentFee: string; // 已还逾期滞纳金
  otherCost: string; // 其它费用
  policyCancelFee: string; // 已还退保盈余
  overdueId: string; // 逾期单号
  overdueCaseNo: string; // 逾期案件单号
  billNo: string; // 账单ID 账单编号
}

export interface IlookCarInfoItem extends IrepaymentInfoItem {
  schemaItemNo: string; // 还款项编号
  overdueCaseNo: string; // 逾期案件单号
  vin: string; // 车架号
  licensePlateNo: string; // 车牌号
  carPolicyTermRspBOS: IrepaymentInfoItem[]; // 车辆还款详情，期维度，key=term
  preSettleAmount: number; // 提前结清违约金
  preSettleDamages: number; // 费率
}

export const isPreSettleMap = {
  0: '否',
  1: '是',
};

export type TisPreSettle = keyof typeof isPreSettleMap;
/**
 * 车险分期特殊处理车辆
 */
export interface IspecialCarPolicyRepayItem {
  uuid?: string; //
  id?: number;
  repaySchemaItemNo?: string; // 	 还款项编号（车辆维度），取billNo值
  isPreSettle?: TisPreSettle; // 是否提前结清,0是，1否
  repayAmount?: number; // 还款金额
  remissionDetailList?: IremissionDetailItem[];
}
/**
 * 减免项
 */
export interface IremissionDetailItem {
  id?: number;
  costType?: TreduceFeeMap; // 减免费用项
  remissionTerms?: number[]; // 减免期数
  remissionAmount?: number; // 减免金额
}
/**
 * 还款凭证
 */
export interface IattachItem {
  name: string; // 文件名称
  filePath: string; // 文件路径
}

/**
 * 线下还款 调审批入参
 */
export interface IofflineRepayParams {
  accountNumber: string; // 用户编号
  orderNo: string; // 订单编号
  repayDate: string; // 还款日期
  actualRepayRole: 1 | 2; // 实际还款方     1: '用户本人', 2: '第三方',
  accountName: string; // 用户名称
  compensateOrgName: string; // 代偿机构名称 代偿机构名称
  attach: IattachItem[]; // 还款凭证
  carPolicyRepayMode: TcarPolicyRepayMode; // 还款方式
  specialCarPolicyRepayList: IspecialCarPolicyRepayItem[]; // 车险分期特殊处理车辆
}
// 还款方式
export const carPolicyRepayModeMap = {
  1: '全部车辆还款至最新一期',
  3: '其他车辆还款至最新一期，部分车辆特殊处理',
  2: '其他车辆不还款，部分车辆特殊处理',
};

export enum EcarPolicyRepayMode {
  DEFAULT = 1,
  ALL_CAR_NOT_REPAY_OTHER = 2,
  ALL_CAR_REPAY_OTHER = 3,
}
export type TcarPolicyRepayMode = keyof typeof carPolicyRepayModeMap;

// 减免费项
export const reduceFeeMap = {
  '1': '利息',
  '3': '逾期罚息',
  '5': '逾期滞纳金',
  // '2': '提前结清违约金', 这期没有 2023-06-26
  '4': '本金',
};
export type TreduceFeeMap = keyof typeof reduceFeeMap;

export interface IcancelPolicyParams {
  attach: IattachItem[];
  schemaItemNo: string; // 还款项编号
  cancelAmount: string; // 退保金额
  orderNo: string; // 订单编号
}

/**
 * 减免信息
 */
export interface IremissionInfo {
  applyNo: string; //
  licensePlateNo: string; // 车牌号
  orderNo: string; // 订单号
  remissionDetailCostBOS: { costType: number; remissionAmount: number }[];
  repayDetailNo: string;
  repayPlanNo: string;
  vin: string; // 车架号
}
