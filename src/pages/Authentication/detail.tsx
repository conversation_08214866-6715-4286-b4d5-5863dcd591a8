import HeaderTab from '@/components/HeaderTab';
import WalletAccountInfo from '@/components/WalletAccountInfo';
import {
  ClaimRechargeModal,
  ClaimRecordModal,
  TransactionDetailModal,
} from '@/pages/CarInsuranceCustomer/components';
import { ModalForm, PageContainer } from '@ant-design/pro-components';
import { ProFormText } from '@ant-design/pro-form';
import { history, useModel } from '@umijs/max';
import type { StepProps } from 'antd';
import { Card, Descriptions, Divider, Empty, Image, message, Popover, Steps } from 'antd';
import React, { memo, useMemo, useRef, useState } from 'react';
import AuthQrCodeModal from './components/AuthQrCodeModal';
import UpdateAuthorizer from './components/UpdateAuthorizer';
import type { IauthInfo } from './services';
import { statusMap, updateEmail } from './services';

const Detail = () => {
  const { authList = [] } = useModel('Authentication.authentication');
  const query = history?.location?.query;
  const { authorizerCertiNo, epAuthNo } = query as any;
  const [email, setEmail] = useState('');
  // modal ref
  const claimRechargeRef = useRef<any>(null);
  const transactionRef = useRef<any>(null);
  const claimRecordRef = useRef<any>(null);

  const data = useMemo(() => {
    const auth = authList
      ?.filter((item: any) => item.epAuthInfo?.epAuthNo === epAuthNo)
      .find((item: any) => item.epAuthorizerAuthInfo?.authorizerCertiNo === authorizerCertiNo);
    const { epAuthorizerAuthInfo = {}, epAuthInfo = {} } = auth || ({} as any);
    setEmail(epAuthorizerAuthInfo?.authorizerEmail);
    return {
      ...epAuthorizerAuthInfo,
      ...epAuthInfo,
      ...(epAuthInfo?.extraInfo || {}),
      epAuthorizerAuthInfo,
      epAuthInfo,
    };
  }, [authList, authorizerCertiNo, epAuthNo]);

  function getItems(dataInfo: IauthInfo): StepProps[] {
    const { epAuthInfo } = dataInfo || {};
    const { createdAt, epAccreditDate, epAuthDate } = epAuthInfo || {};
    const items: StepProps[] = [
      { title: '待认证', description: createdAt, status: 'finish' },
      { title: '待授权', description: epAuthDate, status: 'finish' },
      { title: '已授权', description: epAccreditDate, status: 'finish' },
    ];
    return items.filter((item) => item.description) as StepProps[];
  }

  const renderLookImage = (url: string) => {
    return (
      <Popover
        trigger={'click'}
        content={url ? <Image src={url} width={300} height={250} /> : <Empty />}
      >
        <a type="primary">点击查看</a>
      </Popover>
    );
  };
  return (
    <>
      <HeaderTab />
      <PageContainer title="详情">
        <Card title="认证进度">
          <Steps current={1} items={getItems(data)} />
        </Card>
        <Card>
          <Descriptions
            title="认证企业详情"
            style={{ padding: '20px 0' }}
            extra={<UpdateAuthorizer />}
            column={2}
          >
            <Descriptions.Item label="企业营业执照">
              {renderLookImage(data?.businessLicenseUrl)}
            </Descriptions.Item>
            <Descriptions.Item label="当前状态">{statusMap[data?.authStatus]}</Descriptions.Item>
            <Descriptions.Item label="统一社会信用代码">{data?.certiNo}</Descriptions.Item>
            <Descriptions.Item label="创建时间">{data?.createdAt}</Descriptions.Item>
            <Descriptions.Item label="借款企业名称">{data?.certiName}</Descriptions.Item>
            <Descriptions.Item label="认证时间">{data?.epAuthDate}</Descriptions.Item>
            <Descriptions.Item label="借款企业法人名称">{data?.legalPerson}</Descriptions.Item>
            <Descriptions.Item label="借款企业地址">{data?.enterpriseAddress}</Descriptions.Item>
          </Descriptions>
          <Descriptions column={2}>
            <Descriptions.Item label="授权签约人身份证">
              {renderLookImage(data?.idCardFrontUrl)}
            </Descriptions.Item>
            <Descriptions.Item label="授权签约人联系电话">{data?.authorizerTel}</Descriptions.Item>
            <Descriptions.Item label="授权签约人姓名">{data?.authorizerName}</Descriptions.Item>
            <Descriptions.Item label="授权签约人联系邮箱">
              {email}
              <ModalForm
                title={<div>修改邮箱</div>}
                trigger={<a type="link">点击修改</a>}
                onFinish={async (values) => {
                  try {
                    await updateEmail({
                      authorizerEmail: values?.authorizerEmail,
                      epAuthNo: data?.epAuthNo,
                      stakeholderId: data?.stakeholderId,
                    });
                    setEmail(values?.authorizerEmail);
                    message.success('修改成功');
                    return true;
                  } catch (error) {
                    message.success('修改失败');
                    return true;
                  }
                }}
              >
                <ProFormText
                  label="授权签约人联系邮箱"
                  name={'authorizerEmail'}
                  placeholder={'请输入新的邮箱'}
                  rules={[{ required: true, message: '必填' }]}
                />
              </ModalForm>
            </Descriptions.Item>
            <Descriptions.Item label="授权签约人身份证号">
              {data?.authorizerCertiNo}
            </Descriptions.Item>
            <Descriptions.Item label="授权时间">{data?.epAccreditDate}</Descriptions.Item>
            <Descriptions.Item label="状态">
              {statusMap[data?.authStatus]}
              {[1, 4].includes(data?.authStatus) ? (
                <AuthQrCodeModal epAuthNo={data?.epAuthNo} certiName={data?.certiName}>
                  <a>查案授权二维码</a>
                </AuthQrCodeModal>
              ) : null}
            </Descriptions.Item>
          </Descriptions>
          <Divider />
          {/* 专属充值账户信息 */}
          <WalletAccountInfo
            data={{
              ...(data?.extMappingAccountBO || {}),
              epBankNo: data?.extMappingAccountBO?.bankNo,
              channelName: data?.extMappingAccountBO?.reimbursementEntityTerList,
            }}
            showTransaction={() => {
              transactionRef.current.show();
            }}
            showClaimRecharge={() => {
              claimRechargeRef.current.show();
            }}
            showClaimRecord={() => {
              claimRecordRef.current.show();
            }}
            secondProductCode="0303"
          />
        </Card>
        {/* 充值 */}
        <ClaimRechargeModal
          targetRemitAccountName={data?.certiName}
          externalOwnerId={data?.extMappingAccountBO?.externalOwnerId}
          accountName={data?.extMappingAccountBO?.accountName}
          claimRechargeRef={claimRechargeRef}
          productSecondCode="0303"
        />
        {/* 认领记录 */}
        <ClaimRecordModal
          externalOwnerId={data?.extMappingAccountBO?.externalOwnerId}
          claimRecordRef={claimRecordRef}
        />
        {/* 流水明细 */}
        <TransactionDetailModal
          externalOwnerId={data?.extMappingAccountBO?.externalOwnerId}
          transactionRef={transactionRef}
          secondProductCode="0303"
        />
      </PageContainer>
    </>
  );
};
export default memo(Detail);
