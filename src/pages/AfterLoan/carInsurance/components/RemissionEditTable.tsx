import type { ActionType } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import BigNumber from 'bignumber.js';
import React, { useImperativeHandle, useRef, useState } from 'react';
type RemissionEditTableProps = {
  billInfoData?: Record<string, any>;
  cRef: any;
  formRef: any;
};
const RemissionEditTable: React.FC<RemissionEditTableProps> = ({ billInfoData, cRef, formRef }) => {
  const [dataSource, setDataSource] = useState<[]>([{ id: 1 }]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([1]);
  const [updateRender, setUpdateRender] = useState(false);
  const actionRefEdit: any = useRef<ActionType>();
  // const [curRepayItem, setCurRepayItem] = useState(undefined);
  // console.log({...rest},'rest');
  useImperativeHandle(cRef, () => ({
    validatorEditRemission: () => {
      return actionRefEdit?.current?.validateFields();
    },
  }));

  // const getAmount = () => {
  //   const init = {
  //     totalPrincipalUnpaid: 0,
  //     totalInterestUnpaid: 0,
  //     totalOverduePenaltyUnpaid: 0,
  //     totalBreach: 0,
  //     totalLate: 0,
  //   };

  //   if (curRepayItem) {
  //     const {
  //       totalPrincipalUnpaid,
  //       totalInterestUnpaid,
  //       totalOverduePenaltyUnpaid,
  //       totalBreach,
  //       totalLate,
  //     } = curRepayItem;
  //     // console.log(curRepayItem, 'curRepayItem', {
  //     //   totalPrincipalUnpaid: new BigNumber(totalPrincipalUnpaid).toNumber(),
  //     //   totalInterestUnpaid: new BigNumber(totalInterestUnpaid).toNumber(),
  //     //   totalOverduePenaltyUnpaid: new BigNumber(totalOverduePenaltyUnpaid).toNumber(),
  //     //   totalBreach: new BigNumber(totalBreach).toNumber(),
  //     //   totalLate: new BigNumber(totalLate).toNumber(),
  //     // });
  //     return {
  //       totalPrincipalUnpaid: new BigNumber(totalPrincipalUnpaid).toNumber(),
  //       totalInterestUnpaid: new BigNumber(totalInterestUnpaid).toNumber(),
  //       totalOverduePenaltyUnpaid: new BigNumber(totalOverduePenaltyUnpaid).toNumber(),
  //       totalBreach: new BigNumber(totalBreach).toNumber(),
  //       totalLate: new BigNumber(totalLate).toNumber(),
  //     };
  //   } else {
  //     return init;
  //   }
  // };

  const columns = [
    {
      title: '车辆车架号',
      dataIndex: 'billNo', //后端接口需要传billNo+车架号,只是只展示车架号
      valueType: 'select',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
      width: 200,
      fieldProps: (_, row: any) => {
        // console.log({ ...rest });
        return {
          style: { width: '100%' },
          onChange: (value: string) => {
            // console.log('value123', value, row); //value 为 账单号-车架号
            const billNoT = value?.split('-')[0]; //
            //找到当前value值
            const curRepayItem =
              billInfoData?.billRspDTOList?.find(({ billNo }) => billNoT === billNo) || {};
            const {
              totalPrincipalUnpaid,
              totalInterestUnpaid,
              totalOverduePenaltyUnpaid,
              totalBreach,
              totalLate,
            } = curRepayItem;
            // setCurRepayItem(curRepayItem);
            // const {billNo} = curRepayItem;
            let curInitialVal: Record<string, number | undefined> = {
              remissionPrincipal: undefined,
              remissionInterest: undefined,
              remissionPenalty: undefined,
              remissionAdvanceSettleLiquidatedDamages: undefined,
              remissionDelayAmount: undefined,
              // totalPrincipalUnpaid: 0,
              // totalInterestUnpaid: 0,
              // totalOverduePenaltyUnpaid:0,
              // totalBreach:0,
              // totalLate: 0,
            };
            if (value) {
              curInitialVal = {
                // billNo:billNoT,
                remissionPrincipal: 0,
                remissionInterest: 0,
                remissionPenalty: 0,
                remissionAdvanceSettleLiquidatedDamages: 0,
                remissionDelayAmount: 0,
                totalPrincipalUnpaid: new BigNumber(totalPrincipalUnpaid).toNumber(),
                totalInterestUnpaid: new BigNumber(totalInterestUnpaid).toNumber(),
                totalOverduePenaltyUnpaid: new BigNumber(totalOverduePenaltyUnpaid).toNumber(),
                totalBreach: new BigNumber(totalBreach).toNumber(),
                totalLate: new BigNumber(totalLate).toNumber(),
              };
            }
            // 切换下拉，值清空
            actionRefEdit?.current?.setRowData(row?.rowIndex, curInitialVal);

            // setTimeout(() => {
            //   // setDataSource((temp: any) => {
            //   //   temp[row?.rowIndex] = {
            //   //     ...temp[row?.rowIndex],
            //   //     ...curInitialVal,
            //   //   };
            //   //   onChange(temp);
            //   //   return temp;
            //   // });
            //   setUpdateRender(!updateRender);
            // }, 300);

            // console.log(actionRefEdit?.current?.getFieldsValue(), '----');
            // setTimeout(() => {
            //   setDataSource((temp: any) => {
            //     temp[row?.rowIndex] = {
            //       ...temp[row?.rowIndex],
            //       ...curInitialVal,
            //     };
            //     return temp;
            //   });
            //   setUpdateRender(!updateRender);
            // }, 300);
          },
          // getPopupContainer: (triggerNode: any) => triggerNode.parentNode,
          options: billInfoData?.billRspDTOList?.map(({ subjectMatterNo, billNo }: any) => ({
            label: subjectMatterNo,
            value: `${billNo}-${subjectMatterNo}`,
            key: billNo,
            disabled: actionRefEdit?.current
              ?.getRowsData()
              ?.find((item1) => item1.billNo === `${billNo}-${subjectMatterNo}`),
            //   ?.map((item1) => item1.subjectMatterNo) //billNo代表 账单号 + '-' + 车架号
            //   ?.includes(subjectMatterNo ),
          })),
          showSearch: true,
          showArrow: true,
          optionFilterProp: 'label',
          filterOption: (input: string, option: { label: string }) =>
            option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
        };
      },
    },
    // {
    //   dataIndex: 'billNo',
    //   hideInTable: true,
    // },
    {
      title: '减免本金(元)',
      dataIndex: 'remissionPrincipal',
      valueType: 'digit',
      fieldProps: (_, row: any) => {
        return { precision: 2, disabled: !row?.entity?.billNo };
      },
      formItemProps: {
        validateTrigger: ['onBlur', 'onChange'],
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          // {
          //   type: 'number',
          //   max: getAmount().totalPrincipalUnpaid,
          //   message: `金额不可超过${getAmount().totalPrincipalUnpaid}`,
          // },
          {
            validator: (_, value) => {
              const rowKey = _.field.split('.')[0];
              const rowIndex = _.field.split('.')[1];

              const record = actionRefEdit?.current?.getFieldValue?.(rowKey)?.[Number(rowIndex)];
              const { totalPrincipalUnpaid } = record || {};
              if (record && value > totalPrincipalUnpaid) {
                return Promise.reject(new Error(`金额不可超过${totalPrincipalUnpaid}`));
              }
              return Promise.resolve();
            },
          },
        ],
      },
    },
    {
      title: '减免利息(元)',
      dataIndex: 'remissionInterest',
      valueType: 'digit',
      fieldProps: (_, row: any) => {
        return { precision: 2, disabled: !row?.entity?.billNo };
      },
      formItemProps: {
        validateTrigger: ['onBlur', 'onChange'],
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            validator: (_, value) => {
              const rowKey = _.field.split('.')[0];
              const rowIndex = _.field.split('.')[1];
              const record = actionRefEdit?.current?.getFieldValue?.(rowKey)?.[Number(rowIndex)];
              const { totalInterestUnpaid } = record || {};
              if (record && value > totalInterestUnpaid) {
                return Promise.reject(new Error(`金额不可超过${totalInterestUnpaid}`));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      // formItemProps: {
      //   rules: [
      //     { required: true, message: '请输入大于等于0的数字' },
      //     {
      //       type: 'number',
      //       max: getAmount().totalInterestUnpaid,
      //       message: `金额不可超过${getAmount().totalInterestUnpaid}`,
      //     },
      //   ],
      // },
    },
    {
      title: '减免罚息(元)',
      dataIndex: 'remissionPenalty',
      valueType: 'digit',
      fieldProps: (_, row: any) => {
        return { precision: 2, disabled: !row?.entity?.billNo };
      },
      // fieldProps: { style: { width: '100%' } },
      // formItemProps: {
      //   rules: [
      //     { required: true, message: '请输入大于等于0的数字' },
      //     {
      //       type: 'number',
      //       max: getAmount().totalOverduePenaltyUnpaid,
      //       message: `金额不可超过${getAmount().totalOverduePenaltyUnpaid}`,
      //     },
      //   ],
      // },
      formItemProps: {
        validateTrigger: ['onBlur', 'onChange'],
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            validator: (_, value) => {
              const rowKey = _.field.split('.')[0];
              const rowIndex = _.field.split('.')[1];
              const record = actionRefEdit?.current?.getFieldValue?.(rowKey)?.[Number(rowIndex)];
              const { totalOverduePenaltyUnpaid } = record || {};
              if (record && value > totalOverduePenaltyUnpaid) {
                return Promise.reject(new Error(`金额不可超过${totalOverduePenaltyUnpaid}`));
              }
              return Promise.resolve();
            },
          },
        ],
      },
    },
    {
      title: '减免违约金(元)',
      dataIndex: 'remissionAdvanceSettleLiquidatedDamages',
      valueType: 'digit',
      fieldProps: (_, row: any) => {
        return { precision: 2, disabled: !row?.entity?.billNo };
      },
      // fieldProps: { style: { width: '100%' } },
      // formItemProps: {
      //   rules: [
      //     { required: true, message: '请输入大于等于0的数字' },
      //     {
      //       type: 'number',
      //       max: getAmount().totalBreach,
      //       message: `金额不可超过${getAmount().totalBreach}`,
      //     },
      //   ],
      // },
      formItemProps: {
        validateTrigger: ['onBlur', 'onChange'],
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            validator: (_, value) => {
              const rowKey = _.field.split('.')[0];
              const rowIndex = _.field.split('.')[1];
              const record = actionRefEdit?.current?.getFieldValue?.(rowKey)?.[Number(rowIndex)];
              const { totalBreach } = record || {};
              if (record && value > totalBreach) {
                return Promise.reject(new Error(`金额不可超过${totalBreach}`));
              }
              return Promise.resolve();
            },
          },
        ],
      },
    },
    {
      title: '减免滞纳金(元)',
      dataIndex: 'remissionDelayAmount',
      valueType: 'digit',
      fieldProps: (_, row: any) => {
        return { precision: 2, disabled: !row?.entity?.billNo };
      },
      // fieldProps: { style: { width: '100%' } },
      // formItemProps: {
      //   rules: [
      //     { required: true, message: '请输入大于等于0的数字' },
      //     {
      //       type: 'number',
      //       max: getAmount().totalLate,
      //       message: `金额不可超过${getAmount().totalLate}`,
      //     },
      //   ],
      // },
      formItemProps: {
        validateTrigger: ['onBlur', 'onChange'],
        rules: [
          { required: true, message: '请输入大于等于0的数字' },
          {
            validator: (_, value) => {
              const rowKey = _.field.split('.')[0];
              const rowIndex = _.field.split('.')[1];
              const record = actionRefEdit?.current?.getFieldValue?.(rowKey)?.[Number(rowIndex)];
              const { totalLate } = record || {};
              if (record && value > totalLate) {
                return Promise.reject(new Error(`金额不可超过${totalLate}`));
              }
              return Promise.resolve();
            },
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: () => {
        return null;
      },
    },
  ];
  // console.log(
  //   actionRefEdit?.current?.getRowsData()?.length,
  //   actionRefEdit?.current?.getRowsData(),
  //   formRef,
  //   formRef?.getFieldsValue(),
  //   '----',
  // );

  return (
    <EditableProTable
      name="remission"
      rowKey="id"
      toolBarRender={false}
      columns={columns}
      controlled
      // recordCreatorProps={false}
      value={dataSource}
      // actionRef={actionRefEdit}
      pagination={false}
      maxLength={billInfoData?.billRspDTOList?.length}
      onChange={(val) => {
        // console.log(val);
        // onChange(val);
        setDataSource(val);
      }}
      // editable={{
      //   type: 'multiple',
      //   editableKeys: [1],
      //   onValuesChange: (record, recordList) => {
      //     // 删除不会触发此函数
      //     console.log('record', record);
      //     console.log('recordList', recordList);
      //     // setDataSource(recordList);
      //   },
      //   // onChange: setEditableRowKeys,
      //   actionRender: () => {
      //     return [];
      //   },
      // }}

      editableFormRef={actionRefEdit}
      recordCreatorProps={
        billInfoData?.billRspDTOList?.length > (dataSource?.length || 0)
          ? {
              // newRecordType: 'dataSource',
              record: (index: number) => {
                // console.log(index, 'index');

                return { id: index + 1 };
              },
            }
          : false
      }
      editable={{
        type: 'multiple',
        // onValuesChange: (record, recordList) => {
        //   // console.log('record', record);
        //   console.log('recordList', recordList);
        //   setDataSource(recordList);
        //   onChange(recordList);
        //   setUpdateRender(!updateRender);
        // },
        // onValuesChange: handleValuesChange,
        editableKeys,
        onChange: setEditableRowKeys,
        actionRender: (row, config, defaultDoms) => {
          if (row?.index === 0) {
            return [];
          }
          return [defaultDoms.delete];
        },
      }}
    />
  );
};

export default RemissionEditTable;
