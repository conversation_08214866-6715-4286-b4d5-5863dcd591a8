import { downLoad } from '@/pages/Collection/service';
import type { ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Button, message, Modal, Popconfirm } from 'antd';
import { debounce, pick } from 'lodash';
import React, { memo, useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  createCompensationChannel,
  getAuthEffectiveAuthRecordUser,
  getCompensationCustomerList,
  unbindCompensationChannel,
} from '../services';
import type { CompensationChannelIF } from '../type';
import './index.less';
import UploadFileFormItem from './UploadFileFormItem';
interface IAddCompensationModal {
  addCompensationRef: React.Ref<any>;
  channelCode: string | undefined;
  getDataSource: (values: any) => void;
}

type DataSourceType = {
  id: React.Key;
  customerName?: string;
  idNum?: string;
  certificate?: string;
  status?: number;
};

const statusEnum = {
  init: '待提交',
  submit: '审核中',
  bind: '审核通过',
  reject: '审核拒绝',
  unbind: '解绑',
};
// 不可用状态
const unUseStatus = ['unbind', 'reject', 'submit'];

const customerNameEnum = {
  1: '企业',
  2: '个人',
};

const AddCompensationModal: React.FC<IAddCompensationModal> = ({
  addCompensationRef,
  channelCode,
  getDataSource,
}) => {
  const [visible, setVisible] = useState(false);
  const [editableRowKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<readonly DataSourceType[]>([]);
  const [loading, setLoading] = useState(false);
  const [customerNameList, setCustomerNameList] = useState<any[]>([]);
  const [selectCustomerType, setSelectCustomerType] = useState<number | undefined>(undefined);

  const access = useAccess();

  const editableFormRef = useRef<any>(null);
  // 暴露方法
  useImperativeHandle(addCompensationRef, () => ({
    show: () => {
      setVisible(true);
    },
    hide: () => {
      setVisible(false);
    },
    getDataSource: () => {
      return dataSource;
    },
  }));

  // 搜索客户名称(防抖)
  const handleSearchCustomerName = debounce(async (newValue: string, entityType: number) => {
    if (!newValue || !entityType) return;

    const res = await getAuthEffectiveAuthRecordUser({ nameLike: newValue, entityType });
    setCustomerNameList(res?.data ?? []);
  }, 200);

  // 获取数据 通过channelCode查询
  async function getTableData() {
    // 获取代偿客户列表
    getCompensationCustomerList({
      channelCode,
      productSecondCode: '0303',
      pageSize: 100, // 默认100条，后期会优化，因为关联到外部的代偿客户的计算
    }).then((res) => {
      setDataSource(res?.data ?? []);
      setEditableRowKeys([]);
      // 透传到父组件
      getDataSource(res?.data ?? []);
    });
  }
  // 提交飞书审核
  const handleSubmit = async (values: any) => {
    const pickList = ['entityTeeName', 'entityTeeIdNo', 'entityTee'];
    setLoading(true);

    const pickData = pick(values, pickList);
    const data = {
      ...pickData,
      relativePath: values?.relativePath?.filePath, // 后端在反参时会自己拼接oss路径
      productSecondCode: '0303',
      channelCode,
      entityTee: customerNameList?.find((item: any) => item.idNo === values?.entityTeeIdNo)
        ?.entityId,
    };
    createCompensationChannel(data as CompensationChannelIF)
      .then(() => {
        message.success('提交审核成功');
      })
      .catch(() => {
        // message.error('提交审核失败');
      })
      .finally(() => {
        // 重新更新列表
        getTableData();
        setLoading(false);
      });
  };

  // 解绑代偿客户
  const handleDelete = (target: any) => {
    setLoading(true);
    unbindCompensationChannel({
      authenticationId: target.authenticationId,
    })
      .then(() => {
        message.success('解绑成功');
      })
      .finally(() => {
        // 重新更新列表
        getTableData();
        setLoading(false);
      });
  };

  const columns: ProColumns<any>[] = [
    {
      title: '客户类型',
      dataIndex: 'entityTeeType',
      formItemProps: {
        rules: [{ required: true, message: '请选择客户类型' }],
      },
      valueEnum: customerNameEnum,
      fieldProps: (form, { rowKey, rowIndex }) => {
        return {
          onChange: (value: number) => {
            setSelectCustomerType(value);
            editableFormRef.current?.setRowData(rowIndex, {
              entityTeeIdNo: undefined,
              entityTeeName: undefined,
            });
          },
        };
      },
    },
    {
      title: '客户名称',
      dataIndex: 'entityTeeName',
      valueType: 'select',
      formItemProps: {
        rules: [{ required: true, message: '请选择客户名称' }],
      },
      fieldProps: (form, { rowKey, rowIndex }: { rowKey: string[]; rowIndex: any }) => {
        return {
          showSearch: true,
          showArrow: true,
          filterOption: 'label',
          options: customerNameList?.map((item: any) => ({
            ...item,
            key: item.idNo,
            label: item.name,
            value: `${item.name}$-$${item.idNo}`,
          })),
          onSearch: (value: string) => {
            handleSearchCustomerName(value, selectCustomerType as number);
          },
          onChange: (value: string) => {
            if (!value) {
              form.setFieldValue([...rowKey, 'entityTeeIdNo'], undefined);
              // 清空数据
              editableFormRef.current?.setRowData(rowIndex, {
                entityTeeIdNo: undefined,
                entityTeeName: undefined,
              });
              return;
            }
            const [name, idNo] = value?.split('$-$');
            form.setFieldValue([...rowKey, 'entityTeeIdNo'], idNo);
            editableFormRef.current?.setRowData(rowIndex, {
              entityTeeIdNo: idNo,
              entityTeeName: name,
            });
          },
        };
      },
    },
    {
      title: '身份证号/统一社会信用代码',
      dataIndex: 'entityTeeIdNo',
      readonly: true,
    },
    {
      title: '凭证',
      dataIndex: 'relativePath',
      formItemProps: {
        rules: [
          {
            validator: (_, value) => {
              if (value?.filePath) {
                return Promise.resolve();
              }
              message.error('请上传凭证');
              return Promise.reject('请上传凭证');
            },
          },
        ],
      },
      renderFormItem: () => {
        return <UploadFileFormItem />;
      },
      render: (_, record) => {
        return (
          <div key={record?.relativePath}>
            {record?.relativePath ? (
              <a onClick={() => downLoad(record?.relativePath, '')}>点击下载</a>
            ) : (
              '-'
            )}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'authStatus',
      editable: false,
      formItemProps: {
        initialValue: 'init',
      },
      valueEnum: statusEnum,
      render: (_, record) => {
        return statusEnum[record.authStatus as keyof typeof statusEnum] || '待提交';
      },
    },
    {
      title: '操作',
      valueType: 'option',
      render: (text, record, _, action) => {
        return [
          <Popconfirm
            key="delete"
            title="确定要解绑吗？"
            disabled={unUseStatus.includes(record.authStatus)}
            onConfirm={() => {
              handleDelete(record);
            }}
          >
            <Button type="link" disabled={unUseStatus.includes(record.authStatus)}>
              删除
            </Button>
          </Popconfirm>,
        ];
      },
    },
  ];

  function unMountedModal() {
    // 获取数据
    getDataSource(dataSource);
    setVisible(false);
  }

  useEffect(() => {
    getTableData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [channelCode]);

  useEffect(() => {
    // 获取个人/企业授权列表
    if (selectCustomerType) {
      getAuthEffectiveAuthRecordUser({
        entityType: selectCustomerType,
        pageSize: 500,
      }).then((res) => {
        setCustomerNameList(res?.data ?? []);
      });
    }
  }, [selectCustomerType]);

  return (
    <Modal
      width={800}
      centered
      title="可代偿客户"
      open={visible}
      onCancel={() => {
        unMountedModal();
      }}
      onOk={() => {
        unMountedModal();
      }}
      cancelButtonProps={{
        style: {
          display: 'none',
        },
      }}
    >
      <div style={{ width: '100%', overflow: 'auto' }}>
        <EditableProTable
          editableFormRef={editableFormRef}
          loading={loading}
          formItemProps={{
            wrapperCol: {
              offset: 0,
            },
          }}
          columns={columns}
          // controlled
          rowKey="id"
          scroll={{ x: 'max-content' }}
          value={dataSource}
          onChange={setDataSource}
          pagination={{
            defaultPageSize: 10,
          }}
          toolBarRender={false}
          recordCreatorProps={
            editableRowKeys?.length > 0
              ? false
              : {
                  newRecordType: 'dataSource',
                  position: 'top',
                  creatorButtonText: '新增代偿客户',
                  style: { color: '#0066FF' },
                  disabled: !access.hasAccess('createReimbursement_car_insurance_channel_list'),
                  record: () => ({ id: (Math.random() * 1000000).toFixed(0) }),
                }
          }
          editable={{
            type: 'multiple',
            saveText: '提交审核',
            editableKeys: editableRowKeys,
            onChange: setEditableRowKeys,
            onSave: async (rowKey: any, data: any, row: any) => {
              await handleSubmit(data);
            },
            onDelete: async (rowKey: any, row: any) => {
              setEditableRowKeys(editableRowKeys.filter((key) => key !== row.id));
            },
            actionRender: (row, config, defaultDom) => {
              return [defaultDom.save, defaultDom.delete];
            },
          }}
        />
      </div>
    </Modal>
  );
};

export default memo(AddCompensationModal);
