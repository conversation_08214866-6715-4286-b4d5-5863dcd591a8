import { Table } from 'antd';
import React, { memo } from 'react';

interface UploadExcelIF {
  excelErrorList: any[];
}

const UploadExcel: React.FC<UploadExcelIF> = (props) => {
  const { excelErrorList } = props;

  const columns = [
    { key: 1, title: '序号', dataIndex: 'id' },
    { key: 2, title: '错误', dataIndex: 'error' },
  ];

  return (
    <>
      <div>
        <div style={{ color: 'red' }}>0.批量新增会覆盖已经存在的所有车辆信息</div>
        <div>1.车牌号/新车合格证号：不得含有特殊符号</div>
        <div>2.车架号：需为17位且不得含有空格/O/Q/I/特殊符号</div>
        <div>3.发动机号：不得含有特殊符号</div>
        <div>4.车主姓名/被保险人姓名：不能少于2个字</div>
        <div>5.商业险结束日期：不得早于开始日期，且不能晚于开始日期366天</div>
        <div>6.商业险保费/交强险保费/车船税/其他代付：需要为0-40000之间，最多两位小数点</div>
        <div>7.车牌号/车架号/发动机号不得重复</div>
      </div>
      <Table
        columns={columns}
        dataSource={excelErrorList}
        title={() => <div>表格校验结果</div>}
        pagination={false}
        bordered={true}
        size="small"
      />
    </>
  );
};

export default memo(UploadExcel);
