import HeaderTabs from '@/components/HeaderTab';
import globalStyle from '@/global.less';
import { getAllChannelNameEnum } from '@/services/enum';
import { downLoadExcel } from '@/utils/utils';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { Link } from '@umijs/max';
import { Button, Dropdown, message, Modal } from 'antd';
import type { FormInstance } from 'antd/es/form';
import React, { useRef, useState } from 'react';
import { KeepAlive } from 'react-activation';
import AddNewOrderModal from './components/AddNewOrderModal';
import DisassemblyModal from './components/DisassemblyModal';
import OverhaulModal from './components/OverhaulModal';
import { cancelGpsOrder, exportList, getGpsOrderList } from './service';
import type { GpsListItem } from './type';

enum ORDER_TYPE {
  INSTALL = 1, // 安装
  DISASSEMBLY = 2, // 拆机
  OVERHAUL = 3, // 检修
}

// gps订单状态
enum GPS_ORDER_STATUS {
  CANCEL = -1, // 取消
  INIT = 0, // 初始化
  NEW = 1, // 已推送供应商
  WAIT_DISPATCH = 2, // 供应商派单
  WAIT_INSTALL = 3, // 待供应商加装
  FINISH = 4, // 安装完成
  PUSH_FAIL = 5, // 推送厂商失败
  HAS_DISASSEMBLY = 6, // 已拆机
}

const ORDER_STATUS_ENUM = {
  0: '初始化',
  1: '已推送供应商',
  2: '供应商派单',
  3: '待供应商加装',
  4: '已完成',
  5: '推送厂商失败',
  6: '已拆机',
  7: '推送中',
  '-1': '取消',
};

const ACTION_NAME_MAP = {
  [ORDER_TYPE.INSTALL]: '安装',
  [ORDER_TYPE.DISASSEMBLY]: '拆机',
  [ORDER_TYPE.OVERHAUL]: '检修',
};

const GpsList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [exportLoading, setExportLoading] = useState(false);
  const [showInstallModal, setShowInstallModal] = useState(false);
  const [showOverhaulModal, setShowOverhaulModal] = useState(false);
  const [showDisassemblyModal, setShowDisassemblyModal] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);

  const transformOperateTime = (value: any) => {
    if (!value) return value;
    if (typeof value[0] !== 'string') {
      return {
        operateTimeStart: `${value[0].format('YYYY-MM-DD')} 00:00:00`,
        operateTimeEnd: `${value[1].format('YYYY-MM-DD')} 23:59:59`,
      };
    }
    return {
      operateTimeStart: `${value[0].split(' ')[0]} 00:00:00`,
      operateTimeEnd: `${value[1].split(' ')[0]} 23:59:59`,
    };
  };

  const getAllChannelNameEnumMemo = async () => {
    return await getAllChannelNameEnum();
  };

  const refresh = () => {
    actionRef.current?.reload();
  };

  const handleCancel = (actionType, record) => {
    const { gpsOrderNo, vin } = record;
    Modal.confirm({
      content: `是否确认取消${ACTION_NAME_MAP[actionType]}？`,
      okButtonProps: {
        loading: btnLoading,
      },
      onOk: async () => {
        if (btnLoading) return;
        const params = {
          vin,
          gpsOrderNo,
        };
        setBtnLoading(true);
        try {
          await cancelGpsOrder(params);
        } finally {
          setBtnLoading(false);
        }
        // 刷新数据
        refresh();
        message.success('取消成功！');
      },
    });
  };

  const columns: ProColumns<GpsListItem>[] = [
    {
      title: '工单编号',
      dataIndex: 'gpsOrderNo',
      key: 'gpsOrderNo',
    },
    {
      title: 'GPS供应商',
      dataIndex: 'supplierCode',
      key: 'supplierCode',
      valueType: 'select',
      valueEnum: {
        ZhongRui: '中瑞',
      },
    },
    {
      title: '工单类型',
      dataIndex: 'orderType',
      key: 'orderType',
      valueType: 'select',
      valueEnum: {
        1: '安装',
        2: '拆机',
        3: '检修',
      },
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      search: false,
    },
    {
      title: '车辆信息(车架号)',
      dataIndex: 'vin',
      key: 'vin',
    },
    {
      title: '订单渠道',
      dataIndex: 'orderChannel',
      valueType: 'select',
      request: getAllChannelNameEnumMemo,
      debounceTime: 600000,
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '订单渠道',
      dataIndex: 'orderChannel',
      key: 'orderChannel',
      search: false,
    },
    {
      title: '订单编号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      render: (_, row) => {
        return <Link to={`/businessMng/lease-detail?orderNo=${row.orderNo}`}>{row.orderNo}</Link>;
      },
    },
    {
      title: '外部单号',
      dataIndex: 'outerOrderNo',
      key: 'outerOrderNo',
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      valueType: 'select',
      search: false,
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: ORDER_STATUS_ENUM,
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatusList',
      key: 'orderStatusList',
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: ORDER_STATUS_ENUM,
    },
    {
      title: '下单时间',
      dataIndex: 'operateTime',
      key: 'operateTime',
      search: {
        transform: transformOperateTime,
      },
      render(_, record) {
        return record?.operateTime;
      },
      valueType: 'dateRange',
    },
    {
      title: '下单渠道',
      dataIndex: 'operateChannel',
      valueType: 'select',
      request: getAllChannelNameEnumMemo,
      debounceTime: 600000,
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        // mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '下单渠道',
      dataIndex: 'operateChannel',
      key: 'operateChannel',
      search: false,
    },
    {
      title: '下单人',
      dataIndex: 'operateUser',
      key: 'operateUser',
    },
    {
      title: '操作',
      key: 'option',
      width: 200,
      fixed: 'right',
      valueType: 'option',
      render: (_, row: GpsListItem) => {
        if (
          ![
            GPS_ORDER_STATUS.FINISH,
            GPS_ORDER_STATUS.CANCEL,
            GPS_ORDER_STATUS.PUSH_FAIL,
            GPS_ORDER_STATUS.HAS_DISASSEMBLY,
          ].includes(row.orderStatus)
        ) {
          if (row.orderType === ORDER_TYPE.INSTALL) {
            return (
              <Button
                type="link"
                onClick={() => {
                  handleCancel(ORDER_TYPE.INSTALL, row);
                }}
              >
                取消安装
              </Button>
            );
          }
          if (row.orderType === ORDER_TYPE.DISASSEMBLY) {
            return (
              <Button
                type="link"
                onClick={() => {
                  handleCancel(ORDER_TYPE.DISASSEMBLY, row);
                }}
              >
                取消拆机
              </Button>
            );
          }
          if (row.orderType === ORDER_TYPE.OVERHAUL) {
            return (
              <Button
                type="link"
                onClick={() => {
                  handleCancel(ORDER_TYPE.OVERHAUL, row);
                }}
              >
                取消检修
              </Button>
            );
          }
        }

        return null;
      },
    },
  ];

  // 创建工单
  const createOrder = ({ key }) => {
    switch (Number(key)) {
      case ORDER_TYPE.INSTALL:
        setShowInstallModal(true);
        break;
      case ORDER_TYPE.OVERHAUL:
        setShowOverhaulModal(true);
        break;
      case ORDER_TYPE.DISASSEMBLY:
        setShowDisassemblyModal(true);
        break;
    }
  };

  return (
    <>
      <PageContainer className={globalStyle.mt16}>
        <ProTable<GpsListItem>
          columns={columns}
          formRef={formRef}
          search={{ labelWidth: 120 }}
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          request={(params) => getGpsOrderList(params)}
          toolBarRender={() => {
            return [
              <Dropdown
                menu={{
                  items: [
                    { key: ORDER_TYPE.INSTALL, label: '安装工单' },
                    { key: ORDER_TYPE.OVERHAUL, label: '检修工单' },
                    { key: ORDER_TYPE.DISASSEMBLY, label: '拆机工单' },
                  ],
                  onClick: createOrder,
                }}
                placement="bottomLeft"
              >
                <Button type="primary">创建工单</Button>
              </Dropdown>,
              <Button
                key="exportBtn"
                type="primary"
                loading={exportLoading}
                onClick={() => {
                  setExportLoading(true);
                  const { operateTime, ...data } = formRef?.current?.getFieldsValue();

                  const operateTimeObj = transformOperateTime(operateTime);
                  const newForm = {
                    ...data,
                    ...operateTimeObj,
                  };
                  console.log(newForm);
                  exportList(newForm)
                    .then((res) => {
                      downLoadExcel(res);
                      setExportLoading(false);
                    })
                    .catch(() => {
                      setExportLoading(false);
                    });
                }}
              >
                导出
              </Button>,
            ];
          }}
        />
        {/* 安装弹窗 */}
        <AddNewOrderModal
          open={showInstallModal}
          onCancel={() => {
            setShowInstallModal(false);
          }}
          onSuccess={() => {
            setShowInstallModal(false);
            refresh();
          }}
        />
        {/* 检修弹窗 */}
        <OverhaulModal
          open={showOverhaulModal}
          onCancel={() => {
            setShowOverhaulModal(false);
          }}
          onSuccess={() => {
            setShowOverhaulModal(false);
            refresh();
          }}
        />
        {/* 拆机弹窗 */}
        <DisassemblyModal
          open={showDisassemblyModal}
          onCancel={() => {
            setShowDisassemblyModal(false);
          }}
          onSuccess={() => {
            setShowDisassemblyModal(false);
            refresh();
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTabs />
    <KeepAlive name="operation-manager/car-gps-list">
      <GpsList />
    </KeepAlive>
  </>
);
