import { Input, Select, Spin } from 'antd';
import React, { memo, useRef, useState } from 'react';
import { searchUserList } from '../services';
import type { IsearchUserListItem, IuserType } from '../types';

export type IcustomerName = { userType: IuserType; userNo: string; userName: string };

type Props = {
  onChange?: (val?: IcustomerName) => void;
};
const AccountNameFormItem: React.FC<Props> = (props) => {
  const { onChange } = props;
  const [type, setType] = useState<IuserType>(1);
  const [userNo, setUserNo] = useState<string>();
  const [options, setOptions] = useState<
    {
      label: string;
      value: string;
      certiName: string;
      userItem: IsearchUserListItem;
      title: string;
    }[]
  >([]);

  const timerIdRef = useRef<{ timerId: any }>({ timerId: null });
  const [loading, setLoading] = useState(false);

  function renderHighlightJsx(params: { inputValue: string; value: string }): any {
    /**
     * const str = "********* asd 1231231 asd"
     * replaceAll  --->  "*********-*asd-1231231-*asd-"
     * split ----> ["*********","*asd", "1231231", "*asd"] 如果含有* 代表该项要高亮
     * 循环数组
     */
    const { inputValue, value } = params;
    const valueArr = value.replaceAll(inputValue, `-*${inputValue}-`).split('-');
    return valueArr.map((item, index) => {
      const style = item.includes('*') ? { color: 'red' } : {};
      return (
        <span style={style} key={index}>
          {item.includes('*') ? item.slice(1) : item}
        </span>
      );
    });
  }

  function getOptions(inputValue?: string) {
    setLoading(true);
    setOptions([]);
    searchUserList({ current: 1, pageSize: 50, userType: type, certiName: inputValue })
      .then((data) => {
        setLoading(false);
        setOptions(
          data.map((item) => {
            const { certiName } = item;
            return {
              label: inputValue ? renderHighlightJsx({ inputValue, value: certiName }) : certiName,
              value: item.authNo,
              certiName,
              userItem: item,
              title: certiName,
            };
          }),
        );
      })
      .catch(() => {
        setLoading(false);
      });
  }

  // useEffect(() => {
  //   getOptions();
  // }, []);
  return (
    <div style={{ display: 'flex', gap: 10 }}>
      <Select
        style={{ width: 80 }}
        value={type}
        onChange={(val) => {
          setType(val);
          setOptions([]); // 请款选项
          // getOptions();
          onChange?.(undefined); // 清空form 实例数据
          setUserNo(undefined);
        }}
        options={[
          { value: 1, label: '企业' },
          { value: 2, label: '个人' },
        ]}
      />

      {type === 1 ? (
        <Select
          onSelect={(val, options) => {
            const { certiName, authNo } = options?.userItem;
            onChange?.({ userName: certiName, userNo: authNo, userType: type });
            setUserNo(val);
          }}
          allowClear
          onClear={() => {
            onChange?.(undefined); // 清空form 实例数据
            setUserNo(undefined);
            setOptions([]); // 请款选项
          }}
          dropdownStyle={{ minWidth: 320 }}
          optionLabelProp="certiName"
          style={{ width: 220 }}
          value={userNo}
          notFoundContent={loading ? <Spin size="small" /> : '输入文字搜索企业'}
          filterOption={false}
          showSearch
          onSearch={(value) => {
            if (!value) {
              setOptions([]); // 请款选项
              return;
            }
            if (timerIdRef.current.timerId) {
              clearTimeout(timerIdRef.current.timerId);
            }
            timerIdRef.current.timerId = setTimeout(() => {
              getOptions(value);
            }, 1000);
          }}
          options={options}
          placeholder={'模糊下拉搜索'}
        />
      ) : (
        <Input
          placeholder="请输入客户全名"
          allowClear
          onChange={(e) => {
            onChange?.({ userName: e.target.value, userNo: '', userType: type });
          }}
        />
      )}
    </div>
  );
};

export default memo(AccountNameFormItem);
