/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-07-25 15:34:33
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-13 10:09:59
 * @FilePath: /lala-finance-biz-web/src/pages/Collection/data.d.ts
 * @Description: Collection/data.d.ts
 */

export interface WithHoldParams {
  overdueCaseNo: string;
  withholdMoney: number;
  withholdType: string;
  orderNo: string;
  modalVisible?: string;
}

export interface WithHoldWalletParams {
  overdueCaseNo: string;
  withholdMoney: number;
  freightCustomerType: string; //  货运客户类型：1.司机 2.用户
  freightCustomerId: number; //  货运客户id
  customerPhone: string; //  客户手机号
}

export type SettleAmountParams = {
  orderNo: string; //订单号
  userNo?: string; //用户编号
  productCode: string; //订单的三级产品编码
};

export interface WithHoldListItem {
  reason: string;
  status: number;
  withholdMoney: number;
  withholdTime: string;
}

// 催收拨打详情 & 插入
export interface ICallDetail {
  actionResults?: number; // 行动结果
  actionSign?: number; // 行动标识
  callRecord: string; // 通话记录 ID
  dialNumber: string; // 拨打号码
  dialTime: string; // 拨打时间
  overdueId: string; // 催收单号
  urgePersonId?: string; // 催收员
  urgePersonName: string; // 催收员姓名
  dialBeginTime?: string; // 拨打开始时间
  dialEndTime?: string; // 拨打结束时间
  remarks?: string; // 备注
  status?: string; // 通话状态
  talkTimeSecond?: string; // 通话时长
  turnOnTime?: string; // 接通时间
  callEndTime?: string; // 通话结束时间
  turnOnTime?: string; // 接通时间
  callDateDetail?: any[]; // 拨打时间
}

// 行动标识枚举
export interface IActionEnum {
  value: number;
  label: string;
  actionResultList: [];
}

// 催收拨打详情 & 插入
export interface ICallDetail {
  actionResults?: number; // 行动结果
  actionSign?: number; // 行动标识
  callRecord: string; // 通话记录 ID
  dialNumber: string; // 拨打号码
  dialTime: string; // 拨打时间
  overdueId: string; // 催收单号
  urgePersonId?: string; // 催收员
  urgePersonName: string; // 催收员姓名
  dialBeginTime?: string; // 拨打开始时间
  dialEndTime?: string; // 拨打结束时间
  remarks?: string; // 备注
  status?: string; // 通话状态
  talkTimeSecond?: string; // 通话时长
  turnOnTime?: string; // 接通时间
  callEndTime?: string; // 通话结束时间
  turnOnTime?: string; // 接通时间
  callDateDetail?: any[]; // 拨打时间
}

// 短信记录
export interface SmsRecordDetail {
  phone: string;
  sendStatus: number;
  sendTime: string;
  sendUserName: string;
  sendingMode: string;
  templateContent: string;
  templateName: string;
  urgeId: string;
}

// 申请减免
export interface ApplyReliefParams {
  amount: number;
  fileList: any[];
  overdueId: string;
  urgeRecentlyMsg: string;
}

// 减免记录
export interface CollectReliefListItem {
  createdAt: string;
  fileList?: any[];
  status: number;
  urgeRecentlyMsg: string;
  amount: number;
  urgePerson: string;
}

export interface NewCollectReliefListItem extends CollectReliefListItem {
  attachList?: any[];
  exemptionAmount?: number;
  remark?: string;
}

// 催收回款记录
export interface CallPayMoneyListItem {
  amount: number;
  createdAt: string;
  fileList?: any[];
  attachList?: any[];
  payBackType?: string;
  remitType?: number;
  urgeRecentlyMsg?: string;
  urgePerson: string;
  repayAmount?: string;
  exemptionAmount?: string;
}

// 提交催收回款
export interface SubmitCallBackParams {
  amount: string;
  fileList: any[];
  overdueId: string;
  payBackType: string;
  paymentTime: string;
  urgeRecentlyMsg: string;
  orderNo?: string;
  repayBankNo?: string;
  thirdFlowId?: string;
  repayChannel?: string;
}

// 提交催收回款（新）
export interface BillSubmitCallBackParams {
  billInfoList: object[];
  remissionList: object[];
  repayAmount: number;
  attach: object[];
  bankAmountDate: string;
  overdueCaseNo;
  orderNo: string;
  urgePersonId: string;
  dimension: number;
  repaySerialNo: string;
  remitType: string;
  compensation: number;
  remark: string;
  repayBankNo: string;
  payeeBankNo: string;
}

export interface OverdueCaseListParams {
  current?: number;
  pageSize?: number;
  accountName?: string;
  accountNumber?: string;
  overdueCaseNo?: string;
  productCode?: string;
  productName?: string;
  productSecondTypeCode?: string;
  // urgeStatusList?: [];
  urgeUserId?: string;
  // activeKey?: string;
  myList?: boolean;
  urgeStatusList?: string[];
  remainingPrincipalSort?: string;
}

export interface OverdueCaseList {
  remainingPrincipal: any;
  accountName?: string;
  accountNumber?: string;
  createdAt?: string;
  latestUrgeDateTime?: string;
  latestUrgeResult?: string;
  overdueCaseNo?: string;
  overdueLongestDays?: string;
  overdueMaxLevel?: string;
  overdueTotalCount?: string;
  productCode?: string;
  productName?: string;
  settleDateTime?: string;
  status?: number;
  totalUnpaidAmount?: string;
  urgeUserId?: string;
  totalOverdueAmount?: number;
  withdrawStatus?: number;
  funderChannelCode?: string;
  repurchaseStatus?: number;
}

// 派单
export interface DispatchParams {
  operator: string;
  overdueIdList: any[];
  overdueCaseNoList: any[];
  type: number | string;
  urgeUserId: number | string;
  urgeUserName: number | string;
}

export enum STATUS_CASE {
  'WAIT_FOLLOW_UP' = '20',
  'FOLLOWING_UP' = '30',
  'SETTLE' = '40',
  'REVOKE' = '50',
}

export enum CLASSIFYTYPE {
  'NO_CALL' = '0', // 未拨打案件
  'ENSURE' = '1', //  承诺还款
  'EFFECTIVE' = '2', //  有效联络
  'INVALID' = '3', // 失联案件/无效联络
}

export type CombineRepayBackParams = {
  initiateBillRepaymentReq: {
    productCode: string;
    accountBank: string;
    applyId: string;
    bankName: string;
    bankNumber: string;
    billDate: string;
    billType: number;
    certificateUrl: string;
    productCode: string;
    repayChannel: number;
    repayEntrance: number;
    repayMoney: number;
    repayTime: string;
  };
  paymentRecordReq: {
    amount: number;
    fileList: [
      {
        fileName: string;
        fileUrl: string;
        netWorkPath?: string;
      },
    ];
    overdueCaseNo: string;
    payBackType: string;
    paymentTime: string;
    urgePerson: string;
    urgeRecentlyMsg: string;
  };
};

export const SORT_MAP: Record<string, string> = {
  overdueLongestDays: 'overdueDaySort',
  remainingPrincipal: 'remainingPrincipalSort',
  totalOverdueAmount: 'totalOverdueAmountSort',
};

export interface IgetInfoFromPhoneParams {
  phone: string;
  idNo: string;
  page: number;
  size: number;
}
/**
 * 来电号码查询用户信息
 */
export interface IphoneInfoItem {
  orderNo: string; // 关联业务id
  productCode: string; // 产品code
  productCodeName: string; // 产品名称
  bb: string; // 关联业务编号
  userName: string; // 借款客户名称
  isSelf: string; // 来电客户是否借款本人
  userNo: string; // 借款主体id
  overdueCaseNo: string; // 催收案件编号
}

export interface OverdueSMSItem {
  accountName: string;
  urgePersonName: string;
  urgePersonId: string;
  productName: string;
  productSecondCode: string;
  overDueCaseNo: string;
  totalOverdueAmount: number;
  overdueLongestDays: number;
  overdueNumber: number;
  userNo: string;
  smsUrgeList: [
    {
      templateId: string;
      userInfoList: [
        {
          userName: string;
          userTel: string;
        },
      ];
    },
  ];
}

export interface PhoneListByOverdueCaseItem {
  overdueCaseNo: string;
  pageSize: number;
  current: number;
}

export interface PhoneListByOverdueCaseWithHistoryItem extends PhoneListByOverdueCaseItem {
  productSecondCode: string;
  userNo: string;
  queryHistoryFlag?: boolean;
}

export interface SMSListByOverdueCaseItem {
  overdueCaseNo: string;
  pageSize: number;
  current: number;
}

export interface HistorySMSListByOverdueCaseItem extends SMSListByOverdueCaseItem {
  productSecondCode: string;
  userNo: string;
}

export interface batchOverdueSMSItem {
  urgePersonId: string;
  urgePersonName: string;
  smsTemplateList: any;
}

export interface SmsPageItemProp {
  productName?: string;
  smsTemplateId?: string;
  customerNum?: number;
  smsTemplateName?: string;
  id?: string;
}

export interface IbatchOverdueSMSDataItem {
  customerNum: number;
  smsTemplateId: string;
  smsTemplateName: string;
  tripartite: boolean;
  secondaryClassification: string; // 产品二级分类
}

export interface IaddRefundCardRecord {
  refundReason: string; // 退车原因
  refundTime: string; // 退车时间
  orderNo: string; // 订单号
  productCode: string; // 产品code
  refundAmount: string; // 金额
  remark: string; // 备注
}

export interface IrefundCarRecord {
  orderNo: string;
  productCode: string; // 产品code
  refundTime: string; // 退车时间
  refundReason: string; // 退车原因
  settlePeriods: string; // 	退车时已还期数
  refundAmount: string; // 金额
  remainingPrincipal: string; // 退车时剩余本金
  remark: string; // 备注
  id: number; // 数据库自增id
  operatorId: string;
  operatorName: string;
  guaranteeType: string;
  guaranteePeriod: number;
  channelName: string;
  updatedAt: string;
  createdAt: string;
}

// 货运司机状态映射
export const SIJI_STATUS_MAP = {
  0: '待认证', //  TO_BE_CERTIFIED
  1: '正常', //  NORMAL
  2: '暂时拉黑', //  TEMPORARILY_BLOCK
  3: '永久拉黑', //  PERMANENT_BLOCK
  4: '停止推送', //  STOP_PUSH
  5: '预销户', //  PREPARING_CANCEL
};

export interface IbillInfoParams {
  billNoList: string[];
  dimension: Idimension; // TERM_BILL(1, "期账") ORDER_TERM_BILL(2, "订单期账")
  secondaryClassification: string;
  settleType?: boolean;
}

export interface IbillInfo {
  id: number;
  businessNo: string;
  orderNo: string;
  orderNoList: string[];
  billNo: string;
  billNoList: string[];
  applyPerson: string;
  applyName: string;
  secondProductCode: string;
  productCode: string;
  productName: string;
  repayAmount: number;
  exemptionAmount: string;
  repayAmountDetail: string;
  repayDate: string;
  repayDateStart: string;
  repayDateEnd: string;
  repayMode: number;
  repayModeName: string;
  remitType: number;
  repaySerialNo: string;
  channelName: string;
  channelTypeName: string;
  channelId: string;
  accountName: string;
  accountNameList: string[];
  accountNumber: string;
  customerType: number;
  customerTypeName: string;
  remark: string;
  attach: string;
  attachList: string[];
  creditDate: string;
  creditDateStart: string;
  creditDateEnd: string;
  repayStatus: number;
  repayStatusName: string;
  updatedAt: string;
  createdAt: string;
  status: number;
  statusName: string;
  reason: string;
  approvalLog: string;
  approvalId: string;
  approvalPerson: string;
  approvalTime: string;
  subjectUniqueNo: string;
  subjectUniqueNoList: string[];
  overdueCaseNo: string;
  overdueAmount: string;
  orderAmount: string;
  compensation: number;
  compensationName: string;
  bankName: string;
  bankNo: string;
  bankAccount: string;
  subBranchBank: string;
  smallId: number;
  maxId: number;
  billRspDTOList: IbillRspDTOItem[];
  totalAmountUnpaid: string;
  totalPrincipalUnpaid: string;
  totalInterestUnpaid: string;
  totalOverduePenaltyUnpaid: string;
  totalBreach: string;
  totalLate: string;
}

// 催收回款减免编辑项
export interface AmountDetailType {
  remissionPrincipal: number;
  remissionInterest: number;
  remissionPenalty: number;
  remissionDelayAmount: number;
}
