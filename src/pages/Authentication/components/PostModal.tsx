import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import WalletActions from '@/components/WalletAccountInfo/WalletActions';
import { getBizadminUploadAction } from '@/pages/BusinessExcel/utils';
import { getChannelInfo } from '@/pages/CarInsurance/services';
import {
  ClaimRechargeModal,
  ClaimRecordModal,
  TransactionDetailModal,
} from '@/pages/CarInsuranceCustomer/components';
import { getAuthHeaders } from '@/utils/auth';
import { isCarInsuranceStoreUser } from '@/utils/utils';
import { LinkOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useAccess, useModel } from '@umijs/max';
import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Spin,
  Steps,
  Tag,
  Tooltip,
  Upload,
} from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { authStatusMap, AUTH_STATUS } from '../const';
import {
  ajaxAdd,
  authReview,
  bizLicenseOcr,
  deleteAuthRecord,
  getAuthDetail,
  idCardOcr,
  updateAuthorizer,
  updateChannelCode,
} from '../services';
import AuthQrCodeBtn from './AuthQrCodeBtn';
import './PostModal.less';
import UploadImagePannel from './UploadImagePannel';

interface ModalProps {
  visible: boolean;
  closeModal: () => void;
  currentUser: any;
  refreshTable: () => void;
  // 不传为新增，传了为编辑
  recordInfo?: any;
}
//
const ModalContent: React.FC<ModalProps> = (props: ModalProps) => {
  //
  const { visible, closeModal, currentUser, refreshTable, recordInfo = {} } = props;
  //
  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const isGrayUser = initialState?.currentUser?.isGrayUser;
  //
  const [form] = Form.useForm();
  console.log('currentUser', currentUser);
  console.log('recordInfo', recordInfo);
  // 是否为编辑弹窗
  const isEditMode = useMemo(() => {
    return recordInfo?.authenticationId !== undefined && recordInfo?.authenticationId !== null;
  }, [recordInfo?.authenticationId]);

  // 判断是否为渠道用户，渠道用户禁止修改所属渠道，新增需要返显当前渠道且不可修改
  const isChannelUser = isCarInsuranceStoreUser(access);
  const currentChannelCode = currentUser?.channelCode;
  //
  useEffect(() => {
    // 新增返显当前渠道
    if (!isEditMode && isChannelUser && currentChannelCode) {
      form?.setFieldsValue({
        channelCodes: [currentChannelCode],
      });
    }
  }, [form, isChannelUser, isEditMode, currentChannelCode]);
  // 预审资料
  const [pretrialFileList, setPretrialFileList] = useState<any>([]);

  // 详情信息
  const [detailInfo, setDetailInfo] = useState<any>({});
  // 编辑模式下，状态步骤信息
  const [stepItems, setStepItems] = useState<any>([]);
  const formDisabled = useMemo(() => {
    // 新增模式下，不禁用表单
    if (!isEditMode) {
      return false;
    }
    // 编辑模式下非驳回状态下，禁用表单
    return detailInfo?.authStatus !== AUTH_STATUS.PRE_REVIEW_REJECTED;
  }, [detailInfo.authStatus, isEditMode]);

  // ocr相关
  const [idCardOcrLoading, setIdCardOcrLoading] = useState(false);
  const [bizLicenseOcrLoading, setBizLicenseOcrLoading] = useState(false);
  // 1-国徽面、2-人脸面
  const idCardOcrAction = async (photoType: '1' | '2', formRef: any, ossFilePath: string) => {
    if (!ossFilePath) {
      return;
    }
    setIdCardOcrLoading(true);
    const res: any = await idCardOcr({
      filePath: ossFilePath,
      photoType,
      productCode: '0303',
    }).catch(() => {});
    setIdCardOcrLoading(false);
    if (res?.ret === 0) {
      message.success('识别成功');
      formRef?.setFieldsValue({
        // 授权签约人姓名
        authorizerName: res?.data?.name,
        // 授权签约人身份证号
        authorizerCertiNo: res?.data?.idNo,
      });
    }
  };
  const bizLicenseOcrAction = async (formRef: any, ossFilePath: string) => {
    if (!ossFilePath) {
      return;
    }
    setBizLicenseOcrLoading(true);
    const res: any = await bizLicenseOcr({
      filePath: ossFilePath,
    }).catch(() => {});
    console.log('bizLicenseOcrAction', res);
    setBizLicenseOcrLoading(false);
    if (res?.ret === 0) {
      message.success('识别成功');
      // 新建ocr识别
      if (!isEditMode) {
        formRef?.setFieldsValue({
          // 统一社会信用代码
          certiNo: res?.data?.socialCreditCode,
          // 借款企业名称
          certiName: res?.data?.name,
          // 借款企业法人名称
          legalPerson: res?.data?.legalRepresentative,
          // 借款企业法人身份证号
          // legalPersonCertiNo: '',
          // 企业地址
          enterpriseAddress: res?.data?.address,
        });
      } else if (isEditMode) {
        // 编辑驳回状态下重新提交，ocr识别，不更新信用代码
        formRef?.setFieldsValue({
          // 统一社会信用代码
          // certiNo: res?.data?.socialCreditCode,
          // 借款企业名称
          certiName: res?.data?.name,
          // 借款企业法人名称
          legalPerson: res?.data?.legalRepresentative,
          // 借款企业法人身份证号
          // legalPersonCertiNo: '',
          // 企业地址
          enterpriseAddress: res?.data?.address,
        });
      }
    }
  };
  //
  // 渠道选项初始化
  const [channelCodesOptions, setChannelCodesOptions] = useState<any>([]);
  const [channelCodesOptionsLoading, setChannelCodesOptionsLoading] = useState(false);
  const initChannelCodesOptions = async () => {
    setChannelCodesOptionsLoading(true);
    const data =
      (await getChannelInfo({
        channelLevel: currentUser?.channelLevel,
        channelCode: currentUser?.channelCode,
      }).catch(() => {})) || [];
    setChannelCodesOptionsLoading(false);
    //
    const options = data?.map((item) => {
      const { channelName, channelCode } = item;
      return {
        label: channelName,
        value: channelCode,
        key: channelCode,
      };
    });
    setChannelCodesOptions(options);
  };

  // 详情初始化
  const [initLoading, setInitLoading] = useState(false);
  const init = async () => {
    setInitLoading(true);
    const { authenticationId } = recordInfo;
    console.log('recordInfo', recordInfo);
    //
    const res: any = (await getAuthDetail({ authenticationId }).catch(() => {})) || {};
    setInitLoading(false);
    console.log('getAuthDetail', res);
    if (res?.ret === 0 && res?.data) {
      setDetailInfo(res?.data);
      const data = res?.data || {};
      //
      const tempList =
        data?.pretrialFileList?.map((item: any) => {
          return {
            name: item?.fileName,
            uid: item?.filePath,
            status: 'done',
            response: {
              data: {
                netWorkPath: item?.netWorkPath,
                filePath: item?.filePath,
                fileName: item?.fileName,
              },
            },
          };
        }) || [];
      setPretrialFileList(tempList);
      //
      form.setFieldsValue({
        //
        certiNo: data?.certiNo, // 统一信用代码
        certiName: data?.certiName, // 公司名称
        legalPerson: data?.legalPerson, // 借款企业法人名称
        legalPersonCertiNo: data?.legalPersonCertiNo, // 法人证件号
        legalPersonPhone: data?.legalPersonPhone, // 法人电话
        enterpriseAddress: data?.enterpriseAddress, // 企业地址
        tradingCertificateFilePath: data?.businessLicenseFile?.filePath, // 企业营业执照原件oss相对路径
        //
        authorizerCertiNo: data?.authorizerCertiNo, // 授权人证件号码
        authorizerName: data?.authorizerName, // 授权人姓名
        authorizerTel: data?.authorizerTel, // 授权人电话
        authorizerEmail: data?.authorizerEmail, // 授权人邮箱
        frontFilePath: data?.frontFile?.filePath, // 身份证正面oss相对路径
        unFrontFilePath: data?.unFrontFile?.filePath, // 身份证反面oss相对路径
        //
        remark: data?.remark, // 备注
        channelCodes: data?.channelCodes, // 渠道编码
      });
      // 步骤信息
      const entityAuthLogList = data?.entityAuthLogList || [];
      setStepItems(() => {
        return entityAuthLogList?.map((item: any) => {
          let desc = <div className="gray-text">{item?.createdAt}</div>;
          if (item?.authStatus === AUTH_STATUS.PRE_REVIEW_REJECTED) {
            let extJson = {};
            try {
              extJson = JSON.parse(item?.extJson || '{}');
            } catch (e) {
              console.log('extJson parse error', e);
            }
            //
            desc = (
              <>
                <div className="gray-text">{item?.createdAt}</div>
                <div className="gray-text">{extJson?.rejectReason || '-'}</div>
              </>
            );
          }
          return {
            title: authStatusMap[item?.authStatus],
            description: desc,
          };
        });
      });
    } else {
      // message.error('获取详情失败');
      closeModal();
    }
  };
  useEffect(() => {
    // 渠道选项初始化
    initChannelCodesOptions().catch(() => {});
    // 编辑模式下回显
    if (visible && recordInfo?.authenticationId) {
      init().catch(() => {
        setInitLoading(false);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, recordInfo?.authenticationId]);

  // 驳回原因相关
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [rejectReasonForm] = Form.useForm();
  const [handleReviewLoading, setHandleReviewLoading] = useState(false);
  const closRejectModal = () => {
    try {
      rejectReasonForm?.resetFields();
    } catch (e) {
      console.log(e);
    }
    setRejectModalVisible(false);
  };
  // 驳回
  const handleReject = async () => {
    try {
      rejectReasonForm?.setFieldValue('rejectReason', '');
      rejectReasonForm?.resetFields();
    } catch (e) {
      console.log(e);
    }
    setRejectModalVisible(true);
  };
  const handleRejectAction = async () => {
    try {
      await rejectReasonForm?.validateFields();
    } catch {
      return Promise.reject();
    }
    //
    setHandleReviewLoading(true);
    const { authenticationId } = recordInfo;
    const data = {
      authenticationId,
      auditStatus: false,
      rejectReason: rejectReasonForm?.getFieldValue('rejectReason'),
    };
    const res = await authReview(data).catch(() => {});
    setHandleReviewLoading(false);
    console.log('handleApproveAction', res);
    if (res?.ret === 0) {
      message.success('操作成功');
      closRejectModal();
      closeModal();
      refreshTable();
    }
  };
  // 通过
  const handleApproveAction = async () => {
    setHandleReviewLoading(true);
    const { authenticationId } = recordInfo;
    const data = {
      authenticationId,
      auditStatus: true,
    };
    const res = await authReview(data).catch(() => {});
    setHandleReviewLoading(false);
    console.log('handleApproveAction', res);
    if (res?.ret === 0) {
      message.success('操作成功');
      closeModal();
      refreshTable();
    }
  };

  // 删除客户
  const [handleDeleteLoading, setHandleDeleteLoading] = useState(false);
  const handleDeleteAction = async () => {
    console.log('handleDeleteAction');
    setHandleDeleteLoading(true);
    const { authenticationId } = recordInfo;
    const res = await deleteAuthRecord(authenticationId).catch(() => {});
    setHandleDeleteLoading(false);
    if (res?.ret === 0) {
      message.success('操作成功');
      closeModal();
      refreshTable();
    }
  };

  // 修改授权人
  const [handleModifyAuthorizerLoading, setHandleModifyAuthorizerLoading] = useState(false);
  const [modifyAuthorizerModalVisible, setModifyAuthorizerModalVisible] = useState(false);
  const [modifyAuthorizerForm] = Form.useForm();
  const closeModifyAuthorizerModal = () => {
    try {
      modifyAuthorizerForm?.resetFields();
    } catch (e) {
      console.log(e);
    }
    setModifyAuthorizerModalVisible(false);
  };
  const handleModifyAuthorizer = () => {
    modifyAuthorizerForm?.setFieldsValue({
      //
      authorizerCertiNo: detailInfo?.authorizerCertiNo, // 授权人证件号码
      authorizerName: detailInfo?.authorizerName, // 授权人姓名
      authorizerTel: detailInfo?.authorizerTel, // 授权人电话
      authorizerEmail: detailInfo?.authorizerEmail, // 授权人邮箱
      // frontFilePath: detailInfo?.frontFile?.filePath, // 身份证正面oss相对路径
      // unFrontFilePath: detailInfo?.unFrontFile?.filePath, // 身份证反面oss相对路径
    });
    setModifyAuthorizerModalVisible(true);
  };
  const handleModifyAuthorizerAction = async () => {
    if (idCardOcrLoading) {
      message.error('请等待识别完成');
      return;
    }
    try {
      await modifyAuthorizerForm?.validateFields();
    } catch {
      return Promise.reject();
    }
    const formValues = modifyAuthorizerForm?.getFieldsValue() || {};
    setHandleModifyAuthorizerLoading(true);
    const res = await updateAuthorizer({
      authorizerCertiNo: formValues?.authorizerCertiNo, // 授权人证件号码
      authorizerName: formValues?.authorizerName, // 授权人姓名
      authorizerTel: formValues?.authorizerTel, // 授权人电话
      authorizerEmail: formValues?.authorizerEmail, // 授权人邮箱
      //
      frontFilePath: formValues?.frontFilePath, // 身份证正面oss相对路径
      unFrontFilePath: formValues?.unFrontFilePath, // 身份证反面oss相对路径
      //
      epAuthNo: detailInfo?.epAuthNo,
    } as any).catch(() => {});
    setHandleModifyAuthorizerLoading(false);
    if (res?.ret === 0) {
      message.success('操作成功');
      closeModifyAuthorizerModal();
      closeModal();
      refreshTable();
    }
  };

  // 修改渠道
  const [handleModifyChannelLoading, setHandleModifyChannelLoading] = useState(false);
  const [modifyChannelModalVisible, setModifyChannelModalVisible] = useState(false);
  const [modifyChannelForm] = Form.useForm();
  const closeModifyChannelModal = () => {
    try {
      modifyChannelForm?.resetFields();
    } catch (e) {
      console.log(e);
    }
    setModifyChannelModalVisible(false);
  };
  const handleModifyChannel = () => {
    modifyChannelForm?.setFieldsValue({
      channelCodes: detailInfo?.channelCodes,
    });
    setModifyChannelModalVisible(true);
  };
  const handleModifyChannelAction = async () => {
    try {
      await modifyChannelForm?.validateFields();
    } catch {
      return Promise.reject();
    }
    setHandleModifyChannelLoading(true);
    const formValues = modifyChannelForm?.getFieldsValue() || {};
    const res = await updateChannelCode({
      channelCodes: formValues?.channelCodes,
      enterpriseId: detailInfo?.enterpriseId,
    });
    setHandleModifyChannelLoading(false);
    if (res?.ret === 0) {
      message.success('操作成功');
      closeModifyChannelModal();
      closeModal();
      refreshTable();
    }
  };

  // 提交、重新提交表单
  const [submitLoading, setSubmitLoading] = useState(false);
  const handleSubmit = async () => {
    // 检查上传状态，上传中、ocr中，阻止提交
    if (pretrialFileList?.some((item) => item?.status === 'uploading')) {
      message.error('请等待上传完成');
      return;
    }
    //
    if (idCardOcrLoading || bizLicenseOcrLoading) {
      message.error('请等待识别完成');
      return;
    }
    //
    try {
      await form?.validateFields();
    } catch {
      return Promise.reject();
    }
    //
    const formValues = form.getFieldsValue();
    const {
      certiNo,
      certiName,
      legalPerson,
      legalPersonCertiNo,
      legalPersonPhone,
      enterpriseAddress,
      tradingCertificateFilePath,
      authorizerCertiNo,
      authorizerName,
      authorizerTel,
      authorizerEmail,
      frontFilePath,
      unFrontFilePath,
      //
      remark,
      channelCodes,
    } = formValues;
    //
    const pretrialFileListValue =
      pretrialFileList?.map((item: any) => {
        return {
          fileName: item?.response?.data?.fileName,
          filePath: item?.response?.data?.filePath,
          netWorkPath: item?.response?.data?.netWorkPath,
        };
      }) || [];
    //
    const params: any = {
      epAuthInfo: {
        certiNo, // 统一信用代码
        certiName, // 公司名称
        legalPerson, // 借款企业法人名称
        legalPersonCertiNo, // 法人证件号
        enterpriseAddress, // 企业地址
        tradingCertificateFilePath, // 企业营业执照原件oss相对路径
        legalPersonPhone,
      },
      epAuthorizerAuthInfo: {
        authorizerCertiNo, // 授权人证件号码
        authorizerName, // 授权人姓名
        authorizerTel, // 授权人电话
        authorizerEmail, // 授权人邮箱
        frontFilePath, // 身份证正面oss相对路径
        unFrontFilePath, // 身份证反面oss相对路径
      },
      pretrialFileList: pretrialFileListValue,
      remark,
      channelCodes,
    };
    if (isEditMode) {
      // 编辑时，传id
      params.authenticationId = recordInfo?.authenticationId;
    }
    setSubmitLoading(true);
    ajaxAdd(params as any)
      .then(() => {
        message.success('操作成功');
        closeModal();
        refreshTable();
      })
      .catch((err) => {
        if (err?.ret === 8002) {
          Modal.error({
            content: (
              <div>
                三要素校验失败,请核实
                <Tag color="error">授权签约人姓名</Tag>
                <Tag color="error">授权签约人身份证号</Tag>
                <Tag color="error">授权签约人联系电话</Tag>信息是否准确
              </div>
            ),
          });
        }
        if (err?.ret === 8003) {
          Modal.error({
            content: (
              <div>
                四要素校验失败,请核实
                <Tag color="error">统一社会信用代码</Tag>
                <Tag color="error">借款企业名称</Tag>
                <Tag color="error">借款企业法人名称</Tag>
                <Tag color="error">借款企业法人身份证号</Tag>
                信息是否准确
              </div>
            ),
          });
        }
      })
      .finally(() => {
        setSubmitLoading(false);
      });
  };

  // 图片预览相关
  const previewRef = useRef<ImagePreviewInstance>();
  const handleUploadPreview = async (file: any, urlList: string[]) => {
    console.log('urlList', urlList);
    console.log('file', file);
    if (file?.filePath) {
      previewRef.current?.previewFile({
        url: file?.filePath,
        fileName: file?.fileName,
        urlList,
      });
    }
  };

  // modal ref
  const claimRechargeRef = useRef<any>(null);
  const transactionRef = useRef<any>(null);
  const claimRecordRef = useRef<any>(null);

  const walletActions = {
    showTransaction: () => {
      transactionRef.current.show();
    },
    showClaimRecharge: () => {
      claimRechargeRef.current.show();
    },
    showClaimRecord: () => {
      claimRecordRef.current.show();
    },
  };

  //
  return (
    <>
      <Modal
        width={1060}
        destroyOnClose
        title={isEditMode ? `车险客户客户详情` : '新增车险客户'}
        open={visible}
        onCancel={() => {
          closeModal();
        }}
        footer={
          <>
            {/* <Button
              type="primary"
              onClick={() => {
                console.log('form.getFieldsValue()', form.getFieldsValue());
                console.log('pretrialFileList', pretrialFileList);
              }}
            >
              表单值
            </Button> */}
            {/* 关闭 */}
            <Button
              onClick={() => {
                closeModal();
              }}
            >
              关闭
            </Button>
            {/* 新增 */}
            {!isEditMode && (
              <>
                <Button type="primary" loading={submitLoading} onClick={handleSubmit}>
                  提交
                </Button>
              </>
            )}
            {/* 编辑 */}
            {isEditMode && (
              <>
                {/* 待预审 */}
                {detailInfo?.authStatus === AUTH_STATUS.PENDING_PRE_REVIEW && (
                  <>
                    {access?.hasAccess('pass_detail_verify_manager_enterpriseMng') && (
                      <Button
                        type="primary"
                        loading={handleReviewLoading}
                        onClick={handleApproveAction}
                      >
                        通过
                      </Button>
                    )}
                    {access?.hasAccess('reject_detail_verify_manager_enterpriseMng') && (
                      <Button
                        type="primary"
                        loading={handleReviewLoading}
                        onClick={handleReject}
                        danger
                      >
                        驳回
                      </Button>
                    )}
                  </>
                )}
                {/* 预审驳回 */}
                {detailInfo?.authStatus === AUTH_STATUS.PRE_REVIEW_REJECTED && (
                  <>
                    {access?.hasAccess('resubmit_detail_verify_manager_enterpriseMng') && (
                      <Button type="primary" loading={submitLoading} onClick={handleSubmit}>
                        重新提交
                      </Button>
                    )}
                    {access?.hasAccess('delete_detail_verify_manager_enterpriseMng') && (
                      <Popconfirm
                        title="删除客户"
                        description="确定删除该客户？"
                        onConfirm={handleDeleteAction}
                        okText="确定"
                        okButtonProps={{ loading: handleDeleteLoading }}
                        cancelText="取消"
                      >
                        <Button type="primary" loading={handleDeleteLoading} danger>
                          删除客户
                        </Button>
                      </Popconfirm>
                    )}
                  </>
                )}
                {[AUTH_STATUS.NOT_CERTIFIED, AUTH_STATUS.WAITING_AUTHORIZATION].includes(
                  detailInfo?.authStatus,
                ) && (
                  <>
                    {access?.hasAccess('auth_detail_verify_manager_enterpriseMng') && (
                      <AuthQrCodeBtn
                        epAuthNo={detailInfo?.epAuthNo}
                        certiName={detailInfo?.certiName}
                      >
                        <span>认证二维码</span>
                      </AuthQrCodeBtn>
                    )}
                    {access?.hasAccess('delete_detail_verify_manager_enterpriseMng') && (
                      <Popconfirm
                        title="删除客户"
                        description="确定删除该客户？"
                        onConfirm={handleDeleteAction}
                        okText="确定"
                        okButtonProps={{ loading: handleDeleteLoading }}
                        cancelText="取消"
                      >
                        <Button type="primary" loading={handleDeleteLoading} danger>
                          删除客户
                        </Button>
                      </Popconfirm>
                    )}
                  </>
                )}
                {/* 已授权 */}
                {detailInfo?.authStatus === AUTH_STATUS.AUTHORIZED && (
                  <>
                    {/* <Button loading={false} onClick={() => {}}>
                      修改登录密码
                    </Button> */}
                    {access?.hasAccess('modify_detail_verify_manager_enterpriseMng') && (
                      <Button type="primary" onClick={handleModifyAuthorizer}>
                        修改授权人
                      </Button>
                    )}
                    {/* 已授权状态下（修改渠道）  */}
                    {access?.hasAccess('modify_channel_detail_verify_manager_enterpriseMng') && (
                      <Button type="primary" onClick={handleModifyChannel}>
                        修改渠道
                      </Button>
                    )}
                  </>
                )}
              </>
            )}
          </>
        }
      >
        <Spin spinning={initLoading}>
          {/* 编辑模式下，状态步骤 */}
          {isEditMode && (
            <>
              <Row className="mt-15">
                <Col span={24}>
                  <Steps
                    items={stepItems}
                    size="small"
                    labelPlacement="vertical"
                    current={stepItems?.length}
                    style={{ justifyContent: 'start', 'overflow-x': 'auto' }}
                  />
                </Col>
              </Row>
            </>
          )}
          <Divider className="row-divider" />
          {/* 表单 */}
          <Form form={form} labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} labelAlign="left">
            {/* 企业营业执照 */}
            <Row className="title-row">
              <Col span={3}>
                <span className="red-text"> * </span>企业营业执照：
              </Col>
              <Col span={12} className="primary-text">
                需原件拍照， 四角齐全，图片小于5M，格式为 pdf, png, jpg, jpeg
              </Col>
            </Row>
            <Row>
              {/* 营业执照upload */}
              <Col span={12}>
                <Form.Item
                  label=""
                  name="tradingCertificateFilePath"
                  rules={[{ required: true, message: '必填项' }]}
                  // labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <UploadImagePannel
                    disabled={formDisabled || bizLicenseOcrLoading}
                    defaultFile={detailInfo?.businessLicenseFile?.netWorkPath}
                    onChange={(filePath) => bizLicenseOcrAction(form, filePath)}
                  />
                </Form.Item>
              </Col>
              {/* 营业执照表单 */}
              <Col span={11}>
                <Form.Item
                  label="统一社会信用代码"
                  name="certiNo"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input
                    disabled={
                      formDisabled || detailInfo?.authStatus === AUTH_STATUS.PRE_REVIEW_REJECTED
                    }
                  />
                </Form.Item>
                <Form.Item
                  label="借款企业名称"
                  name="certiName"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
                <Form.Item
                  label="借款企业法人名称"
                  name="legalPerson"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
                <Form.Item
                  label="借款企业法人身份证号"
                  name="legalPersonCertiNo"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
                <Form.Item
                  label="借款企业法人联系电话"
                  name="legalPersonPhone"
                  rules={[
                    { required: true, message: '必填项' },
                    {
                      validator: (_, value) => {
                        if (value && (value?.length !== 11 || !/^[0-9]*$/.test(value))) {
                          return Promise.reject(new Error('请输入11位数字'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
                <Form.Item
                  label="借款企业地址"
                  name="enterpriseAddress"
                  rules={[
                    { required: true, message: '必填项' },
                    { max: 30, message: '最长30字' },
                  ]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
              </Col>
            </Row>
            {/* 授权人身份证 */}
            <Row className="title-row">
              <Col span={3}>
                <span className="red-text"> * </span>授权人身份证：
              </Col>
              <Col span={12} className="primary-text">
                需原件拍照， 四角齐全，图片小于5M，格式为 pdf, png, jpg, jpeg
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item
                  label="授权人身份证人像面"
                  name="frontFilePath"
                  rules={[{ required: true, message: '必填项' }]}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                >
                  <UploadImagePannel
                    disabled={formDisabled || idCardOcrLoading}
                    defaultFile={detailInfo?.frontFile?.netWorkPath}
                    onChange={(filePath) => idCardOcrAction('2', form, filePath)}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="授权人身份证国徽面"
                  name="unFrontFilePath"
                  rules={[{ required: true, message: '必填项' }]}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                >
                  <UploadImagePannel
                    disabled={formDisabled}
                    defaultFile={detailInfo?.unFrontFile?.netWorkPath}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人姓名"
                  name="authorizerName"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人联系电话"
                  name="authorizerTel"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人身份证号"
                  name="authorizerCertiNo"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人联系邮箱"
                  name="authorizerEmail"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input disabled={formDisabled} />
                </Form.Item>
              </Col>
            </Row>
            <Divider className="row-divider" />
            {/* 备注 */}
            <Row>
              <Col span={24}>
                <Form.Item
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 16 }}
                  label="备注信息"
                  name="remark"
                  // 最长200字
                  rules={[
                    // { required: true, message: '必填项' },
                    { max: 200, message: '最长200字' },
                  ]}
                >
                  <Input.TextArea disabled={formDisabled} />
                </Form.Item>
              </Col>
            </Row>
            {/* 预审资料 */}
            <Row className="mb-10">
              <Col span={3}>
                {/* <span className="red-text"> * </span> */}
                预审资料：
              </Col>
              <Col span={21} className="primary-text">
                支持格式：.doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar，单个文件最大50M
              </Col>
            </Row>
            {/* 不允许上传 */}
            {formDisabled && (
              <Row className="title-row">
                <Col span={3} />
                <Col span={21}>
                  <div className="flex-wrap">
                    {/* 不允许 */}
                    <div className="img-list">
                      {detailInfo?.pretrialFileList?.map((file: any) => {
                        return (
                          <div key={file?.filePath} className="img-item">
                            <span className="list-wrap">
                              <span
                                className="list-icon"
                                onClick={() =>
                                  handleUploadPreview(
                                    {
                                      fileName: file?.fileName,
                                      filePath: file?.netWorkPath,
                                    },
                                    detailInfo?.pretrialFileList?.map(
                                      (item: any) => item?.netWorkPath,
                                    ),
                                  )
                                }
                              >
                                <LinkOutlined />
                              </span>
                              <span className="list-ellipsis-text">
                                <span
                                  onClick={() =>
                                    handleUploadPreview(
                                      {
                                        fileName: file?.fileName,
                                        filePath: file?.netWorkPath,
                                      },
                                      detailInfo?.pretrialFileList?.map(
                                        (item: any) => item?.netWorkPath,
                                      ),
                                    )
                                  }
                                >
                                  {file?.fileName}
                                </span>
                              </span>
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </Col>
              </Row>
            )}
            {/* 上传 */}
            {!formDisabled && (
              <Row className="mb-20">
                <Col span={3} />
                <Col span={15}>
                  {/* 非渠道用户 */}
                  <div className="upload-pannel-wrapper">
                    <Upload.Dragger
                      name="file"
                      className="upload-area-inline"
                      fileList={pretrialFileList}
                      multiple={true}
                      disabled={formDisabled}
                      accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
                      action={getBizadminUploadAction()}
                      headers={{ ...getAuthHeaders(), 'hll-appid': 'bme-finance-bizadmin-svc' }}
                      data={{ acl: 'PRIVATE_ACL', destPath: 'CAR_POLICY_ACCOUNT_PRETRIAL' }}
                      // data={{ acl: 'PRIVATE_ACL', destPath: 'CAR_POLICY_CANCELLATION' }}
                      beforeUpload={(file: any) => {
                        // 限制大小：50m
                        const sizeValid = file.size / 1024 / 1024 <= 50;
                        // 超过限制
                        if (!sizeValid) {
                          message.error('文件大小不能超过50M');
                        }
                        return sizeValid || Upload.LIST_IGNORE;
                      }}
                      onPreview={(file: any) => {
                        console.log('onPreview1', file);
                        console.log('onPreview2', pretrialFileList);
                        //
                        const urlList =
                          pretrialFileList?.map(
                            (item: any) => item?.response?.data?.netWorkPath || item?.filePath,
                          ) || [];
                        return handleUploadPreview(
                          {
                            fileName: file?.name || file?.fileName,
                            filePath: file?.response?.data?.netWorkPath || file?.filePath,
                            fileRelativePath:
                              file?.response?.data?.filePath || file?.fileRelativePath,
                            ...file,
                          },
                          urlList,
                        );
                      }}
                      onChange={({ fileList }) => {
                        console.log('onChange1', fileList);
                        setPretrialFileList([...fileList]);
                      }}
                    >
                      <p className="ant-upload-text primary-color">点击或拖动文件到此处上传</p>
                    </Upload.Dragger>
                  </div>
                </Col>
              </Row>
            )}
            {/* 所属渠道 */}
            <Row>
              <Col span={24}>
                <Form.Item
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 12 }}
                  label={
                    <>
                      所属渠道&nbsp;
                      <Tooltip
                        placement="topLeft"
                        title={'系统会默认将该企业与二级渠道的父渠道关联'}
                      >
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </>
                  }
                  name="channelCodes"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Select
                    mode="multiple"
                    // 渠道用户在新增、编辑任何场景下，不允许修改所属渠道
                    disabled={formDisabled || channelCodesOptionsLoading || isChannelUser}
                    loading={channelCodesOptionsLoading}
                    filterOption={(inputValue, option: any) => {
                      return (
                        option?.label?.includes(inputValue) || option?.value?.includes(inputValue)
                      );
                    }}
                    options={channelCodesOptions}
                  />
                </Form.Item>
              </Col>
            </Row>
            {/* 修改授权人弹窗*/}
            {/* 修改密码弹窗 */}
          </Form>
          {/* 已授权状态，展示专属还款账号 */}
          {isEditMode && detailInfo?.authStatus === AUTH_STATUS.AUTHORIZED && isGrayUser && (
            <>
              <Row>
                <Col className="mb-15" span={3}>
                  专属账号：
                </Col>
                <Col span={21}>{recordInfo?.epAuthInfo?.extMappingAccountBO?.bankNo}</Col>
                <Col className="mb-15" span={3}>
                  专属账号账户名：
                </Col>
                <Col span={21}>{recordInfo?.epAuthInfo?.extMappingAccountBO?.accountName}</Col>
                <Col className="mb-15" span={3}>
                  专属账号银行名：
                </Col>
                <Col span={21}>{recordInfo?.epAuthInfo?.extMappingAccountBO?.bankName}</Col>
                <Col className="mb-15" span={3}>
                  代偿渠道：
                </Col>
                <Col span={21}>
                  {recordInfo?.epAuthInfo?.extMappingAccountBO?.reimbursementEntityTerList ||
                    '暂无'}
                </Col>
                <Col className="mb-15" span={3}>
                  钱包余额：
                </Col>
                <Col span={21}>
                  <WalletActions
                    actions={walletActions}
                    externalOwnerId={recordInfo?.epAuthInfo?.extMappingAccountBO?.externalOwnerId}
                    secondProductCode="0303"
                  />
                </Col>
              </Row>
            </>
          )}
        </Spin>
      </Modal>
      {/* 驳回原因 */}
      {rejectModalVisible && (
        <Modal
          open={rejectModalVisible}
          title="驳回原因"
          onCancel={() => {
            closRejectModal();
          }}
          footer={
            <Button type="primary" onClick={handleRejectAction}>
              提交
            </Button>
          }
        >
          <Form form={rejectReasonForm} labelCol={{ span: 5 }} wrapperCol={{ span: 16 }}>
            <Form.Item
              label="驳回原因"
              name="rejectReason"
              rules={[
                { required: true, message: '必填项' },
                { max: 200, message: '最长200字' },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
          </Form>
        </Modal>
      )}
      {/* 修改授权人弹窗 */}
      {modifyAuthorizerModalVisible && (
        <Modal
          open={modifyAuthorizerModalVisible}
          width={1060}
          destroyOnClose
          title="修改授权人"
          onCancel={() => {
            closeModifyAuthorizerModal();
          }}
          footer={
            <>
              <Button
                onClick={() => {
                  closeModifyAuthorizerModal();
                }}
              >
                关闭
              </Button>
              <Button
                type="primary"
                loading={handleModifyAuthorizerLoading}
                onClick={handleModifyAuthorizerAction}
              >
                提交
              </Button>
            </>
          }
        >
          <Form form={modifyAuthorizerForm} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
            {/* 授权人身份证 */}
            <Row className="title-row">
              <Col span={3}>
                <span className="red-text"> * </span>授权人身份证：
              </Col>
              <Col span={12} className="primary-text">
                需原件拍照， 四角齐全，图片小于5M，格式为 pdf, png, jpg, jpeg
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <Form.Item
                  label="授权人身份证人像面"
                  name="frontFilePath"
                  rules={[{ required: true, message: '必填项' }]}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                >
                  <UploadImagePannel
                    disabled={idCardOcrLoading}
                    onChange={(filePath) => idCardOcrAction('2', modifyAuthorizerForm, filePath)}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="授权人身份证国徽面"
                  name="unFrontFilePath"
                  rules={[{ required: true, message: '必填项' }]}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                >
                  <UploadImagePannel />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人姓名"
                  name="authorizerName"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人联系电话"
                  name="authorizerTel"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人身份证号"
                  name="authorizerCertiNo"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 15 }}
                  label="授权签约人联系邮箱"
                  name="authorizerEmail"
                  rules={[{ required: true, message: '必填项' }]}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
      )}
      {/* 修改渠道弹窗 */}
      {modifyChannelModalVisible && (
        <Modal
          open={modifyChannelModalVisible}
          title="修改所属渠道"
          width={860}
          destroyOnClose
          onCancel={() => {
            closeModifyChannelModal();
          }}
          footer={
            <>
              <Button
                onClick={() => {
                  closeModifyChannelModal();
                }}
              >
                关闭
              </Button>
              <Button
                type="primary"
                loading={handleModifyChannelLoading}
                onClick={handleModifyChannelAction}
              >
                提交
              </Button>
            </>
          }
        >
          <Form form={modifyChannelForm} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
            <Form.Item
              label={
                <>
                  所属渠道&nbsp;
                  <Tooltip placement="top" title={'系统会默认将该企业与二级渠道的父渠道关联'}>
                    <QuestionCircleOutlined />
                  </Tooltip>
                </>
              }
              name="channelCodes"
              rules={[{ required: true, message: '必填项' }]}
            >
              <Select
                mode="multiple"
                disabled={channelCodesOptionsLoading}
                loading={channelCodesOptionsLoading}
                filterOption={(inputValue, option: any) => {
                  return option?.label?.includes(inputValue) || option?.value?.includes(inputValue);
                }}
                options={channelCodesOptions}
              />
            </Form.Item>
          </Form>
        </Modal>
      )}
      {/* 图片预览 */}
      <ImagePreview ref={previewRef as any} />
      {/* 充值 */}
      <ClaimRechargeModal
        targetRemitAccountName={recordInfo?.epAuthInfo?.certiName}
        externalOwnerId={recordInfo?.epAuthInfo?.extMappingAccountBO?.externalOwnerId}
        accountName={recordInfo?.epAuthInfo?.extMappingAccountBO?.accountName}
        claimRechargeRef={claimRechargeRef}
        productSecondCode="0303" // 车险
      />
      {/* 认领记录 */}
      <ClaimRecordModal
        externalOwnerId={recordInfo?.epAuthInfo?.extMappingAccountBO?.externalOwnerId}
        claimRecordRef={claimRecordRef}
      />
      {/* 流水明细 */}
      <TransactionDetailModal
        externalOwnerId={recordInfo?.epAuthInfo?.extMappingAccountBO?.externalOwnerId}
        transactionRef={transactionRef}
        secondProductCode="0303"
      />
    </>
  );
};

export default ModalContent;
