/*
 * @Author: your name
 * @Date: 2020-11-24 11:10:13
 * @LastEditTime: 2022-06-20 15:00:30
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/BusinessMng/components/ LoanInfo.ts
 */

import React from 'react';
import { Card, Row, Col } from 'antd';
import globalStyle from '@/global.less';

interface LoanInfoProps {
  dataInfo: object;
}
const LoanInfo: React.FC<LoanInfoProps> = (props) => {
  const data = props.dataInfo;
  const loanInfoMap = {
    lendingNo: '放款流水号',
    type: '放款类型',
    amount: '放款金额',
    lendingMaster: '放款主体',
    receiptMaster: '收款主体',
    re: '放款周期',
    lendingModel: '放款方式',
    fundFlow: '是否发生资金流',
    lendingTime: '放款时间',
    status: '放款状态',
  };
  const itemMap = {
    type: {
      1: '进件',
      2: '债转',
      3: '代偿',
    },
    fundFlow: {
      false: '否',
      true: '是',
    },
    status: {
      0: '放款失败',
      1: '待放款',
      2: '放款成功',
      '-1': '已取消',
    },
    lendingModel: {
      1: '线上',
      2: '线下',
    },
  };
  return (
    <Card title="放款信息" className={globalStyle.mt30}>
      <Row>
        {Object.keys(loanInfoMap).map((item) => {
          const value = (data && (itemMap[item] ? itemMap[item][data[item]] : data[item])) || '';
          return (
            <Col span={8} key={item}>
              <div style={{ lineHeight: '40px' }}>
                <span>{loanInfoMap[item]}:</span>
                <span className={globalStyle.ml20}>{value}</span>
              </div>
            </Col>
          );
        })}
      </Row>
    </Card>
  );
};

export default LoanInfo;
