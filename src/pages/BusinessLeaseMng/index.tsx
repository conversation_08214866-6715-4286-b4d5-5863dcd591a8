/* eslint-disable react-hooks/exhaustive-deps */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { pathList } from '@/components/HeaderTab/getTabsList';
import HeaderTab from '@/components/HeaderTab/index';
import { QrViewer } from '@/components/QrViewer';
import {
  CHANNEL_TYPES_MAP,
  leaseStatusMap,
  LEASE_LOAN_FILE_TYPE,
  LICENSE_TYPES_MAP,
  NOTARIZATION_STATUS_MAP,
  PRODUCT_CLASSIFICATION_CODE,
  REPURCHASE_STATUS_MAP,
  SECONDARY_CLASSIFICATION_CODE,
} from '@/enums';
import { getAllChannelNameEnum, getProductNameEnum } from '@/services/enum';
import { filterProps, optionsMap, removeBlankFromObject } from '@/utils/tools';
import { disableFutureDate, isChannelStoreUser } from '@/utils/utils';
import type { ActionType, Key, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout'; //  pro-components包的PageContainer ui有些问题，固仍使用pro-layout
import { history, useAccess, useLocation, useNavigate, useSearchParams } from '@umijs/max';
import { Button, Col, message, Row } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { KeepAlive, useActivate, useAliveController } from 'react-activation';
import { getStoreAndApplyCityAll } from '../AfterLoan/services';
import { BatchPushToRisk } from './components/AutomaticUndoCon';
import CallDeliverySendMsgModal from './components/CallDeliverySendMsgModal';
import ErrorDataTableModal from './components/ErrorDataTableModal';
import ThreeModal from './components/ThreeModal';
import {
  APPLY_SOURCE,
  CAN_EDIT_STATUS,
  CAN_LOOK_INCOME_STATUS,
  CAR_DELIVERY_STATUS,
  channelStatusMap,
  ORDER_STATUS,
  statusLeaseMap,
} from './consts';
import type { OrderListItem } from './data';
import './index.less';
import {
  exportNotraizationList,
  getNotraizationList,
  orderExport,
  queryOrder,
  queryOrderDetail,
} from './service';

// 渠道门店帐号限制渠道
export const channelSearchFilter = ({ access, formRef }: any) => {
  if (isChannelStoreUser(access) && access?.currentUser?.channelCode) {
    formRef?.current?.setFieldValue('channelIds', access.currentUser.channelCode);
  }
};

// 渠道门店帐号限制门店
export const storeSearchFilter = ({ access, formRef }: any) => {
  if (isChannelStoreUser(access) && access.currentUser?.extSource?.storeId) {
    formRef.current?.setFieldValue('storeIds', access?.currentUser?.extSource?.storeId?.toString());
  }
};

// 渠道门店帐号限制渠道类型
export const channelTypeFilter = ({ access, formRef }: any) => {
  if (isChannelStoreUser(access) && access.currentUser?.channelType) {
    formRef.current?.setFieldValue('channelTypes', access.currentUser.channelType.toString());
  }
};

// 渠道门店帐号请求参数初始化
export const channelStoreRequestInit = ({ access, formRef, params }: any) => {
  if (isChannelStoreUser(access)) {
    // 渠道类型
    if (access.currentUser.channelType) {
      params.channelTypes = access.currentUser.channelType;
    }
    // 渠道
    if (access.currentUser?.channelCode) params.channelIds = access.currentUser.channelCode;
    // 门店
    if (access.currentUser?.extSource?.storeId)
      params.storeIds = access.currentUser.extSource.storeId;
    channelTypeFilter({ access, formRef });
  }
  return params;
};

const OrderList: React.FC<any> = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const preCheckRef = useRef<any>();
  // const [exportLoading, setExportLoading] = useState(false);
  const [signQrVisible, setSignQrVisible] = useState(false);
  const [signQrData, setSignQrData] = useState();
  const [showErrorDataTable, setShowErrorDataTable] = useState(false);

  const access = useAccess();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const nav = useNavigate();

  // 初始化值，分组件加载和被激活useActivate两种情况
  const initParamsData = (type: string) => {
    // @ts-ignore
    const userName = location.state?.userName; // @ts-ignore
    const orderNo = location.state?.orderNo || searchParams.get('orderNo'); // @ts-ignore
    const userNo = searchParams.get('userNo'); // @ts-ignore
    const orderStatus = location.state?.orderStatus; // @ts-ignore
    const loanStatus = location.state?.loanStatus; // @ts-ignore
    const carDeliveryStatus = location.state?.carDeliveryStatus; // @ts-ignore
    const startTime = location.state?.startTime; // @ts-ignore
    const endTime = location.state?.endTime; // @ts-ignore
    const hasCreateGpsOrder = location.state?.hasCreateGpsOrder; // @ts-ignore
    const fromHeaderTabs = location.state?.fromHeaderTabs; // @ts-ignore

    console.log('fromHeaderTabs', fromHeaderTabs);
    if (fromHeaderTabs) return; //  headerTabs点击过来的链接不做额外处理，沿用缓存
    if (userName || orderNo || orderStatus || loanStatus || userNo || carDeliveryStatus) {
      formRef?.current?.resetFields();
      const fieldValues: any = {};
      if (userName) fieldValues.userName = userName || '';
      if (orderNo) fieldValues.orderNo = orderNo || '';
      if (userNo) fieldValues.userNo = userNo || '';
      if (orderStatus) fieldValues.orderStatus = orderStatus || '';
      if (loanStatus) fieldValues.loanStatus = loanStatus || '';
      if (carDeliveryStatus) fieldValues.carDeliveryStatus = carDeliveryStatus || '';
      if (startTime && endTime) {
        fieldValues.createdAt = [dayjs(startTime), dayjs(endTime)];
      }
      if (hasCreateGpsOrder) {
        fieldValues.hasCreateGpsOrder = hasCreateGpsOrder;
      }
      console.log(type, fieldValues);
      formRef?.current?.setFieldsValue(fieldValues);
      setTimeout(() => {
        formRef?.current?.submit();
        // actionRef?.current?.reload();
        // history.location.state = null;
        window.history.replaceState(null, '', window.location.href);
      }, 500);
    }
  };

  useActivate(() => {
    initParamsData('useActivate:');
  });

  useLayoutEffect(() => {
    initParamsData('useLayoutEffect:');
  }, []);

  const getStoreAndApplyCityAllMemo = useMemo(async () => {
    //  缓存一个Promise，避免多次请求
    return await getStoreAndApplyCityAll();
  }, []);
  const getAllChannelNameEnumMemo = useMemo(async () => {
    return await getAllChannelNameEnum();
  }, []);

  const [curOrderStatus, setCurOrderStatus] = useState<any>();
  const [curOrderNo, setCurOrderNo] = useState('');
  const [showThreeModal, setShowThreeModal] = useState(false); // 三要素弹窗
  // 点击编辑按钮逻辑
  const handleEdit = (record: OrderListItem) => {
    // TODO: 判断当前订单是否已经完成签约，
    // 订单已注册状态，则弹出三要素弹窗，否则直接跳去进件编辑页面
    const { orderStatus, orderNo } = record;
    if (orderStatus === ORDER_STATUS.REGISTRY) {
      setCurOrderNo(orderNo);
      setCurOrderStatus(orderStatus);
      setShowThreeModal(true);
    } else {
      // 判断一下打开了几个编辑tab，超过5个时给出提示
      // console.log('pathlist', pathList);
      const editTab = pathList.filter((item) => item.path === '/businessMng/lease-come');
      if (editTab?.length >= 5) {
        message.warning('最多打开5个编辑页面');
      } else {
        nav(`/businessMng/lease-come?orderNo=${orderNo}&userNo=${record?.userNo}&openNew=true`);
      }
    }
  };
  // 点击三要素弹窗下一步
  const handleNextStep = (userNo: string) => {
    setShowThreeModal(false);
    setCurOrderNo('');
    setCurOrderStatus('');
    nav(`/businessMng/lease-come?orderNo=${curOrderNo}&userNo=${userNo}&openNew=true`);
  };

  const columns: ProColumns<OrderListItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      render: (_, row) => {
        return (
          <>
            {row.sendMsgStatus === 1 && <span className="green-radio" />}
            {row.orderNo}
          </>
        );
      },
    },
    {
      title: '用户ID',
      dataIndex: 'userNo',
      render: (_, row) => {
        // 门店渠道帐号不跳转个人进件详情页面
        return (
          <>
            {isChannelStoreUser(access) ? (
              row?.userNo
            ) : (
              <a
                onClick={() => {
                  history.push(`/userMng/personalMng/detail?orderNo=${row?.activateOrderNo}`);
                }}
              >
                {row?.userNo}
              </a>
            )}
          </>
        );
      },
      // search: false,
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
      // initialValue: userName,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      valueType: 'select',
      request: () => getProductNameEnum(PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE),
      hideInTable: true,
      // initialValue: '020101',
      search: !isChannelStoreUser(access) as any, //  渠道门店帐号不需展示产品名称筛选
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '期限',
      dataIndex: 'loanTerm',
      search: false,
    },
    {
      title: '申请金额',
      dataIndex: 'applyAmount',
      search: false,
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      valueEnum: statusLeaseMap,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
      render: (_, record) => {
        if (record.orderStatus === 14) {
          //  预审驳回是用红色字体
          return <span style={{ color: 'red' }}>{statusLeaseMap[record.orderStatus]}</span>;
        }
        if (record.orderStatus === 51 && record.applySource === APPLY_SOURCE.CHANNEL) {
          // 车辆绑定成功状态
          return (
            <>
              {statusLeaseMap[record.orderStatus]}
              <div>
                <a
                  onClick={() => {
                    queryOrderDetail(record.orderNo).then((res: any) => {
                      setSignQrData(res?.data);
                      setSignQrVisible(true);
                    });
                  }}
                >
                  绑卡签约二维码
                </a>
              </div>
            </>
          );
        }
        return (
          <>
            {statusLeaseMap[record.orderStatus as keyof typeof statusLeaseMap] ||
              record.orderStatus}
          </>
        );
      },
    },
    {
      title: '借款合同类型',
      dataIndex: 'funderChannelCode',
      valueType: 'select',
      valueEnum: LEASE_LOAN_FILE_TYPE,
      render: (_, record) => {
        return (
          LEASE_LOAN_FILE_TYPE[record.funderChannelCode as keyof typeof LEASE_LOAN_FILE_TYPE] || '-'
        );
      },
    },
    {
      title: '赋强公证节点',
      dataIndex: 'notarizationStatus',
      valueEnum: NOTARIZATION_STATUS_MAP,
      search: false,
    },
    {
      title: '资方还款状态',
      dataIndex: 'channelStatus',
      valueEnum: channelStatusMap,
      valueType: 'select',
      hideInTable: isChannelStoreUser(access),
      search: false,
    },
    {
      title: '逾期代偿回购',
      dataIndex: 'repurchaseStatus',
      valueType: 'select',
      search: false,
      hideInTable: isChannelStoreUser(access),
      valueEnum: REPURCHASE_STATUS_MAP,
    },
    {
      title: '放款状态',
      dataIndex: 'loanStatus',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        options: optionsMap(leaseStatusMap),
      },
    },
    {
      title: '交车资料状态',
      dataIndex: 'carDeliveryStatus',
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        mode: 'multiple',
      },
      valueEnum: CAR_DELIVERY_STATUS,
    },
    {
      title: '申请城市',
      dataIndex: 'applyCityName',
      search: false,
    },
    {
      title: '上牌城市',
      dataIndex: 'carCityName',
      search: false,
    },
    {
      title: '车型名称',
      dataIndex: 'carModel',
      search: false,
    },
    {
      title: '车辆类型',
      dataIndex: 'carType',
      valueType: 'select',
      valueEnum: {
        1: '新车',
        2: '二手车',
      },
    },
    {
      title: '渠道类型',
      dataIndex: 'channelProductType',
      // valueType: 'select',
      search: false,
      valueEnum: CHANNEL_TYPES_MAP,
    },
    {
      title: '渠道类型',
      dataIndex: 'channelTypes',
      valueType: 'select',
      hideInTable: true,
      valueEnum: CHANNEL_TYPES_MAP,
      initialValue: isChannelStoreUser(access)
        ? access.currentUser?.channelType?.toString()
        : undefined,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled:
          isChannelStoreUser(access) &&
          !!CHANNEL_TYPES_MAP[
            (access.currentUser?.channelType as unknown) as keyof typeof CHANNEL_TYPES_MAP
          ],
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },

    {
      title: '渠道名称',
      dataIndex: 'channelIds',
      valueType: 'select',
      request: () => {
        return getAllChannelNameEnumMemo.then((res: any) => {
          // 渠道帐号限制渠道
          channelSearchFilter({ access, formRef });
          return res;
        });
      },
      debounceTime: 600000,
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.channelCode,
        showArrow: true,
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channelProductName',
      search: false,
    },
    {
      title: '上牌类型',
      dataIndex: 'licenseType',
      valueEnum: LICENSE_TYPES_MAP,
      search: false,
    },
    {
      title: '车架号',
      dataIndex: 'carUniqueCode',
    },
    {
      title: '注册手机号',
      dataIndex: 'phone',
      search: false,
    },
    {
      title: '车牌号',
      dataIndex: 'licenseCode',
      search: false,
    },
    {
      title: '进件时间',
      dataIndex: 'createdAt',
      search: false,
    },
    {
      title: '提交风控时间',
      dataIndex: 'submitRiskTime',
      search: false,
    },
    {
      title: '放款时间',
      dataIndex: 'lendingTime',
      search: false,
    },
    {
      title: '进件时间',
      dataIndex: 'createdAt',
      valueType: 'dateRange',
      hideInTable: true,
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      search: {
        // to-do: valueType 导致 renderFormItem 虽然为日期选择，但是值依然带有当前时间，需要特殊处理
        transform: (value: any) => ({
          applyTimeStart: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
          applyTimeEnd: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
        }),
      },
    },
    {
      title: '门店',
      dataIndex: 'storeIds',
      hideInTable: true,
      debounceTime: 600000,
      request: () => {
        return getStoreAndApplyCityAllMemo.then((res) => {
          // 渠道帐号限制门店
          storeSearchFilter({ access, formRef });
          return (
            res?.data?.storeList?.map((item: { storeName: string; id: string }) => {
              return { value: item.id?.toString(), label: item.storeName };
            }) || []
          );
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.extSource?.storeId,
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '申请城市',
      dataIndex: 'applyCityIds',
      hideInTable: true,
      debounceTime: 600000,
      request: () => {
        return getStoreAndApplyCityAllMemo.then((res) => {
          return (
            res?.data?.applyCityList?.map((item: { saleCity: string; saleCityCode: string }) => {
              return { value: item.saleCityCode, label: item.saleCity };
            }) || []
          );
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: 'GPS下单状态',
      dataIndex: 'hasCreateGpsOrder',
      valueType: 'select',
      hideInTable: true,
      valueEnum: {
        0: '未下单',
        1: '已下单',
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      // valueType: 'option',
      fixed: 'right',
      search: false,
      width: 300,
      render: (_, record) => {
        return (
          <Row>
            {
              // 1. 渠道进件的订单可以编辑
              // 2. 预审通过、补件状态的订单，不限制渠道
              (record.applySource === APPLY_SOURCE.CHANNEL ||
                [
                  ORDER_STATUS.RISK_REJECT,
                  ORDER_STATUS.PRE_AUDIT_PASS,
                  ORDER_STATUS.EDIT_PLAN,
                ].includes(record.orderStatus)) &&
                CAN_EDIT_STATUS.includes(record.orderStatus) && (
                  <Col span={8}>
                    <Button
                      type="link"
                      onClick={() => {
                        handleEdit(record);
                      }}
                    >
                      编辑
                    </Button>
                  </Col>
                )
            }

            {record.applySource === APPLY_SOURCE.CHANNEL &&
              CAN_LOOK_INCOME_STATUS.includes(record.orderStatus) && (
                <Col span={8}>
                  <Button
                    type="link"
                    onClick={() => {
                      nav(
                        `/businessMng/lease-come?orderNo=${record.orderNo}&userNo=${record?.userNo}&readOnly=1&openNew=true&tabName=进件详情`,
                      );
                    }}
                  >
                    查看进件信息
                  </Button>
                </Col>
              )}

            <Col span={8}>
              <Button
                type="link"
                onClick={() => {
                  history.push(`/businessMng/lease-detail?orderNo=${record.orderNo}`);
                }}
              >
                查看详情
              </Button>
            </Col>

            {record.orderStatus === 15 && (
              <Col span={8}>
                <Button
                  danger={record.orderStatus === 15 && record.isTimeoutFlag === 1}
                  type="link"
                  block
                  onClick={() => {
                    preCheckRef.current.checkPreBtn?.(record.orderNo);
                  }}
                >
                  提交风控
                </Button>
              </Col>
            )}
          </Row>
        );
      },
    },
    {
      title: '注册手机号',
      dataIndex: 'phone',
      hideInTable: true,
    },
    {
      title: '提交风控时间',
      dataIndex: 'submitRiskTimeSelect',
      valueType: 'dateRange',
      hideInTable: true,
      fieldProps: {
        disabledDate: disableFutureDate,
      },
      search: {
        transform: (value: any) => ({
          submitRiskStartTime: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
          submitRiskEndTime: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
        }),
      },
    },
    {
      title: '上牌类型',
      dataIndex: 'licenseType',
      valueType: 'select',
      valueEnum: LICENSE_TYPES_MAP,
      hideInTable: true,
    },
  ];

  const [showModal, setShowModal] = useState(false);
  const [selectedOrderNo, setSelectedOrderNo] = useState([]); // 选择的订单
  const [selectedOrders, setSelectedOrders] = useState([]); // 选择的订单

  async function getSearchDataTotal() {
    const searchParamsT = filterProps(formRef.current?.getFieldsFormatValue?.());
    const paramsInit = channelStoreRequestInit({
      access,
      formRef,
      params: searchParamsT,
    });
    const data = await queryOrder({ ...paramsInit });
    return data?.total;
  }

  const getNotraizationTotal = async () => {
    const data = await getNotraizationList({
      productSecondTypeCode: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
    });
    return data?.total;
  };

  return (
    <>
      <PageContainer>
        <ProTable<OrderListItem>
          actionRef={actionRef}
          formRef={formRef}
          rowKey="orderNo"
          scroll={{ x: 'max-content' }}
          onReset={() => {
            //  重置时初始化数据
            channelSearchFilter({ access, formRef });
            storeSearchFilter({ access, formRef });
            channelTypeFilter({ access, formRef });
          }}
          request={(params) => {
            // 业务系统_融租渠道用户只能看到固定渠道及门店
            // eslint-disable-next-line
            const paramsInit = channelStoreRequestInit({
              access,
              formRef,
              params,
            });
            console.log(paramsInit, '---');
            return queryOrder({ ...paramsInit });
          }}
          search={{
            labelWidth: 105,
            defaultCollapsed: false,
          }}
          columns={columns}
          rowSelection={{
            onChange: (selectedRowKeys: Key[], selectedRows: OrderListItem[]) => {
              setSelectedOrderNo(selectedRowKeys as []);
              setSelectedOrders(selectedRows as []);
              // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
            },
          }}
          toolBarRender={() => {
            return [
              !isChannelStoreUser(access) && (
                <AsyncExport
                  key="exportNotraizationExport"
                  getSearchDataTotal={getNotraizationTotal}
                  getSearchParams={() => {
                    return {
                      productSecondTypeCode: SECONDARY_CLASSIFICATION_CODE.FINANCE_LEASE_SECOND,
                    };
                  }}
                  trigger={
                    <Button key="notraizationList" type="primary">
                      赋强公证清单
                    </Button>
                  }
                  exportAsync={exportNotraizationList}
                  taskCode={[ItaskCodeEnValueEnum.NOTARIZATION_LIST]}
                />
              ),
              !isChannelStoreUser(access) && (
                <Button
                  key="errorData"
                  type="primary"
                  onClick={() => {
                    setShowErrorDataTable(true);
                  }}
                >
                  差错数据
                </Button>
              ),
              <Button
                key="createBtn"
                type="primary"
                onClick={() => {
                  setShowThreeModal(true);
                }}
              >
                新建
              </Button>,
              <BatchPushToRisk
                key="BatchPushToRisk"
                selectedOrders={selectedOrders}
                preCheckRef={preCheckRef}
                onCallback={() => {
                  setTimeout(() => {
                    actionRef.current?.clearSelected?.();
                    actionRef.current?.reload();
                  }, 2000);
                }}
              />, //  批量提交风控
              access.hasAccess('btn_delivery_sms_businessMng_leaseList') && (
                <Button
                  type="primary"
                  onClick={() => {
                    if (!selectedOrderNo.length) {
                      message.error('请先选中本次需要处理的订单数据');
                      return;
                    }
                    setShowModal(true);
                  }}
                >
                  交车催促短信提醒
                </Button>
              ),
              <AsyncExport
                key="exportButton"
                getSearchDataTotal={getSearchDataTotal}
                getSearchParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  return removeBlankFromObject(
                    filterProps({
                      ...params,
                    }),
                  );
                }}
                trigger={<Button type="primary">导出</Button>}
                exportAsync={orderExport}
                taskCode={[ItaskCodeEnValueEnum.ORDER_LEASE]}
              />,
              // <Button
              //   key="exportButton"
              //   type="primary"
              //   loading={exportLoading}
              //   onClick={() => {
              //     setExportLoading(true);
              //     const { createdAt, ...data } = formRef?.current?.getFieldsValue();
              //     let newForm = { ...data };
              //     if (createdAt?.length) {
              //       const applyTimeStart = `${createdAt[0].format('YYYY-MM-DD')} 00:00:00`;
              //       const applyTimeEnd = `${createdAt[1].format('YYYY-MM-DD')} 23:59:59`;
              //       newForm = { ...data, applyTimeStart, applyTimeEnd };
              //     }
              //     // /bizadmin/lease/order/export
              //     orderExport(newForm)
              //       .then((res) => {
              //         downLoadExcel(res);
              //         setExportLoading(false);
              //       })
              //       .catch(() => {
              //         setExportLoading(false);
              //       });
              //   }}
              // >
              //   导出
              // </Button>,
            ];
          }}
        />
        <CallDeliverySendMsgModal
          visible={showModal}
          orderList={selectedOrderNo}
          onClose={() => {
            setShowModal(false);
          }}
        />
        <ThreeModal
          visible={showThreeModal}
          orderStatus={curOrderStatus}
          orderNo={curOrderNo}
          onClose={() => {
            setShowThreeModal(false);
            setCurOrderNo('');
            setCurOrderStatus('');
          }}
          onReflash={() => {
            actionRef.current?.reload();
          }}
          onNextStep={handleNextStep}
        />
        <QrViewer
          data={signQrData}
          qrVisible={signQrVisible}
          handleQRVisible={setSignQrVisible}
          title="签约二维码"
        />
        <ErrorDataTableModal
          open={showErrorDataTable}
          onClose={() => {
            setShowErrorDataTable(false);
          }}
        />
      </PageContainer>
    </>
  );
};

export default () => {
  const { refresh } = useAliveController();
  // 从 另外一个页面跳 到该列表 由于订单号是变的，要根据query条件赋值到列表查询中，查询到最新数据,  是不希望缓存的
  useEffect(() => {
    const isCache = history?.location?.query?.isCache !== '0';
    if (!isCache) {
      refresh('businessMng/lease-list');
    }
  }, []);

  return (
    <>
      <HeaderTab />
      <KeepAlive name={'businessMng/lease-list'}>
        <OrderList />
      </KeepAlive>
    </>
  );
};
