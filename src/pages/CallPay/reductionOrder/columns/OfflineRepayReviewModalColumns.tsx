import { Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React from 'react';
import type { IbillRspDTOItem } from '../types';

export const OfflineRepayReviewModalColumns: ColumnsType<IbillRspDTOItem> = [
  {
    title: '还款金额（元）',
    dataIndex: 'expectRepayAmount',
    render: (_, record) => {
      return record?.expectRepayAmount || '-';
    },
  },
  {
    title: '还款顺序',
    dataIndex: 'index',
    render: (_, record, index) => {
      return <div>{index + 1}</div>;
    },
  },
  {
    title: '账单ID',
    dataIndex: 'billNo',
  },
  {
    title: '期数',
    dataIndex: 'termNumber',
  },
  {
    title: '订单号',
    dataIndex: 'orderNo',
  },
  {
    title: '车架号',
    dataIndex: 'subjectMatterNo',
    render(_, record) {
      return (
        <Tooltip
          title={
            <div>
              {record?.subjectMatterNoList?.map((subjectMatterNo) => {
                return <div>{subjectMatterNo}</div>;
              })}
            </div>
          }
        >
          <div
            style={{
              width: 200,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {record.subjectMatterNo}
          </div>
        </Tooltip>
      );
    },
  },
  {
    title: '客户名称',
    dataIndex: 'accountName',
  },

  {
    title: '未还总额',
    dataIndex: 'totalAmountUnpaid',
  },
  {
    title: '未还本金',
    dataIndex: 'totalPrincipalUnpaid',
  },
  {
    title: '未还利息',
    dataIndex: 'totalInterestUnpaid',
  },
  {
    title: '未还罚息',
    dataIndex: 'totalOverduePenaltyUnpaid',
  },
  {
    title: '未还违约金',
    dataIndex: 'totalBreach',
  },
  {
    title: '未还滞纳金',
    dataIndex: 'totalLate',
  },

  {
    title: '应还款总额',
    dataIndex: 'totalAmountDue',
  },
  {
    title: '应还本金',
    dataIndex: 'totalPrincipalDue',
  },
  {
    title: '应还利息',
    dataIndex: 'totalInterestDue',
  },

  {
    title: '应还罚息',
    dataIndex: 'totalOverduePenaltyDue',
  },

  {
    title: '应还违约金',
    dataIndex: 'totalBreachDue',
  },

  {
    title: '应还滞纳金',
    dataIndex: 'totalLateDue',
  },
  {
    title: '更新日期',
    dataIndex: 'updatedAt',
  },
  {
    title: '放款日期',
    dataIndex: 'lendingTime',
  },
  {
    title: '应还日期',
    dataIndex: 'dueDate',
  },

  {
    title: '账单状态',
    dataIndex: 'statusName',
  },
  {
    title: '逾期天数',
    dataIndex: 'overdueDateNumber',
  },
];
