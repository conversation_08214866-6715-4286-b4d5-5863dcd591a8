/*
 * @Author: your name
 * @Date: 2020-12-29 13:38:27
 * @LastEditTime: 2024-04-16 13:47:50
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/services/enum.ts
 */

import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import { queryAllUserList } from './global';

// 受让类型
export async function getRepayTypeEnum() {
  return request('/repayment/query/from').then((res) => res.data);
}

// 产品名称
// firstCode可以传‘’
// secondaryClassifcation二级分类
export async function getProductNameEnum(
  firstCode?: string,
): Promise<{ value: string; label: string }[]> {
  // return request('/repayment/query/productName').then((res) => res.data);
  return request('/bizadmin/product/getProductEnum', {
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
  }).then((res) => {
    const allItems =
      res?.data?.map((item: { productCode: string; productName: string }) => {
        return { value: item.productCode, label: item.productName };
      }) || [];
    if (firstCode) {
      const filterItems = allItems.filter((item: { value: string; label: string }) => {
        return item?.value?.substring(0, 2) === firstCode;
      });
      return filterItems;
    } else {
      return allItems;
    }
  });
}

//产品编码
export async function productCodeEnum(firstCode?: string) {
  // return request('/repayment/query/productName').then((res) => res.data);
  // /loan/product/getProductEnum
  return request('/bizadmin/product/getProductEnum', {
    headers: { 'hll-appid': 'bme-finance-bizadmin-svc' },
  }).then((res) => {
    const allItems =
      res?.data?.map((item: { productCode: string; productName: string }) => {
        return { value: item.productCode, label: item.productCode };
      }) || [];
    if (firstCode) {
      const filterItems = allItems.filter((item: { value: string; label: string }) => {
        return item?.value?.substring(0, 2) === firstCode;
      });
      return filterItems;
    } else {
      return allItems;
    }
  });
}

// 产品类型
export async function getProductTypeEnum() {
  return request('/repayment/query/classification').then((res) => res.data);
}

export async function getUserListEnum() {
  // request('/auth/operator/privilege/getUserList')
  return queryAllUserList().then((res) => {
    return (
      res.data?.map((item: { id: number; username: string }) => {
        return { value: item.id, label: item.username };
      }) || []
    );
  });
}

// 上牌城市枚举
export async function getLicenseCityEnum() {
  // return request('/repayment/query/productName').then((res) => res.data);
  return request('/bizadmin/lease/licenseCity/getLicenseCityEnum', {
    headers: bizAdminHeader,
  }).then((res) => {
    return (
      res?.data?.map((item: { cityName: string; id: string }) => {
        return { value: item.id, label: item.cityName };
      }) || []
    );
  });
}

//获取渠道名称
export async function getAllChannelNameEnum() {
  return request(`/bizadmin/channel/lease/allList`, {
    method: 'GET',
    headers: bizAdminHeader,
  }).then((res) => {
    return (
      res?.data?.map((item: { channelName: string; id: string }) => {
        return { ...item, value: item.id, label: item.channelName };
      }) || []
    );
  });
}
