export enum STATUS_TYPE {
  ENABLE = 1,
  DISABLE = 0,
}
export const statusMap = {
  [STATUS_TYPE.ENABLE]: {
    text: '启用',
    status: 'success',
  },
  [STATUS_TYPE.DISABLE]: {
    text: '未启用',
    status: 'default',
  },
};

export enum EMPLOYEE_TYPE {
  // 运营
  OPERATION = 9,
  // 车险分期渠道
  INSURANCE_CHANNEL = 10,
  // 外部用户
  EXTERNAL_USER = 11,
}
export const employeeTypeMap = {
  [EMPLOYEE_TYPE.OPERATION]: '运营',
  [EMPLOYEE_TYPE.INSURANCE_CHANNEL]: '渠道',
  [EMPLOYEE_TYPE.EXTERNAL_USER]: '委外',
};

export enum envEnum {
  stg = 'stg',
  pre = 'pre',
  prd = 'prd',
}
export const getEnvOptions = (env: envEnum) => {
  return [
    {
      label: envEnum.stg,
      value: envEnum.stg,
      disabled: [envEnum.stg, envEnum.prd].includes(env),
    },
    {
      label: envEnum.pre,
      value: envEnum.pre,
      disabled: [envEnum.pre].includes(env),
    },
    {
      label: envEnum.prd,
      value: envEnum.prd,
      disabled: [envEnum.stg, envEnum.prd].includes(env),
    },
  ];
};
