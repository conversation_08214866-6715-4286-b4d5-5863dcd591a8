import { useModel } from '@umijs/max';
import { Form, FormInstance, InputNumber } from 'antd';
import React, { memo, useEffect, useState } from 'react';

type Props = {
  currentProductData: any;
  expenseItem: 'LOW_DOWN_PAYMENT' | 'BAIL' | 'COMMISSION';
  productInfoForm: FormInstance;
  label: string;
  name: string;
  productIsChange: boolean;
};

const calculationEnumMap = {
  PROPORTIONALLY: 1,
  AMOUNT_PROPORTION: 2,
};

const FeeConfig: React.FC<Props> = (props) => {
  const { productInfoForm, currentProductData, label, name, expenseItem, productIsChange } = props;
  const { isEditable } = useModel('CarInsurance.carInsurance', (model) => {
    return {
      isEditable: model?.isEditable,
    };
  });
  const [disabled, setDisabled] = useState(false);
  const [addonAfter, setAddonAfter] = useState<any>(null); // 后缀
  const [prefix, setPrefix] = useState<any>(null); // 前缀

  console.log('currentProductData', currentProductData);

  // 保留两位小数 兼容任何数据类型 nan 取 0
  function saveToFixed(value: any, num = 2) {
    const numValue = Number(value);
    return isNaN(numValue) ? 0 : numValue.toFixed(num);
  }

  /**
   * 得到首付 手续费 保证金 配置 包括什么时候禁用 什么时候用什么值
   * 按范围(比例和金额) 可以进行输入更改
   * 按固定值(比例和金额) 不可更改
   */
  function setProductConfig() {
    const { costConfigs = [] } = currentProductData;
    // const proportionNumberValue = costConfigs?.[0]?.proportionNumberValue;
    // 首付 保证金 手续费
    const expenseItemData =
      (costConfigs || [])?.find((item: any) => item.expenseItem === expenseItem) || {};
    const {
      calculationEnum, // 按 比例 还是 按 金额
      proportionNumberEnum, // 固定比例 区间范围
      amountTypeEnum, // 固定金额 区间范围
      // proportionNumberValue, // 固定比例
      minProportionNumberValue, // 最小比例
      maxProportionNumberValue, // 最大比例
      // amount, // 固定金额
      amountMax, // 最大金额
      amountMin, // 最小金额
    } = expenseItemData;
    if (calculationEnum) {
      if (calculationEnum === 'PROPORTIONALLY') {
        // 假如计算方式是按比例
        if (proportionNumberEnum === 'BY_FIXED_RATION') {
          // 固定比例
          setDisabled(true);
          setAddonAfter(<a>固定比例</a>);
          setPrefix(null);
        } else {
          // 区间比例
          setDisabled(false);
          setPrefix(null);
          setAddonAfter(
            <a>
              {saveToFixed(minProportionNumberValue)}-{saveToFixed(maxProportionNumberValue)}
              (比例区间)
            </a>,
          );
        }
      } else {
        // 按照金额
        if (amountTypeEnum === 'FIXED_AMOUNT') {
          // 固定金额
          setDisabled(true);
          setAddonAfter(<a>固定金额</a>);
          setPrefix('¥');
        } else {
          // 区间范围
          setDisabled(false);
          setPrefix('¥');
          setAddonAfter(
            <a>
              ¥{amountMin}-¥{amountMax}(金额区间){' '}
            </a>,
          );
        }
      }
    } else {
      // 说明没有这个配置项
      setAddonAfter(null);
      setPrefix(null);
      setDisabled(false);
      productInfoForm.setFieldsValue({ [name]: 0 });
    }
  }

  // 设置 首付 保证金 手续费类型
  function setExpenseItemType() {
    const { costConfigs = [] } = currentProductData;
    // 首付 保正金 手续费 数据
    const configData: any =
      costConfigs?.find((item: any) => item.expenseItem === expenseItem) || {};
    const { calculationEnum } = configData;
    productInfoForm.setFieldsValue({
      [`${name}Type`]: calculationEnumMap[calculationEnum] || 0, // 比如保证金 和 手续费可能没有 没有就是 0
    });
  }

  // 根据配置设置值
  function setFormValue() {
    const { costConfigs = [] } = currentProductData;
    // const proportionNumberValue = costConfigs?.[0]?.proportionNumberValue;
    // 首付 保证金 手续费
    const expenseItemData =
      costConfigs?.find((item: any) => item.expenseItem === expenseItem) || {};
    const {
      calculationEnum, // 按 比例 还是 按 金额
      proportionNumberEnum, // 固定比例 区间范围
      amountTypeEnum, // 固定金额 区间范围
      proportionNumberValue, // 固定比例
      amount, // 固定金额
    } = expenseItemData;
    if (calculationEnum) {
      if (calculationEnum === 'PROPORTIONALLY') {
        // 假如计算方式是按比例
        if (proportionNumberEnum === 'BY_FIXED_RATION') {
          // 固定比例
          productInfoForm.setFieldsValue({ [name]: proportionNumberValue });
        } else {
          // 区间比例
          productInfoForm.setFieldsValue({ [name]: undefined });
        }
      } else {
        // 按照金额
        if (amountTypeEnum === 'FIXED_AMOUNT') {
          // 固定金额
          productInfoForm.setFieldsValue({ [name]: `${amount}` });
        } else {
          productInfoForm.setFieldsValue({ [name]: undefined });
        }
      }
    }
  }

  console.log('112233currentProductData', currentProductData);

  useEffect(() => {
    if (currentProductData?.productCode) {
      // 设置产品的配置
      setProductConfig();

      if (productIsChange) {
        // 根据配置设置表单值
        setFormValue();
        // 设置一些额外的需要提交的
        setExpenseItemType();
      }
    }
  }, [currentProductData]);

  // 判断一个数是否属于某两个数的中间
  function isRange(num: number, num1: number, num2: number) {
    if (num1 > num2) {
      return num <= num1 && num >= num2;
    } else {
      return num <= num2 && num >= num1;
    }
  }

  // 是否可以转成有效的数字
  // function isConvertValidNumber(num: any) {
  //   return isNaN(Number(num)) ? false : true;
  // }

  return (
    <Form.Item
      label={label}
      name={name}
      rules={[
        { required: true, message: '必填' },
        {
          validator: (_, value) => {
            const { costConfigs = [] } = currentProductData;
            const expenseItemData =
              costConfigs?.find((item: any) => item.expenseItem === expenseItem) || {};
            const {
              calculationEnum, // 按 比例 还是 按 金额
              proportionNumberEnum, // 固定比例 区间范围
              amountTypeEnum, // 固定金额 区间范围
              minProportionNumberValue, // 最小比例
              maxProportionNumberValue, // 最大比例
              // amount, // 固定金额
              amountMax, // 最大金额
              amountMin, // 最小金额
            } = expenseItemData;

            // isConvertValidNumber

            if (!calculationEnum) {
              // 不存在不需要判断
              return Promise.resolve();
            }

            if (calculationEnum === 'PROPORTIONALLY') {
              if (proportionNumberEnum === 'BY_FIXED_RATION') {
                return Promise.resolve();
              } else {
                return isRange(value, minProportionNumberValue, maxProportionNumberValue)
                  ? Promise.resolve()
                  : Promise.reject('超出范围');
              }
            } else {
              if (amountTypeEnum === 'FIXED_AMOUNT') {
                return Promise.resolve();
              } else {
                return isRange(value, amountMin, amountMax)
                  ? Promise.resolve()
                  : Promise.reject('超出范围');
              }
            }
          },
        },
      ]}
    >
      <InputNumber
        disabled={!isEditable || disabled}
        prefix={prefix}
        addonAfter={addonAfter}
        // style={{ width: 220 }}
      />
    </Form.Item>
  );
};

export default memo(FeeConfig);
