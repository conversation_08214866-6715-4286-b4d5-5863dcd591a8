import ImagePreview, { ImagePreviewInstance } from '@/components/ImagePreview';
import { getBizadminUploadAction } from '@/pages/BusinessExcel/utils';
import { getAuthHeaders } from '@/utils/auth';
import { LinkOutlined } from '@ant-design/icons';
import { useAccess } from '@umijs/max';
import { Divider, message, Upload } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { CancelStatusEnum, DetailInfo, FileInfoAttach } from '../const';
import '../index.less';
import { cancellationUploadFile } from '../service';

interface ModalProps {
  defaultFileList: FileInfoAttach[];
  detailInfo: DetailInfo;
}

const Page: React.FC<any> = forwardRef(({ defaultFileList, detailInfo }, ref) => {
  // console.log('defaultFileList', defaultFileList);
  const access = useAccess();
  // 附件保存权限
  const hasUploadAccess =
    access?.hasAccess('upload_file_biz_businessMng_postLoanMng_cancellation') || false;
  const [fileList, setFileList] = React.useState<any[]>([]);
  useEffect(() => {
    const fileList =
      defaultFileList?.map((item: FileInfoAttach) => {
        return {
          uid: item?.filePath,
          name: item?.fileName,
          status: 'done',
          //
          filePath: item?.filePath,
          fileRelativePath: item?.fileRelativePath,
          fileName: item?.fileName,
        };
      }) || [];
    setFileList(fileList);
  }, [defaultFileList]);
  //
  // 提交
  const handleUploadSubmit = async () => {
    console.log('handleSubmit');
    if (!fileList || fileList.length === 0) {
      message.error('请上传附件');
      return;
    }
    const isUploading = fileList?.some((item: any) => item.status === 'uploading');
    const hasValidFile = fileList?.some((item: any) => item.status === 'done');
    if (isUploading) {
      message.error('文件未上传完成');
      return;
    }
    if (!hasValidFile) {
      message.error('请上传附件');
      return;
    }
    // 格式化数据
    const fileInfo: FileInfoAttach[] = fileList
      ?.filter((item: any) => item?.status === 'done')
      ?.map((item: any) => {
        return {
          fileName: item?.name || item?.fileName,
          // filePath: item?.response?.data?.netWorkPath || item?.filePath,
          // fileRelativePath: item?.response?.data?.filePath || item?.fileRelativePath,
          // 此处filePath需要传相对路径
          filePath: item?.response?.data?.filePath || item?.fileRelativePath,
        };
      });
    const data: any = {
      bizNo: detailInfo?.bizNo,
      fileList: fileInfo,
    };
    console.log('upload data', data);
    const res: any = await cancellationUploadFile(data).catch(() => {});
    if (res?.ret === 0) {
      message.success('保存成功');
    }
    return res;
  };
  //
  useImperativeHandle(ref, () => ({
    // 暴露给父组件的方法
    submitUpload: () => {
      return handleUploadSubmit().catch(() => {});
    },
  }));
  // 图片预览
  const previewRef = useRef<ImagePreviewInstance>();
  const handleUploadPreview = async (file: any, urlList: string[]) => {
    console.log('urlList', urlList);
    console.log('file', file);
    if (file?.filePath) {
      previewRef.current?.previewFile({
        url: file?.filePath,
        fileName: file?.fileName,
        urlList,
      });
    }
  };
  //
  //
  return (
    <>
      <div>
        <div className="flex-wrap">
          <div className="detail-label">
            <span className="red-text">*</span>批单及其他资料:{' '}
          </div>
          {/* 区分渠道，待定：渠道是否允许上传 */}
          {!hasUploadAccess || detailInfo?.orderStatus === CancelStatusEnum.CANCEL ? (
            <>
              {/* 渠道用户 */}
              <div className="img-list">
                {detailInfo?.fileInfoAttach?.map((file: any) => {
                  return (
                    <div key={file?.filePath} className="img-item">
                      <span
                        className="list-wrap"
                        onClick={() =>
                          handleUploadPreview(
                            {
                              url: file?.filePath,
                              fileName: file?.fileName,
                              filePath: file?.filePath,
                            },
                            detailInfo?.fileInfoAttach?.map((item: any) => item?.filePath),
                          )
                        }
                      >
                        <span>
                          <LinkOutlined className="list-icon" />
                        </span>
                        <span className="list-ellipsis-text">{file?.fileName}</span>
                      </span>
                    </div>
                  );
                })}
              </div>
            </>
          ) : (
            <>
              {/* 非渠道用户 */}
              <div className="upload-pannel-wrapper">
                <p className="upload-pannel-title">
                  支持格式：.doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar，单个文件最大50M
                </p>
                {/* <UploadPannel
                  ref={uploadPannelRef}
                  detailInfo={detailInfo}
                  defaultFileList={detailInfo?.fileInfoAttach ? detailInfo?.fileInfoAttach : []}
                /> */}
                <Upload.Dragger
                  name="file"
                  className="upload-area-inline"
                  fileList={fileList}
                  multiple={true}
                  disabled={!hasUploadAccess}
                  accept=".doc,.docx,.txt,.zip,.pdf,.png,.jpg,.jpeg,.gif,.xls,.xlsx,.bmp,.rar"
                  action={getBizadminUploadAction()}
                  headers={{ ...getAuthHeaders(), 'hll-appid': 'bme-finance-bizadmin-svc' }}
                  // headers={{ ...getAuthHeaders() }}
                  data={{ acl: 'PRIVATE_ACL', destPath: 'CAR_POLICY_CANCELLATION' }}
                  beforeUpload={(file: any) => {
                    // 限制大小：50m
                    const sizeValid = file.size / 1024 / 1024 <= 50;
                    // 超过限制
                    if (!sizeValid) {
                      message.error('文件大小不能超过50M');
                    }
                    return sizeValid || Upload.LIST_IGNORE;
                  }}
                  onPreview={(file: any) => {
                    console.log('onPreview1', file);
                    console.log('onPreview2', fileList);
                    const urlList =
                      fileList?.map(
                        (item: any) => item?.response?.data?.netWorkPath || item?.filePath,
                      ) || [];
                    return handleUploadPreview(
                      {
                        fileName: file?.name || file?.fileName,
                        filePath: file?.response?.data?.netWorkPath || file?.filePath,
                        fileRelativePath: file?.response?.data?.filePath || file?.fileRelativePath,
                        ...file,
                      },
                      urlList,
                    );
                  }}
                  onChange={({ fileList }) => {
                    console.log('onChange1', fileList);
                    setFileList([...fileList]);
                  }}
                >
                  <p className="ant-upload-text primary-color">点击或拖动文件到此处上传</p>
                </Upload.Dragger>
              </div>
            </>
          )}
        </div>
      </div>
      {/* 图片预览 */}
      <div className="img-preview">
        <ImagePreview ref={previewRef as any} />
      </div>
      {/* 分割线 */}
      <div className="row-divider-wrap">
        <Divider className="row-divider" />
      </div>
    </>
  );
});
export default Page;
